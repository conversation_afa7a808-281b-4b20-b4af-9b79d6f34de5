import { Controller, Get, Patch, Body, Param, Delete, UseGuards } from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { User } from './schemas/user.schema';
import { UpdateUserDto, UpdateUserWrapperDto } from './dto/update-user.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../common/guards/role.guard';
import { Roles } from '../common/decorators/roles.decorator';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

/*   @Post()
  async create(@Body() createUserDto: CreateUserDto): Promise<User> {
    return this.usersService.create(createUserDto);
  }
 */
  @Get()
  async findAll(): Promise<User[]> {
    return this.usersService.findAll();
  }
  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('secretary_general')
  async update(@Param('id') id: string, @Body() data: UpdateUserDto): Promise<User | null> {
    return this.usersService.update(id, data);
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<User|null> {
    return this.usersService.findOne(id);
  }

  @Delete(':id')
  async remove(@Param('id') id: string): Promise<void | User|null> {
    return this.usersService.remove(id);
  }
}