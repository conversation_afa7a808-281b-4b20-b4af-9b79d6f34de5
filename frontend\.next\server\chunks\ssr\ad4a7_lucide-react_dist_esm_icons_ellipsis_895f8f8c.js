module.exports=[78928,51190,51075,a=>{"use strict";a.s(["MoreHorizontal",()=>c],78928);var b=a.i(621);let c=(0,b.default)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);a.s(["Edit",()=>d],51190);let d=(0,b.default)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);a.s(["DropdownMenu",()=>a8,"DropdownMenuContent",()=>ba,"DropdownMenuItem",()=>bb,"DropdownMenuLabel",()=>bc,"DropdownMenuSeparator",()=>bd,"DropdownMenuTrigger",()=>a9],51075);var e=a.i(68116),f=a.i(128),g=a.i(57565),h=a.i(9403),i=a.i(54130),j=a.i(65662),k=a.i(48206),l=a.i(48099),m=a.i(85955),n=a.i(44177),o=a.i(95686),p=a.i(45105),q=a.i(90368),r=a.i(60091),s=a.i(24321),t=a.i(96780),u=a.i(1510),v="rovingFocusGroup.onEntryFocus",w={bubbles:!1,cancelable:!0},x="RovingFocusGroup",[y,z,A]=(0,l.createCollection)(x),[B,C]=(0,i.createContextScope)(x,[A]),[D,E]=B(x),F=f.forwardRef((a,b)=>(0,e.jsx)(y.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,e.jsx)(y.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,e.jsx)(G,{...a,ref:b})})}));F.displayName=x;var G=f.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:d,loop:i=!1,dir:l,currentTabStopId:n,defaultCurrentTabStopId:o,onCurrentTabStopIdChange:p,onEntryFocus:q,preventScrollOnEntryFocus:r=!1,...s}=a,t=f.useRef(null),y=(0,h.useComposedRefs)(b,t),A=(0,m.useDirection)(l),[B,C]=(0,j.useControllableState)({prop:n,defaultProp:o??null,onChange:p,caller:x}),[E,F]=f.useState(!1),G=(0,u.useCallbackRef)(q),H=z(c),I=f.useRef(!1),[J,L]=f.useState(0);return f.useEffect(()=>{let a=t.current;if(a)return a.addEventListener(v,G),()=>a.removeEventListener(v,G)},[G]),(0,e.jsx)(D,{scope:c,orientation:d,dir:A,loop:i,currentTabStopId:B,onItemFocus:f.useCallback(a=>C(a),[C]),onItemShiftTab:f.useCallback(()=>F(!0),[]),onFocusableItemAdd:f.useCallback(()=>L(a=>a+1),[]),onFocusableItemRemove:f.useCallback(()=>L(a=>a-1),[]),children:(0,e.jsx)(k.Primitive.div,{tabIndex:E||0===J?-1:0,"data-orientation":d,...s,ref:y,style:{outline:"none",...a.style},onMouseDown:(0,g.composeEventHandlers)(a.onMouseDown,()=>{I.current=!0}),onFocus:(0,g.composeEventHandlers)(a.onFocus,a=>{let b=!I.current;if(a.target===a.currentTarget&&b&&!E){let b=new CustomEvent(v,w);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=H().filter(a=>a.focusable);K([a.find(a=>a.active),a.find(a=>a.id===B),...a].filter(Boolean).map(a=>a.ref.current),r)}}I.current=!1}),onBlur:(0,g.composeEventHandlers)(a.onBlur,()=>F(!1))})})}),H="RovingFocusGroupItem",I=f.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:d=!0,active:h=!1,tabStopId:i,children:j,...l}=a,m=(0,q.useId)(),n=i||m,o=E(H,c),p=o.currentTabStopId===n,r=z(c),{onFocusableItemAdd:s,onFocusableItemRemove:t,currentTabStopId:u}=o;return f.useEffect(()=>{if(d)return s(),()=>t()},[d,s,t]),(0,e.jsx)(y.ItemSlot,{scope:c,id:n,focusable:d,active:h,children:(0,e.jsx)(k.Primitive.span,{tabIndex:p?0:-1,"data-orientation":o.orientation,...l,ref:b,onMouseDown:(0,g.composeEventHandlers)(a.onMouseDown,a=>{d?o.onItemFocus(n):a.preventDefault()}),onFocus:(0,g.composeEventHandlers)(a.onFocus,()=>o.onItemFocus(n)),onKeyDown:(0,g.composeEventHandlers)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void o.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return J[e]}(a,o.orientation,o.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=r().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=o.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>K(c))}}),children:"function"==typeof j?j({isCurrentTabStop:p,hasTabStop:null!=u}):j})})});I.displayName=H;var J={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function K(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var L=a.i(85689),M=a.i(22999),N=a.i(39491),O=["Enter"," "],P=["ArrowUp","PageDown","End"],Q=["ArrowDown","PageUp","Home",...P],R={ltr:[...O,"ArrowRight"],rtl:[...O,"ArrowLeft"]},S={ltr:["ArrowLeft"],rtl:["ArrowRight"]},T="Menu",[U,V,W]=(0,l.createCollection)(T),[X,Y]=(0,i.createContextScope)(T,[W,r.createPopperScope,C]),Z=(0,r.createPopperScope)(),$=C(),[_,aa]=X(T),[ab,ac]=X(T),ad=a=>{let{__scopeMenu:b,open:c=!1,children:d,dir:g,onOpenChange:h,modal:i=!0}=a,j=Z(b),[k,l]=f.useState(null),n=f.useRef(!1),o=(0,u.useCallbackRef)(h),p=(0,m.useDirection)(g);return f.useEffect(()=>{let a=()=>{n.current=!0,document.addEventListener("pointerdown",b,{capture:!0,once:!0}),document.addEventListener("pointermove",b,{capture:!0,once:!0})},b=()=>n.current=!1;return document.addEventListener("keydown",a,{capture:!0}),()=>{document.removeEventListener("keydown",a,{capture:!0}),document.removeEventListener("pointerdown",b,{capture:!0}),document.removeEventListener("pointermove",b,{capture:!0})}},[]),(0,e.jsx)(r.Root,{...j,children:(0,e.jsx)(_,{scope:b,open:c,onOpenChange:o,content:k,onContentChange:l,children:(0,e.jsx)(ab,{scope:b,onClose:f.useCallback(()=>o(!1),[o]),isUsingKeyboardRef:n,dir:p,modal:i,children:d})})})};ad.displayName=T;var ae=f.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,f=Z(c);return(0,e.jsx)(r.Anchor,{...f,...d,ref:b})});ae.displayName="MenuAnchor";var af="MenuPortal",[ag,ah]=X(af,{forceMount:void 0}),ai=a=>{let{__scopeMenu:b,forceMount:c,children:d,container:f}=a,g=aa(af,b);return(0,e.jsx)(ag,{scope:b,forceMount:c,children:(0,e.jsx)(t.Presence,{present:c||g.open,children:(0,e.jsx)(s.Portal,{asChild:!0,container:f,children:d})})})};ai.displayName=af;var aj="MenuContent",[ak,al]=X(aj),am=f.forwardRef((a,b)=>{let c=ah(aj,a.__scopeMenu),{forceMount:d=c.forceMount,...f}=a,g=aa(aj,a.__scopeMenu),h=ac(aj,a.__scopeMenu);return(0,e.jsx)(U.Provider,{scope:a.__scopeMenu,children:(0,e.jsx)(t.Presence,{present:d||g.open,children:(0,e.jsx)(U.Slot,{scope:a.__scopeMenu,children:h.modal?(0,e.jsx)(an,{...f,ref:b}):(0,e.jsx)(ao,{...f,ref:b})})})})}),an=f.forwardRef((a,b)=>{let c=aa(aj,a.__scopeMenu),d=f.useRef(null),i=(0,h.useComposedRefs)(b,d);return f.useEffect(()=>{let a=d.current;if(a)return(0,M.hideOthers)(a)},[]),(0,e.jsx)(aq,{...a,ref:i,trapFocus:c.open,disableOutsidePointerEvents:c.open,disableOutsideScroll:!0,onFocusOutside:(0,g.composeEventHandlers)(a.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>c.onOpenChange(!1)})}),ao=f.forwardRef((a,b)=>{let c=aa(aj,a.__scopeMenu);return(0,e.jsx)(aq,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>c.onOpenChange(!1)})}),ap=(0,L.createSlot)("MenuContent.ScrollLock"),aq=f.forwardRef((a,b)=>{let{__scopeMenu:c,loop:d=!1,trapFocus:i,onOpenAutoFocus:j,onCloseAutoFocus:k,disableOutsidePointerEvents:l,onEntryFocus:m,onEscapeKeyDown:q,onPointerDownOutside:s,onFocusOutside:t,onInteractOutside:u,onDismiss:v,disableOutsideScroll:w,...x}=a,y=aa(aj,c),z=ac(aj,c),A=Z(c),B=$(c),C=V(c),[D,E]=f.useState(null),G=f.useRef(null),H=(0,h.useComposedRefs)(b,G,y.onContentChange),I=f.useRef(0),J=f.useRef(""),K=f.useRef(0),L=f.useRef(null),M=f.useRef("right"),O=f.useRef(0),R=w?N.RemoveScroll:f.Fragment;f.useEffect(()=>()=>window.clearTimeout(I.current),[]),(0,o.useFocusGuards)();let S=f.useCallback(a=>M.current===L.current?.side&&function(a,b){return!!b&&function(a,b){let{x:c,y:d}=a,e=!1;for(let a=0,f=b.length-1;a<b.length;f=a++){let g=b[a],h=b[f],i=g.x,j=g.y,k=h.x,l=h.y;j>d!=l>d&&c<(k-i)*(d-j)/(l-j)+i&&(e=!e)}return e}({x:a.clientX,y:a.clientY},b)}(a,L.current?.area),[]);return(0,e.jsx)(ak,{scope:c,searchRef:J,onItemEnter:f.useCallback(a=>{S(a)&&a.preventDefault()},[S]),onItemLeave:f.useCallback(a=>{S(a)||(G.current?.focus(),E(null))},[S]),onTriggerLeave:f.useCallback(a=>{S(a)&&a.preventDefault()},[S]),pointerGraceTimerRef:K,onPointerGraceIntentChange:f.useCallback(a=>{L.current=a},[]),children:(0,e.jsx)(R,{...w?{as:ap,allowPinchZoom:!0}:void 0,children:(0,e.jsx)(p.FocusScope,{asChild:!0,trapped:i,onMountAutoFocus:(0,g.composeEventHandlers)(j,a=>{a.preventDefault(),G.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:k,children:(0,e.jsx)(n.DismissableLayer,{asChild:!0,disableOutsidePointerEvents:l,onEscapeKeyDown:q,onPointerDownOutside:s,onFocusOutside:t,onInteractOutside:u,onDismiss:v,children:(0,e.jsx)(F,{asChild:!0,...B,dir:z.dir,orientation:"vertical",loop:d,currentTabStopId:D,onCurrentTabStopIdChange:E,onEntryFocus:(0,g.composeEventHandlers)(m,a=>{z.isUsingKeyboardRef.current||a.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,e.jsx)(r.Content,{role:"menu","aria-orientation":"vertical","data-state":aQ(y.open),"data-radix-menu-content":"",dir:z.dir,...A,...x,ref:H,style:{outline:"none",...x.style},onKeyDown:(0,g.composeEventHandlers)(x.onKeyDown,a=>{let b=a.target.closest("[data-radix-menu-content]")===a.currentTarget,c=a.ctrlKey||a.altKey||a.metaKey,d=1===a.key.length;b&&("Tab"===a.key&&a.preventDefault(),!c&&d&&(a=>{let b=J.current+a,c=C().filter(a=>!a.disabled),d=document.activeElement,e=c.find(a=>a.ref.current===d)?.textValue,f=function(a,b,c){var d;let e=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,f=c?a.indexOf(c):-1,g=(d=Math.max(f,0),a.map((b,c)=>a[(d+c)%a.length]));1===e.length&&(g=g.filter(a=>a!==c));let h=g.find(a=>a.toLowerCase().startsWith(e.toLowerCase()));return h!==c?h:void 0}(c.map(a=>a.textValue),b,e),g=c.find(a=>a.textValue===f)?.ref.current;!function a(b){J.current=b,window.clearTimeout(I.current),""!==b&&(I.current=window.setTimeout(()=>a(""),1e3))}(b),g&&setTimeout(()=>g.focus())})(a.key));let e=G.current;if(a.target!==e||!Q.includes(a.key))return;a.preventDefault();let f=C().filter(a=>!a.disabled).map(a=>a.ref.current);P.includes(a.key)&&f.reverse(),function(a){let b=document.activeElement;for(let c of a)if(c===b||(c.focus(),document.activeElement!==b))return}(f)}),onBlur:(0,g.composeEventHandlers)(a.onBlur,a=>{a.currentTarget.contains(a.target)||(window.clearTimeout(I.current),J.current="")}),onPointerMove:(0,g.composeEventHandlers)(a.onPointerMove,aT(a=>{let b=a.target,c=O.current!==a.clientX;a.currentTarget.contains(b)&&c&&(M.current=a.clientX>O.current?"right":"left",O.current=a.clientX)}))})})})})})})});am.displayName=aj;var ar=f.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,e.jsx)(k.Primitive.div,{role:"group",...d,ref:b})});ar.displayName="MenuGroup";var as=f.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,e.jsx)(k.Primitive.div,{...d,ref:b})});as.displayName="MenuLabel";var at="MenuItem",au="menu.itemSelect",av=f.forwardRef((a,b)=>{let{disabled:c=!1,onSelect:d,...i}=a,j=f.useRef(null),l=ac(at,a.__scopeMenu),m=al(at,a.__scopeMenu),n=(0,h.useComposedRefs)(b,j),o=f.useRef(!1);return(0,e.jsx)(aw,{...i,ref:n,disabled:c,onClick:(0,g.composeEventHandlers)(a.onClick,()=>{let a=j.current;if(!c&&a){let b=new CustomEvent(au,{bubbles:!0,cancelable:!0});a.addEventListener(au,a=>d?.(a),{once:!0}),(0,k.dispatchDiscreteCustomEvent)(a,b),b.defaultPrevented?o.current=!1:l.onClose()}}),onPointerDown:b=>{a.onPointerDown?.(b),o.current=!0},onPointerUp:(0,g.composeEventHandlers)(a.onPointerUp,a=>{o.current||a.currentTarget?.click()}),onKeyDown:(0,g.composeEventHandlers)(a.onKeyDown,a=>{let b=""!==m.searchRef.current;c||b&&" "===a.key||O.includes(a.key)&&(a.currentTarget.click(),a.preventDefault())})})});av.displayName=at;var aw=f.forwardRef((a,b)=>{let{__scopeMenu:c,disabled:d=!1,textValue:i,...j}=a,l=al(at,c),m=$(c),n=f.useRef(null),o=(0,h.useComposedRefs)(b,n),[p,q]=f.useState(!1),[r,s]=f.useState("");return f.useEffect(()=>{let a=n.current;a&&s((a.textContent??"").trim())},[j.children]),(0,e.jsx)(U.ItemSlot,{scope:c,disabled:d,textValue:i??r,children:(0,e.jsx)(I,{asChild:!0,...m,focusable:!d,children:(0,e.jsx)(k.Primitive.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":d||void 0,"data-disabled":d?"":void 0,...j,ref:o,onPointerMove:(0,g.composeEventHandlers)(a.onPointerMove,aT(a=>{d?l.onItemLeave(a):(l.onItemEnter(a),a.defaultPrevented||a.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,g.composeEventHandlers)(a.onPointerLeave,aT(a=>l.onItemLeave(a))),onFocus:(0,g.composeEventHandlers)(a.onFocus,()=>q(!0)),onBlur:(0,g.composeEventHandlers)(a.onBlur,()=>q(!1))})})})}),ax=f.forwardRef((a,b)=>{let{checked:c=!1,onCheckedChange:d,...f}=a;return(0,e.jsx)(aF,{scope:a.__scopeMenu,checked:c,children:(0,e.jsx)(av,{role:"menuitemcheckbox","aria-checked":aR(c)?"mixed":c,...f,ref:b,"data-state":aS(c),onSelect:(0,g.composeEventHandlers)(f.onSelect,()=>d?.(!!aR(c)||!c),{checkForDefaultPrevented:!1})})})});ax.displayName="MenuCheckboxItem";var ay="MenuRadioGroup",[az,aA]=X(ay,{value:void 0,onValueChange:()=>{}}),aB=f.forwardRef((a,b)=>{let{value:c,onValueChange:d,...f}=a,g=(0,u.useCallbackRef)(d);return(0,e.jsx)(az,{scope:a.__scopeMenu,value:c,onValueChange:g,children:(0,e.jsx)(ar,{...f,ref:b})})});aB.displayName=ay;var aC="MenuRadioItem",aD=f.forwardRef((a,b)=>{let{value:c,...d}=a,f=aA(aC,a.__scopeMenu),h=c===f.value;return(0,e.jsx)(aF,{scope:a.__scopeMenu,checked:h,children:(0,e.jsx)(av,{role:"menuitemradio","aria-checked":h,...d,ref:b,"data-state":aS(h),onSelect:(0,g.composeEventHandlers)(d.onSelect,()=>f.onValueChange?.(c),{checkForDefaultPrevented:!1})})})});aD.displayName=aC;var aE="MenuItemIndicator",[aF,aG]=X(aE,{checked:!1}),aH=f.forwardRef((a,b)=>{let{__scopeMenu:c,forceMount:d,...f}=a,g=aG(aE,c);return(0,e.jsx)(t.Presence,{present:d||aR(g.checked)||!0===g.checked,children:(0,e.jsx)(k.Primitive.span,{...f,ref:b,"data-state":aS(g.checked)})})});aH.displayName=aE;var aI=f.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,e.jsx)(k.Primitive.div,{role:"separator","aria-orientation":"horizontal",...d,ref:b})});aI.displayName="MenuSeparator";var aJ=f.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,f=Z(c);return(0,e.jsx)(r.Arrow,{...f,...d,ref:b})});aJ.displayName="MenuArrow";var[aK,aL]=X("MenuSub"),aM="MenuSubTrigger",aN=f.forwardRef((a,b)=>{let c=aa(aM,a.__scopeMenu),d=ac(aM,a.__scopeMenu),i=aL(aM,a.__scopeMenu),j=al(aM,a.__scopeMenu),k=f.useRef(null),{pointerGraceTimerRef:l,onPointerGraceIntentChange:m}=j,n={__scopeMenu:a.__scopeMenu},o=f.useCallback(()=>{k.current&&window.clearTimeout(k.current),k.current=null},[]);return f.useEffect(()=>o,[o]),f.useEffect(()=>{let a=l.current;return()=>{window.clearTimeout(a),m(null)}},[l,m]),(0,e.jsx)(ae,{asChild:!0,...n,children:(0,e.jsx)(aw,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":i.contentId,"data-state":aQ(c.open),...a,ref:(0,h.composeRefs)(b,i.onTriggerChange),onClick:b=>{a.onClick?.(b),a.disabled||b.defaultPrevented||(b.currentTarget.focus(),c.open||c.onOpenChange(!0))},onPointerMove:(0,g.composeEventHandlers)(a.onPointerMove,aT(b=>{j.onItemEnter(b),!b.defaultPrevented&&(a.disabled||c.open||k.current||(j.onPointerGraceIntentChange(null),k.current=window.setTimeout(()=>{c.onOpenChange(!0),o()},100)))})),onPointerLeave:(0,g.composeEventHandlers)(a.onPointerLeave,aT(a=>{o();let b=c.content?.getBoundingClientRect();if(b){let d=c.content?.dataset.side,e="right"===d,f=b[e?"left":"right"],g=b[e?"right":"left"];j.onPointerGraceIntentChange({area:[{x:a.clientX+(e?-5:5),y:a.clientY},{x:f,y:b.top},{x:g,y:b.top},{x:g,y:b.bottom},{x:f,y:b.bottom}],side:d}),window.clearTimeout(l.current),l.current=window.setTimeout(()=>j.onPointerGraceIntentChange(null),300)}else{if(j.onTriggerLeave(a),a.defaultPrevented)return;j.onPointerGraceIntentChange(null)}})),onKeyDown:(0,g.composeEventHandlers)(a.onKeyDown,b=>{let e=""!==j.searchRef.current;a.disabled||e&&" "===b.key||R[d.dir].includes(b.key)&&(c.onOpenChange(!0),c.content?.focus(),b.preventDefault())})})})});aN.displayName=aM;var aO="MenuSubContent",aP=f.forwardRef((a,b)=>{let c=ah(aj,a.__scopeMenu),{forceMount:d=c.forceMount,...i}=a,j=aa(aj,a.__scopeMenu),k=ac(aj,a.__scopeMenu),l=aL(aO,a.__scopeMenu),m=f.useRef(null),n=(0,h.useComposedRefs)(b,m);return(0,e.jsx)(U.Provider,{scope:a.__scopeMenu,children:(0,e.jsx)(t.Presence,{present:d||j.open,children:(0,e.jsx)(U.Slot,{scope:a.__scopeMenu,children:(0,e.jsx)(aq,{id:l.contentId,"aria-labelledby":l.triggerId,...i,ref:n,align:"start",side:"rtl"===k.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:a=>{k.isUsingKeyboardRef.current&&m.current?.focus(),a.preventDefault()},onCloseAutoFocus:a=>a.preventDefault(),onFocusOutside:(0,g.composeEventHandlers)(a.onFocusOutside,a=>{a.target!==l.trigger&&j.onOpenChange(!1)}),onEscapeKeyDown:(0,g.composeEventHandlers)(a.onEscapeKeyDown,a=>{k.onClose(),a.preventDefault()}),onKeyDown:(0,g.composeEventHandlers)(a.onKeyDown,a=>{let b=a.currentTarget.contains(a.target),c=S[k.dir].includes(a.key);b&&c&&(j.onOpenChange(!1),l.trigger?.focus(),a.preventDefault())})})})})})});function aQ(a){return a?"open":"closed"}function aR(a){return"indeterminate"===a}function aS(a){return aR(a)?"indeterminate":a?"checked":"unchecked"}function aT(a){return b=>"mouse"===b.pointerType?a(b):void 0}aP.displayName=aO;var aU="DropdownMenu",[aV,aW]=(0,i.createContextScope)(aU,[Y]),aX=Y(),[aY,aZ]=aV(aU),a$=a=>{let{__scopeDropdownMenu:b,children:c,dir:d,open:g,defaultOpen:h,onOpenChange:i,modal:k=!0}=a,l=aX(b),m=f.useRef(null),[n,o]=(0,j.useControllableState)({prop:g,defaultProp:h??!1,onChange:i,caller:aU});return(0,e.jsx)(aY,{scope:b,triggerId:(0,q.useId)(),triggerRef:m,contentId:(0,q.useId)(),open:n,onOpenChange:o,onOpenToggle:f.useCallback(()=>o(a=>!a),[o]),modal:k,children:(0,e.jsx)(ad,{...l,open:n,onOpenChange:o,dir:d,modal:k,children:c})})};a$.displayName=aU;var a_="DropdownMenuTrigger",a0=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,disabled:d=!1,...f}=a,i=aZ(a_,c),j=aX(c);return(0,e.jsx)(ae,{asChild:!0,...j,children:(0,e.jsx)(k.Primitive.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":d?"":void 0,disabled:d,...f,ref:(0,h.composeRefs)(b,i.triggerRef),onPointerDown:(0,g.composeEventHandlers)(a.onPointerDown,a=>{!d&&0===a.button&&!1===a.ctrlKey&&(i.onOpenToggle(),i.open||a.preventDefault())}),onKeyDown:(0,g.composeEventHandlers)(a.onKeyDown,a=>{!d&&(["Enter"," "].includes(a.key)&&i.onOpenToggle(),"ArrowDown"===a.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});a0.displayName=a_;var a1=a=>{let{__scopeDropdownMenu:b,...c}=a,d=aX(b);return(0,e.jsx)(ai,{...d,...c})};a1.displayName="DropdownMenuPortal";var a2="DropdownMenuContent",a3=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,h=aZ(a2,c),i=aX(c),j=f.useRef(!1);return(0,e.jsx)(am,{id:h.contentId,"aria-labelledby":h.triggerId,...i,...d,ref:b,onCloseAutoFocus:(0,g.composeEventHandlers)(a.onCloseAutoFocus,a=>{j.current||h.triggerRef.current?.focus(),j.current=!1,a.preventDefault()}),onInteractOutside:(0,g.composeEventHandlers)(a.onInteractOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey,d=2===b.button||c;(!h.modal||d)&&(j.current=!0)}),style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});a3.displayName=a2,f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=aX(c);return(0,e.jsx)(ar,{...f,...d,ref:b})}).displayName="DropdownMenuGroup";var a4=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=aX(c);return(0,e.jsx)(as,{...f,...d,ref:b})});a4.displayName="DropdownMenuLabel";var a5=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=aX(c);return(0,e.jsx)(av,{...f,...d,ref:b})});a5.displayName="DropdownMenuItem",f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=aX(c);return(0,e.jsx)(ax,{...f,...d,ref:b})}).displayName="DropdownMenuCheckboxItem",f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=aX(c);return(0,e.jsx)(aB,{...f,...d,ref:b})}).displayName="DropdownMenuRadioGroup",f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=aX(c);return(0,e.jsx)(aD,{...f,...d,ref:b})}).displayName="DropdownMenuRadioItem",f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=aX(c);return(0,e.jsx)(aH,{...f,...d,ref:b})}).displayName="DropdownMenuItemIndicator";var a6=f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=aX(c);return(0,e.jsx)(aI,{...f,...d,ref:b})});a6.displayName="DropdownMenuSeparator",f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=aX(c);return(0,e.jsx)(aJ,{...f,...d,ref:b})}).displayName="DropdownMenuArrow",f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=aX(c);return(0,e.jsx)(aN,{...f,...d,ref:b})}).displayName="DropdownMenuSubTrigger",f.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,f=aX(c);return(0,e.jsx)(aP,{...f,...d,ref:b,style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent",a.i(54839),(0,b.default)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),(0,b.default)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var a7=a.i(22171);function a8({...a}){return(0,e.jsx)(a$,{"data-slot":"dropdown-menu",...a})}function a9({...a}){return(0,e.jsx)(a0,{"data-slot":"dropdown-menu-trigger",...a})}function ba({className:a,sideOffset:b=4,...c}){return(0,e.jsx)(a1,{children:(0,e.jsx)(a3,{"data-slot":"dropdown-menu-content",sideOffset:b,className:(0,a7.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",a),...c})})}function bb({className:a,inset:b,variant:c="default",...d}){return(0,e.jsx)(a5,{"data-slot":"dropdown-menu-item","data-inset":b,"data-variant":c,className:(0,a7.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...d})}function bc({className:a,inset:b,...c}){return(0,e.jsx)(a4,{"data-slot":"dropdown-menu-label","data-inset":b,className:(0,a7.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",a),...c})}function bd({className:a,...b}){return(0,e.jsx)(a6,{"data-slot":"dropdown-menu-separator",className:(0,a7.cn)("bg-border -mx-1 my-1 h-px",a),...b})}}];

//# sourceMappingURL=ad4a7_lucide-react_dist_esm_icons_ellipsis_895f8f8c.js.map