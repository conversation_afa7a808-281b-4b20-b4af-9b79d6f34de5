import 'reflect-metadata';
import { Test } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AuthController } from '../src/auth/auth.controller';
import { AuthService } from '../src/auth/auth.service';

const authServiceMock = {
  register: jest.fn().mockResolvedValue({ _id: 'u1' }),
  validateUser: jest.fn().mockResolvedValue({ _id: 'u1', username: 'a' }),
  login: jest.fn().mockResolvedValue({ access_token: 'token' }),
};

describe('AuthController', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [{ provide: AuthService, useValue: authServiceMock }],
    }).compile();

    app = moduleRef.createNestApplication();
    await app.init();
  });

  afterAll(async () => { await app.close(); });

  it('POST /auth/register', async () => {
    await request(app.getHttpServer()).post('/auth/register').send({ username: 'a', password: 'b' }).expect(201);
  });

  it('POST /auth/login', async () => {
    await request(app.getHttpServer()).post('/auth/login').send({ username: 'a', password: 'b' }).expect(201);
  });
});