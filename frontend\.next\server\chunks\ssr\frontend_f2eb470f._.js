module.exports=[32083,a=>{a.n(a.i(40836))},25838,a=>{"use strict";a.s(["ClientPageRoot",()=>R.ClientPageRoot,"ClientSegmentRoot",()=>R.ClientSegmentRoot,"GlobalError",()=>Q.default,"HTTPAccessFallbackBoundary",()=>R.HTT<PERSON>ccessFallbackBoundary,"LayoutRouter",()=><PERSON><PERSON>,"MetadataBoundary",()=>R.<PERSON>ada<PERSON>ou<PERSON>,"OutletBoundary",()=>R.<PERSON>let<PERSON>oundary,"Postpone",()=><PERSON><PERSON>,"RenderFromTemplateContext",()=>R.<PERSON>der<PERSON>rom<PERSON>emplateContext,"RootLayoutBoundary",()=>R.RootLayoutBoundary,"SegmentViewNode",()=>R.Segment<PERSON>w<PERSON>ode,"SegmentViewStateNode",()=><PERSON><PERSON><PERSON>w<PERSON>tateNode,"ViewportBoundary",()=>R.<PERSON>,"__next_app__",()=>N,"actionAsyncStorage",()=>R.actionAsyncStorage,"captureOwnerStack",()=>R.captureOwnerStack,"collectSegmentData",()=>R.collectSegmentData,"createMetadataComponents",()=>R.createMetadataComponents,"createPrerenderParamsForClientSegment",()=>R.createPrerenderParamsForClientSegment,"createPrerenderSearchParamsForClientPage",()=>R.createPrerenderSearchParamsForClientPage,"createServerParamsForServerSegment",()=>R.createServerParamsForServerSegment,"createServerSearchParamsForServerPage",()=>R.createServerSearchParamsForServerPage,"createTemporaryReferenceSet",()=>R.createTemporaryReferenceSet,"decodeAction",()=>R.decodeAction,"decodeFormState",()=>R.decodeFormState,"decodeReply",()=>R.decodeReply,"handler",()=>P,"pages",()=>M,"patchFetch",()=>R.patchFetch,"preconnect",()=>R.preconnect,"preloadFont",()=>R.preloadFont,"preloadStyle",()=>R.preloadStyle,"prerender",()=>R.prerender,"renderToReadableStream",()=>R.renderToReadableStream,"routeModule",()=>O,"serverHooks",()=>R.serverHooks,"taintObjectReference",()=>R.taintObjectReference,"tree",()=>L,"workAsyncStorage",()=>R.workAsyncStorage,"workUnitAsyncStorage",()=>R.workUnitAsyncStorage],25838),a.s(["__next_app__",()=>N,"handler",()=>P,"pages",()=>M,"routeModule",()=>O,"tree",()=>L],94348);var b=a.i(93273),c=a.i(55896),d=a.i(36399),e=a.i(26543),f=a.i(25350),g=a.i(9603),h=a.i(62049),i=a.i(89068),j=a.i(32083),k=a.i(41792),l=a.i(96314),m=a.i(71002),n=a.i(19017),o=a.i(80913),p=a.i(48747),q=a.i(74213),r=a.i(89574),s=a.i(63351),t=a.i(98835),u=a.i(53056),v=a.i(62549),w=a.i(76078),x=a.i(73649),y=a.i(88119),z=a.i(94831),A=a.i(4231),B=a.i(43589);a.i(83516);var C=a.i(1178),D=a.i(67365),E=a.i(40819),F=a.i(73994),G=a.i(15793),H=a.i(87118),I=a.i(93695);a.i(19528);var J=a.i(98641),K=a.i(8076);let L=["",{children:["dashboard",{children:["__PAGE__",{},{metadata:{},page:[()=>j,"[project]/frontend/src/app/dashboard/page.tsx"]}]},{metadata:{},layout:[()=>i,"[project]/frontend/src/app/dashboard/layout.tsx"]}]},{metadata:{icon:[async a=>[{url:(0,b.fillMetadataSegment)("//",await a.params,"favicon.ico")+`?${c.default.src.split("/").splice(-1)[0]}`,sizes:`${c.default.width}x${c.default.height}`,type:"image/x-icon"}]]},layout:[()=>d,"[project]/frontend/src/app/layout.tsx"],"not-found":[()=>e,"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/not-found.js"],forbidden:[()=>f,"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>g,"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/unauthorized.js"],"global-error":[()=>h,"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js"]}],M=["[project]/frontend/src/app/dashboard/page.tsx"],N={require:a.r.bind(a),loadChunk:a.l.bind(a)},O=new k.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:L},distDir:".next",relativeProjectDir:""});async function P(a,b,c){var d;let e="/dashboard/page";e=e.replace(/\/index$/,"")||"/";let f=(0,o.getRequestMeta)(a,"postponed"),g=(0,o.getRequestMeta)(a,"minimalMode"),i=await O.prepare(a,b,{srcPage:e,multiZoneDraftMode:!1});if(!i)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:j,query:k,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac,interceptionRoutePatterns:ad}=i,ae=R.pathname||"/",af=(0,y.normalizeAppPath)(e),{isOnDemandRevalidate:ag}=i,ah=O.match(ae,Z),ai=!!Z.routes[_],aj=!!(ah||ai||Z.routes[af]),ak=a.headers["user-agent"]||"",al=(0,B.getBotType)(ak),am=(0,w.isHtmlBotRequest)(a),an=(0,o.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[A.NEXT_ROUTER_PREFETCH_HEADER],ao=(0,o.getRequestMeta)(a,"isRSCRequest")??!!a.headers[A.RSC_HEADER],ap=(0,z.getIsPossibleServerAction)(a),aq=(0,t.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[af]??Z.dynamicRoutes[af])?void 0:d.renderingMode)==="PARTIALLY_STATIC",ar=!1,as=!1,at=aq?f:void 0,au=aq&&ao&&!an,av=(0,o.getRequestMeta)(a,"segmentPrefetchRSCRequest"),aw=!ak||(0,w.shouldServeStreamingMetadata)(ak,ac.htmlLimitedBots);am&&aq&&(aj=!1,aw=!1);let ax=!0===O.isDev||!aj||"string"==typeof f||au,ay=am&&aq,az=null;$||!aj||ax||ap||at||au||(az=_);let aA=az;!aA&&O.isDev&&(aA=_),O.isDev||$||!aj||!ao||au||(0,r.stripFlightHeaders)(a.headers);let aB={...J,tree:L,pages:M,GlobalError:h.default,handler:P,routeModule:O,__next_app__:N};W&&X&&(0,v.setReferenceManifestsSingleton)({page:e,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,x.createServerModuleMap)({serverActionsManifest:W})});let aC=a.method||"GET",aD=(0,n.getTracer)(),aE=aD.getActiveScopeSpan();try{let d=O.getVaryHeader(_,ad);b.setHeader("Vary",d);let f=async(c,d)=>{let e=new s.NodeNextRequest(a),f=new s.NodeNextResponse(b);return O.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aD.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==p.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aC} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aC} ${a.url}`)})},h=async({span:d,postponed:g,fallbackRouteParams:h})=>{let i={query:k,params:Q,page:af,sharedContext:{buildId:j},serverComponentsHmrCache:(0,o.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:h,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aB,Component:(0,q.interopDefault)(aB),params:Q,routeModule:O,page:e,postponed:g,shouldWaitOnAllReady:ay,serveStreamingMetadata:aw,supportsDynamicResponse:"string"==typeof g||ax,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:require("path").join(process.cwd(),O.relativeProjectDir),isDraftMode:$,isRevalidate:aj&&!g&&!au,botType:al,isOnDemandRevalidate:ag,isPossibleServerAction:ap,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,o.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...ar?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:ar}:{},experimental:{isRoutePPREnabled:aq,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,cacheComponents:!!ac.experimental.cacheComponents,clientSegmentCache:!!ac.experimental.clientSegmentCache,clientParamParsing:!!ac.experimental.clientParamParsing,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>O.onRequestError(a,b,d,ab),err:(0,o.getRequestMeta)(a,"invokeError"),dev:O.isDev}},l=await f(d,i),{metadata:m}=l,{cacheControl:n,headers:p={},fetchTags:r}=m;if(r&&(p[F.NEXT_CACHE_TAGS_HEADER]=r),a.fetchMetrics=m.fetchMetrics,aj&&(null==n?void 0:n.revalidate)===0&&!O.isDev&&!aq){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:C.CachedRouteKind.APP_PAGE,html:l,headers:p,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},i=async({hasResolved:d,previousCacheEntry:e,isRevalidating:f,span:i})=>{let j,k=!1===O.isDev,m=d||b.writableEnded;if(ag&&aa&&!e&&!g)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ah&&(j=(0,D.parseFallbackField)(ah.fallback)),j===D.FallbackMode.PRERENDER&&(0,B.isBot)(ak)&&(!aq||am)&&(j=D.FallbackMode.BLOCKING_STATIC_RENDER),(null==e?void 0:e.isStale)===-1&&(ag=!0),ag&&(j!==D.FallbackMode.NOT_FOUND||e)&&(j=D.FallbackMode.BLOCKING_STATIC_RENDER),!g&&j!==D.FallbackMode.BLOCKING_STATIC_RENDER&&aA&&!m&&!$&&S&&(k||!ai)){let b;if((k||ah)&&j===D.FallbackMode.NOT_FOUND)throw new I.NoFallbackError;if(aq&&!ao){let d="string"==typeof(null==ah?void 0:ah.fallback)?ah.fallback:k?af:null;if(b=await O.handleResponse({cacheKey:d,req:a,nextConfig:ac,routeKind:l.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:aq,responseGenerator:async()=>h({span:i,postponed:void 0,fallbackRouteParams:k||as?(0,u.getFallbackRouteParams)(af):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let n=ag||f||!at?void 0:at;if(ar&&void 0!==n)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:C.CachedRouteKind.PAGES,html:E.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=S&&aq&&((0,o.getRequestMeta)(a,"renderFallbackShell")||as)?(0,u.getFallbackRouteParams)(ae):null;return h({span:i,postponed:n,fallbackRouteParams:p})},m=async d=>{var e,f,j,k,m;let n,p=await O.handleResponse({cacheKey:az,responseGenerator:a=>i({span:d,...a}),routeKind:l.RouteKind.APP_PAGE,isOnDemandRevalidate:ag,isRoutePPREnabled:aq,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),O.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!p){if(az)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(e=p.value)?void 0:e.kind)!==C.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=p.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let q="string"==typeof p.value.postponed;aj&&!au&&(!q||an)&&(g||b.setHeader("x-nextjs-cache",ag?"REVALIDATED":p.isMiss?"MISS":p.isStale?"STALE":"HIT"),b.setHeader(A.NEXT_IS_PRERENDER_HEADER,"1"));let{value:r}=p;if(at)n={revalidate:0,expire:void 0};else if(g&&ao&&!an&&aq)n={revalidate:0,expire:void 0};else if(!O.isDev)if($)n={revalidate:0,expire:void 0};else if(aj){if(p.cacheControl)if("number"==typeof p.cacheControl.revalidate){if(p.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${p.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:p.cacheControl.revalidate,expire:(null==(k=p.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:F.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(p.cacheControl=n,"string"==typeof av&&(null==r?void 0:r.kind)===C.CachedRouteKind.APP_PAGE&&r.segmentData){b.setHeader(A.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=r.headers)?void 0:m[F.NEXT_CACHE_TAGS_HEADER];g&&aj&&c&&"string"==typeof c&&b.setHeader(F.NEXT_CACHE_TAGS_HEADER,c);let d=r.segmentData.get(av);return void 0!==d?(0,H.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:E.default.fromStatic(d,A.RSC_CONTENT_TYPE_HEADER),cacheControl:p.cacheControl}):(b.statusCode=204,(0,H.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:E.default.EMPTY,cacheControl:p.cacheControl}))}let s=(0,o.getRequestMeta)(a,"onCacheEntry");if(s&&await s({...p,value:{...p.value,kind:"PAGE"}},{url:(0,o.getRequestMeta)(a,"initURL")}))return null;if(q&&at)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(r.headers){let a={...r.headers};for(let[c,d]of(g&&aj||delete a[F.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(f=r.headers)?void 0:f[F.NEXT_CACHE_TAGS_HEADER];if(g&&aj&&t&&"string"==typeof t&&b.setHeader(F.NEXT_CACHE_TAGS_HEADER,t),!r.status||ao&&aq||(b.statusCode=r.status),!g&&r.status&&K.RedirectStatusCode[r.status]&&ao&&(b.statusCode=200),q&&b.setHeader(A.NEXT_DID_POSTPONE_HEADER,"1"),ao&&!$){if(void 0===r.rscData){if(r.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,H.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:r.html,cacheControl:au?{revalidate:0,expire:void 0}:p.cacheControl})}return(0,H.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:E.default.fromStatic(r.rscData,A.RSC_CONTENT_TYPE_HEADER),cacheControl:p.cacheControl})}let u=r.html;if(!q||g||ao)return(0,H.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:u,cacheControl:p.cacheControl});if(ar)return u.push(new ReadableStream({start(a){a.enqueue(G.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,H.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let v=new TransformStream;return u.push(v.readable),h({span:d,postponed:r.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==C.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(v.writable)}).catch(a=>{v.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,H.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aE)return await aD.withPropagatedContext(a.headers,()=>aD.trace(p.BaseServerSpan.handleRequest,{spanName:`${aC} ${a.url}`,kind:n.SpanKind.SERVER,attributes:{"http.method":aC,"http.target":a.url}},m));await m(aE)}catch(b){throw aE||b instanceof I.NoFallbackError||await O.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"render",revalidateReason:(0,m.getRevalidateReason)({isRevalidate:aj,isOnDemandRevalidate:ag})},ab),b}}a.i(94348);var Q=h,R=J}];

//# sourceMappingURL=frontend_f2eb470f._.js.map