"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ArrowLeft, Wallet, DollarSign, Trash2 } from "lucide-react";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useApi } from "@/hooks/use-api";
import { <PERSON><PERSON><PERSON>, CaisseForm, CaisseType, Session } from "@/types";

const caisseSchema = z
	.object({
		nom: z
			.string()
			.min(1, "Le nom est requis")
			.max(100, "Le nom est trop long"),
		type: z.nativeEnum(CaisseType, {
			required_error: "Le type est requis",
		}),
		soldeActuel: z
			.number()
			.min(0, "Le solde ne peut pas être négatif")
			.max(10000000, "Le solde ne peut pas dépasser 10,000,000 FCFA"),
		sessionId: z.string().optional(),
		caissePrincipaleId: z.string().optional(),
	})
	.refine(
		(data) => {
			if (data.type === CaisseType.REUNION) {
				return data.sessionId && data.caissePrincipaleId;
			}
			return true;
		},
		{
			message:
				"Pour une caisse de réunion, la session et la caisse principale sont requises",
			path: ["sessionId"],
		},
	);

export default function EditCaissePage() {
	const { id: caisseId } = useParams();
	const { data: session, status } = useSession();
	const router = useRouter();
	const api = useApi();

	const [caisseData, setCaisseData] = useState<Caisse | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [loading, setLoading] = useState(true);
	const [sessions, setSessions] = useState<Session[]>([]);
	const [caissesPrincipales, setCaissesPrincipales] = useState<Caisse[]>([]);

	// Vérifier les permissions
	const canEditCaisses =
		session?.user && (session.user as any).role === "secretary_general";

	const form = useForm<CaisseForm>({
		resolver: zodResolver(caisseSchema),
		defaultValues: {
			nom: "",
			type: CaisseType.PRINCIPALE,
			soldeActuel: 0,
			sessionId: "",
			caissePrincipaleId: "",
		},
	});

	const watchType = form.watch("type");

	// Charger les données
	useEffect(() => {
		const loadData = async () => {
			if (!caisseId || typeof caisseId !== "string") return;

			try {
				setLoading(true);
				const [caisse, sessionsData, caissesData] = await Promise.all([
					api.getCaisse(caisseId),
					api.getSessions(),
					api.getCaisses(),
				]);

				setCaisseData(caisse);
				setSessions(sessionsData);
				setCaissesPrincipales(
					caissesData.filter(
						(c) => c.type === CaisseType.PRINCIPALE && c._id !== caisse._id,
					),
				);

				// Mettre à jour le formulaire
				form.reset({
					nom: caisse.nom,
					type: caisse.type,
					soldeActuel: caisse.soldeActuel,
					sessionId: caisse.sessionId || "",
					caissePrincipaleId: caisse.caissePrincipaleId || "",
				});
			} catch (error) {
				console.error("Erreur lors du chargement:", error);
				setError("Caisse introuvable");
			} finally {
				setLoading(false);
			}
		};

		if (session?.accessToken) {
			loadData();
		}
	}, [caisseId, status]);

	const onSubmit = async (data: CaisseForm) => {
		if (!canEditCaisses || !caisseId || typeof caisseId !== "string") {
			setError("Vous n'avez pas les permissions pour modifier cette caisse");
			return;
		}

		try {
			setIsLoading(true);
			setError(null);

			// Préparer les données
			const updateData = {
				nom: data.nom,
				type: data.type,
				soldeActuel: data.soldeActuel,
				...(data.type === CaisseType.REUNION && {
					sessionId: data.sessionId,
					caissePrincipaleId: data.caissePrincipaleId,
				}),
			};

			await api.updateCaisse(caisseId, updateData);
			router.push("/dashboard/caisses");
		} catch (error: any) {
			console.error("Erreur lors de la modification:", error);
			setError(
				error.message || "Une erreur est survenue lors de la modification",
			);
		} finally {
			setIsLoading(false);
		}
	};

	const handleDelete = async () => {
		if (
			!canEditCaisses ||
			!caisseId ||
			typeof caisseId !== "string" ||
			!caisseData
		) {
			return;
		}

		if (
			!confirm(
				`Êtes-vous sûr de vouloir supprimer la caisse "${caisseData.nom}" ?`,
			)
		) {
			return;
		}

		try {
			setIsDeleting(true);
			await api.deleteCaisse(caisseId);
			router.push("/dashboard/caisses");
		} catch (error: any) {
			console.error("Erreur lors de la suppression:", error);
			setError(
				error.message || "Une erreur est survenue lors de la suppression",
			);
		} finally {
			setIsDeleting(false);
		}
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
					<p className="mt-2 text-sm text-gray-600">
						Chargement de la caisse...
					</p>
				</div>
			</div>
		);
	}

	if (!canEditCaisses) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Button variant="outline" size="icon" asChild>
						<Link href="/dashboard/caisses">
							<ArrowLeft className="h-4 w-4" />
						</Link>
					</Button>
					<div>
						<h1 className="text-3xl font-bold tracking-tight">
							Modifier Caisse
						</h1>
						<p className="text-muted-foreground">
							Modifier les paramètres de la caisse
						</p>
					</div>
				</div>

				<Card>
					<CardContent className="pt-6">
						<div className="text-center py-8">
							<p className="text-muted-foreground">
								Vous n'avez pas les permissions pour modifier cette caisse.
							</p>
							<p className="text-sm text-muted-foreground mt-2">
								Seuls les administrateurs et trésoriers peuvent modifier les
								caisses.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	if (!caisseData) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Button variant="outline" size="icon" asChild>
						<Link href="/dashboard/caisses">
							<ArrowLeft className="h-4 w-4" />
						</Link>
					</Button>
					<div>
						<h1 className="text-3xl font-bold tracking-tight">
							Caisse introuvable
						</h1>
					</div>
				</div>

				<Card>
					<CardContent className="pt-6">
						<div className="text-center py-8">
							<p className="text-muted-foreground">
								La caisse demandée n'a pas été trouvée.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-4">
					<Button variant="outline" size="icon" asChild>
						<Link href="/dashboard/caisses">
							<ArrowLeft className="h-4 w-4" />
						</Link>
					</Button>
					<div>
						<h1 className="text-3xl font-bold tracking-tight">
							Modifier {caisseData.nom}
						</h1>
						<p className="text-muted-foreground">
							Modifier les paramètres de la caisse
						</p>
					</div>
				</div>

				<Button
					variant="destructive"
					onClick={handleDelete}
					disabled={isDeleting}
				>
					<Trash2 className="mr-2 h-4 w-4" />
					{isDeleting ? "Suppression..." : "Supprimer"}
				</Button>
			</div>

			{/* Formulaire */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Wallet className="h-5 w-5" />
						Informations de la caisse
					</CardTitle>
					<CardDescription>
						Modifiez les paramètres de la caisse
					</CardDescription>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							{error && (
								<div className="bg-red-50 border border-red-200 rounded-md p-4">
									<p className="text-sm text-red-600">{error}</p>
								</div>
							)}

							<div className="grid gap-6 md:grid-cols-2">
								<FormField
									control={form.control}
									name="nom"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Nom de la caisse</FormLabel>
											<FormControl>
												<Input
													placeholder="Ex: Caisse Principale 2025"
													{...field}
												/>
											</FormControl>
											<FormDescription>
												Nom descriptif de la caisse
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="type"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Type de caisse</FormLabel>
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Sélectionnez le type" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value={CaisseType.PRINCIPALE}>
														Principale
													</SelectItem>
													<SelectItem value={CaisseType.REUNION}>
														Réunion
													</SelectItem>
												</SelectContent>
											</Select>
											<FormDescription>
												{watchType === CaisseType.PRINCIPALE
													? "Caisse pour les fonds consolidés"
													: "Caisse liée à une session spécifique"}
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<FormField
								control={form.control}
								name="soldeActuel"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-2">
											<DollarSign className="h-4 w-4" />
											Solde actuel (FCFA)
										</FormLabel>
										<FormControl>
											<Input
												type="number"
												{...field}
												onChange={(e) =>
													field.onChange(parseInt(e.target.value) || 0)
												}
											/>
										</FormControl>
										<FormDescription>
											Montant actuel dans la caisse
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							{watchType === CaisseType.REUNION && (
								<div className="grid gap-6 md:grid-cols-2">
									<FormField
										control={form.control}
										name="sessionId"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Session associée</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Sélectionnez une session" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{sessions.map((session) => (
															<SelectItem key={session._id} value={session._id}>
																{session.annee} (
																{new Date(
																	session.dateDebut,
																).toLocaleDateString()}{" "}
																-{" "}
																{new Date(session.dateFin).toLocaleDateString()}
																)
															</SelectItem>
														))}
													</SelectContent>
												</Select>
												<FormDescription>
													Session à laquelle cette caisse est liée
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="caissePrincipaleId"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Caisse principale</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Sélectionnez une caisse principale" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{caissesPrincipales.map((caisse) => (
															<SelectItem key={caisse._id} value={caisse._id}>
																{caisse.nom} (
																{caisse.soldeActuel.toLocaleString()} FCFA)
															</SelectItem>
														))}
													</SelectContent>
												</Select>
												<FormDescription>
													Caisse principale pour l'émargement
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							)}

							<div className="flex justify-end gap-4">
								<Button variant="outline" asChild>
									<Link href="/dashboard/caisses">Annuler</Link>
								</Button>
								<Button type="submit" disabled={isLoading}>
									{isLoading ? "Modification..." : "Modifier la caisse"}
								</Button>
							</div>
						</form>
					</Form>
				</CardContent>
			</Card>
		</div>
	);
}
