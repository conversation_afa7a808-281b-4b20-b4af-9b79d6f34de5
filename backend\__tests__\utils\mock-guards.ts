import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

@Injectable()
export class MockJwtAuthGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const req = context.switchToHttp().getRequest();
    const role = req.headers['x-user-role'] || 'secretary_general';
    const userId = req.headers['x-user-id'] || 'test-user-id';
    req.user = { _id: String(userId), role: String(role) };
    return true;
  }
}

@Injectable()
export class MockRolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}
  canActivate(context: ExecutionContext): boolean {
    const required = this.reflector.get<string[] | undefined>('roles', context.getHandler())
      || this.reflector.get<string[] | undefined>('roles', context.getClass());
    if (!required || required.length === 0) return true;

    const req = context.switchToHttp().getRequest();
    const userRole: string = (req.user?.role || '').toString();

    // normalize enum values like 'SECRETARY_GENERAL' and string roles like 'secretary_general'
    const normalize = (r: string) => r.toString().toLowerCase();

    const ok = required.some(r => normalize(r) === normalize(userRole));
    return ok;
  }
}