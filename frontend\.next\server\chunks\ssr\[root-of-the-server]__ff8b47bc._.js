module.exports=[24725,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},62303,a=>{"use strict";a.s(["DollarSign",()=>b],62303);let b=(0,a.i(621).default)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6601,a=>{"use strict";a.s(["Trash2",()=>b],6601);let b=(0,a.i(621).default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},41405,a=>{"use strict";a.s(["Label",()=>g],41405);var b=a.i(68116),c=a.i(128),d=a.i(48206),e=c.forwardRef((a,c)=>(0,b.jsx)(d.Primitive.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));e.displayName="Label";var f=a.i(22171);function g({className:a,...c}){return(0,b.jsx)(e,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...c})}},12594,a=>{"use strict";a.s(["useApi",()=>d]);var b=a.i(81223),c=a.i(34075);function d(){let{data:a}=(0,b.useSession)(),d=async(b,d={})=>{if(!a?.accessToken)throw Error("Non authentifié");return c.apiService.authenticatedRequest(b,a.accessToken,d)};return{login:c.apiService.login.bind(c.apiService),register:c.apiService.register.bind(c.apiService),authenticatedRequest:d,getUsers:()=>d("/users"),getUser:a=>d(`/users/${a}`),createUser:a=>d("/users",{method:"POST",body:JSON.stringify(a)}),updateUser:(a,b)=>d(`/users/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteUser:a=>d(`/users/${a}`,{method:"DELETE"}),getSessions:()=>d("/sessions"),getSession:a=>d(`/sessions/${a}`),createSession:a=>d("/sessions",{method:"POST",body:JSON.stringify(a)}),updateSession:(a,b)=>d(`/sessions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteSession:a=>d(`/sessions/${a}`,{method:"DELETE"}),getCaisses:()=>d("/caisses"),getCaisse:a=>d(`/caisses/${a}`),createCaisse:a=>d("/caisses",{method:"POST",body:JSON.stringify(a)}),updateCaisse:(a,b)=>d(`/caisses/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteCaisse:a=>d(`/caisses/${a}`,{method:"DELETE"}),emargerCaisse:a=>d(`/caisses/${a}/emarger`,{method:"POST"}),getReunions:()=>d("/reunions"),getReunion:a=>d(`/reunions/${a}`),updateReunion:(a,b)=>d(`/reunions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),getMembers:()=>d("/members"),getMember:a=>d(`/members/${a}`),createMember:a=>d("/members",{method:"POST",body:JSON.stringify(a)}),updateMember:(a,b)=>d(`/members/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteMember:a=>d(`/members/${a}`,{method:"DELETE"}),getMemberDebrief:(a,b)=>{let c=new URLSearchParams;b?.dateFrom&&c.append("dateFrom",b.dateFrom),b?.dateTo&&c.append("dateTo",b.dateTo),b?.sessionId&&c.append("sessionId",b.sessionId);let e=c.toString()?`?${c.toString()}`:"";return d(`/members/${a}/debrief${e}`)},createPayment:a=>d("/payments",{method:"POST",body:JSON.stringify(a)}),getSessionMembers:a=>d(`/sessions/${a}/members`),addSessionMember:a=>d("/session-members",{method:"POST",body:JSON.stringify(a)}),removeSessionMember:(a,b)=>d(`/sessions/${a}/members/${b}`,{method:"DELETE"})}}},6821,a=>{"use strict";a.s(["ArrowLeft",()=>b],6821);let b=(0,a.i(621).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8641,a=>{"use strict";a.s(["default",()=>t]);var b=a.i(68116),c=a.i(128),d=a.i(81223),e=a.i(50395),f=a.i(64853),g=a.i(54636),h=a.i(66446),i=a.i(6821),j=a.i(20762),k=a.i(62303),l=a.i(6601),m=a.i(33055),n=a.i(2979),o=a.i(78184),p=a.i(75780),q=a.i(60563),r=a.i(12594);let s=h.z.object({annee:h.z.number().min(2020,"L'année doit être supérieure à 2020").max(2050,"L'année doit être inférieure à 2050"),dateDebut:h.z.string().min(1,"La date de début est requise"),dateFin:h.z.string().min(1,"La date de fin est requise"),partFixe:h.z.number().min(1,"La part fixe doit être supérieure à 0").max(1e6,"La part fixe ne peut pas dépasser 1,000,000 FCFA")}).refine(a=>{let b=new Date(a.dateDebut);return new Date(a.dateFin)>b},{message:"La date de fin doit être postérieure à la date de début",path:["dateFin"]});function t(){let{id:a}=(0,e.useParams)(),{data:h,status:t}=(0,d.useSession)(),u=(0,e.useRouter)(),v=(0,r.useApi)(),[w,x]=(0,c.useState)(null),[y,z]=(0,c.useState)(!1),[A,B]=(0,c.useState)(!1),[C,D]=(0,c.useState)(null),[E,F]=(0,c.useState)(!0),G=h?.user&&"secretary_general"===h.user.role,H=(0,f.useForm)({resolver:(0,g.zodResolver)(s),defaultValues:{annee:new Date().getFullYear(),dateDebut:"",dateFin:"",partFixe:0}});(0,c.useEffect)(()=>{let b=async()=>{if(a&&"string"==typeof a)try{F(!0);let b=await v.getSession(a);x(b),H.reset({annee:b.annee,dateDebut:b.dateDebut.split("T")[0],dateFin:b.dateFin.split("T")[0],partFixe:b.partFixe})}catch(a){console.error("Erreur lors du chargement:",a),D("Session introuvable")}finally{F(!1)}};h?.accessToken&&b()},[a,t]);let I=async b=>{if(!G||!a||"string"!=typeof a)return void D("Vous n'avez pas les permissions pour modifier cette session");try{z(!0),D(null),await v.updateSession(a,b),u.push("/dashboard/sessions")}catch(a){console.error("Erreur lors de la modification:",a),D(a.message||"Une erreur est survenue lors de la modification")}finally{z(!1)}},J=async()=>{if(G&&a&&"string"==typeof a&&w&&confirm(`\xcates-vous s\xfbr de vouloir supprimer la session ${w.annee} ? Cette action supprimera \xe9galement toutes les r\xe9unions associ\xe9es.`))try{B(!0),await v.deleteSession(a),u.push("/dashboard/sessions")}catch(a){console.error("Erreur lors de la suppression:",a),D(a.message||"Une erreur est survenue lors de la suppression")}finally{B(!1)}};return E?(0,b.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"}),(0,b.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Chargement de la session..."})]})}):G?w?(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(n.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,b.jsx)(m.default,{href:"/dashboard/sessions",children:(0,b.jsx)(i.ArrowLeft,{className:"h-4 w-4"})})}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("h1",{className:"text-3xl font-bold tracking-tight",children:["Modifier Session ",w.annee]}),(0,b.jsx)("p",{className:"text-muted-foreground",children:"Modifier les paramètres de la session"})]})]}),(0,b.jsxs)(n.Button,{variant:"destructive",onClick:J,disabled:A,children:[(0,b.jsx)(l.Trash2,{className:"mr-2 h-4 w-4"}),A?"Suppression...":"Supprimer"]})]}),(0,b.jsxs)(p.Card,{children:[(0,b.jsxs)(p.CardHeader,{children:[(0,b.jsxs)(p.CardTitle,{className:"flex items-center gap-2",children:[(0,b.jsx)(j.Calendar,{className:"h-5 w-5"}),"Informations de la session"]}),(0,b.jsx)(p.CardDescription,{children:"Modifiez les paramètres de la session. Attention : les modifications peuvent affecter les réunions existantes."})]}),(0,b.jsx)(p.CardContent,{children:(0,b.jsx)(q.Form,{...H,children:(0,b.jsxs)("form",{onSubmit:H.handleSubmit(I),className:"space-y-6",children:[C&&(0,b.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,b.jsx)("p",{className:"text-sm text-red-600",children:C})}),(0,b.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,b.jsx)(q.FormField,{control:H.control,name:"annee",render:({field:a})=>(0,b.jsxs)(q.FormItem,{children:[(0,b.jsx)(q.FormLabel,{children:"Année"}),(0,b.jsx)(q.FormControl,{children:(0,b.jsx)(o.Input,{type:"number",...a,onChange:b=>a.onChange(parseInt(b.target.value)||0)})}),(0,b.jsx)(q.FormDescription,{children:"L'année de la session de tontine"}),(0,b.jsx)(q.FormMessage,{})]})}),(0,b.jsx)(q.FormField,{control:H.control,name:"partFixe",render:({field:a})=>(0,b.jsxs)(q.FormItem,{children:[(0,b.jsxs)(q.FormLabel,{className:"flex items-center gap-2",children:[(0,b.jsx)(k.DollarSign,{className:"h-4 w-4"}),"Part Fixe (FCFA)"]}),(0,b.jsx)(q.FormControl,{children:(0,b.jsx)(o.Input,{type:"number",...a,onChange:b=>a.onChange(parseInt(b.target.value)||0)})}),(0,b.jsx)(q.FormDescription,{children:"Montant de la cotisation fixe par réunion"}),(0,b.jsx)(q.FormMessage,{})]})})]}),(0,b.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,b.jsx)(q.FormField,{control:H.control,name:"dateDebut",render:({field:a})=>(0,b.jsxs)(q.FormItem,{children:[(0,b.jsx)(q.FormLabel,{children:"Date de début"}),(0,b.jsx)(q.FormControl,{children:(0,b.jsx)(o.Input,{type:"date",...a})}),(0,b.jsx)(q.FormDescription,{children:"Date de début de la session"}),(0,b.jsx)(q.FormMessage,{})]})}),(0,b.jsx)(q.FormField,{control:H.control,name:"dateFin",render:({field:a})=>(0,b.jsxs)(q.FormItem,{children:[(0,b.jsx)(q.FormLabel,{children:"Date de fin"}),(0,b.jsx)(q.FormControl,{children:(0,b.jsx)(o.Input,{type:"date",...a})}),(0,b.jsx)(q.FormDescription,{children:"Date de fin de la session"}),(0,b.jsx)(q.FormMessage,{})]})})]}),(0,b.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,b.jsx)(n.Button,{variant:"outline",asChild:!0,children:(0,b.jsx)(m.default,{href:"/dashboard/sessions",children:"Annuler"})}),(0,b.jsx)(n.Button,{type:"submit",disabled:y,children:y?"Modification...":"Modifier la session"})]})]})})})]})]}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(n.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,b.jsx)(m.default,{href:"/dashboard/sessions",children:(0,b.jsx)(i.ArrowLeft,{className:"h-4 w-4"})})}),(0,b.jsx)("div",{children:(0,b.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Session introuvable"})})]}),(0,b.jsx)(p.Card,{children:(0,b.jsx)(p.CardContent,{className:"pt-6",children:(0,b.jsx)("div",{className:"text-center py-8",children:(0,b.jsx)("p",{className:"text-muted-foreground",children:"La session demandée n'a pas été trouvée."})})})})]}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(n.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,b.jsx)(m.default,{href:"/dashboard/sessions",children:(0,b.jsx)(i.ArrowLeft,{className:"h-4 w-4"})})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Modifier Session"}),(0,b.jsx)("p",{className:"text-muted-foreground",children:"Modifier les paramètres de la session"})]})]}),(0,b.jsx)(p.Card,{children:(0,b.jsx)(p.CardContent,{className:"pt-6",children:(0,b.jsxs)("div",{className:"text-center py-8",children:[(0,b.jsx)("p",{className:"text-muted-foreground",children:"Vous n'avez pas les permissions pour modifier cette session."}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Seuls les administrateurs peuvent modifier les sessions."})]})})})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__ff8b47bc._.js.map