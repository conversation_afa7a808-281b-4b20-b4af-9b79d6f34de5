(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,72355,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return s}});let s=e=>{}},96134,e=>{"use strict";e.s(["Input",()=>s]);var r=e.i(4051),t=e.i(41428);function s(e){let{className:s,type:i,...n}=e;return(0,r.jsx)("input",{type:i,"data-slot":"input",className:(0,t.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...n})}},38909,e=>{"use strict";e.s(["Primitive",()=>n,"dispatchDiscreteCustomEvent",()=>a]);var r=e.i(38477),t=e.i(41902),s=e.i(81221),i=e.i(4051),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,s.createSlot)("Primitive.".concat(t)),a=r.forwardRef((e,r)=>{let{asChild:s,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(s?n:t,{...a,ref:r})});return a.displayName="Primitive.".concat(t),{...e,[t]:a}},{});function a(e,r){e&&t.flushSync(()=>e.dispatchEvent(r))}},65429,e=>{"use strict";e.s(["Label",()=>a],65429);var r=e.i(4051),t=e.i(38477),s=e.i(38909),i=t.forwardRef((e,t)=>(0,r.jsx)(s.Primitive.label,{...e,ref:t,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var n=e.i(41428);function a(e){let{className:t,...s}=e;return(0,r.jsx)(i,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}},68185,e=>{"use strict";e.s(["default",()=>p]);var r=e.i(4051),t=e.i(38477),s=e.i(1269),i=e.i(57691),n=e.i(67967),a=e.i(78381),o=e.i(45086),l=e.i(5085),d=e.i(85205),c=e.i(96134),u=e.i(75680),m=e.i(5647);let x=o.z.object({username:o.z.string().min(1,"Username is required"),password:o.z.string().min(4,"Password is required")});function p(){let[e,o]=(0,t.useState)(!1),[p,f]=(0,t.useState)(null),b=(0,i.useRouter)(),v=(0,n.useForm)({resolver:(0,a.zodResolver)(x),defaultValues:{username:"",password:""}}),h=async e=>{o(!0),f(null);try{let r=await (0,s.signIn)("credentials",{username:e.username,password:e.password,redirect:!1});(null==r?void 0:r.error)?f("Nom d'utilisateur ou mot de passe incorrect"):b.push("/dashboard")}catch(e){console.error("Erreur de connexion:",e),f("Erreur de connexion au serveur. Veuillez réessayer.")}finally{o(!1)}};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)(u.Card,{className:"w-full max-w-md",children:[(0,r.jsxs)(u.CardHeader,{className:"space-y-1",children:[(0,r.jsx)(u.CardTitle,{className:"text-2xl font-bold text-center",children:"Connexion"}),(0,r.jsx)(u.CardDescription,{className:"text-center",children:"Connectez-vous à votre compte Tontine"})]}),(0,r.jsxs)(u.CardContent,{children:[(0,r.jsx)(m.Form,{...v,children:(0,r.jsxs)("form",{onSubmit:v.handleSubmit(h),className:"space-y-4",children:[(0,r.jsx)(m.FormField,{control:v.control,name:"username",render:t=>{let{field:s}=t;return(0,r.jsxs)(m.FormItem,{children:[(0,r.jsx)(m.FormLabel,{children:"Nom d'utilisateur"}),(0,r.jsx)(m.FormControl,{children:(0,r.jsx)(c.Input,{placeholder:"Entrez votre nom d'utilisateur",...s,disabled:e})}),(0,r.jsx)(m.FormMessage,{})]})}}),(0,r.jsx)(m.FormField,{control:v.control,name:"password",render:t=>{let{field:s}=t;return(0,r.jsxs)(m.FormItem,{children:[(0,r.jsx)(m.FormLabel,{children:"Mot de passe"}),(0,r.jsx)(m.FormControl,{children:(0,r.jsx)(c.Input,{type:"password",placeholder:"Entrez votre mot de passe",...s,disabled:e})}),(0,r.jsx)(m.FormMessage,{})]})}}),p&&(0,r.jsx)("div",{className:"text-red-600 text-sm text-center",children:p}),(0,r.jsx)(d.Button,{type:"submit",className:"w-full",disabled:e,children:e?"Connexion...":"Se connecter"})]})}),(0,r.jsxs)("div",{className:"mt-4 text-center text-sm text-gray-600",children:["Pas encore de compte ?"," ",(0,r.jsx)(l.default,{href:"/auth/register",className:"text-blue-600 hover:underline",children:"Créer un compte"}),(0,r.jsx)("div",{className:"text-xs text-orange-600 mt-1",children:"(Temporaire - pour les tests)"})]})]})]})})}}]);