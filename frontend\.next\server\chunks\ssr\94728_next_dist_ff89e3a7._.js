module.exports=[50223,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"normalizeLocalePath",{enumerable:!0,get:function(){return e}});let d=new WeakMap;function e(a,b){let c;if(!b)return{pathname:a};let e=d.get(b);e||(e=b.map(a=>a.toLowerCase()),d.set(b,e));let f=a.split("/",2);if(!f[1])return{pathname:a};let g=f[1].toLowerCase(),h=e.indexOf(g);return h<0?{pathname:a}:(c=b[h],{pathname:a=a.slice(c.length+1)||"/",detectedLocale:c})}},60712,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ACTION_SUFFIX:function(){return p},APP_DIR_ALIAS:function(){return J},CACHE_ONE_YEAR:function(){return B},DOT_NEXT_ALIAS:function(){return H},ESLINT_DEFAULT_DIRS:function(){return ab},GSP_NO_RETURNED_VALUE:function(){return X},GSSP_COMPONENT_MEMBER_ERROR:function(){return $},GSSP_NO_RETURNED_VALUE:function(){return Y},HTML_CONTENT_TYPE_HEADER:function(){return e},INFINITE_CACHE:function(){return C},INSTRUMENTATION_HOOK_FILENAME:function(){return F},JSON_CONTENT_TYPE_HEADER:function(){return f},MATCHED_PATH_HEADER:function(){return i},MIDDLEWARE_FILENAME:function(){return D},MIDDLEWARE_LOCATION_REGEXP:function(){return E},NEXT_BODY_SUFFIX:function(){return s},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return A},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return u},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return v},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return z},NEXT_CACHE_TAGS_HEADER:function(){return t},NEXT_CACHE_TAG_MAX_ITEMS:function(){return x},NEXT_CACHE_TAG_MAX_LENGTH:function(){return y},NEXT_DATA_SUFFIX:function(){return q},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return h},NEXT_META_SUFFIX:function(){return r},NEXT_QUERY_PARAM_PREFIX:function(){return g},NEXT_RESUME_HEADER:function(){return w},NON_STANDARD_NODE_ENV:function(){return _},PAGES_DIR_ALIAS:function(){return G},PRERENDER_REVALIDATE_HEADER:function(){return j},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return k},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return R},ROOT_DIR_ALIAS:function(){return I},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return Q},RSC_ACTION_ENCRYPTION_ALIAS:function(){return P},RSC_ACTION_PROXY_ALIAS:function(){return M},RSC_ACTION_VALIDATE_ALIAS:function(){return L},RSC_CACHE_WRAPPER_ALIAS:function(){return N},RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS:function(){return O},RSC_MOD_REF_PROXY_ALIAS:function(){return K},RSC_PREFETCH_SUFFIX:function(){return l},RSC_SEGMENTS_DIR_SUFFIX:function(){return m},RSC_SEGMENT_SUFFIX:function(){return n},RSC_SUFFIX:function(){return o},SERVER_PROPS_EXPORT_ERROR:function(){return W},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return T},SERVER_PROPS_SSG_CONFLICT:function(){return U},SERVER_RUNTIME:function(){return ac},SSG_FALLBACK_EXPORT_ERROR:function(){return aa},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return S},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return V},TEXT_PLAIN_CONTENT_TYPE_HEADER:function(){return d},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return Z},WEBPACK_LAYERS:function(){return ae},WEBPACK_RESOURCE_QUERIES:function(){return af}});let d="text/plain",e="text/html; charset=utf-8",f="application/json; charset=utf-8",g="nxtP",h="nxtI",i="x-matched-path",j="x-prerender-revalidate",k="x-prerender-revalidate-if-generated",l=".prefetch.rsc",m=".segments",n=".segment.rsc",o=".rsc",p=".action",q=".json",r=".meta",s=".body",t="x-next-cache-tags",u="x-next-revalidated-tags",v="x-next-revalidate-tag-token",w="next-resume",x=128,y=256,z=1024,A="_N_T_",B=31536e3,C=0xfffffffe,D="middleware",E=`(?:src/)?${D}`,F="instrumentation",G="private-next-pages",H="private-dot-next",I="private-next-root-dir",J="private-next-app-dir",K="private-next-rsc-mod-ref-proxy",L="private-next-rsc-action-validate",M="private-next-rsc-server-reference",N="private-next-rsc-cache-wrapper",O="private-next-rsc-track-dynamic-import",P="private-next-rsc-action-encryption",Q="private-next-rsc-action-client-wrapper",R="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",S="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",T="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",U="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",V="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",W="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",X="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",Y="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",Z="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",$="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",_='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',aa="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",ab=["app","pages","components","lib","src"],ac={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},ad={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},ae={...ad,GROUP:{builtinReact:[ad.reactServerComponents,ad.actionBrowser],serverOnly:[ad.reactServerComponents,ad.actionBrowser,ad.instrument,ad.middleware],neutralTarget:[ad.apiNode,ad.apiEdge],clientOnly:[ad.serverSideRendering,ad.appPagesBrowser],bundled:[ad.reactServerComponents,ad.actionBrowser,ad.serverSideRendering,ad.appPagesBrowser,ad.shared,ad.instrument,ad.middleware],appPages:[ad.reactServerComponents,ad.serverSideRendering,ad.appPagesBrowser,ad.actionBrowser]}},af={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},41299,(a,b,c)=>{"use strict";function d(a){return a.replace(/\/$/,"")||"/"}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"removeTrailingSlash",{enumerable:!0,get:function(){return d}})},46995,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{fromNodeOutgoingHttpHeaders:function(){return e},normalizeNextQueryParam:function(){return i},splitCookiesString:function(){return f},toNodeOutgoingHttpHeaders:function(){return g},validateURL:function(){return h}});let d=a.r(60712);function e(a){let b=new Headers;for(let[c,d]of Object.entries(a))for(let a of Array.isArray(d)?d:[d])void 0!==a&&("number"==typeof a&&(a=a.toString()),b.append(c,a));return b}function f(a){var b,c,d,e,f,g=[],h=0;function i(){for(;h<a.length&&/\s/.test(a.charAt(h));)h+=1;return h<a.length}for(;h<a.length;){for(b=h,f=!1;i();)if(","===(c=a.charAt(h))){for(d=h,h+=1,i(),e=h;h<a.length&&"="!==(c=a.charAt(h))&&";"!==c&&","!==c;)h+=1;h<a.length&&"="===a.charAt(h)?(f=!0,h=e,g.push(a.substring(b,d)),b=h):h=d+1}else h+=1;(!f||h>=a.length)&&g.push(a.substring(b,a.length))}return g}function g(a){let b={},c=[];if(a)for(let[d,e]of a.entries())"set-cookie"===d.toLowerCase()?(c.push(...f(e)),b[d]=1===c.length?c[0]:c):b[d]=e;return b}function h(a){try{return String(new URL(String(a)))}catch(b){throw Object.defineProperty(Error(`URL is malformed "${String(a)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:b}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}function i(a){for(let b of[d.NEXT_QUERY_PARAM_PREFIX,d.NEXT_INTERCEPTION_MARKER_PREFIX])if(a!==b&&a.startsWith(b))return a.substring(b.length);return null}},1594,(a,b,c)=>{"use strict";b.exports=a.r(40273).vendored["react-rsc"].React},92089,12031,89907,25744,93071,35807,83332,a=>{"use strict";let b;a.s(["patchFetch",()=>R],92089);var c=a.i(48747),d=a.i(19017),e=a.i(73994);a.s(["Postpone",()=>E,"annotateDynamicAccess",()=>I,"delayUntilRuntimeStage",()=>J,"isPrerenderInterruptedError",()=>H,"markCurrentScopeAsDynamic",()=>A,"postponeWithTracking",()=>F,"throwToInterruptStaticGeneration",()=>B,"trackDynamicDataInDynamicRender",()=>C,"trackSynchronousRequestDataAccessInDev",()=>D],83332);var f=a.i(1594);a.s(["DynamicServerError",()=>h,"isDynamicServerError",()=>i],12031);let g="DYNAMIC_SERVER_USAGE";class h extends Error{constructor(a){super("Dynamic server usage: "+a),this.description=a,this.digest=g}}function i(a){return"object"==typeof a&&null!==a&&"digest"in a&&"string"==typeof a.digest&&a.digest===g}a.s(["StaticGenBailoutError",()=>j],89907);class j extends Error{constructor(...a){super(...a),this.code="NEXT_STATIC_GEN_BAILOUT"}}var k=a.i(32319);a.i(56704),a.s(["makeDevtoolsIOAwarePromise",()=>p,"makeHangingPromise",()=>n],25744);class l extends Error{constructor(a,b){super(`During prerendering, ${b} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${b} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context. This occurred at route "${a}".`),this.route=a,this.expression=b,this.digest="HANGING_PROMISE_REJECTION"}}let m=new WeakMap;function n(a,b,c){if(a.aborted)return Promise.reject(new l(b,c));{let d=new Promise((d,e)=>{let f=e.bind(null,new l(b,c)),g=m.get(a);if(g)g.push(f);else{let b=[f];m.set(a,b),a.addEventListener("abort",()=>{for(let a=0;a<b.length;a++)b[a]()},{once:!0})}});return d.catch(o),d}}function o(){}function p(a){return new Promise(b=>{setTimeout(()=>{b(a)},0)})}a.s(["METADATA_BOUNDARY_NAME",()=>q,"OUTLET_BOUNDARY_NAME",()=>s,"ROOT_LAYOUT_BOUNDARY_NAME",()=>t,"VIEWPORT_BOUNDARY_NAME",()=>r],93071);let q="__next_metadata_boundary__",r="__next_viewport_boundary__",s="__next_outlet_boundary__",t="__next_root_layout_boundary__";var u=a.i(3626);a.s(["BailoutToCSRError",()=>w,"isBailoutToCSRError",()=>x],35807);let v="BAILOUT_TO_CLIENT_SIDE_RENDERING";class w extends Error{constructor(a){super("Bail out to client-side rendering: "+a),this.reason=a,this.digest=v}}function x(a){return"object"==typeof a&&null!==a&&"digest"in a&&a.digest===v}var y=a.i(1277);let z="function"==typeof f.default.unstable_postpone;function A(a,b,c){if(b)switch(b.type){case"cache":case"unstable-cache":case"private-cache":return}if(!a.forceDynamic&&!a.forceStatic){if(a.dynamicShouldError)throw Object.defineProperty(new j(`Route ${a.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${c}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(b)switch(b.type){case"prerender-ppr":return F(a.route,c,b.dynamicTracking);case"prerender-legacy":b.revalidate=0;let d=Object.defineProperty(new h(`Route ${a.route} couldn't be rendered statically because it used ${c}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E550",enumerable:!1,configurable:!0});throw a.dynamicUsageDescription=c,a.dynamicUsageStack=d.stack,d}}}function B(a,b,c){let d=Object.defineProperty(new h(`Route ${b.route} couldn't be rendered statically because it used \`${a}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw c.revalidate=0,b.dynamicUsageDescription=a,b.dynamicUsageStack=d.stack,d}function C(a){switch(a.type){case"cache":case"unstable-cache":case"private-cache":return}}let D=function(a){a.prerenderPhase=!1};function E({reason:a,route:b}){let c=k.workUnitAsyncStorage.getStore();F(b,a,c&&"prerender-ppr"===c.type?c.dynamicTracking:null)}function F(a,b,c){(function(){if(!z)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),c&&c.dynamicAccesses.push({stack:c.isDebugDynamicAccesses?Error().stack:void 0,expression:b}),f.default.unstable_postpone(G(a,b))}function G(a,b){return`Route ${a} needs to bail out of prerendering at this point because it used ${b}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}if(!1===function(a){return a.includes("needs to bail out of prerendering at this point because it used")&&a.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}(G("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});function H(a){return"object"==typeof a&&null!==a&&"NEXT_PRERENDER_INTERRUPTED"===a.digest&&"name"in a&&"message"in a&&a instanceof Error}function I(a,b){let c=b.dynamicTracking;c&&c.dynamicAccesses.push({stack:c.isDebugDynamicAccesses?Error().stack:void 0,expression:a})}function J(a,b){return a.runtimeStagePromise?a.runtimeStagePromise.then(()=>b):b}RegExp(`\\n\\s+at Suspense \\(<anonymous>\\)(?:(?!\\n\\s+at (?:body|div|main|section|article|aside|header|footer|nav|form|p|span|h1|h2|h3|h4|h5|h6) \\(<anonymous>\\))[\\s\\S])*?\\n\\s+at ${t} \\([^\\n]*\\)`),RegExp(`\\n\\s+at ${q}[\\n\\s]`),RegExp(`\\n\\s+at ${r}[\\n\\s]`),RegExp(`\\n\\s+at ${s}[\\n\\s]`);let K=()=>{};function L(a){if(!a.body)return[a,a];let[c,d]=a.body.tee(),e=new Response(c,{status:a.status,statusText:a.statusText,headers:a.headers});Object.defineProperty(e,"url",{value:a.url,configurable:!0,enumerable:!0,writable:!1}),b&&e.body&&b.register(e,new WeakRef(e.body));let f=new Response(d,{status:a.status,statusText:a.statusText,headers:a.headers});return Object.defineProperty(f,"url",{value:a.url,configurable:!0,enumerable:!0,writable:!1}),[e,f]}globalThis.FinalizationRegistry&&(b=new FinalizationRegistry(a=>{let b=a.deref();b&&!b.locked&&b.cancel("Response object has been garbage collected").then(K)})),a.i(83516);var M=a.i(1178);let N=Symbol.for("next-patch");function O(a,b){a.shouldTrackFetchMetrics&&(a.fetchMetrics??=[],a.fetchMetrics.push({...b,end:performance.timeOrigin+performance.now(),idx:a.nextFetchId||0}))}async function P(a,b,c,d,e,f){let g=await a.arrayBuffer(),h={headers:Object.fromEntries(a.headers.entries()),body:Buffer.from(g).toString("base64"),status:a.status,url:a.url};return c&&await d.set(b,{kind:M.CachedRouteKind.FETCH,data:h,revalidate:e},c),await f(),new Response(g,{headers:a.headers,status:a.status,statusText:a.statusText})}async function Q(a,b,c,d,e,f,g,h,i){let[j,k]=L(b),l=j.arrayBuffer().then(async a=>{let b=Buffer.from(a),h={headers:Object.fromEntries(j.headers.entries()),body:b.toString("base64"),status:j.status,url:j.url};null==f||f.set(c,h),d&&await e.set(c,{kind:M.CachedRouteKind.FETCH,data:h,revalidate:g},d)}).catch(a=>console.warn("Failed to set fetch cache",h,a)).finally(i),m=`cache-set-${c}`;return a.pendingRevalidates??={},m in a.pendingRevalidates&&await a.pendingRevalidates[m],a.pendingRevalidates[m]=l.finally(()=>{var b;(null==(b=a.pendingRevalidates)?void 0:b[m])&&delete a.pendingRevalidates[m]}),k}function R(a){if(!0===globalThis[N])return;let b=function(a){let b=f.cache(a=>[]);return function(c,d){let e,f;if(d&&d.signal)return a(c,d);if("string"!=typeof c||d){let b="string"==typeof c||c instanceof URL?new Request(c,d):c;if("GET"!==b.method&&"HEAD"!==b.method||b.keepalive)return a(c,d);f=JSON.stringify([b.method,Array.from(b.headers.entries()),b.mode,b.redirect,b.credentials,b.referrer,b.referrerPolicy,b.integrity]),e=b.url}else f='["GET",[],null,"follow",null,null,null,null]',e=c;let g=b(e);for(let a=0,b=g.length;a<b;a+=1){let[b,c]=g[a];if(b===f)return c.then(()=>{let b=g[a][2];if(!b)throw Object.defineProperty(new y.InvariantError("No cached response"),"__NEXT_ERROR_CODE",{value:"E579",enumerable:!1,configurable:!0});let[c,d]=L(b);return g[a][2]=d,c})}let h=a(c,d),i=[f,h,null];return g.push(i),h.then(a=>{let[b,c]=L(a);return i[2]=c,b})}}(globalThis.fetch);globalThis.fetch=function(a,{workAsyncStorage:b,workUnitAsyncStorage:f}){let g=async function(g,h){var i,j;let l;try{(l=new URL(g instanceof Request?g.url:g)).username="",l.password=""}catch{l=void 0}let m=(null==l?void 0:l.href)??"",o=(null==h||null==(i=h.method)?void 0:i.toUpperCase())||"GET",p=(null==h||null==(j=h.next)?void 0:j.internal)===!0,q="1"===process.env.NEXT_OTEL_FETCH_DISABLED,r=p?void 0:performance.timeOrigin+performance.now(),s=b.getStore(),t=f.getStore(),v=t?(0,k.getCacheSignal)(t):null;v&&v.beginRead();let w=(0,d.getTracer)().trace(p?c.NextNodeServerSpan.internalFetch:c.AppRenderSpan.fetch,{hideSpan:q,kind:d.SpanKind.CLIENT,spanName:["fetch",o,m].filter(Boolean).join(" "),attributes:{"http.url":m,"http.method":o,"net.peer.name":null==l?void 0:l.hostname,"net.peer.port":(null==l?void 0:l.port)||void 0}},async()=>{var b;let c,d,f,i,j,k;if(p||!s||s.isDraftMode)return a(g,h);let l=g&&"object"==typeof g&&"string"==typeof g.method,o=a=>(null==h?void 0:h[a])||(l?g[a]:null),q=a=>{var b,c,d;return void 0!==(null==h||null==(b=h.next)?void 0:b[a])?null==h||null==(c=h.next)?void 0:c[a]:l?null==(d=g.next)?void 0:d[a]:void 0},w=q("revalidate"),x=w,y=function(a,b){let c=[],d=[];for(let f=0;f<a.length;f++){let g=a[f];if("string"!=typeof g?d.push({tag:g,reason:"invalid type, must be a string"}):g.length>e.NEXT_CACHE_TAG_MAX_LENGTH?d.push({tag:g,reason:`exceeded max length of ${e.NEXT_CACHE_TAG_MAX_LENGTH}`}):c.push(g),c.length>e.NEXT_CACHE_TAG_MAX_ITEMS){console.warn(`Warning: exceeded max tag count for ${b}, dropped tags:`,a.slice(f).join(", "));break}}if(d.length>0)for(let{tag:a,reason:c}of(console.warn(`Warning: invalid tags passed to ${b}: `),d))console.log(`tag: "${a}" ${c}`);return c}(q("tags")||[],`fetch ${g.toString()}`);if(t)switch(t.type){case"prerender":case"prerender-runtime":case"prerender-client":case"prerender-ppr":case"prerender-legacy":case"cache":case"private-cache":c=t}if(c&&Array.isArray(y)){let a=c.tags??(c.tags=[]);for(let b of y)a.includes(b)||a.push(b)}let z=null==t?void 0:t.implicitTags,B=s.fetchCache;t&&"unstable-cache"===t.type&&(B="force-no-store");let C=!!s.isUnstableNoStore,D=o("cache"),E="";"string"==typeof D&&void 0!==x&&("force-cache"===D&&0===x||"no-store"===D&&(x>0||!1===x))&&(d=`Specified "cache: ${D}" and "revalidate: ${x}", only one should be specified.`,D=void 0,x=void 0);let F="no-cache"===D||"no-store"===D||"force-no-store"===B||"only-no-store"===B,G=!B&&!D&&!x&&s.forceDynamic;"force-cache"===D&&void 0===x?x=!1:(F||G)&&(x=0),("no-cache"===D||"no-store"===D)&&(E=`cache: ${D}`),k=function(a,b){try{let c;if(!1===a)c=e.INFINITE_CACHE;else if("number"==typeof a&&!isNaN(a)&&a>-1)c=a;else if(void 0!==a)throw Object.defineProperty(Error(`Invalid revalidate value "${a}" on "${b}", must be a non-negative number or false`),"__NEXT_ERROR_CODE",{value:"E179",enumerable:!1,configurable:!0});return c}catch(a){if(a instanceof Error&&a.message.includes("Invalid revalidate"))throw a;return}}(x,s.route);let H=o("headers"),I="function"==typeof(null==H?void 0:H.get)?H:new Headers(H||{}),J=I.get("authorization")||I.get("cookie"),K=!["get","head"].includes((null==(b=o("method"))?void 0:b.toLowerCase())||"get"),N=void 0==B&&(void 0==D||"default"===D)&&void 0==x,R=!!((J||K)&&(null==c?void 0:c.revalidate)===0),S=!1;if(!R&&N&&(s.isBuildTimePrerendering?S=!0:R=!0),N&&void 0!==t)switch(t.type){case"prerender":case"prerender-runtime":case"prerender-client":return v&&(v.endRead(),v=null),n(t.renderSignal,s.route,"fetch()")}switch(B){case"force-no-store":E="fetchCache = force-no-store";break;case"only-no-store":if("force-cache"===D||void 0!==k&&k>0)throw Object.defineProperty(Error(`cache: 'force-cache' used on fetch for ${m} with 'export const fetchCache = 'only-no-store'`),"__NEXT_ERROR_CODE",{value:"E448",enumerable:!1,configurable:!0});E="fetchCache = only-no-store";break;case"only-cache":if("no-store"===D)throw Object.defineProperty(Error(`cache: 'no-store' used on fetch for ${m} with 'export const fetchCache = 'only-cache'`),"__NEXT_ERROR_CODE",{value:"E521",enumerable:!1,configurable:!0});break;case"force-cache":(void 0===x||0===x)&&(E="fetchCache = force-cache",k=e.INFINITE_CACHE)}if(void 0===k?"default-cache"!==B||C?"default-no-store"===B?(k=0,E="fetchCache = default-no-store"):C?(k=0,E="noStore call"):R?(k=0,E="auto no cache"):(E="auto cache",k=c?c.revalidate:e.INFINITE_CACHE):(k=e.INFINITE_CACHE,E="fetchCache = default-cache"):E||(E=`revalidate: ${k}`),!(s.forceStatic&&0===k)&&!R&&c&&k<c.revalidate){if(0===k){if(t)switch(t.type){case"prerender":case"prerender-client":case"prerender-runtime":return v&&(v.endRead(),v=null),n(t.renderSignal,s.route,"fetch()")}A(s,t,`revalidate: 0 fetch ${g} ${s.route}`)}c&&w===k&&(c.revalidate=k)}let T="number"==typeof k&&k>0,{incrementalCache:U}=s,V=!1;if(t)switch(t.type){case"request":case"cache":case"private-cache":V=t.isHmrRefresh??!1,i=t.serverComponentsHmrCache}if(U&&(T||i))try{f=await U.generateCacheKey(m,l?g:h)}catch(a){console.error("Failed to generate cache key for",g)}let W=s.nextFetchId??1;s.nextFetchId=W+1;let X=()=>{},Y=async(b,c)=>{let j=["cache","credentials","headers","integrity","keepalive","method","mode","redirect","referrer","referrerPolicy","window","duplex",...b?[]:["signal"]];if(l){let a=g,b={body:a._ogBody||a.body};for(let c of j)b[c]=a[c];g=new Request(a.url,b)}else if(h){let{_ogBody:a,body:c,signal:d,...e}=h;h={...e,body:a||c,signal:b?void 0:d}}let n={...h,next:{...null==h?void 0:h.next,fetchType:"origin",fetchIdx:W}};return a(g,n).then(async a=>{if(!b&&r&&O(s,{start:r,url:m,cacheReason:c||E,cacheStatus:0===k||c?"skip":"miss",cacheWarning:d,status:a.status,method:n.method||"GET"}),200===a.status&&U&&f&&(T||i)){let b=k>=e.INFINITE_CACHE?e.CACHE_ONE_YEAR:k,c=T?{fetchCache:!0,fetchUrl:m,fetchIdx:W,tags:y,isImplicitBuildTimeCache:S}:void 0;switch(null==t?void 0:t.type){case"prerender":case"prerender-client":case"prerender-runtime":return P(a,f,c,U,b,X);case"prerender-ppr":case"prerender-legacy":case"request":case"cache":case"private-cache":case"unstable-cache":case void 0:return Q(s,a,f,c,U,i,b,g,X)}}return await X(),a}).catch(a=>{throw X(),a})},Z=!1,$=!1;if(f&&U){let a;if(V&&i&&(a=i.get(f),$=!0),T&&!a){X=await U.lock(f);let b=s.isOnDemandRevalidate?null:await U.get(f,{kind:M.IncrementalCacheKind.FETCH,revalidate:k,fetchUrl:m,fetchIdx:W,tags:y,softTags:null==z?void 0:z.tags});if(N&&t)switch(t.type){case"prerender":case"prerender-client":case"prerender-runtime":await (0,u.waitAtLeastOneReactRenderTask)()}if(b?await X():j="cache-control: no-cache (hard refresh)",(null==b?void 0:b.value)&&b.value.kind===M.CachedRouteKind.FETCH)if(s.isRevalidate&&b.isStale)Z=!0;else{if(b.isStale&&(s.pendingRevalidates??={},!s.pendingRevalidates[f])){let a=Y(!0).then(async a=>({body:await a.arrayBuffer(),headers:a.headers,status:a.status,statusText:a.statusText})).finally(()=>{s.pendingRevalidates??={},delete s.pendingRevalidates[f||""]});a.catch(console.error),s.pendingRevalidates[f]=a}a=b.value.data}}if(a){r&&O(s,{start:r,url:m,cacheReason:E,cacheStatus:$?"hmr":"hit",cacheWarning:d,status:a.status||200,method:(null==h?void 0:h.method)||"GET"});let b=new Response(Buffer.from(a.body,"base64"),{headers:a.headers,status:a.status});return Object.defineProperty(b,"url",{value:a.url}),b}}if(s.isStaticGeneration&&h&&"object"==typeof h){let{cache:a}=h;if("no-store"===a){if(t)switch(t.type){case"prerender":case"prerender-client":case"prerender-runtime":return v&&(v.endRead(),v=null),n(t.renderSignal,s.route,"fetch()")}A(s,t,`no-store fetch ${g} ${s.route}`)}let b="next"in h,{next:d={}}=h;if("number"==typeof d.revalidate&&c&&d.revalidate<c.revalidate){if(0===d.revalidate){if(t)switch(t.type){case"prerender":case"prerender-client":case"prerender-runtime":return n(t.renderSignal,s.route,"fetch()")}A(s,t,`revalidate: 0 fetch ${g} ${s.route}`)}s.forceStatic&&0===d.revalidate||(c.revalidate=d.revalidate)}b&&delete h.next}if(!f||!Z)return Y(!1,j);{let a=f;s.pendingRevalidates??={};let b=s.pendingRevalidates[a];if(b){let a=await b;return new Response(a.body,{headers:a.headers,status:a.status,statusText:a.statusText})}let c=Y(!0,j).then(L);return(b=c.then(async a=>{let b=a[0];return{body:await b.arrayBuffer(),headers:b.headers,status:b.status,statusText:b.statusText}}).finally(()=>{var b;(null==(b=s.pendingRevalidates)?void 0:b[a])&&delete s.pendingRevalidates[a]})).catch(()=>{}),s.pendingRevalidates[a]=b,c.then(a=>a[1])}});if(v)try{return await w}finally{v&&v.endRead()}return w};return g.__nextPatched=!0,g.__nextGetStaticStore=()=>b,g._nextOriginalFetch=a,globalThis[N]=!0,Object.defineProperty(g,"name",{value:"fetch",writable:!1}),g}(b,a)}}];

//# sourceMappingURL=94728_next_dist_ff89e3a7._.js.map