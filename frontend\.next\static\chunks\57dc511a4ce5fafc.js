(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,57223,e=>{"use strict";e.s(["DollarSign",()=>t],57223);let t=(0,e.i(44571).default)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},44640,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ReflectAdapter",{enumerable:!0,get:function(){return n}});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},8356,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorageInstance",{enumerable:!0,get:function(){return n}});let n=(0,e.r(97918).createAsyncLocalStorage)()},62355,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorage",{enumerable:!0,get:function(){return n.afterTaskAsyncStorageInstance}});let n=e.r(8356)},17939,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return a},throwWithStaticGenerationBailoutError:function(){return o},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return i}});let n=e.r(85115),s=e.r(62355);function o(e,t){throw Object.defineProperty(new n.StaticGenBailoutError("Route ".concat(e," couldn't be rendered statically because it used ").concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function i(e,t){throw Object.defineProperty(new n.StaticGenBailoutError("Route ".concat(e,' with `dynamic = "error"` couldn\'t be rendered statically because it used ').concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function a(e,t){let r=Object.defineProperty(Error("Route ".concat(e.route,' used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(r,t),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=r),r}function c(){let e=s.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},93168,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{describeHasCheckingStringProperty:function(){return o},describeStringPropertyAccess:function(){return s},wellKnownProperties:function(){return i}});let n=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function s(e,t){return n.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function o(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let i=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},63470,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var a=o?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(n,i,a):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(e.r(38477));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}let o={current:null},i="function"==typeof n.cache?n.cache:e=>e,a=console.warn;function c(e){return function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];a(e(...r))}}i(e=>{try{a(o.current)}finally{o.current=null}})},4467,e=>{"use strict";e.s(["useApi",()=>n]);var t=e.i(1269),r=e.i(1831);function n(){let{data:e}=(0,t.useSession)(),n=async function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(null==e?void 0:e.accessToken))throw Error("Non authentifié");return r.apiService.authenticatedRequest(t,e.accessToken,n)};return{login:r.apiService.login.bind(r.apiService),register:r.apiService.register.bind(r.apiService),authenticatedRequest:n,getUsers:()=>n("/users"),getUser:e=>n("/users/".concat(e)),createUser:e=>n("/users",{method:"POST",body:JSON.stringify(e)}),updateUser:(e,t)=>n("/users/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteUser:e=>n("/users/".concat(e),{method:"DELETE"}),getSessions:()=>n("/sessions"),getSession:e=>n("/sessions/".concat(e)),createSession:e=>n("/sessions",{method:"POST",body:JSON.stringify(e)}),updateSession:(e,t)=>n("/sessions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteSession:e=>n("/sessions/".concat(e),{method:"DELETE"}),getCaisses:()=>n("/caisses"),getCaisse:e=>n("/caisses/".concat(e)),createCaisse:e=>n("/caisses",{method:"POST",body:JSON.stringify(e)}),updateCaisse:(e,t)=>n("/caisses/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteCaisse:e=>n("/caisses/".concat(e),{method:"DELETE"}),emargerCaisse:e=>n("/caisses/".concat(e,"/emarger"),{method:"POST"}),getReunions:()=>n("/reunions"),getReunion:e=>n("/reunions/".concat(e)),updateReunion:(e,t)=>n("/reunions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),getMembers:()=>n("/members"),getMember:e=>n("/members/".concat(e)),createMember:e=>n("/members",{method:"POST",body:JSON.stringify(e)}),updateMember:(e,t)=>n("/members/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteMember:e=>n("/members/".concat(e),{method:"DELETE"}),getMemberDebrief:(e,t)=>{let r=new URLSearchParams;(null==t?void 0:t.dateFrom)&&r.append("dateFrom",t.dateFrom),(null==t?void 0:t.dateTo)&&r.append("dateTo",t.dateTo),(null==t?void 0:t.sessionId)&&r.append("sessionId",t.sessionId);let s=r.toString()?"?".concat(r.toString()):"";return n("/members/".concat(e,"/debrief").concat(s))},createPayment:e=>n("/payments",{method:"POST",body:JSON.stringify(e)}),getSessionMembers:e=>n("/sessions/".concat(e,"/members")),addSessionMember:e=>n("/session-members",{method:"POST",body:JSON.stringify(e)}),removeSessionMember:(e,t)=>n("/sessions/".concat(e,"/members/").concat(t),{method:"DELETE"})}}},12058,e=>{"use strict";e.s(["CaisseType",()=>t,"PaymentDirection",()=>n,"PaymentFunction",()=>s,"UserRole",()=>r]);var t=function(e){return e.PRINCIPALE="PRINCIPALE",e.REUNION="REUNION",e}({}),r=function(e){return e.SECRETARY_GENERAL="secretary_general",e.CONTROLLER="controller",e.CASHIER="cashier",e}({}),n=function(e){return e.IN="IN",e.OUT="OUT",e}({}),s=function(e){return e.CONTRIBUTION="cotisation",e.TRANSFER="transfert",e.EXTERNAL="exterieur",e}({})},81787,e=>{"use strict";e.s(["Plus",()=>t],81787);let t=(0,e.i(44571).default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}]);