module.exports=[24725,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},12594,a=>{"use strict";a.s(["useApi",()=>d]);var b=a.i(81223),c=a.i(34075);function d(){let{data:a}=(0,b.useSession)(),d=async(b,d={})=>{if(!a?.accessToken)throw Error("Non authentifié");return c.apiService.authenticatedRequest(b,a.accessToken,d)};return{login:c.apiService.login.bind(c.apiService),register:c.apiService.register.bind(c.apiService),authenticatedRequest:d,getUsers:()=>d("/users"),getUser:a=>d(`/users/${a}`),createUser:a=>d("/users",{method:"POST",body:JSON.stringify(a)}),updateUser:(a,b)=>d(`/users/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteUser:a=>d(`/users/${a}`,{method:"DELETE"}),getSessions:()=>d("/sessions"),getSession:a=>d(`/sessions/${a}`),createSession:a=>d("/sessions",{method:"POST",body:JSON.stringify(a)}),updateSession:(a,b)=>d(`/sessions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteSession:a=>d(`/sessions/${a}`,{method:"DELETE"}),getCaisses:()=>d("/caisses"),getCaisse:a=>d(`/caisses/${a}`),createCaisse:a=>d("/caisses",{method:"POST",body:JSON.stringify(a)}),updateCaisse:(a,b)=>d(`/caisses/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteCaisse:a=>d(`/caisses/${a}`,{method:"DELETE"}),emargerCaisse:a=>d(`/caisses/${a}/emarger`,{method:"POST"}),getReunions:()=>d("/reunions"),getReunion:a=>d(`/reunions/${a}`),updateReunion:(a,b)=>d(`/reunions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),getMembers:()=>d("/members"),getMember:a=>d(`/members/${a}`),createMember:a=>d("/members",{method:"POST",body:JSON.stringify(a)}),updateMember:(a,b)=>d(`/members/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteMember:a=>d(`/members/${a}`,{method:"DELETE"}),getMemberDebrief:(a,b)=>{let c=new URLSearchParams;b?.dateFrom&&c.append("dateFrom",b.dateFrom),b?.dateTo&&c.append("dateTo",b.dateTo),b?.sessionId&&c.append("sessionId",b.sessionId);let e=c.toString()?`?${c.toString()}`:"";return d(`/members/${a}/debrief${e}`)},createPayment:a=>d("/payments",{method:"POST",body:JSON.stringify(a)}),getSessionMembers:a=>d(`/sessions/${a}/members`),addSessionMember:a=>d("/session-members",{method:"POST",body:JSON.stringify(a)}),removeSessionMember:(a,b)=>d(`/sessions/${a}/members/${b}`,{method:"DELETE"})}}},78655,a=>{"use strict";a.s(["CaisseType",()=>b,"PaymentDirection",()=>d,"PaymentFunction",()=>e,"UserRole",()=>c]);var b=function(a){return a.PRINCIPALE="PRINCIPALE",a.REUNION="REUNION",a}({}),c=function(a){return a.SECRETARY_GENERAL="secretary_general",a.CONTROLLER="controller",a.CASHIER="cashier",a}({}),d=function(a){return a.IN="IN",a.OUT="OUT",a}({}),e=function(a){return a.CONTRIBUTION="cotisation",a.TRANSFER="transfert",a.EXTERNAL="exterieur",a}({})},41405,a=>{"use strict";a.s(["Label",()=>g],41405);var b=a.i(68116),c=a.i(128),d=a.i(48206),e=c.forwardRef((a,c)=>(0,b.jsx)(d.Primitive.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));e.displayName="Label";var f=a.i(22171);function g({className:a,...c}){return(0,b.jsx)(e,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...c})}},6821,a=>{"use strict";a.s(["ArrowLeft",()=>b],6821);let b=(0,a.i(621).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},66577,a=>{"use strict";a.s(["Textarea",()=>d]);var b=a.i(68116),c=a.i(22171);function d({className:a,...d}){return(0,b.jsx)("textarea",{"data-slot":"textarea",className:(0,c.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...d})}},99170,a=>{"use strict";a.s(["default",()=>s]);var b=a.i(68116),c=a.i(128),d=a.i(50395),e=a.i(81223),f=a.i(64853),g=a.i(54636),h=a.i(66446),i=a.i(6821),j=a.i(33055),k=a.i(2979),l=a.i(78184),m=a.i(66577),n=a.i(75780),o=a.i(60563),p=a.i(12594),q=a.i(78655);let r=h.z.object({firstName:h.z.string().min(2,"Le prénom doit contenir au moins 2 caractères"),lastName:h.z.string().min(2,"Le nom doit contenir au moins 2 caractères"),email:h.z.string().email("Adresse email invalide").optional().or(h.z.literal("")),phone:h.z.string().optional(),address:h.z.string().optional()});function s(){let{data:a}=(0,e.useSession)(),h=(0,d.useRouter)(),s=(0,p.useApi)(),[t,u]=(0,c.useState)(!1),[v,w]=(0,c.useState)(null),x=a?.user&&a.user.role===q.UserRole.SECRETARY_GENERAL,y=(0,f.useForm)({resolver:(0,g.zodResolver)(r),defaultValues:{firstName:"",lastName:"",email:"",phone:"",address:""}}),z=async a=>{u(!0),w(null);try{let b={firstName:a.firstName,lastName:a.lastName,...a.email&&{email:a.email},...a.phone&&{phone:a.phone},...a.address&&{address:a.address}};await s.createMember(b),h.push("/dashboard/members")}catch(a){console.error("Erreur lors de la création du membre:",a),a instanceof Error?w(a.message):w("Erreur lors de la création du membre. Veuillez réessayer.")}finally{u(!1)}};return x?(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(j.default,{href:"/dashboard/members",children:(0,b.jsxs)(k.Button,{variant:"ghost",size:"sm",children:[(0,b.jsx)(i.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Nouveau Membre"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Créer un nouveau membre de la tontine"})]})]}),(0,b.jsxs)(n.Card,{className:"max-w-2xl",children:[(0,b.jsxs)(n.CardHeader,{children:[(0,b.jsx)(n.CardTitle,{children:"Informations du membre"}),(0,b.jsx)(n.CardDescription,{children:"Remplissez tous les champs pour créer un nouveau membre"})]}),(0,b.jsx)(n.CardContent,{children:(0,b.jsx)(o.Form,{...y,children:(0,b.jsxs)("form",{onSubmit:y.handleSubmit(z),className:"space-y-6",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsx)(o.FormField,{control:y.control,name:"firstName",render:({field:a})=>(0,b.jsxs)(o.FormItem,{children:[(0,b.jsx)(o.FormLabel,{children:"Prénom *"}),(0,b.jsx)(o.FormControl,{children:(0,b.jsx)(l.Input,{placeholder:"Prénom",...a,disabled:t})}),(0,b.jsx)(o.FormMessage,{})]})}),(0,b.jsx)(o.FormField,{control:y.control,name:"lastName",render:({field:a})=>(0,b.jsxs)(o.FormItem,{children:[(0,b.jsx)(o.FormLabel,{children:"Nom *"}),(0,b.jsx)(o.FormControl,{children:(0,b.jsx)(l.Input,{placeholder:"Nom de famille",...a,disabled:t})}),(0,b.jsx)(o.FormMessage,{})]})})]}),(0,b.jsx)(o.FormField,{control:y.control,name:"email",render:({field:a})=>(0,b.jsxs)(o.FormItem,{children:[(0,b.jsx)(o.FormLabel,{children:"Email"}),(0,b.jsx)(o.FormControl,{children:(0,b.jsx)(l.Input,{type:"email",placeholder:"<EMAIL>",...a,disabled:t})}),(0,b.jsx)(o.FormMessage,{})]})}),(0,b.jsx)(o.FormField,{control:y.control,name:"phone",render:({field:a})=>(0,b.jsxs)(o.FormItem,{children:[(0,b.jsx)(o.FormLabel,{children:"Téléphone"}),(0,b.jsx)(o.FormControl,{children:(0,b.jsx)(l.Input,{placeholder:"+237123456789",...a,disabled:t})}),(0,b.jsx)(o.FormMessage,{})]})}),(0,b.jsx)(o.FormField,{control:y.control,name:"address",render:({field:a})=>(0,b.jsxs)(o.FormItem,{children:[(0,b.jsx)(o.FormLabel,{children:"Adresse"}),(0,b.jsx)(o.FormControl,{children:(0,b.jsx)(m.Textarea,{placeholder:"Adresse complète",...a,disabled:t,rows:3})}),(0,b.jsx)(o.FormMessage,{})]})}),v&&(0,b.jsx)("div",{className:"text-red-600 text-sm bg-red-50 p-3 rounded",children:v}),(0,b.jsxs)("div",{className:"flex justify-end gap-4 pt-6",children:[(0,b.jsx)(j.default,{href:"/dashboard/members",children:(0,b.jsx)(k.Button,{variant:"outline",disabled:t,children:"Annuler"})}),(0,b.jsx)(k.Button,{type:"submit",disabled:t,children:t?"Création...":"Créer le membre"})]})]})})})]})]}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsx)("div",{className:"flex items-center gap-4",children:(0,b.jsx)(j.default,{href:"/dashboard/members",children:(0,b.jsxs)(k.Button,{variant:"ghost",size:"sm",children:[(0,b.jsx)(i.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,b.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Accès refusé"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Seuls les administrateurs peuvent créer des membres."})]})})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__2e4477f3._.js.map