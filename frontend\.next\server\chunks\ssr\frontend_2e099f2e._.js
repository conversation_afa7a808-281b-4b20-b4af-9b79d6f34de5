module.exports=[12594,a=>{"use strict";a.s(["useApi",()=>d]);var b=a.i(81223),c=a.i(34075);function d(){let{data:a}=(0,b.useSession)(),d=async(b,d={})=>{if(!a?.accessToken)throw Error("Non authentifié");return c.apiService.authenticatedRequest(b,a.accessToken,d)};return{login:c.apiService.login.bind(c.apiService),register:c.apiService.register.bind(c.apiService),authenticatedRequest:d,getUsers:()=>d("/users"),getUser:a=>d(`/users/${a}`),createUser:a=>d("/users",{method:"POST",body:JSON.stringify(a)}),updateUser:(a,b)=>d(`/users/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteUser:a=>d(`/users/${a}`,{method:"DELETE"}),getSessions:()=>d("/sessions"),getSession:a=>d(`/sessions/${a}`),createSession:a=>d("/sessions",{method:"POST",body:JSON.stringify(a)}),updateSession:(a,b)=>d(`/sessions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteSession:a=>d(`/sessions/${a}`,{method:"DELETE"}),getCaisses:()=>d("/caisses"),getCaisse:a=>d(`/caisses/${a}`),createCaisse:a=>d("/caisses",{method:"POST",body:JSON.stringify(a)}),updateCaisse:(a,b)=>d(`/caisses/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteCaisse:a=>d(`/caisses/${a}`,{method:"DELETE"}),emargerCaisse:a=>d(`/caisses/${a}/emarger`,{method:"POST"}),getReunions:()=>d("/reunions"),getReunion:a=>d(`/reunions/${a}`),updateReunion:(a,b)=>d(`/reunions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),getMembers:()=>d("/members"),getMember:a=>d(`/members/${a}`),createMember:a=>d("/members",{method:"POST",body:JSON.stringify(a)}),updateMember:(a,b)=>d(`/members/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteMember:a=>d(`/members/${a}`,{method:"DELETE"}),getMemberDebrief:(a,b)=>{let c=new URLSearchParams;b?.dateFrom&&c.append("dateFrom",b.dateFrom),b?.dateTo&&c.append("dateTo",b.dateTo),b?.sessionId&&c.append("sessionId",b.sessionId);let e=c.toString()?`?${c.toString()}`:"";return d(`/members/${a}/debrief${e}`)},createPayment:a=>d("/payments",{method:"POST",body:JSON.stringify(a)}),getSessionMembers:a=>d(`/sessions/${a}/members`),addSessionMember:a=>d("/session-members",{method:"POST",body:JSON.stringify(a)}),removeSessionMember:(a,b)=>d(`/sessions/${a}/members/${b}`,{method:"DELETE"})}}},78655,a=>{"use strict";a.s(["CaisseType",()=>b,"PaymentDirection",()=>d,"PaymentFunction",()=>e,"UserRole",()=>c]);var b=function(a){return a.PRINCIPALE="PRINCIPALE",a.REUNION="REUNION",a}({}),c=function(a){return a.SECRETARY_GENERAL="secretary_general",a.CONTROLLER="controller",a.CASHIER="cashier",a}({}),d=function(a){return a.IN="IN",a.OUT="OUT",a}({}),e=function(a){return a.CONTRIBUTION="cotisation",a.TRANSFER="transfert",a.EXTERNAL="exterieur",a}({})},48099,85955,54839,a=>{"use strict";a.s(["createCollection",()=>g],48099);var b=a.i(128),c=a.i(54130),d=a.i(9403),e=a.i(85689),f=a.i(68116);function g(a){let g=a+"CollectionProvider",[h,i]=(0,c.createContextScope)(g),[j,k]=h(g,{collectionRef:{current:null},itemMap:new Map}),l=a=>{let{scope:c,children:d}=a,e=b.default.useRef(null),g=b.default.useRef(new Map).current;return(0,f.jsx)(j,{scope:c,itemMap:g,collectionRef:e,children:d})};l.displayName=g;let m=a+"CollectionSlot",n=(0,e.createSlot)(m),o=b.default.forwardRef((a,b)=>{let{scope:c,children:e}=a,g=k(m,c),h=(0,d.useComposedRefs)(b,g.collectionRef);return(0,f.jsx)(n,{ref:h,children:e})});o.displayName=m;let p=a+"CollectionItemSlot",q="data-radix-collection-item",r=(0,e.createSlot)(p),s=b.default.forwardRef((a,c)=>{let{scope:e,children:g,...h}=a,i=b.default.useRef(null),j=(0,d.useComposedRefs)(c,i),l=k(p,e);return b.default.useEffect(()=>(l.itemMap.set(i,{ref:i,...h}),()=>void l.itemMap.delete(i))),(0,f.jsx)(r,{...{[q]:""},ref:j,children:g})});return s.displayName=p,[{Provider:l,Slot:o,ItemSlot:s},function(c){let d=k(a+"CollectionConsumer",c);return b.default.useCallback(()=>{let a=d.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${q}]`));return Array.from(d.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[d.collectionRef,d.itemMap])},i]}var h=new WeakMap;function i(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=j(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function j(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],h.set(this,!0)}set(a,b){return h.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=j(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let k=this.size+ +!e;g<0&&h++;let l=[...this.#a],m=!1;for(let a=h;a<k;a++)if(h===a){let f=l[a];l[a]===b&&(f=l[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{m||l[a-1]!==b||(m=!0);let c=l[m?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=i(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=i(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return i(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}}),a.s(["useDirection",()=>l],85955);var k=b.createContext(void 0);function l(a){let c=b.useContext(k);return a||c||"ltr"}a.s(["CheckIcon",()=>m],54839);let m=(0,a.i(621).default)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])}];

//# sourceMappingURL=frontend_2e099f2e._.js.map