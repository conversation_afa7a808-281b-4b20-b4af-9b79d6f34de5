{"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/dollar-sign.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-label@2.1.7_261ca6dc9b795d3e6e9f99d20849d772/node_modules/@radix-ui/react-label/dist/index.mjs", "turbopack:///[project]/frontend/src/components/ui/label.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/arrow-left.ts", "turbopack:///[project]/frontend/src/app/dashboard/caisses/new/page.tsx"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n", "\"use client\";\n\n// src/label.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Label\";\nvar Label = React.forwardRef((props, forwardedRef) => {\n  return /* @__PURE__ */ jsx(\n    Primitive.label,\n    {\n      ...props,\n      ref: forwardedRef,\n      onMouseDown: (event) => {\n        const target = event.target;\n        if (target.closest(\"button, input, select, textarea\")) return;\n        props.onMouseDown?.(event);\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }\n    }\n  );\n});\nLabel.displayName = NAME;\nvar Root = Label;\nexport {\n  Label,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\n\nimport { cn } from '@/lib/utils';\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot='label'\n      className={cn(\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Label };\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n", "\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSession } from \"next-auth/react\";\nimport { useRouter } from \"next/navigation\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { ArrowLeft, Wallet, DollarSign } from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport {\n\tForm,\n\tFormControl,\n\tFormDescription,\n\tFormField,\n\tFormItem,\n\tFormLabel,\n\tFormMessage,\n} from \"@/components/ui/form\";\nimport {\n\tSelect,\n\tSelectContent,\n\tSelectItem,\n\tSelectTrigger,\n\tSelectValue,\n} from \"@/components/ui/select\";\nimport { useApi } from \"@/hooks/use-api\";\nimport { CaisseForm, CaisseType, Session, Caisse } from \"@/types\";\n\nconst caisseSchema = z\n\t.object({\n\t\tnom: z\n\t\t\t.string()\n\t\t\t.min(1, \"Le nom est requis\")\n\t\t\t.max(100, \"Le nom est trop long\"),\n\t\ttype: z.nativeEnum(CaisseType, {\n\t\t\trequired_error: \"Le type est requis\",\n\t\t}),\n\t\tsoldeActuel: z\n\t\t\t.number()\n\t\t\t.gte(0, \"Le solde ne peut pas être négatif\")\n\t\t\t.max(10000000, \"Le solde ne peut pas dépasser 10,000,000 FCFA\"),\n\t\tsessionId: z.string().optional(),\n\t\tcaissePrincipaleId: z.string().optional(),\n\t})\n\t.refine(\n\t\t(data) => {\n\t\t\tif (data.type === CaisseType.REUNION) {\n\t\t\t\treturn data.sessionId && data.caissePrincipaleId;\n\t\t\t}\n\t\t\treturn true;\n\t\t},\n\t\t{\n\t\t\tmessage:\n\t\t\t\t\"Pour une caisse de réunion, la session et la caisse principale sont requises\",\n\t\t\tpath: [\"sessionId\"],\n\t\t},\n\t);\n\nexport default function NewCaissePage() {\n\tconst { data: session, status } = useSession();\n\tconst router = useRouter();\n\tconst api = useApi();\n\n\tconst [isLoading, setIsLoading] = useState(false);\n\tconst [error, setError] = useState<string | null>(null);\n\tconst [sessions, setSessions] = useState<Session[]>([]);\n\tconst [caissesPrincipales, setCaissesPrincipales] = useState<Caisse[]>([]);\n\tconst [loadingData, setLoadingData] = useState(true);\n\n\t// Vérifier les permissions\n\tconst canCreateCaisses =\n\t\tsession?.user && (session.user as any).role === \"secretary_general\";\n\n\tconst form = useForm<CaisseForm>({\n\t\tresolver: zodResolver(caisseSchema),\n\t\tdefaultValues: {\n\t\t\tnom: \"\",\n\t\t\ttype: CaisseType.PRINCIPALE,\n\t\t\tsoldeActuel: 0,\n\t\t\tsessionId: \"\",\n\t\t\tcaissePrincipaleId: \"\",\n\t\t},\n\t});\n\n\tconst watchType = form.watch(\"type\");\n\n\t// Charger les données nécessaires\n\tuseEffect(() => {\n\t\tconst loadData = async () => {\n\t\t\ttry {\n\t\t\t\tsetLoadingData(true);\n\t\t\t\tconst [sessionsData, caissesData] = await Promise.all([\n\t\t\t\t\tapi.getSessions(),\n\t\t\t\t\tapi.getCaisses(),\n\t\t\t\t]);\n\n\t\t\t\tsetSessions(sessionsData);\n\t\t\t\tsetCaissesPrincipales(\n\t\t\t\t\tcaissesData.filter((c) => c.type === CaisseType.PRINCIPALE),\n\t\t\t\t);\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(\"Erreur lors du chargement des données:\", error);\n\t\t\t} finally {\n\t\t\t\tsetLoadingData(false);\n\t\t\t}\n\t\t};\n\n\t\tif (session?.accessToken) {\n\t\t\tloadData();\n\t\t}\n\t}, [status]);\n\n\tconst onSubmit = async (data: CaisseForm) => {\n\t\tif (!canCreateCaisses) {\n\t\t\tsetError(\"Vous n'avez pas les permissions pour créer une caisse\");\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tsetIsLoading(true);\n\t\t\tsetError(null);\n\n\t\t\t// Préparer les données\n\t\t\tconst caisseData = {\n\t\t\t\tnom: data.nom,\n\t\t\t\ttype: data.type,\n\t\t\t\tsoldeActuel: data.soldeActuel,\n\t\t\t\t...(data.type === CaisseType.REUNION && {\n\t\t\t\t\tsessionId: data.sessionId,\n\t\t\t\t\tcaissePrincipaleId: data.caissePrincipaleId,\n\t\t\t\t}),\n\t\t\t};\n\n\t\t\tawait api.createCaisse(caisseData);\n\t\t\trouter.push(\"/dashboard/caisses\");\n\t\t} catch (error: any) {\n\t\t\tconsole.error(\"Erreur lors de la création:\", error);\n\t\t\tsetError(error.message || \"Une erreur est survenue lors de la création\");\n\t\t} finally {\n\t\t\tsetIsLoading(false);\n\t\t}\n\t};\n\n\tif (!canCreateCaisses) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Button variant=\"outline\" size=\"icon\" asChild>\n\t\t\t\t\t\t<Link href=\"/dashboard/caisses\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4\" />\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</Button>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<h1 className=\"text-3xl font-bold tracking-tight\">\n\t\t\t\t\t\t\tNouvelle Caisse\n\t\t\t\t\t\t</h1>\n\t\t\t\t\t\t<p className=\"text-muted-foreground\">Créer une nouvelle caisse</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<Card>\n\t\t\t\t\t<CardContent className=\"pt-6\">\n\t\t\t\t\t\t<div className=\"text-center py-8\">\n\t\t\t\t\t\t\t<p className=\"text-muted-foreground\">\n\t\t\t\t\t\t\t\tVous n'avez pas les permissions pour créer une caisse.\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t<p className=\"text-sm text-muted-foreground mt-2\">\n\t\t\t\t\t\t\t\tSeuls les administrateurs et trésoriers peuvent créer des\n\t\t\t\t\t\t\t\tcaisses.\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t</div>\n\t\t);\n\t}\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* En-tête */}\n\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t<Button variant=\"outline\" size=\"icon\" asChild>\n\t\t\t\t\t<Link href=\"/dashboard/caisses\">\n\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4\" />\n\t\t\t\t\t</Link>\n\t\t\t\t</Button>\n\t\t\t\t<div>\n\t\t\t\t\t<h1 className=\"text-3xl font-bold tracking-tight\">Nouvelle Caisse</h1>\n\t\t\t\t\t<p className=\"text-muted-foreground\">\n\t\t\t\t\t\tCréer une nouvelle caisse principale ou de réunion\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t{/* Formulaire */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle className=\"flex items-center gap-2\">\n\t\t\t\t\t\t<Wallet className=\"h-5 w-5\" />\n\t\t\t\t\t\tInformations de la caisse\n\t\t\t\t\t</CardTitle>\n\t\t\t\t\t<CardDescription>\n\t\t\t\t\t\tDéfinissez les paramètres de la nouvelle caisse\n\t\t\t\t\t</CardDescription>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t{loadingData ? (\n\t\t\t\t\t\t<div className=\"flex items-center justify-center py-8\">\n\t\t\t\t\t\t\t<div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900\"></div>\n\t\t\t\t\t\t\t<span className=\"ml-2\">Chargement des données...</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<Form {...form}>\n\t\t\t\t\t\t\t<form\n\t\t\t\t\t\t\t\tonSubmit={form.handleSubmit(onSubmit)}\n\t\t\t\t\t\t\t\tclassName=\"space-y-6\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{error && (\n\t\t\t\t\t\t\t\t\t<div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-sm text-red-600\">{error}</p>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t\t\t<div className=\"grid gap-6 md:grid-cols-2\">\n\t\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\t\tname=\"nom\"\n\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Nom de la caisse</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Ex: Caisse Principale 2025\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t\tNom descriptif de la caisse\n\t\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\t\tname=\"type\"\n\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Type de caisse</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\t\t\tonValueChange={field.onChange}\n\t\t\t\t\t\t\t\t\t\t\t\t\tdefaultValue={field.value}\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Sélectionnez le type\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value={CaisseType.PRINCIPALE}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tPrincipale\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value={CaisseType.REUNION}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tRéunion\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{watchType === CaisseType.PRINCIPALE\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"Caisse pour les fonds consolidés\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"Caisse liée à une session spécifique\"}\n\t\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"soldeActuel\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t\t\t\t<DollarSign className=\"h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t\tSolde initial (FCFA)\n\t\t\t\t\t\t\t\t\t\t\t</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) =>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield.onChange(parseInt(e.target.value) || 0)\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\tMontant initial dans la caisse\n\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t{watchType === CaisseType.REUNION && (\n\t\t\t\t\t\t\t\t\t<div className=\"grid gap-6 md:grid-cols-2\">\n\t\t\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\t\t\tname=\"sessionId\"\n\t\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Session associée</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonValueChange={field.onChange}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdefaultValue={field.value}\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Sélectionnez une session\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{sessions.map((session) => (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={session._id}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={session._id}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{session.annee} (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{new Date(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsession.dateDebut,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t).toLocaleDateString()}{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t-{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{new Date(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsession.dateFin,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t).toLocaleDateString()}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tSession à laquelle cette caisse est liée\n\t\t\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\t\t\tname=\"caissePrincipaleId\"\n\t\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Caisse principale</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonValueChange={field.onChange}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdefaultValue={field.value}\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Sélectionnez une caisse principale\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{caissesPrincipales.map((caisse) => (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem key={caisse._id} value={caisse._id}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{caisse.nom} (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{caisse.soldeActuel.toLocaleString()} FCFA)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tCaisse principale pour l'émargement\n\t\t\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t\t\t<div className=\"flex justify-end gap-4\">\n\t\t\t\t\t\t\t\t\t<Button variant=\"outline\" asChild>\n\t\t\t\t\t\t\t\t\t\t<Link href=\"/dashboard/caisses\">Annuler</Link>\n\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t<Button type=\"submit\" disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t\t{isLoading ? \"Création...\" : \"Créer la caisse\"}\n\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</form>\n\t\t\t\t\t\t</Form>\n\t\t\t\t\t)}\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\n\t\t\t{/* Informations supplémentaires */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle>Types de caisses</CardTitle>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<div className=\"space-y-4\">\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<h4 className=\"font-medium\">Caisse Principale</h4>\n\t\t\t\t\t\t\t<p className=\"text-sm text-muted-foreground\">\n\t\t\t\t\t\t\t\tCaisse pour consolider les fonds de toutes les réunions. Les\n\t\t\t\t\t\t\t\tfonds des caisses de réunion peuvent être émargés vers cette\n\t\t\t\t\t\t\t\tcaisse.\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<h4 className=\"font-medium\">Caisse de Réunion</h4>\n\t\t\t\t\t\t\t<p className=\"text-sm text-muted-foreground\">\n\t\t\t\t\t\t\t\tCaisse liée à une session spécifique. Doit être associée à une\n\t\t\t\t\t\t\t\tcaisse principale pour permettre l'émargement des fonds.\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": "0PAmBA,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAAM,CAAA,EAAa,CAAA,CAAA,CAAA,CAAA,CAAb,AAAa,CAAb,AAAa,CAAb,AAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAhBA,CAgBe,AAfjD,CAeiD,AAfhD,CAegD,AAfhD,CAAA,AAegD,CAfhD,AAegD,CAAA,AAfhD,CAAA,AAegD,CAAA,AAfhD,CAAQ,AAewC,AAfhD,CAegD,AAfxC,AAAE,CAAA,AAegD,CAfhD,AAegD,CAf5C,CAAA,CAAA,CAAA,CAAA,CAAA,AAAM,CAAA,CAAA,CAAA,AAAI,CAAA,CAAA,CAAA,CAAA,CAAA,AAAM,EAAA,CAAA,AAAI,CAAA,CAAA,CAAA,CAAK,AAAL,CAAK,CAAA,CAAA,AAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACjE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAqD,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACpF,sECHA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OAGI,EAAQ,EAAA,UAAgB,CAAC,CAAC,EAAO,IACZ,CAAA,EAAA,EAAA,GAAA,AAAG,EACxB,EAAA,SAAS,CAAC,KAAK,CACf,CACE,GAAG,CAAK,CACR,IAAK,EACL,YAAa,AAAC,IACG,AACX,EADiB,MAAM,CAChB,OAAO,CAAC,oCAAoC,CACvD,EAAM,WAAW,GAAG,GAChB,CAAC,EAAM,gBAAgB,EAAI,EAAM,MAAM,CAAG,GAAG,EAAM,cAAc,GACvE,CACF,IAGJ,EAAM,WAAW,CAhBN,EAgBS,MCjBpB,IAAA,EAAA,EAAA,CAAA,CAAA,OAEA,SAAS,EAAM,CACb,WAAS,CACT,GAAG,EAC8C,EACjD,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,ADWM,ECXN,CACC,YAAU,QACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,sNACA,GAED,GAAG,CAAK,EAGf,uDCFA,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAAM,CAAA,EAAY,CAAA,CAAA,CAAA,CAAA,AAAZ,CAAY,AAAZ,CAAY,AAAZ,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAhBC,CAClC,AAe+C,CAAA,AAf9C,CAe8C,AAf9C,CAe8C,AAf9C,CAAA,AAe8C,CAf9C,AAe8C,CAf9C,AAe8C,CAAA,AAf9C,CAAA,AAAQ,AAesC,CAftC,AAAE,AAeoC,CAAU,CAf3C,AAe2C,CAf3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAkB,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAC/C,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAY,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAC3C,kECJA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAOA,EAAA,EAAA,CAAA,CAAA,OASA,EAAA,EAAA,CAAA,CAAA,OAOA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,IAAM,EAAe,EAAA,CAAC,CACpB,MAAM,CAAC,CACP,IAAK,EAAA,CAAC,CACJ,MAAM,GACN,GAAG,CAAC,EAAG,qBACP,GAAG,CAAC,IAAK,wBACX,KAAM,EAAA,CAAC,CAAC,UAAU,CAAC,EAAA,UAAU,CAAE,CAC9B,eAAgB,oBACjB,GACA,YAAa,EAAA,CAAC,CACZ,MAAM,GACN,GAAG,CAAC,EAAG,qCACP,GAAG,CAAC,IAAU,iDAChB,UAAW,EAAA,CAAC,CAAC,MAAM,GAAG,QAAQ,GAC9B,mBAAoB,EAAA,CAAC,CAAC,MAAM,GAAG,QAAQ,EACxC,GACC,MAAM,CACN,AAAC,GACA,AAAI,EAAK,IAAI,GAAK,EAAA,UAAU,CAAC,OAAO,EAAE,AAC9B,EAAK,SAAS,EAAI,EAAK,kBAAkB,CAIlD,CACC,QACC,+EACD,KAAM,CAAC,YAAY,AACpB,GAGa,SAAS,IACvB,GAAM,CAAE,KAAM,CAAO,QAAE,CAAM,CAAE,CAAG,CAAA,EAAA,EAAA,UAAA,AAAU,IACtC,EAAS,CAAA,EAAA,EAAA,SAAS,AAAT,IACT,EAAM,CAAA,EAAA,EAAA,MAAA,AAAM,IAEZ,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACrC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAgB,MAC5C,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAY,EAAE,EAChD,CAAC,EAAoB,EAAsB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAW,EAAE,EACnE,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,GAAS,GAGzC,EACL,GAAS,MAAuC,sBAA9B,EAAQ,IAAI,CAAS,IAAI,CAEtC,EAAO,CAAA,EAAA,EAAA,OAAO,AAAP,EAAoB,CAChC,SAAU,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,GACtB,cAAe,CACd,IAAK,GACL,KAAM,EAAA,UAAU,CAAC,UAAU,CAC3B,YAAa,EACb,UAAW,GACX,mBAAoB,EACrB,CACD,GAEM,EAAY,EAAK,KAAK,CAAC,QAG7B,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACT,IAAM,EAAW,UAChB,GAAI,CACH,GAAe,GACf,GAAM,CAAC,EAAc,EAAY,CAAG,MAAM,QAAQ,GAAG,CAAC,CACrD,EAAI,WAAW,GACf,EAAI,UAAU,GACd,EAED,EAAY,GACZ,EACC,EAAY,MAAM,CAAC,AAAC,GAAM,EAAE,IAAI,GAAK,EAAA,UAAU,CAAC,UAAU,EAE5D,CAAE,MAAO,EAAO,CACf,QAAQ,KAAK,CAAC,yCAA0C,EACzD,QAAU,CACT,GAAe,EAChB,CACD,EAEI,GAAS,aAAa,AACzB,GAEF,EAAG,CAAC,EAAO,EAEX,IAAM,EAAW,MAAO,IACvB,GAAI,CAAC,EAAkB,YACtB,EAAS,yDAIV,GAAI,CACH,GAAa,GACb,EAAS,MAGT,IAAM,EAAa,CAClB,IAAK,EAAK,GAAG,CACb,KAAM,EAAK,IAAI,CACf,YAAa,EAAK,WAAW,CAC7B,GAAI,EAAK,IAAI,GAAK,EAAA,UAAU,CAAC,OAAO,EAAI,CACvC,UAAW,EAAK,SAAS,CACzB,mBAAoB,EAAK,kBAAkB,AAC5C,CAAC,AACF,CAEA,OAAM,EAAI,YAAY,CAAC,GACvB,EAAO,IAAI,CAAC,qBACb,CAAE,MAAO,EAAY,CACpB,QAAQ,KAAK,CAAC,8BAA+B,GAC7C,EAAS,EAAM,OAAO,EAAI,8CAC3B,QAAU,CACT,GAAa,EACd,CACD,SAEA,AAAK,EAmCJ,CAAA,CAnCG,CAmCH,EAAA,IAAA,EAAC,KAnCqB,CAmCrB,CAAI,UAAU,sBAEd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,KAAK,OAAO,OAAO,CAAA,CAAA,WAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,8BACV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,gBAGvB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6CAAoC,oBAClD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,6DAOvC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,WACV,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,oCACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,YAAY,+BAG/B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,uDAIlB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,EACA,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iEACf,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,gBAAO,iCAGxB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAE,GAAG,CAAI,UACb,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CACA,SAAU,EAAK,YAAY,CAAC,GAC5B,UAAU,sBAET,GACA,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0DACd,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,gCAAwB,MAIvC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,MACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,qBACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACL,YAAY,6BACX,GAAG,CAAK,KAGX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,gCAGjB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,QAKf,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,OACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,mBACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CACN,cAAe,EAAM,QAAQ,CAC7B,aAAc,EAAM,KAAK,WAEzB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,UACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,YAAY,6BAG3B,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,aAAa,CAAA,WACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,MAAO,EAAA,UAAU,CAAC,UAAU,UAAE,eAG1C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,MAAO,EAAA,UAAU,CAAC,OAAO,UAAE,kBAKzC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UACd,IAAc,EAAA,UAAU,CAAC,UAAU,CACjC,mCACA,yCAEJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,WAMhB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,cACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,oCACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,YAAY,0BAGnC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACL,KAAK,SACJ,GAAG,CAAK,CACT,SAAU,AAAC,GACV,EAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,GAAK,OAI9C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,mCAGjB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,QAKd,IAAc,EAAA,UAAU,CAAC,OAAO,EAChC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,YACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,qBACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CACN,cAAe,EAAM,QAAQ,CAC7B,aAAc,EAAM,KAAK,WAEzB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,UACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,YAAY,iCAG3B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,UACZ,EAAS,GAAG,CAAC,AAAC,GACd,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAEV,MAAO,EAAQ,GAAG,WAEjB,EAAQ,KAAK,CAAC,KACd,IAAI,KACJ,EAAQ,SAAS,EAChB,kBAAkB,GAAI,IAAI,IAC1B,IACD,IAAI,KACJ,EAAQ,OAAO,EACd,kBAAkB,GAAG,MAVlB,EAAQ,GAAG,QAgBpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,6CAGjB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,QAKf,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,qBACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,sBACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CACN,cAAe,EAAM,QAAQ,CAC7B,aAAc,EAAM,KAAK,WAEzB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,UACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,YAAY,2CAG3B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,UACZ,EAAmB,GAAG,CAAC,AAAC,GACxB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAkB,MAAO,EAAO,GAAG,WAC5C,EAAO,GAAG,CAAC,KACX,EAAO,WAAW,CAAC,cAAc,GAAG,WAFrB,EAAO,GAAG,QAO9B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,wCAGjB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,WAOjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,OAAO,CAAA,CAAA,WAChC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,8BAAqB,cAEjC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,KAAK,SAAS,SAAU,WAC9B,EAAY,cAAgB,iCAUpC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,UACV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,uBAEZ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uBAAc,sBAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yCAAgC,yIAM9C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uBAAc,sBAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yCAAgC,0IA7QjD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,KAAK,OAAO,OAAO,CAAA,CAAA,WAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,8BACV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,gBAGvB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6CAAoC,oBAGlD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,oCAIvC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,UACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,gBACtB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACd,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,2DAGrC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8CAAqC,gFAkQzD", "ignoreList": [0, 1, 3]}