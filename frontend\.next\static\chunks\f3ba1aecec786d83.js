(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,38909,e=>{"use strict";e.s(["Primitive",()=>o,"dispatchDiscreteCustomEvent",()=>a]);var t=e.i(38477),r=e.i(41902),n=e.i(81221),i=e.i(4051),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let o=(0,n.createSlot)("Primitive.".concat(r)),a=t.forwardRef((e,t)=>{let{asChild:n,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?o:r,{...a,ref:t})});return a.displayName="Primitive.".concat(r),{...e,[r]:a}},{});function a(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},72355,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"warnOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},44571,e=>{"use strict";e.s(["default",()=>a],44571);var t=e.i(38477);let r=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,t.forwardRef)((e,r)=>{let{color:o="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:s,className:u="",children:d,iconNode:c,...p}=e;return(0,t.createElement)("svg",{ref:r,...i,width:a,height:a,stroke:o,strokeWidth:s?24*Number(l)/Number(a):l,className:n("lucide",u),...!d&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(p)&&{"aria-hidden":"true"},...p},[...c.map(e=>{let[r,n]=e;return(0,t.createElement)(r,n)}),...Array.isArray(d)?d:[d]])}),a=(e,i)=>{let a=(0,t.forwardRef)((a,l)=>{let{className:s,...u}=a;return(0,t.createElement)(o,{ref:l,iconNode:i,className:n("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),s),...u})});return a.displayName=r(e),a}},96134,e=>{"use strict";e.s(["Input",()=>n]);var t=e.i(4051),r=e.i(41428);function n(e){let{className:n,type:i,...o}=e;return(0,t.jsx)("input",{type:i,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",n),...o})}},65023,e=>{"use strict";e.s(["Root",()=>a,"VISUALLY_HIDDEN_STYLES",()=>i]);var t=e.i(38477),r=e.i(38909),n=e.i(4051),i=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),o=t.forwardRef((e,t)=>(0,n.jsx)(r.Primitive.span,{...e,ref:t,style:{...i,...e.style}}));o.displayName="VisuallyHidden";var a=o},67435,e=>{"use strict";e.s(["Calendar",()=>t],67435);let t=(0,e.i(44571).default)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},42633,e=>{"use strict";e.s(["Wallet",()=>t],42633);let t=(0,e.i(44571).default)("wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]])},60019,e=>{"use strict";e.s(["Search",()=>t],60019);let t=(0,e.i(44571).default)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},90253,e=>{"use strict";e.s(["Presence",()=>i]);var t=e.i(38477),r=e.i(44636),n=e.i(97945),i=e=>{let{present:i,children:a}=e,l=function(e){var r,i;let[a,l]=t.useState(),s=t.useRef(null),u=t.useRef(e),d=t.useRef("none"),[c,p]=(r=e?"mounted":"unmounted",i={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,t)=>{let r=i[e][t];return null!=r?r:e},r));return t.useEffect(()=>{let e=o(s.current);d.current="mounted"===c?e:"none"},[c]),(0,n.useLayoutEffect)(()=>{let t=s.current,r=u.current;if(r!==e){let n=d.current,i=o(t);e?p("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):r&&n!==i?p("ANIMATION_OUT"):p("UNMOUNT"),u.current=e}},[e,p]),(0,n.useLayoutEffect)(()=>{if(a){var e;let t,r=null!=(e=a.ownerDocument.defaultView)?e:window,n=e=>{let n=o(s.current).includes(CSS.escape(e.animationName));if(e.target===a&&n&&(p("ANIMATION_END"),!u.current)){let e=a.style.animationFillMode;a.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=e)})}},i=e=>{e.target===a&&(d.current=o(s.current))};return a.addEventListener("animationstart",i),a.addEventListener("animationcancel",n),a.addEventListener("animationend",n),()=>{r.clearTimeout(t),a.removeEventListener("animationstart",i),a.removeEventListener("animationcancel",n),a.removeEventListener("animationend",n)}}p("ANIMATION_END")},[a,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:t.useCallback(e=>{s.current=e?getComputedStyle(e):null,l(e)},[])}}(i),s="function"==typeof a?a({present:l.isPresent}):t.Children.only(a),u=(0,r.useComposedRefs)(l.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,i=n&&"isReactWarning"in n&&n.isReactWarning;return i?e.ref:(i=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof a||l.isPresent?t.cloneElement(s,{ref:u}):null};function o(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},1827,e=>{"use strict";e.s(["Users",()=>t],1827);let t=(0,e.i(44571).default)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},28149,19203,e=>{"use strict";e.s(["Close",()=>en,"Content",()=>ee,"Description",()=>er,"Overlay",()=>X,"Portal",()=>Q,"Root",()=>G,"Title",()=>et,"Trigger",()=>J],28149);var t=e.i(38477),r=e.i(94798),n=e.i(44636),i=e.i(1767),o=e.i(33348),a=e.i(26183),l=e.i(76355),s=e.i(7535),u=e.i(37439),d=e.i(90253),c=e.i(38909),p=e.i(15589),f=e.i(54574),m=e.i(23952),v=e.i(81221),g=e.i(4051),y="Dialog",[h,b]=(0,i.createContextScope)(y),[w,x]=h(y),N=e=>{let{__scopeDialog:r,children:n,open:i,defaultOpen:l,onOpenChange:s,modal:u=!0}=e,d=t.useRef(null),c=t.useRef(null),[p,f]=(0,a.useControllableState)({prop:i,defaultProp:null!=l&&l,onChange:s,caller:y});return(0,g.jsx)(w,{scope:r,triggerRef:d,contentRef:c,contentId:(0,o.useId)(),titleId:(0,o.useId)(),descriptionId:(0,o.useId)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:u,children:n})};N.displayName=y;var R="DialogTrigger",C=t.forwardRef((e,t)=>{let{__scopeDialog:i,...o}=e,a=x(R,i),l=(0,n.useComposedRefs)(t,a.triggerRef);return(0,g.jsx)(c.Primitive.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":q(a.open),...o,ref:l,onClick:(0,r.composeEventHandlers)(e.onClick,a.onOpenToggle)})});C.displayName=R;var O="DialogPortal",[D,j]=h(O,{forceMount:void 0}),E=e=>{let{__scopeDialog:r,forceMount:n,children:i,container:o}=e,a=x(O,r);return(0,g.jsx)(D,{scope:r,forceMount:n,children:t.Children.map(i,e=>(0,g.jsx)(d.Presence,{present:n||a.open,children:(0,g.jsx)(u.Portal,{asChild:!0,container:o,children:e})}))})};E.displayName=O;var I="DialogOverlay",P=t.forwardRef((e,t)=>{let r=j(I,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,o=x(I,e.__scopeDialog);return o.modal?(0,g.jsx)(d.Presence,{present:n||o.open,children:(0,g.jsx)(M,{...i,ref:t})}):null});P.displayName=I;var k=(0,v.createSlot)("DialogOverlay.RemoveScroll"),M=t.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=x(I,r);return(0,g.jsx)(f.RemoveScroll,{as:k,allowPinchZoom:!0,shards:[i.contentRef],children:(0,g.jsx)(c.Primitive.div,{"data-state":q(i.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),T="DialogContent",A=t.forwardRef((e,t)=>{let r=j(T,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,o=x(T,e.__scopeDialog);return(0,g.jsx)(d.Presence,{present:n||o.open,children:o.modal?(0,g.jsx)(S,{...i,ref:t}):(0,g.jsx)(_,{...i,ref:t})})});A.displayName=T;var S=t.forwardRef((e,i)=>{let o=x(T,e.__scopeDialog),a=t.useRef(null),l=(0,n.useComposedRefs)(i,o.contentRef,a);return t.useEffect(()=>{let e=a.current;if(e)return(0,m.hideOthers)(e)},[]),(0,g.jsx)(F,{...e,ref:l,trapFocus:o.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.composeEventHandlers)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=o.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,r.composeEventHandlers)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,r.composeEventHandlers)(e.onFocusOutside,e=>e.preventDefault())})}),_=t.forwardRef((e,r)=>{let n=x(T,e.__scopeDialog),i=t.useRef(!1),o=t.useRef(!1);return(0,g.jsx)(F,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(i.current||null==(a=n.triggerRef.current)||a.focus(),t.preventDefault()),i.current=!1,o.current=!1},onInteractOutside:t=>{var r,a;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(i.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let l=t.target;(null==(a=n.triggerRef.current)?void 0:a.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),F=t.forwardRef((e,r)=>{let{__scopeDialog:i,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:u,...d}=e,c=x(T,i),f=t.useRef(null),m=(0,n.useComposedRefs)(r,f);return(0,p.useFocusGuards)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(s.FocusScope,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:u,children:(0,g.jsx)(l.DismissableLayer,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":q(c.open),...d,ref:m,onDismiss:()=>c.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(Y,{titleId:c.titleId}),(0,g.jsx)($,{contentRef:f,descriptionId:c.descriptionId})]})]})}),U="DialogTitle",L=t.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=x(U,r);return(0,g.jsx)(c.Primitive.h2,{id:i.titleId,...n,ref:t})});L.displayName=U;var W="DialogDescription",H=t.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=x(W,r);return(0,g.jsx)(c.Primitive.p,{id:i.descriptionId,...n,ref:t})});H.displayName=W;var B="DialogClose",V=t.forwardRef((e,t)=>{let{__scopeDialog:n,...i}=e,o=x(B,n);return(0,g.jsx)(c.Primitive.button,{type:"button",...i,ref:t,onClick:(0,r.composeEventHandlers)(e.onClick,()=>o.onOpenChange(!1))})});function q(e){return e?"open":"closed"}V.displayName=B;var K="DialogTitleWarning",[Z,z]=(0,i.createContext)(K,{contentName:T,titleName:U,docsSlug:"dialog"}),Y=e=>{let{titleId:r}=e,n=z(K),i="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return t.useEffect(()=>{r&&(document.getElementById(r)||console.error(i))},[i,r]),null},$=e=>{let{contentRef:r,descriptionId:n}=e,i=z("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(i.contentName,"}.");return t.useEffect(()=>{var e;let t=null==(e=r.current)?void 0:e.getAttribute("aria-describedby");n&&t&&(document.getElementById(n)||console.warn(o))},[o,r,n]),null},G=N,J=C,Q=E,X=P,ee=A,et=L,er=H,en=V;e.s(["default",()=>ei],19203);let ei=(0,e.i(44571).default)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])}]);