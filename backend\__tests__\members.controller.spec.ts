import 'reflect-metadata';
import { Test } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { MembersController } from '../src/members/members.controller';
import { MembersService } from '../src/members/members.service';
import { AppTestingModule } from './app-testing.module';

const serviceMock = {
  create: jest.fn().mockResolvedValue({ _id: 'm1' }),
  findAll: jest.fn().mockResolvedValue([{ _id: 'm1' }]),
  findOne: jest.fn().mockResolvedValue({ _id: 'm1' }),
  update: jest.fn().mockResolvedValue({ _id: 'm1' }),
  remove: jest.fn().mockResolvedValue({}),
  paymentsDebrief: jest.fn().mockResolvedValue({ totalIn: 0, totalOut: 0 }),
};

describe('MembersController', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      controllers: [MembersController],
      providers: [{ provide: MembersService, useValue: serviceMock }],
    })
      .overrideGuard(require('../src/auth/jwt-auth.guard').JwtAuthGuard)
      .useClass(require('./utils/mock-guards').MockJwtAuthGuard)
      .overrideGuard(require('../src/common/guards/role.guard').RolesGuard)
      .useClass(require('./utils/mock-guards').MockRolesGuard)
      .compile();

    app = moduleRef.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('POST /members', async () => {
    await request(app.getHttpServer()).post('/members').set('x-user-role','secretary_general').send({ firstName: 'A', lastName: 'B' }).expect(201);
  });

  it('GET /members', async () => {
    await request(app.getHttpServer()).get('/members').set('x-user-role','controller').expect(200);
  });

  it('GET /members/:id', async () => {
    await request(app.getHttpServer()).get('/members/m1').set('x-user-role','controller').expect(200);
  });

  it('PATCH /members/:id', async () => {
    await request(app.getHttpServer()).patch('/members/m1').set('x-user-role','secretary_general').send({ lastName: 'C' }).expect(200);
  });

  it('DELETE /members/:id', async () => {
    await request(app.getHttpServer()).delete('/members/m1').set('x-user-role','secretary_general').expect(200);
  });

  it('GET /members/:id/debrief', async () => {
    await request(app.getHttpServer()).get('/members/m1/debrief').set('x-user-role','controller').expect(200);
  });
});