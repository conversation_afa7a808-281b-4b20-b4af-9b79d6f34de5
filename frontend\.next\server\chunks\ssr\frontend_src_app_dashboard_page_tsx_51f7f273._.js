module.exports=[15881,a=>{"use strict";a.s(["default",()=>H],15881);var b=a.i(68116),c=a.i(128),d=a.i(81223),e=a.i(33055),f=a.i(75780),g=a.i(2979),h=a.i(62303),i=a.i(20762),j=a.i(72376),k=a.i(91486);let l=(0,a.i(621).default)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var m=a.i(15285),n=a.i(99731),o=a.i(25405),p=a.i(20475),q=a.i(11257),r=a.i(84536),s=a.i(18777),t=class extends p.Subscribable{constructor(a,b){super(),this.options=b,this.#a=a,this.#b=null,this.#c=(0,q.pendingThenable)(),this.bindMethods(),this.setOptions(b)}#a;#d=void 0;#e=void 0;#f=void 0;#g;#h;#c;#b;#i;#j;#k;#l;#m;#n;#o=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#d.addObserver(this),u(this.#d,this.options)?this.#p():this.updateResult(),this.#q())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return v(this.#d,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return v(this.#d,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#r(),this.#s(),this.#d.removeObserver(this)}setOptions(a){let b=this.options,c=this.#d;if(this.options=this.#a.defaultQueryOptions(a),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,r.resolveEnabled)(this.options.enabled,this.#d))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#t(),this.#d.setOptions(this.options),b._defaulted&&!(0,r.shallowEqualObjects)(this.options,b)&&this.#a.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#d,observer:this});let d=this.hasListeners();d&&w(this.#d,c,this.options,b)&&this.#p(),this.updateResult(),d&&(this.#d!==c||(0,r.resolveEnabled)(this.options.enabled,this.#d)!==(0,r.resolveEnabled)(b.enabled,this.#d)||(0,r.resolveStaleTime)(this.options.staleTime,this.#d)!==(0,r.resolveStaleTime)(b.staleTime,this.#d))&&this.#u();let e=this.#v();d&&(this.#d!==c||(0,r.resolveEnabled)(this.options.enabled,this.#d)!==(0,r.resolveEnabled)(b.enabled,this.#d)||e!==this.#n)&&this.#w(e)}getOptimisticResult(a){var b,c;let d=this.#a.getQueryCache().build(this.#a,a),e=this.createResult(d,a);return b=this,c=e,(0,r.shallowEqualObjects)(b.getCurrentResult(),c)||(this.#f=e,this.#h=this.options,this.#g=this.#d.state),e}getCurrentResult(){return this.#f}trackResult(a,b){return new Proxy(a,{get:(a,c)=>(this.trackProp(c),b?.(c),"promise"!==c||this.options.experimental_prefetchInRender||"pending"!==this.#c.status||this.#c.reject(Error("experimental_prefetchInRender feature flag is not enabled")),Reflect.get(a,c))})}trackProp(a){this.#o.add(a)}getCurrentQuery(){return this.#d}refetch({...a}={}){return this.fetch({...a})}fetchOptimistic(a){let b=this.#a.defaultQueryOptions(a),c=this.#a.getQueryCache().build(this.#a,b);return c.fetch().then(()=>this.createResult(c,b))}fetch(a){return this.#p({...a,cancelRefetch:a.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#f))}#p(a){this.#t();let b=this.#d.fetch(this.options,a);return a?.throwOnError||(b=b.catch(r.noop)),b}#u(){this.#r();let a=(0,r.resolveStaleTime)(this.options.staleTime,this.#d);if(r.isServer||this.#f.isStale||!(0,r.isValidTimeout)(a))return;let b=(0,r.timeUntilStale)(this.#f.dataUpdatedAt,a);this.#l=s.timeoutManager.setTimeout(()=>{this.#f.isStale||this.updateResult()},b+1)}#v(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#d):this.options.refetchInterval)??!1}#w(a){this.#s(),this.#n=a,!r.isServer&&!1!==(0,r.resolveEnabled)(this.options.enabled,this.#d)&&(0,r.isValidTimeout)(this.#n)&&0!==this.#n&&(this.#m=s.timeoutManager.setInterval(()=>{(this.options.refetchIntervalInBackground||m.focusManager.isFocused())&&this.#p()},this.#n))}#q(){this.#u(),this.#w(this.#v())}#r(){this.#l&&(s.timeoutManager.clearTimeout(this.#l),this.#l=void 0)}#s(){this.#m&&(s.timeoutManager.clearInterval(this.#m),this.#m=void 0)}createResult(a,b){let c,d=this.#d,e=this.options,f=this.#f,g=this.#g,h=this.#h,i=a!==d?a.state:this.#e,{state:j}=a,k={...j},l=!1;if(b._optimisticResults){let c=this.hasListeners(),f=!c&&u(a,b),g=c&&w(a,d,b,e);(f||g)&&(k={...k,...(0,o.fetchState)(j.data,a.options)}),"isRestoring"===b._optimisticResults&&(k.fetchStatus="idle")}let{error:m,errorUpdatedAt:n,status:p}=k;c=k.data;let s=!1;if(void 0!==b.placeholderData&&void 0===c&&"pending"===p){let a;f?.isPlaceholderData&&b.placeholderData===h?.placeholderData?(a=f.data,s=!0):a="function"==typeof b.placeholderData?b.placeholderData(this.#k?.state.data,this.#k):b.placeholderData,void 0!==a&&(p="success",c=(0,r.replaceData)(f?.data,a,b),l=!0)}if(b.select&&void 0!==c&&!s)if(f&&c===g?.data&&b.select===this.#i)c=this.#j;else try{this.#i=b.select,c=b.select(c),c=(0,r.replaceData)(f?.data,c,b),this.#j=c,this.#b=null}catch(a){this.#b=a}this.#b&&(m=this.#b,c=this.#j,n=Date.now(),p="error");let t="fetching"===k.fetchStatus,v="pending"===p,y="error"===p,z=v&&t,A=void 0!==c,B={status:p,fetchStatus:k.fetchStatus,isPending:v,isSuccess:"success"===p,isError:y,isInitialLoading:z,isLoading:z,data:c,dataUpdatedAt:k.dataUpdatedAt,error:m,errorUpdatedAt:n,failureCount:k.fetchFailureCount,failureReason:k.fetchFailureReason,errorUpdateCount:k.errorUpdateCount,isFetched:k.dataUpdateCount>0||k.errorUpdateCount>0,isFetchedAfterMount:k.dataUpdateCount>i.dataUpdateCount||k.errorUpdateCount>i.errorUpdateCount,isFetching:t,isRefetching:t&&!v,isLoadingError:y&&!A,isPaused:"paused"===k.fetchStatus,isPlaceholderData:l,isRefetchError:y&&A,isStale:x(a,b),refetch:this.refetch,promise:this.#c,isEnabled:!1!==(0,r.resolveEnabled)(b.enabled,a)};if(this.options.experimental_prefetchInRender){let b=a=>{"error"===B.status?a.reject(B.error):void 0!==B.data&&a.resolve(B.data)},c=()=>{b(this.#c=B.promise=(0,q.pendingThenable)())},e=this.#c;switch(e.status){case"pending":a.queryHash===d.queryHash&&b(e);break;case"fulfilled":("error"===B.status||B.data!==e.value)&&c();break;case"rejected":("error"!==B.status||B.error!==e.reason)&&c()}}return B}updateResult(){let a=this.#f,b=this.createResult(this.#d,this.options);if(this.#g=this.#d.state,this.#h=this.options,void 0!==this.#g.data&&(this.#k=this.#d),(0,r.shallowEqualObjects)(b,a))return;this.#f=b;let c=()=>{if(!a)return!0;let{notifyOnChangeProps:b}=this.options,c="function"==typeof b?b():b;if("all"===c||!c&&!this.#o.size)return!0;let d=new Set(c??this.#o);return this.options.throwOnError&&d.add("error"),Object.keys(this.#f).some(b=>this.#f[b]!==a[b]&&d.has(b))};this.#x({listeners:c()})}#t(){let a=this.#a.getQueryCache().build(this.#a,this.options);if(a===this.#d)return;let b=this.#d;this.#d=a,this.#e=a.state,this.hasListeners()&&(b?.removeObserver(this),a.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#q()}#x(a){n.notifyManager.batch(()=>{a.listeners&&this.listeners.forEach(a=>{a(this.#f)}),this.#a.getQueryCache().notify({query:this.#d,type:"observerResultsUpdated"})})}};function u(a,b){return!1!==(0,r.resolveEnabled)(b.enabled,a)&&void 0===a.state.data&&("error"!==a.state.status||!1!==b.retryOnMount)||void 0!==a.state.data&&v(a,b,b.refetchOnMount)}function v(a,b,c){if(!1!==(0,r.resolveEnabled)(b.enabled,a)&&"static"!==(0,r.resolveStaleTime)(b.staleTime,a)){let d="function"==typeof c?c(a):c;return"always"===d||!1!==d&&x(a,b)}return!1}function w(a,b,c,d){return(a!==b||!1===(0,r.resolveEnabled)(d.enabled,a))&&(!c.suspense||"error"!==a.state.status)&&x(a,c)}function x(a,b){return!1!==(0,r.resolveEnabled)(b.enabled,a)&&a.isStaleByTime((0,r.resolveStaleTime)(b.staleTime,a))}var y=a.i(70208),z=c.createContext(function(){let a=!1;return{clearReset:()=>{a=!1},reset:()=>{a=!0},isReset:()=>a}}()),A=c.createContext(!1);A.Provider;var B=(a,b,c)=>b.fetchOptimistic(a).catch(()=>{c.clearReset()});function C(a,b){return function(a,b,d){let e=c.useContext(A),f=c.useContext(z),g=(0,y.useQueryClient)(d),h=g.defaultQueryOptions(a);if(g.getDefaultOptions().queries?._experimental_beforeQuery?.(h),h._optimisticResults=e?"isRestoring":"optimistic",h.suspense){let a=a=>"static"===a?a:Math.max(a??1e3,1e3),b=h.staleTime;h.staleTime="function"==typeof b?(...c)=>a(b(...c)):a(b),"number"==typeof h.gcTime&&(h.gcTime=Math.max(h.gcTime,1e3))}(h.suspense||h.throwOnError||h.experimental_prefetchInRender)&&!f.isReset()&&(h.retryOnMount=!1),c.useEffect(()=>{f.clearReset()},[f]);let i=!g.getQueryCache().get(h.queryHash),[j]=c.useState(()=>new b(g,h)),k=j.getOptimisticResult(h),l=!e&&!1!==a.subscribed;if(c.useSyncExternalStore(c.useCallback(a=>{let b=l?j.subscribe(n.notifyManager.batchCalls(a)):r.noop;return j.updateResult(),b},[j,l]),()=>j.getCurrentResult(),()=>j.getCurrentResult()),c.useEffect(()=>{j.setOptions(h)},[h,j]),h?.suspense&&k.isPending)throw B(h,j,f);if((({result:a,errorResetBoundary:b,throwOnError:c,query:d,suspense:e})=>a.isError&&!b.isReset()&&!a.isFetching&&d&&(e&&void 0===a.data||(0,r.shouldThrowError)(c,[a.error,d])))({result:k,errorResetBoundary:f,throwOnError:h.throwOnError,query:g.getQueryCache().get(h.queryHash),suspense:h.suspense}))throw k.error;if(g.getDefaultOptions().queries?._experimental_afterQuery?.(h,k),h.experimental_prefetchInRender&&!r.isServer&&k.isLoading&&k.isFetching&&!e){let a=i?B(h,j,f):g.getQueryCache().get(h.queryHash)?.promise;a?.catch(r.noop).finally(()=>{j.updateResult()})}return h.notifyOnChangeProps?k:j.trackResult(k)}(a,t,b)}a.i(61289),p.Subscribable;var D=a.i(12594);let E={all:["sessions"],lists:()=>[...E.all,"list"],list:a=>[...E.lists(),{filters:a}],details:()=>[...E.all,"detail"],detail:a=>[...E.details(),a],members:a=>[...E.detail(a),"members"]},F={all:["caisses"],lists:()=>[...F.all,"list"],list:a=>[...F.lists(),{filters:a}],details:()=>[...F.all,"detail"],detail:a=>[...F.details(),a]};var G=a.i(78655);function H(){let{data:a}=(0,d.useSession)(),m=(0,c.useMemo)(()=>{let b=a?.user?.role;return{canViewSessions:["secretary_general","controller","cashier"].includes(b),canViewCaisses:["secretary_general","controller","cashier"].includes(b),canCreateSessions:"secretary_general"===b,canCreateCaisses:"secretary_general"===b}},[a?.user]),n=function(){let{data:a}=(0,d.useSession)(),b=(0,D.useApi)();return C({queryKey:E.lists(),queryFn:()=>b.getSessions(),enabled:!!a?.accessToken,staleTime:3e5})}(),o=function(){let{data:a}=(0,d.useSession)(),b=(0,D.useApi)();return C({queryKey:F.lists(),queryFn:()=>b.getCaisses(),enabled:!!a?.accessToken,staleTime:3e5})}(),p=m.canViewSessions&&n.data||[],q=m.canViewCaisses&&o.data||[],r=m.canViewSessions&&n.isLoading||m.canViewCaisses&&o.isLoading,s=new Date,t=p.filter(a=>new Date(a.dateDebut)<=s&&new Date(a.dateFin)>=s),u=q.reduce((a,b)=>a+b.soldeActuel,0),v=q.filter(a=>a.type===G.CaisseType.PRINCIPALE),w=q.filter(a=>a.type===G.CaisseType.REUNION);return r?(0,b.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"}),(0,b.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Chargement du tableau de bord..."})]})}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["Bienvenue, ",a?.user?.username,"!"]}),(0,b.jsx)("p",{className:"text-gray-600 mt-1",children:"Voici un aperçu de votre tontine aujourd'hui."}),a?.user&&(0,b.jsxs)("div",{className:"mt-2 text-sm text-gray-500",children:["Connecté en tant que"," ",(0,b.jsx)("span",{className:"font-medium",children:a.user.username})," (",a.user.role,")"]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[m.canViewSessions&&(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(f.CardTitle,{className:"text-sm font-medium",children:"Sessions Totales"}),(0,b.jsx)(i.Calendar,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsxs)(f.CardContent,{children:[(0,b.jsx)("div",{className:"text-2xl font-bold",children:p.length}),(0,b.jsxs)("p",{className:"text-xs text-muted-foreground",children:[t.length," actives"]})]})]}),m.canViewSessions&&(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(f.CardTitle,{className:"text-sm font-medium",children:"Sessions Actives"}),(0,b.jsx)(i.Calendar,{className:"h-4 w-4 text-green-600"})]}),(0,b.jsxs)(f.CardContent,{children:[(0,b.jsx)("div",{className:"text-2xl font-bold text-green-600",children:t.length}),(0,b.jsx)("p",{className:"text-xs text-muted-foreground",children:"En cours actuellement"})]})]}),m.canViewCaisses&&(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(f.CardTitle,{className:"text-sm font-medium",children:"Total Caisses"}),(0,b.jsx)(j.Wallet,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsxs)(f.CardContent,{children:[(0,b.jsx)("div",{className:"text-2xl font-bold",children:q.length}),(0,b.jsxs)("p",{className:"text-xs text-muted-foreground",children:[v.length," principales,"," ",w.length," réunions"]})]})]}),m.canViewCaisses&&(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(f.CardTitle,{className:"text-sm font-medium",children:"Solde Total"}),(0,b.jsx)(h.DollarSign,{className:"h-4 w-4 text-green-600"})]}),(0,b.jsxs)(f.CardContent,{children:[(0,b.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[u.toLocaleString()," FCFA"]}),(0,b.jsx)("p",{className:"text-xs text-muted-foreground",children:"Toutes caisses confondues"})]})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[m.canViewSessions&&(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{className:"flex flex-row items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(f.CardTitle,{children:"Sessions Récentes"}),(0,b.jsx)(f.CardDescription,{children:"Dernières sessions créées"})]}),m.canCreateSessions&&(0,b.jsx)(g.Button,{asChild:!0,size:"sm",children:(0,b.jsxs)(e.default,{href:"/dashboard/sessions/new",children:[(0,b.jsx)(k.Plus,{className:"mr-2 h-4 w-4"}),"Nouvelle"]})})]}),(0,b.jsx)(f.CardContent,{children:(0,b.jsxs)("div",{className:"space-y-4",children:[p.slice(0,4).map(a=>(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["Session ",a.annee]}),(0,b.jsxs)("p",{className:"text-xs text-gray-500",children:[new Date(a.dateDebut).toLocaleDateString()," -"," ",new Date(a.dateFin).toLocaleDateString()]})]}),(0,b.jsxs)("div",{className:"text-sm font-medium text-blue-600",children:[a.partFixe.toLocaleString()," FCFA"]})]},a._id)),0===p.length&&(0,b.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"Aucune session trouvée"}),p.length>0&&(0,b.jsx)("div",{className:"pt-2",children:(0,b.jsx)(g.Button,{variant:"outline",size:"sm",asChild:!0,className:"w-full",children:(0,b.jsxs)(e.default,{href:"/dashboard/sessions",children:["Voir toutes les sessions",(0,b.jsx)(l,{className:"ml-2 h-4 w-4"})]})})})]})})]}),m.canViewCaisses&&(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{className:"flex flex-row items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(f.CardTitle,{children:"Caisses Récentes"}),(0,b.jsx)(f.CardDescription,{children:"Dernières caisses créées"})]}),m.canCreateCaisses&&(0,b.jsx)(g.Button,{asChild:!0,size:"sm",children:(0,b.jsxs)(e.default,{href:"/dashboard/caisses/new",children:[(0,b.jsx)(k.Plus,{className:"mr-2 h-4 w-4"}),"Nouvelle"]})})]}),(0,b.jsx)(f.CardContent,{children:(0,b.jsxs)("div",{className:"space-y-4",children:[q.slice(0,4).map(a=>(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"text-sm font-medium text-gray-900",children:a.nom}),(0,b.jsx)("p",{className:"text-xs text-gray-500",children:a.type===G.CaisseType.PRINCIPALE?"Principale":"Réunion"})]}),(0,b.jsxs)("div",{className:`text-sm font-medium ${a.soldeActuel>0?"text-green-600":"text-gray-500"}`,children:[a.soldeActuel.toLocaleString()," FCFA"]})]},a._id)),0===q.length&&(0,b.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"Aucune caisse trouvée"}),q.length>0&&(0,b.jsx)("div",{className:"pt-2",children:(0,b.jsx)(g.Button,{variant:"outline",size:"sm",asChild:!0,className:"w-full",children:(0,b.jsxs)(e.default,{href:"/dashboard/caisses",children:["Voir toutes les caisses",(0,b.jsx)(l,{className:"ml-2 h-4 w-4"})]})})})]})})]})]})]})}}];

//# sourceMappingURL=frontend_src_app_dashboard_page_tsx_51f7f273._.js.map