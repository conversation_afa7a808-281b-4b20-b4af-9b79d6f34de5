import { Body, Controller, Get, Param, Patch, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQ<PERSON>y, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../common/guards/role.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../common/enums/user-role.enum';
import { ReunionsService } from './reunions.service';
import { UpdateReunionDto } from './dto/update-reunion.dto';
import { PaymentFiltersDto } from '../common/dto/payment-filters.dto';

@ApiTags('reunions')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('reunions')
export class ReunionsController {
  constructor(private readonly reunionsService: ReunionsService) {}

  @Get()
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER, UserRole.CASHIER)
  @ApiOperation({ summary: 'Lister les réunions' })
  findAll() {
    return this.reunionsService.findAll();
  }

  @Get(':id')
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER, UserRole.CASHIER)
  @ApiOperation({ summary: 'Détail d\'une réunion' })
  @ApiParam({ name: 'id', description: 'ID de la réunion' })
  findOne(@Param('id') id: string) {
    return this.reunionsService.findOne(id);
  }

  // expected payments for a meeting
  @Get(':id/expected-payments')
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER, UserRole.CASHIER)
  @ApiOperation({ summary: 'Paiements attendus pour une réunion', description: 'Pour chaque membre inscrit à la session, renvoie la cotisation par réunion et l\'état actuel des soldes.' })
  expected(@Param('id') id: string) {
    return this.reunionsService.expectedPayments(id);
  }

  // debrief financier d'une réunion (filtrable sur paiement)
  @Get(':id/debrief')
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER)
  @ApiOperation({ summary: 'Debrief financier d\'une réunion', description: 'Totaux IN/OUT et agrégations, filtrables (membre, période, direction, fonction).' })
  debrief(@Param('id') id: string, @Query() filters: PaymentFiltersDto) {
    return this.reunionsService.debrief(id, filters);
  }

  // état de la réunion (membres attendus, somme attendue, reçue, écart)
  @Get(':id/state')
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER, UserRole.CASHIER)
  @ApiOperation({ summary: 'État de la réunion', description: 'Membres attendus, somme attendue, reçue et écart.' })
  state(@Param('id') id: string) {
    return this.reunionsService.debrief(id, {} as any); // réutilise debrief pour retourner state inclus
  }

  @Patch(':id')
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Mettre à jour une réunion', description: 'Met à jour la date, le lieu, la caisse principale associée. Met à jour automatiquement la prochaine réunion de la session.' })
  update(@Param('id') id: string, @Body() dto: UpdateReunionDto) {
    return this.reunionsService.update(id, dto);
  }
}