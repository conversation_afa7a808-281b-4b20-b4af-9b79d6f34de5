import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { useApi } from '@/hooks/use-api';
import type { Payment, CreatePaymentDto, PaymentFilters } from '@/types';
import { memberKeys } from './use-members';
import { caisseKeys } from './use-caisses';

// Query Keys
export const paymentKeys = {
  all: ['payments'] as const,
  lists: () => [...paymentKeys.all, 'list'] as const,
  list: (filters: PaymentFilters) => [...paymentKeys.lists(), { filters }] as const,
  details: () => [...paymentKeys.all, 'detail'] as const,
  detail: (id: string) => [...paymentKeys.details(), id] as const,
};

// Hooks
export function usePayments(filters?: PaymentFilters) {
  const { data: session } = useSession();
  const api = useApi();

  return useQuery({
    queryKey: paymentKeys.list(filters || {}),
    queryFn: () => api.getPayments(filters),
    enabled: !!session?.accessToken,
    staleTime: 2 * 60 * 1000, // 2 minutes - payments change more frequently
  });
}

export function usePayment(paymentId: string) {
  const { data: session } = useSession();
  const api = useApi();

  return useQuery({
    queryKey: paymentKeys.detail(paymentId),
    queryFn: () => api.getPayment(paymentId),
    enabled: !!session?.accessToken && !!paymentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Mutations
export function useCreatePayment() {
  const queryClient = useQueryClient();
  const api = useApi();

  return useMutation({
    mutationFn: (data: CreatePaymentDto) => api.createPayment(data),
    onSuccess: (newPayment) => {
      // Invalidate payments lists
      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });
      
      // Invalidate related member debrief if memberId is available
      if (newPayment.memberId) {
        queryClient.invalidateQueries({ 
          queryKey: memberKeys.debrief(newPayment.memberId) 
        });
      }
      
      // Invalidate related caisse if caisseId is available
      if (newPayment.caisseId) {
        queryClient.invalidateQueries({ 
          queryKey: caisseKeys.detail(newPayment.caisseId) 
        });
        queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });
      }
    },
    onError: (error) => {
      console.error('Error creating payment:', error);
    },
  });
}

export function useUpdatePayment() {
  const queryClient = useQueryClient();
  const api = useApi();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreatePaymentDto> }) => 
      api.updatePayment(id, data),
    onSuccess: (updatedPayment, { id }) => {
      // Invalidate specific payment and payments lists
      queryClient.invalidateQueries({ queryKey: paymentKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });
      
      // Invalidate related member debrief
      if (updatedPayment.memberId) {
        queryClient.invalidateQueries({ 
          queryKey: memberKeys.debrief(updatedPayment.memberId) 
        });
      }
      
      // Invalidate related caisse
      if (updatedPayment.caisseId) {
        queryClient.invalidateQueries({ 
          queryKey: caisseKeys.detail(updatedPayment.caisseId) 
        });
        queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });
      }
    },
    onError: (error) => {
      console.error('Error updating payment:', error);
    },
  });
}

export function useDeletePayment() {
  const queryClient = useQueryClient();
  const api = useApi();

  return useMutation({
    mutationFn: (paymentId: string) => api.deletePayment(paymentId),
    onSuccess: (_, paymentId) => {
      // Remove from cache and invalidate lists
      queryClient.removeQueries({ queryKey: paymentKeys.detail(paymentId) });
      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });
      
      // Invalidate all member debriefs and caisses since we don't know which ones were affected
      queryClient.invalidateQueries({ queryKey: memberKeys.all });
      queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });
    },
    onError: (error) => {
      console.error('Error deleting payment:', error);
    },
  });
}
