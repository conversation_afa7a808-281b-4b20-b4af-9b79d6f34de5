import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsNumber, IsPositive } from 'class-validator';

export class MemberContributionDto {
  @ApiProperty({ description: 'Membre qui cotise' })
  @IsMongoId()
  memberId!: string;

  @ApiProperty({ description: 'Session concernée' })
  @IsMongoId()
  sessionId!: string;

  @ApiProperty({ description: 'Réunion point de départ pour l\'allocation' })
  @IsMongoId()
  reunionId!: string;

  @ApiProperty({ example: 1000, description: 'Montant versé (peut couvrir plusieurs réunions)' })
  @IsNumber()
  @IsPositive()
  amount!: number;
}