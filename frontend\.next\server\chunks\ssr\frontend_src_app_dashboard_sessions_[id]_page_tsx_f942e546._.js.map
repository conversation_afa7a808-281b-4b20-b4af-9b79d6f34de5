{"version": 3, "sources": ["turbopack:///[project]/frontend/src/app/dashboard/sessions/[id]/page.tsx", "turbopack:///[project]/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { usePara<PERSON>, useRouter } from \"next/navigation\";\nimport { useSession } from \"next-auth/react\";\nimport { ArrowLeft, Users, Plus, Trash2, DollarSign } from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { Button } from \"@/components/ui/button\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport {\n\tTable,\n\tTableBody,\n\tTableCell,\n\tTableHead,\n\tTableHeader,\n\tTableRow,\n} from \"@/components/ui/table\";\nimport {\n\tDialog,\n\tDialogContent,\n\tDialogDescription,\n\tDialogHeader,\n\tDialogTitle,\n\tDialogTrigger,\n} from \"@/components/ui/dialog\";\nimport {\n\tSelect,\n\tSelectContent,\n\tSelectItem,\n\tSelectTrigger,\n\tSelectValue,\n} from \"@/components/ui/select\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { useApi } from \"@/hooks/use-api\";\nimport { Session, SessionMember, Member, UserRole } from \"@/types\";\n\nexport default function SessionDetailPage() {\n\tconst params = useParams();\n\tconst router = useRouter();\n\tconst { data: session } = useSession();\n\tconst api = useApi();\n\n\tconst [sessionData, setSessionData] = useState<Session | null>(null);\n\tconst [sessionMembers, setSessionMembers] = useState<SessionMember[]>([]);\n\tconst [availableMembers, setAvailableMembers] = useState<Member[]>([]);\n\tconst [loading, setLoading] = useState(true);\n\tconst [error, setError] = useState<string | null>(null);\n\tconst [isAddDialogOpen, setIsAddDialogOpen] = useState(false);\n\tconst [selectedMemberId, setSelectedMemberId] = useState(\"\");\n\tconst [memberParts, setMemberParts] = useState(1);\n\n\tconst sessionId = params.id as string;\n\n\t// Vérifier les permissions\n\tconst canManageSession =\n\t\tsession?.user &&\n\t\t((session.user as any).role === UserRole.SECRETARY_GENERAL ||\n\t\t\t(session.user as any).role === UserRole.CONTROLLER);\n\n\tuseEffect(() => {\n\t\tif (session?.accessToken && sessionId) {\n\t\t\tloadData();\n\t\t}\n\t}, [session, sessionId]);\n\n\tconst loadData = async () => {\n\t\ttry {\n\t\t\tsetLoading(true);\n\t\t\tsetError(null);\n\n\t\t\tconst [sessionData, sessionMembersData, allMembers] = await Promise.all([\n\t\t\t\tapi.getSession(sessionId),\n\t\t\t\tapi.getSessionMembers(sessionId),\n\t\t\t\tapi.getMembers(),\n\t\t\t]);\n\n\t\t\tsetSessionData(sessionData);\n\t\t\tsetSessionMembers(sessionMembersData);\n\t\t\t\n\t\t\t// Filtrer les membres disponibles (non encore inscrits)\n\t\t\tconst enrolledMemberIds = sessionMembersData.map(sm => sm.memberId);\n\t\t\tconst available = allMembers.filter(m => !enrolledMemberIds.includes(m._id));\n\t\t\tsetAvailableMembers(available);\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors du chargement:\", error);\n\t\t\tsetError(\"Erreur lors du chargement des données\");\n\t\t} finally {\n\t\t\tsetLoading(false);\n\t\t}\n\t};\n\n\tconst handleAddMember = async () => {\n\t\tif (!selectedMemberId || !sessionData) return;\n\n\t\ttry {\n\t\t\tconst totalDue = sessionData.partFixe * memberParts;\n\t\t\t\n\t\t\tawait api.addSessionMember({\n\t\t\t\tsessionId: sessionId,\n\t\t\t\tmemberId: selectedMemberId,\n\t\t\t\tparts: memberParts,\n\t\t\t});\n\n\t\t\t// Recharger les données\n\t\t\tawait loadData();\n\t\t\t\n\t\t\t// Réinitialiser le formulaire\n\t\t\tsetSelectedMemberId(\"\");\n\t\t\tsetMemberParts(1);\n\t\t\tsetIsAddDialogOpen(false);\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors de l'ajout du membre:\", error);\n\t\t}\n\t};\n\n\tconst handleRemoveMember = async (memberId: string) => {\n\t\tif (!confirm(\"Êtes-vous sûr de vouloir retirer ce membre de la session ?\")) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tawait api.removeSessionMember(sessionId, memberId);\n\t\t\tawait loadData();\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors de la suppression:\", error);\n\t\t}\n\t};\n\n\tconst formatCurrency = (amount: number) => {\n\t\treturn new Intl.NumberFormat('fr-FR', {\n\t\t\tstyle: 'currency',\n\t\t\tcurrency: 'XAF',\n\t\t}).format(amount);\n\t};\n\n\tconst formatDate = (dateString: string) => {\n\t\treturn new Date(dateString).toLocaleDateString('fr-FR', {\n\t\t\tyear: 'numeric',\n\t\t\tmonth: 'long',\n\t\t\tday: 'numeric',\n\t\t});\n\t};\n\n\tif (!canManageSession) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Link href=\"/dashboard/sessions\">\n\t\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tRetour\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\t\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<h2 className=\"text-lg font-semibold text-gray-900\">Accès refusé</h2>\n\t\t\t\t\t\t<p className=\"text-gray-600\">\n\t\t\t\t\t\t\tVous n'avez pas les permissions pour accéder à cette page.\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tif (loading) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Link href=\"/dashboard/sessions\">\n\t\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tRetour\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\t\t\t\t<div className=\"flex justify-center py-8\">\n\t\t\t\t\t<div className=\"text-gray-500\">Chargement...</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tif (error || !sessionData) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Link href=\"/dashboard/sessions\">\n\t\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tRetour\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\t\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<h2 className=\"text-lg font-semibold text-gray-900\">Erreur</h2>\n\t\t\t\t\t\t<p className=\"text-gray-600\">{error || \"Session introuvable\"}</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tconst totalMembers = sessionMembers.length;\n\tconst totalParts = sessionMembers.reduce((sum, sm) => sum + sm.parts, 0);\n\tconst totalDue = sessionMembers.reduce((sum, sm) => sum + sm.totalDue, 0);\n\tconst totalPaid = sessionMembers.reduce((sum, sm) => sum + sm.paidSoFar, 0);\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* En-tête */}\n\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Link href=\"/dashboard/sessions\">\n\t\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tRetour\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<h1 className=\"text-2xl font-bold text-gray-900\">\n\t\t\t\t\t\t\tSession {sessionData.annee}\n\t\t\t\t\t\t</h1>\n\t\t\t\t\t\t<p className=\"text-gray-600\">\n\t\t\t\t\t\t\t{formatDate(sessionData.dateDebut)} - {formatDate(sessionData.dateFin)}\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<Link href={`/dashboard/sessions/${sessionData._id}/edit`}>\n\t\t\t\t\t<Button>Modifier</Button>\n\t\t\t\t</Link>\n\t\t\t</div>\n\n\t\t\t{/* Informations de la session */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle>Informations de la session</CardTitle>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<h3 className=\"font-medium text-gray-900 mb-2\">Année</h3>\n\t\t\t\t\t\t\t<p className=\"text-gray-600\">{sessionData.annee}</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<h3 className=\"font-medium text-gray-900 mb-2\">Part fixe</h3>\n\t\t\t\t\t\t\t<p className=\"text-gray-600\">{formatCurrency(sessionData.partFixe)}</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<h3 className=\"font-medium text-gray-900 mb-2\">Date de début</h3>\n\t\t\t\t\t\t\t<p className=\"text-gray-600\">{formatDate(sessionData.dateDebut)}</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<h3 className=\"font-medium text-gray-900 mb-2\">Date de fin</h3>\n\t\t\t\t\t\t\t<p className=\"text-gray-600\">{formatDate(sessionData.dateFin)}</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\n\t\t\t{/* Statistiques des membres */}\n\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n\t\t\t\t<Card>\n\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\tMembres inscrits\n\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t</CardHeader>\n\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t<div className=\"text-2xl font-bold\">{totalMembers}</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t\t<Card>\n\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\tTotal parts\n\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t</CardHeader>\n\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t<div className=\"text-2xl font-bold text-blue-600\">{totalParts}</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t\t<Card>\n\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\tMontant dû\n\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t</CardHeader>\n\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t<div className=\"text-2xl font-bold text-orange-600\">\n\t\t\t\t\t\t\t{formatCurrency(totalDue)}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t\t<Card>\n\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\tMontant payé\n\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t</CardHeader>\n\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t<div className=\"text-2xl font-bold text-green-600\">\n\t\t\t\t\t\t\t{formatCurrency(totalPaid)}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t</div>\n\n\t\t\t{/* Liste des membres */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<CardTitle>Membres de la session</CardTitle>\n\t\t\t\t\t\t\t<CardDescription>\n\t\t\t\t\t\t\t\t{totalMembers} membre(s) inscrit(s)\n\t\t\t\t\t\t\t</CardDescription>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>\n\t\t\t\t\t\t\t<DialogTrigger asChild>\n\t\t\t\t\t\t\t\t<Button>\n\t\t\t\t\t\t\t\t\t<Plus className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\t\t\tAjouter un membre\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t</DialogTrigger>\n\t\t\t\t\t\t\t<DialogContent>\n\t\t\t\t\t\t\t\t<DialogHeader>\n\t\t\t\t\t\t\t\t\t<DialogTitle>Ajouter un membre à la session</DialogTitle>\n\t\t\t\t\t\t\t\t\t<DialogDescription>\n\t\t\t\t\t\t\t\t\t\tSélectionnez un membre et définissez le nombre de parts\n\t\t\t\t\t\t\t\t\t</DialogDescription>\n\t\t\t\t\t\t\t\t</DialogHeader>\n\t\t\t\t\t\t\t\t<div className=\"space-y-4\">\n\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t<Label htmlFor=\"member\">Membre</Label>\n\t\t\t\t\t\t\t\t\t\t<Select value={selectedMemberId} onValueChange={setSelectedMemberId}>\n\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Sélectionner un membre\" />\n\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t{availableMembers.map((member) => (\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem key={member._id} value={member._id}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{member.firstName} {member.lastName}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t<Label htmlFor=\"parts\">Nombre de parts</Label>\n\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\tid=\"parts\"\n\t\t\t\t\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t\t\t\t\t\tmin=\"1\"\n\t\t\t\t\t\t\t\t\t\t\tvalue={memberParts}\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => setMemberParts(Number(e.target.value))}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t{sessionData && memberParts > 0 && (\n\t\t\t\t\t\t\t\t\t\t<div className=\"text-sm text-gray-600\">\n\t\t\t\t\t\t\t\t\t\t\tMontant total dû: {formatCurrency(sessionData.partFixe * memberParts)}\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t<div className=\"flex justify-end gap-2\">\n\t\t\t\t\t\t\t\t\t\t<Button variant=\"outline\" onClick={() => setIsAddDialogOpen(false)}>\n\t\t\t\t\t\t\t\t\t\t\tAnnuler\n\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t\t<Button onClick={handleAddMember} disabled={!selectedMemberId}>\n\t\t\t\t\t\t\t\t\t\t\tAjouter\n\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</DialogContent>\n\t\t\t\t\t\t</Dialog>\n\t\t\t\t\t</div>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t{sessionMembers.length > 0 ? (\n\t\t\t\t\t\t<Table>\n\t\t\t\t\t\t\t<TableHeader>\n\t\t\t\t\t\t\t\t<TableRow>\n\t\t\t\t\t\t\t\t\t<TableHead>Membre</TableHead>\n\t\t\t\t\t\t\t\t\t<TableHead>Parts</TableHead>\n\t\t\t\t\t\t\t\t\t<TableHead>Montant dû</TableHead>\n\t\t\t\t\t\t\t\t\t<TableHead>Payé</TableHead>\n\t\t\t\t\t\t\t\t\t<TableHead>Reste</TableHead>\n\t\t\t\t\t\t\t\t\t<TableHead>Actions</TableHead>\n\t\t\t\t\t\t\t\t</TableRow>\n\t\t\t\t\t\t\t</TableHeader>\n\t\t\t\t\t\t\t<TableBody>\n\t\t\t\t\t\t\t\t{sessionMembers.map((sessionMember) => {\n\t\t\t\t\t\t\t\t\tconst remainingAmount = sessionMember.totalDue - sessionMember.paidSoFar;\n\t\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\t\t<TableRow key={sessionMember._id}>\n\t\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"font-medium\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{/* Note: Dans une vraie implémentation, il faudrait joindre les données du membre */}\n\t\t\t\t\t\t\t\t\t\t\t\t\tMembre ID: {sessionMember.memberId}\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t\t<TableCell>{sessionMember.parts}</TableCell>\n\t\t\t\t\t\t\t\t\t\t\t<TableCell>{formatCurrency(sessionMember.totalDue)}</TableCell>\n\t\t\t\t\t\t\t\t\t\t\t<TableCell className=\"text-green-600\">\n\t\t\t\t\t\t\t\t\t\t\t\t{formatCurrency(sessionMember.paidSoFar)}\n\t\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t\t<TableCell className={remainingAmount > 0 ? \"text-red-600\" : \"text-green-600\"}>\n\t\t\t\t\t\t\t\t\t\t\t\t{formatCurrency(remainingAmount)}\n\t\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"ghost\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"sm\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleRemoveMember(sessionMember.memberId)}\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"text-red-600 hover:text-red-700\"\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Trash2 className=\"h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t</TableRow>\n\t\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t\t})}\n\t\t\t\t\t\t\t</TableBody>\n\t\t\t\t\t\t</Table>\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<div className=\"text-center py-8 text-gray-500\">\n\t\t\t\t\t\t\tAucun membre inscrit à cette session\n\t\t\t\t\t\t</div>\n\t\t\t\t\t)}\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\t\t</div>\n\t);\n}\n", "\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": "mFAEA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OAOA,EAAA,EAAA,CAAA,CAAA,OCbA,EAAA,EAAA,CAAA,CAAA,wBAGA,EAAA,EAAA,CAAA,CAAA,OAEA,IAAM,EAAS,EAAA,IAAoB,CAE7B,EAAgB,EAAA,OAAuB,CAEvC,EAAe,EAAA,MAAsB,CAEvB,EAAA,KAAqB,CAEzC,IAAM,EAAgB,EAAA,UAAgB,CAGpC,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAuB,CAAA,CACtB,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,0JACA,GAED,GAAG,CAAK,IAGb,EAAc,WAAW,CAAG,EAAA,OAAuB,CAAC,WAAW,CAE/D,IAAM,EAAgB,EAAA,UAAgB,CAGpC,CAAC,CAAE,WAAS,UAAE,CAAQ,CAAE,GAAG,EAAO,CAAE,IACpC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,GACD,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,OAAuB,CAAA,CACtB,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,8fACA,GAED,GAAG,CAAK,WAER,EACD,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,KAAqB,CAAA,CAAC,UAAU,0RAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAC,CAAA,CAAC,UAAU,YACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,mBAAU,oBAKlC,EAAc,WAAW,CAAG,EAAA,OAAuB,CAAC,WAAW,CAE/D,IAAM,EAAe,CAAC,WACpB,CAAS,CACT,GAAG,EACkC,GACrC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,qDACA,GAED,GAAG,CAAK,GAGb,EAAa,WAAW,CAAG,eAgB3B,IAAM,EAAc,EAAA,UAAgB,CAGlC,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAqB,CAAA,CACpB,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,oDACA,GAED,GAAG,CAAK,IAGb,EAAY,WAAW,CAAG,EAAA,KAAqB,CAAC,WAAW,CAE3D,IAAM,EAAoB,EAAA,UAAgB,CAGxC,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAA2B,CAAA,CAC1B,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,gCAAiC,GAC9C,GAAG,CAAK,IAGb,EAAkB,WAAW,CAAG,EAAA,WAA2B,CAAC,WAAW,CD5EvE,IAAA,EAAA,EAAA,CAAA,CAAA,OAOA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACvB,IAAM,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IACT,CAAA,EAAA,EAAA,SAAA,AAAS,IACxB,GAAM,CAAE,KAAM,CAAO,CAAE,CAAG,CAAA,EAAA,EAAA,UAAA,AAAU,IAC9B,EAAM,CAAA,EAAA,EAAA,MAAA,AAAM,IAEZ,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAiB,MACzD,CAAC,EAAgB,EAAkB,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAA0B,EAAE,EAClE,CAAC,EAAkB,EAAoB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAW,EAAE,EAC/D,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAwB,MAC5C,CAAC,EAAiB,EAAmB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjD,CAAC,EAAkB,EAAoB,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAS,IACnD,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,GAEzC,EAAY,EAAO,EAAE,CAGrB,EACL,GAAS,OACP,CAAF,CAAU,IAAI,CAAS,IAAI,GAAK,EAAA,QAAQ,CAAC,iBAAiB,EACxD,EAAQ,IAAI,CAAS,IAAI,GAAK,EAAA,QAAQ,CAAC,UAAA,AAAU,EAEpD,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACL,GAAS,aAAe,GAC3B,GAEF,EAAG,CAAC,EAHoC,AAG3B,EAAU,EAEvB,IAAM,EAAW,UAChB,GAAI,CACH,GAAW,GACX,EAAS,MAET,GAAM,CAAC,EAAa,EAAoB,EAAW,CAAG,MAAM,QAAQ,GAAG,CAAC,CACvE,EAAI,UAAU,CAAC,GACf,EAAI,iBAAiB,CAAC,GACtB,EAAI,UAAU,GACd,EAED,EAAe,GACf,EAAkB,GAGlB,IAAM,EAAoB,EAAmB,GAAG,CAAC,GAAM,EAAG,QAAQ,EAC5D,EAAY,EAAW,MAAM,CAAC,GAAK,CAAC,EAAkB,QAAQ,CAAC,EAAE,GAAG,GAC1E,EAAoB,EACrB,CAAE,MAAO,EAAO,CACf,QAAQ,KAAK,CAAC,6BAA8B,GAC5C,EAAS,wCACV,QAAU,CACT,EAAW,GACZ,CACD,EAEM,EAAkB,UACvB,GAAI,AAAC,GAAqB,EAE1B,GAAI,CACc,EAAY,KAHS,GAGD,CAErC,AALwB,EAGgB,IAElC,EAAI,gBAAgB,CAAC,CAC1B,UAAW,EACX,SAAU,EACV,MAAO,CACR,GAGA,MAAM,IAGN,EAAoB,IACpB,EAAe,GACf,GAAmB,EACpB,CAAE,MAAO,EAAO,CACf,QAAQ,KAAK,CAAC,oCAAqC,EACpD,CACD,EAEM,EAAqB,MAAO,IACjC,GAAK,CAAD,OAAS,8DAIb,CAJ4E,EAIxE,CACH,MAAM,EAAI,mBAAmB,CAAC,EAAW,GACzC,MAAM,GACP,CAAE,MAAO,EAAO,CACf,QAAQ,KAAK,CAAC,iCAAkC,EACjD,CACD,EAEM,EAAkB,AAAD,GACf,IAAI,KAAK,YAAY,CAAC,QAAS,CACrC,MAAO,WACP,SAAU,KACX,GAAG,MAAM,CAAC,GAGL,EAAa,AAAC,GACZ,IAAI,KAAK,GAAY,kBAAkB,CAAC,QAAS,CACvD,KAAM,UACN,MAAO,OACP,IAAK,SACN,GAGD,GAAI,CAAC,EACJ,MACC,CAAA,EAAA,EAAA,IAAA,CAFqB,CAEpB,MAAA,CAAI,UAAU,sBACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,+BACV,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,QAAQ,KAAK,eAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,iBAAiB,gBAKzC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACd,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,+CAAsC,iBACpD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,uEASlC,GAAI,EACH,MACC,CAFW,AAEX,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,+BACV,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,QAAQ,KAAK,eAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,iBAAiB,gBAKzC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oCACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,uBAMnC,GAAI,GAAS,CAAC,EACb,MACC,CAAA,EAAA,EAFyB,AAEzB,IAAA,EAAC,MAAA,CAAI,UAAU,sBACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,+BACV,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,QAAQ,KAAK,eAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,iBAAiB,gBAKzC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACd,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,+CAAsC,WACpD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAiB,GAAS,gCAO5C,IAAM,EAAe,EAAe,MAAM,CACpC,EAAa,EAAe,MAAM,CAAC,CAAC,EAAK,IAAO,EAAM,EAAG,KAAK,CAAE,GAChE,EAAW,EAAe,MAAM,CAAC,CAAC,EAAK,IAAO,EAAM,EAAG,QAAQ,CAAE,GACjE,EAAY,EAAe,MAAM,CAAC,CAAC,EAAK,IAAO,EAAM,EAAG,SAAS,CAAE,GAEzE,MACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBAEd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,+BACV,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,QAAQ,KAAK,eAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,iBAAiB,cAIxC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,6CAAmC,WACvC,EAAY,KAAK,IAE3B,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,0BACX,EAAW,EAAY,SAAS,EAAE,MAAI,EAAW,EAAY,OAAO,WAIxE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAM,CAAC,oBAAoB,EAAE,EAAY,GAAG,CAAC,KAAK,CAAC,UACxD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,UAAC,kBAKV,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,UACV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,iCAEZ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0CAAiC,UAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAiB,EAAY,KAAK,MAEhD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0CAAiC,cAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAiB,EAAe,EAAY,QAAQ,OAElE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0CAAiC,kBAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAiB,EAAW,EAAY,SAAS,OAE/D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0CAAiC,gBAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAiB,EAAW,EAAY,OAAO,eAOhE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACd,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,gBACrB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,6CAAoC,uBAI1D,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BAAsB,SAGvC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,gBACrB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,6CAAoC,kBAI1D,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CAAoC,SAGrD,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,gBACrB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,6CAAoC,iBAI1D,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8CACb,EAAe,UAInB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,gBACrB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,6CAAoC,mBAI1D,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CACb,EAAe,aAOpB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,UACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,0BACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,eAAe,CAAA,WACd,EAAa,8BAGhB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,CAAO,KAAM,EAAiB,aAAc,YAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAc,OAAO,CAAA,CAAA,WACrB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,WACN,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,iBAAiB,yBAInC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,WACA,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAY,mCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAkB,+DAIpB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,kBAAS,WACxB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,MAAO,EAAkB,cAAe,YAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,UACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,YAAY,6BAE1B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,UACZ,EAAiB,GAAG,CAAE,AAAD,GACrB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAkB,MAAO,EAAO,GAAG,WAC5C,EAAO,SAAS,CAAC,IAAE,EAAO,QAAQ,GADnB,EAAO,GAAG,WAO/B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAQ,iBAAQ,oBACvB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACL,GAAG,QACH,KAAK,SACL,IAAI,IACJ,MAAO,EACP,SAAU,AAAC,GAAM,EAAe,OAAO,EAAE,MAAM,CAAC,KAAK,QAGtD,GAAe,EAAc,GAC7B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kCAAwB,qBACnB,EAAe,EAAY,QAAQ,CAAG,MAG3D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,QAAS,IAAM,GAAmB,YAAQ,YAGpE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAS,EAAiB,SAAU,CAAC,WAAkB,6BASrE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,EAAe,MAAM,CAAG,EACxB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,KAAK,CAAA,WACL,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,WACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,eACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,SACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,iBAGb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UACR,EAAe,GAAG,CAAC,AAAC,IACpB,IAAM,EAAkB,EAAc,QAAQ,CAAG,EAAc,SAAS,CACxE,MACC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UACT,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACwE,cAC1E,EAAc,QAAQ,MAGpC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAE,EAAc,KAAK,GAC/B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAE,EAAe,EAAc,QAAQ,IACjD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,0BACnB,EAAe,EAAc,SAAS,IAExC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAW,EAAkB,EAAI,eAAiB,0BAC3D,EAAe,KAEjB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UACT,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CACN,QAAQ,QACR,KAAK,KACL,QAAS,IAAM,EAAmB,EAAc,QAAQ,EACxD,UAAU,2CAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,kBAtBN,EAAc,GAAG,CA2BlC,QAIF,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0CAAiC,gDAQtD"}