(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,42740,(e,t,r)=>{"use strict";function n(e,t,r){if(e)for(let o of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=o.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===o.defaultLocale.toLowerCase()||(null==(i=o.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return o}}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"detectDomainLocale",{enumerable:!0,get:function(){return n}})},88368,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"addPathSuffix",{enumerable:!0,get:function(){return i}});let n=e.r(98954);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:o}=(0,n.parsePath)(e);return""+r+t+i+o}},92863,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"addLocale",{enumerable:!0,get:function(){return o}});let n=e.r(45707),i=e.r(16297);function o(e,t,r,o){if(!t||t===r)return e;let a=e.toLowerCase();return!o&&((0,i.pathHasPrefix)(a,"/api")||(0,i.pathHasPrefix)(a,"/"+t.toLowerCase()))?e:(0,n.addPathPrefix)(e,"/"+t)}},33631,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"formatNextPathnameInfo",{enumerable:!0,get:function(){return s}});let n=e.r(37040),i=e.r(45707),o=e.r(88368),a=e.r(92863);function s(e){let t=(0,a.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,n.removeTrailingSlash)(t)),e.buildId&&(t=(0,o.addPathSuffix)((0,i.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,i.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,o.addPathSuffix)(t,"/"):(0,n.removeTrailingSlash)(t)}},27006,(e,t,r)=>{"use strict";function n(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getHostname",{enumerable:!0,get:function(){return n}})},16082,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"normalizeLocalePath",{enumerable:!0,get:function(){return i}});let n=new WeakMap;function i(e,t){let r;if(!t)return{pathname:e};let i=n.get(t);i||(i=t.map(e=>e.toLowerCase()),n.set(t,i));let o=e.split("/",2);if(!o[1])return{pathname:e};let a=o[1].toLowerCase(),s=i.indexOf(a);return s<0?{pathname:e}:(r=t[s],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}},49865,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"removePathPrefix",{enumerable:!0,get:function(){return i}});let n=e.r(16297);function i(e,t){if(!(0,n.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},83643,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getNextPathnameInfo",{enumerable:!0,get:function(){return a}});let n=e.r(16082),i=e.r(49865),o=e.r(16297);function a(e,t){var r,a;let{basePath:s,i18n:c,trailingSlash:l}=null!=(r=t.nextConfig)?r:{},u={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):l};s&&(0,o.pathHasPrefix)(u.pathname,s)&&(u.pathname=(0,i.removePathPrefix)(u.pathname,s),u.basePath=s);let d=u.pathname;if(u.pathname.startsWith("/_next/data/")&&u.pathname.endsWith(".json")){let e=u.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");u.buildId=e[0],d="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(u.pathname=d)}if(c){let e=t.i18nProvider?t.i18nProvider.analyze(u.pathname):(0,n.normalizeLocalePath)(u.pathname,c.locales);u.locale=e.detectedLocale,u.pathname=null!=(a=e.pathname)?a:u.pathname,!e.detectedLocale&&u.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(d):(0,n.normalizeLocalePath)(d,c.locales)).detectedLocale&&(u.locale=e.detectedLocale)}return u}},15469,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"NextURL",{enumerable:!0,get:function(){return u}});let n=e.r(42740),i=e.r(33631),o=e.r(27006),a=e.r(83643),s=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function c(e,t){return new URL(String(e).replace(s,"localhost"),t&&String(t).replace(s,"localhost"))}let l=Symbol("NextURLInternal");class u{analyze(){var e,t,r,i,s,c,u;let d=(0,a.getNextPathnameInfo)(this[l].url.pathname,{nextConfig:this[l].options.nextConfig,parseData:!0,i18nProvider:this[l].options.i18nProvider}),p=(0,o.getHostname)(this[l].url,this[l].options.headers);this[l].domainLocale=this[l].options.i18nProvider?this[l].options.i18nProvider.detectDomainLocale(p):(0,n.detectDomainLocale)(null==(t=this[l].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,p);let h=(null==(r=this[l].domainLocale)?void 0:r.defaultLocale)||(null==(s=this[l].options.nextConfig)||null==(i=s.i18n)?void 0:i.defaultLocale);this[l].url.pathname=d.pathname,this[l].defaultLocale=h,this[l].basePath=null!=(c=d.basePath)?c:"",this[l].buildId=d.buildId,this[l].locale=null!=(u=d.locale)?u:h,this[l].trailingSlash=d.trailingSlash}formatPathname(){return(0,i.formatNextPathnameInfo)({basePath:this[l].basePath,buildId:this[l].buildId,defaultLocale:this[l].options.forceLocale?void 0:this[l].defaultLocale,locale:this[l].locale,pathname:this[l].url.pathname,trailingSlash:this[l].trailingSlash})}formatSearch(){return this[l].url.search}get buildId(){return this[l].buildId}set buildId(e){this[l].buildId=e}get locale(){var e;return null!=(e=this[l].locale)?e:""}set locale(e){var t,r;if(!this[l].locale||!(null==(r=this[l].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError('The NextURL configuration includes no locale "'.concat(e,'"')),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[l].locale=e}get defaultLocale(){return this[l].defaultLocale}get domainLocale(){return this[l].domainLocale}get searchParams(){return this[l].url.searchParams}get host(){return this[l].url.host}set host(e){this[l].url.host=e}get hostname(){return this[l].url.hostname}set hostname(e){this[l].url.hostname=e}get port(){return this[l].url.port}set port(e){this[l].url.port=e}get protocol(){return this[l].url.protocol}set protocol(e){this[l].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return"".concat(this.protocol,"//").concat(this.host).concat(e).concat(t).concat(this.hash)}set href(e){this[l].url=c(e),this.analyze()}get origin(){return this[l].url.origin}get pathname(){return this[l].url.pathname}set pathname(e){this[l].url.pathname=e}get hash(){return this[l].url.hash}set hash(e){this[l].url.hash=e}get search(){return this[l].url.search}set search(e){this[l].url.search=e}get password(){return this[l].url.password}set password(e){this[l].url.password=e}get username(){return this[l].url.username}set username(e){this[l].url.username=e}get basePath(){return this[l].basePath}set basePath(e){this[l].basePath=e.startsWith("/")?e:"/".concat(e)}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new u(String(this),this[l].options)}constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[l]={url:c(e,null!=n?n:i.base),options:i,basePath:""},this.analyze()}}},2043,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{ACTION_SUFFIX:function(){return m},APP_DIR_ALIAS:function(){return D},CACHE_ONE_YEAR:function(){return R},DOT_NEXT_ALIAS:function(){return I},ESLINT_DEFAULT_DIRS:function(){return et},GSP_NO_RETURNED_VALUE:function(){return X},GSSP_COMPONENT_MEMBER_ERROR:function(){return Z},GSSP_NO_RETURNED_VALUE:function(){return $},HTML_CONTENT_TYPE_HEADER:function(){return i},INFINITE_CACHE:function(){return P},INSTRUMENTATION_HOOK_FILENAME:function(){return O},JSON_CONTENT_TYPE_HEADER:function(){return o},MATCHED_PATH_HEADER:function(){return c},MIDDLEWARE_FILENAME:function(){return T},MIDDLEWARE_LOCATION_REGEXP:function(){return C},NEXT_BODY_SUFFIX:function(){return b},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return x},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return v},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return _},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return S},NEXT_CACHE_TAGS_HEADER:function(){return w},NEXT_CACHE_TAG_MAX_ITEMS:function(){return k},NEXT_CACHE_TAG_MAX_LENGTH:function(){return A},NEXT_DATA_SUFFIX:function(){return y},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return s},NEXT_META_SUFFIX:function(){return g},NEXT_QUERY_PARAM_PREFIX:function(){return a},NEXT_RESUME_HEADER:function(){return E},NON_STANDARD_NODE_ENV:function(){return Q},PAGES_DIR_ALIAS:function(){return U},PRERENDER_REVALIDATE_HEADER:function(){return l},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return u},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return q},ROOT_DIR_ALIAS:function(){return j},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return J},RSC_ACTION_ENCRYPTION_ALIAS:function(){return K},RSC_ACTION_PROXY_ALIAS:function(){return H},RSC_ACTION_VALIDATE_ALIAS:function(){return L},RSC_CACHE_WRAPPER_ALIAS:function(){return W},RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS:function(){return M},RSC_MOD_REF_PROXY_ALIAS:function(){return N},RSC_PREFETCH_SUFFIX:function(){return d},RSC_SEGMENTS_DIR_SUFFIX:function(){return p},RSC_SEGMENT_SUFFIX:function(){return h},RSC_SUFFIX:function(){return f},SERVER_PROPS_EXPORT_ERROR:function(){return V},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return z},SERVER_PROPS_SSG_CONFLICT:function(){return F},SERVER_RUNTIME:function(){return er},SSG_FALLBACK_EXPORT_ERROR:function(){return ee},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return B},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return G},TEXT_PLAIN_CONTENT_TYPE_HEADER:function(){return n},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return Y},WEBPACK_LAYERS:function(){return ei},WEBPACK_RESOURCE_QUERIES:function(){return eo}});let n="text/plain",i="text/html; charset=utf-8",o="application/json; charset=utf-8",a="nxtP",s="nxtI",c="x-matched-path",l="x-prerender-revalidate",u="x-prerender-revalidate-if-generated",d=".prefetch.rsc",p=".segments",h=".segment.rsc",f=".rsc",m=".action",y=".json",g=".meta",b=".body",w="x-next-cache-tags",v="x-next-revalidated-tags",_="x-next-revalidate-tag-token",E="next-resume",k=128,A=256,S=1024,x="_N_T_",R=31536e3,P=0xfffffffe,T="middleware",C="(?:src/)?".concat(T),O="instrumentation",U="private-next-pages",I="private-dot-next",j="private-next-root-dir",D="private-next-app-dir",N="private-next-rsc-mod-ref-proxy",L="private-next-rsc-action-validate",H="private-next-rsc-server-reference",W="private-next-rsc-cache-wrapper",M="private-next-rsc-track-dynamic-import",K="private-next-rsc-action-encryption",J="private-next-rsc-action-client-wrapper",q="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",B="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",z="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",F="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",G="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",V="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",X="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",$="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",Y="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",Z="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",Q='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',ee="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",et=["app","pages","components","lib","src"],er={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},en={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},ei={...en,GROUP:{builtinReact:[en.reactServerComponents,en.actionBrowser],serverOnly:[en.reactServerComponents,en.actionBrowser,en.instrument,en.middleware],neutralTarget:[en.apiNode,en.apiEdge],clientOnly:[en.serverSideRendering,en.appPagesBrowser],bundled:[en.reactServerComponents,en.actionBrowser,en.serverSideRendering,en.appPagesBrowser,en.shared,en.instrument,en.middleware],appPages:[en.reactServerComponents,en.serverSideRendering,en.appPagesBrowser,en.actionBrowser]}},eo={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},85646,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{fromNodeOutgoingHttpHeaders:function(){return i},normalizeNextQueryParam:function(){return c},splitCookiesString:function(){return o},toNodeOutgoingHttpHeaders:function(){return a},validateURL:function(){return s}});let n=e.r(2043);function i(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function o(e){var t,r,n,i,o,a=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,o=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(o=!0,s=i,a.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!o||s>=e.length)&&a.push(e.substring(t,e.length))}return a}function a(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...o(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function s(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error('URL is malformed "'.concat(String(e),'". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls'),{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}function c(e){for(let t of[n.NEXT_QUERY_PARAM_PREFIX,n.NEXT_INTERCEPTION_MARKER_PREFIX])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}},86265,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{PageSignatureError:function(){return n},RemovedPageError:function(){return i},RemovedUAError:function(){return o}});class n extends Error{constructor({page:e}){super('The middleware "'.concat(e,"\" accepts an async API directly with the form:\n  \n  export function middleware(request, event) {\n    return NextResponse.redirect('/new-location')\n  }\n  \n  Read more: https://nextjs.org/docs/messages/middleware-new-signature\n  "))}}class i extends Error{constructor(){super("The request.page has been deprecated in favour of `URLPattern`.\n  Read more: https://nextjs.org/docs/messages/middleware-request-page\n  ")}}class o extends Error{constructor(){super("The request.ua has been removed in favour of `userAgent` function.\n  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent\n  ")}}},40606,(e,t,r)=>{"use strict";var n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,s={};function c(e){var t;let r=["path"in e&&e.path&&"Path=".concat(e.path),"expires"in e&&(e.expires||0===e.expires)&&"Expires=".concat(("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()),"maxAge"in e&&"number"==typeof e.maxAge&&"Max-Age=".concat(e.maxAge),"domain"in e&&e.domain&&"Domain=".concat(e.domain),"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&"SameSite=".concat(e.sameSite),"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&"Priority=".concat(e.priority)].filter(Boolean),n="".concat(e.name,"=").concat(encodeURIComponent(null!=(t=e.value)?t:""));return 0===r.length?n:"".concat(n,"; ").concat(r.join("; "))}function l(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch(e){}}return t}function u(e){if(!e)return;let[[t,r],...n]=l(e),{domain:i,expires:o,httponly:a,maxage:s,path:c,samesite:u,secure:h,partitioned:f,priority:m}=Object.fromEntries(n.map(e=>{let[t,r]=e;return[t.toLowerCase().replace(/-/g,""),r]}));{var y,g,b={name:t,value:decodeURIComponent(r),domain:i,...o&&{expires:new Date(o)},...a&&{httpOnly:!0},..."string"==typeof s&&{maxAge:Number(s)},path:c,...u&&{sameSite:d.includes(y=(y=u).toLowerCase())?y:void 0},...h&&{secure:!0},...m&&{priority:p.includes(g=(g=m).toLowerCase())?g:void 0},...f&&{partitioned:!0}};let e={};for(let t in b)b[t]&&(e[t]=b[t]);return e}}((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(s,{RequestCookies:()=>h,ResponseCookies:()=>f,parseCookie:()=>l,parseSetCookie:()=>u,stringifyCookie:()=>c}),t.exports=((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let c of o(t))a.call(e,c)||c===r||n(e,c,{get:()=>t[c],enumerable:!(s=i(t,c))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s);var d=["strict","lax","none"],p=["low","medium","high"],h=class{[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n="string"==typeof t[0]?t[0]:t[0].name;return this._parsed.get(n)}getAll(){for(var e,t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let i=Array.from(this._parsed);if(!r.length)return i.map(e=>{let[t,r]=e;return r});let o="string"==typeof r[0]?r[0]:null==(e=r[0])?void 0:e.name;return i.filter(e=>{let[t]=e;return t===o}).map(e=>{let[t,r]=e;return r})}has(e){return this._parsed.has(e)}set(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,i]=1===t.length?[t[0].name,t[0].value]:t,o=this._parsed;return o.set(n,{name:n,value:i}),this._headers.set("cookie",Array.from(o).map(e=>{let[t,r]=e;return c(r)}).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(e=>{let[t,r]=e;return c(r)}).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return"RequestCookies ".concat(JSON.stringify(Object.fromEntries(this._parsed)))}toString(){return[...this._parsed.values()].map(e=>"".concat(e.name,"=").concat(encodeURIComponent(e.value))).join("; ")}constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of l(t))this._parsed.set(e,{name:e,value:r})}},f=class{get(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n="string"==typeof t[0]?t[0]:t[0].name;return this._parsed.get(n)}getAll(){for(var e,t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let i=Array.from(this._parsed.values());if(!r.length)return i;let o="string"==typeof r[0]?r[0]:null==(e=r[0])?void 0:e.name;return i.filter(e=>e.name===o)}has(e){return this._parsed.has(e)}set(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,i,o]=1===t.length?[t[0].name,t[0].value,t[0]]:t,a=this._parsed;return a.set(n,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{name:"",value:""};return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:n,value:i,...o})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=c(r);t.append("set-cookie",e)}}(a,this._headers),this}delete(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,i]="string"==typeof t[0]?[t[0]]:[t[0].name,t[0]];return this.set({...i,name:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return"ResponseCookies ".concat(JSON.stringify(Object.fromEntries(this._parsed)))}toString(){return[...this._parsed.values()].map(c).join("; ")}constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,o,a=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,o=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(o=!0,s=i,a.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!o||s>=e.length)&&a.push(e.substring(t,e.length))}return a}(i)){let t=u(e);t&&this._parsed.set(t.name,t)}}}},16762,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{RequestCookies:function(){return n.RequestCookies},ResponseCookies:function(){return n.ResponseCookies},stringifyCookie:function(){return n.stringifyCookie}});let n=e.r(40606)},20039,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{INTERNALS:function(){return s},NextRequest:function(){return c}});let n=e.r(15469),i=e.r(85646),o=e.r(86265),a=e.r(16762),s=Symbol("internal request");class c extends Request{[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[s].cookies}get nextUrl(){return this[s].nextUrl}get page(){throw new o.RemovedPageError}get ua(){throw new o.RemovedUAError}get url(){return this[s].url}constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);(0,i.validateURL)(r),t.body&&"half"!==t.duplex&&(t.duplex="half"),e instanceof Request?super(e,t):super(r,t);let o=new n.NextURL(r,{headers:(0,i.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:t.nextConfig});this[s]={cookies:new a.RequestCookies(this.headers),nextUrl:o,url:o.toString()}}}},74168,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"NextResponse",{enumerable:!0,get:function(){return d}});let n=e.r(16762),i=e.r(15469),o=e.r(85646),a=e.r(44640),s=e.r(16762),c=Symbol("internal response"),l=new Set([301,302,303,307,308]);function u(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class d extends Response{[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[c].cookies}static json(e,t){let r=Response.json(e,t);return new d(r.body,r)}static redirect(e,t){var r;let n="number"==typeof t?t:null!=(r=null==t?void 0:t.status)?r:307;if(!l.has(n))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let i="object"==typeof t?t:{},a=new Headers(null==i?void 0:i.headers);return a.set("Location",(0,o.validateURL)(e)),new d(null,{...i,headers:a,status:n})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,o.validateURL)(e)),u(t,r),new d(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),u(e,t),new d(null,{...e,headers:t})}constructor(e,t={}){super(e,t);let r=this.headers,l=new Proxy(new s.ResponseCookies(r),{get(e,i,o){switch(i){case"delete":case"set":return function(){for(var o=arguments.length,a=Array(o),c=0;c<o;c++)a[c]=arguments[c];let l=Reflect.apply(e[i],e,a),d=new Headers(r);return l instanceof s.ResponseCookies&&r.set("x-middleware-set-cookie",l.getAll().map(e=>(0,n.stringifyCookie)(e)).join(",")),u(t,d),l};default:return a.ReflectAdapter.get(e,i,o)}}});this[c]={cookies:l,url:t.url?new i.NextURL(t.url,{headers:(0,o.toNodeOutgoingHttpHeaders)(r),nextConfig:t.nextConfig}):void 0}}}},63639,(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead'),"__NEXT_ERROR_CODE",{value:"E183",enumerable:!1,configurable:!0})}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ImageResponse",{enumerable:!0,get:function(){return n}})},87552,(e,t,r)=>{(()=>{var r={226:function(t,r){!function(n,i){"use strict";var o="function",a="undefined",s="object",c="string",l="major",u="model",d="name",p="type",h="vendor",f="version",m="architecture",y="console",g="mobile",b="tablet",w="smarttv",v="wearable",_="embedded",E="Amazon",k="Apple",A="ASUS",S="BlackBerry",x="Browser",R="Chrome",P="Firefox",T="Google",C="Huawei",O="Microsoft",U="Motorola",I="Opera",j="Samsung",D="Sharp",N="Sony",L="Xiaomi",H="Zebra",W="Facebook",M="Chromium OS",K="Mac OS",J=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},q=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},B=function(e,t){return typeof e===c&&-1!==z(t).indexOf(z(e))},z=function(e){return e.toLowerCase()},F=function(e,t){if(typeof e===c)return e=e.replace(/^\s\s*/,""),typeof t===a?e:e.substring(0,350)},G=function(e,t){for(var r,n,a,c,l,u,d=0;d<t.length&&!l;){var p=t[d],h=t[d+1];for(r=n=0;r<p.length&&!l&&p[r];)if(l=p[r++].exec(e))for(a=0;a<h.length;a++)u=l[++n],typeof(c=h[a])===s&&c.length>0?2===c.length?typeof c[1]==o?this[c[0]]=c[1].call(this,u):this[c[0]]=c[1]:3===c.length?typeof c[1]!==o||c[1].exec&&c[1].test?this[c[0]]=u?u.replace(c[1],c[2]):void 0:this[c[0]]=u?c[1].call(this,u,c[2]):void 0:4===c.length&&(this[c[0]]=u?c[3].call(this,u.replace(c[1],c[2])):i):this[c]=u||i;d+=2}},V=function(e,t){for(var r in t)if(typeof t[r]===s&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(B(t[r][n],e))return"?"===r?i:r}else if(B(t[r],e))return"?"===r?i:r;return e},X={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},$={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[f,[d,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[f,[d,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[d,f],[/opios[\/ ]+([\w\.]+)/i],[f,[d,I+" Mini"]],[/\bopr\/([\w\.]+)/i],[f,[d,I]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[d,f],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[f,[d,"UC"+x]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[f,[d,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[f,[d,"WeChat"]],[/konqueror\/([\w\.]+)/i],[f,[d,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[f,[d,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[f,[d,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[d,/(.+)/,"$1 Secure "+x],f],[/\bfocus\/([\w\.]+)/i],[f,[d,P+" Focus"]],[/\bopt\/([\w\.]+)/i],[f,[d,I+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[f,[d,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[f,[d,"Dolphin"]],[/coast\/([\w\.]+)/i],[f,[d,I+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[f,[d,"MIUI "+x]],[/fxios\/([-\w\.]+)/i],[f,[d,P]],[/\bqihu|(qi?ho?o?|360)browser/i],[[d,"360 "+x]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[d,/(.+)/,"$1 "+x],f],[/(comodo_dragon)\/([\w\.]+)/i],[[d,/_/g," "],f],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[d,f],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[d],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[d,W],f],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[d,f],[/\bgsa\/([\w\.]+) .*safari\//i],[f,[d,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[f,[d,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[f,[d,R+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[d,R+" WebView"],f],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[f,[d,"Android "+x]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[d,f],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[f,[d,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[f,d],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[d,[f,V,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[d,f],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[d,"Netscape"],f],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[f,[d,P+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[d,f],[/(cobalt)\/([\w\.]+)/i],[d,[f,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[m,"amd64"]],[/(ia32(?=;))/i],[[m,z]],[/((?:i[346]|x)86)[;\)]/i],[[m,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[m,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[m,"armhf"]],[/windows (ce|mobile); ppc;/i],[[m,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[m,/ower/,"",z]],[/(sun4\w)[;\)]/i],[[m,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[m,z]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[u,[h,j],[p,b]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[u,[h,j],[p,g]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[u,[h,k],[p,g]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[u,[h,k],[p,b]],[/(macintosh);/i],[u,[h,k]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[u,[h,D],[p,g]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[u,[h,C],[p,b]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[u,[h,C],[p,g]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[u,/_/g," "],[h,L],[p,g]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[u,/_/g," "],[h,L],[p,b]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[u,[h,"OPPO"],[p,g]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[u,[h,"Vivo"],[p,g]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[u,[h,"Realme"],[p,g]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[u,[h,U],[p,g]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[u,[h,U],[p,b]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[u,[h,"LG"],[p,b]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[u,[h,"LG"],[p,g]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[u,[h,"Lenovo"],[p,b]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[u,/_/g," "],[h,"Nokia"],[p,g]],[/(pixel c)\b/i],[u,[h,T],[p,b]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[u,[h,T],[p,g]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[u,[h,N],[p,g]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[u,"Xperia Tablet"],[h,N],[p,b]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[u,[h,"OnePlus"],[p,g]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[u,[h,E],[p,b]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[u,/(.+)/g,"Fire Phone $1"],[h,E],[p,g]],[/(playbook);[-\w\),; ]+(rim)/i],[u,h,[p,b]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[u,[h,S],[p,g]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[u,[h,A],[p,b]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[u,[h,A],[p,g]],[/(nexus 9)/i],[u,[h,"HTC"],[p,b]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[h,[u,/_/g," "],[p,g]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[u,[h,"Acer"],[p,b]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[u,[h,"Meizu"],[p,g]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[h,u,[p,g]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[h,u,[p,b]],[/(surface duo)/i],[u,[h,O],[p,b]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[u,[h,"Fairphone"],[p,g]],[/(u304aa)/i],[u,[h,"AT&T"],[p,g]],[/\bsie-(\w*)/i],[u,[h,"Siemens"],[p,g]],[/\b(rct\w+) b/i],[u,[h,"RCA"],[p,b]],[/\b(venue[\d ]{2,7}) b/i],[u,[h,"Dell"],[p,b]],[/\b(q(?:mv|ta)\w+) b/i],[u,[h,"Verizon"],[p,b]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[u,[h,"Barnes & Noble"],[p,b]],[/\b(tm\d{3}\w+) b/i],[u,[h,"NuVision"],[p,b]],[/\b(k88) b/i],[u,[h,"ZTE"],[p,b]],[/\b(nx\d{3}j) b/i],[u,[h,"ZTE"],[p,g]],[/\b(gen\d{3}) b.+49h/i],[u,[h,"Swiss"],[p,g]],[/\b(zur\d{3}) b/i],[u,[h,"Swiss"],[p,b]],[/\b((zeki)?tb.*\b) b/i],[u,[h,"Zeki"],[p,b]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[h,"Dragon Touch"],u,[p,b]],[/\b(ns-?\w{0,9}) b/i],[u,[h,"Insignia"],[p,b]],[/\b((nxa|next)-?\w{0,9}) b/i],[u,[h,"NextBook"],[p,b]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[h,"Voice"],u,[p,g]],[/\b(lvtel\-)?(v1[12]) b/i],[[h,"LvTel"],u,[p,g]],[/\b(ph-1) /i],[u,[h,"Essential"],[p,g]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[u,[h,"Envizen"],[p,b]],[/\b(trio[-\w\. ]+) b/i],[u,[h,"MachSpeed"],[p,b]],[/\btu_(1491) b/i],[u,[h,"Rotor"],[p,b]],[/(shield[\w ]+) b/i],[u,[h,"Nvidia"],[p,b]],[/(sprint) (\w+)/i],[h,u,[p,g]],[/(kin\.[onetw]{3})/i],[[u,/\./g," "],[h,O],[p,g]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[u,[h,H],[p,b]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[u,[h,H],[p,g]],[/smart-tv.+(samsung)/i],[h,[p,w]],[/hbbtv.+maple;(\d+)/i],[[u,/^/,"SmartTV"],[h,j],[p,w]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[h,"LG"],[p,w]],[/(apple) ?tv/i],[h,[u,k+" TV"],[p,w]],[/crkey/i],[[u,R+"cast"],[h,T],[p,w]],[/droid.+aft(\w)( bui|\))/i],[u,[h,E],[p,w]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[u,[h,D],[p,w]],[/(bravia[\w ]+)( bui|\))/i],[u,[h,N],[p,w]],[/(mitv-\w{5}) bui/i],[u,[h,L],[p,w]],[/Hbbtv.*(technisat) (.*);/i],[h,u,[p,w]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[h,F],[u,F],[p,w]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[p,w]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[h,u,[p,y]],[/droid.+; (shield) bui/i],[u,[h,"Nvidia"],[p,y]],[/(playstation [345portablevi]+)/i],[u,[h,N],[p,y]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[u,[h,O],[p,y]],[/((pebble))app/i],[h,u,[p,v]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[u,[h,k],[p,v]],[/droid.+; (glass) \d/i],[u,[h,T],[p,v]],[/droid.+; (wt63?0{2,3})\)/i],[u,[h,H],[p,v]],[/(quest( 2| pro)?)/i],[u,[h,W],[p,v]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[h,[p,_]],[/(aeobc)\b/i],[u,[h,E],[p,_]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[u,[p,g]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[u,[p,b]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[p,b]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[p,g]],[/(android[-\w\. ]{0,9});.+buil/i],[u,[h,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[f,[d,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[f,[d,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[d,f],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[f,d]],os:[[/microsoft (windows) (vista|xp)/i],[d,f],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[d,[f,V,X]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[d,"Windows"],[f,V,X]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[f,/_/g,"."],[d,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[d,K],[f,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[f,d],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[d,f],[/\(bb(10);/i],[f,[d,S]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[f,[d,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[f,[d,P+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[f,[d,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[f,[d,"watchOS"]],[/crkey\/([\d\.]+)/i],[f,[d,R+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[d,M],f],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[d,f],[/(sunos) ?([\w\.\d]*)/i],[[d,"Solaris"],f],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[d,f]]},Y=function(e,t){if(typeof e===s&&(t=e,e=i),!(this instanceof Y))return new Y(e,t).getResult();var r=typeof n!==a&&n.navigator?n.navigator:i,y=e||(r&&r.userAgent?r.userAgent:""),w=r&&r.userAgentData?r.userAgentData:i,v=t?J($,t):$,_=r&&r.userAgent==y;return this.getBrowser=function(){var e,t={};return t[d]=i,t[f]=i,G.call(t,y,v.browser),t[l]=typeof(e=t[f])===c?e.replace(/[^\d\.]/g,"").split(".")[0]:i,_&&r&&r.brave&&typeof r.brave.isBrave==o&&(t[d]="Brave"),t},this.getCPU=function(){var e={};return e[m]=i,G.call(e,y,v.cpu),e},this.getDevice=function(){var e={};return e[h]=i,e[u]=i,e[p]=i,G.call(e,y,v.device),_&&!e[p]&&w&&w.mobile&&(e[p]=g),_&&"Macintosh"==e[u]&&r&&typeof r.standalone!==a&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[u]="iPad",e[p]=b),e},this.getEngine=function(){var e={};return e[d]=i,e[f]=i,G.call(e,y,v.engine),e},this.getOS=function(){var e={};return e[d]=i,e[f]=i,G.call(e,y,v.os),_&&!e[d]&&w&&"Unknown"!=w.platform&&(e[d]=w.platform.replace(/chrome os/i,M).replace(/macos/i,K)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return y},this.setUA=function(e){return y=typeof e===c&&e.length>350?F(e,350):e,this},this.setUA(y),this};if(Y.VERSION="1.0.35",Y.BROWSER=q([d,f,l]),Y.CPU=q([m]),Y.DEVICE=q([u,h,p,y,g,w,b,v,_]),Y.ENGINE=Y.OS=q([d,f]),typeof r!==a)t.exports&&(r=t.exports=Y),r.UAParser=Y;else if(typeof define===o&&define.amd)e.r,void 0!==Y&&e.v(Y);else typeof n!==a&&(n.UAParser=Y);var Z=typeof n!==a&&(n.jQuery||n.Zepto);if(Z&&!Z.ua){var Q=new Y;Z.ua=Q.getResult(),Z.ua.get=function(){return Q.getUA()},Z.ua.set=function(e){Q.setUA(e);var t=Q.getResult();for(var r in t)Z.ua[r]=t[r]}}}("object"==typeof window?window:this)}},n={};function i(e){var t=n[e];if(void 0!==t)return t.exports;var o=n[e]={exports:{}},a=!0;try{r[e].call(o.exports,o,o.exports,i),a=!1}finally{a&&delete n[e]}return o.exports}i.ab="/ROOT/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/ua-parser-js/",t.exports=i(226)})()},11384,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{isBot:function(){return i},userAgent:function(){return a},userAgentFromString:function(){return o}});let n=function(e){return e&&e.__esModule?e:{default:e}}(e.r(87552));function i(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}function o(e){return{...(0,n.default)(e),isBot:void 0!==e&&i(e)}}function a(e){let{headers:t}=e;return o(t.get("user-agent")||void 0)}},25271,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"URLPattern",{enumerable:!0,get:function(){return n}});let n="undefined"==typeof URLPattern?void 0:URLPattern},38340,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"after",{enumerable:!0,get:function(){return i}});let n=e.r(52113);function i(e){let t=n.workAsyncStorage.getStore();if(!t)throw Object.defineProperty(Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context"),"__NEXT_ERROR_CODE",{value:"E468",enumerable:!1,configurable:!0});let{afterContext:r}=t;return r.after(e)}},25510,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),function(e,t){Object.keys(e).forEach(function(r){"default"===r||Object.prototype.hasOwnProperty.call(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[r]}})})}(e.r(38340),r)},39694,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"connection",{enumerable:!0,get:function(){return l}});let n=e.r(52113),i=e.r(3335),o=e.r(35092),a=e.r(85115),s=e.r(82698),c=e.r(17939);function l(){let e=n.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,c.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error("Route ".concat(e.route,' used "connection" inside "after(...)". The `connection()` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after')),"__NEXT_ERROR_CODE",{value:"E186",enumerable:!1,configurable:!0});if(e.forceStatic)return Promise.resolve(void 0);if(e.dynamicShouldError)throw Object.defineProperty(new a.StaticGenBailoutError("Route ".concat(e.route,' with `dynamic = "error"` couldn\'t be rendered statically because it used `connection`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering')),"__NEXT_ERROR_CODE",{value:"E562",enumerable:!1,configurable:!0});if(t){switch(t.type){case"cache":{let t=Object.defineProperty(Error("Route ".concat(e.route,' used "connection" inside "use cache". The `connection()` function is used to indicate the subsequent code must only run when there is an actual request, but caches must be able to be produced before a request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E752",enumerable:!1,configurable:!0});throw Error.captureStackTrace(t,l),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=t),t}case"private-cache":{let t=Object.defineProperty(Error("Route ".concat(e.route,' used "connection" inside "use cache: private". The `connection()` function is used to indicate the subsequent code must only run when there is an actual navigation request, but caches must be able to be produced before a navigation request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E753",enumerable:!1,configurable:!0});throw Error.captureStackTrace(t,l),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=t),t}case"unstable-cache":throw Object.defineProperty(Error("Route ".concat(e.route,' used "connection" inside a function cached with "unstable_cache(...)". The `connection()` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache')),"__NEXT_ERROR_CODE",{value:"E1",enumerable:!1,configurable:!0});case"prerender":case"prerender-client":case"prerender-runtime":return(0,s.makeHangingPromise)(t.renderSignal,e.route,"`connection()`");case"prerender-ppr":return(0,o.postponeWithTracking)(e.route,"connection",t.dynamicTracking);case"prerender-legacy":return(0,o.throwToInterruptStaticGeneration)("connection",e,t);case"request":return(0,o.trackDynamicDataInDynamicRender)(t),Promise.resolve(void 0)}}}(0,i.throwForMissingRequestStore)("connection")}},48421,(e,t,r)=>{"use strict";var n,i;Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{bgBlack:function(){return P},bgBlue:function(){return U},bgCyan:function(){return j},bgGreen:function(){return C},bgMagenta:function(){return I},bgRed:function(){return T},bgWhite:function(){return D},bgYellow:function(){return O},black:function(){return b},blue:function(){return E},bold:function(){return d},cyan:function(){return S},dim:function(){return p},gray:function(){return R},green:function(){return v},hidden:function(){return y},inverse:function(){return m},italic:function(){return h},magenta:function(){return k},purple:function(){return A},red:function(){return w},reset:function(){return u},strikethrough:function(){return g},underline:function(){return f},white:function(){return x},yellow:function(){return _}});let{env:o,stdout:a}=null!=(i=null==(n=globalThis)?void 0:n.process)?i:{},s=o&&!o.NO_COLOR&&(o.FORCE_COLOR||(null==a?void 0:a.isTTY)&&!o.CI&&"dumb"!==o.TERM),c=(e,t,r,n)=>{let i=e.substring(0,n)+r,o=e.substring(n+t.length),a=o.indexOf(t);return~a?i+c(o,t,r,a):i+o},l=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e;return s?n=>{let i=""+n,o=i.indexOf(t,e.length);return~o?e+c(i,t,r,o)+t:e+i+t}:String},u=s?e=>"\x1b[0m".concat(e,"\x1b[0m"):String,d=l("\x1b[1m","\x1b[22m","\x1b[22m\x1b[1m"),p=l("\x1b[2m","\x1b[22m","\x1b[22m\x1b[2m"),h=l("\x1b[3m","\x1b[23m"),f=l("\x1b[4m","\x1b[24m"),m=l("\x1b[7m","\x1b[27m"),y=l("\x1b[8m","\x1b[28m"),g=l("\x1b[9m","\x1b[29m"),b=l("\x1b[30m","\x1b[39m"),w=l("\x1b[31m","\x1b[39m"),v=l("\x1b[32m","\x1b[39m"),_=l("\x1b[33m","\x1b[39m"),E=l("\x1b[34m","\x1b[39m"),k=l("\x1b[35m","\x1b[39m"),A=l("\x1b[38;2;173;127;168m","\x1b[39m"),S=l("\x1b[36m","\x1b[39m"),x=l("\x1b[37m","\x1b[39m"),R=l("\x1b[90m","\x1b[39m"),P=l("\x1b[40m","\x1b[49m"),T=l("\x1b[41m","\x1b[49m"),C=l("\x1b[42m","\x1b[49m"),O=l("\x1b[43m","\x1b[49m"),U=l("\x1b[44m","\x1b[49m"),I=l("\x1b[45m","\x1b[49m"),j=l("\x1b[46m","\x1b[49m"),D=l("\x1b[47m","\x1b[49m")},19812,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"LRUCache",{enumerable:!0,get:function(){return o}});class n{constructor(e,t,r){this.prev=null,this.next=null,this.key=e,this.data=t,this.size=r}}class i{constructor(){this.prev=null,this.next=null}}class o{addToHead(e){e.prev=this.head,e.next=this.head.next,this.head.next.prev=e,this.head.next=e}removeNode(e){e.prev.next=e.next,e.next.prev=e.prev}moveToHead(e){this.removeNode(e),this.addToHead(e)}removeTail(){let e=this.tail.prev;return this.removeNode(e),e}set(e,t){var r;let i=null!=(r=null==this.calculateSize?void 0:this.calculateSize.call(this,t))?r:1;if(i>this.maxSize)return void console.warn("Single item size exceeds maxSize");let o=this.cache.get(e);if(o)o.data=t,this.totalSize=this.totalSize-o.size+i,o.size=i,this.moveToHead(o);else{let r=new n(e,t,i);this.cache.set(e,r),this.addToHead(r),this.totalSize+=i}for(;this.totalSize>this.maxSize&&this.cache.size>0;){let e=this.removeTail();this.cache.delete(e.key),this.totalSize-=e.size}}has(e){return this.cache.has(e)}get(e){let t=this.cache.get(e);if(t)return this.moveToHead(t),t.data}*[Symbol.iterator](){let e=this.head.next;for(;e&&e!==this.tail;){let t=e;yield[t.key,t.data],e=e.next}}remove(e){let t=this.cache.get(e);t&&(this.removeNode(t),this.cache.delete(e),this.totalSize-=t.size)}get size(){return this.cache.size}get currentSize(){return this.totalSize}constructor(e,t){this.cache=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t,this.head=new i,this.tail=new i,this.head.next=this.tail,this.tail.prev=this.head}}},63143,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{bootstrap:function(){return c},error:function(){return u},event:function(){return f},info:function(){return h},prefixes:function(){return o},ready:function(){return p},trace:function(){return m},wait:function(){return l},warn:function(){return d},warnOnce:function(){return g}});let n=e.r(48421),i=e.r(19812),o={wait:(0,n.white)((0,n.bold)("○")),error:(0,n.red)((0,n.bold)("⨯")),warn:(0,n.yellow)((0,n.bold)("⚠")),ready:"▲",info:(0,n.white)((0,n.bold)(" ")),event:(0,n.green)((0,n.bold)("✓")),trace:(0,n.magenta)((0,n.bold)("»"))},a={log:"log",warn:"warn",error:"error"};function s(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];(""===r[0]||void 0===r[0])&&1===r.length&&r.shift();let i=e in a?a[e]:"log",s=o[e];0===r.length?console[i](""):1===r.length&&"string"==typeof r[0]?console[i](" "+s+" "+r[0]):console[i](" "+s,...r)}function c(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];console.log("   "+t.join(" "))}function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];s("wait",...t)}function u(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];s("error",...t)}function d(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];s("warn",...t)}function p(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];s("ready",...t)}function h(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];s("info",...t)}function f(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];s("event",...t)}function m(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];s("trace",...t)}let y=new i.LRUCache(1e4,e=>e.length);function g(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=t.join(" ");y.has(n)||(y.set(n,n),d(...t))}},44874,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{getRootParam:function(){return h},unstable_rootParams:function(){return p}});let n=e.r(33076),i=e.r(35092),o=e.r(52113),a=e.r(3335),s=e.r(82698),c=e.r(93168),l=e.r(67923),u=e.r(63143),d=new WeakMap;async function p(){(0,u.warnOnce)("`unstable_rootParams()` is deprecated and will be removed in an upcoming major release. Import specific root params from `next/root-params` instead.");let e=o.workAsyncStorage.getStore();if(!e)throw Object.defineProperty(new n.InvariantError("Missing workStore in unstable_rootParams"),"__NEXT_ERROR_CODE",{value:"E615",enumerable:!1,configurable:!0});let t=a.workUnitAsyncStorage.getStore();if(!t)throw Object.defineProperty(Error("Route ".concat(e.route," used `unstable_rootParams()` in Pages Router. This API is only available within App Router.")),"__NEXT_ERROR_CODE",{value:"E641",enumerable:!1,configurable:!0});switch(t.type){case"cache":case"unstable-cache":throw Object.defineProperty(Error("Route ".concat(e.route,' used `unstable_rootParams()` inside `"use cache"` or `unstable_cache`. Support for this API inside cache scopes is planned for a future version of Next.js.')),"__NEXT_ERROR_CODE",{value:"E642",enumerable:!1,configurable:!0});case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":return function(e,t,r){switch(r.type){case"prerender-client":{let e="`unstable_rootParams`";throw Object.defineProperty(new n.InvariantError("".concat(e," must not be used within a client component. Next.js should be preventing ").concat(e," from being included in client components statically, but did not in this case.")),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0})}case"prerender":{let n=r.fallbackRouteParams;if(n){for(let i in e)if(n.has(i)){let n=d.get(e);if(n)return n;let i=(0,s.makeHangingPromise)(r.renderSignal,t.route,"`unstable_rootParams`");return d.set(e,i),i}}break}case"prerender-ppr":{let n=r.fallbackRouteParams;if(n){for(let o in e)if(n.has(o))return function(e,t,r,n){let o=d.get(e);if(o)return o;let a={...e},s=Promise.resolve(a);return d.set(e,s),Object.keys(e).forEach(o=>{c.wellKnownProperties.has(o)||(t.has(o)?Object.defineProperty(a,o,{get(){let e=(0,c.describeStringPropertyAccess)("unstable_rootParams",o);"prerender-ppr"===n.type?(0,i.postponeWithTracking)(r.route,e,n.dynamicTracking):(0,i.throwToInterruptStaticGeneration)(e,r,n)},enumerable:!0}):s[o]=e[o])}),s}(e,n,t,r)}}}return Promise.resolve(e)}(t.rootParams,e,t);case"private-cache":case"prerender-runtime":case"request":return Promise.resolve(t.rootParams);default:return t}}function h(e){let t="`import('next/root-params').".concat(e,"()`"),r=o.workAsyncStorage.getStore();if(!r)throw Object.defineProperty(new n.InvariantError("Missing workStore in ".concat(t)),"__NEXT_ERROR_CODE",{value:"E764",enumerable:!1,configurable:!0});let i=a.workUnitAsyncStorage.getStore();if(!i)throw Object.defineProperty(Error("Route ".concat(r.route," used ").concat(t," outside of a Server Component. This is not allowed.")),"__NEXT_ERROR_CODE",{value:"E774",enumerable:!1,configurable:!0});let c=l.actionAsyncStorage.getStore();if(c){if(c.isAppRoute)throw Object.defineProperty(Error("Route ".concat(r.route," used ").concat(t," inside a Route Handler. Support for this API in Route Handlers is planned for a future version of Next.js.")),"__NEXT_ERROR_CODE",{value:"E765",enumerable:!1,configurable:!0});if(c.isAction&&"action"===i.phase)throw Object.defineProperty(Error("".concat(t," was used inside a Server Action. This is not supported. Functions from 'next/root-params' can only be called in the context of a route.")),"__NEXT_ERROR_CODE",{value:"E766",enumerable:!1,configurable:!0})}switch(i.type){case"unstable-cache":case"cache":throw Object.defineProperty(Error("Route ".concat(r.route," used ").concat(t,' inside `"use cache"` or `unstable_cache`. Support for this API inside cache scopes is planned for a future version of Next.js.')),"__NEXT_ERROR_CODE",{value:"E760",enumerable:!1,configurable:!0});case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":var u=e,d=r,p=i,h=t;if("prerender-client"===p.type)throw Object.defineProperty(new n.InvariantError("".concat(h," must not be used within a client component. Next.js should be preventing ").concat(h," from being included in client components statically, but did not in this case.")),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});let m=p.rootParams;switch(p.type){case"prerender":if(p.fallbackRouteParams&&p.fallbackRouteParams.has(u))return(0,s.makeHangingPromise)(p.renderSignal,d.route,h);break;case"prerender-ppr":if(p.fallbackRouteParams&&p.fallbackRouteParams.has(u))return f(u,d,p,h)}return Promise.resolve(m[u])}return Promise.resolve(i.rootParams[e])}async function f(e,t,r,n){let o=(0,c.describeStringPropertyAccess)(n,e);switch(r.type){case"prerender-ppr":return(0,i.postponeWithTracking)(t.route,o,r.dynamicTracking);case"prerender-legacy":return(0,i.throwToInterruptStaticGeneration)(o,t,r)}}},52120,(e,t,r)=>{let n={NextRequest:e.r(20039).NextRequest,NextResponse:e.r(74168).NextResponse,ImageResponse:e.r(63639).ImageResponse,userAgentFromString:e.r(11384).userAgentFromString,userAgent:e.r(11384).userAgent,URLPattern:e.r(25271).URLPattern,after:e.r(25510).after,connection:e.r(39694).connection,unstable_rootParams:e.r(44874).unstable_rootParams};t.exports=n,r.NextRequest=n.NextRequest,r.NextResponse=n.NextResponse,r.ImageResponse=n.ImageResponse,r.userAgentFromString=n.userAgentFromString,r.userAgent=n.userAgent,r.URLPattern=n.URLPattern,r.after=n.after,r.connection=n.connection,r.unstable_rootParams=n.unstable_rootParams},83788,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{MutableRequestCookiesAdapter:function(){return d},ReadonlyRequestCookiesError:function(){return a},RequestCookiesAdapter:function(){return s},appendMutableCookies:function(){return u},areCookiesMutableInCurrentPhase:function(){return h},createCookiesWithMutableAccessCheck:function(){return p},getModifiedCookieValues:function(){return l},responseCookiesToRequestCookies:function(){return m}});let n=e.r(16762),i=e.r(44640),o=e.r(52113);class a extends Error{static callable(){throw new a}constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}}class s{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return a.callable;default:return i.ReflectAdapter.get(e,t,r)}}})}}let c=Symbol.for("next.mutated.cookies");function l(e){let t=e[c];return t&&Array.isArray(t)&&0!==t.length?t:[]}function u(e,t){let r=l(t);if(0===r.length)return!1;let i=new n.ResponseCookies(e),o=i.getAll();for(let e of r)i.set(e);for(let e of o)i.set(e);return!0}class d{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let a=[],s=new Set,l=()=>{let e=o.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of a){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},u=new Proxy(r,{get(e,t,r){switch(t){case c:return a;case"delete":return function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];s.add("string"==typeof r[0]?r[0]:r[0].name);try{return e.delete(...r),u}finally{l()}};case"set":return function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];s.add("string"==typeof r[0]?r[0]:r[0].name);try{return e.set(...r),u}finally{l()}};default:return i.ReflectAdapter.get(e,t,r)}}});return u}}function p(e){let t=new Proxy(e.mutableCookies,{get(r,n,o){switch(n){case"delete":return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return f(e,"cookies().delete"),r.delete(...i),t};case"set":return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return f(e,"cookies().set"),r.set(...i),t};default:return i.ReflectAdapter.get(r,n,o)}}});return t}function h(e){return"action"===e.phase}function f(e,t){if(!h(e))throw new a}function m(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},11178,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"cookies",{enumerable:!0,get:function(){return h}});let n=e.r(83788),i=e.r(16762),o=e.r(52113),a=e.r(3335),s=e.r(35092),c=e.r(85115),l=e.r(82698),u=e.r(63470),d=e.r(17939),p=e.r(33076);function h(){let e="cookies",t=o.workAsyncStorage.getStore(),r=a.workUnitAsyncStorage.getStore();if(t){if(r&&"after"===r.phase&&!(0,d.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error("Route ".concat(t.route,' used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after')),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return m(n.RequestCookiesAdapter.seal(new i.RequestCookies(new Headers({}))));if(t.dynamicShouldError)throw Object.defineProperty(new c.StaticGenBailoutError("Route ".concat(t.route,' with `dynamic = "error"` couldn\'t be rendered statically because it used `cookies`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering')),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r)switch(r.type){case"cache":let o=Object.defineProperty(Error("Route ".concat(t.route,' used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});throw Error.captureStackTrace(o,h),null!=t.invalidDynamicUsageError||(t.invalidDynamicUsageError=o),o;case"unstable-cache":throw Object.defineProperty(Error("Route ".concat(t.route,' used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache')),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0});case"prerender":var u=t,y=r;let a=f.get(y);if(a)return a;let g=(0,l.makeHangingPromise)(y.renderSignal,u.route,"`cookies()`");return f.set(y,g),g;case"prerender-client":let b="`cookies`";throw Object.defineProperty(new p.InvariantError("".concat(b," must not be used within a client component. Next.js should be preventing ").concat(b," from being included in client components statically, but did not in this case.")),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":return(0,s.postponeWithTracking)(t.route,e,r.dynamicTracking);case"prerender-legacy":return(0,s.throwToInterruptStaticGeneration)(e,t,r);case"prerender-runtime":return(0,s.delayUntilRuntimeStage)(r,function(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),r}(r.cookies));case"private-cache":return m(r.cookies);case"request":return(0,s.trackDynamicDataInDynamicRender)(r),m((0,n.areCookiesMutableInCurrentPhase)(r)?r.userspaceMutableCookies:r.cookies)}}(0,a.throwForMissingRequestStore)(e)}e.r(44640);let f=new WeakMap;function m(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):y.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):g.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function y(){return this.getAll().map(e=>[e.name,e]).values()}function g(e){for(let e of this.getAll())this.delete(e.name);return e}(0,u.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){return Object.defineProperty(Error("".concat(e?'Route "'.concat(e,'" '):"This route ","used ").concat(t,". ")+"`cookies()` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis"),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})})},52123,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{HeadersAdapter:function(){return o},ReadonlyHeadersError:function(){return i}});let n=e.r(44640);class i extends Error{static callable(){throw new i}constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}}class o extends Headers{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return i.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new o(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,i);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);if(void 0!==a)return n.ReflectAdapter.get(t,a,i)},set(t,r,i,o){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,i,o);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);return n.ReflectAdapter.set(t,null!=s?s:r,i,o)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==o&&n.ReflectAdapter.has(t,o)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===o||n.ReflectAdapter.deleteProperty(t,o)}})}}},35223,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"headers",{enumerable:!0,get:function(){return p}});let n=e.r(52123),i=e.r(52113),o=e.r(3335),a=e.r(35092),s=e.r(85115),c=e.r(82698),l=e.r(63470),u=e.r(17939),d=e.r(33076);function p(){let e="headers",t=i.workAsyncStorage.getStore(),r=o.workUnitAsyncStorage.getStore();if(t){if(r&&"after"===r.phase&&!(0,u.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error("Route ".concat(t.route,' used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after')),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(t.forceStatic)return f(n.HeadersAdapter.seal(new Headers({})));if(r){switch(r.type){case"cache":{let e=Object.defineProperty(Error("Route ".concat(t.route,' used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});throw Error.captureStackTrace(e,p),null!=t.invalidDynamicUsageError||(t.invalidDynamicUsageError=e),e}case"private-cache":{let e=Object.defineProperty(Error("Route ".concat(t.route,' used "headers" inside "use cache: private". Accessing "headers" inside a private cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E742",enumerable:!1,configurable:!0});throw Error.captureStackTrace(e,p),null!=t.invalidDynamicUsageError||(t.invalidDynamicUsageError=e),e}case"unstable-cache":throw Object.defineProperty(Error("Route ".concat(t.route,' used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache')),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}}if(t.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(t.route,' with `dynamic = "error"` couldn\'t be rendered statically because it used `headers`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering')),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(r)switch(r.type){case"prerender":case"prerender-runtime":var l=t,m=r;let i=h.get(m);if(i)return i;let o=(0,c.makeHangingPromise)(m.renderSignal,l.route,"`headers()`");return h.set(m,o),o;case"prerender-client":let y="`headers`";throw Object.defineProperty(new d.InvariantError("".concat(y," must not be used within a client component. Next.js should be preventing ").concat(y," from being included in client components statically, but did not in this case.")),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":return(0,a.postponeWithTracking)(t.route,e,r.dynamicTracking);case"prerender-legacy":return(0,a.throwToInterruptStaticGeneration)(e,t,r);case"request":return(0,a.trackDynamicDataInDynamicRender)(r),f(r.headers)}}(0,o.throwForMissingRequestStore)(e)}e.r(44640);let h=new WeakMap;function f(e){let t=h.get(e);if(t)return t;let r=Promise.resolve(e);return h.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}(0,l.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){return Object.defineProperty(Error("".concat(e?'Route "'.concat(e,'" '):"This route ","used ").concat(t,". ")+"`headers()` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis"),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})})},68774,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"draftMode",{enumerable:!0,get:function(){return u}});let n=e.r(3335),i=e.r(52113),o=e.r(35092),a=e.r(63470),s=e.r(85115),c=e.r(3091),l=e.r(33076);function u(){let e=i.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,n.throwForMissingRequestStore)("draftMode"),t.type){case"prerender-runtime":return(0,o.delayUntilRuntimeStage)(t,d(t.draftMode,e));case"request":return d(t.draftMode,e);case"cache":case"private-cache":case"unstable-cache":let r=(0,n.getDraftModeProviderForCacheScope)(e,t);if(r)return d(r,e);case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":return d(null,e);default:return t}}function d(e,t){let r,n=null!=e?e:p,i=h.get(n);return i||(r=function(e){let t=new f(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}(e),h.set(n,r),r)}e.r(44640);let p={},h=new WeakMap;class f{get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){m("draftMode().enable()",this.enable),null!==this._provider&&this._provider.enable()}disable(){m("draftMode().disable()",this.disable),null!==this._provider&&this._provider.disable()}constructor(e){this._provider=e}}function m(e,t){let r=i.workAsyncStorage.getStore(),a=n.workUnitAsyncStorage.getStore();if(r){if((null==a?void 0:a.phase)==="after")throw Object.defineProperty(Error("Route ".concat(r.route,' used "').concat(e,'" inside `after`. The enabled status of draftMode can be read inside `after` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after')),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0});if(r.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(r.route,' with `dynamic = "error"` couldn\'t be rendered statically because it used `').concat(e,"`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(a)switch(a.type){case"cache":case"private-cache":{let n=Object.defineProperty(Error("Route ".concat(r.route,' used "').concat(e,'" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});throw Error.captureStackTrace(n,t),null!=r.invalidDynamicUsageError||(r.invalidDynamicUsageError=n),n}case"unstable-cache":throw Object.defineProperty(Error("Route ".concat(r.route,' used "').concat(e,'" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache')),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});case"prerender":case"prerender-runtime":{let t=Object.defineProperty(Error("Route ".concat(r.route," used ").concat(e," without first calling `await connection()`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers")),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});return(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r.route,e,t,a)}case"prerender-client":let n="`draftMode`";throw Object.defineProperty(new l.InvariantError("".concat(n," must not be used within a client component. Next.js should be preventing ").concat(n," from being included in client components statically, but did not in this case.")),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":return(0,o.postponeWithTracking)(r.route,e,a.dynamicTracking);case"prerender-legacy":a.revalidate=0;let i=Object.defineProperty(new c.DynamicServerError("Route ".concat(r.route," couldn't be rendered statically because it used `").concat(e,"`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error")),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.dynamicUsageDescription=e,r.dynamicUsageStack=i.stack,i;case"request":(0,o.trackDynamicDataInDynamicRender)(a)}}}(0,a.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){return Object.defineProperty(Error("".concat(e?'Route "'.concat(e,'" '):"This route ","used ").concat(t,". ")+"`draftMode()` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis"),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})})},42700,(e,t,r)=>{t.exports.cookies=e.r(11178).cookies,t.exports.headers=e.r(35223).headers,t.exports.draftMode=e.r(68774).draftMode},1831,e=>{"use strict";let t,r,n,i;e.s(["apiService",()=>oj],1831);var o,a,s=e.i(50460);function c(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var l=e.i(1269),u=function(e,t,r,n,i){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?i.call(e,r):i?i.value=r:t.set(e,r),r},d=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};function p(e){let t=e?"__Secure-":"";return{sessionToken:{name:"".concat(t,"authjs.session-token"),options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:"".concat(t,"authjs.callback-url"),options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:"".concat(e?"__Host-":"","authjs.csrf-token"),options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:"".concat(t,"authjs.pkce.code_verifier"),options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:"".concat(t,"authjs.state"),options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:"".concat(t,"authjs.nonce"),options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:"".concat(t,"authjs.challenge"),options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class h{get value(){return Object.keys(d(this,t7,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>d(this,t7,"f")[e]).join("")}chunk(e,t){let r=d(this,t9,"m",rn).call(this);for(let n of d(this,t9,"m",rr).call(this,{name:d(this,re,"f").name,value:e,options:{...d(this,re,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(d(this,t9,"m",rn).call(this))}constructor(e,t,r){if(t9.add(this),t7.set(this,{}),re.set(this,void 0),rt.set(this,void 0),u(this,rt,r,"f"),u(this,re,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(d(this,t7,"f")[e]=r)}}t7=new WeakMap,re=new WeakMap,rt=new WeakMap,t9=new WeakSet,rr=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return d(this,t7,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t="".concat(e.name,".").concat(n),i=e.value.substr(3936*n,3936);r.push({...e,name:t,value:i}),d(this,t7,"f")[t]=i}return d(this,rt,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed ".concat(4096," bytes."),emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},rn=function(){let e={};for(let r in d(this,t7,"f")){var t;null==(t=d(this,t7,"f"))||delete t[r],e[r]={name:r,value:"",options:{...d(this,re,"f").options,maxAge:0}}}return e};var f=e.i(53838);let m=!1;function y(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch(e){return!1}}let g=!1,b=!1,w=!1,v=["createVerificationToken","useVerificationToken","getUserByEmail"],_=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],E=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"],k=async(e,t,r,n,i)=>{let{crypto:{subtle:o}}=(()=>{if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;throw Error("unable to locate global object")})();return new Uint8Array(await o.deriveBits({name:"HKDF",hash:"SHA-".concat(e.substr(3)),salt:r,info:n},await o.importKey("raw",t,"HKDF",!1,["deriveBits"]),i<<3))};function A(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError('"'.concat(t,'"" must be an instance of Uint8Array or a string'));return e}async function S(e,t,r,n,i){return k(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=A(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),A(r,"salt"),function(e){let t=A(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(i,e))}var x=e.i(61816),R=e.i(40003),P=e.i(76233);e.s(["decode",()=>D,"encode",()=>N],28706);let T=new TextEncoder,C=new TextDecoder;function O(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=new Uint8Array(t.reduce((e,t)=>{let{length:r}=t;return e+r},0)),i=0;for(let e of t)n.set(e,i),i+=e.length;return n}function U(e,t,r){if(t<0||t>=0x100000000)throw RangeError("value must be >= 0 and <= ".concat(0x100000000-1,". Received ").concat(t));e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function I(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return U(r,t,0),U(r,e%0x100000000,4),r}function j(e){let t=new Uint8Array(4);return U(t,e),t}function D(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:C.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=C.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{var r=t;if(Uint8Array.fromBase64)return Uint8Array.fromBase64(r);let e=atob(r),n=new Uint8Array(e.length);for(let t=0;t<e.length;t++)n[t]=e.charCodeAt(t);return n}catch(e){throw TypeError("The input to be decoded is not correctly encoded.")}}function N(e){let t=e;return("string"==typeof t&&(t=T.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}let L=Symbol();class H extends Error{constructor(e,t){var r,n;super(e,t),c(this,"code","ERR_JOSE_GENERIC"),this.name=this.constructor.name,null==(r=(n=Error).captureStackTrace)||r.call(n,this,this.constructor)}}c(H,"code","ERR_JOSE_GENERIC");class W extends H{constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),c(this,"code","ERR_JWT_CLAIM_VALIDATION_FAILED"),c(this,"claim",void 0),c(this,"reason",void 0),c(this,"payload",void 0),this.claim=r,this.reason=n,this.payload=t}}c(W,"code","ERR_JWT_CLAIM_VALIDATION_FAILED");class M extends H{constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),c(this,"code","ERR_JWT_EXPIRED"),c(this,"claim",void 0),c(this,"reason",void 0),c(this,"payload",void 0),this.claim=r,this.reason=n,this.payload=t}}c(M,"code","ERR_JWT_EXPIRED");class K extends H{constructor(...e){super(...e),c(this,"code","ERR_JOSE_ALG_NOT_ALLOWED")}}c(K,"code","ERR_JOSE_ALG_NOT_ALLOWED");class J extends H{constructor(...e){super(...e),c(this,"code","ERR_JOSE_NOT_SUPPORTED")}}c(J,"code","ERR_JOSE_NOT_SUPPORTED");class q extends H{constructor(e="decryption operation failed",t){super(e,t),c(this,"code","ERR_JWE_DECRYPTION_FAILED")}}c(q,"code","ERR_JWE_DECRYPTION_FAILED");class B extends H{constructor(...e){super(...e),c(this,"code","ERR_JWE_INVALID")}}c(B,"code","ERR_JWE_INVALID"),c(class extends H{constructor(...e){super(...e),c(this,"code","ERR_JWS_INVALID")}},"code","ERR_JWS_INVALID");class z extends H{constructor(...e){super(...e),c(this,"code","ERR_JWT_INVALID")}}c(z,"code","ERR_JWT_INVALID");class F extends H{constructor(...e){super(...e),c(this,"code","ERR_JWK_INVALID")}}function G(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new J("Unsupported JWE Algorithm: ".concat(e))}}c(F,"code","ERR_JWK_INVALID"),c(class extends H{constructor(...e){super(...e),c(this,"code","ERR_JWKS_INVALID")}},"code","ERR_JWKS_INVALID"),c(class extends H{constructor(e="no applicable key found in the JSON Web Key Set",t){super(e,t),c(this,"code","ERR_JWKS_NO_MATCHING_KEY")}},"code","ERR_JWKS_NO_MATCHING_KEY"),c(class extends H{constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t),c(this,Symbol.asyncIterator,void 0),c(this,"code","ERR_JWKS_MULTIPLE_MATCHING_KEYS")}},"code","ERR_JWKS_MULTIPLE_MATCHING_KEYS"),c(class extends H{constructor(e="request timed out",t){super(e,t),c(this,"code","ERR_JWKS_TIMEOUT")}},"code","ERR_JWKS_TIMEOUT"),c(class extends H{constructor(e="signature verification failed",t){super(e,t),c(this,"code","ERR_JWS_SIGNATURE_VERIFICATION_FAILED")}},"code","ERR_JWS_SIGNATURE_VERIFICATION_FAILED");let V=(e,t)=>{if(t.length<<3!==G(e))throw new B("Invalid Initialization Vector length")},X=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new B("Invalid Content Encryption Key length. Expected ".concat(t," bits, got ").concat(r," bits"))};function $(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"algorithm.name";return TypeError("CryptoKey does not support this operation, its ".concat(t," must be ").concat(e))}function Y(e,t){return e.name===t}function Z(e,t,r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!Y(e.algorithm,"AES-GCM"))throw $("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw $(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!Y(e.algorithm,"AES-KW"))throw $("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw $(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":break;default:throw $("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!Y(e.algorithm,"PBKDF2"))throw $("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!Y(e.algorithm,"RSA-OAEP"))throw $("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw $("SHA-".concat(r),"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}if(r&&!e.usages.includes(r))throw TypeError("CryptoKey does not support this operation, its usages must include ".concat(r,"."))}function Q(e,t){for(var r,n=arguments.length,i=Array(n>2?n-2:0),o=2;o<n;o++)i[o-2]=arguments[o];if((i=i.filter(Boolean)).length>2){let t=i.pop();e+="one of type ".concat(i.join(", "),", or ").concat(t,".")}else 2===i.length?e+="one of type ".concat(i[0]," or ").concat(i[1],"."):e+="of type ".concat(i[0],".");return null==t?e+=" Received ".concat(t):"function"==typeof t&&t.name?e+=" Received function ".concat(t.name):"object"==typeof t&&null!=t&&(null==(r=t.constructor)?void 0:r.name)&&(e+=" Received an instance of ".concat(t.constructor.name)),e}let ee=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return Q("Key must be ",e,...r)};function et(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i];return Q("Key for the ".concat(e," algorithm must be "),t,...n)}function er(e){if(!en(e))throw Error("CryptoKey instance expected")}function en(e){return(null==e?void 0:e[Symbol.toStringTag])==="CryptoKey"}function ei(e){return(null==e?void 0:e[Symbol.toStringTag])==="KeyObject"}let eo=e=>en(e)||ei(e);async function ea(e,t,r,n,i){if(!(r instanceof Uint8Array))throw TypeError(ee(r,"Uint8Array"));let o=parseInt(e.slice(1,4),10),a=await crypto.subtle.importKey("raw",r.subarray(o>>3),"AES-CBC",!1,["encrypt"]),s=await crypto.subtle.importKey("raw",r.subarray(0,o>>3),{hash:"SHA-".concat(o<<1),name:"HMAC"},!1,["sign"]),c=new Uint8Array(await crypto.subtle.encrypt({iv:n,name:"AES-CBC"},a,t)),l=O(i,n,c,I(i.length<<3));return{ciphertext:c,tag:new Uint8Array((await crypto.subtle.sign("HMAC",s,l)).slice(0,o>>3)),iv:n}}async function es(e,t,r,n,i){let o;r instanceof Uint8Array?o=await crypto.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(Z(r,e,"encrypt"),o=r);let a=new Uint8Array(await crypto.subtle.encrypt({additionalData:i,iv:n,name:"AES-GCM",tagLength:128},o,t)),s=a.slice(-16);return{ciphertext:a.slice(0,-16),tag:s,iv:n}}let ec=async(e,t,r,n,i)=>{if(!en(r)&&!(r instanceof Uint8Array))throw TypeError(ee(r,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(n)V(e,n);else n=crypto.getRandomValues(new Uint8Array(G(e)>>3));switch(e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&X(r,parseInt(e.slice(-3),10)),ea(e,t,r,n,i);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&X(r,parseInt(e.slice(1,4),10)),es(e,t,r,n,i);default:throw new J("Unsupported JWE Content Encryption Algorithm")}};function el(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError("Invalid key size for alg: ".concat(t))}function eu(e,t,r){return e instanceof Uint8Array?crypto.subtle.importKey("raw",e,"AES-KW",!0,[r]):(Z(e,t,r),e)}async function ed(e,t,r){let n=await eu(t,e,"wrapKey");el(n,e);let i=await crypto.subtle.importKey("raw",r,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",i,n,"AES-KW"))}async function ep(e,t,r){let n=await eu(t,e,"unwrapKey");el(n,e);let i=await crypto.subtle.unwrapKey("raw",r,n,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",i))}let eh=async(e,t)=>{let r="SHA-".concat(e.slice(-3));return new Uint8Array(await crypto.subtle.digest(r,t))};function ef(e){return O(j(e.length),e)}async function em(e,t,r){let n=t>>3,i=Math.ceil(n/32),o=new Uint8Array(32*i);for(let t=1;t<=i;t++){let n=new Uint8Array(4+e.length+r.length);n.set(j(t),0),n.set(e,4),n.set(r,4+e.length);let i=await eh("sha256",n);o.set(i,(t-1)*32)}return o.slice(0,n)}async function ey(e,t,r,n){var i;let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:new Uint8Array(0),a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:new Uint8Array(0);Z(e,"ECDH"),Z(t,"ECDH","deriveBits");let s=ef(T.encode(r)),c=ef(o),l=ef(a),u=O(s,c,l,j(n),new Uint8Array(0));return em(new Uint8Array(await crypto.subtle.deriveBits({name:e.algorithm.name,public:e},t,"X25519"===(i=e).algorithm.name?256:Math.ceil(parseInt(i.algorithm.namedCurve.slice(-3),10)/8)<<3)),n,u)}function eg(e){switch(e.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===e.algorithm.name}}async function eb(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new B("PBES2 Salt Input must be 8 or more octets");let i=O(T.encode(t),new Uint8Array([0]),e),o=parseInt(t.slice(13,16),10),a={hash:"SHA-".concat(t.slice(8,11)),iterations:r,name:"PBKDF2",salt:i},s=await (n instanceof Uint8Array?crypto.subtle.importKey("raw",n,"PBKDF2",!1,["deriveBits"]):(Z(n,t,"deriveBits"),n));return new Uint8Array(await crypto.subtle.deriveBits(a,s,o))}async function ew(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:2048,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:crypto.getRandomValues(new Uint8Array(16)),o=await eb(i,e,n,t);return{encryptedKey:await ed(e.slice(-6),o,r),p2c:n,p2s:N(i)}}async function ev(e,t,r,n,i){let o=await eb(i,e,n,t);return ep(e.slice(-6),o,r)}let e_=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError("".concat(e," requires key modulusLength to be 2048 bits or larger"))}},eE=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new J("alg ".concat(e," is not supported either by JOSE or your javascript runtime"))}};async function ek(e,t,r){return Z(t,e,"encrypt"),e_(e,t),new Uint8Array(await crypto.subtle.encrypt(eE(e),t,r))}async function eA(e,t,r){return Z(t,e,"decrypt"),e_(e,t),new Uint8Array(await crypto.subtle.decrypt(eE(e),t,r))}let eS=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function ex(e){return eS(e)&&"string"==typeof e.kty}let eR=async e=>{var t,r;if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:n,keyUsages:i}=function(e){let t,r;switch(e.kty){case"AKP":switch(e.alg){case"ML-DSA-44":case"ML-DSA-65":case"ML-DSA-87":t={name:e.alg},r=e.priv?["sign"]:["verify"];break;default:throw new J('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:"SHA-".concat(e.alg.slice(-3))},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:"SHA-".concat(e.alg.slice(-3))},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:"SHA-".concat(parseInt(e.alg.slice(-3),10)||1)},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new J('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new J('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new J('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new J('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),o={...e};return"AKP"!==o.kty&&delete o.alg,delete o.use,crypto.subtle.importKey("jwk",o,n,null!=(t=e.ext)?t:!e.d&&!e.priv,null!=(r=e.key_ops)?r:i)},eP=async function(e,r,n){let i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];t||(t=new WeakMap);let o=t.get(e);if(null==o?void 0:o[n])return o[n];let a=await eR({...r,alg:n});return i&&Object.freeze(e),o?o[n]=a:t.set(e,{[n]:a}),a},eT=async(e,r)=>{if(e instanceof Uint8Array||en(e))return e;if(ei(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return((e,r)=>{let n;t||(t=new WeakMap);let i=t.get(e);if(null==i?void 0:i[r])return i[r];let o="public"===e.type,a=!!o;if("x25519"===e.asymmetricKeyType){switch(r){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}n=e.toCryptoKey(e.asymmetricKeyType,a,o?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==r&&"Ed25519"!==r)throw TypeError("given KeyObject instance cannot be used for this algorithm");n=e.toCryptoKey(e.asymmetricKeyType,a,[o?"verify":"sign"])}switch(e.asymmetricKeyType){case"ml-dsa-44":case"ml-dsa-65":case"ml-dsa-87":if(r!==e.asymmetricKeyType.toUpperCase())throw TypeError("given KeyObject instance cannot be used for this algorithm");n=e.toCryptoKey(e.asymmetricKeyType,a,[o?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let t;switch(r){case"RSA-OAEP":t="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":t="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":t="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":t="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(r.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:t},a,o?["encrypt"]:["decrypt"]);n=e.toCryptoKey({name:r.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:t},a,[o?"verify":"sign"])}if("ec"===e.asymmetricKeyType){var s;let t=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(null==(s=e.asymmetricKeyDetails)?void 0:s.namedCurve);if(!t)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===r&&"P-256"===t&&(n=e.toCryptoKey({name:"ECDSA",namedCurve:t},a,[o?"verify":"sign"])),"ES384"===r&&"P-384"===t&&(n=e.toCryptoKey({name:"ECDSA",namedCurve:t},a,[o?"verify":"sign"])),"ES512"===r&&"P-521"===t&&(n=e.toCryptoKey({name:"ECDSA",namedCurve:t},a,[o?"verify":"sign"])),r.startsWith("ECDH-ES")&&(n=e.toCryptoKey({name:"ECDH",namedCurve:t},a,o?[]:["deriveBits"]))}if(!n)throw TypeError("given KeyObject instance cannot be used for this algorithm");return i?i[r]=n:t.set(e,{[r]:n}),n})(e,r)}catch(e){if(e instanceof TypeError)throw e}let n=e.export({format:"jwk"});return eP(e,n,r)}if(ex(e))return e.k?D(e.k):eP(e,e,r,!0);throw Error("unreachable")};function eC(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new J("Unsupported JWE Algorithm: ".concat(e))}}let eO=e=>crypto.getRandomValues(new Uint8Array(eC(e)>>3));async function eU(e){if(ei(e))if("secret"!==e.type)return e.export({format:"jwk"});else e=e.export();if(e instanceof Uint8Array)return{kty:"oct",k:N(e)};if(!en(e))throw TypeError(ee(e,"CryptoKey","KeyObject","Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:i,...o}=await crypto.subtle.exportKey("jwk",e);return"AKP"===o.kty&&(o.alg=n),o}async function eI(e){return eU(e)}async function ej(e,t){if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let r={name:"HMAC",hash:"SHA-256"},n=await crypto.subtle.generateKey(r,!1,["sign"]),i=new Uint8Array(await crypto.subtle.sign(r,n,e)),o=new Uint8Array(await crypto.subtle.sign(r,n,t)),a=0,s=-1;for(;++s<32;)a|=i[s]^o[s];return 0===a}async function eD(e,t,r,n,i,o){let a,s;if(!(t instanceof Uint8Array))throw TypeError(ee(t,"Uint8Array"));let c=parseInt(e.slice(1,4),10),l=await crypto.subtle.importKey("raw",t.subarray(c>>3),"AES-CBC",!1,["decrypt"]),u=await crypto.subtle.importKey("raw",t.subarray(0,c>>3),{hash:"SHA-".concat(c<<1),name:"HMAC"},!1,["sign"]),d=O(o,n,r,I(o.length<<3)),p=new Uint8Array((await crypto.subtle.sign("HMAC",u,d)).slice(0,c>>3));try{a=await ej(i,p)}catch(e){}if(!a)throw new q;try{s=new Uint8Array(await crypto.subtle.decrypt({iv:n,name:"AES-CBC"},l,r))}catch(e){}if(!s)throw new q;return s}async function eN(e,t,r,n,i,o){let a;t instanceof Uint8Array?a=await crypto.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(Z(t,e,"decrypt"),a=t);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:o,iv:n,name:"AES-GCM",tagLength:128},a,O(r,i)))}catch(e){throw new q}}let eL=async(e,t,r,n,i,o)=>{if(!en(t)&&!(t instanceof Uint8Array))throw TypeError(ee(t,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!n)throw new B("JWE Initialization Vector missing");if(!i)throw new B("JWE Authentication Tag missing");switch(V(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&X(t,parseInt(e.slice(-3),10)),eD(e,t,r,n,i,o);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&X(t,parseInt(e.slice(1,4),10)),eN(e,t,r,n,i,o);default:throw new J("Unsupported JWE Content Encryption Algorithm")}};async function eH(e,t,r,n){let i=e.slice(0,7),o=await ec(i,r,t,n,new Uint8Array(0));return{encryptedKey:o.ciphertext,iv:N(o.iv),tag:N(o.tag)}}async function eW(e,t,r,n,i){return eL(e.slice(0,7),t,r,n,i,new Uint8Array(0))}let eM=async function(e,t,r,n){let i,o,a,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};switch(e){case"dir":a=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let c;if(er(r),!eg(r))throw new J("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:l,apv:u}=s;c=s.epk?await eT(s.epk,e):(await crypto.subtle.generateKey(r.algorithm,!0,["deriveBits"])).privateKey;let{x:d,y:p,crv:h,kty:f}=await eI(c),m=await ey(r,c,"ECDH-ES"===e?t:e,"ECDH-ES"===e?eC(t):parseInt(e.slice(-5,-2),10),l,u);if(o={epk:{x:d,crv:h,kty:f}},"EC"===f&&(o.epk.y=p),l&&(o.apu=N(l)),u&&(o.apv=N(u)),"ECDH-ES"===e){a=m;break}a=n||eO(t);let y=e.slice(-6);i=await ed(y,m,a);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":a=n||eO(t),er(r),i=await ek(e,r,a);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{a=n||eO(t);let{p2c:c,p2s:l}=s;({encryptedKey:i,...o}=await ew(e,r,a,c,l));break}case"A128KW":case"A192KW":case"A256KW":a=n||eO(t),i=await ed(e,r,a);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{a=n||eO(t);let{iv:c}=s;({encryptedKey:i,...o}=await eH(e,r,a,c));break}default:throw new J('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:a,encryptedKey:i,parameters:o}},eK=function(){let e;for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let i=r.filter(Boolean);if(0===i.length||1===i.length)return!0;for(let t of i){let r=Object.keys(t);if(!e||0===e.size){e=new Set(r);continue}for(let t of r){if(e.has(t))return!1;e.add(t)}}return!0},eJ=(e,t,r,n,i)=>{let o;if(void 0!==i.crit&&(null==n?void 0:n.crit)===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let a of(o=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!o.has(a))throw new J('Extension Header Parameter "'.concat(a,'" is not recognized'));if(void 0===i[a])throw new e('Extension Header Parameter "'.concat(a,'" is missing'));if(o.get(a)&&void 0===n[a])throw new e('Extension Header Parameter "'.concat(a,'" MUST be integrity protected'))}return new Set(n.crit)},eq=e=>null==e?void 0:e[Symbol.toStringTag],eB=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError('Invalid key for this operation, its "use" must be "'.concat(e,'" when present'))}if(void 0!==t.alg&&t.alg!==e)throw TypeError('Invalid key for this operation, its "alg" must be "'.concat(e,'" when present'));if(Array.isArray(t.key_ops)){var n,i;let o;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):o=r;break;case e.startsWith("PBES2"):o="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):o=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):o="wrapKey";break;case"decrypt"===r:o=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(o&&(null==(i=t.key_ops)||null==(n=i.includes)?void 0:n.call(i,o))===!1)throw TypeError('Invalid key for this operation, its "key_ops" must include "'.concat(o,'" when present'))}return!0},ez=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?((e,t,r)=>{if(!(t instanceof Uint8Array)){if(ex(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&eB(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!eo(t))throw TypeError(et(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError("".concat(eq(t),' instances for symmetric algorithms must be of type "secret"'))}})(e,t,r):((e,t,r)=>{if(ex(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&("AKP"===e.kty&&"string"==typeof e.priv||"string"==typeof e.d)}(t)&&eB(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d&&void 0===e.priv}(t)&&eB(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!eo(t))throw TypeError(et(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError("".concat(eq(t),' instances for asymmetric algorithms must not be of type "secret"'));if("public"===t.type)switch(r){case"sign":throw TypeError("".concat(eq(t),' instances for asymmetric algorithm signing must be of type "private"'));case"decrypt":throw TypeError("".concat(eq(t),' instances for asymmetric algorithm decryption must be of type "private"'))}if("private"===t.type)switch(r){case"verify":throw TypeError("".concat(eq(t),' instances for asymmetric algorithm verifying must be of type "public"'));case"encrypt":throw TypeError("".concat(eq(t),' instances for asymmetric algorithm encryption must be of type "public"'))}})(e,t,r)};var eF=new WeakMap,eG=new WeakMap,eV=new WeakMap,eX=new WeakMap,e$=new WeakMap,eY=new WeakMap,eZ=new WeakMap,eQ=new WeakMap;class e0{setKeyManagementParameters(e){if((0,x._)(this,eQ))throw TypeError("setKeyManagementParameters can only be called once");return(0,P._)(this,eQ,e),this}setProtectedHeader(e){if((0,x._)(this,eG))throw TypeError("setProtectedHeader can only be called once");return(0,P._)(this,eG,e),this}setSharedUnprotectedHeader(e){if((0,x._)(this,eV))throw TypeError("setSharedUnprotectedHeader can only be called once");return(0,P._)(this,eV,e),this}setUnprotectedHeader(e){if((0,x._)(this,eX))throw TypeError("setUnprotectedHeader can only be called once");return(0,P._)(this,eX,e),this}setAdditionalAuthenticatedData(e){return(0,P._)(this,e$,e),this}setContentEncryptionKey(e){if((0,x._)(this,eY))throw TypeError("setContentEncryptionKey can only be called once");return(0,P._)(this,eY,e),this}setInitializationVector(e){if((0,x._)(this,eZ))throw TypeError("setInitializationVector can only be called once");return(0,P._)(this,eZ,e),this}async encrypt(e,t){let r,n,i,o,a;if(!(0,x._)(this,eG)&&!(0,x._)(this,eX)&&!(0,x._)(this,eV))throw new B("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!eK((0,x._)(this,eG),(0,x._)(this,eX),(0,x._)(this,eV)))throw new B("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let s={...(0,x._)(this,eG),...(0,x._)(this,eX),...(0,x._)(this,eV)};if(eJ(B,new Map,null==t?void 0:t.crit,(0,x._)(this,eG),s),void 0!==s.zip)throw new J('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:l}=s;if("string"!=typeof c||!c)throw new B('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof l||!l)throw new B('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if((0,x._)(this,eY)&&("dir"===c||"ECDH-ES"===c))throw TypeError('setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header '.concat(c));ez("dir"===c?l:c,e,"encrypt");{let i,o=await eT(e,c);({cek:n,encryptedKey:r,parameters:i}=await eM(c,l,o,(0,x._)(this,eY),(0,x._)(this,eQ))),i&&(t&&L in t?(0,x._)(this,eX)?(0,P._)(this,eX,{...(0,x._)(this,eX),...i}):this.setUnprotectedHeader(i):(0,x._)(this,eG)?(0,P._)(this,eG,{...(0,x._)(this,eG),...i}):this.setProtectedHeader(i))}o=(0,x._)(this,eG)?T.encode(N(JSON.stringify((0,x._)(this,eG)))):T.encode(""),(0,x._)(this,e$)?(a=N((0,x._)(this,e$)),i=O(o,T.encode("."),T.encode(a))):i=o;let{ciphertext:u,tag:d,iv:p}=await ec(l,(0,x._)(this,eF),n,(0,x._)(this,eZ),i),h={ciphertext:N(u)};return p&&(h.iv=N(p)),d&&(h.tag=N(d)),r&&(h.encrypted_key=N(r)),a&&(h.aad=a),(0,x._)(this,eG)&&(h.protected=C.decode(o)),(0,x._)(this,eV)&&(h.unprotected=(0,x._)(this,eV)),(0,x._)(this,eX)&&(h.header=(0,x._)(this,eX)),h}constructor(e){if((0,R._)(this,eF,{writable:!0,value:void 0}),(0,R._)(this,eG,{writable:!0,value:void 0}),(0,R._)(this,eV,{writable:!0,value:void 0}),(0,R._)(this,eX,{writable:!0,value:void 0}),(0,R._)(this,e$,{writable:!0,value:void 0}),(0,R._)(this,eY,{writable:!0,value:void 0}),(0,R._)(this,eZ,{writable:!0,value:void 0}),(0,R._)(this,eQ,{writable:!0,value:void 0}),!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");(0,P._)(this,eF,e)}}var e1=new WeakMap;class e2{setContentEncryptionKey(e){return(0,x._)(this,e1).setContentEncryptionKey(e),this}setInitializationVector(e){return(0,x._)(this,e1).setInitializationVector(e),this}setProtectedHeader(e){return(0,x._)(this,e1).setProtectedHeader(e),this}setKeyManagementParameters(e){return(0,x._)(this,e1).setKeyManagementParameters(e),this}async encrypt(e,t){let r=await (0,x._)(this,e1).encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}constructor(e){(0,R._)(this,e1,{writable:!0,value:void 0}),(0,P._)(this,e1,new e0(e))}}let e3=e=>Math.floor(e.getTime()/1e3),e6=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,e5=e=>{let t,r=e6.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function e4(e,t){if(!Number.isFinite(t))throw TypeError("Invalid ".concat(e," input"));return t}let e8=e=>e.includes("/")?e.toLowerCase():"application/".concat(e.toLowerCase());var e9=new WeakMap;class e7{data(){return T.encode(JSON.stringify((0,x._)(this,e9)))}get iss(){return(0,x._)(this,e9).iss}set iss(e){(0,x._)(this,e9).iss=e}get sub(){return(0,x._)(this,e9).sub}set sub(e){(0,x._)(this,e9).sub=e}get aud(){return(0,x._)(this,e9).aud}set aud(e){(0,x._)(this,e9).aud=e}set jti(e){(0,x._)(this,e9).jti=e}set nbf(e){"number"==typeof e?(0,x._)(this,e9).nbf=e4("setNotBefore",e):e instanceof Date?(0,x._)(this,e9).nbf=e4("setNotBefore",e3(e)):(0,x._)(this,e9).nbf=e3(new Date)+e5(e)}set exp(e){"number"==typeof e?(0,x._)(this,e9).exp=e4("setExpirationTime",e):e instanceof Date?(0,x._)(this,e9).exp=e4("setExpirationTime",e3(e)):(0,x._)(this,e9).exp=e3(new Date)+e5(e)}set iat(e){void 0===e?(0,x._)(this,e9).iat=e3(new Date):e instanceof Date?(0,x._)(this,e9).iat=e4("setIssuedAt",e3(e)):"string"==typeof e?(0,x._)(this,e9).iat=e4("setIssuedAt",e3(new Date)+e5(e)):(0,x._)(this,e9).iat=e4("setIssuedAt",e)}constructor(e){if((0,R._)(this,e9,{writable:!0,value:void 0}),!eS(e))throw TypeError("JWT Claims Set MUST be an object");(0,P._)(this,e9,structuredClone(e))}}var te=new WeakMap,tt=new WeakMap,tr=new WeakMap,tn=new WeakMap,ti=new WeakMap,to=new WeakMap,ta=new WeakMap,ts=new WeakMap;class tc{setIssuer(e){return(0,x._)(this,ts).iss=e,this}setSubject(e){return(0,x._)(this,ts).sub=e,this}setAudience(e){return(0,x._)(this,ts).aud=e,this}setJti(e){return(0,x._)(this,ts).jti=e,this}setNotBefore(e){return(0,x._)(this,ts).nbf=e,this}setExpirationTime(e){return(0,x._)(this,ts).exp=e,this}setIssuedAt(e){return(0,x._)(this,ts).iat=e,this}setProtectedHeader(e){if((0,x._)(this,tn))throw TypeError("setProtectedHeader can only be called once");return(0,P._)(this,tn,e),this}setKeyManagementParameters(e){if((0,x._)(this,tr))throw TypeError("setKeyManagementParameters can only be called once");return(0,P._)(this,tr,e),this}setContentEncryptionKey(e){if((0,x._)(this,te))throw TypeError("setContentEncryptionKey can only be called once");return(0,P._)(this,te,e),this}setInitializationVector(e){if((0,x._)(this,tt))throw TypeError("setInitializationVector can only be called once");return(0,P._)(this,tt,e),this}replicateIssuerAsHeader(){return(0,P._)(this,ti,!0),this}replicateSubjectAsHeader(){return(0,P._)(this,to,!0),this}replicateAudienceAsHeader(){return(0,P._)(this,ta,!0),this}async encrypt(e,t){let r=new e2((0,x._)(this,ts).data());return(0,x._)(this,tn)&&((0,x._)(this,ti)||(0,x._)(this,to)||(0,x._)(this,ta))&&(0,P._)(this,tn,{...(0,x._)(this,tn),iss:(0,x._)(this,ti)?(0,x._)(this,ts).iss:void 0,sub:(0,x._)(this,to)?(0,x._)(this,ts).sub:void 0,aud:(0,x._)(this,ta)?(0,x._)(this,ts).aud:void 0}),r.setProtectedHeader((0,x._)(this,tn)),(0,x._)(this,tt)&&r.setInitializationVector((0,x._)(this,tt)),(0,x._)(this,te)&&r.setContentEncryptionKey((0,x._)(this,te)),(0,x._)(this,tr)&&r.setKeyManagementParameters((0,x._)(this,tr)),r.encrypt(e,t)}constructor(e={}){(0,R._)(this,te,{writable:!0,value:void 0}),(0,R._)(this,tt,{writable:!0,value:void 0}),(0,R._)(this,tr,{writable:!0,value:void 0}),(0,R._)(this,tn,{writable:!0,value:void 0}),(0,R._)(this,ti,{writable:!0,value:void 0}),(0,R._)(this,to,{writable:!0,value:void 0}),(0,R._)(this,ta,{writable:!0,value:void 0}),(0,R._)(this,ts,{writable:!0,value:void 0}),(0,P._)(this,ts,new e7(e))}}var tl=e.i(28706),tl=tl;let tu=(e,t)=>{if("string"!=typeof e||!e)throw new F("".concat(t," missing or invalid"))};async function td(e,t){let r,n;if(ex(e))r=e;else if(eo(e))r=await eI(e);else throw TypeError(ee(e,"CryptoKey","KeyObject","JSON Web Key"));if(null!=t||(t="sha256"),"sha256"!==t&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(r.kty){case"AKP":tu(r.alg,'"alg" (Algorithm) Parameter'),tu(r.pub,'"pub" (Public key) Parameter'),n={alg:r.alg,kty:r.kty,pub:r.pub};break;case"EC":tu(r.crv,'"crv" (Curve) Parameter'),tu(r.x,'"x" (X Coordinate) Parameter'),tu(r.y,'"y" (Y Coordinate) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x,y:r.y};break;case"OKP":tu(r.crv,'"crv" (Subtype of Key Pair) Parameter'),tu(r.x,'"x" (Public Key) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x};break;case"RSA":tu(r.e,'"e" (Exponent) Parameter'),tu(r.n,'"n" (Modulus) Parameter'),n={e:r.e,kty:r.kty,n:r.n};break;case"oct":tu(r.k,'"k" (Key Value) Parameter'),n={k:r.k,kty:r.kty};break;default:throw new J('"kty" (Key Type) Parameter missing or unsupported')}let i=T.encode(JSON.stringify(n));return N(await eh(t,i))}async function tp(e,t,r){var n;let i;if(!eS(e))throw TypeError("JWK must be an object");switch(null!=t||(t=e.alg),null!=i||(i=null!=(n=null==r?void 0:r.extractable)?n:e.ext),e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return D(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new J('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');return eR({...e,alg:t,ext:i});case"AKP":if("string"!=typeof e.alg||!e.alg)throw TypeError('missing "alg" (Algorithm) Parameter value');if(void 0!==t&&t!==e.alg)throw TypeError("JWK alg and alg option value mismatch");return eR({...e,ext:i});case"EC":case"OKP":return eR({...e,alg:t,ext:i});default:throw new J('Unsupported "kty" (Key Type) Parameter value')}}let th=async(e,t,r,n,i)=>{switch(e){case"dir":if(void 0!==r)throw new B("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new B("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i,o;if(!eS(n.epk))throw new B('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(er(t),!eg(t))throw new J("ECDH with the provided key is not allowed or not supported by your javascript runtime");let a=await tp(n.epk,e);if(er(a),void 0!==n.apu){if("string"!=typeof n.apu)throw new B('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{i=D(n.apu)}catch(e){throw new B("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new B('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{o=D(n.apv)}catch(e){throw new B("Failed to base64url decode the apv")}}let s=await ey(a,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?eC(n.enc):parseInt(e.slice(-5,-2),10),i,o);if("ECDH-ES"===e)return s;if(void 0===r)throw new B("JWE Encrypted Key missing");return ep(e.slice(-6),s,r)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new B("JWE Encrypted Key missing");return er(t),eA(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let o;if(void 0===r)throw new B("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new B('JOSE Header "p2c" (PBES2 Count) missing or invalid');let a=(null==i?void 0:i.maxPBES2Count)||1e4;if(n.p2c>a)throw new B('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new B('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{o=D(n.p2s)}catch(e){throw new B("Failed to base64url decode the p2s")}return ev(e,t,r,n.p2c,o)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new B("JWE Encrypted Key missing");return ep(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let i,o;if(void 0===r)throw new B("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new B('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new B('JOSE Header "tag" (Authentication Tag) missing or invalid');try{i=D(n.iv)}catch(e){throw new B("Failed to base64url decode the iv")}try{o=D(n.tag)}catch(e){throw new B("Failed to base64url decode the tag")}return eW(e,t,r,i,o)}default:throw new J('Invalid or unsupported "alg" (JWE Algorithm) header value')}},tf=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError('"'.concat(e,'" option must be an array of strings'));if(t)return new Set(t)};async function tm(e,t,r){var n;let i,o,a,s,c,l,u;if(!eS(e))throw new B("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new B("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new B("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new B("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new B("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new B("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new B("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new B("JWE AAD incorrect type");if(void 0!==e.header&&!eS(e.header))throw new B("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!eS(e.unprotected))throw new B("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=D(e.protected);i=JSON.parse(C.decode(t))}catch(e){throw new B("JWE Protected Header is invalid")}if(!eK(i,e.header,e.unprotected))throw new B("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let d={...i,...e.header,...e.unprotected};if(eJ(B,new Map,null==r?void 0:r.crit,i,d),void 0!==d.zip)throw new J('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:p,enc:h}=d;if("string"!=typeof p||!p)throw new B("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof h||!h)throw new B("missing JWE Encryption Algorithm (enc) in JWE Header");let f=r&&tf("keyManagementAlgorithms",r.keyManagementAlgorithms),m=r&&tf("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(f&&!f.has(p)||!f&&p.startsWith("PBES2"))throw new K('"alg" (Algorithm) Header Parameter value not allowed');if(m&&!m.has(h))throw new K('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{o=D(e.encrypted_key)}catch(e){throw new B("Failed to base64url decode the encrypted_key")}let y=!1;"function"==typeof t&&(t=await t(i,e),y=!0),ez("dir"===p?h:p,t,"decrypt");let g=await eT(t,p);try{a=await th(p,g,o,d,r)}catch(e){if(e instanceof TypeError||e instanceof B||e instanceof J)throw e;a=eO(h)}if(void 0!==e.iv)try{s=D(e.iv)}catch(e){throw new B("Failed to base64url decode the iv")}if(void 0!==e.tag)try{c=D(e.tag)}catch(e){throw new B("Failed to base64url decode the tag")}let b=T.encode(null!=(n=e.protected)?n:"");l=void 0!==e.aad?O(b,T.encode("."),T.encode(e.aad)):b;try{u=D(e.ciphertext)}catch(e){throw new B("Failed to base64url decode the ciphertext")}let w={plaintext:await eL(h,a,u,s,c,l)};if(void 0!==e.protected&&(w.protectedHeader=i),void 0!==e.aad)try{w.additionalAuthenticatedData=D(e.aad)}catch(e){throw new B("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(w.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(w.unprotectedHeader=e.header),y)?{...w,key:g}:w}async function ty(e,t,r){if(e instanceof Uint8Array&&(e=C.decode(e)),"string"!=typeof e)throw new B("Compact JWE must be a string or Uint8Array");let{0:n,1:i,2:o,3:a,4:s,length:c}=e.split(".");if(5!==c)throw new B("Invalid Compact JWE");let l=await tm({ciphertext:a,iv:o||void 0,protected:n,tag:s||void 0,encrypted_key:i||void 0},t,r),u={plaintext:l.plaintext,protectedHeader:l.protectedHeader};return"function"==typeof t?{...u,key:l.key}:u}async function tg(e,t,r){let n=await ty(e,t,r),i=function(e,t){var r,n;let i,o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{i=JSON.parse(C.decode(t))}catch(e){}if(!eS(i))throw new z("JWT Claims Set must be a top-level JSON object");let{typ:s}=a;if(s&&("string"!=typeof e.typ||e8(e.typ)!==e8(s)))throw new W('unexpected "typ" JWT header value',i,"typ","check_failed");let{requiredClaims:c=[],issuer:l,subject:u,audience:d,maxTokenAge:p}=a,h=[...c];for(let e of(void 0!==p&&h.push("iat"),void 0!==d&&h.push("aud"),void 0!==u&&h.push("sub"),void 0!==l&&h.push("iss"),new Set(h.reverse())))if(!(e in i))throw new W('missing required "'.concat(e,'" claim'),i,e,"missing");if(l&&!(Array.isArray(l)?l:[l]).includes(i.iss))throw new W('unexpected "iss" claim value',i,"iss","check_failed");if(u&&i.sub!==u)throw new W('unexpected "sub" claim value',i,"sub","check_failed");if(d&&(r=i.aud,n="string"==typeof d?[d]:d,"string"==typeof r?!n.includes(r):!(Array.isArray(r)&&n.some(Set.prototype.has.bind(new Set(r))))))throw new W('unexpected "aud" claim value',i,"aud","check_failed");switch(typeof a.clockTolerance){case"string":o=e5(a.clockTolerance);break;case"number":o=a.clockTolerance;break;case"undefined":o=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:f}=a,m=e3(f||new Date);if((void 0!==i.iat||p)&&"number"!=typeof i.iat)throw new W('"iat" claim must be a number',i,"iat","invalid");if(void 0!==i.nbf){if("number"!=typeof i.nbf)throw new W('"nbf" claim must be a number',i,"nbf","invalid");if(i.nbf>m+o)throw new W('"nbf" claim timestamp check failed',i,"nbf","check_failed")}if(void 0!==i.exp){if("number"!=typeof i.exp)throw new W('"exp" claim must be a number',i,"exp","invalid");if(i.exp<=m-o)throw new M('"exp" claim timestamp check failed',i,"exp","check_failed")}if(p){let e=m-i.iat;if(e-o>("number"==typeof p?p:e5(p)))throw new M('"iat" claim timestamp check failed (too far in the past)',i,"iat","check_failed");if(e<0-o)throw new W('"iat" claim timestamp check failed (it should be in the past)',i,"iat","check_failed")}return i}(n.protectedHeader,n.plaintext,r),{protectedHeader:o}=n;if(void 0!==o.iss&&o.iss!==i.iss)throw new W('replicated "iss" claim header parameter mismatch',i,"iss","mismatch");if(void 0!==o.sub&&o.sub!==i.sub)throw new W('replicated "sub" claim header parameter mismatch',i,"sub","mismatch");if(void 0!==o.aud&&JSON.stringify(o.aud)!==JSON.stringify(i.aud))throw new W('replicated "aud" claim header parameter mismatch',i,"aud","mismatch");let a={payload:i,protectedHeader:o};return"function"==typeof t?{...a,key:n.key}:a}e.s(["parse",()=>tA,"serialize",()=>tR],36281);let tb=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,tw=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,tv=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,t_=/^[\u0020-\u003A\u003D-\u007E]*$/,tE=Object.prototype.toString,tk=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function tA(e,t){let r=new tk,n=e.length;if(n<2)return r;let i=(null==t?void 0:t.decode)||tP,o=0;do{let t=e.indexOf("=",o);if(-1===t)break;let a=e.indexOf(";",o),s=-1===a?n:a;if(t>s){o=e.lastIndexOf(";",t-1)+1;continue}let c=tS(e,o,t),l=tx(e,t,c),u=e.slice(c,l);if(void 0===r[u]){let n=tS(e,t+1,s),o=tx(e,s,n),a=i(e.slice(n,o));r[u]=a}o=s+1}while(o<n)return r}function tS(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r)return r}function tx(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function tR(e,t,r){let n=(null==r?void 0:r.encode)||encodeURIComponent;if(!tb.test(e))throw TypeError("argument name is invalid: ".concat(e));let i=n(t);if(!tw.test(i))throw TypeError("argument val is invalid: ".concat(t));let o=e+"="+i;if(!r)return o;if(void 0!==r.maxAge){if(!Number.isInteger(r.maxAge))throw TypeError("option maxAge is invalid: ".concat(r.maxAge));o+="; Max-Age="+r.maxAge}if(r.domain){if(!tv.test(r.domain))throw TypeError("option domain is invalid: ".concat(r.domain));o+="; Domain="+r.domain}if(r.path){if(!t_.test(r.path))throw TypeError("option path is invalid: ".concat(r.path));o+="; Path="+r.path}if(r.expires){var a;if(a=r.expires,"[object Date]"!==tE.call(a)||!Number.isFinite(r.expires.valueOf()))throw TypeError("option expires is invalid: ".concat(r.expires));o+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(o+="; HttpOnly"),r.secure&&(o+="; Secure"),r.partitioned&&(o+="; Partitioned"),r.priority)switch("string"==typeof r.priority?r.priority.toLowerCase():void 0){case"low":o+="; Priority=Low";break;case"medium":o+="; Priority=Medium";break;case"high":o+="; Priority=High";break;default:throw TypeError("option priority is invalid: ".concat(r.priority))}if(r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":o+="; SameSite=Strict";break;case"lax":o+="; SameSite=Lax";break;case"none":o+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid: ".concat(r.sameSite))}return o}function tP(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}var tT=e.i(36281);let{parse:tC}=tT,tO="A256CBC-HS512";async function tU(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:i}=e,o=Array.isArray(r)?r:[r],a=await tj(tO,o[0],i),s=await td({kty:"oct",k:tl.encode(a)},"sha".concat(a.byteLength<<3));return await new tc(t).setProtectedHeader({alg:"dir",enc:tO,kid:s}).setIssuedAt().setExpirationTime((Date.now()/1e3|0)+n).setJti(crypto.randomUUID()).encrypt(a)}async function tI(e){let{token:t,secret:r,salt:n}=e,i=Array.isArray(r)?r:[r];if(!t)return null;let{payload:o}=await tg(t,async e=>{let{kid:t,enc:r}=e;for(let e of i){let i=await tj(r,e,n);if(void 0===t||t===await td({kty:"oct",k:tl.encode(i)},"sha".concat(i.byteLength<<3)))return i}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[tO,"A256GCM"]});return o}async function tj(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await S("sha256",t,r,"Auth.js Generated Encryption Key (".concat(r,")"),n)}async function tD(e){let{options:t,paramValue:r,cookieValue:n}=e,{url:i,callbacks:o}=t,a=i.origin;return r?a=await o.redirect({url:r,baseUrl:i.origin}):n&&(a=await o.redirect({url:n,baseUrl:i.origin})),{callbackUrl:a,callbackUrlCookie:a!==n?a:void 0}}let tN="\x1b[31m",tL="\x1b[0m",tH={error(e){let t=e instanceof f.AuthError?e.type:e.name;if(console.error("".concat(tN,"[auth][error]").concat(tL," ").concat(t,": ").concat(e.message)),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error("".concat(tN,"[auth][cause]").concat(tL,":"),t.stack),r&&console.error("".concat(tN,"[auth][details]").concat(tL,":"),JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){console.warn("".concat("\x1b[33m","[auth][warn][").concat(e,"]").concat(tL),"Read more: ".concat("https://warnings.authjs.dev"))},debug(e,t){console.log("".concat("\x1b[90m","[auth][debug]:").concat(tL," ").concat(e),JSON.stringify(t,null,2))}};function tW(e){var t,r,n;let i={...tH};return e.debug||(i.debug=()=>{}),(null==(t=e.logger)?void 0:t.error)&&(i.error=e.logger.error),(null==(r=e.logger)?void 0:r.warn)&&(i.warn=e.logger.warn),(null==(n=e.logger)?void 0:n.debug)&&(i.debug=e.logger.debug),null!=e.logger||(e.logger=i),i}let tM=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{parse:tK,serialize:tJ}=tT;async function tq(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return(null==t?void 0:t.includes("application/json"))?await e.json():(null==t?void 0:t.includes("application/x-www-form-urlencoded"))?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function tB(e,t){try{var r,n,i;if("GET"!==e.method&&"POST"!==e.method)throw new f.UnknownAction("Only GET and POST requests are supported");null!=t.basePath||(t.basePath="/auth");let o=new URL(e.url),{action:a,providerId:s}=function(e,t){let r=e.match(new RegExp("^".concat(t,"(.+)")));if(null===r)throw new f.UnknownAction("Cannot parse action at ".concat(e));let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new f.UnknownAction("Cannot parse action at ".concat(e));let[i,o]=n;if(!tM.includes(i)||o&&!["signin","callback","webauthn-options"].includes(i))throw new f.UnknownAction("Cannot parse action at ".concat(e));return{action:i,providerId:"undefined"==o?void 0:o}}(o.pathname,t.basePath);return{url:o,action:a,providerId:s,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await tq(e):void 0,cookies:null!=(n=tK(null!=(r=e.headers.get("cookie"))?r:""))?n:{},error:null!=(i=o.searchParams.get("error"))?i:void 0,query:Object.fromEntries(o.searchParams)}}catch(n){let r=tW(t);r.error(n),r.debug("request",e)}}function tz(e){var t,r;let n=new Headers(e.headers);null==(t=e.cookies)||t.forEach(e=>{let{name:t,value:r,options:i}=e,o=tJ(t,r,i);n.has("Set-Cookie")?n.append("Set-Cookie",o):n.set("Set-Cookie",o)});let i=e.body;"application/json"===n.get("content-type")?i=JSON.stringify(e.body):"application/x-www-form-urlencoded"===n.get("content-type")&&(i=new URLSearchParams(e.body).toString());let o=new Response(i,{headers:n,status:e.redirect?302:null!=(r=e.status)?r:200});return e.redirect&&o.headers.set("Location",e.redirect),o}async function tF(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function tG(e){return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,t)=>e+("0"+t.toString(16)).slice(-2),"")}async function tV(e){let{options:t,cookieValue:r,isPost:n,bodyValue:i}=e;if(r){let[e,o]=r.split("|");if(o===await tF("".concat(e).concat(t.secret)))return{csrfTokenVerified:n&&e===i,csrfToken:e}}let o=tG(32),a=await tF("".concat(o).concat(t.secret));return{cookie:"".concat(o,"|").concat(a),csrfToken:o}}function tX(e,t){if(!t)throw new f.MissingCSRF("CSRF token was missing during an action ".concat(e))}function t$(e){return null!==e&&"object"==typeof e}function tY(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];if(!r.length)return e;let i=r.shift();if(t$(e)&&t$(i))for(let t in i)t$(i[t])?(t$(e[t])||(e[t]=Array.isArray(i[t])?[]:{}),tY(e[t],i[t])):void 0!==i[t]&&(e[t]=i[t]);return tY(e,...r)}let tZ=Symbol("skip-csrf-check"),tQ=Symbol("return-type-raw"),t0=Symbol("custom-fetch"),t1=Symbol("conform-internal"),t2=e=>{var t,r,n,i;return t6({id:null!=(r=null!=(t=e.sub)?t:e.id)?r:crypto.randomUUID(),name:null!=(i=null!=(n=e.name)?n:e.nickname)?i:e.preferred_username,email:e.email,image:e.picture})},t3=e=>t6({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function t6(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function t5(e,t){var r;if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let n=new URL(null!=(r=null==e?void 0:e.url)?r:"https://authjs.dev");if((null==e?void 0:e.params)!=null)for(let[t,r]of Object.entries(e.params))"claims"===t&&(r=JSON.stringify(r)),n.searchParams.set(t,String(r));return{url:n,request:null==e?void 0:e.request,conform:null==e?void 0:e.conform,...(null==e?void 0:e.clientPrivateKey)?{clientPrivateKey:null==e?void 0:e.clientPrivateKey}:null}}let t4={signIn:()=>!0,redirect(e){let{url:t,baseUrl:r}=e;return t.startsWith("/")?"".concat(r).concat(t):new URL(t).origin===r?t:r},session(e){var t,r,n,i,o,a;let{session:s}=e;return{user:{name:null==(t=s.user)?void 0:t.name,email:null==(r=s.user)?void 0:r.email,image:null==(n=s.user)?void 0:n.image},expires:null!=(a=null==(o=s.expires)||null==(i=o.toISOString)?void 0:i.call(o))?a:s.expires}},jwt(e){let{token:t}=e;return t}};async function t8(e){var t,r,n,i,o,a;let{authOptions:s,providerId:c,action:l,url:u,cookies:d,callbackUrl:h,csrfToken:m,csrfDisabled:y,isPost:g}=e,b=tW(s),{providers:w,provider:v}=function(e){var t;let{providerId:r,config:n}=e,i=new URL(null!=(t=n.basePath)?t:"/auth",e.url.origin),o=n.providers.map(e=>{var t,r,o;let a="function"==typeof e?e():e,{options:s,...c}=a,l=null!=(t=null==s?void 0:s.id)?t:c.id,u=tY(c,s,{signinUrl:"".concat(i,"/signin/").concat(l),callbackUrl:"".concat(i,"/callback/").concat(l)});if("oauth"===a.type||"oidc"===a.type){null!=u.redirectProxyUrl||(u.redirectProxyUrl=null!=(o=null==s?void 0:s.redirectProxyUrl)?o:n.redirectProxyUrl);let e=function(e){var t,r,n,i;e.issuer&&(null!=e.wellKnown||(e.wellKnown="".concat(e.issuer,"/.well-known/openid-configuration")));let o=t5(e.authorization,e.issuer);!o||(null==(t=o.url)?void 0:t.searchParams.has("scope"))||o.url.searchParams.set("scope","openid profile email");let a=t5(e.token,e.issuer),s=t5(e.userinfo,e.issuer),c=null!=(r=e.checks)?r:["pkce"];return e.redirectProxyUrl&&(c.includes("state")||c.push("state"),e.redirectProxyUrl="".concat(e.redirectProxyUrl,"/callback/").concat(e.id)),{...e,authorization:o,token:a,checks:c,userinfo:s,profile:null!=(n=e.profile)?n:t2,account:null!=(i=e.account)?i:t3}}(u);return(null==(r=e.authorization)?void 0:r.url.searchParams.get("response_mode"))==="form_post"&&delete e.redirectProxyUrl,null!=e[t0]||(e[t0]=null==s?void 0:s[t0]),e}return u}),a=o.find(e=>{let{id:t}=e;return t===r});if(r&&!a){let e=o.map(e=>e.id).join(", ");throw Error('Provider with id "'.concat(r,'" not found. Available providers: [').concat(e,"]."))}return{providers:o,provider:a}}({url:u,providerId:c,config:s}),_=!1;if(((null==v?void 0:v.type)==="oauth"||(null==v?void 0:v.type)==="oidc")&&v.redirectProxyUrl)try{_=new URL(v.redirectProxyUrl).origin===u.origin}catch(e){throw TypeError("redirectProxyUrl must be a valid URL. Received: ".concat(v.redirectProxyUrl))}let E={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...s,url:u,action:l,provider:v,cookies:tY(p(null!=(r=s.useSecureCookies)?r:"https:"===u.protocol),s.cookies),providers:w,session:{strategy:s.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...s.session},jwt:{secret:s.secret,maxAge:null!=(n=null==(t=s.session)?void 0:t.maxAge)?n:2592e3,encode:tU,decode:tI,...s.jwt},events:(o=null!=(i=s.events)?i:{},a=b,Object.keys(o).reduce((e,t)=>(e[t]=async function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];try{let e=o[t];return await e(...r)}catch(e){a.error(new f.EventError(e))}},e),{})),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async function(){for(var r=arguments.length,i=Array(r),o=0;o<r;o++)i[o]=arguments[o];try{t.debug("adapter_".concat(n),{args:i});let r=e[n];return await r(...i)}catch(r){let e=new f.AdapterError(r);throw t.error(e),e}},r),{})}(s.adapter,b),callbacks:{...t4,...s.callbacks},logger:b,callbackUrl:u.origin,isOnRedirectProxy:_,experimental:{...s.experimental}},k=[];if(y)E.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await tV({options:E,cookieValue:null==d?void 0:d[E.cookies.csrfToken.name],isPost:g,bodyValue:m});E.csrfToken=e,E.csrfTokenVerified=r,t&&k.push({name:E.cookies.csrfToken.name,value:t,options:E.cookies.csrfToken.options})}let{callbackUrl:A,callbackUrlCookie:S}=await tD({options:E,cookieValue:null==d?void 0:d[E.cookies.callbackUrl.name],paramValue:h});return E.callbackUrl=A,S&&k.push({name:E.cookies.callbackUrl.name,value:S,options:E.cookies.callbackUrl.options}),{options:E,cookies:k}}var t9,t7,re,rt,rr,rn,ri,ro,ra,rs,rc,rl,ru,rd,rp,rh,rf={},rm=[],ry=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,rg=Array.isArray;function rb(e,t){for(var r in t)e[r]=t[r];return e}function rw(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function rv(e,t,r,n,i){var o={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==i?++ra:i,__i:-1,__u:0};return null==i&&null!=ro.vnode&&ro.vnode(o),o}function r_(e){return e.children}function rE(e,t){this.props=e,this.context=t}function rk(e,t){if(null==t)return e.__?rk(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?rk(e):null}function rA(e){(!e.__d&&(e.__d=!0)&&rs.push(e)&&!rS.__r++||rc!==ro.debounceRendering)&&((rc=ro.debounceRendering)||rl)(rS)}function rS(){var e,t,r,n,i,o,a,s;for(rs.sort(ru);e=rs.shift();)e.__d&&(t=rs.length,n=void 0,o=(i=(r=e).__v).__e,a=[],s=[],r.__P&&((n=rb({},i)).__v=i.__v+1,ro.vnode&&ro.vnode(n),rC(r.__P,n,i,r.__n,r.__P.namespaceURI,32&i.__u?[o]:null,a,null==o?rk(i):o,!!(32&i.__u),s),n.__v=i.__v,n.__.__k[n.__i]=n,function(e,t,r){t.__d=void 0;for(var n=0;n<r.length;n++)rO(r[n],r[++n],r[++n]);ro.__c&&ro.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){ro.__e(e,t.__v)}})}(a,n,s),n.__e!=o&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)),rs.length>t&&rs.sort(ru));rS.__r=0}function rx(e,t,r,n,i,o,a,s,c,l,u){var d,p,h,f,m,y=n&&n.__k||rm,g=t.length;for(r.__d=c,function(e,t,r){var n,i,o,a,s,c=t.length,l=r.length,u=l,d=0;for(e.__k=[],n=0;n<c;n++)null!=(i=t[n])&&"boolean"!=typeof i&&"function"!=typeof i?(a=n+d,(i=e.__k[n]="string"==typeof i||"number"==typeof i||"bigint"==typeof i||i.constructor==String?rv(null,i,null,null,null):rg(i)?rv(r_,{children:i},null,null,null):void 0===i.constructor&&i.__b>0?rv(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i).__=e,i.__b=e.__b+1,o=null,-1!==(s=i.__i=function(e,t,r,n){var i=e.key,o=e.type,a=r-1,s=r+1,c=t[r];if(null===c||c&&i==c.key&&o===c.type&&0==(131072&c.__u))return r;if(n>+(null!=c&&0==(131072&c.__u)))for(;a>=0||s<t.length;){if(a>=0){if((c=t[a])&&0==(131072&c.__u)&&i==c.key&&o===c.type)return a;a--}if(s<t.length){if((c=t[s])&&0==(131072&c.__u)&&i==c.key&&o===c.type)return s;s++}}return -1}(i,r,a,u))&&(u--,(o=r[s])&&(o.__u|=131072)),null==o||null===o.__v?(-1==s&&d--,"function"!=typeof i.type&&(i.__u|=65536)):s!==a&&(s==a-1?d--:s==a+1?d++:(s>a?d--:d++,i.__u|=65536))):i=e.__k[n]=null;if(u)for(n=0;n<l;n++)null!=(o=r[n])&&0==(131072&o.__u)&&(o.__e==e.__d&&(e.__d=rk(o)),function e(t,r,n){var i,o;if(ro.unmount&&ro.unmount(t),(i=t.ref)&&(i.current&&i.current!==t.__e||rO(i,null,r)),null!=(i=t.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){ro.__e(e,r)}i.base=i.__P=null}if(i=t.__k)for(o=0;o<i.length;o++)i[o]&&e(i[o],r,n||"function"!=typeof t.type);n||rw(t.__e),t.__c=t.__=t.__e=t.__d=void 0}(o,o))}(r,t,y),c=r.__d,d=0;d<g;d++)null!=(h=r.__k[d])&&(p=-1===h.__i?rf:y[h.__i]||rf,h.__i=d,rC(e,h,p,i,o,a,s,c,l,u),f=h.__e,h.ref&&p.ref!=h.ref&&(p.ref&&rO(p.ref,null,h),u.push(h.ref,h.__c||f,h)),null==m&&null!=f&&(m=f),65536&h.__u||p.__k===h.__k?c=function e(t,r,n){var i,o;if("function"==typeof t.type){for(i=t.__k,o=0;i&&o<i.length;o++)i[o]&&(i[o].__=t,r=e(i[o],r,n));return r}t.__e!=r&&(r&&t.type&&!n.contains(r)&&(r=rk(t)),n.insertBefore(t.__e,r||null),r=t.__e);do r=r&&r.nextSibling;while(null!=r&&8===r.nodeType)return r}(h,c,e):"function"==typeof h.type&&void 0!==h.__d?c=h.__d:f&&(c=f.nextSibling),h.__d=void 0,h.__u&=-196609);r.__d=c,r.__e=m}function rR(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||ry.test(t)?r:r+"px"}function rP(e,t,r,n,i){var o;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||rR(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||rR(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])o=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+o]=r,r?n?r.u=n.u:(r.u=rd,e.addEventListener(t,o?rh:rp,o)):e.removeEventListener(t,o?rh:rp,o);else{if("http://www.w3.org/2000/svg"==i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==r?"":r))}}function rT(e){return function(t){if(this.l){var r=this.l[t.type+e];if(null==t.t)t.t=rd++;else if(t.t<r.u)return;return r(ro.event?ro.event(t):t)}}}function rC(e,t,r,n,i,o,a,s,c,l){var u,d,p,h,f,m,y,g,b,w,v,_,E,k,A,S,x=t.type;if(void 0!==t.constructor)return null;128&r.__u&&(c=!!(32&r.__u),o=[s=t.__e=r.__e]),(u=ro.__b)&&u(t);e:if("function"==typeof x)try{if(g=t.props,b="prototype"in x&&x.prototype.render,w=(u=x.contextType)&&n[u.__c],v=u?w?w.props.value:u.__:n,r.__c?y=(d=t.__c=r.__c).__=d.__E:(b?t.__c=d=new x(g,v):(t.__c=d=new rE(g,v),d.constructor=x,d.render=rU),w&&w.sub(d),d.props=g,d.state||(d.state={}),d.context=v,d.__n=n,p=d.__d=!0,d.__h=[],d._sb=[]),b&&null==d.__s&&(d.__s=d.state),b&&null!=x.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=rb({},d.__s)),rb(d.__s,x.getDerivedStateFromProps(g,d.__s))),h=d.props,f=d.state,d.__v=t,p)b&&null==x.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),b&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(b&&null==x.getDerivedStateFromProps&&g!==h&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(g,v),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(g,d.__s,v)||t.__v===r.__v)){for(t.__v!==r.__v&&(d.props=g,d.state=d.__s,d.__d=!1),t.__e=r.__e,t.__k=r.__k,t.__k.some(function(e){e&&(e.__=t)}),_=0;_<d._sb.length;_++)d.__h.push(d._sb[_]);d._sb=[],d.__h.length&&a.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(g,d.__s,v),b&&null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(h,f,m)})}if(d.context=v,d.props=g,d.__P=e,d.__e=!1,E=ro.__r,k=0,b){for(d.state=d.__s,d.__d=!1,E&&E(t),u=d.render(d.props,d.state,d.context),A=0;A<d._sb.length;A++)d.__h.push(d._sb[A]);d._sb=[]}else do d.__d=!1,E&&E(t),u=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++k<25)d.state=d.__s,null!=d.getChildContext&&(n=rb(rb({},n),d.getChildContext())),b&&!p&&null!=d.getSnapshotBeforeUpdate&&(m=d.getSnapshotBeforeUpdate(h,f)),rx(e,rg(S=null!=u&&u.type===r_&&null==u.key?u.props.children:u)?S:[S],t,r,n,i,o,a,s,c,l),d.base=t.__e,t.__u&=-161,d.__h.length&&a.push(d),y&&(d.__E=d.__=null)}catch(e){if(t.__v=null,c||null!=o){for(t.__u|=c?160:128;s&&8===s.nodeType&&s.nextSibling;)s=s.nextSibling;o[o.indexOf(s)]=null,t.__e=s}else t.__e=r.__e,t.__k=r.__k;ro.__e(e,t,r)}else null==o&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,i,o,a,s,c){var l,u,d,p,h,f,m,y=r.props,g=t.props,b=t.type;if("svg"===b?i="http://www.w3.org/2000/svg":"math"===b?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),null!=o){for(l=0;l<o.length;l++)if((h=o[l])&&"setAttribute"in h==!!b&&(b?h.localName===b:3===h.nodeType)){e=h,o[l]=null;break}}if(null==e){if(null===b)return document.createTextNode(g);e=document.createElementNS(i,b,g.is&&g),s&&(ro.__m&&ro.__m(t,o),s=!1),o=null}if(null===b)y===g||s&&e.data===g||(e.data=g);else{if(o=o&&ri.call(e.childNodes),y=r.props||rf,!s&&null!=o)for(y={},l=0;l<e.attributes.length;l++)y[(h=e.attributes[l]).name]=h.value;for(l in y)if(h=y[l],"children"==l);else if("dangerouslySetInnerHTML"==l)d=h;else if(!(l in g)){if("value"==l&&"defaultValue"in g||"checked"==l&&"defaultChecked"in g)continue;rP(e,l,null,h,i)}for(l in g)h=g[l],"children"==l?p=h:"dangerouslySetInnerHTML"==l?u=h:"value"==l?f=h:"checked"==l?m=h:s&&"function"!=typeof h||y[l]===h||rP(e,l,h,y[l],i);if(u)s||d&&(u.__html===d.__html||u.__html===e.innerHTML)||(e.innerHTML=u.__html),t.__k=[];else if(d&&(e.innerHTML=""),rx(e,rg(p)?p:[p],t,r,n,"foreignObject"===b?"http://www.w3.org/1999/xhtml":i,o,a,o?o[0]:r.__k&&rk(r,0),s,c),null!=o)for(l=o.length;l--;)rw(o[l]);s||(l="value","progress"===b&&null==f?e.removeAttribute("value"):void 0===f||f===e[l]&&("progress"!==b||f)&&("option"!==b||f===y[l])||rP(e,l,f,y[l],i),l="checked",void 0!==m&&m!==e[l]&&rP(e,l,m,y[l],i))}return e}(r.__e,t,r,n,i,o,a,c,l);(u=ro.diffed)&&u(t)}function rO(e,t,r){try{if("function"==typeof e){var n="function"==typeof e.__u;n&&e.__u(),n&&null==t||(e.__u=e(t))}else e.current=t}catch(e){ro.__e(e,r)}}function rU(e,t,r){return this.constructor(e,r)}ri=rm.slice,ro={__e:function(e,t,r,n){for(var i,o,a;t=t.__;)if((i=t.__c)&&!i.__)try{if((o=i.constructor)&&null!=o.getDerivedStateFromError&&(i.setState(o.getDerivedStateFromError(e)),a=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,n||{}),a=i.__d),a)return i.__E=i}catch(t){e=t}throw e}},ra=0,rE.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=rb({},this.state),"function"==typeof e&&(e=e(rb({},r),this.props)),e&&rb(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),rA(this))},rE.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),rA(this))},rE.prototype.render=r_,rs=[],rl="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,ru=function(e,t){return e.__v.__b-t.__v.__b},rS.__r=0,rd=0,rp=rT(!1),rh=rT(!0);var rI=/[\s\n\\/='"\0<>]/,rj=/^(xlink|xmlns|xml)([A-Z])/,rD=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,rN=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,rL=new Set(["draggable","spellcheck"]),rH=/["&<]/;function rW(e){if(0===e.length||!1===rH.test(e))return e;for(var t=0,r=0,n="",i="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:i="&quot;";break;case 38:i="&amp;";break;case 60:i="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=i,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var rM={},rK=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),rJ=/[A-Z]/g;function rq(){this.__d=!0}function rB(e,t,r){if(!e.s){if(r instanceof rX){if(!r.s)return void(r.o=rB.bind(null,e,t));1&t&&(t=r.s),r=r.v}if(r&&r.then)return void r.then(rB.bind(null,e,t),rB.bind(null,e,2));e.s=t,e.v=r;let n=e.o;n&&n(e)}}var rz,rF,rG,rV,rX=function(){function e(){}return e.prototype.then=function(t,r){var n=new e,i=this.s;if(i){var o=1&i?t:r;if(o){try{rB(n,1,o(this.v))}catch(e){rB(n,2,e)}return n}return this}return this.o=function(e){try{var i=e.v;1&e.s?rB(n,1,t?t(i):i):r?rB(n,1,r(i)):rB(n,2,i)}catch(e){rB(n,2,e)}},n},e}(),r$={},rY=[],rZ=Array.isArray,rQ=Object.assign;function r0(e,t){var r,n=e.type,i=!0;return e.__c?(i=!1,(r=e.__c).state=r.__s):r=new n(e.props,t),e.__c=r,r.__v=e,r.props=e.props,r.context=t,r.__d=!0,null==r.state&&(r.state=r$),null==r.__s&&(r.__s=r.state),n.getDerivedStateFromProps?r.state=rQ({},r.state,n.getDerivedStateFromProps(r.props,r.state)):i&&r.componentWillMount?(r.componentWillMount(),r.state=r.__s!==r.state?r.__s:r.state):!i&&r.componentWillUpdate&&r.componentWillUpdate(),rG&&rG(e),r.render(r.props,r.state,t)}var r1=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),r2=0;function r3(e,t,r,n,i,o){t||(t={});var a,s,c=t;"ref"in t&&(a=t.ref,delete t.ref);var l={type:e,props:c,key:r,ref:a,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--r2,__i:-1,__u:0,__source:i,__self:o};if("function"==typeof e&&(a=e.defaultProps))for(s in a)void 0===c[s]&&(c[s]=a[s]);return ro.vnode&&ro.vnode(l),l}async function r6(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL("".concat(e,"/webauthn-options/").concat(t));r&&n.searchParams.append("action",r),o().forEach(e=>{n.searchParams.append(e.name,e.value)});let i=await fetch(n);return i.ok?i.json():void console.error("Failed to fetch options",i)}function i(){let e="#".concat(t,"-form"),r=document.querySelector(e);if(!r)throw Error("Form '".concat(e,"' not found"));return r}function o(){return Array.from(i().querySelectorAll("input[data-form-field]"))}async function a(e,t){let r=i();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let n=await r.startAuthentication(e,t);return await a("authenticate",n)}async function c(e){o().forEach(e=>{if(e.required&&!e.value)throw Error("Missing required field: ".concat(e.name))});let t=await r.startRegistration(e);return await a("register",t)}async function l(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e)return void console.error("Failed to fetch option for autofill authentication");try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=i();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t)return void console.error("Failed to fetch options for form submission");if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),l()}let r5={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."};function r4(e){var t;let{html:r,title:n,status:i,cookies:o,theme:a,headTags:s}=e;return{cookies:o,status:i,headers:{"Content-Type":"text/html"},body:'<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>'.concat(':root {\n  --border-width: 1px;\n  --border-radius: 0.5rem;\n  --color-error: #c94b4b;\n  --color-info: #157efb;\n  --color-info-hover: #0f6ddb;\n  --color-info-text: #fff;\n}\n\n.__next-auth-theme-auto,\n.__next-auth-theme-light {\n  --color-background: #ececec;\n  --color-background-hover: rgba(236, 236, 236, 0.8);\n  --color-background-card: #fff;\n  --color-text: #000;\n  --color-primary: #444;\n  --color-control-border: #bbb;\n  --color-button-active-background: #f9f9f9;\n  --color-button-active-border: #aaa;\n  --color-separator: #ccc;\n  --provider-bg: #fff;\n  --provider-bg-hover: color-mix(\n    in srgb,\n    var(--provider-brand-color) 30%,\n    #fff\n  );\n}\n\n.__next-auth-theme-dark {\n  --color-background: #161b22;\n  --color-background-hover: rgba(22, 27, 34, 0.8);\n  --color-background-card: #0d1117;\n  --color-text: #fff;\n  --color-primary: #ccc;\n  --color-control-border: #555;\n  --color-button-active-background: #060606;\n  --color-button-active-border: #666;\n  --color-separator: #444;\n  --provider-bg: #161b22;\n  --provider-bg-hover: color-mix(\n    in srgb,\n    var(--provider-brand-color) 30%,\n    #000\n  );\n}\n\n.__next-auth-theme-dark img[src$="42-school.svg"],\n  .__next-auth-theme-dark img[src$="apple.svg"],\n  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],\n  .__next-auth-theme-dark img[src$="eveonline.svg"],\n  .__next-auth-theme-dark img[src$="github.svg"],\n  .__next-auth-theme-dark img[src$="mailchimp.svg"],\n  .__next-auth-theme-dark img[src$="medium.svg"],\n  .__next-auth-theme-dark img[src$="okta.svg"],\n  .__next-auth-theme-dark img[src$="patreon.svg"],\n  .__next-auth-theme-dark img[src$="ping-id.svg"],\n  .__next-auth-theme-dark img[src$="roblox.svg"],\n  .__next-auth-theme-dark img[src$="threads.svg"],\n  .__next-auth-theme-dark img[src$="wikimedia.svg"] {\n    filter: invert(1);\n  }\n\n.__next-auth-theme-dark #submitButton {\n    background-color: var(--provider-bg, var(--color-info));\n  }\n\n@media (prefers-color-scheme: dark) {\n  .__next-auth-theme-auto {\n    --color-background: #161b22;\n    --color-background-hover: rgba(22, 27, 34, 0.8);\n    --color-background-card: #0d1117;\n    --color-text: #fff;\n    --color-primary: #ccc;\n    --color-control-border: #555;\n    --color-button-active-background: #060606;\n    --color-button-active-border: #666;\n    --color-separator: #444;\n    --provider-bg: #161b22;\n    --provider-bg-hover: color-mix(\n      in srgb,\n      var(--provider-brand-color) 30%,\n      #000\n    );\n  }\n    .__next-auth-theme-auto img[src$="42-school.svg"],\n    .__next-auth-theme-auto img[src$="apple.svg"],\n    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],\n    .__next-auth-theme-auto img[src$="eveonline.svg"],\n    .__next-auth-theme-auto img[src$="github.svg"],\n    .__next-auth-theme-auto img[src$="mailchimp.svg"],\n    .__next-auth-theme-auto img[src$="medium.svg"],\n    .__next-auth-theme-auto img[src$="okta.svg"],\n    .__next-auth-theme-auto img[src$="patreon.svg"],\n    .__next-auth-theme-auto img[src$="ping-id.svg"],\n    .__next-auth-theme-auto img[src$="roblox.svg"],\n    .__next-auth-theme-auto img[src$="threads.svg"],\n    .__next-auth-theme-auto img[src$="wikimedia.svg"] {\n      filter: invert(1);\n    }\n    .__next-auth-theme-auto #submitButton {\n      background-color: var(--provider-bg, var(--color-info));\n    }\n}\n\nhtml {\n  box-sizing: border-box;\n}\n\n*,\n*:before,\n*:after {\n  box-sizing: inherit;\n  margin: 0;\n  padding: 0;\n}\n\nbody {\n  background-color: var(--color-background);\n  margin: 0;\n  padding: 0;\n  font-family:\n    ui-sans-serif,\n    system-ui,\n    -apple-system,\n    BlinkMacSystemFont,\n    "Segoe UI",\n    Roboto,\n    "Helvetica Neue",\n    Arial,\n    "Noto Sans",\n    sans-serif,\n    "Apple Color Emoji",\n    "Segoe UI Emoji",\n    "Segoe UI Symbol",\n    "Noto Color Emoji";\n}\n\nh1 {\n  margin-bottom: 1.5rem;\n  padding: 0 1rem;\n  font-weight: 400;\n  color: var(--color-text);\n}\n\np {\n  margin-bottom: 1.5rem;\n  padding: 0 1rem;\n  color: var(--color-text);\n}\n\nform {\n  margin: 0;\n  padding: 0;\n}\n\nlabel {\n  font-weight: 500;\n  text-align: left;\n  margin-bottom: 0.25rem;\n  display: block;\n  color: var(--color-text);\n}\n\ninput[type] {\n  box-sizing: border-box;\n  display: block;\n  width: 100%;\n  padding: 0.5rem 1rem;\n  border: var(--border-width) solid var(--color-control-border);\n  background: var(--color-background-card);\n  font-size: 1rem;\n  border-radius: var(--border-radius);\n  color: var(--color-text);\n}\n\np {\n  font-size: 1.1rem;\n  line-height: 2rem;\n}\n\na.button {\n  text-decoration: none;\n  line-height: 1rem;\n}\n\na.button:link,\n  a.button:visited {\n    background-color: var(--color-background);\n    color: var(--color-primary);\n  }\n\nbutton,\na.button {\n  padding: 0.75rem 1rem;\n  color: var(--provider-color, var(--color-primary));\n  background-color: var(--provider-bg, var(--color-background));\n  border: 1px solid #00000031;\n  font-size: 0.9rem;\n  height: 50px;\n  border-radius: var(--border-radius);\n  transition: background-color 250ms ease-in-out;\n  font-weight: 300;\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n:is(button,a.button):hover {\n    background-color: var(--provider-bg-hover, var(--color-background-hover));\n    cursor: pointer;\n  }\n\n:is(button,a.button):active {\n    cursor: pointer;\n  }\n\n:is(button,a.button) span {\n    color: var(--provider-bg);\n  }\n\n#submitButton {\n  color: var(--button-text-color, var(--color-info-text));\n  background-color: var(--brand-color, var(--color-info));\n  width: 100%;\n}\n\n#submitButton:hover {\n    background-color: var(\n      --button-hover-bg,\n      var(--color-info-hover)\n    ) !important;\n  }\n\na.site {\n  color: var(--color-primary);\n  text-decoration: none;\n  font-size: 1rem;\n  line-height: 2rem;\n}\n\na.site:hover {\n    text-decoration: underline;\n  }\n\n.page {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  display: grid;\n  place-items: center;\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n.page > div {\n    text-align: center;\n  }\n\n.error a.button {\n    padding-left: 2rem;\n    padding-right: 2rem;\n    margin-top: 0.5rem;\n  }\n\n.error .message {\n    margin-bottom: 1.5rem;\n  }\n\n.signin input[type="text"] {\n    margin-left: auto;\n    margin-right: auto;\n    display: block;\n  }\n\n.signin hr {\n    display: block;\n    border: 0;\n    border-top: 1px solid var(--color-separator);\n    margin: 2rem auto 1rem auto;\n    overflow: visible;\n  }\n\n.signin hr::before {\n      content: "or";\n      background: var(--color-background-card);\n      color: #888;\n      padding: 0 0.4rem;\n      position: relative;\n      top: -0.7rem;\n    }\n\n.signin .error {\n    background: #f5f5f5;\n    font-weight: 500;\n    border-radius: 0.3rem;\n    background: var(--color-error);\n  }\n\n.signin .error p {\n      text-align: left;\n      padding: 0.5rem 1rem;\n      font-size: 0.9rem;\n      line-height: 1.2rem;\n      color: var(--color-info-text);\n    }\n\n.signin > div,\n  .signin form {\n    display: block;\n  }\n\n.signin > div input[type], .signin form input[type] {\n      margin-bottom: 0.5rem;\n    }\n\n.signin > div button, .signin form button {\n      width: 100%;\n    }\n\n.signin .provider + .provider {\n    margin-top: 1rem;\n  }\n\n.logo {\n  display: inline-block;\n  max-width: 150px;\n  margin: 1.25rem 0;\n  max-height: 70px;\n}\n\n.card {\n  background-color: var(--color-background-card);\n  border-radius: 1rem;\n  padding: 1.25rem 2rem;\n}\n\n.card .header {\n    color: var(--color-primary);\n  }\n\n.card input[type]::-moz-placeholder {\n    color: color-mix(\n      in srgb,\n      var(--color-text) 20%,\n      var(--color-button-active-background)\n    );\n  }\n\n.card input[type]::placeholder {\n    color: color-mix(\n      in srgb,\n      var(--color-text) 20%,\n      var(--color-button-active-background)\n    );\n  }\n\n.card input[type] {\n    background: color-mix(in srgb, var(--color-background-card) 95%, black);\n  }\n\n.section-header {\n  color: var(--color-text);\n}\n\n@media screen and (min-width: 450px) {\n  .card {\n    margin: 2rem 0;\n    width: 368px;\n  }\n}\n\n@media screen and (max-width: 450px) {\n  .card {\n    margin: 1rem 0;\n    width: 343px;\n  }\n}\n',"</style><title>").concat(n,"</title>").concat(null!=s?s:"",'</head><body class="__next-auth-theme-').concat(null!=(t=null==a?void 0:a.colorScheme)?t:"auto",'"><div class="page">').concat(function(e,t,r){var n=ro.__s;ro.__s=!0,rz=ro.__b,rF=ro.diffed,rG=ro.__r,rV=ro.unmount;var i=function(e,t,r){var n,i,o,a={};for(o in t)"key"==o?n=t[o]:"ref"==o?i=t[o]:a[o]=t[o];if(arguments.length>2&&(a.children=arguments.length>3?ri.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(o in e.defaultProps)void 0===a[o]&&(a[o]=e.defaultProps[o]);return rv(e,a,n,i,null)}(r_,null);i.__k=[e];try{var o=function e(t,r,n,i,o,a,s){if(null==t||!0===t||!1===t||""===t)return"";var c=typeof t;if("object"!=c)return"function"==c?"":"string"==c?rW(t):t+"";if(rZ(t)){var l,u="";o.__k=t;for(var d=0;d<t.length;d++){var p=t[d];if(null!=p&&"boolean"!=typeof p){var h,f=e(p,r,n,i,o,a,s);"string"==typeof f?u+=f:(l||(l=[]),u&&l.push(u),u="",rZ(f)?(h=l).push.apply(h,f):l.push(f))}}return l?(u&&l.push(u),l):u}if(void 0!==t.constructor)return"";t.__=o,rz&&rz(t);var m=t.type,y=t.props;if("function"==typeof m){var g,b,w,v=r;if(m===r_){if("tpl"in y){for(var _="",E=0;E<y.tpl.length;E++)if(_+=y.tpl[E],y.exprs&&E<y.exprs.length){var k=y.exprs[E];if(null==k)continue;"object"==typeof k&&(void 0===k.constructor||rZ(k))?_+=e(k,r,n,i,t,a,s):_+=k}return _}if("UNSTABLE_comment"in y)return"<!--"+rW(y.UNSTABLE_comment)+"-->";b=y.children}else{if(null!=(g=m.contextType)){var A=r[g.__c];v=A?A.props.value:g.__}var S=m.prototype&&"function"==typeof m.prototype.render;if(S)b=r0(t,v),w=t.__c;else{t.__c=w={__v:t,context:v,props:t.props,setState:rq,forceUpdate:rq,__d:!0,__h:[]};for(var x=0;w.__d&&x++<25;)w.__d=!1,rG&&rG(t),b=m.call(w,y,v);w.__d=!0}if(null!=w.getChildContext&&(r=rQ({},r,w.getChildContext())),S&&ro.errorBoundaries&&(m.getDerivedStateFromError||w.componentDidCatch)){b=null!=b&&b.type===r_&&null==b.key&&null==b.props.tpl?b.props.children:b;try{return e(b,r,n,i,t,a,s)}catch(o){return m.getDerivedStateFromError&&(w.__s=m.getDerivedStateFromError(o)),w.componentDidCatch&&w.componentDidCatch(o,r$),w.__d?(b=r0(t,r),null!=(w=t.__c).getChildContext&&(r=rQ({},r,w.getChildContext())),e(b=null!=b&&b.type===r_&&null==b.key&&null==b.props.tpl?b.props.children:b,r,n,i,t,a,s)):""}finally{rF&&rF(t),t.__=null,rV&&rV(t)}}}b=null!=b&&b.type===r_&&null==b.key&&null==b.props.tpl?b.props.children:b;try{var R=e(b,r,n,i,t,a,s);return rF&&rF(t),t.__=null,ro.unmount&&ro.unmount(t),R}catch(o){if(!a&&s&&s.onError){var P=s.onError(o,t,function(o){return e(o,r,n,i,t,a,s)});if(void 0!==P)return P;var T=ro.__e;return T&&T(o,t),""}if(!a||!o||"function"!=typeof o.then)throw o;return o.then(function o(){try{return e(b,r,n,i,t,a,s)}catch(c){if(!c||"function"!=typeof c.then)throw c;return c.then(function(){return e(b,r,n,i,t,a,s)},o)}})}}var C,O="<"+m,U="";for(var I in y){var j=y[I];if("function"!=typeof j||"class"===I||"className"===I){switch(I){case"children":C=j;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in y)continue;I="for";break;case"className":if("class"in y)continue;I="class";break;case"defaultChecked":I="checked";break;case"defaultSelected":I="selected";break;case"defaultValue":case"value":switch(I="value",m){case"textarea":C=j;continue;case"select":i=j;continue;case"option":i!=j||"selected"in y||(O+=" selected")}break;case"dangerouslySetInnerHTML":U=j&&j.__html;continue;case"style":"object"==typeof j&&(j=function(e){var t="";for(var r in e){var n=e[r];if(null!=n&&""!==n){var i="-"==r[0]?r:rM[r]||(rM[r]=r.replace(rJ,"-$&").toLowerCase()),o=";";"number"!=typeof n||i.startsWith("--")||rK.has(i)||(o="px;"),t=t+i+":"+n+o}}return t||void 0}(j));break;case"acceptCharset":I="accept-charset";break;case"httpEquiv":I="http-equiv";break;default:if(rj.test(I))I=I.replace(rj,"$1:$2").toLowerCase();else{if(rI.test(I))continue;("-"===I[4]||rL.has(I))&&null!=j?j+="":n?rN.test(I)&&(I="panose1"===I?"panose-1":I.replace(/([A-Z])/g,"-$1").toLowerCase()):rD.test(I)&&(I=I.toLowerCase())}}null!=j&&!1!==j&&(O=!0===j||""===j?O+" "+I:O+" "+I+'="'+("string"==typeof j?rW(j):j+"")+'"')}}if(rI.test(m))throw Error(m+" is not a valid HTML tag name in "+O+">");if(U||("string"==typeof C?U=rW(C):null!=C&&!1!==C&&!0!==C&&(U=e(C,r,"svg"===m||"foreignObject"!==m&&n,i,t,a,s))),rF&&rF(t),t.__=null,rV&&rV(t),!U&&r1.has(m))return O+"/>";var D="</"+m+">",N=O+">";return rZ(U)?[N].concat(U,[D]):"string"!=typeof U?[N,U,D]:N+U+D}(e,r$,!1,void 0,i,!1,void 0);return rZ(o)?o.join(""):o}catch(e){if(e.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw e}finally{ro.__c&&ro.__c(e,rY),ro.__s=n,rY.length=0}}(r),"</div></body></html>")}}function r8(e){let{url:t,theme:r,query:n,cookies:i,pages:o,providers:a}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,t)=>{let{id:r,name:n,type:i,signinUrl:o,callbackUrl:a}=t;return e[r]={id:r,name:n,type:i,signinUrl:o,callbackUrl:a},e},{})}),signin(t,s){var c,l;if(t)throw new f.UnknownAction("Unsupported action");if(null==o?void 0:o.signIn){let t="".concat(o.signIn).concat(o.signIn.includes("?")?"&":"?").concat(new URLSearchParams({callbackUrl:null!=(l=e.callbackUrl)?l:"/"}));return s&&(t="".concat(t,"&").concat(new URLSearchParams({error:s}))),{redirect:t,cookies:i}}let u=null==a?void 0:a.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),d="";if(u){let{simpleWebAuthnBrowserVersion:e}=u;d='<script src="https://unpkg.com/@simplewebauthn/browser@'.concat(e,'/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>')}return r4({cookies:i,theme:r,html:function(e){var t,r;let{csrfToken:n,providers:i=[],callbackUrl:o,theme:a,email:s,error:c}=e;"undefined"!=typeof document&&(null==a?void 0:a.brandColor)&&document.documentElement.style.setProperty("--brand-color",a.brandColor),"undefined"!=typeof document&&(null==a?void 0:a.buttonText)&&document.documentElement.style.setProperty("--button-text-color",a.buttonText);let l=c&&(null!=(r=r5[c])?r:r5.default),u=null==(t=i.find(e=>"webauthn"===e.type&&e.enableConditionalUI))?void 0:t.id;return r3("div",{className:"signin",children:[(null==a?void 0:a.brandColor)&&r3("style",{dangerouslySetInnerHTML:{__html:":root {--brand-color: ".concat(a.brandColor,"}")}}),(null==a?void 0:a.buttonText)&&r3("style",{dangerouslySetInnerHTML:{__html:"\n        :root {\n          --button-text-color: ".concat(a.buttonText,"\n        }\n      ")}}),r3("div",{className:"card",children:[l&&r3("div",{className:"error",children:r3("p",{children:l})}),(null==a?void 0:a.logo)&&r3("img",{src:a.logo,alt:"Logo",className:"logo"}),i.map((e,t)=>{var r,a;let c,l,u;("oauth"===e.type||"oidc"===e.type)&&({bg:c="#fff",brandColor:l,logo:u="".concat("https://authjs.dev/img/providers","/").concat(e.id,".svg")}=null!=(r=e.style)?r:{});let d=null!=(a=null!=l?l:c)?a:"#fff";return r3("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?r3("form",{action:e.signinUrl,method:"POST",children:[r3("input",{type:"hidden",name:"csrfToken",value:n}),o&&r3("input",{type:"hidden",name:"callbackUrl",value:o}),r3("button",{type:"submit",className:"button",style:{"--provider-brand-color":d},tabIndex:0,children:[r3("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),u&&r3("img",{loading:"lazy",height:24,src:u})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&t>0&&"email"!==i[t-1].type&&"credentials"!==i[t-1].type&&"webauthn"!==i[t-1].type&&r3("hr",{}),"email"===e.type&&r3("form",{action:e.signinUrl,method:"POST",children:[r3("input",{type:"hidden",name:"csrfToken",value:n}),r3("label",{className:"section-header",htmlFor:"input-email-for-".concat(e.id,"-provider"),children:"Email"}),r3("input",{id:"input-email-for-".concat(e.id,"-provider"),autoFocus:!0,type:"email",name:"email",value:s,placeholder:"<EMAIL>",required:!0}),r3("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&r3("form",{action:e.callbackUrl,method:"POST",children:[r3("input",{type:"hidden",name:"csrfToken",value:n}),Object.keys(e.credentials).map(t=>{var r,n,i;return r3("div",{children:[r3("label",{className:"section-header",htmlFor:"input-".concat(t,"-for-").concat(e.id,"-provider"),children:null!=(r=e.credentials[t].label)?r:t}),r3("input",{name:t,id:"input-".concat(t,"-for-").concat(e.id,"-provider"),type:null!=(n=e.credentials[t].type)?n:"text",placeholder:null!=(i=e.credentials[t].placeholder)?i:"",...e.credentials[t]})]},"input-group-".concat(e.id))}),r3("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&r3("form",{action:e.callbackUrl,method:"POST",id:"".concat(e.id,"-form"),children:[r3("input",{type:"hidden",name:"csrfToken",value:n}),Object.keys(e.formFields).map(t=>{var r,n,i;return r3("div",{children:[r3("label",{className:"section-header",htmlFor:"input-".concat(t,"-for-").concat(e.id,"-provider"),children:null!=(r=e.formFields[t].label)?r:t}),r3("input",{name:t,"data-form-field":!0,id:"input-".concat(t,"-for-").concat(e.id,"-provider"),type:null!=(n=e.formFields[t].type)?n:"text",placeholder:null!=(i=e.formFields[t].placeholder)?i:"",...e.formFields[t]})]},"input-group-".concat(e.id))}),r3("button",{id:"submitButton-".concat(e.id),type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&t+1<i.length&&r3("hr",{})]},e.id)})]}),u&&r3(r_,{children:r3("script",{dangerouslySetInnerHTML:{__html:"\nconst currentURL = window.location.href;\nconst authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));\n(".concat(r6,')(authURL, "').concat(u,'");\n')}})})]})}({csrfToken:e.csrfToken,providers:null==(c=e.providers)?void 0:c.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...n}),title:"Sign In",headTags:d})},signout:()=>(null==o?void 0:o.signOut)?{redirect:o.signOut,cookies:i}:r4({cookies:i,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return r3("div",{className:"signout",children:[(null==n?void 0:n.brandColor)&&r3("style",{dangerouslySetInnerHTML:{__html:"\n        :root {\n          --brand-color: ".concat(n.brandColor,"\n        }\n      ")}}),(null==n?void 0:n.buttonText)&&r3("style",{dangerouslySetInnerHTML:{__html:"\n        :root {\n          --button-text-color: ".concat(n.buttonText,"\n        }\n      ")}}),r3("div",{className:"card",children:[(null==n?void 0:n.logo)&&r3("img",{src:n.logo,alt:"Logo",className:"logo"}),r3("h1",{children:"Signout"}),r3("p",{children:"Are you sure you want to sign out?"}),r3("form",{action:null==t?void 0:t.toString(),method:"POST",children:[r3("input",{type:"hidden",name:"csrfToken",value:r}),r3("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest(e){var n;return(null==o?void 0:o.verifyRequest)?{redirect:"".concat(o.verifyRequest).concat(null!=(n=null==t?void 0:t.search)?n:""),cookies:i}:r4({cookies:i,theme:r,html:function(e){let{url:t,theme:r}=e;return r3("div",{className:"verify-request",children:[r.brandColor&&r3("style",{dangerouslySetInnerHTML:{__html:"\n        :root {\n          --brand-color: ".concat(r.brandColor,"\n        }\n      ")}}),r3("div",{className:"card",children:[r.logo&&r3("img",{src:r.logo,alt:"Logo",className:"logo"}),r3("h1",{children:"Check your email"}),r3("p",{children:"A sign in link has been sent to your email address."}),r3("p",{children:r3("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"})},error:e=>(null==o?void 0:o.error)?{redirect:"".concat(o.error).concat(o.error.includes("?")?"&":"?","error=").concat(e),cookies:i}:r4({cookies:i,theme:r,...function(e){var t;let{url:r,error:n="default",theme:i}=e,o="".concat(r,"/signin"),a={default:{status:200,heading:"Error",message:r3("p",{children:r3("a",{className:"site",href:null==r?void 0:r.origin,children:null==r?void 0:r.host})})},Configuration:{status:500,heading:"Server error",message:r3("div",{children:[r3("p",{children:"There is a problem with the server configuration."}),r3("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:r3("div",{children:[r3("p",{children:"You do not have permission to sign in."}),r3("p",{children:r3("a",{className:"button",href:o,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:r3("div",{children:[r3("p",{children:"The sign in link is no longer valid."}),r3("p",{children:"It may have been used already or it may have expired."})]}),signin:r3("a",{className:"button",href:o,children:"Sign in"})}},{status:s,heading:c,message:l,signin:u}=null!=(t=a[n])?t:a.default;return{status:s,html:r3("div",{className:"error",children:[(null==i?void 0:i.brandColor)&&r3("style",{dangerouslySetInnerHTML:{__html:"\n        :root {\n          --brand-color: ".concat(null==i?void 0:i.brandColor,"\n        }\n      ")}}),r3("div",{className:"card",children:[(null==i?void 0:i.logo)&&r3("img",{src:null==i?void 0:i.logo,alt:"Logo",className:"logo"}),r3("h1",{children:c}),r3("div",{className:"message",children:l}),u]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function r9(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();return new Date(t+1e3*e)}async function r7(e,t,r,n){var i,o,a,s,c,l,u,d,p;if(!(null==r?void 0:r.providerAccountId)||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:h,jwt:m,events:y,session:{strategy:g,generateSessionToken:b}}=n;if(!h)return{user:t,account:r};let w=r,{createUser:v,updateUser:_,getUser:E,getUserByAccount:k,getUserByEmail:A,linkAccount:S,createSession:x,getSessionAndUser:R,deleteSession:P}=h,T=null,C=null,O=!1,U="jwt"===g;if(e)if(U)try{let t=n.cookies.sessionToken.name;(T=await m.decode({...m,token:e,salt:t}))&&"sub"in T&&T.sub&&(C=await E(T.sub))}catch(e){}else{let t=await R(e);t&&(T=t.session,C=t.user)}if("email"===w.type){let r=await A(t.email);return r?((null==C?void 0:C.id)!==r.id&&!U&&e&&await P(e),C=await _({id:r.id,emailVerified:new Date}),await (null==(i=y.updateUser)?void 0:i.call(y,{user:C}))):(C=await v({...t,emailVerified:new Date}),await (null==(o=y.createUser)?void 0:o.call(y,{user:C})),O=!0),{session:T=U?{}:await x({sessionToken:b(),userId:C.id,expires:r9(n.session.maxAge)}),user:C,isNewUser:O}}if("webauthn"===w.type){let e=await k({providerAccountId:w.providerAccountId,provider:w.provider});if(e){if(C){if(e.id===C.id){let e={...w,userId:C.id};return{session:T,user:C,isNewUser:O,account:e}}throw new f.AccountNotLinked("The account is already associated with another user",{provider:w.provider})}T=U?{}:await x({sessionToken:b(),userId:e.id,expires:r9(n.session.maxAge)});let t={...w,userId:e.id};return{session:T,user:e,isNewUser:O,account:t}}{if(C){await S({...w,userId:C.id}),await (null==(c=y.linkAccount)?void 0:c.call(y,{user:C,account:w,profile:t}));let e={...w,userId:C.id};return{session:T,user:C,isNewUser:O,account:e}}if(t.email?await A(t.email):null)throw new f.AccountNotLinked("Another account already exists with the same e-mail address",{provider:w.provider});C=await v({...t}),await (null==(a=y.createUser)?void 0:a.call(y,{user:C})),await S({...w,userId:C.id}),await (null==(s=y.linkAccount)?void 0:s.call(y,{user:C,account:w,profile:t})),T=U?{}:await x({sessionToken:b(),userId:C.id,expires:r9(n.session.maxAge)});let e={...w,userId:C.id};return{session:T,user:C,isNewUser:!0,account:e}}}let I=await k({providerAccountId:w.providerAccountId,provider:w.provider});if(I){if(C){if(I.id===C.id)return{session:T,user:C,isNewUser:O};throw new f.OAuthAccountNotLinked("The account is already associated with another user",{provider:w.provider})}return{session:T=U?{}:await x({sessionToken:b(),userId:I.id,expires:r9(n.session.maxAge)}),user:I,isNewUser:O}}{let{provider:e}=n,{type:r,provider:i,providerAccountId:o,userId:a,...s}=w;if(w=Object.assign(null!=(d=e.account(s))?d:{},{providerAccountId:o,provider:i,type:r,userId:a}),C)return await S({...w,userId:C.id}),await (null==(p=y.linkAccount)?void 0:p.call(y,{user:C,account:w,profile:t})),{session:T,user:C,isNewUser:O};let c=t.email?await A(t.email):null;if(c){let e=n.provider;if(null==e?void 0:e.allowDangerousEmailAccountLinking)C=c,O=!1;else throw new f.OAuthAccountNotLinked("Another account already exists with the same e-mail address",{provider:w.provider})}else C=await v({...t,emailVerified:null}),O=!0;return await (null==(l=y.createUser)?void 0:l.call(y,{user:C})),await S({...w,userId:C.id}),await (null==(u=y.linkAccount)?void 0:u.call(y,{user:C,account:w,profile:t})),{session:T=U?{}:await x({sessionToken:b(),userId:C.id,expires:r9(n.session.maxAge)}),user:C,isNewUser:O}}}function ne(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch(e){return!1}}e.i(25839),e.i(22e3),"undefined"!=typeof navigator&&(null==(a=navigator.userAgent)||null==(o=a.startsWith)?void 0:o.call(a,"Mozilla/5.0 "))||(r="".concat("oauth4webapi","/").concat("v3.8.1"));let nt="ERR_INVALID_ARG_VALUE",nr="ERR_INVALID_ARG_TYPE";function nn(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let ni=Symbol(),no=Symbol(),na=Symbol(),ns=Symbol(),nc=Symbol(),nl=Symbol();Symbol();let nu=new TextEncoder,nd=new TextDecoder;function np(e){return"string"==typeof e?nu.encode(e):nd.decode(e)}function nh(e){return"string"==typeof e?i(e):n(e)}n=Uint8Array.prototype.toBase64?e=>(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e.toBase64({alphabet:"base64url",omitPadding:!0})):e=>{e instanceof ArrayBuffer&&(e=new Uint8Array(e));let t=[];for(let r=0;r<e.byteLength;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},i=Uint8Array.fromBase64?e=>{try{return Uint8Array.fromBase64(e,{alphabet:"base64url"})}catch(e){throw nn("The input to be decoded is not correctly encoded.",nt,e)}}:e=>{try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw nn("The input to be decoded is not correctly encoded.",nt,e)}};class nf extends Error{constructor(e,t){var r,n;super(e,t),c(this,"code",void 0),this.name=this.constructor.name,this.code=ig,null==(r=(n=Error).captureStackTrace)||r.call(n,this,this.constructor)}}class nm extends Error{constructor(e,t){var r,n;super(e,t),c(this,"code",void 0),this.name=this.constructor.name,(null==t?void 0:t.code)&&(this.code=null==t?void 0:t.code),null==(r=(n=Error).captureStackTrace)||r.call(n,this,this.constructor)}}function ny(e,t,r){return new nm(e,{code:t,cause:r})}function ng(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function nb(e){ne(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(null!=e?e:{});if(r&&!t.has("user-agent")&&t.set("user-agent",r),t.has("authorization"))throw nn('"options.headers" must not include the "authorization" header name',nt);return t}function nw(e,t){if(void 0!==t){if("function"==typeof t&&(t=t(e.href)),!(t instanceof AbortSignal))throw nn('"options.signal" must return or be an instance of AbortSignal',nr);return t}}function nv(e){return e.includes("//")?e.replace("//","/"):e}async function n_(e,t,r,n){if(!(e instanceof URL))throw nn('"'.concat(t,'" must be an instance of URL'),nr);nL(e,(null==n?void 0:n[ni])!==!0);let i=r(new URL(e.href)),o=nb(null==n?void 0:n.headers);return o.set("accept","application/json"),((null==n?void 0:n[ns])||fetch)(i.href,{body:void 0,headers:Object.fromEntries(o.entries()),method:"GET",redirect:"manual",signal:nw(i,null==n?void 0:n.signal)})}async function nE(e,t){return n_(e,"issuerIdentifier",e=>{switch(null==t?void 0:t.algorithm){case void 0:case"oidc":e.pathname=nv("".concat(e.pathname,"/").concat(".well-known/openid-configuration"));break;case"oauth2":!function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];"/"===e.pathname?e.pathname=t:e.pathname=nv("".concat(t,"/").concat(r?e.pathname:e.pathname.replace(/(\/)$/,"")))}(e,".well-known/oauth-authorization-server");break;default:throw nn('"options.algorithm" must be "oidc" (default), or "oauth2"',nt)}return e},t)}function nk(e,t,r,n,i){try{if("number"!=typeof e||!Number.isFinite(e))throw nn("".concat(r," must be a number"),nr,i);if(e>0)return;if(t){if(0!==e)throw nn("".concat(r," must be a non-negative number"),nt,i);return}throw nn("".concat(r," must be a positive number"),nt,i)}catch(e){if(n)throw ny(e.message,n,i);throw e}}function nA(e,t,r,n){try{if("string"!=typeof e)throw nn("".concat(t," must be a string"),nr,n);if(0===e.length)throw nn("".concat(t," must not be empty"),nt,n)}catch(e){if(r)throw ny(e.message,r,n);throw e}}async function nS(e,t){if(!(e instanceof URL)&&e!==iW)throw nn('"expectedIssuerIdentifier" must be an instance of URL',nr);if(!ne(t,Response))throw nn('"response" must be an instance of Response',nr);if(200!==t.status)throw ny('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',ik,t);iO(t);let r=await iH(t);if(nA(r.issuer,'"response" body "issuer" property',i_,{body:r}),e!==iW&&new URL(r.issuer).href!==e.href)throw ny('"response" body "issuer" property does not match the expected value',iP,{expected:e.href,body:r,attribute:"issuer"});return r}function nx(e){var t=e,r="application/json";if(n0(t)!==r)throw function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];let i='"response" content-type must be ';if(r.length>2){let e=r.pop();i+="".concat(r.join(", "),", or ").concat(e)}else 2===r.length?i+="".concat(r[0]," or ").concat(r[1]):i+=r[0];return ny(i,iE,e)}(t,r)}function nR(){return nh(crypto.getRandomValues(new Uint8Array(32)))}async function nP(e){return nA(e,"codeVerifier"),nh(await crypto.subtle.digest("SHA-256",np(e)))}function nT(e){let t=null==e?void 0:e[no];return"number"==typeof t&&Number.isFinite(t)?t:0}function nC(e){let t=null==e?void 0:e[na];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function nO(){return Math.floor(Date.now()/1e3)}function nU(e){if("object"!=typeof e||null===e)throw nn('"as" must be an object',nr);nA(e.issuer,'"as.issuer"')}function nI(e){if("object"!=typeof e||null===e)throw nn('"client" must be an object',nr);nA(e.client_id,'"client.client_id"')}function nj(e,t){let r=nO()+nT(t);return{jti:nR(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function nD(e,t,r){if(!r.usages.includes("sign"))throw nn('CryptoKey instances used for signing assertions must include "sign" in their "usages"',nt);let n="".concat(nh(np(JSON.stringify(e))),".").concat(nh(np(JSON.stringify(t)))),i=nh(await crypto.subtle.sign(function(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new nf("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(iU(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new nf("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return iU(e),e.algorithm.name;case"ML-DSA-44":case"ML-DSA-65":case"ML-DSA-87":case"Ed25519":return e.algorithm.name}throw new nf("unsupported CryptoKey algorithm name",{cause:e})}(r),r,np(n)));return"".concat(n,".").concat(i)}let nN=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch(e){return null}};function nL(e,t){if(t&&"https:"!==e.protocol)throw ny("only requests to HTTPS are allowed",iA,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw ny("only HTTP and HTTPS requests are allowed",iS,e)}function nH(e,t,r,n){let i;if("string"!=typeof e||!(i=nN(e)))throw ny("authorization server metadata does not contain a valid ".concat(r?'"as.mtls_endpoint_aliases.'.concat(t,'"'):'"as.'.concat(t,'"')),void 0===e?iT:iC,{attribute:r?"mtls_endpoint_aliases.".concat(t):t});return nL(i,n),i}function nW(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?nH(e.mtls_endpoint_aliases[t],t,r,n):nH(e[t],t,r,n)}new WeakMap,new WeakMap,new WeakMap,new WeakMap,new WeakMap,new WeakMap,new WeakMap,new WeakSet,new WeakSet;class nM extends Error{constructor(e,t){var r,n;super(e,t),c(this,"cause",void 0),c(this,"code",void 0),c(this,"error",void 0),c(this,"status",void 0),c(this,"error_description",void 0),c(this,"response",void 0),this.name=this.constructor.name,this.code=iy,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),null==(r=(n=Error).captureStackTrace)||r.call(n,this,this.constructor)}}class nK extends Error{constructor(e,t){var r,n,i;super(e,t),c(this,"cause",void 0),c(this,"code",void 0),c(this,"error",void 0),c(this,"error_description",void 0),this.name=this.constructor.name,this.code=ib,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=null!=(i=t.cause.get("error_description"))?i:void 0,null==(r=(n=Error).captureStackTrace)||r.call(n,this,this.constructor)}}class nJ extends Error{constructor(e,t){var r,n;super(e,t),c(this,"cause",void 0),c(this,"code",void 0),c(this,"response",void 0),c(this,"status",void 0),this.name=this.constructor.name,this.code=im,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),null==(r=(n=Error).captureStackTrace)||r.call(n,this,this.constructor)}}let nq="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",nB=RegExp("^[,\\s]*("+nq+")\\s(.*)"),nz=RegExp("^[,\\s]*("+nq+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),nF=RegExp("^[,\\s]*"+("("+nq+")\\s*=\\s*(")+nq+")[,\\s]*(.*)"),nG=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function nV(e){if(e.status>399&&e.status<500){iO(e),nx(e);try{let t=await e.clone().json();if(ng(t)&&"string"==typeof t.error&&t.error.length)return t}catch(e){}}}async function nX(e,t,r){if(e.status!==t){let t;if(n9(e),t=await nV(e)){var n;throw await (null==(n=e.body)?void 0:n.cancel()),new nM("server responded with an error in the response body",{cause:t,response:e})}throw ny('"response" is not a conform '.concat(r," response (unexpected HTTP status code)"),ik,e)}}function n$(e){if(!ii.has(e))throw nn('"options.DPoP" is not a valid DPoPHandle',nt)}async function nY(e,t,r,n,i,o){var a;if(nA(e,'"accessToken"'),!(r instanceof URL))throw nn('"url" must be an instance of URL',nr);nL(r,(null==o?void 0:o[ni])!==!0),n=nb(n),(null==o?void 0:o.DPoP)&&(n$(o.DPoP),await o.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization","".concat(n.has("dpop")?"DPoP":"Bearer"," ").concat(e));let s=await ((null==o?void 0:o[ns])||fetch)(r.href,{body:i,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:nw(r,null==o?void 0:o.signal)});return null==o||null==(a=o.DPoP)||a.cacheNonce(s),s}async function nZ(e,t,r,n){nU(e),nI(t);let i=nW(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,(null==n?void 0:n[ni])!==!0),o=nb(null==n?void 0:n.headers);return t.userinfo_signed_response_alg?o.set("accept","application/jwt"):(o.set("accept","application/json"),o.append("accept","application/jwt")),nY(r,"GET",i,o,null,{...n,[no]:nT(t)})}let nQ=Symbol();function n0(e){var t;return null==(t=e.headers.get("content-type"))?void 0:t.split(";")[0]}async function n1(e,t,r,n,i){let o;if(nU(e),nI(t),!ne(n,Response))throw nn('"response" must be an instance of Response',nr);if(n9(n),200!==n.status)throw ny('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',ik,n);if(iO(n),"application/jwt"===n0(n)){let{claims:r,jwt:a}=await iI(await n.text(),ij.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),nT(t),nC(t),null==i?void 0:i[nl]).then(n7.bind(void 0,t.client_id)).then(it.bind(void 0,e));n5.set(n,a),o=r}else{if(t.userinfo_signed_response_alg)throw ny("JWT UserInfo Response expected",iw,n);o=await iH(n)}if(nA(o.sub,'"response" body "sub" property',i_,{body:o}),r===nQ);else if(nA(r,'"expectedSubject"'),o.sub!==r)throw ny('unexpected "response" body "sub" property value',iP,{expected:r,body:o,attribute:"sub"});return o}async function n2(e,t,r,n,i,o,a){return await r(e,t,i,o),o.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),((null==a?void 0:a[ns])||fetch)(n.href,{body:i,headers:Object.fromEntries(o.entries()),method:"POST",redirect:"manual",signal:nw(n,null==a?void 0:a.signal)})}async function n3(e,t,r,n,i,o){var a;let s=nW(e,"token_endpoint",t.use_mtls_endpoint_aliases,(null==o?void 0:o[ni])!==!0);i.set("grant_type",n);let c=nb(null==o?void 0:o.headers);c.set("accept","application/json"),(null==o?void 0:o.DPoP)!==void 0&&(n$(o.DPoP),await o.DPoP.addProof(s,c,"POST"));let l=await n2(e,t,r,s,i,c,o);return null==o||null==(a=o.DPoP)||a.cacheNonce(l),l}let n6=new WeakMap,n5=new WeakMap;function n4(e){if(!e.id_token)return;let t=n6.get(e);if(!t)throw nn('"ref" was already garbage collected or did not resolve from the proper sources',nt);return t}async function n8(e,t,r,n,i,o){if(nU(e),nI(t),!ne(r,Response))throw nn('"response" must be an instance of Response',nr);await nX(r,200,"Token Endpoint"),iO(r);let a=await iH(r);if(nA(a.access_token,'"response" body "access_token" property',i_,{body:a}),nA(a.token_type,'"response" body "token_type" property',i_,{body:a}),a.token_type=a.token_type.toLowerCase(),void 0!==a.expires_in){let e="number"!=typeof a.expires_in?parseFloat(a.expires_in):a.expires_in;nk(e,!0,'"response" body "expires_in" property',i_,{body:a}),a.expires_in=e}if(void 0!==a.refresh_token&&nA(a.refresh_token,'"response" body "refresh_token" property',i_,{body:a}),void 0!==a.scope&&"string"!=typeof a.scope)throw ny('"response" body "scope" property must be a string',i_,{body:a});if(void 0!==a.id_token){nA(a.id_token,'"response" body "id_token" property',i_,{body:a});let o=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&o.push("auth_time"),void 0!==t.default_max_age&&(nk(t.default_max_age,!0,'"client.default_max_age"'),o.push("auth_time")),(null==n?void 0:n.length)&&o.push(...n);let{claims:s,jwt:c}=await iI(a.id_token,ij.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),nT(t),nC(t),i).then(ic.bind(void 0,o)).then(ir.bind(void 0,e)).then(ie.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw ny('ID Token "aud" (audience) claim includes additional untrusted audiences',iR,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw ny('unexpected ID Token "azp" (authorized party) claim value',iR,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&nk(s.auth_time,!0,'ID Token "auth_time" (authentication time)',i_,{claims:s}),n5.set(r,c),n6.set(a,s)}if((null==o?void 0:o[a.token_type])!==void 0)o[a.token_type](r,a);else if("dpop"!==a.token_type&&"bearer"!==a.token_type)throw new nf("unsupported `token_type` value",{cause:{body:a}});return a}function n9(e){let t;if(t=function(e){if(!ne(e,Response))throw nn('"response" must be an instance of Response',nr);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(nB),i=null==t?void 0:t["1"].toLowerCase();if(n=null==t?void 0:t["2"],!i)return;let o={};for(;n;){let r,i;if(t=n.match(nz)){if([,r,i,n]=t,i.includes("\\"))try{i=JSON.parse('"'.concat(i,'"'))}catch(e){}o[r.toLowerCase()]=i;continue}if(t=n.match(nF)){[,r,i,n]=t,o[r.toLowerCase()]=i;continue}if(t=n.match(nG)){if(Object.keys(o).length)break;[,e,n]=t;break}return}let a={scheme:i,parameters:o};e&&(a.token68=e),r.push(a)}if(r.length)return r}(e))throw new nJ("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function n7(e,t){return void 0!==t.claims.aud?ie(e,t):t}function ie(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw ny('unexpected JWT "aud" (audience) claim value',iR,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw ny('unexpected JWT "aud" (audience) claim value',iR,{expected:e,claims:t.claims,claim:"aud"});return t}function it(e,t){return void 0!==t.claims.iss?ir(e,t):t}function ir(e,t){var r,n;let i=null!=(n=null==(r=e[iM])?void 0:r.call(e,t))?n:e.issuer;if(t.claims.iss!==i)throw ny('unexpected JWT "iss" (issuer) claim value',iR,{expected:i,claims:t.claims,claim:"iss"});return t}let ii=new WeakSet,io=Symbol();async function ia(e,t,r,n,i,o,a){if(nU(e),nI(t),!ii.has(n))throw nn('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',nt);nA(i,'"redirectUri"');let s=iD(n,"code");if(!s)throw ny('no authorization code in "callbackParameters"',i_);let c=new URLSearchParams(null==a?void 0:a.additionalParameters);return c.set("redirect_uri",i),c.set("code",s),o!==io&&(nA(o,'"codeVerifier"'),c.set("code_verifier",o)),n3(e,t,r,"authorization_code",c,a)}let is={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function ic(e,t){for(let r of e)if(void 0===t.claims[r])throw ny('JWT "'.concat(r,'" (').concat(is[r],") claim missing"),i_,{claims:t.claims});return t}let il=Symbol(),iu=Symbol();async function id(e,t,r,n){return"string"==typeof(null==n?void 0:n.expectedNonce)||"number"==typeof(null==n?void 0:n.maxAge)||(null==n?void 0:n.requireIdToken)?ip(e,t,r,n.expectedNonce,n.maxAge,n[nl],n.recognizedTokenTypes):ih(e,t,r,null==n?void 0:n[nl],null==n?void 0:n.recognizedTokenTypes)}async function ip(e,t,r,n,i,o,a){let s=[];switch(n){case void 0:n=il;break;case il:break;default:nA(n,'"expectedNonce" argument'),s.push("nonce")}switch(null!=i||(i=t.default_max_age),i){case void 0:i=iu;break;case iu:break;default:nk(i,!0,'"maxAge" argument'),s.push("auth_time")}let c=await n8(e,t,r,s,o,a);nA(c.id_token,'"response" body "id_token" property',i_,{body:c});let l=n4(c);if(i!==iu){let e=nO()+nT(t),r=nC(t);if(l.auth_time+i<e-r)throw ny("too much time has elapsed since the last End-User authentication",ix,{claims:l,now:e,tolerance:r,claim:"auth_time"})}if(n===il){if(void 0!==l.nonce)throw ny('unexpected ID Token "nonce" claim value',iR,{expected:void 0,claims:l,claim:"nonce"})}else if(l.nonce!==n)throw ny('unexpected ID Token "nonce" claim value',iR,{expected:n,claims:l,claim:"nonce"});return c}async function ih(e,t,r,n,i){let o=await n8(e,t,r,void 0,n,i),a=n4(o);if(a){if(void 0!==t.default_max_age){nk(t.default_max_age,!0,'"client.default_max_age"');let e=nO()+nT(t),r=nC(t);if(a.auth_time+t.default_max_age<e-r)throw ny("too much time has elapsed since the last End-User authentication",ix,{claims:a,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==a.nonce)throw ny('unexpected ID Token "nonce" claim value',iR,{expected:void 0,claims:a,claim:"nonce"})}return o}let im="OAUTH_WWW_AUTHENTICATE_CHALLENGE",iy="OAUTH_RESPONSE_BODY_ERROR",ig="OAUTH_UNSUPPORTED_OPERATION",ib="OAUTH_AUTHORIZATION_RESPONSE_ERROR",iw="OAUTH_JWT_USERINFO_EXPECTED",iv="OAUTH_PARSE_ERROR",i_="OAUTH_INVALID_RESPONSE",iE="OAUTH_RESPONSE_IS_NOT_JSON",ik="OAUTH_RESPONSE_IS_NOT_CONFORM",iA="OAUTH_HTTP_REQUEST_FORBIDDEN",iS="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",ix="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",iR="OAUTH_JWT_CLAIM_COMPARISON_FAILED",iP="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",iT="OAUTH_MISSING_SERVER_METADATA",iC="OAUTH_INVALID_SERVER_METADATA";function iO(e){if(e.bodyUsed)throw nn('"response" body has been used already',nt)}function iU(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new nf("unsupported ".concat(t.name," modulusLength"),{cause:e})}async function iI(e,t,r,n,i){let o,a,{0:s,1:c,length:l}=e.split(".");if(5===l)if(void 0!==i)e=await i(e),{0:s,1:c,length:l}=e.split(".");else throw new nf("JWE decryption is not configured",{cause:e});if(3!==l)throw ny("Invalid JWT",i_,e);try{o=JSON.parse(np(nh(s)))}catch(e){throw ny("failed to parse JWT Header body as base64url encoded JSON",iv,e)}if(!ng(o))throw ny("JWT Header must be a top level object",i_,e);if(t(o),void 0!==o.crit)throw new nf('no JWT "crit" header parameter extensions are supported',{cause:{header:o}});try{a=JSON.parse(np(nh(c)))}catch(e){throw ny("failed to parse JWT Payload body as base64url encoded JSON",iv,e)}if(!ng(a))throw ny("JWT Payload must be a top level object",i_,e);let u=nO()+r;if(void 0!==a.exp){if("number"!=typeof a.exp)throw ny('unexpected JWT "exp" (expiration time) claim type',i_,{claims:a});if(a.exp<=u-n)throw ny('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',ix,{claims:a,now:u,tolerance:n,claim:"exp"})}if(void 0!==a.iat&&"number"!=typeof a.iat)throw ny('unexpected JWT "iat" (issued at) claim type',i_,{claims:a});if(void 0!==a.iss&&"string"!=typeof a.iss)throw ny('unexpected JWT "iss" (issuer) claim type',i_,{claims:a});if(void 0!==a.nbf){if("number"!=typeof a.nbf)throw ny('unexpected JWT "nbf" (not before) claim type',i_,{claims:a});if(a.nbf>u+n)throw ny('unexpected JWT "nbf" (not before) claim value',ix,{claims:a,now:u,tolerance:n,claim:"nbf"})}if(void 0!==a.aud&&"string"!=typeof a.aud&&!Array.isArray(a.aud))throw ny('unexpected JWT "aud" (audience) claim type',i_,{claims:a});return{header:o,claims:a,jwt:e}}function ij(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw ny('unexpected JWT "alg" header parameter',i_,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw ny('unexpected JWT "alg" header parameter',i_,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw ny('unexpected JWT "alg" header parameter',i_,{header:n,expected:r,reason:"default value"});return}throw ny('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function iD(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw ny('"'.concat(t,'" parameter must be provided only once'),i_);return r}let iN=Symbol(),iL=Symbol();async function iH(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:nx;try{t=await e.json()}catch(t){throw r(e),ny('failed to parse "response" body as JSON',iv,t)}if(!ng(t))throw ny('"response" body must be a top level object',i_,{body:t});return t}let iW=Symbol(),iM=Symbol();async function iK(e,t,r){let{cookies:n,logger:i}=r,o=n[e],a=new Date;a.setTime(a.getTime()+9e5),i.debug("CREATE_".concat(e.toUpperCase()),{name:o.name,payload:t,COOKIE_TTL:900,expires:a});let s=await tU({...r.jwt,maxAge:900,token:{value:t},salt:o.name}),c={...o.options,expires:a};return{name:o.name,value:s,options:c}}async function iJ(e,t,r){try{let{logger:n,cookies:i,jwt:o}=r;if(n.debug("PARSE_".concat(e.toUpperCase()),{cookie:t}),!t)throw new f.InvalidCheck("".concat(e," cookie was missing"));let a=await tI({...o,token:t,salt:i[e].name});if(null==a?void 0:a.value)return a.value;throw Error("Invalid cookie")}catch(t){throw new f.InvalidCheck("".concat(e," value could not be parsed"),{cause:t})}}function iq(e,t,r){let{logger:n,cookies:i}=t,o=i[e];n.debug("CLEAR_".concat(e.toUpperCase()),{cookie:o}),r.push({name:o.name,value:"",options:{...i[e].options,maxAge:0}})}function iB(e,t){return async function(r,n,i){var o;let{provider:a,logger:s}=i;if(!(null==a||null==(o=a.checks)?void 0:o.includes(e)))return;let c=null==r?void 0:r[i.cookies[t].name];s.debug("USE_".concat(t.toUpperCase()),{value:c});let l=await iJ(t,c,i);return iq(t,i,n),l}}let iz={async create(e){let t=nR(),r=await nP(t);return{cookie:await iK("pkceCodeVerifier",t,e),value:r}},use:iB("pkce","pkceCodeVerifier")},iF="encodedState",iG={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new f.InvalidCheck("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:nR()},i=await tU({secret:e.jwt.secret,token:n,salt:iF,maxAge:900});return{cookie:await iK("state",i,e),value:i}},use:iB("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await tI({secret:t.jwt.secret,token:e,salt:iF});if(r)return r;throw Error("Invalid state")}catch(e){throw new f.InvalidCheck("State could not be decoded",{cause:e})}}},iV={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=nR();return{cookie:await iK("nonce",t,e),value:t}},use:iB("nonce","nonce")},iX="encodedWebauthnChallenge",i$={create:async(e,t,r)=>({cookie:await iK("webauthnChallenge",await tU({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:iX,maxAge:900}),e)}),async use(e,t,r){let n=null==t?void 0:t[e.cookies.webauthnChallenge.name],i=await iJ("webauthnChallenge",n,e),o=await tI({secret:e.jwt.secret,token:i,salt:iX});if(iq("webauthnChallenge",e,r),!o)throw new f.InvalidCheck("WebAuthn challenge was missing");return o}};function iY(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function iZ(e,t,r){var n,i,o,a,s,c,l;let u,d,p,{logger:h,provider:m}=r,{token:y,userinfo:g}=m;if((null==y?void 0:y.url)&&"authjs.dev"!==y.url.host||(null==g?void 0:g.url)&&"authjs.dev"!==g.url.host)u={issuer:null!=(i=m.issuer)?i:"https://authjs.dev",token_endpoint:null==y?void 0:y.url.toString(),userinfo_endpoint:null==g?void 0:g.url.toString()};else{let e=new URL(m.issuer),t=await nE(e,{[ni]:!0,[ns]:m[t0]});if(!(u=await nS(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!u.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let b={client_id:m.clientId,...m.client};switch(b.token_endpoint_auth_method){case void 0:case"client_secret_basic":d=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=iY(e),n=iY(t),i=btoa("".concat(r,":").concat(n));return"Basic ".concat(i)}(m.clientId,m.clientSecret))};break;case"client_secret_post":nA(l=m.clientSecret,'"clientSecret"'),d=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",l)};break;case"client_secret_jwt":d=function(e,t){let r;nA(e,'"clientSecret"');let n=void 0;return async(t,i,o,a)=>{r||(r=await crypto.subtle.importKey("raw",np(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]));let s={alg:"HS256"},c=nj(t,i);null==n||n(s,c);let l="".concat(nh(np(JSON.stringify(s))),".").concat(nh(np(JSON.stringify(c)))),u=await crypto.subtle.sign(r.algorithm,r,np(l));o.set("client_id",i.client_id),o.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),o.set("client_assertion","".concat(l,".").concat(nh(new Uint8Array(u))))}}(m.clientSecret);break;case"private_key_jwt":d=function(e,t){let{key:r,kid:n}=e instanceof CryptoKey?{key:e}:(null==e?void 0:e.key)instanceof CryptoKey?(void 0!==e.kid&&nA(e.kid,'"kid"'),{key:e.key,kid:e.kid}):{};var i='"clientPrivateKey.key"';if(!(r instanceof CryptoKey))throw nn("".concat(i," must be a CryptoKey"),nr);if("private"!==r.type)throw nn("".concat(i," must be a private CryptoKey"),nt);return async(e,i,o,a)=>{var s;let c={alg:function(e){switch(e.algorithm.name){case"RSA-PSS":switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new nf("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new nf("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new nf("unsupported EcKeyAlgorithm namedCurve",{cause:e})}case"Ed25519":case"ML-DSA-44":case"ML-DSA-65":case"ML-DSA-87":return e.algorithm.name;case"EdDSA":return"Ed25519";default:throw new nf("unsupported CryptoKey algorithm name",{cause:e})}}(r),kid:n},l=nj(e,i);null==t||null==(s=t[nc])||s.call(t,c,l),o.set("client_id",i.client_id),o.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),o.set("client_assertion",await nD(c,l,r))}}(m.token.clientPrivateKey,{[nc](e,t){t.aud=[u.issuer,u.token_endpoint]}});break;case"none":d=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let w=[],v=await iG.use(t,w,r);try{p=function(e,t,r,n){var i;if(nU(e),nI(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw nn('"parameters" must be an instance of URLSearchParams, or URL',nr);if(iD(r,"response"))throw ny('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',i_,{parameters:r});let o=iD(r,"iss"),a=iD(r,"state");if(!o&&e.authorization_response_iss_parameter_supported)throw ny('response parameter "iss" (issuer) missing',i_,{parameters:r});if(o&&o!==e.issuer)throw ny('unexpected "iss" (issuer) response parameter value',i_,{expected:e.issuer,parameters:r});switch(n){case void 0:case iL:if(void 0!==a)throw ny('unexpected "state" response parameter encountered',i_,{expected:void 0,parameters:r});break;case iN:break;default:if(nA(n,'"expectedState" argument'),a!==n)throw ny(void 0===a?'response parameter "state" missing':'unexpected "state" response parameter value',i_,{expected:n,parameters:r})}if(iD(r,"error"))throw new nK("authorization response from the server is an error",{cause:r});let s=iD(r,"id_token"),c=iD(r,"token");if(void 0!==s||void 0!==c)throw new nf("implicit and hybrid flows are not supported");return i=new URLSearchParams(r),ii.add(i),i}(u,b,new URLSearchParams(e),m.checks.includes("state")?v:iN)}catch(e){if(e instanceof nK){let t={providerId:m.id,...Object.fromEntries(e.cause.entries())};throw h.debug("OAuthCallbackError",t),new f.OAuthCallbackError("OAuth Provider returned an error",t)}throw e}let _=await iz.use(t,w,r),E=m.callbackUrl;!r.isOnRedirectProxy&&m.redirectProxyUrl&&(E=m.redirectProxyUrl);let k=await ia(u,b,d,p,E,null!=_?_:"decoy",{[ni]:!0,[ns]:function(){for(var e,t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return m.checks.includes("pkce")||r[1].body.delete("code_verifier"),(null!=(e=m[t0])?e:fetch)(...r)}});(null==(n=m.token)?void 0:n.conform)&&(k=null!=(o=await m.token.conform(k.clone()))?o:k);let A={},S="oidc"===m.type;if(m[t1])switch(m.id){case"microsoft-entra-id":case"azure-ad":{let e=await k.clone().json();if(e.error){let t={providerId:m.id,...e};throw new f.OAuthCallbackError("OAuth Provider returned an error: ".concat(e.error),t)}let{tid:t}=function(e){let t,r;if("string"!=typeof e)throw new z("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:i}=e.split(".");if(5===i)throw new z("Only JWTs using Compact JWS serialization can be decoded");if(3!==i)throw new z("Invalid JWT");if(!n)throw new z("JWTs must contain a payload");try{t=D(n)}catch(e){throw new z("Failed to base64url decode the payload")}try{r=JSON.parse(C.decode(t))}catch(e){throw new z("Failed to parse the decoded payload as JSON")}if(!eS(r))throw new z("Invalid JWT Claims Set");return r}(e.id_token);if("string"==typeof t){let e=null!=(c=null==(s=u.issuer)||null==(a=s.match(/microsoftonline\.com\/(\w+)\/v2\.0/))?void 0:a[1])?c:"common",r=new URL(u.issuer.replace(e,t)),n=await nE(r,{[ns]:m[t0]});u=await nS(r,n)}}}let x=await id(u,b,k,{expectedNonce:await iV.use(t,w,r),requireIdToken:S});if(S){let t=n4(x);if(A=t,m[t1]&&"apple"===m.id)try{A.user=JSON.parse(null==e?void 0:e.user)}catch(e){}if(!1===m.idToken){let e=await nZ(u,b,x.access_token,{[ns]:m[t0],[ni]:!0});A=await n1(u,b,t.sub,e)}}else if(null==g?void 0:g.request){let e=await g.request({tokens:x,provider:m});e instanceof Object&&(A=e)}else if(null==g?void 0:g.url){let e=await nZ(u,b,x.access_token,{[ns]:m[t0],[ni]:!0});A=await e.json()}else throw TypeError("No userinfo endpoint configured");return x.expires_in&&(x.expires_at=Math.floor(Date.now()/1e3)+Number(x.expires_in)),{...await iQ(A,m,x,h),profile:A,cookies:w}}async function iQ(e,t,r,n){try{var i,o;let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:null==(i=n.email)?void 0:i.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:null!=(o=n.id)?o:crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new f.OAuthProfileParseError(r,{provider:t.id}))}}var i0=e.i(12815);async function i1(e,t,r,n){let i=await i4(e,t,r),{cookie:o}=await i$.create(e,i.challenge,r);return{status:200,cookies:[...null!=n?n:[],o],body:{action:"register",options:i},headers:{"Content-Type":"application/json"}}}async function i2(e,t,r,n){let i=await i5(e,t,r),{cookie:o}=await i$.create(e,i.challenge);return{status:200,cookies:[...null!=n?n:[],o],body:{action:"authenticate",options:i},headers:{"Content-Type":"application/json"}}}async function i3(e,t,r){let n,{adapter:i,provider:o}=e,a=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!a||"object"!=typeof a||!("id"in a)||"string"!=typeof a.id)throw new f.AuthError("Invalid WebAuthn Authentication response");let s=i7(i9(a.id)),c=await i.getAuthenticator(s);if(!c)throw new f.AuthError("WebAuthn authenticator not found in database: ".concat(JSON.stringify({credentialID:s})));let{challenge:l}=await i$.use(e,t.cookies,r);try{var u;let r=o.getRelayingParty(e,t);n=await o.simpleWebAuthn.verifyAuthenticationResponse({...o.verifyAuthenticationOptions,expectedChallenge:l,response:a,authenticator:{...u=c,credentialDeviceType:u.credentialDeviceType,transports:oe(u.transports),credentialID:i9(u.credentialID),credentialPublicKey:i9(u.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new f.WebAuthnVerificationError(e)}let{verified:d,authenticationInfo:p}=n;if(!d)throw new f.WebAuthnVerificationError("WebAuthn authentication response could not be verified");try{let{newCounter:e}=p;await i.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new f.AdapterError("Failed to update authenticator counter. This may cause future authentication attempts to fail. ".concat(JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:p.newCounter})),e)}let h=await i.getAccount(c.providerAccountId,o.id);if(!h)throw new f.AuthError("WebAuthn account not found in database: ".concat(JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})));let m=await i.getUser(h.userId);if(!m)throw new f.AuthError("WebAuthn user not found in database: ".concat(JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:h.userId})));return{account:h,user:m}}async function i6(e,t,r){var n;let i,{provider:o}=e,a=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!a||"object"!=typeof a||!("id"in a)||"string"!=typeof a.id)throw new f.AuthError("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await i$.use(e,t.cookies,r);if(!c)throw new f.AuthError("Missing user registration data in WebAuthn challenge cookie");try{let r=o.getRelayingParty(e,t);i=await o.simpleWebAuthn.verifyRegistrationResponse({...o.verifyRegistrationOptions,expectedChallenge:s,response:a,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new f.WebAuthnVerificationError(e)}if(!i.verified||!i.registrationInfo)throw new f.WebAuthnVerificationError("WebAuthn registration response could not be verified");let l={providerAccountId:i7(i.registrationInfo.credentialID),provider:e.provider.id,type:o.type},u={providerAccountId:l.providerAccountId,counter:i.registrationInfo.counter,credentialID:i7(i.registrationInfo.credentialID),credentialPublicKey:i7(i.registrationInfo.credentialPublicKey),credentialBackedUp:i.registrationInfo.credentialBackedUp,credentialDeviceType:i.registrationInfo.credentialDeviceType,transports:null==(n=a.response.transports)?void 0:n.join(",")};return{user:c,account:l,authenticator:u}}async function i5(e,t,r){let{provider:n,adapter:i}=e,o=r&&r.id?await i.listAuthenticatorsByUserId(r.id):null,a=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:a.id,allowCredentials:null==o?void 0:o.map(e=>({id:i9(e.credentialID),type:"public-key",transports:oe(e.transports)}))})}async function i4(e,t,r){var n;let{provider:i,adapter:o}=e,a=r.id?await o.listAuthenticatorsByUserId(r.id):null,s=tG(32),c=i.getRelayingParty(e,t);return await i.simpleWebAuthn.generateRegistrationOptions({...i.registrationOptions,userID:s,userName:r.email,userDisplayName:null!=(n=r.name)?n:void 0,rpID:c.id,rpName:c.name,excludeCredentials:null==a?void 0:a.map(e=>({id:i9(e.credentialID),type:"public-key",transports:oe(e.transports)}))})}function i8(e){let{provider:t,adapter:r}=e;if(!r)throw new f.MissingAdapter("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new f.InvalidProvider("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function i9(e){return new Uint8Array(i0.Buffer.from(e,"base64"))}function i7(e){return i0.Buffer.from(e).toString("base64")}function oe(e){return e?e.split(","):void 0}async function ot(e,t,r,n){if(!t.provider)throw new f.InvalidProvider("Callback route called without provider");let{query:i,body:o,method:a,headers:s}=e,{provider:c,adapter:l,url:u,callbackUrl:d,pages:p,jwt:h,events:m,callbacks:y,session:{strategy:g,maxAge:b},logger:w}=t,v="jwt"===g;try{var _,E,k,A,S,x,R,P,T,C,O,U,I;if("oauth"===c.type||"oidc"===c.type){let a,s=(null==(_=c.authorization)?void 0:_.url.searchParams.get("response_mode"))==="form_post"?o:i;if(t.isOnRedirectProxy&&(null==s?void 0:s.state)){let e=await iG.decode(s.state,t);if((null==e?void 0:e.origin)&&new URL(e.origin).origin!==t.url.origin){let t="".concat(e.origin,"?").concat(new URLSearchParams(s));return w.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let f=await iZ(s,e.cookies,t);f.cookies.length&&n.push(...f.cookies),w.debug("authorization result",f);let{user:g,account:A,profile:S}=f;if(!g||!A||!S)return{redirect:"".concat(u,"/signin"),cookies:n};if(l){let{getUserByAccount:e}=l;a=await e({providerAccountId:A.providerAccountId,provider:c.id})}let x=await or({user:null!=a?a:g,account:A,profile:S},t);if(x)return{redirect:x,cookies:n};let{user:R,session:P,isNewUser:T}=await r7(r.value,g,A,t);if(v){let e={name:R.name,email:R.email,picture:R.image,sub:null==(k=R.id)?void 0:k.toString()},i=await y.jwt({token:e,user:R,account:A,profile:S,isNewUser:T,trigger:T?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,o=await h.encode({...h,token:i,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*b);let s=r.chunk(o,{expires:a});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:P.sessionToken,options:{...t.cookies.sessionToken.options,expires:P.expires}});if(await (null==(E=m.signIn)?void 0:E.call(m,{user:R,account:A,profile:S,isNewUser:T})),T&&p.newUser)return{redirect:"".concat(p.newUser).concat(p.newUser.includes("?")?"&":"?").concat(new URLSearchParams({callbackUrl:d})),cookies:n};return{redirect:d,cookies:n}}if("email"===c.type){let e=null==i?void 0:i.token,o=null==i?void 0:i.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let a=null!=(S=c.secret)?S:t.secret,s=await l.useVerificationToken({identifier:o,token:await tF("".concat(e).concat(a))}),u=!!s,g=u&&s.expires.valueOf()<Date.now();if(!u||g||o&&s.identifier!==o)throw new f.Verification({hasInvite:u,expired:g});let{identifier:w}=s,_=null!=(x=await l.getUserByEmail(w))?x:{id:crypto.randomUUID(),email:w,emailVerified:null},E={providerAccountId:_.email,userId:_.id,type:"email",provider:c.id},k=await or({user:_,account:E},t);if(k)return{redirect:k,cookies:n};let{user:P,session:T,isNewUser:C}=await r7(r.value,_,E,t);if(v){let e={name:P.name,email:P.email,picture:P.image,sub:null==(R=P.id)?void 0:R.toString()},i=await y.jwt({token:e,user:P,account:E,isNewUser:C,trigger:C?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,o=await h.encode({...h,token:i,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*b);let s=r.chunk(o,{expires:a});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:T.sessionToken,options:{...t.cookies.sessionToken.options,expires:T.expires}});if(await (null==(A=m.signIn)?void 0:A.call(m,{user:P,account:E,isNewUser:C})),C&&p.newUser)return{redirect:"".concat(p.newUser).concat(p.newUser.includes("?")?"&":"?").concat(new URLSearchParams({callbackUrl:d})),cookies:n};return{redirect:d,cookies:n}}if("credentials"===c.type&&"POST"===a){let e=null!=o?o:{};Object.entries(null!=i?i:{}).forEach(e=>{let[t,r]=e;return u.searchParams.set(t,r)});let l=await c.authorize(e,new Request(u,{headers:s,method:a,body:JSON.stringify(o)}));if(l)l.id=null!=(C=null==(P=l.id)?void 0:P.toString())?C:crypto.randomUUID();else throw new f.CredentialsSignin;let p={providerAccountId:l.id,type:"credentials",provider:c.id},g=await or({user:l,account:p,credentials:e},t);if(g)return{redirect:g,cookies:n};let w={name:l.name,email:l.email,picture:l.image,sub:l.id},v=await y.jwt({token:w,user:l,account:p,isNewUser:!1,trigger:"signIn"});if(null===v)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await h.encode({...h,token:v,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*b);let a=r.chunk(i,{expires:o});n.push(...a)}return await (null==(T=m.signIn)?void 0:T.call(m,{user:l,account:p})),{redirect:d,cookies:n}}else if("webauthn"===c.type&&"POST"===a){let i,o,a,s=null==(O=e.body)?void 0:O.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new f.AuthError("Invalid action parameter");let c=i8(t);switch(s){case"authenticate":{let t=await i3(c,e,n);i=t.user,o=t.account;break}case"register":{let r=await i6(t,e,n);i=r.user,o=r.account,a=r.authenticator}}await or({user:i,account:o},t);let{user:l,isNewUser:u,session:g,account:w}=await r7(r.value,i,o,t);if(!w)throw new f.AuthError("Error creating or finding account");if(a&&l.id&&await c.adapter.createAuthenticator({...a,userId:l.id}),v){let e={name:l.name,email:l.email,picture:l.image,sub:null==(I=l.id)?void 0:I.toString()},i=await y.jwt({token:e,user:l,account:w,isNewUser:u,trigger:u?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,o=await h.encode({...h,token:i,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*b);let s=r.chunk(o,{expires:a});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:g.sessionToken,options:{...t.cookies.sessionToken.options,expires:g.expires}});if(await (null==(U=m.signIn)?void 0:U.call(m,{user:l,account:w,isNewUser:u})),u&&p.newUser)return{redirect:"".concat(p.newUser).concat(p.newUser.includes("?")?"&":"?").concat(new URLSearchParams({callbackUrl:d})),cookies:n};return{redirect:d,cookies:n}}throw new f.InvalidProvider("Callback for provider type (".concat(c.type,") is not supported"))}catch(t){if(t instanceof f.AuthError)throw t;let e=new f.CallbackRouteError(t,{provider:c.id});throw w.debug("callback route error details",{method:a,query:i,body:o}),e}}async function or(e,t){let r,{signIn:n,redirect:i}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof f.AuthError)throw e;throw new f.AccessDenied(e)}if(!r)throw new f.AccessDenied("AccessDenied");if("string"==typeof r)return await i({url:r,baseUrl:t.url.origin})}async function on(e,t,r,n,i){var o,a,s,c,l,u,d;let{adapter:p,jwt:h,events:m,callbacks:y,logger:g,session:{strategy:b,maxAge:w}}=e,v={body:null,headers:{"Content-Type":"application/json",...!n&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:r},_=t.value;if(!_)return v;if("jwt"===b){try{let r=e.cookies.sessionToken.name,c=await h.decode({...h,token:_,salt:r});if(!c)throw Error("Invalid JWT");let l=await y.jwt({token:c,...n&&{trigger:"update"},session:i}),u=r9(w);if(null!==l){let e={user:{name:l.name,email:l.email,image:l.picture},expires:u.toISOString()},n=await y.session({session:e,token:l});v.body=n;let i=await h.encode({...h,token:l,salt:r}),s=t.chunk(i,{expires:u});null==(o=v.cookies)||o.push(...s),await (null==(a=m.session)?void 0:a.call(m,{session:n,token:l}))}else null==(s=v.cookies)||s.push(...t.clean())}catch(e){g.error(new f.JWTSessionError(e)),null==(c=v.cookies)||c.push(...t.clean())}return v}try{let{getSessionAndUser:r,deleteSession:o,updateSession:a}=p,s=await r(_);if(s&&s.session.expires.valueOf()<Date.now()&&(await o(_),s=null),s){let{user:t,session:r}=s,o=e.session.updateAge,c=r.expires.valueOf()-1e3*w+1e3*o,d=r9(w);c<=Date.now()&&await a({sessionToken:_,expires:d});let p=await y.session({session:{...r,user:t},user:t,newSession:i,...n?{trigger:"update"}:{}});v.body=p,null==(l=v.cookies)||l.push({name:e.cookies.sessionToken.name,value:_,options:{...e.cookies.sessionToken.options,expires:d}}),await (null==(u=m.session)?void 0:u.call(m,{session:p}))}else _&&(null==(d=v.cookies)||d.push(...t.clean()))}catch(e){g.error(new f.SessionTokenError(e))}return v}async function oi(e,t){var r,n,i,o,a,s,c;let l,u,{logger:d,provider:p}=t,h=null==(r=p.authorization)?void 0:r.url;if(!h||"authjs.dev"===h.host){let e=new URL(p.issuer),t=await nE(e,{[ns]:p[t0],[ni]:!0}),r=await nS(e,t).catch(t=>{if(!(t instanceof TypeError)||"Invalid URL"!==t.message)throw t;throw TypeError("Discovery request responded with an invalid issuer. expected: ".concat(e))});if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");h=new URL(r.authorization_endpoint)}let f=h.searchParams,m=p.callbackUrl;!t.isOnRedirectProxy&&p.redirectProxyUrl&&(m=p.redirectProxyUrl,u=p.callbackUrl,d.debug("using redirect proxy",{redirect_uri:m,data:u}));let y=Object.assign({response_type:"code",client_id:p.clientId,redirect_uri:m,...null==(n=p.authorization)?void 0:n.params},Object.fromEntries(null!=(s=null==(i=p.authorization)?void 0:i.url.searchParams)?s:[]),e);for(let e in y)f.set(e,y[e]);let g=[];(null==(o=p.authorization)?void 0:o.url.searchParams.get("response_mode"))==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let b=await iG.create(t,u);if(b&&(f.set("state",b.value),g.push(b.cookie)),null==(a=p.checks)?void 0:a.includes("pkce"))if(!l||(null==(c=l.code_challenge_methods_supported)?void 0:c.includes("S256"))){let{value:e,cookie:r}=await iz.create(t);f.set("code_challenge",e),f.set("code_challenge_method","S256"),g.push(r)}else"oidc"===p.type&&(p.checks=["nonce"]);let w=await iV.create(t);return w&&(f.set("nonce",w.value),g.push(w.cookie)),"oidc"!==p.type||h.searchParams.has("scope")||h.searchParams.set("scope","openid profile email"),d.debug("authorization url is ready",{url:h,cookies:g,provider:p}),{redirect:h.toString(),cookies:g}}async function oo(e,t){var r,n,i,o,a,s,c,l;let u,{body:d}=e,{provider:p,callbacks:h,adapter:m}=t,y=(null!=(i=p.normalizeIdentifier)?i:function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],"".concat(t,"@").concat(r)})(null==d?void 0:d.email),g={id:crypto.randomUUID(),email:y,emailVerified:null},b=null!=(o=await m.getUserByEmail(y))?o:g,w={providerAccountId:y,userId:b.id,type:"email",provider:p.id};try{u=await h.signIn({user:b,account:w,email:{verificationRequest:!0}})}catch(e){throw new f.AccessDenied(e)}if(!u)throw new f.AccessDenied("AccessDenied");if("string"==typeof u)return{redirect:await h.redirect({url:u,baseUrl:t.url.origin})};let{callbackUrl:v,theme:_}=t,E=null!=(a=await (null==(r=p.generateVerificationToken)?void 0:r.call(p)))?a:tG(32),k=new Date(Date.now()+(null!=(s=p.maxAge)?s:86400)*1e3),A=null!=(c=p.secret)?c:t.secret,S=new URL(t.basePath,t.url.origin),x=p.sendVerificationRequest({identifier:y,token:E,expires:k,url:"".concat(S,"/callback/").concat(p.id,"?").concat(new URLSearchParams({callbackUrl:v,token:E,email:y})),provider:p,theme:_,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(null!=(l=e.body)?l:{}):void 0})}),R=null==(n=m.createVerificationToken)?void 0:n.call(m,{identifier:y,token:await tF("".concat(E).concat(A)),expires:k});return await Promise.all([x,R]),{redirect:"".concat(S,"/verify-request?").concat(new URLSearchParams({provider:p.id,type:p.type}))}}async function oa(e,t,r){let n="".concat(r.url.origin).concat(r.basePath,"/signin");if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:i}=await oi(e.query,r);return i&&t.push(...i),{redirect:n,cookies:t}}case"email":return{...await oo(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function os(e,t,r){let{jwt:n,events:i,callbackUrl:o,logger:a,session:s}=r,c=t.value;if(!c)return{redirect:o,cookies:e};try{var l,u,d;if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await (null==(l=i.signOut)?void 0:l.call(i,{token:t}))}else{let e=await (null==(u=r.adapter)?void 0:u.deleteSession(c));await (null==(d=i.signOut)?void 0:d.call(i,{session:e}))}}catch(e){a.error(new f.SignOutError(e))}return e.push(...t.clean()),{redirect:o,cookies:e}}async function oc(e,t){let{adapter:r,jwt:n,session:{strategy:i}}=e,o=t.value;if(!o)return null;if("jwt"===i){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:o,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await (null==r?void 0:r.getSessionAndUser(o));if(e)return e.user}return null}async function ol(e,t,r,n){var i;let o=i8(t),{provider:a}=o,{action:s}=null!=(i=e.query)?i:{};if("register"!==s&&"authenticate"!==s&&void 0!==s)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let c=await oc(t,r),l=c?{user:c,exists:!0}:await a.getUserInfo(t,e),u=null==l?void 0:l.user;switch(function(e,t,r){let{user:n,exists:i=!1}=null!=r?r:{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===i)return"register";break;case void 0:if(!t)if(!n)return"authenticate";else if(i)return"authenticate";else return"register"}return null}(s,!!c,l)){case"authenticate":return i2(o,e,u,n);case"register":if("string"==typeof(null==u?void 0:u.email))return i1(o,e,u,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function ou(e,t){var r,n,i,o,a;let{action:s,providerId:c,error:l,method:u}=e,d=t.skipCSRFCheck===tZ,{options:p,cookies:m}=await t8({authOptions:t,action:s,providerId:c,url:e.url,callbackUrl:null!=(o=null==(r=e.body)?void 0:r.callbackUrl)?o:null==(n=e.query)?void 0:n.callbackUrl,csrfToken:null==(i=e.body)?void 0:i.csrfToken,cookies:e.cookies,isPost:"POST"===u,csrfDisabled:d}),y=new h(p.cookies.sessionToken,e.cookies,p.logger);if("GET"===u){let t=r8({...p,query:e.query,cookies:m});switch(s){case"callback":return await ot(e,p,y,m);case"csrf":return t.csrf(d,p,m);case"error":return t.error(l);case"providers":return t.providers(p.providers);case"session":return await on(p,y,m);case"signin":return t.signin(c,l);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await ol(e,p,y,m)}}else{let{csrfTokenVerified:t}=p;switch(s){case"callback":return"credentials"===p.provider.type&&tX(s,t),await ot(e,p,y,m);case"session":return tX(s,t),await on(p,y,m,!0,null==(a=e.body)?void 0:a.data);case"signin":return tX(s,t),await oa(e,m,p);case"signout":return tX(s,t),await os(m,y,p)}}throw new f.UnknownAction("Cannot handle action: ".concat(s))}function od(e,t,r,n,i){var o,a,s,c,l;let u,d=null==i?void 0:i.basePath,p=null!=(o=n.AUTH_URL)?o:n.NEXTAUTH_URL;if(p)u=new URL(p),d&&"/"!==d&&"/"!==u.pathname&&(u.pathname!==d&&tW(i).warn("env-url-basepath-mismatch"),u.pathname="/");else{let e=null!=(a=r.get("x-forwarded-host"))?a:r.get("host"),n=null!=(c=null!=(s=r.get("x-forwarded-proto"))?s:t)?c:"https",i=n.endsWith(":")?n:n+":";u=new URL("".concat(i,"//").concat(e))}let h=u.toString().replace(/\/$/,"");if(d){let t=null!=(l=null==d?void 0:d.replace(/(^\/|\/$)/g,""))?l:"";return new URL("".concat(h,"/").concat(t,"/").concat(e))}return new URL("".concat(h,"/").concat(e))}async function op(e,t){var r,n,i,o;let a=tW(t),s=await tB(e,t);if(!s)return Response.json("Bad request.",{status:400});let c=function(e,t){var r,n,i,o,a,s,c,l,u,d,h,k;let{url:A}=e,S=[];if(!m&&t.debug&&S.push("debug-enabled"),!t.trustHost)return new f.UntrustedHost("Host must be trusted. URL was: ".concat(e.url));if(!(null==(r=t.secret)?void 0:r.length))return new f.MissingSecret("Please define a `secret`");let x=null==(n=e.query)?void 0:n.callbackUrl;if(x&&!y(x,A.origin))return new f.InvalidCallbackUrl("Invalid callback URL. Received: ".concat(x));let{callbackUrl:R}=p(null!=(s=t.useSecureCookies)?s:"https:"===A.protocol),P=null==(a=e.cookies)?void 0:a[null!=(c=null==(o=t.cookies)||null==(i=o.callbackUrl)?void 0:i.name)?c:R.name];if(P&&!y(P,A.origin))return new f.InvalidCallbackUrl("Invalid callback URL. Received: ".concat(P));let T=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(null!=(u=t.issuer)?u:null==(l=t.options)?void 0:l.issuer)){let e,{authorization:r,token:n,userinfo:i}=t;if("string"==typeof r||(null==r?void 0:r.url)?"string"==typeof n||(null==n?void 0:n.url)?"string"==typeof i||(null==i?void 0:i.url)||(e="userinfo"):e="token":e="authorization",e)return new f.InvalidEndpoints('Provider "'.concat(t.id,'" is missing both `issuer` and `').concat(e,"` endpoint config. At least one of them is required"))}if("credentials"===t.type)g=!0;else if("email"===t.type)b=!0;else if("webauthn"===t.type){if(w=!0,t.simpleWebAuthnBrowserVersion&&(k=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(k)))return new f.AuthError('Invalid provider config for "'.concat(t.id,'": simpleWebAuthnBrowserVersion "').concat(t.simpleWebAuthnBrowserVersion,'" must be a valid semver string.'));if(t.enableConditionalUI){if(T)return new f.DuplicateConditionalUI("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(T=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new f.MissingWebAuthnAutocomplete('Provider "'.concat(t.id,"\" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param"))}}}if(g){let e=(null==(d=t.session)?void 0:d.strategy)==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new f.UnsupportedStrategy("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new f.MissingAuthorize("Must define an authorize() handler to use credentials authentication provider")}let{adapter:C,session:O}=t,U=[];if(b||(null==O?void 0:O.strategy)==="database"||!(null==O?void 0:O.strategy)&&C)if(b){if(!C)return new f.MissingAdapter("Email login requires an adapter");U.push(...v)}else{if(!C)return new f.MissingAdapter("Database session requires an adapter");U.push(..._)}if(w){if(null==(h=t.experimental)||!h.enableWebAuthn)return new f.ExperimentalFeatureNotEnabled("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(S.push("experimental-webauthn"),!C)return new f.MissingAdapter("WebAuthn requires an adapter");U.push(...E)}if(C){let e=U.filter(e=>!(e in C));if(e.length)return new f.MissingAdapterMethods("Required adapter methods were missing: ".concat(e.join(", ")))}return m||(m=!0),S}(s,t);if(Array.isArray(c))c.forEach(a.warn);else if(c){if(a.error(c),!new Set(["signin","signout","error","verify-request"]).has(s.action)||"GET"!==s.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:r}=t,i=(null==e?void 0:e.error)&&(null==(n=s.url.searchParams.get("callbackUrl"))?void 0:n.startsWith(e.error));if(!(null==e?void 0:e.error)||i)return i&&a.error(new f.ErrorPageLoop("The error page ".concat(null==e?void 0:e.error," should not require authentication"))),tz(r8({theme:r}).error("Configuration"));let o="".concat(s.url.origin).concat(e.error,"?error=Configuration");return Response.redirect(o)}let l=null==(r=e.headers)?void 0:r.has("X-Auth-Return-Redirect"),u=t.raw===tQ;try{let e=await ou(s,t);if(u)return e;let r=tz(e),n=r.headers.get("Location");if(!l||!n)return r;return Response.json({url:n},{headers:r.headers})}catch(h){a.error(h);let r=h instanceof f.AuthError;if(r&&u&&!l)throw h;if("POST"===e.method&&"session"===s.action)return Response.json(null,{status:400});let n=new URLSearchParams({error:(0,f.isClientError)(h)?h.type:"Configuration"});h instanceof f.CredentialsSignin&&n.set("code",h.code);let c=r&&h.kind||"error",d=null!=(o=null==(i=t.pages)?void 0:i[c])?o:"".concat(t.basePath,"/").concat(c.toLowerCase()),p="".concat(s.url.origin).concat(d,"?").concat(n);if(l)return Response.json({url:p});return Response.redirect(p)}}var oh=e.i(52120);function of(e){var t;let r=null!=(t=s.default.env.AUTH_URL)?t:s.default.env.NEXTAUTH_URL;if(!r)return e;let{origin:n}=new URL(r),{href:i,origin:o}=e.nextUrl;return new oh.NextRequest(i.replace(o,n),e)}function om(e){try{var t,r;null!=e.secret||(e.secret=null!=(t=s.default.env.AUTH_SECRET)?t:s.default.env.NEXTAUTH_SECRET);let n=null!=(r=s.default.env.AUTH_URL)?r:s.default.env.NEXTAUTH_URL;if(!n)return;let{pathname:i}=new URL(n);if("/"===i)return;e.basePath||(e.basePath=i)}catch(e){}finally{e.basePath||(e.basePath="/api/auth"),function(e,t){var r,n,i,o,a;let s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{let r=e.AUTH_URL;r&&(t.basePath?s||tW(t).warn("env-url-basepath-redundant"):t.basePath=new URL(r).pathname)}catch(e){}finally{null!=t.basePath||(t.basePath="/auth")}if(!(null==(r=t.secret)?void 0:r.length)){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e["AUTH_SECRET_".concat(n)];r&&t.secret.unshift(r)}}null!=t.redirectProxyUrl||(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),null!=t.trustHost||(t.trustHost=!!(null!=(a=null!=(o=null!=(i=null!=(n=e.AUTH_URL)?n:e.AUTH_TRUST_HOST)?i:e.VERCEL)?o:e.CF_PAGES)?a:"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),i=e["AUTH_".concat(n,"_ID")],o=e["AUTH_".concat(n,"_SECRET")],a=e["AUTH_".concat(n,"_ISSUER")],s=e["AUTH_".concat(n,"_KEY")],c="function"==typeof t?t({clientId:i,clientSecret:o,issuer:a,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(null!=c.clientId||(c.clientId=i),null!=c.clientSecret||(c.clientSecret=o),null!=c.issuer||(c.issuer=a)):"email"===c.type&&(null!=c.apiKey||(c.apiKey=s)),c})}(s.default.env,e,!0)}}var oy=e.i(42700);async function og(e,t){var r;return op(new Request(od("session",e.get("x-forwarded-proto"),e,s.default.env,t),{headers:{cookie:null!=(r=e.get("cookie"))?r:""}}),{...t,callbacks:{...t.callbacks,async session(){for(var e,r,n,i,o,a,s,c=arguments.length,l=Array(c),u=0;u<c;u++)l[u]=arguments[u];let d=null!=(a=await (null==(r=t.callbacks)||null==(e=r.session)?void 0:e.call(r,...l)))?a:{...l[0].session,expires:null!=(o=null==(i=l[0].session.expires)||null==(n=i.toISOString)?void 0:n.call(i))?o:l[0].session.expires};return{user:null!=(s=l[0].user)?s:l[0].token,...d}}}})}function ob(e){return"function"==typeof e}function ow(e,t){return"function"==typeof e?async function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];if(!n.length){let r=await (0,oy.headers)(),n=await e(void 0);return null==t||t(n),og(r,n).then(e=>e.json())}if(n[0]instanceof Request){let r=n[0],i=n[1],o=await e(r);return null==t||t(o),ov([r,i],o)}if(ob(n[0])){let r=n[0];return async function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];let a=await e(i[0]);return null==t||t(a),ov(i,a,r)}}let o="req"in n[0]?n[0].req:n[0],a="res"in n[0]?n[0].res:n[1],s=await e(o);return null==t||t(s),og(new Headers(o.headers),s).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in a?a.headers.append("set-cookie",t):a.appendHeader("set-cookie",t);return t})}:function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return Promise.resolve((0,oy.headers)()).then(t=>og(t,e).then(e=>e.json()));if(r[0]instanceof Request)return ov([r[0],r[1]],e);if(ob(r[0])){let t=r[0];return async function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];return ov(n,e,t).then(e=>e)}}let i="req"in r[0]?r[0].req:r[0],o="res"in r[0]?r[0].res:r[1];return og(new Headers(i.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in o?o.headers.append("set-cookie",t):o.appendHeader("set-cookie",t);return t})}}async function ov(e,t,r){var n,i,o,a,s;let c=of(e[0]),l=await og(c.headers,t),u=await l.json(),d=!0;(null==(n=t.callbacks)?void 0:n.authorized)&&(d=await t.callbacks.authorized({request:c,auth:u}));let p=null==(i=oh.NextResponse.next)?void 0:i.call(oh.NextResponse);if(d instanceof Response){p=d;let e=d.headers.get("Location"),{pathname:r}=c.nextUrl;e&&function(e,t,r){var n;let i=t.replace("".concat(e,"/"),""),o=Object.values(null!=(n=r.pages)?n:{});return(o_.has(i)||o.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(d=!0)}else if(r)c.auth=u,p=null!=(o=await r(c,e[1]))?o:oh.NextResponse.next();else if(!d){let e=null!=(s=null==(a=t.pages)?void 0:a.signIn)?s:"".concat(t.basePath,"/signin");if(c.nextUrl.pathname!==e){let t=c.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",c.nextUrl.href),p=oh.NextResponse.redirect(t)}}let h=new Response(null==p?void 0:p.body,p);for(let e of l.headers.getSetCookie())h.headers.append("set-cookie",e);return h}let o_=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var oE=e.i(57691);async function ok(e){var t,r,n,i,o;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},c=arguments.length>2?arguments[2]:void 0,l=arguments.length>3?arguments[3]:void 0,u=new Headers(await (0,oy.headers)()),{redirect:d=!0,redirectTo:p,...h}=a instanceof FormData?Object.fromEntries(a):a,f=null!=(r=null!=(t=null==p?void 0:p.toString())?t:u.get("Referer"))?r:"/",m=od("signin",u.get("x-forwarded-proto"),u,s.default.env,l);if(!e)return m.searchParams.append("callbackUrl",f),d&&(0,oE.redirect)(m.toString()),m.toString();let y="".concat(m,"/").concat(e,"?").concat(new URLSearchParams(c)),g={};for(let t of l.providers){let{options:r,...o}="function"==typeof t?t():t,a=null!=(n=null==r?void 0:r.id)?n:o.id;if(a===e){g={id:a,type:null!=(i=null==r?void 0:r.type)?i:o.type};break}}if(!g.id){let e="".concat(m,"?").concat(new URLSearchParams({callbackUrl:f}));return d&&(0,oE.redirect)(e),e}"credentials"===g.type&&(y=y.replace("signin","callback")),u.set("Content-Type","application/x-www-form-urlencoded");let b=new Request(y,{method:"POST",headers:u,body:new URLSearchParams({...h,callbackUrl:f})}),w=await op(b,{...l,raw:tQ,skipCSRFCheck:tZ}),v=await (0,oy.cookies)();for(let e of null!=(o=null==w?void 0:w.cookies)?o:[])v.set(e.name,e.value,e.options);let _=w instanceof Response?w.headers.get("Location"):w.redirect,E=null!=_?_:y;return d?(0,oE.redirect)(E):E}async function oA(e,t){var r,n,i,o;let a=new Headers(await (0,oy.headers)());a.set("Content-Type","application/x-www-form-urlencoded");let c=od("signout",a.get("x-forwarded-proto"),a,s.default.env,t),l=new URLSearchParams({callbackUrl:null!=(n=null!=(r=null==e?void 0:e.redirectTo)?r:a.get("Referer"))?n:"/"}),u=new Request(c,{method:"POST",headers:a,body:l}),d=await op(u,{...t,raw:tQ,skipCSRFCheck:tZ}),p=await (0,oy.cookies)();for(let e of null!=(i=null==d?void 0:d.cookies)?i:[])p.set(e.name,e.value,e.options);return null==(o=null==e?void 0:e.redirect)||o?(0,oE.redirect)(d.redirect):d}async function oS(e,t){var r;let n=new Headers(await (0,oy.headers)());n.set("Content-Type","application/json");let i=new Request(od("session",n.get("x-forwarded-proto"),n,s.default.env,t),{method:"POST",headers:n,body:JSON.stringify({data:e})}),o=await op(i,{...t,raw:tQ,skipCSRFCheck:tZ}),a=await (0,oy.cookies)();for(let e of null!=(r=null==o?void 0:o.cookies)?r:[])a.set(e.name,e.value,e.options);return o.body}var ox=e.i(45086),oR=e.i(41428);let oP=ox.z.object({username:ox.z.string().min(1,"Username is required"),password:ox.z.string().min(1,"Password is required")}),{handlers:oT,signIn:oC,signOut:oO,auth:oU}=function(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return om(r),op(of(t),r)};return{handlers:{GET:t,POST:t},auth:ow(e,e=>om(e)),signIn:async(t,r,n)=>{let i=await e(void 0);return om(i),ok(t,r,n,i)},signOut:async t=>{let r=await e(void 0);return om(r),oA(t,r)},unstable_update:async t=>{let r=await e(void 0);return om(r),oS(t,r)}}}om(e);let t=t=>op(of(t),e);return{handlers:{GET:t,POST:t},auth:ow(e),signIn:(t,r,n)=>ok(t,r,n,e),signOut:t=>oA(t,e),unstable_update:t=>oS(t,e)}}({providers:[{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:{name:"credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){try{let{username:t,password:r}=oP.parse(e),n=await oj.login({username:t,password:r}),i=(0,oR.decodeJwt)(n.access_token);if(!i)return null;let o=i.payload;return{id:o.sub,username:o.username,role:o.role,accessToken:n.access_token}}catch(e){return console.error("Erreur d'authentification:",e),null}}}}],pages:{signIn:"/auth/signin"},callbacks:{authorized:e=>{let{auth:t}=e;return!!t},async jwt(e){let{token:t,user:r}=e;return r&&(t.username=r.username,t.role=r.role,t.accessToken=r.accessToken),t},async session(e){let{session:t,token:r}=e;return r&&(t.user.id=r.sub||"",t.user.username=r.username,t.user.role=r.role,t.accessToken=r.accessToken),t}},session:{strategy:"jwt"}}),oI=s.default.env.NEXT_PUBLIC_API_URL||"http://localhost:4000",oj=new class{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat(this.baseUrl).concat(e),n={headers:{"Content-Type":"application/json",...t.headers},...t};try{let e=await fetch(r,n);if(!e.ok){let t=await e.json().catch(()=>({message:"Une erreur est survenue",statusCode:e.status}));throw 401===e.status&&await (0,l.signOut)({callbackUrl:"/auth/signin"}),Error(t.message||"HTTP ".concat(e.status))}return await e.json()}catch(e){if(console.log(e),e instanceof Error)throw e;throw Error("Erreur de connexion au serveur")}}async login(e){return this.request("/auth/login",{method:"POST",body:JSON.stringify(e)})}async register(e){return this.request("/auth/register",{method:"POST",body:JSON.stringify(e)})}async authenticatedRequest(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.request(e,{...r,headers:{...r.headers,"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}})}async getUsers(e){return this.authenticatedRequest("/users",e)}async getUser(e,t){return this.authenticatedRequest("/users/".concat(e),t)}constructor(e=oI){c(this,"baseUrl",void 0),this.baseUrl=e}}}]);