import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { useApi } from "@/hooks/use-api";
import type { Session, CreateSessionDto, UpdateSessionDto } from "@/types";

// Query Keys
export const sessionKeys = {
	all: ["sessions"] as const,
	lists: () => [...sessionKeys.all, "list"] as const,
	list: (filters: Record<string, any>) =>
		[...sessionKeys.lists(), { filters }] as const,
	details: () => [...sessionKeys.all, "detail"] as const,
	detail: (id: string) => [...sessionKeys.details(), id] as const,
	members: (id: string) => [...sessionKeys.detail(id), "members"] as const,
};

// Hooks
export function useSessions() {
	const { data: session } = useSession();
	const api = useApi();

	return useQuery({
		queryKey: sessionKeys.lists(),
		queryFn: () => api.getSessions(),
		enabled: !!session?.accessToken,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
}

export function useSessionDetail(sessionId: string) {
	const { data: session } = useSession();
	const api = useApi();

	return useQuery({
		queryKey: sessionKeys.detail(sessionId),
		queryFn: () => api.getSession(sessionId),
		enabled: !!session?.accessToken && !!sessionId,
		staleTime: 5 * 60 * 1000, // 5 minutes
	});
}

export function useSessionMembers(sessionId: string) {
	const { data: session } = useSession();
	const api = useApi();

	return useQuery({
		queryKey: sessionKeys.members(sessionId),
		queryFn: () => api.getSessionMembers(sessionId),
		enabled: !!session?.accessToken && !!sessionId,
		staleTime: 2 * 60 * 1000, // 2 minutes
	});
}

// Mutations
export function useCreateSession() {
	const queryClient = useQueryClient();
	const api = useApi();

	return useMutation({
		mutationFn: (data: CreateSessionDto) => api.createSession(data),
		onSuccess: () => {
			// Invalidate and refetch sessions list
			queryClient.invalidateQueries({ queryKey: sessionKeys.lists() });
		},
		onError: (error) => {
			console.error("Error creating session:", error);
		},
	});
}

export function useUpdateSession() {
	const queryClient = useQueryClient();
	const api = useApi();

	return useMutation({
		mutationFn: ({ id, data }: { id: string; data: UpdateSessionDto }) =>
			api.updateSession(id, data),
		onSuccess: (_, { id }) => {
			// Invalidate specific session and sessions list
			queryClient.invalidateQueries({ queryKey: sessionKeys.detail(id) });
			queryClient.invalidateQueries({ queryKey: sessionKeys.lists() });
		},
		onError: (error) => {
			console.error("Error updating session:", error);
		},
	});
}

export function useDeleteSession() {
	const queryClient = useQueryClient();
	const api = useApi();

	return useMutation({
		mutationFn: (sessionId: string) => api.deleteSession(sessionId),
		onSuccess: (_, sessionId) => {
			// Remove from cache and invalidate lists
			queryClient.removeQueries({ queryKey: sessionKeys.detail(sessionId) });
			queryClient.invalidateQueries({ queryKey: sessionKeys.lists() });
		},
		onError: (error) => {
			console.error("Error deleting session:", error);
		},
	});
}

export function useAddSessionMember() {
	const queryClient = useQueryClient();
	const api = useApi();

	return useMutation({
		mutationFn: (data: {
			sessionId: string;
			memberId: string;
			partFixe: number;
		}) =>
			api.addSessionMember({
				sessionId: data.sessionId,
				memberId: data.memberId,
				partFixe: data.partFixe,
			}),
		onSuccess: (_, { sessionId }) => {
			// Invalidate session members
			queryClient.invalidateQueries({
				queryKey: sessionKeys.members(sessionId),
			});
		},
		onError: (error) => {
			console.error("Error adding session member:", error);
		},
	});
}

export function useRemoveSessionMember() {
	const queryClient = useQueryClient();
	const api = useApi();

	return useMutation({
		mutationFn: ({
			sessionId,
			memberId,
		}: {
			sessionId: string;
			memberId: string;
		}) => api.removeSessionMember(sessionId, memberId),
		onSuccess: (_, { sessionId }) => {
			// Invalidate session members
			queryClient.invalidateQueries({
				queryKey: sessionKeys.members(sessionId),
			});
		},
		onError: (error) => {
			console.error("Error removing session member:", error);
		},
	});
}
