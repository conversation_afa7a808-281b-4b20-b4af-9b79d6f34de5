import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { useApi } from '@/hooks/use-api';
import type { Caisse, CreateCaisseDto, UpdateCaisseDto } from '@/types';

// Query Keys
export const caisseKeys = {
  all: ['caisses'] as const,
  lists: () => [...caisseKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...caisseKeys.lists(), { filters }] as const,
  details: () => [...caisseKeys.all, 'detail'] as const,
  detail: (id: string) => [...caisseKeys.details(), id] as const,
};

// Hooks
export function useCaisses() {
  const { data: session } = useSession();
  const api = useApi();

  return useQuery({
    queryKey: caisseKeys.lists(),
    queryFn: () => api.getCaisses(),
    enabled: !!session?.accessToken,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCaisse(caisseId: string) {
  const { data: session } = useSession();
  const api = useApi();

  return useQuery({
    queryKey: caisseKeys.detail(caisseId),
    queryFn: () => api.getCaisse(caisseId),
    enabled: !!session?.accessToken && !!caisseId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Mutations
export function useCreateCaisse() {
  const queryClient = useQueryClient();
  const api = useApi();

  return useMutation({
    mutationFn: (data: CreateCaisseDto) => api.createCaisse(data),
    onSuccess: () => {
      // Invalidate and refetch caisses list
      queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });
    },
    onError: (error) => {
      console.error('Error creating caisse:', error);
    },
  });
}

export function useUpdateCaisse() {
  const queryClient = useQueryClient();
  const api = useApi();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateCaisseDto }) => 
      api.updateCaisse(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate specific caisse and caisses list
      queryClient.invalidateQueries({ queryKey: caisseKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });
    },
    onError: (error) => {
      console.error('Error updating caisse:', error);
    },
  });
}

export function useDeleteCaisse() {
  const queryClient = useQueryClient();
  const api = useApi();

  return useMutation({
    mutationFn: (caisseId: string) => api.deleteCaisse(caisseId),
    onSuccess: (_, caisseId) => {
      // Remove from cache and invalidate lists
      queryClient.removeQueries({ queryKey: caisseKeys.detail(caisseId) });
      queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });
    },
    onError: (error) => {
      console.error('Error deleting caisse:', error);
    },
  });
}
