"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { DollarSign, Calendar, Wallet, Plus, ArrowRight } from "lucide-react";
import { useApi } from "@/hooks/use-api";
import { type Session, type Caisse, CaisseType } from "@/types";

export default function DashboardPage() {
	const { data: session, status } = useSession();
	const api = useApi();

	const [sessions, setSessions] = useState<Session[]>([]);
	const [caisses, setCaisses] = useState<Caisse[]>([]);
	const [loading, setLoading] = useState(true);

	// Vérifier les permissions
	const canViewSessions =
		session?.user &&
		["secretary_general", "controller", "cashier"].includes((session.user as any).role);
	const canViewCaisses =
		session?.user &&
		["secretary_general", "controller", "cashier"].includes((session.user as any).role);
	const canCreateSessions =
		session?.user && (session.user as any).role === "secretary_general";
	const canCreateCaisses =
		session?.user && (session.user as any).role === "secretary_general";

	// Charger les données
	useEffect(() => {
		let isMounted = true;

		const loadData = async () => {
			if (!session?.accessToken) return;

			// Permissions calculées ici
			const role = (session.user as any)?.role;
			const canViewSessions = ["secretary_general", "controller", "cashier"].includes(role);
			const canViewCaisses = ["secretary_general", "controller", "cashier"].includes(role);

			if (!canViewSessions && !canViewCaisses) {
				setLoading(false);
				return;
			}

			try {
				setLoading(true);
				const promises = [];

				if (canViewSessions) promises.push(api.getSessions());
				if (canViewCaisses) promises.push(api.getCaisses());

				const results = await Promise.all(promises);

				let idx = 0;
				if (canViewSessions && isMounted)
					setSessions((results[idx++] as Session[]) || []);
				if (canViewCaisses && isMounted)
					setCaisses((results[idx++] as Caisse[]) || []);
			} catch (err) {
				console.error(err);
			} finally {
				if (isMounted) setLoading(false);
			}
		};

		if (status === "authenticated") {
			loadData();
		} else if (status === "unauthenticated") {
			setLoading(false);
		}

		return () => {
			isMounted = false;
		};
	}, [status]);

	// Calculer les statistiques
	const now = new Date();
	const activeSessions = sessions.filter(
		(s) => new Date(s.dateDebut) <= now && new Date(s.dateFin) >= now,
	);
	const totalSolde = caisses.reduce((sum, c) => sum + c.soldeActuel, 0);
	const caissesPrincipales = caisses.filter(
		(c) => c.type === CaisseType.PRINCIPALE,
	);
	const caissesReunions = caisses.filter((c) => c.type === CaisseType.REUNION);

	if (loading) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
					<p className="mt-2 text-sm text-gray-600">
						Chargement du tableau de bord...
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Welcome section */}
			<div>
				<h1 className="text-2xl font-bold text-gray-900">
					Bienvenue, {(session?.user as any)?.username}!
				</h1>
				<p className="text-gray-600 mt-1">
					Voici un aperçu de votre tontine aujourd'hui.
				</p>
				{session?.user && (
					<div className="mt-2 text-sm text-gray-500">
						Connecté en tant que{" "}
						<span className="font-medium">{(session.user).username}</span> (
						{(session.user).role})
					</div>
				)}
			</div>

			{/* Stats grid */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				{canViewSessions && (
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Sessions Totales
							</CardTitle>
							<Calendar className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{sessions.length}</div>
							<p className="text-xs text-muted-foreground">
								{activeSessions.length} actives
							</p>
						</CardContent>
					</Card>
				)}

				{canViewSessions && (
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Sessions Actives
							</CardTitle>
							<Calendar className="h-4 w-4 text-green-600" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-green-600">
								{activeSessions.length}
							</div>
							<p className="text-xs text-muted-foreground">
								En cours actuellement
							</p>
						</CardContent>
					</Card>
				)}

				{canViewCaisses && (
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Total Caisses
							</CardTitle>
							<Wallet className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{caisses.length}</div>
							<p className="text-xs text-muted-foreground">
								{caissesPrincipales.length} principales,{" "}
								{caissesReunions.length} réunions
							</p>
						</CardContent>
					</Card>
				)}

				{canViewCaisses && (
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">Solde Total</CardTitle>
							<DollarSign className="h-4 w-4 text-green-600" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-green-600">
								{totalSolde.toLocaleString()} FCFA
							</div>
							<p className="text-xs text-muted-foreground">
								Toutes caisses confondues
							</p>
						</CardContent>
					</Card>
				)}
			</div>

			{/* Actions rapides */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{canViewSessions && (
					<Card>
						<CardHeader className="flex flex-row items-center justify-between">
							<div>
								<CardTitle>Sessions Récentes</CardTitle>
								<CardDescription>Dernières sessions créées</CardDescription>
							</div>
							{canCreateSessions && (
								<Button asChild size="sm">
									<Link href="/dashboard/sessions/new">
										<Plus className="mr-2 h-4 w-4" />
										Nouvelle
									</Link>
								</Button>
							)}
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								{sessions.slice(0, 4).map((session) => (
									<div
										key={session._id}
										className="flex items-center justify-between"
									>
										<div>
											<p className="text-sm font-medium text-gray-900">
												Session {session.annee}
											</p>
											<p className="text-xs text-gray-500">
												{new Date(session.dateDebut).toLocaleDateString()} -{" "}
												{new Date(session.dateFin).toLocaleDateString()}
											</p>
										</div>
										<div className="text-sm font-medium text-blue-600">
											{session.partFixe.toLocaleString()} FCFA
										</div>
									</div>
								))}
								{sessions.length === 0 && (
									<p className="text-sm text-muted-foreground text-center py-4">
										Aucune session trouvée
									</p>
								)}
								{sessions.length > 0 && (
									<div className="pt-2">
										<Button
											variant="outline"
											size="sm"
											asChild
											className="w-full"
										>
											<Link href="/dashboard/sessions">
												Voir toutes les sessions
												<ArrowRight className="ml-2 h-4 w-4" />
											</Link>
										</Button>
									</div>
								)}
							</div>
						</CardContent>
					</Card>
				)}

				{canViewCaisses && (
					<Card>
						<CardHeader className="flex flex-row items-center justify-between">
							<div>
								<CardTitle>Caisses Récentes</CardTitle>
								<CardDescription>Dernières caisses créées</CardDescription>
							</div>
							{canCreateCaisses && (
								<Button asChild size="sm">
									<Link href="/dashboard/caisses/new">
										<Plus className="mr-2 h-4 w-4" />
										Nouvelle
									</Link>
								</Button>
							)}
						</CardHeader>
						<CardContent>
							<div className="space-y-4">
								{caisses.slice(0, 4).map((caisse) => (
									<div
										key={caisse._id}
										className="flex items-center justify-between"
									>
										<div>
											<p className="text-sm font-medium text-gray-900">
												{caisse.nom}
											</p>
											<p className="text-xs text-gray-500">
												{caisse.type === CaisseType.PRINCIPALE
													? "Principale"
													: "Réunion"}
											</p>
										</div>
										<div
											className={`text-sm font-medium ${
												caisse.soldeActuel > 0
													? "text-green-600"
													: "text-gray-500"
											}`}
										>
											{caisse.soldeActuel.toLocaleString()} FCFA
										</div>
									</div>
								))}
								{caisses.length === 0 && (
									<p className="text-sm text-muted-foreground text-center py-4">
										Aucune caisse trouvée
									</p>
								)}
								{caisses.length > 0 && (
									<div className="pt-2">
										<Button
											variant="outline"
											size="sm"
											asChild
											className="w-full"
										>
											<Link href="/dashboard/caisses">
												Voir toutes les caisses
												<ArrowRight className="ml-2 h-4 w-4" />
											</Link>
										</Button>
									</div>
								)}
							</div>
						</CardContent>
					</Card>
				)}
			</div>
		</div>
	);
}
