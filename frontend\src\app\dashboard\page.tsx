import { DashboardClient } from "@/components/dashboard/dashboard-client";
import { ApiService } from "@/lib/api";
import { auth } from "@/lib/auth";
import { cookies } from "next/headers";

// Server-side data fetching
async function getDashboardData() {
	try {
		const session = await auth();

		if (!session) {
			return { sessions: [], caisses: [] };
		}

		// Create API service instance for server-side fetching
		const api = new ApiService();

		// Fetch initial data on the server
		const [sessions, caisses] = await Promise.all([
			api.getSessions(session.accessToken).catch(() => []),
			api.getCaisses(session.accessToken).catch(() => []),
		]);

		return { sessions, caisses };
	} catch (error) {
		console.error("Error fetching dashboard data:", error);
		return { sessions: [], caisses: [] };
	}
}

export default async function DashboardPage() {
	// Fetch initial data on the server
	const { sessions, caisses } = await getDashboardData();

	// Return the client component with initial data
	return (
		<DashboardClient initialSessions={sessions} initialCaisses={caisses} />
	);
}
