import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsPositive, IsString } from 'class-validator';
import { CaisseType } from '../schemas/caisse.schema';

export class CreateCaisseDto {
  @ApiProperty({ example: 'Caisse Principale' })
  @IsString()
  @IsNotEmpty()
  nom!: string;

  @ApiProperty({ enum: CaisseType })
  @IsEnum(CaisseType)
  type!: CaisseType;

  @ApiPropertyOptional({ example: 0 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  soldeActuel?: number;

  @ApiPropertyOptional({ description: 'Required if type = REUNION' })
  @IsOptional()
  @IsMongoId()
  sessionId?: string;

  @ApiPropertyOptional({ description: 'Linked principal caisse for emargement (REUNION)' })
  @IsOptional()
  @IsMongoId()
  caissePrincipaleId?: string;

  @ApiPropertyOptional({ description: 'Cashier user id (REUNION)' })
  @IsOptional()
  @IsMongoId()
  cashierId?: string;
}