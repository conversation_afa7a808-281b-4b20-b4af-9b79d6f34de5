import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsNumber, IsPositive, IsString } from 'class-validator';

export class ExtractionDto {
  @ApiProperty({ description: 'le montant à verser dans la petite caisse' })
  @IsNumber()
  @IsPositive()
  montant!: number;

  @ApiProperty({ description: 'la caisse principale dans laquelle on retire montant vers exterieur' })
  @IsMongoId()
  caissePrincipale!: string;

  @ApiProperty({ description: 'la raison à extraction de la caisse principale' })
  @IsString()
  reason!: string;
}