(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,1269,53838,e=>{"use strict";e.s(["SessionContext",()=>et,"SessionProvider",()=>el,"__NEXTAUTH",()=>J,"getCsrfToken",()=>en,"getProviders",()=>er,"getSession",()=>es,"signIn",()=>ea,"signOut",()=>eo,"useSession",()=>ei],1269);var t,i,s,n,r,a=e.i(50460),o=e.i(4051),l=e.i(38477);e.s(["AccessDenied",()=>d,"AccountNotLinked",()=>K,"AdapterError",()=>h,"AuthError",()=>u,"CallbackRouteError",()=>f,"CredentialsSignin",()=>_,"DuplicateConditionalUI",()=>W,"ErrorPageLoop",()=>p,"EventError",()=>y,"ExperimentalFeatureNotEnabled",()=>N,"InvalidCallbackUrl",()=>g,"InvalidCheck",()=>m,"InvalidEndpoints",()=>b,"InvalidProvider",()=>U,"JWTSessionError",()=>w,"MissingAdapter",()=>S,"MissingAdapterMethods",()=>E,"MissingAuthorize",()=>M,"MissingCSRF",()=>Q,"MissingSecret",()=>O,"MissingWebAuthnAutocomplete",()=>j,"OAuthAccountNotLinked",()=>k,"OAuthCallbackError",()=>A,"OAuthProfileParseError",()=>P,"SessionTokenError",()=>T,"SignOutError",()=>C,"UnknownAction",()=>x,"UnsupportedStrategy",()=>R,"UntrustedHost",()=>F,"Verification",()=>L,"WebAuthnVerificationError",()=>I,"isClientError",()=>q],53838);class u extends Error{constructor(e,t){var i,s,n,r;e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=null!=(n=this.constructor.type)?n:"AuthError",this.kind=null!=(r=this.constructor.kind)?r:"error",null==(i=(s=Error).captureStackTrace)||i.call(s,this,this.constructor);let a="https://errors.authjs.dev#".concat(this.type.toLowerCase());this.message+="".concat(this.message?". ":"","Read more at ").concat(a)}}class c extends u{}c.kind="signIn";class h extends u{}h.type="AdapterError";class d extends u{}d.type="AccessDenied";class f extends u{}f.type="CallbackRouteError";class p extends u{}p.type="ErrorPageLoop";class y extends u{}y.type="EventError";class g extends u{}g.type="InvalidCallbackUrl";class _ extends c{constructor(){super(...arguments),this.code="credentials"}}_.type="CredentialsSignin";class b extends u{}b.type="InvalidEndpoints";class m extends u{}m.type="InvalidCheck";class w extends u{}w.type="JWTSessionError";class S extends u{}S.type="MissingAdapter";class E extends u{}E.type="MissingAdapterMethods";class M extends u{}M.type="MissingAuthorize";class O extends u{}O.type="MissingSecret";class k extends c{}k.type="OAuthAccountNotLinked";class A extends c{}A.type="OAuthCallbackError";class P extends u{}P.type="OAuthProfileParseError";class T extends u{}T.type="SessionTokenError";class C extends u{}C.type="SignOutError";class x extends u{}x.type="UnknownAction";class R extends u{}R.type="UnsupportedStrategy";class U extends u{}U.type="InvalidProvider";class F extends u{}F.type="UntrustedHost";class L extends u{}L.type="Verification";class Q extends c{}Q.type="MissingCSRF";let D=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);function q(e){return e instanceof u&&D.has(e.type)}class W extends u{}W.type="DuplicateConditionalUI";class j extends u{}j.type="MissingWebAuthnAutocomplete";class I extends u{}I.type="WebAuthnVerificationError";class K extends c{}K.type="AccountNotLinked";class N extends u{}N.type="ExperimentalFeatureNotEnabled";class H extends u{}class G extends u{}async function B(e,t,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n="".concat(V(t),"/").concat(e);try{var r;let e={headers:{"Content-Type":"application/json",...(null==s||null==(r=s.headers)?void 0:r.cookie)?{cookie:s.headers.cookie}:{}}};(null==s?void 0:s.body)&&(e.body=JSON.stringify(s.body),e.method="POST");let t=await fetch(n,e),i=await t.json();if(!t.ok)throw i;return i}catch(e){return i.error(new H(e.message,e)),null}}function V(e){return"undefined"==typeof window?"".concat(e.baseUrlServer).concat(e.basePathServer):e.basePath}function X(){return Math.floor(Date.now()/1e3)}function z(e){let t=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e="https://".concat(e));let i=new URL(e||t),s=("/"===i.pathname?t.pathname:i.pathname).replace(/\/$/,""),n="".concat(i.origin).concat(s);return{origin:i.origin,host:i.host,path:s,base:n,toString:()=>n}}let J={baseUrl:z(null!=(i=a.default.env.NEXTAUTH_URL)?i:a.default.env.VERCEL_URL).origin,basePath:z(a.default.env.NEXTAUTH_URL).path,baseUrlServer:z(null!=(n=null!=(s=a.default.env.NEXTAUTH_URL_INTERNAL)?s:a.default.env.NEXTAUTH_URL)?n:a.default.env.VERCEL_URL).origin,basePathServer:z(null!=(r=a.default.env.NEXTAUTH_URL_INTERNAL)?r:a.default.env.NEXTAUTH_URL).path,_lastSync:0,_session:void 0,_getSession:()=>{}},Z=null;function $(){return"undefined"==typeof BroadcastChannel?{postMessage:()=>{},addEventListener:()=>{},removeEventListener:()=>{},name:"next-auth",onmessage:null,onmessageerror:null,close:()=>{},dispatchEvent:()=>!1}:new BroadcastChannel("next-auth")}function Y(){return null===Z&&(Z=$()),Z}let ee={debug:console.debug,error:console.error,warn:console.warn},et=null==(t=l.createContext)?void 0:t.call(l,void 0);function ei(e){if(!et)throw Error("React Context is unavailable in Server Components");let t=l.useContext(et),{required:i,onUnauthenticated:s}=null!=e?e:{},n=i&&"unauthenticated"===t.status;return(l.useEffect(()=>{if(n){let e="".concat(J.basePath,"/signin?").concat(new URLSearchParams({error:"SessionRequired",callbackUrl:window.location.href}));s?s():window.location.href=e}},[n,s]),n)?{data:t.data,update:t.update,status:"loading"}:t}async function es(e){var t;let i=await B("session",J,ee,e);return(null==(t=null==e?void 0:e.broadcast)||t)&&$().postMessage({event:"session",data:{trigger:"getSession"}}),i}async function en(){var e;let t=await B("csrf",J,ee);return null!=(e=null==t?void 0:t.csrfToken)?e:""}async function er(){return B("providers",J,ee)}async function ea(e,t,i){var s,n,r;let{callbackUrl:a,...o}=null!=t?t:{},{redirect:l=!0,redirectTo:u=null!=a?a:window.location.href,...c}=o,h=V(J),d=await er();if(!d){window.location.href="".concat(h,"/error");return}if(!e||!d[e]){let e="".concat(h,"/signin?").concat(new URLSearchParams({callbackUrl:u}));window.location.href=e;return}let f=d[e].type;if("webauthn"===f)throw TypeError(['Provider id "'.concat(e,'" refers to a WebAuthn provider.'),'Please use `import { signIn } from "next-auth/webauthn"` instead.'].join("\n"));let p="".concat(h,"/").concat("credentials"===f?"callback":"signin","/").concat(e),y=await en(),g=await fetch("".concat(p,"?").concat(new URLSearchParams(i)),{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({...c,csrfToken:y,callbackUrl:u})}),_=await g.json();if(l){let e=null!=(s=_.url)?s:u;window.location.href=e,e.includes("#")&&window.location.reload();return}let b=null!=(n=new URL(_.url).searchParams.get("error"))?n:void 0,m=null!=(r=new URL(_.url).searchParams.get("code"))?r:void 0;return g.ok&&await J._getSession({event:"storage"}),{error:b,code:m,status:g.status,ok:g.ok,url:b?null:_.url}}async function eo(e){var t,i;let{redirect:s=!0,redirectTo:n=null!=(t=null==e?void 0:e.callbackUrl)?t:window.location.href}=null!=e?e:{},r=V(J),a=await en(),o=await fetch("".concat(r,"/signout"),{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({csrfToken:a,callbackUrl:n})}),l=await o.json();if(Y().postMessage({event:"session",data:{trigger:"signout"}}),s){let e=null!=(i=l.url)?i:n;window.location.href=e,e.includes("#")&&window.location.reload();return}return await J._getSession({event:"storage"}),l}function el(e){if(!et)throw Error("React Context is unavailable in Server Components");let{children:t,basePath:i,refetchInterval:s,refetchWhenOffline:n}=e;i&&(J.basePath=i);let r=void 0!==e.session;J._lastSync=r?X():0;let[a,u]=l.useState(()=>(r&&(J._session=e.session),e.session)),[c,h]=l.useState(!r);l.useEffect(()=>(J._getSession=async function(){let{event:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t="storage"===e;if(t||void 0===J._session){J._lastSync=X(),J._session=await es({broadcast:!t}),u(J._session);return}if(!e||null===J._session||X()<J._lastSync)return;J._lastSync=X(),J._session=await es(),u(J._session)}catch(e){ee.error(new G(e.message,e))}finally{h(!1)}},J._getSession(),()=>{J._lastSync=0,J._session=void 0,J._getSession=()=>{}}),[]),l.useEffect(()=>{let e=()=>J._getSession({event:"storage"});return Y().addEventListener("message",e),()=>Y().removeEventListener("message",e)},[]),l.useEffect(()=>{let{refetchOnWindowFocus:t=!0}=e,i=()=>{t&&"visible"===document.visibilityState&&J._getSession({event:"visibilitychange"})};return document.addEventListener("visibilitychange",i,!1),()=>document.removeEventListener("visibilitychange",i,!1)},[e.refetchOnWindowFocus]);let d=function(){let[e,t]=l.useState("undefined"!=typeof navigator&&navigator.onLine),i=()=>t(!0),s=()=>t(!1);return l.useEffect(()=>(window.addEventListener("online",i),window.addEventListener("offline",s),()=>{window.removeEventListener("online",i),window.removeEventListener("offline",s)}),[]),e}(),f=!1!==n||d;l.useEffect(()=>{if(s&&f){let e=setInterval(()=>{J._session&&J._getSession({event:"poll"})},1e3*s);return()=>clearInterval(e)}},[s,f]);let p=l.useMemo(()=>({data:a,status:c?"loading":a?"authenticated":"unauthenticated",async update(e){if(c)return;h(!0);let t=await B("session",J,ee,void 0===e?void 0:{body:{csrfToken:await en(),data:e}});return h(!1),t&&(u(t),Y().postMessage({event:"session",data:{trigger:"getSession"}})),t}}),[a,c]);return(0,o.jsx)(et.Provider,{value:p,children:t})}},61816,65497,40003,76233,25839,22e3,e=>{"use strict";function t(e,t,i){if(!t.has(e))throw TypeError("attempted to "+i+" private field on non-instance");return t.get(e)}function i(e,i){var s=t(e,i,"get");return s.get?s.get.call(e):s.value}function s(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}function n(e,t,i){s(e,t),t.set(e,i)}function r(e,i,s){var n=t(e,i,"set");if(n.set)n.set.call(e,s);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=s}return s}function a(e,t,i){if(!t.has(e))throw TypeError("attempted to get private field on non-instance");return i}function o(e,t){s(e,t),t.add(e)}e.s(["_",()=>i],61816),e.s(["_",()=>t],65497),e.s(["_",()=>n],40003),e.s(["_",()=>r],76233),e.s(["_",()=>a],25839),e.s(["_",()=>o],22e3)},1660,e=>{"use strict";e.s(["QueryClientProvider",()=>r,"useQueryClient",()=>n]);var t=e.i(38477),i=e.i(4051),s=t.createContext(void 0),n=e=>{let i=t.useContext(s);if(e)return e;if(!i)throw Error("No QueryClient set, use QueryClientProvider to set one");return i},r=e=>{let{client:n,children:r}=e;return t.useEffect(()=>(n.mount(),()=>{n.unmount()}),[n]),(0,i.jsx)(s.Provider,{value:n,children:r})}},76252,20800,e=>{"use strict";e.s(["addToEnd",()=>T,"addToStart",()=>C,"ensureQueryFn",()=>R,"functionalUpdate",()=>h,"hashKey",()=>m,"hashQueryKeyByOptions",()=>b,"isServer",()=>u,"isValidTimeout",()=>d,"matchMutation",()=>_,"matchQuery",()=>g,"noop",()=>c,"partialMatchKey",()=>w,"replaceData",()=>P,"resolveEnabled",()=>y,"resolveStaleTime",()=>p,"shallowEqualObjects",()=>E,"shouldThrowError",()=>U,"skipToken",()=>x,"sleep",()=>A,"timeUntilStale",()=>f],76252),e.s(["systemSetTimeoutZero",()=>l,"timeoutManager",()=>o],20800);var t,i,s=e.i(61816),n=e.i(40003),r=e.i(76233),a={setTimeout:(e,t)=>setTimeout(e,t),clearTimeout:e=>clearTimeout(e),setInterval:(e,t)=>setInterval(e,t),clearInterval:e=>clearInterval(e)},o=new(t=new WeakMap,i=new WeakMap,class{setTimeoutProvider(e){(0,r._)(this,t,e)}setTimeout(e,i){return(0,s._)(this,t).setTimeout(e,i)}clearTimeout(e){(0,s._)(this,t).clearTimeout(e)}setInterval(e,i){return(0,s._)(this,t).setInterval(e,i)}clearInterval(e){(0,s._)(this,t).clearInterval(e)}constructor(){(0,n._)(this,t,{writable:!0,value:a}),(0,n._)(this,i,{writable:!0,value:!1})}});function l(e){setTimeout(e,0)}var u="undefined"==typeof window||"Deno"in globalThis;function c(){}function h(e,t){return"function"==typeof e?e(t):e}function d(e){return"number"==typeof e&&e>=0&&e!==1/0}function f(e,t){return Math.max(e+(t||0)-Date.now(),0)}function p(e,t){return"function"==typeof e?e(t):e}function y(e,t){return"function"==typeof e?e(t):e}function g(e,t){let{type:i="all",exact:s,fetchStatus:n,predicate:r,queryKey:a,stale:o}=e;if(a){if(s){if(t.queryHash!==b(a,t.options))return!1}else if(!w(t.queryKey,a))return!1}if("all"!==i){let e=t.isActive();if("active"===i&&!e||"inactive"===i&&e)return!1}return("boolean"!=typeof o||t.isStale()===o)&&(!n||n===t.state.fetchStatus)&&(!r||!!r(t))}function _(e,t){let{exact:i,status:s,predicate:n,mutationKey:r}=e;if(r){if(!t.options.mutationKey)return!1;if(i){if(m(t.options.mutationKey)!==m(r))return!1}else if(!w(t.options.mutationKey,r))return!1}return(!s||t.state.status===s)&&(!n||!!n(t))}function b(e,t){return((null==t?void 0:t.queryKeyHashFn)||m)(e)}function m(e){return JSON.stringify(e,(e,t)=>O(t)?Object.keys(t).sort().reduce((e,i)=>(e[i]=t[i],e),{}):t)}function w(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(i=>w(e[i],t[i]))}var S=Object.prototype.hasOwnProperty;function E(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(let i in e)if(e[i]!==t[i])return!1;return!0}function M(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function O(e){if(!k(e))return!1;let t=e.constructor;if(void 0===t)return!0;let i=t.prototype;return!!k(i)&&!!i.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function k(e){return"[object Object]"===Object.prototype.toString.call(e)}function A(e){return new Promise(t=>{o.setTimeout(t,e)})}function P(e,t,i){return"function"==typeof i.structuralSharing?i.structuralSharing(e,t):!1!==i.structuralSharing?function e(t,i){if(t===i)return t;let s=M(t)&&M(i);if(!s&&!(O(t)&&O(i)))return i;let n=(s?t:Object.keys(t)).length,r=s?i:Object.keys(i),a=r.length,o=s?Array(a):{},l=0;for(let u=0;u<a;u++){let a=s?u:r[u],c=t[a],h=i[a];if(c===h){o[a]=c,(s?u<n:S.call(t,a))&&l++;continue}if(null===c||null===h||"object"!=typeof c||"object"!=typeof h){o[a]=h;continue}let d=e(c,h);o[a]=d,d===c&&l++}return n===a&&l===n?t:o}(e,t):t}function T(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=[...e,t];return i&&s.length>i?s.slice(1):s}function C(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=[t,...e];return i&&s.length>i?s.slice(0,-1):s}var x=Symbol();function R(e,t){return!e.queryFn&&(null==t?void 0:t.initialPromise)?()=>t.initialPromise:e.queryFn&&e.queryFn!==x?e.queryFn:()=>Promise.reject(Error("Missing queryFn: '".concat(e.queryHash,"'")))}function U(e,t){return"function"==typeof e?e(...t):!!e}},27399,e=>{"use strict";e.s(["notifyManager",()=>i]);var t=e.i(20800).systemSetTimeoutZero,i=function(){let e=[],i=0,s=e=>{e()},n=e=>{e()},r=t,a=t=>{i?e.push(t):r(()=>{s(t)})};return{batch:t=>{let a;i++;try{a=t()}finally{--i||(()=>{let t=e;e=[],t.length&&r(()=>{n(()=>{t.forEach(e=>{s(e)})})})})()}return a},batchCalls:e=>function(){for(var t=arguments.length,i=Array(t),s=0;s<t;s++)i[s]=arguments[s];a(()=>{e(...i)})},schedule:a,setNotifyFunction:e=>{s=e},setBatchNotifyFunction:e=>{n=e},setScheduler:e=>{r=e}}}()},26797,e=>{"use strict";e.s(["Subscribable",()=>t]);var t=class{subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}}},53779,e=>{"use strict";e.s(["focusManager",()=>u]);var t,i,s,n=e.i(61816),r=e.i(40003),a=e.i(76233),o=e.i(26797),l=e.i(76252),u=new(t=new WeakMap,i=new WeakMap,s=new WeakMap,class extends o.Subscribable{onSubscribe(){(0,n._)(this,i)||this.setEventListener((0,n._)(this,s))}onUnsubscribe(){var e;this.hasListeners()||(null==(e=(0,n._)(this,i))||e.call(this),(0,a._)(this,i,void 0))}setEventListener(e){var t;(0,a._)(this,s,e),null==(t=(0,n._)(this,i))||t.call(this),(0,a._)(this,i,e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){(0,n._)(this,t)!==e&&((0,a._)(this,t,e),this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){var e;return"boolean"==typeof(0,n._)(this,t)?(0,n._)(this,t):(null==(e=globalThis.document)?void 0:e.visibilityState)!=="hidden"}constructor(){super(),(0,r._)(this,t,{writable:!0,value:void 0}),(0,r._)(this,i,{writable:!0,value:void 0}),(0,r._)(this,s,{writable:!0,value:void 0}),(0,a._)(this,s,e=>{if(!l.isServer&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}})}})},75335,86487,99905,6108,14887,e=>{"use strict";e.s(["Query",()=>x,"fetchState",()=>R],75335),e.i(50460);var t,i,s,n,r,a,o,l,u,c,h,d,f=e.i(61816),p=e.i(40003),y=e.i(76233),g=e.i(25839),_=e.i(22e3),b=e.i(76252),m=e.i(27399);e.s(["CancelledError",()=>A,"canFetch",()=>k,"createRetryer",()=>P],6108);var w=e.i(53779);e.s(["onlineManager",()=>E],86487);var S=e.i(26797),E=new(t=new WeakMap,i=new WeakMap,s=new WeakMap,class extends S.Subscribable{onSubscribe(){(0,f._)(this,i)||this.setEventListener((0,f._)(this,s))}onUnsubscribe(){var e;this.hasListeners()||(null==(e=(0,f._)(this,i))||e.call(this),(0,y._)(this,i,void 0))}setEventListener(e){var t;(0,y._)(this,s,e),null==(t=(0,f._)(this,i))||t.call(this),(0,y._)(this,i,e(this.setOnline.bind(this)))}setOnline(e){(0,f._)(this,t)!==e&&((0,y._)(this,t,e),this.listeners.forEach(t=>{t(e)}))}isOnline(){return(0,f._)(this,t)}constructor(){super(),(0,p._)(this,t,{writable:!0,value:!0}),(0,p._)(this,i,{writable:!0,value:void 0}),(0,p._)(this,s,{writable:!0,value:void 0}),(0,y._)(this,s,e=>{if(!b.isServer&&window.addEventListener){let t=()=>e(!0),i=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",i,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",i)}}})}});function M(){let e,t,i=new Promise((i,s)=>{e=i,t=s});function s(e){Object.assign(i,e),delete i.resolve,delete i.reject}return i.status="pending",i.catch(()=>{}),i.resolve=t=>{s({status:"fulfilled",value:t}),e(t)},i.reject=e=>{s({status:"rejected",reason:e}),t(e)},i}function O(e){return Math.min(1e3*2**e,3e4)}function k(e){return(null!=e?e:"online")!=="online"||E.isOnline()}e.s(["pendingThenable",()=>M],99905);var A=class extends Error{constructor(e){super("CancelledError"),this.revert=null==e?void 0:e.revert,this.silent=null==e?void 0:e.silent}};function P(e){let t,i=!1,s=0,n=M(),r=()=>w.focusManager.isFocused()&&("always"===e.networkMode||E.isOnline())&&e.canRun(),a=()=>k(e.networkMode)&&e.canRun(),o=e=>{"pending"===n.status&&(null==t||t(),n.resolve(e))},l=e=>{"pending"===n.status&&(null==t||t(),n.reject(e))},u=()=>new Promise(i=>{var s;t=e=>{("pending"!==n.status||r())&&i(e)},null==(s=e.onPause)||s.call(e)}).then(()=>{if(t=void 0,"pending"===n.status){var i;null==(i=e.onContinue)||i.call(e)}}),c=()=>{let t;if("pending"!==n.status)return;let a=0===s?e.initialPromise:void 0;try{t=null!=a?a:e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(o).catch(t=>{var a,o,h;if("pending"!==n.status)return;let d=null!=(o=e.retry)?o:3*!b.isServer,f=null!=(h=e.retryDelay)?h:O,p="function"==typeof f?f(s,t):f,y=!0===d||"number"==typeof d&&s<d||"function"==typeof d&&d(s,t);if(i||!y)return void l(t);s++,null==(a=e.onFail)||a.call(e,s,t),(0,b.sleep)(p).then(()=>r()?void 0:u()).then(()=>{i?l(t):c()})})};return{promise:n,status:()=>n.status,cancel:t=>{if("pending"===n.status){var i;let s=new A(t);l(s),null==(i=e.onCancel)||i.call(e,s)}},continue:()=>(null==t||t(),n),cancelRetry:()=>{i=!0},continueRetry:()=>{i=!1},canStart:a,start:()=>(a()?c():u().then(c),n)}}e.s(["Removable",()=>C],14887);var T=e.i(20800),C=(n=new WeakMap,class{destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,b.isValidTimeout)(this.gcTime)&&(0,y._)(this,n,T.timeoutManager.setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,null!=e?e:b.isServer?1/0:3e5)}clearGcTimeout(){(0,f._)(this,n)&&(T.timeoutManager.clearTimeout((0,f._)(this,n)),(0,y._)(this,n,void 0))}constructor(){(0,p._)(this,n,{writable:!0,value:void 0})}}),x=(r=new WeakMap,a=new WeakMap,o=new WeakMap,l=new WeakMap,u=new WeakMap,c=new WeakMap,h=new WeakMap,d=new WeakSet,class extends C{get meta(){return this.options.meta}get promise(){var e;return null==(e=(0,f._)(this,u))?void 0:e.promise}setOptions(e){if(this.options={...(0,f._)(this,c),...e},this.updateGcTime(this.options.gcTime),this.state&&void 0===this.state.data){let e=U(this.options);void 0!==e.data&&(this.setData(e.data,{updatedAt:e.dataUpdatedAt,manual:!0}),(0,y._)(this,r,e))}}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||(0,f._)(this,o).remove(this)}setData(e,t){let i=(0,b.replaceData)(this.state.data,e,this.options);return(0,g._)(this,d,F).call(this,{data:i,type:"success",dataUpdatedAt:null==t?void 0:t.updatedAt,manual:null==t?void 0:t.manual}),i}setState(e,t){(0,g._)(this,d,F).call(this,{type:"setState",state:e,setStateOptions:t})}cancel(e){var t,i;let s=null==(t=(0,f._)(this,u))?void 0:t.promise;return null==(i=(0,f._)(this,u))||i.cancel(e),s?s.then(b.noop).catch(b.noop):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState((0,f._)(this,r))}isActive(){return this.observers.some(e=>!1!==(0,b.resolveEnabled)(e.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===b.skipToken||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(e=>"static"===(0,b.resolveStaleTime)(e.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return void 0===this.state.data||"static"!==e&&(!!this.state.isInvalidated||!(0,b.timeUntilStale)(this.state.dataUpdatedAt,e))}onFocus(){var e;let t=this.observers.find(e=>e.shouldFetchOnWindowFocus());null==t||t.refetch({cancelRefetch:!1}),null==(e=(0,f._)(this,u))||e.continue()}onOnline(){var e;let t=this.observers.find(e=>e.shouldFetchOnReconnect());null==t||t.refetch({cancelRefetch:!1}),null==(e=(0,f._)(this,u))||e.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),(0,f._)(this,o).notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||((0,f._)(this,u)&&((0,f._)(this,h)?(0,f._)(this,u).cancel({revert:!0}):(0,f._)(this,u).cancelRetry()),this.scheduleGc()),(0,f._)(this,o).notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||(0,g._)(this,d,F).call(this,{type:"invalidate"})}async fetch(e,t){var i,s,n,r,c,p,_,m,w,S,E,M;if("idle"!==this.state.fetchStatus&&(null==(i=(0,f._)(this,u))?void 0:i.status())!=="rejected"){if(void 0!==this.state.data&&(null==t?void 0:t.cancelRefetch))this.cancel({silent:!0});else if((0,f._)(this,u))return(0,f._)(this,u).continueRetry(),(0,f._)(this,u).promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let O=new AbortController,k=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>((0,y._)(this,h,!0),O.signal)})},T=()=>{let e=(0,b.ensureQueryFn)(this.options,t),i=(()=>{let e={client:(0,f._)(this,l),queryKey:this.queryKey,meta:this.meta};return k(e),e})();return((0,y._)(this,h,!1),this.options.persister)?this.options.persister(e,i,this):e(i)},C=(()=>{let e={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:(0,f._)(this,l),state:this.state,fetchFn:T};return k(e),e})();null==(s=this.options.behavior)||s.onFetch(C,this),(0,y._)(this,a,this.state),("idle"===this.state.fetchStatus||this.state.fetchMeta!==(null==(n=C.fetchOptions)?void 0:n.meta))&&(0,g._)(this,d,F).call(this,{type:"fetch",meta:null==(r=C.fetchOptions)?void 0:r.meta}),(0,y._)(this,u,P({initialPromise:null==t?void 0:t.initialPromise,fn:C.fetchFn,onCancel:e=>{e instanceof A&&e.revert&&this.setState({...(0,f._)(this,a),fetchStatus:"idle"}),O.abort()},onFail:(e,t)=>{(0,g._)(this,d,F).call(this,{type:"failed",failureCount:e,error:t})},onPause:()=>{(0,g._)(this,d,F).call(this,{type:"pause"})},onContinue:()=>{(0,g._)(this,d,F).call(this,{type:"continue"})},retry:C.options.retry,retryDelay:C.options.retryDelay,networkMode:C.options.networkMode,canRun:()=>!0}));try{let e=await (0,f._)(this,u).start();if(void 0===e)throw Error("".concat(this.queryHash," data is undefined"));return this.setData(e),null==(c=(p=(0,f._)(this,o).config).onSuccess)||c.call(p,e,this),null==(_=(m=(0,f._)(this,o).config).onSettled)||_.call(m,e,this.state.error,this),e}catch(e){if(e instanceof A){if(e.silent)return(0,f._)(this,u).promise;else if(e.revert){if(void 0===this.state.data)throw e;return this.state.data}}throw(0,g._)(this,d,F).call(this,{type:"error",error:e}),null==(w=(S=(0,f._)(this,o).config).onError)||w.call(S,e,this),null==(E=(M=(0,f._)(this,o).config).onSettled)||E.call(M,this.state.data,e,this),e}finally{this.scheduleGc()}}constructor(e){var t;super(),(0,_._)(this,d),(0,p._)(this,r,{writable:!0,value:void 0}),(0,p._)(this,a,{writable:!0,value:void 0}),(0,p._)(this,o,{writable:!0,value:void 0}),(0,p._)(this,l,{writable:!0,value:void 0}),(0,p._)(this,u,{writable:!0,value:void 0}),(0,p._)(this,c,{writable:!0,value:void 0}),(0,p._)(this,h,{writable:!0,value:void 0}),(0,y._)(this,h,!1),(0,y._)(this,c,e.defaultOptions),this.setOptions(e.options),this.observers=[],(0,y._)(this,l,e.client),(0,y._)(this,o,(0,f._)(this,l).getQueryCache()),this.queryKey=e.queryKey,this.queryHash=e.queryHash,(0,y._)(this,r,U(this.options)),this.state=null!=(t=e.state)?t:(0,f._)(this,r),this.scheduleGc()}});function R(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:k(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}function U(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,i=void 0!==t,s=i?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:i?null!=s?s:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:i?"success":"pending",fetchStatus:"idle"}}function F(e){let t=t=>{var i,s;switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...R(t.data,this.options),fetchMeta:null!=(i=e.meta)?i:null};case"success":let n={...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:null!=(s=e.dataUpdatedAt)?s:Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};return(0,y._)(this,a,e.manual?n:void 0),n;case"error":let r=e.error;return{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}};this.state=t(this.state),m.notifyManager.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),(0,f._)(this,o).notify({query:this,type:"updated",action:e})})}},86784,e=>{"use strict";e.s(["Mutation",()=>f,"getDefaultState",()=>p]);var t,i,s,n,r=e.i(61816),a=e.i(40003),o=e.i(76233),l=e.i(25839),u=e.i(22e3),c=e.i(27399),h=e.i(14887),d=e.i(6108),f=(t=new WeakMap,i=new WeakMap,s=new WeakMap,n=new WeakSet,class extends h.Removable{setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){(0,r._)(this,t).includes(e)||((0,r._)(this,t).push(e),this.clearGcTimeout(),(0,r._)(this,i).notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){(0,o._)(this,t,(0,r._)(this,t).filter(t=>t!==e)),this.scheduleGc(),(0,r._)(this,i).notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){(0,r._)(this,t).length||("pending"===this.state.status?this.scheduleGc():(0,r._)(this,i).remove(this))}continue(){var e,t;return null!=(t=null==(e=(0,r._)(this,s))?void 0:e.continue())?t:this.execute(this.state.variables)}async execute(e){var t,a,u,c,h,f,p,g,_,b,m,w,S,E,M,O,k,A,P,T,C;let x=()=>{(0,l._)(this,n,y).call(this,{type:"continue"})};(0,o._)(this,s,(0,d.createRetryer)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{(0,l._)(this,n,y).call(this,{type:"failed",failureCount:e,error:t})},onPause:()=>{(0,l._)(this,n,y).call(this,{type:"pause"})},onContinue:x,retry:null!=(t=this.options.retry)?t:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>(0,r._)(this,i).canRun(this)}));let R="pending"===this.state.status,U=!(0,r._)(this,s).canStart();try{if(R)x();else{(0,l._)(this,n,y).call(this,{type:"pending",variables:e,isPaused:U}),await (null==(b=(m=(0,r._)(this,i).config).onMutate)?void 0:b.call(m,e,this));let t=await (null==(w=(S=this.options).onMutate)?void 0:w.call(S,e));t!==this.state.context&&(0,l._)(this,n,y).call(this,{type:"pending",context:t,variables:e,isPaused:U})}let t=await (0,r._)(this,s).start();return await (null==(a=(u=(0,r._)(this,i).config).onSuccess)?void 0:a.call(u,t,e,this.state.context,this)),await (null==(c=(h=this.options).onSuccess)?void 0:c.call(h,t,e,this.state.context)),await (null==(f=(p=(0,r._)(this,i).config).onSettled)?void 0:f.call(p,t,null,this.state.variables,this.state.context,this)),await (null==(g=(_=this.options).onSettled)?void 0:g.call(_,t,null,e,this.state.context)),(0,l._)(this,n,y).call(this,{type:"success",data:t}),t}catch(t){try{throw await (null==(E=(M=(0,r._)(this,i).config).onError)?void 0:E.call(M,t,e,this.state.context,this)),await (null==(O=(k=this.options).onError)?void 0:O.call(k,t,e,this.state.context)),await (null==(A=(P=(0,r._)(this,i).config).onSettled)?void 0:A.call(P,void 0,t,this.state.variables,this.state.context,this)),await (null==(T=(C=this.options).onSettled)?void 0:T.call(C,void 0,t,e,this.state.context)),t}finally{(0,l._)(this,n,y).call(this,{type:"error",error:t})}}finally{(0,r._)(this,i).runNext(this)}}constructor(e){super(),(0,u._)(this,n),(0,a._)(this,t,{writable:!0,value:void 0}),(0,a._)(this,i,{writable:!0,value:void 0}),(0,a._)(this,s,{writable:!0,value:void 0}),this.mutationId=e.mutationId,(0,o._)(this,i,e.mutationCache),(0,o._)(this,t,[]),this.state=e.state||p(),this.setOptions(e.options),this.scheduleGc()}});function p(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}function y(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),c.notifyManager.batch(()=>{(0,r._)(this,t).forEach(t=>{t.onMutationUpdate(e)}),(0,r._)(this,i).notify({mutation:this,type:"updated",action:e})})}},75102,e=>{"use strict";let t;e.s(["default",()=>W],75102);var i,s,n,r,a,o,l,u,c,h,d,f,p=e.i(4051),y=e.i(1269),g=e.i(1660);e.i(50460);var _=function(){return null},b=e.i(38477),m=e.i(61816),w=e.i(40003),S=e.i(76233),E=e.i(65497);function M(e,t){var i=(0,E._)(e,t,"update");if(i.set){if(!i.get)throw TypeError("attempted to read set only private field");return"__destrWrapper"in i||(i.__destrWrapper={set value(v){i.set.call(e,v)},get value(){return i.get.call(e)}}),i.__destrWrapper}if(!i.writable)throw TypeError("attempted to set read only private field");return i}var O=e.i(76252),k=e.i(75335),A=e.i(27399),P=e.i(26797),T=(i=new WeakMap,class extends P.Subscribable{build(e,t,i){var s;let n=t.queryKey,r=null!=(s=t.queryHash)?s:(0,O.hashQueryKeyByOptions)(n,t),a=this.get(r);return a||(a=new k.Query({client:e,queryKey:n,queryHash:r,options:e.defaultQueryOptions(t),state:i,defaultOptions:e.getQueryDefaults(n)}),this.add(a)),a}add(e){(0,m._)(this,i).has(e.queryHash)||((0,m._)(this,i).set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=(0,m._)(this,i).get(e.queryHash);t&&(e.destroy(),t===e&&(0,m._)(this,i).delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){A.notifyManager.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return(0,m._)(this,i).get(e)}getAll(){return[...(0,m._)(this,i).values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,O.matchQuery)(t,e))}findAll(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,O.matchQuery)(e,t)):t}notify(e){A.notifyManager.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){A.notifyManager.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){A.notifyManager.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}constructor(e={}){super(),(0,w._)(this,i,{writable:!0,value:void 0}),this.config=e,(0,S._)(this,i,new Map)}}),C=e.i(86784),x=P,R=(s=new WeakMap,n=new WeakMap,r=new WeakMap,class extends x.Subscribable{build(e,t,i){let s=new C.Mutation({mutationCache:this,mutationId:++M(this,r).value,options:e.defaultMutationOptions(t),state:i});return this.add(s),s}add(e){(0,m._)(this,s).add(e);let t=U(e);if("string"==typeof t){let i=(0,m._)(this,n).get(t);i?i.push(e):(0,m._)(this,n).set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if((0,m._)(this,s).delete(e)){let t=U(e);if("string"==typeof t){let i=(0,m._)(this,n).get(t);if(i)if(i.length>1){let t=i.indexOf(e);-1!==t&&i.splice(t,1)}else i[0]===e&&(0,m._)(this,n).delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=U(e);if("string"!=typeof t)return!0;{let i=(0,m._)(this,n).get(t),s=null==i?void 0:i.find(e=>"pending"===e.state.status);return!s||s===e}}runNext(e){let t=U(e);if("string"!=typeof t)return Promise.resolve();{var i,s;let r=null==(i=(0,m._)(this,n).get(t))?void 0:i.find(t=>t!==e&&t.state.isPaused);return null!=(s=null==r?void 0:r.continue())?s:Promise.resolve()}}clear(){A.notifyManager.batch(()=>{(0,m._)(this,s).forEach(e=>{this.notify({type:"removed",mutation:e})}),(0,m._)(this,s).clear(),(0,m._)(this,n).clear()})}getAll(){return Array.from((0,m._)(this,s))}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,O.matchMutation)(t,e))}findAll(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.getAll().filter(t=>(0,O.matchMutation)(e,t))}notify(e){A.notifyManager.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return A.notifyManager.batch(()=>Promise.all(e.map(e=>e.continue().catch(O.noop))))}constructor(e={}){super(),(0,w._)(this,s,{writable:!0,value:void 0}),(0,w._)(this,n,{writable:!0,value:void 0}),(0,w._)(this,r,{writable:!0,value:void 0}),this.config=e,(0,S._)(this,s,new Set),(0,S._)(this,n,new Map),(0,S._)(this,r,0)}});function U(e){var t;return null==(t=e.options.scope)?void 0:t.id}var F=e.i(53779),L=e.i(86487);function Q(e){return{onFetch:(t,i)=>{var s,n,r,a,o;let l=t.options,u=null==(r=t.fetchOptions)||null==(n=r.meta)||null==(s=n.fetchMore)?void 0:s.direction,c=(null==(a=t.state.data)?void 0:a.pages)||[],h=(null==(o=t.state.data)?void 0:o.pageParams)||[],d={pages:[],pageParams:[]},f=0,p=async()=>{let i=!1,s=(0,O.ensureQueryFn)(t.options,t.fetchOptions),n=async(e,n,r)=>{if(i)return Promise.reject();if(null==n&&e.pages.length)return Promise.resolve(e);let a=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:n,direction:r?"backward":"forward",meta:t.options.meta};return Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?i=!0:t.signal.addEventListener("abort",()=>{i=!0}),t.signal)}),e})(),o=await s(a),{maxPages:l}=t.options,u=r?O.addToStart:O.addToEnd;return{pages:u(e.pages,o,l),pageParams:u(e.pageParams,n,l)}};if(u&&c.length){let e="backward"===u,t={pages:c,pageParams:h},i=(e?function(e,t){var i;let{pages:s,pageParams:n}=t;return s.length>0?null==(i=e.getPreviousPageParam)?void 0:i.call(e,s[0],s,n[0],n):void 0}:D)(l,t);d=await n(t,i,e)}else{let t=null!=e?e:c.length;do{var r;let e=0===f?null!=(r=h[0])?r:l.initialPageParam:D(l,d);if(f>0&&null==e)break;d=await n(d,e),f++}while(f<t)}return d};t.options.persister?t.fetchFn=()=>{var e,s;return null==(e=(s=t.options).persister)?void 0:e.call(s,p,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},i)}:t.fetchFn=p}}}function D(e,t){let{pages:i,pageParams:s}=t,n=i.length-1;return i.length>0?e.getNextPageParam(i[n],i,s[n],s):void 0}var q=(a=new WeakMap,o=new WeakMap,l=new WeakMap,u=new WeakMap,c=new WeakMap,h=new WeakMap,d=new WeakMap,f=new WeakMap,class{mount(){M(this,h).value++,1===(0,m._)(this,h)&&((0,S._)(this,d,F.focusManager.subscribe(async e=>{e&&(await this.resumePausedMutations(),(0,m._)(this,a).onFocus())})),(0,S._)(this,f,L.onlineManager.subscribe(async e=>{e&&(await this.resumePausedMutations(),(0,m._)(this,a).onOnline())})))}unmount(){var e,t;M(this,h).value--,0===(0,m._)(this,h)&&(null==(e=(0,m._)(this,d))||e.call(this),(0,S._)(this,d,void 0),null==(t=(0,m._)(this,f))||t.call(this),(0,S._)(this,f,void 0))}isFetching(e){return(0,m._)(this,a).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return(0,m._)(this,o).findAll({...e,status:"pending"}).length}getQueryData(e){var t;let i=this.defaultQueryOptions({queryKey:e});return null==(t=(0,m._)(this,a).get(i.queryHash))?void 0:t.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),i=(0,m._)(this,a).build(this,t),s=i.state.data;return void 0===s?this.fetchQuery(e):(e.revalidateIfStale&&i.isStaleByTime((0,O.resolveStaleTime)(t.staleTime,i))&&this.prefetchQuery(t),Promise.resolve(s))}getQueriesData(e){return(0,m._)(this,a).findAll(e).map(e=>{let{queryKey:t,state:i}=e;return[t,i.data]})}setQueryData(e,t,i){let s=this.defaultQueryOptions({queryKey:e}),n=(0,m._)(this,a).get(s.queryHash),r=null==n?void 0:n.state.data,o=(0,O.functionalUpdate)(t,r);if(void 0!==o)return(0,m._)(this,a).build(this,s).setData(o,{...i,manual:!0})}setQueriesData(e,t,i){return A.notifyManager.batch(()=>(0,m._)(this,a).findAll(e).map(e=>{let{queryKey:s}=e;return[s,this.setQueryData(s,t,i)]}))}getQueryState(e){var t;let i=this.defaultQueryOptions({queryKey:e});return null==(t=(0,m._)(this,a).get(i.queryHash))?void 0:t.state}removeQueries(e){let t=(0,m._)(this,a);A.notifyManager.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let i=(0,m._)(this,a);return A.notifyManager.batch(()=>(i.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i={revert:!0,...t};return Promise.all(A.notifyManager.batch(()=>(0,m._)(this,a).findAll(e).map(e=>e.cancel(i)))).then(O.noop).catch(O.noop)}invalidateQueries(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return A.notifyManager.batch(()=>{var i,s;return((0,m._)(this,a).findAll(e).forEach(e=>{e.invalidate()}),(null==e?void 0:e.refetchType)==="none")?Promise.resolve():this.refetchQueries({...e,type:null!=(s=null!=(i=null==e?void 0:e.refetchType)?i:null==e?void 0:e.type)?s:"active"},t)})}refetchQueries(e){var t;let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s={...i,cancelRefetch:null==(t=i.cancelRefetch)||t};return Promise.all(A.notifyManager.batch(()=>(0,m._)(this,a).findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,s);return s.throwOnError||(t=t.catch(O.noop)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(O.noop)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let i=(0,m._)(this,a).build(this,t);return i.isStaleByTime((0,O.resolveStaleTime)(t.staleTime,i))?i.fetch(t):Promise.resolve(i.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(O.noop).catch(O.noop)}fetchInfiniteQuery(e){return e.behavior=Q(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(O.noop).catch(O.noop)}ensureInfiniteQueryData(e){return e.behavior=Q(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return L.onlineManager.isOnline()?(0,m._)(this,o).resumePausedMutations():Promise.resolve()}getQueryCache(){return(0,m._)(this,a)}getMutationCache(){return(0,m._)(this,o)}getDefaultOptions(){return(0,m._)(this,l)}setDefaultOptions(e){(0,S._)(this,l,e)}setQueryDefaults(e,t){(0,m._)(this,u).set((0,O.hashKey)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...(0,m._)(this,u).values()],i={};return t.forEach(t=>{(0,O.partialMatchKey)(e,t.queryKey)&&Object.assign(i,t.defaultOptions)}),i}setMutationDefaults(e,t){(0,m._)(this,c).set((0,O.hashKey)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...(0,m._)(this,c).values()],i={};return t.forEach(t=>{(0,O.partialMatchKey)(e,t.mutationKey)&&Object.assign(i,t.defaultOptions)}),i}defaultQueryOptions(e){if(e._defaulted)return e;let t={...(0,m._)(this,l).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,O.hashQueryKeyByOptions)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===O.skipToken&&(t.enabled=!1),t}defaultMutationOptions(e){return(null==e?void 0:e._defaulted)?e:{...(0,m._)(this,l).mutations,...(null==e?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){(0,m._)(this,a).clear(),(0,m._)(this,o).clear()}constructor(e={}){(0,w._)(this,a,{writable:!0,value:void 0}),(0,w._)(this,o,{writable:!0,value:void 0}),(0,w._)(this,l,{writable:!0,value:void 0}),(0,w._)(this,u,{writable:!0,value:void 0}),(0,w._)(this,c,{writable:!0,value:void 0}),(0,w._)(this,h,{writable:!0,value:void 0}),(0,w._)(this,d,{writable:!0,value:void 0}),(0,w._)(this,f,{writable:!0,value:void 0}),(0,S._)(this,a,e.queryCache||new T),(0,S._)(this,o,e.mutationCache||new R),(0,S._)(this,l,e.defaultOptions||{}),(0,S._)(this,u,new Map),(0,S._)(this,c,new Map),(0,S._)(this,h,0)}});let W=function(e){let{children:i}=e,[s]=(0,b.useState)(()=>(t||(t=new q({defaultOptions:{queries:{staleTime:6e4,gcTime:3e5,retry:(e,t)=>{if(t instanceof Error&&"status"in t){let e=t.status;if(e>=400&&e<500)return!1}return e<3},retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:!1,onError:e=>{console.error("Mutation error:",e)}}}})),t));return(0,p.jsx)(g.QueryClientProvider,{client:s,children:(0,p.jsxs)(y.SessionProvider,{children:[i,(0,p.jsx)(_,{initialIsOpen:!1,buttonPosition:"bottom-right"})]})})}}]);