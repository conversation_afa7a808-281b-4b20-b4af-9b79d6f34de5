module.exports=[9322,a=>{"use strict";a.s(["default",()=>C],9322);var b=a.i(68116),c=a.i(128),d=a.i(50395),e=a.i(81223),f=a.i(6821),g=a.i(91486),h=a.i(6601),i=a.i(33055),j=a.i(2979),k=a.i(75780),l=a.i(20283),m=a.i(66423),n=a.i(55667),n=n,o=a.i(22171);let p=m.Root,q=m.Trigger,r=m.Portal;m.Close;let s=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(m.Overlay,{ref:d,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...c}));s.displayName=m.Overlay.displayName;let t=c.forwardRef(({className:a,children:c,...d},e)=>(0,b.jsxs)(r,{children:[(0,b.jsx)(s,{}),(0,b.jsxs)(m.Content,{ref:e,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...d,children:[c,(0,b.jsxs)(m.Close,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,b.jsx)(n.default,{className:"h-4 w-4"}),(0,b.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));t.displayName=m.Content.displayName;let u=({className:a,...c})=>(0,b.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...c});u.displayName="DialogHeader";let v=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(m.Title,{ref:d,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",a),...c}));v.displayName=m.Title.displayName;let w=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(m.Description,{ref:d,className:(0,o.cn)("text-sm text-muted-foreground",a),...c}));w.displayName=m.Description.displayName;var x=a.i(44932),y=a.i(78184),z=a.i(41405),A=a.i(12594),B=a.i(78655);function C(){let a=(0,d.useParams)();(0,d.useRouter)();let{data:m}=(0,e.useSession)(),n=(0,A.useApi)(),[o,r]=(0,c.useState)(null),[s,C]=(0,c.useState)([]),[D,E]=(0,c.useState)([]),[F,G]=(0,c.useState)(!0),[H,I]=(0,c.useState)(null),[J,K]=(0,c.useState)(!1),[L,M]=(0,c.useState)(""),[N,O]=(0,c.useState)(1),P=a.id,Q=m?.user&&(m.user.role===B.UserRole.SECRETARY_GENERAL||m.user.role===B.UserRole.CONTROLLER);(0,c.useEffect)(()=>{m?.accessToken&&P&&R()},[m,P]);let R=async()=>{try{G(!0),I(null);let[a,b,c]=await Promise.all([n.getSession(P),n.getSessionMembers(P),n.getMembers()]);r(a),C(b);let d=b.map(a=>a.memberId),e=c.filter(a=>!d.includes(a._id));E(e)}catch(a){console.error("Erreur lors du chargement:",a),I("Erreur lors du chargement des données")}finally{G(!1)}},S=async()=>{if(L&&o)try{o.partFixe,await n.addSessionMember({sessionId:P,memberId:L,parts:N}),await R(),M(""),O(1),K(!1)}catch(a){console.error("Erreur lors de l'ajout du membre:",a)}},T=async a=>{if(confirm("Êtes-vous sûr de vouloir retirer ce membre de la session ?"))try{await n.removeSessionMember(P,a),await R()}catch(a){console.error("Erreur lors de la suppression:",a)}},U=a=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"XAF"}).format(a),V=a=>new Date(a).toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric"});if(!Q)return(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsx)("div",{className:"flex items-center gap-4",children:(0,b.jsx)(i.default,{href:"/dashboard/sessions",children:(0,b.jsxs)(j.Button,{variant:"ghost",size:"sm",children:[(0,b.jsx)(f.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,b.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Accès refusé"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Vous n'avez pas les permissions pour accéder à cette page."})]})})]});if(F)return(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsx)("div",{className:"flex items-center gap-4",children:(0,b.jsx)(i.default,{href:"/dashboard/sessions",children:(0,b.jsxs)(j.Button,{variant:"ghost",size:"sm",children:[(0,b.jsx)(f.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,b.jsx)("div",{className:"flex justify-center py-8",children:(0,b.jsx)("div",{className:"text-gray-500",children:"Chargement..."})})]});if(H||!o)return(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsx)("div",{className:"flex items-center gap-4",children:(0,b.jsx)(i.default,{href:"/dashboard/sessions",children:(0,b.jsxs)(j.Button,{variant:"ghost",size:"sm",children:[(0,b.jsx)(f.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,b.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Erreur"}),(0,b.jsx)("p",{className:"text-gray-600",children:H||"Session introuvable"})]})})]});let W=s.length,X=s.reduce((a,b)=>a+b.parts,0),Y=s.reduce((a,b)=>a+b.totalDue,0),Z=s.reduce((a,b)=>a+b.paidSoFar,0);return(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(i.default,{href:"/dashboard/sessions",children:(0,b.jsxs)(j.Button,{variant:"ghost",size:"sm",children:[(0,b.jsx)(f.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["Session ",o.annee]}),(0,b.jsxs)("p",{className:"text-gray-600",children:[V(o.dateDebut)," - ",V(o.dateFin)]})]})]}),(0,b.jsx)(i.default,{href:`/dashboard/sessions/${o._id}/edit`,children:(0,b.jsx)(j.Button,{children:"Modifier"})})]}),(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{children:(0,b.jsx)(k.CardTitle,{children:"Informations de la session"})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Année"}),(0,b.jsx)("p",{className:"text-gray-600",children:o.annee})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Part fixe"}),(0,b.jsx)("p",{className:"text-gray-600",children:U(o.partFixe)})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Date de début"}),(0,b.jsx)("p",{className:"text-gray-600",children:V(o.dateDebut)})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Date de fin"}),(0,b.jsx)("p",{className:"text-gray-600",children:V(o.dateFin)})]})]})})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{className:"pb-2",children:(0,b.jsx)(k.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Membres inscrits"})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold",children:W})})]}),(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{className:"pb-2",children:(0,b.jsx)(k.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Total parts"})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:X})})]}),(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{className:"pb-2",children:(0,b.jsx)(k.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Montant dû"})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:U(Y)})})]}),(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{className:"pb-2",children:(0,b.jsx)(k.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Montant payé"})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-green-600",children:U(Z)})})]})]}),(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(k.CardTitle,{children:"Membres de la session"}),(0,b.jsxs)(k.CardDescription,{children:[W," membre(s) inscrit(s)"]})]}),(0,b.jsxs)(p,{open:J,onOpenChange:K,children:[(0,b.jsx)(q,{asChild:!0,children:(0,b.jsxs)(j.Button,{children:[(0,b.jsx)(g.Plus,{className:"h-4 w-4 mr-2"}),"Ajouter un membre"]})}),(0,b.jsxs)(t,{children:[(0,b.jsxs)(u,{children:[(0,b.jsx)(v,{children:"Ajouter un membre à la session"}),(0,b.jsx)(w,{children:"Sélectionnez un membre et définissez le nombre de parts"})]}),(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(z.Label,{htmlFor:"member",children:"Membre"}),(0,b.jsxs)(x.Select,{value:L,onValueChange:M,children:[(0,b.jsx)(x.SelectTrigger,{children:(0,b.jsx)(x.SelectValue,{placeholder:"Sélectionner un membre"})}),(0,b.jsx)(x.SelectContent,{children:D.map(a=>(0,b.jsxs)(x.SelectItem,{value:a._id,children:[a.firstName," ",a.lastName]},a._id))})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(z.Label,{htmlFor:"parts",children:"Nombre de parts"}),(0,b.jsx)(y.Input,{id:"parts",type:"number",min:"1",value:N,onChange:a=>O(Number(a.target.value))})]}),o&&N>0&&(0,b.jsxs)("div",{className:"text-sm text-gray-600",children:["Montant total dû: ",U(o.partFixe*N)]}),(0,b.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,b.jsx)(j.Button,{variant:"outline",onClick:()=>K(!1),children:"Annuler"}),(0,b.jsx)(j.Button,{onClick:S,disabled:!L,children:"Ajouter"})]})]})]})]})]})}),(0,b.jsx)(k.CardContent,{children:s.length>0?(0,b.jsxs)(l.Table,{children:[(0,b.jsx)(l.TableHeader,{children:(0,b.jsxs)(l.TableRow,{children:[(0,b.jsx)(l.TableHead,{children:"Membre"}),(0,b.jsx)(l.TableHead,{children:"Parts"}),(0,b.jsx)(l.TableHead,{children:"Montant dû"}),(0,b.jsx)(l.TableHead,{children:"Payé"}),(0,b.jsx)(l.TableHead,{children:"Reste"}),(0,b.jsx)(l.TableHead,{children:"Actions"})]})}),(0,b.jsx)(l.TableBody,{children:s.map(a=>{let c=a.totalDue-a.paidSoFar;return(0,b.jsxs)(l.TableRow,{children:[(0,b.jsx)(l.TableCell,{children:(0,b.jsxs)("div",{className:"font-medium",children:["Membre ID: ",a.memberId]})}),(0,b.jsx)(l.TableCell,{children:a.parts}),(0,b.jsx)(l.TableCell,{children:U(a.totalDue)}),(0,b.jsx)(l.TableCell,{className:"text-green-600",children:U(a.paidSoFar)}),(0,b.jsx)(l.TableCell,{className:c>0?"text-red-600":"text-green-600",children:U(c)}),(0,b.jsx)(l.TableCell,{children:(0,b.jsx)(j.Button,{variant:"ghost",size:"sm",onClick:()=>T(a.memberId),className:"text-red-600 hover:text-red-700",children:(0,b.jsx)(h.Trash2,{className:"h-4 w-4"})})})]},a._id)})})]}):(0,b.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Aucun membre inscrit à cette session"})})]})]})}}];

//# sourceMappingURL=frontend_src_app_dashboard_sessions_%5Bid%5D_page_tsx_f942e546._.js.map