"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Plus, Search, Edit, Trash2 } from "lucide-react";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useApi } from "@/hooks/use-api";
import { User, UserRole } from "@/types";

export default function UsersPage() {
	const { data: session } = useSession();
	const api = useApi();

	const [users, setUsers] = useState<User[]>([]);
	const [loading, setLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");

	// Vérifier les permissions - Se<PERSON> le Secrétaire Général peut gérer les utilisateurs
	const canManageUsers =
		session?.user && (session.user as any).role === UserRole.SECRETARY_GENERAL;

	useEffect(() => {
		if (session?.accessToken) {
			loadUsers();
		}
	}, [session]);

	const loadUsers = async () => {
		try {
			setLoading(true);
			const usersData = await api.getUsers();
			setUsers(usersData);
		} catch (error) {
			console.error("Erreur lors du chargement des utilisateurs:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleDeleteUser = async (userId: string) => {
		if (!confirm("Êtes-vous sûr de vouloir supprimer cet utilisateur ?")) {
			return;
		}

		try {
			await api.deleteUser(userId);
			await loadUsers();
		} catch (error) {
			console.error("Erreur lors de la suppression:", error);
		}
	};

	const filteredUsers = users.filter((user) =>
		user.username.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const getRoleBadgeColor = (role: UserRole) => {
		switch (role) {
			case UserRole.SECRETARY_GENERAL:
				return "bg-purple-100 text-purple-800";
			case UserRole.CONTROLLER:
				return "bg-blue-100 text-blue-800";
			case UserRole.CASHIER:
				return "bg-green-100 text-green-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const getRoleLabel = (role: UserRole) => {
		switch (role) {
			case UserRole.SECRETARY_GENERAL:
				return "Secrétaire Général";
			case UserRole.CONTROLLER:
				return "Contrôleur";
			case UserRole.CASHIER:
				return "Caissier";
			default:
				return role;
		}
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('fr-FR', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	};

	if (!canManageUsers) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-center h-64">
					<div className="text-center">
						<h2 className="text-lg font-semibold text-gray-900">Accès refusé</h2>
						<p className="text-gray-600">
							Seul le Secrétaire Général peut accéder à cette page.
						</p>
					</div>
				</div>
			</div>
		);
	}

	if (loading) {
		return (
			<div className="space-y-6">
				<div className="flex justify-center py-8">
					<div className="text-gray-500">Chargement...</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-gray-900">Utilisateurs</h1>
					<p className="text-gray-600">
						Gestion des comptes d'accès à l'application
					</p>
				</div>
				<Link href="/auth/register">
					<Button>
						<Plus className="h-4 w-4 mr-2" />
						Nouvel utilisateur
					</Button>
				</Link>
			</div>

			{/* Statistiques */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-gray-600">
							Total utilisateurs
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{users.length}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-gray-600">
							Secrétaires Généraux
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-purple-600">
							{users.filter(u => u.role === UserRole.SECRETARY_GENERAL).length}
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-gray-600">
							Contrôleurs
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-blue-600">
							{users.filter(u => u.role === UserRole.CONTROLLER).length}
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-gray-600">
							Caissiers
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-green-600">
							{users.filter(u => u.role === UserRole.CASHIER).length}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Recherche et liste */}
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle>Liste des utilisateurs</CardTitle>
							<CardDescription>
								{filteredUsers.length} utilisateur(s)
							</CardDescription>
						</div>
						<div className="flex items-center gap-4">
							<div className="relative">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
								<Input
									placeholder="Rechercher un utilisateur..."
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className="pl-10 w-64"
								/>
							</div>
						</div>
					</div>
				</CardHeader>
				<CardContent>
					{filteredUsers.length > 0 ? (
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Nom d'utilisateur</TableHead>
									<TableHead>Rôle</TableHead>
									<TableHead>Créé le</TableHead>
									<TableHead>Actions</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{filteredUsers.map((user) => (
									<TableRow key={user._id}>
										<TableCell>
											<div className="font-medium">{user.username}</div>
										</TableCell>
										<TableCell>
											<Badge className={getRoleBadgeColor(user.role)}>
												{getRoleLabel(user.role)}
											</Badge>
										</TableCell>
										<TableCell className="text-gray-600">
											{formatDate(user.createdAt)}
										</TableCell>
										<TableCell>
											<div className="flex items-center gap-2">
												<Button
													variant="ghost"
													size="sm"
													onClick={() => handleDeleteUser(user._id)}
													className="text-red-600 hover:text-red-700"
												>
													<Trash2 className="h-4 w-4" />
												</Button>
											</div>
										</TableCell>
									</TableRow>
								))}
							</TableBody>
						</Table>
					) : (
						<div className="text-center py-8 text-gray-500">
							{searchTerm ? "Aucun utilisateur trouvé" : "Aucun utilisateur"}
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
