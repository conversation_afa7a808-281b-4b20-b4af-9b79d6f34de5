import { Injectable, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcryptjs';

import { User, UserDocument } from './schemas/user.schema';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) {}
  async create(createUserData: CreateUserDto): Promise<User> {
    const { username, password, role } = createUserData;

    const existingUser = await this.userModel.findOne({ username });
    if (existingUser) {
      throw new ConflictException('Username already taken.');
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const createdUser = new this.userModel({
      username,
      password: hashedPassword,
      role,
    });

    return createdUser.save();
  }
  async findByUsername(username: string): Promise<User | null> {
    return this.userModel.findOne({ username }).exec();
  }
  async findById(id: string): Promise<User | null> {
    return this.userModel.findById(id ).exec();
  }
  async getUserId(username: string): Promise<String | unknown> {
    const user = await this.userModel.findOne({ username }).exec();
    if(user) {
      return user._id;
    }
  }
  async validateUser(username: string, pass: string): Promise<User | null> {
    const user = await this.findByUsername(username);
    if (!user) return null;

    const isPasswordValid = await bcrypt.compare(pass, user.password);
    if (!isPasswordValid) return null;

    return user;
  }

  async findAll(): Promise<User[]> {
    return this.userModel.find().exec();
  }

  async findOne(id: string): Promise<User | null> {
    return this.userModel.findById(id).exec();
  }

  async update(id: string, updateUserData: UpdateUserDto): Promise<User | null> {
    let data: UpdateUserDto;
    if(updateUserData.password){
        updateUserData.password = await bcrypt.hash(updateUserData.password, 10);
        data = updateUserData;
    }else{
      // Exclude password from update if not provided
      const { password, ...rest } = updateUserData;
      data = rest as UpdateUserDto;
    }
    return this.userModel.findByIdAndUpdate(id, data, { new: true }).exec();
  }

  async remove(id: string): Promise<User | null> {
    return this.userModel.findByIdAndDelete(id).exec();
  }
}

