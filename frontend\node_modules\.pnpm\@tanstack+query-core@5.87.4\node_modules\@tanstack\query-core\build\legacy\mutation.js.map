{"version": 3, "sources": ["../../src/mutation.ts"], "sourcesContent": ["import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Removable } from './removable'\nimport { createR<PERSON>ry<PERSON> } from './retryer'\nimport type {\n  DefaultError,\n  MutationMeta,\n  MutationOptions,\n  MutationStatus,\n} from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport type { <PERSON><PERSON><PERSON> } from './retryer'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TContext = unknown,\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  failureReason: TError | null\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n  submittedAt: number\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError | null\n}\n\ninterface PendingAction<TVariables, TContext> {\n  type: 'pending'\n  isPaused: boolean\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | PendingAction<TVariables, TContext>\n  | PauseAction\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TContext = unknown,\n> extends Removable {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options!: MutationOptions<TData, TError, TVariables, TContext>\n  readonly mutationId: number\n\n  #observers: Array<MutationObserver<TData, TError, TVariables, TContext>>\n  #mutationCache: MutationCache\n  #retryer?: Retryer<TData>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    super()\n\n    this.mutationId = config.mutationId\n    this.#mutationCache = config.mutationCache\n    this.#observers = []\n    this.state = config.state || getDefaultState()\n\n    this.setOptions(config.options)\n    this.scheduleGc()\n  }\n\n  setOptions(\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n  ): void {\n    this.options = options\n\n    this.updateGcTime(this.options.gcTime)\n  }\n\n  get meta(): MutationMeta | undefined {\n    return this.options.meta\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer)\n\n      // Stop the mutation from being garbage collected\n      this.clearGcTimeout()\n\n      this.#mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer,\n      })\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.#observers = this.#observers.filter((x) => x !== observer)\n\n    this.scheduleGc()\n\n    this.#mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer,\n    })\n  }\n\n  protected optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === 'pending') {\n        this.scheduleGc()\n      } else {\n        this.#mutationCache.remove(this)\n      }\n    }\n  }\n\n  continue(): Promise<unknown> {\n    return (\n      this.#retryer?.continue() ??\n      // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n      this.execute(this.state.variables!)\n    )\n  }\n\n  async execute(variables: TVariables): Promise<TData> {\n    const onContinue = () => {\n      this.#dispatch({ type: 'continue' })\n    }\n\n    this.#retryer = createRetryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error('No mutationFn found'))\n        }\n        return this.options.mutationFn(variables)\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.#dispatch({ type: 'pause' })\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this),\n    })\n\n    const restored = this.state.status === 'pending'\n    const isPaused = !this.#retryer.canStart()\n\n    try {\n      if (restored) {\n        // Dispatch continue action to unpause restored mutation\n        onContinue()\n      } else {\n        this.#dispatch({ type: 'pending', variables, isPaused })\n        // Notify cache callback\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n        const context = await this.options.onMutate?.(variables)\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: 'pending',\n            context,\n            variables,\n            isPaused,\n          })\n        }\n      }\n      const data = await this.#retryer.start()\n\n      // Notify cache callback\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSuccess?.(data, variables, this.state.context!)\n\n      // Notify cache callback\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSettled?.(data, null, variables, this.state.context)\n\n      this.#dispatch({ type: 'success', data })\n      return data\n    } catch (error) {\n      try {\n        // Notify cache callback\n        await this.#mutationCache.config.onError?.(\n          error as any,\n          variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onError?.(\n          error as TError,\n          variables,\n          this.state.context,\n        )\n\n        // Notify cache callback\n        await this.#mutationCache.config.onSettled?.(\n          undefined,\n          error as any,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onSettled?.(\n          undefined,\n          error as TError,\n          variables,\n          this.state.context,\n        )\n        throw error\n      } finally {\n        this.#dispatch({ type: 'error', error: error as TError })\n      }\n    } finally {\n      this.#mutationCache.runNext(this)\n    }\n  }\n\n  #dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    const reducer = (\n      state: MutationState<TData, TError, TVariables, TContext>,\n    ): MutationState<TData, TError, TVariables, TContext> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            isPaused: true,\n          }\n        case 'continue':\n          return {\n            ...state,\n            isPaused: false,\n          }\n        case 'pending':\n          return {\n            ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: 'pending',\n            variables: action.variables,\n            submittedAt: Date.now(),\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false,\n          }\n        case 'error':\n          return {\n            ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error',\n          }\n      }\n    }\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action)\n      })\n      this.#mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action,\n      })\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext,\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n    submittedAt: 0,\n  }\n}\n"], "mappings": ";;;;;;;;AAAA,SAAS,qBAAqB;AAC9B,SAAS,iBAAiB;AAC1B,SAAS,qBAAqB;AAF9B;AAgFO,IAAM,WAAN,cAKG,UAAU;AAAA,EASlB,YAAY,QAA6D;AACvE,UAAM;AAfH;AAUL;AACA;AACA;AAKE,SAAK,aAAa,OAAO;AACzB,uBAAK,gBAAiB,OAAO;AAC7B,uBAAK,YAAa,CAAC;AACnB,SAAK,QAAQ,OAAO,SAAS,gBAAgB;AAE7C,SAAK,WAAW,OAAO,OAAO;AAC9B,SAAK,WAAW;AAAA,EAClB;AAAA,EAEA,WACE,SACM;AACN,SAAK,UAAU;AAEf,SAAK,aAAa,KAAK,QAAQ,MAAM;AAAA,EACvC;AAAA,EAEA,IAAI,OAAiC;AACnC,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,YAAY,UAAsD;AAChE,QAAI,CAAC,mBAAK,YAAW,SAAS,QAAQ,GAAG;AACvC,yBAAK,YAAW,KAAK,QAAQ;AAG7B,WAAK,eAAe;AAEpB,yBAAK,gBAAe,OAAO;AAAA,QACzB,MAAM;AAAA,QACN,UAAU;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,eAAe,UAAsD;AACnE,uBAAK,YAAa,mBAAK,YAAW,OAAO,CAAC,MAAM,MAAM,QAAQ;AAE9D,SAAK,WAAW;AAEhB,uBAAK,gBAAe,OAAO;AAAA,MACzB,MAAM;AAAA,MACN,UAAU;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEU,iBAAiB;AACzB,QAAI,CAAC,mBAAK,YAAW,QAAQ;AAC3B,UAAI,KAAK,MAAM,WAAW,WAAW;AACnC,aAAK,WAAW;AAAA,MAClB,OAAO;AACL,2BAAK,gBAAe,OAAO,IAAI;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAA6B;AA3J/B;AA4JI,aACE,wBAAK,cAAL,mBAAe;AAAA,IAEf,KAAK,QAAQ,KAAK,MAAM,SAAU;AAAA,EAEtC;AAAA,EAEA,MAAM,QAAQ,WAAuC;AAnKvD;AAoKI,UAAM,aAAa,MAAM;AACvB,4BAAK,kCAAL,WAAe,EAAE,MAAM,WAAW;AAAA,IACpC;AAEA,uBAAK,UAAW,cAAc;AAAA,MAC5B,IAAI,MAAM;AACR,YAAI,CAAC,KAAK,QAAQ,YAAY;AAC5B,iBAAO,QAAQ,OAAO,IAAI,MAAM,qBAAqB,CAAC;AAAA,QACxD;AACA,eAAO,KAAK,QAAQ,WAAW,SAAS;AAAA,MAC1C;AAAA,MACA,QAAQ,CAAC,cAAc,UAAU;AAC/B,8BAAK,kCAAL,WAAe,EAAE,MAAM,UAAU,cAAc,MAAM;AAAA,MACvD;AAAA,MACA,SAAS,MAAM;AACb,8BAAK,kCAAL,WAAe,EAAE,MAAM,QAAQ;AAAA,MACjC;AAAA,MACA;AAAA,MACA,OAAO,KAAK,QAAQ,SAAS;AAAA,MAC7B,YAAY,KAAK,QAAQ;AAAA,MACzB,aAAa,KAAK,QAAQ;AAAA,MAC1B,QAAQ,MAAM,mBAAK,gBAAe,OAAO,IAAI;AAAA,IAC/C,CAAC;AAED,UAAM,WAAW,KAAK,MAAM,WAAW;AACvC,UAAM,WAAW,CAAC,mBAAK,UAAS,SAAS;AAEzC,QAAI;AACF,UAAI,UAAU;AAEZ,mBAAW;AAAA,MACb,OAAO;AACL,8BAAK,kCAAL,WAAe,EAAE,MAAM,WAAW,WAAW,SAAS;AAEtD,gBAAM,8BAAK,gBAAe,QAAO,aAA3B;AAAA;AAAA,UACJ;AAAA,UACA;AAAA;AAEF,cAAM,UAAU,QAAM,gBAAK,SAAQ,aAAb,4BAAwB;AAC9C,YAAI,YAAY,KAAK,MAAM,SAAS;AAClC,gCAAK,kCAAL,WAAe;AAAA,YACb,MAAM;AAAA,YACN;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,OAAO,MAAM,mBAAK,UAAS,MAAM;AAGvC,cAAM,8BAAK,gBAAe,QAAO,cAA3B;AAAA;AAAA,QACJ;AAAA,QACA;AAAA,QACA,KAAK,MAAM;AAAA,QACX;AAAA;AAGF,cAAM,gBAAK,SAAQ,cAAb,4BAAyB,MAAM,WAAW,KAAK,MAAM;AAG3D,cAAM,8BAAK,gBAAe,QAAO,cAA3B;AAAA;AAAA,QACJ;AAAA,QACA;AAAA,QACA,KAAK,MAAM;AAAA,QACX,KAAK,MAAM;AAAA,QACX;AAAA;AAGF,cAAM,gBAAK,SAAQ,cAAb,4BAAyB,MAAM,MAAM,WAAW,KAAK,MAAM;AAEjE,4BAAK,kCAAL,WAAe,EAAE,MAAM,WAAW,KAAK;AACvC,aAAO;AAAA,IACT,SAAS,OAAO;AACd,UAAI;AAEF,gBAAM,8BAAK,gBAAe,QAAO,YAA3B;AAAA;AAAA,UACJ;AAAA,UACA;AAAA,UACA,KAAK,MAAM;AAAA,UACX;AAAA;AAGF,gBAAM,gBAAK,SAAQ,YAAb;AAAA;AAAA,UACJ;AAAA,UACA;AAAA,UACA,KAAK,MAAM;AAAA;AAIb,gBAAM,8BAAK,gBAAe,QAAO,cAA3B;AAAA;AAAA,UACJ;AAAA,UACA;AAAA,UACA,KAAK,MAAM;AAAA,UACX,KAAK,MAAM;AAAA,UACX;AAAA;AAGF,gBAAM,gBAAK,SAAQ,cAAb;AAAA;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK,MAAM;AAAA;AAEb,cAAM;AAAA,MACR,UAAE;AACA,8BAAK,kCAAL,WAAe,EAAE,MAAM,SAAS,MAAuB;AAAA,MACzD;AAAA,IACF,UAAE;AACA,yBAAK,gBAAe,QAAQ,IAAI;AAAA,IAClC;AAAA,EACF;AAuEF;AAhQE;AACA;AACA;AAZK;AAqML,cAAS,SAAC,QAA2D;AACnE,QAAM,UAAU,CACd,UACuD;AACvD,YAAQ,OAAO,MAAM;AAAA,MACnB,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,cAAc,OAAO;AAAA,UACrB,eAAe,OAAO;AAAA,QACxB;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,UAAU;AAAA,QACZ;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,UAAU;AAAA,QACZ;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,SAAS,OAAO;AAAA,UAChB,MAAM;AAAA,UACN,cAAc;AAAA,UACd,eAAe;AAAA,UACf,OAAO;AAAA,UACP,UAAU,OAAO;AAAA,UACjB,QAAQ;AAAA,UACR,WAAW,OAAO;AAAA,UAClB,aAAa,KAAK,IAAI;AAAA,QACxB;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,MAAM,OAAO;AAAA,UACb,cAAc;AAAA,UACd,eAAe;AAAA,UACf,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,UAAU;AAAA,QACZ;AAAA,MACF,KAAK;AACH,eAAO;AAAA,UACL,GAAG;AAAA,UACH,MAAM;AAAA,UACN,OAAO,OAAO;AAAA,UACd,cAAc,MAAM,eAAe;AAAA,UACnC,eAAe,OAAO;AAAA,UACtB,UAAU;AAAA,UACV,QAAQ;AAAA,QACV;AAAA,IACJ;AAAA,EACF;AACA,OAAK,QAAQ,QAAQ,KAAK,KAAK;AAE/B,gBAAc,MAAM,MAAM;AACxB,uBAAK,YAAW,QAAQ,CAAC,aAAa;AACpC,eAAS,iBAAiB,MAAM;AAAA,IAClC,CAAC;AACD,uBAAK,gBAAe,OAAO;AAAA,MACzB,UAAU;AAAA,MACV,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAGK,SAAS,kBAKwC;AACtD,SAAO;AAAA,IACL,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,cAAc;AAAA,IACd,eAAe;AAAA,IACf,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,EACf;AACF;", "names": []}