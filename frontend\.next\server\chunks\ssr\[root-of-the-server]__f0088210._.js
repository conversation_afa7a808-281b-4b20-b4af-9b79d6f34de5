module.exports=[24725,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},41405,a=>{"use strict";a.s(["Label",()=>g],41405);var b=a.i(68116),c=a.i(128),d=a.i(48206),e=c.forwardRef((a,c)=>(0,b.jsx)(d.Primitive.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));e.displayName="Label";var f=a.i(22171);function g({className:a,...c}){return(0,b.jsx)(e,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...c})}},6821,a=>{"use strict";a.s(["ArrowLeft",()=>b],6821);let b=(0,a.i(621).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},66577,a=>{"use strict";a.s(["Textarea",()=>d]);var b=a.i(68116),c=a.i(22171);function d({className:a,...d}){return(0,b.jsx)("textarea",{"data-slot":"textarea",className:(0,c.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...d})}},33920,a=>{"use strict";a.s(["default",()=>t]);var b=a.i(68116),c=a.i(128),d=a.i(50395),e=a.i(81223),f=a.i(64853),g=a.i(54636),h=a.i(66446),i=a.i(6821),j=a.i(33055),k=a.i(2979),l=a.i(78184),m=a.i(66577),n=a.i(75780),o=a.i(60563),p=a.i(44932),q=a.i(12594),r=a.i(78655);let s=h.z.object({direction:h.z.nativeEnum(r.PaymentDirection),func:h.z.nativeEnum(r.PaymentFunction),amount:h.z.number().positive("Le montant doit être positif"),caisseId:h.z.string().min(1,"Veuillez sélectionner une caisse"),receivingCaisseId:h.z.string().optional(),sessionId:h.z.string().optional(),reunionId:h.z.string().optional(),memberId:h.z.string().optional(),reason:h.z.string().optional()});function t(){let{data:a}=(0,e.useSession)(),h=(0,d.useRouter)(),t=(0,d.useSearchParams)(),u=(0,q.useApi)(),[v,w]=(0,c.useState)(!1),[x,y]=(0,c.useState)(null),[z,A]=(0,c.useState)([]),[B,C]=(0,c.useState)([]),[D,E]=(0,c.useState)([]),F=t.get("type"),G=a?.user&&(a.user.role===r.UserRole.SECRETARY_GENERAL||a.user.role===r.UserRole.CONTROLLER||a.user.role===r.UserRole.CASHIER),H=(0,f.useForm)({resolver:(0,g.zodResolver)(s),defaultValues:{direction:r.PaymentDirection.IN,func:"contribution"===F?r.PaymentFunction.CONTRIBUTION:"transfer"===F?r.PaymentFunction.TRANSFER:"external"===F?r.PaymentFunction.EXTERNAL:r.PaymentFunction.CONTRIBUTION,amount:0,caisseId:"",receivingCaisseId:"",sessionId:"",reunionId:"",memberId:"",reason:""}}),I=H.watch("func"),J=H.watch("direction");(0,c.useEffect)(()=>{a?.accessToken&&K()},[a]),(0,c.useEffect)(()=>{I===r.PaymentFunction.CONTRIBUTION&&H.setValue("direction",r.PaymentDirection.IN)},[I,H]);let K=async()=>{try{let[a,b,c]=await Promise.all([u.getCaisses(),u.getMembers(),u.getSessions()]);A(a),C(b),E(c)}catch(a){console.error("Erreur lors du chargement des données:",a)}},L=async a=>{w(!0),y(null);try{let b={direction:a.direction,func:a.func,amount:a.amount,caisseId:a.caisseId};a.receivingCaisseId&&(b.receivingCaisseId=a.receivingCaisseId),a.sessionId&&(b.sessionId=a.sessionId),a.reunionId&&(b.reunionId=a.reunionId),a.memberId&&(b.memberId=a.memberId),a.reason&&(b.reason=a.reason),await u.createPayment(b),h.push("/dashboard/payments")}catch(a){console.error("Erreur lors de la création du paiement:",a),a instanceof Error?y(a.message):y("Erreur lors de la création du paiement. Veuillez réessayer.")}finally{w(!1)}};return G?(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(j.default,{href:"/dashboard/payments",children:(0,b.jsxs)(k.Button,{variant:"ghost",size:"sm",children:[(0,b.jsx)(i.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Nouveau Paiement"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Enregistrer un nouveau paiement"})]})]}),(0,b.jsxs)(n.Card,{className:"max-w-2xl",children:[(0,b.jsxs)(n.CardHeader,{children:[(0,b.jsx)(n.CardTitle,{children:"Informations du paiement"}),(0,b.jsx)(n.CardDescription,{children:"Remplissez les informations du paiement à enregistrer"})]}),(0,b.jsx)(n.CardContent,{children:(0,b.jsx)(o.Form,{...H,children:(0,b.jsxs)("form",{onSubmit:H.handleSubmit(L),className:"space-y-6",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsx)(o.FormField,{control:H.control,name:"func",render:({field:a})=>(0,b.jsxs)(o.FormItem,{children:[(0,b.jsx)(o.FormLabel,{children:"Fonction *"}),(0,b.jsxs)(p.Select,{onValueChange:a.onChange,value:a.value,children:[(0,b.jsx)(o.FormControl,{children:(0,b.jsx)(p.SelectTrigger,{disabled:v,children:(0,b.jsx)(p.SelectValue,{placeholder:"Type de paiement"})})}),(0,b.jsxs)(p.SelectContent,{children:[(0,b.jsx)(p.SelectItem,{value:r.PaymentFunction.CONTRIBUTION,children:"Cotisation"}),(0,b.jsx)(p.SelectItem,{value:r.PaymentFunction.TRANSFER,children:"Transfert"}),(0,b.jsx)(p.SelectItem,{value:r.PaymentFunction.EXTERNAL,children:"Externe"})]})]}),(0,b.jsx)(o.FormMessage,{})]})}),(0,b.jsx)(o.FormField,{control:H.control,name:"direction",render:({field:a})=>(0,b.jsxs)(o.FormItem,{children:[(0,b.jsx)(o.FormLabel,{children:"Direction *"}),(0,b.jsxs)(p.Select,{onValueChange:a.onChange,value:a.value,disabled:I===r.PaymentFunction.CONTRIBUTION,children:[(0,b.jsx)(o.FormControl,{children:(0,b.jsx)(p.SelectTrigger,{disabled:v,children:(0,b.jsx)(p.SelectValue,{placeholder:"Direction"})})}),(0,b.jsxs)(p.SelectContent,{children:[(0,b.jsx)(p.SelectItem,{value:r.PaymentDirection.IN,children:"Entrée"}),(0,b.jsx)(p.SelectItem,{value:r.PaymentDirection.OUT,children:"Sortie"})]})]}),(0,b.jsx)(o.FormMessage,{})]})})]}),(0,b.jsx)(o.FormField,{control:H.control,name:"amount",render:({field:a})=>(0,b.jsxs)(o.FormItem,{children:[(0,b.jsx)(o.FormLabel,{children:"Montant *"}),(0,b.jsx)(o.FormControl,{children:(0,b.jsx)(l.Input,{type:"number",placeholder:"0",...a,onChange:b=>a.onChange(Number(b.target.value)),disabled:v})}),(0,b.jsx)(o.FormMessage,{})]})}),(0,b.jsx)(o.FormField,{control:H.control,name:"caisseId",render:({field:a})=>(0,b.jsxs)(o.FormItem,{children:[(0,b.jsx)(o.FormLabel,{children:"Caisse *"}),(0,b.jsxs)(p.Select,{onValueChange:a.onChange,value:a.value,children:[(0,b.jsx)(o.FormControl,{children:(0,b.jsx)(p.SelectTrigger,{disabled:v,children:(0,b.jsx)(p.SelectValue,{placeholder:"Sélectionner une caisse"})})}),(0,b.jsx)(p.SelectContent,{children:z.map(a=>(0,b.jsxs)(p.SelectItem,{value:a._id,children:[a.nom," (",a.type,")"]},a._id))})]}),(0,b.jsx)(o.FormMessage,{})]})}),I===r.PaymentFunction.TRANSFER&&(0,b.jsx)(o.FormField,{control:H.control,name:"receivingCaisseId",render:({field:a})=>(0,b.jsxs)(o.FormItem,{children:[(0,b.jsx)(o.FormLabel,{children:"Caisse de destination *"}),(0,b.jsxs)(p.Select,{onValueChange:a.onChange,value:a.value,children:[(0,b.jsx)(o.FormControl,{children:(0,b.jsx)(p.SelectTrigger,{disabled:v,children:(0,b.jsx)(p.SelectValue,{placeholder:"Caisse de destination"})})}),(0,b.jsx)(p.SelectContent,{children:z.map(a=>(0,b.jsxs)(p.SelectItem,{value:a._id,children:[a.nom," (",a.type,")"]},a._id))})]}),(0,b.jsx)(o.FormMessage,{})]})}),I===r.PaymentFunction.CONTRIBUTION&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(o.FormField,{control:H.control,name:"memberId",render:({field:a})=>(0,b.jsxs)(o.FormItem,{children:[(0,b.jsx)(o.FormLabel,{children:"Membre *"}),(0,b.jsxs)(p.Select,{onValueChange:a.onChange,value:a.value,children:[(0,b.jsx)(o.FormControl,{children:(0,b.jsx)(p.SelectTrigger,{disabled:v,children:(0,b.jsx)(p.SelectValue,{placeholder:"Sélectionner un membre"})})}),(0,b.jsx)(p.SelectContent,{children:B.map(a=>(0,b.jsxs)(p.SelectItem,{value:a._id,children:[a.firstName," ",a.lastName]},a._id))})]}),(0,b.jsx)(o.FormMessage,{})]})}),(0,b.jsx)(o.FormField,{control:H.control,name:"sessionId",render:({field:a})=>(0,b.jsxs)(o.FormItem,{children:[(0,b.jsx)(o.FormLabel,{children:"Session"}),(0,b.jsxs)(p.Select,{onValueChange:a.onChange,value:a.value,children:[(0,b.jsx)(o.FormControl,{children:(0,b.jsx)(p.SelectTrigger,{disabled:v,children:(0,b.jsx)(p.SelectValue,{placeholder:"Sélectionner une session"})})}),(0,b.jsx)(p.SelectContent,{children:D.map(a=>(0,b.jsxs)(p.SelectItem,{value:a._id,children:["Session ",a.annee]},a._id))})]}),(0,b.jsx)(o.FormMessage,{})]})})]}),I===r.PaymentFunction.EXTERNAL&&J===r.PaymentDirection.OUT&&(0,b.jsx)(o.FormField,{control:H.control,name:"reason",render:({field:a})=>(0,b.jsxs)(o.FormItem,{children:[(0,b.jsx)(o.FormLabel,{children:"Motif *"}),(0,b.jsx)(o.FormControl,{children:(0,b.jsx)(m.Textarea,{placeholder:"Motif du paiement externe",...a,disabled:v,rows:3})}),(0,b.jsx)(o.FormMessage,{})]})}),x&&(0,b.jsx)("div",{className:"text-red-600 text-sm bg-red-50 p-3 rounded",children:x}),(0,b.jsxs)("div",{className:"flex justify-end gap-4 pt-6",children:[(0,b.jsx)(j.default,{href:"/dashboard/payments",children:(0,b.jsx)(k.Button,{variant:"outline",disabled:v,children:"Annuler"})}),(0,b.jsx)(k.Button,{type:"submit",disabled:v,children:v?"Enregistrement...":"Enregistrer le paiement"})]})]})})})]})]}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsx)("div",{className:"flex items-center gap-4",children:(0,b.jsx)(j.default,{href:"/dashboard/payments",children:(0,b.jsxs)(k.Button,{variant:"ghost",size:"sm",children:[(0,b.jsx)(i.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,b.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Accès refusé"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Vous n'avez pas les permissions pour créer des paiements."})]})})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__f0088210._.js.map