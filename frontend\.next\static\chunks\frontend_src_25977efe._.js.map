{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card'\n      className={cn(\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-header'\n      className={cn(\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-title'\n      className={cn('leading-none font-semibold', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-description'\n      className={cn('text-muted-foreground text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-action'\n      className={cn(\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-content'\n      className={cn('px-6', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-footer'\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\n\nimport { cn } from '@/lib/utils';\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot='label'\n      className={cn(\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Label };\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,wUAAC,qSAAmB;QAClB,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/form.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { Slot } from '@radix-ui/react-slot';\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues\n} from 'react-hook-form';\n\nimport { cn } from '@/lib/utils';\nimport { Label } from '@/components/ui/label';\n\nconst Form = FormProvider;\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n> = {\n  name: TName;\n};\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n);\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  );\n};\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext);\n  const itemContext = React.useContext(FormItemContext);\n  const { getFieldState } = useFormContext();\n  const formState = useFormState({ name: fieldContext.name });\n  const fieldState = getFieldState(fieldContext.name, formState);\n\n  if (!fieldContext) {\n    throw new Error('useFormField should be used within <FormField>');\n  }\n\n  const { id } = itemContext;\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState\n  };\n};\n\ntype FormItemContextValue = {\n  id: string;\n};\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n);\n\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\n  const id = React.useId();\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot='form-item'\n        className={cn('grid gap-2', className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  );\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField();\n\n  return (\n    <Label\n      data-slot='form-label'\n      data-error={!!error}\n      className={cn('data-[error=true]:text-destructive', className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  );\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } =\n    useFormField();\n\n  return (\n    <Slot\n      data-slot='form-control'\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  );\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\n  const { formDescriptionId } = useFormField();\n\n  return (\n    <p\n      data-slot='form-description'\n      id={formDescriptionId}\n      className={cn('text-muted-foreground text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\n  const { error, formMessageId } = useFormField();\n  const body = error ? String(error?.message ?? '') : props.children;\n\n  if (!body) {\n    return null;\n  }\n\n  return (\n    <p\n      data-slot='form-message'\n      id={formMessageId}\n      className={cn('text-destructive text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,sRAAY;AASzB,MAAM,iCAAmB,yTAAmB,CAC1C,CAAC;AAGH,MAAM,YAAY;QAGhB,EACA,GAAG,OACkC;IACrC,qBACE,wUAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,wUAAC,oRAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,sTAAgB,CAAC;IACtC,MAAM,cAAc,sTAAgB,CAAC;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,IAAA,wRAAc;IACxC,MAAM,YAAY,IAAA,sRAAY,EAAC;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,AAAC,GAAK,OAAH,IAAG;QAClB,mBAAmB,AAAC,GAAK,OAAH,IAAG;QACzB,eAAe,AAAC,GAAK,OAAH,IAAG;QACrB,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,wRAAc;QACtB,sRAAY;;;AAuBhC,MAAM,gCAAkB,yTAAmB,CACzC,CAAC;AAGH,SAAS,SAAS,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;;IAChB,MAAM,KAAK,iTAAW;IAEtB,qBACE,wUAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,wUAAC;YACC,aAAU;YACV,WAAW,IAAA,wIAAE,EAAC,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,KAGgC;QAHhC,EACjB,SAAS,EACT,GAAG,OAC8C,GAHhC;;IAIjB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,wUAAC,yJAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,IAAA,wIAAE,EAAC,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,KAA+C;QAA/C,EAAE,GAAG,OAA0C,GAA/C;;IACnB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAC3D;IAEF,qBACE,wUAAC,4TAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,AAAC,GAAoB,OAAlB,qBACH,AAAC,GAAuB,OAArB,mBAAkB,KAAiB,OAAd;QAE9B,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAjBS;;QAEL;;;MAFK;AAmBT,SAAS,gBAAgB,KAAkD;QAAlD,EAAE,SAAS,EAAE,GAAG,OAAkC,GAAlD;;IACvB,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,wUAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,IAAA,wIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,KAAkD;QAAlD,EAAE,SAAS,EAAE,GAAG,OAAkC,GAAlD;;IACnB,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;QACL;IAA5B,MAAM,OAAO,QAAQ,OAAO,CAAA,iBAAA,kBAAA,4BAAA,MAAO,OAAO,cAAd,4BAAA,iBAAkB,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,wUAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,IAAA,wIAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;IAhBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/auth.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from \"next-auth/providers/credentials\";\nimport { z } from \"zod\";\nimport { apiService } from \"./api\";\nimport { decodeJwt } from \"./utils\";\n\nconst loginSchema = z.object({\n\tusername: z.string().min(1, \"Username is required\"),\n\tpassword: z.string().min(1, \"Password is required\"),\n});\n\nexport const { handlers, signIn, signOut, auth } = NextAuth({\n\tproviders: [\n\t\tCredentialsProvider({\n\t\t\tname: \"credentials\",\n\t\t\tcredentials: {\n\t\t\t\tusername: { label: \"Username\", type: \"text\" },\n\t\t\t\tpassword: { label: \"Password\", type: \"password\" },\n\t\t\t},\n\t\t\tasync authorize(credentials) {\n\t\t\t\ttry {\n\t\t\t\t\tconst { username, password } = loginSchema.parse(credentials);\n\n\t\t\t\t\t// Authentification avec l'API backend\n\t\t\t\t\tconst response = await apiService.login({ username, password });\n\t\t\t\t\tconst jwt = decodeJwt(response.access_token);\n\t\t\t\t\tif(!jwt) return null;\n\t\t\t\t\tconst jwtPayload = jwt.payload as unknown as { sub: string; username: string; role:  'secretary_general' | 'controller' | 'cashier' };\n\t\t\t\t\t\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tid: jwtPayload.sub,\n\t\t\t\t\t\t\tusername: jwtPayload.username,\n\t\t\t\t\t\t\trole: jwtPayload.role,\n\t\t\t\t\t\t\taccessToken: response.access_token,\n\t\t\t\t\t}\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"Erreur d'authentification:\", error);\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\t\t}),\n\t],\n\tpages: {\n\t\tsignIn: \"/auth/signin\",\n\t},\n\tcallbacks: {\n\t\tauthorized: ({ auth }) => !!auth,\n\t\tasync jwt({ token, user }) {\n\t\t\tif (user) {\n\t\t\t\ttoken.username = user.username;\n\t\t\t\ttoken.role = user.role;\n\t\t\t\ttoken.accessToken = user.accessToken;\n\t\t\t}\n\t\t\treturn token;\n\t\t},\n\t\tasync session({ session, token }) {\n\t\t\tif (token) {\n\t\t\t\tsession.user.id = token.sub || \"\";\n\t\t\t\tsession.user.username = token.username as string;\n\t\t\t\tsession.user.role = token.role as  'secretary_general' | 'controller' | 'cashier';\n\t\t\t\tsession.accessToken = token.accessToken as string;\n\t\t\t}\n\t\t\treturn session;\n\t\t},\n\t},\n\tsession: {\n\t\tstrategy: \"jwt\",\n\t},\n});\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,cAAc,iPAAC,CAAC,MAAM,CAAC;IAC5B,UAAU,iPAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,UAAU,iPAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC7B;AAEO,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAA,2RAAQ,EAAC;IAC3D,WAAW;QACV,IAAA,gPAAmB,EAAC;YACnB,MAAM;YACN,aAAa;gBACZ,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAO;gBAC5C,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YACjD;YACA,MAAM,WAAU,WAAW;gBAC1B,IAAI;oBACH,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,YAAY,KAAK,CAAC;oBAEjD,sCAAsC;oBACtC,MAAM,WAAW,MAAM,8IAAU,CAAC,KAAK,CAAC;wBAAE;wBAAU;oBAAS;oBAC7D,MAAM,MAAM,IAAA,+IAAS,EAAC,SAAS,YAAY;oBAC3C,IAAG,CAAC,KAAK,OAAO;oBAChB,MAAM,aAAa,IAAI,OAAO;oBAE7B,OAAO;wBACN,IAAI,WAAW,GAAG;wBAClB,UAAU,WAAW,QAAQ;wBAC7B,MAAM,WAAW,IAAI;wBACrB,aAAa,SAAS,YAAY;oBACpC;gBAED,EAAE,OAAO,OAAO;oBACf,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,OAAO;gBACR;YACD;QACD;KACA;IACD,OAAO;QACN,QAAQ;IACT;IACA,WAAW;QACV,YAAY;gBAAC,EAAE,IAAI,EAAE;mBAAK,CAAC,CAAC;;QAC5B,MAAM,KAAI,KAAe;gBAAf,EAAE,KAAK,EAAE,IAAI,EAAE,GAAf;YACT,IAAI,MAAM;gBACT,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,WAAW,GAAG,KAAK,WAAW;YACrC;YACA,OAAO;QACR;QACA,MAAM,SAAQ,KAAkB;gBAAlB,EAAE,OAAO,EAAE,KAAK,EAAE,GAAlB;YACb,IAAI,OAAO;gBACV,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI;gBAC/B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,WAAW,GAAG,MAAM,WAAW;YACxC;YACA,OAAO;QACR;IACD;IACA,SAAS;QACR,UAAU;IACX;AACD", "debugId": null}}, {"offset": {"line": 481, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/api.ts"], "sourcesContent": ["import { signOut } from \"next-auth/react\";\nimport { signOut as ServerSignOut } from \"./auth\";\n\n// Configuration de l'API\nexport const API_BASE_URL =\n\tprocess.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000\";\n\n// Types pour l'authentification\nexport interface LoginRequest {\n\tusername: string;\n\tpassword: string;\n}\n\nexport interface LoginResponse {\n\taccess_token: string;\n\tuser: {\n\t\tid: string;\n\t\tusername: string;\n\t\tnom: string;\n\t\tprenom: string;\n\t\temail: string;\n\t\trole: \"admin\" | \"tresorier\" | \"membre\";\n\t\tstatut: \"actif\" | \"en_attente\" | \"suspendu\";\n\t};\n}\n\nexport interface RegisterRequest {\n\tusername: string;\n\tpassword: string;\n\trole: string;\n\t// statut?: \"actif\" | \"en_attente\" | \"suspendu\";\n}\n\nexport interface ApiError {\n\tmessage: string;\n\tstatusCode: number;\n\terror?: string;\n}\n\n// Service API\nexport class ApiService {\n\tprivate baseUrl: string;\n\n\tconstructor(baseUrl: string = API_BASE_URL) {\n\t\tthis.baseUrl = baseUrl;\n\t}\n\n\tprivate async request<T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\tconst url = `${this.baseUrl}${endpoint}`;\n\n\t\tconst config: RequestInit = {\n\t\t\theaders: {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t...options.headers,\n\t\t\t},\n\t\t\t...options,\n\t\t};\n\n\t\ttry {\n\t\t\tconst response = await fetch(url, config);\n\n\t\t\tif (!response.ok) {\n\t\t\t\tconst errorData: ApiError = await response.json().catch(() => ({\n\t\t\t\t\tmessage: \"Une erreur est survenue\",\n\t\t\t\t\tstatusCode: response.status,\n\t\t\t\t}));\n\n\t\t\t\t// 🚨 Gestion du cas 401\n\t\t\t\tif (response.status === 401) {\n\t\t\t\t\t// Si côté client → on déconnecte\n\t\t\t\t\tif (typeof window !== \"undefined\") {\n\t\t\t\t\t\tawait signOut({ callbackUrl: \"/auth/signin\" });\n\t\t\t\t\t} else {\n\t\t\t\t\t\tawait ServerSignOut({ redirectTo: \"/auth/signin\" });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthrow new Error(errorData.message || `HTTP ${response.status}`);\n\t\t\t}\n\n\t\t\treturn await response.json();\n\t\t} catch (error) {\n\t\t\tconsole.log(error);\n\n\t\t\tif (error instanceof Error) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t\tthrow new Error(\"Erreur de connexion au serveur\");\n\t\t}\n\t}\n\n\t// Authentification\n\tasync login(credentials: LoginRequest): Promise<LoginResponse> {\n\t\treturn this.request<LoginResponse>(\"/auth/login\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(credentials),\n\t\t});\n\t}\n\n\tasync register(\n\t\tuserData: RegisterRequest,\n\t): Promise<{ message: string; user: any }> {\n\t\treturn this.request<{ message: string; user: any }>(\"/auth/register\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(userData),\n\t\t});\n\t}\n\n\t// Méthodes avec authentification\n\tasync authenticatedRequest<T>(\n\t\tendpoint: string,\n\t\ttoken: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\treturn this.request<T>(endpoint, {\n\t\t\t...options,\n\t\t\theaders: {\n\t\t\t\t...options.headers,\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\tAuthorization: `Bearer ${token}`,\n\t\t\t},\n\t\t});\n\t}\n\n\t// Utilisateurs\n\tasync getUsers(token: string): Promise<any[]> {\n\t\treturn this.authenticatedRequest<any[]>(\"/users\", token);\n\t}\n\n\tasync getUser(id: string, token: string): Promise<any> {\n\t\treturn this.authenticatedRequest<any>(`/users/${id}`, token);\n\t}\n}\n\n// Instance par défaut\nexport const apiService = new ApiService();\n"], "names": [], "mappings": ";;;;;;;;AAKC;;AALD;AACA;;;;AAGO,MAAM,eACZ,sTAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAmC7B,MAAM;IAOZ,MAAc,QACb,QAAgB,EAEH;YADb,UAAA,iEAAuB,CAAC;QAExB,MAAM,MAAM,AAAC,GAAiB,OAAf,IAAI,CAAC,OAAO,EAAY,OAAT;QAE9B,MAAM,SAAsB;YAC3B,SAAS;gBACR,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACnB;YACA,GAAG,OAAO;QACX;QAEA,IAAI;YACH,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACjB,MAAM,YAAsB,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;wBAC9D,SAAS;wBACT,YAAY,SAAS,MAAM;oBAC5B,CAAC;gBAED,wBAAwB;gBACxB,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC5B,iCAAiC;oBACjC,wCAAmC;wBAClC,MAAM,IAAA,2QAAO,EAAC;4BAAE,aAAa;wBAAe;oBAC7C;;gBAGD;gBACA,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,QAAuB,OAAhB,SAAS,MAAM;YAC7D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC3B,EAAE,OAAO,OAAO;YACf,QAAQ,GAAG,CAAC;YAEZ,IAAI,iBAAiB,OAAO;gBAC3B,MAAM;YACP;YACA,MAAM,IAAI,MAAM;QACjB;IACD;IAEA,mBAAmB;IACnB,MAAM,MAAM,WAAyB,EAA0B;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAgB,eAAe;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,MAAM,SACL,QAAyB,EACiB;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAiC,kBAAkB;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,iCAAiC;IACjC,MAAM,qBACL,QAAgB,EAChB,KAAa,EAEA;YADb,UAAA,iEAAuB,CAAC;QAExB,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAChC,GAAG,OAAO;YACV,SAAS;gBACR,GAAG,QAAQ,OAAO;gBAClB,gBAAgB;gBAChB,eAAe,AAAC,UAAe,OAAN;YAC1B;QACD;IACD;IAEA,eAAe;IACf,MAAM,SAAS,KAAa,EAAkB;QAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAQ,UAAU;IACnD;IAEA,MAAM,QAAQ,EAAU,EAAE,KAAa,EAAgB;QACtD,OAAO,IAAI,CAAC,oBAAoB,CAAM,AAAC,UAAY,OAAH,KAAM;IACvD;IA1FA,YAAY,UAAkB,YAAY,CAAE;QAF5C,yPAAQ,WAAR,KAAA;QAGC,IAAI,CAAC,OAAO,GAAG;IAChB;AAyFD;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/use-api.ts"], "sourcesContent": ["import { useSession } from \"next-auth/react\";\nimport { apiService } from \"@/lib/api\";\nimport {\n\tSession,\n\tCaisse,\n\tReunion,\n\tMember,\n\tPayment,\n\tSessionMember,\n\tCreateSessionDto,\n\tUpdateSessionDto,\n\tCreateCaisseDto,\n\tUpdateCaisseDto,\n\tUpdateReunionDto,\n\tCreateMemberDto,\n\tUpdateMemberDto,\n\tCreatePaymentDto,\n\tCreateSessionMemberDto,\n\tMemberDebrief,\n\tPaymentFilters,\n} from \"@/types\";\n\nexport function useApi() {\n\tconst { data: session } = useSession();\n\n\tconst authenticatedRequest = async <T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> => {\n\t\tif (!session?.accessToken) {\n\t\t\tthrow new Error(\"Non authentifié\");\n\t\t}\n\n\t\treturn apiService.authenticatedRequest<T>(\n\t\t\tendpoint,\n\t\t\tsession.accessToken,\n\t\t\toptions,\n\t\t);\n\t};\n\n\treturn {\n\t\t// Méthodes d'authentification (pas besoin de token)\n\t\tlogin: apiService.login.bind(apiService),\n\t\tregister: apiService.register.bind(apiService),\n\n\t\t// Méthodes authentifiées\n\t\tauthenticatedRequest,\n\n\t\t// Raccourcis pour les endpoints courants\n\t\tgetUsers: () => authenticatedRequest<any[]>(\"/users\"),\n\t\tgetUser: (id: string) => authenticatedRequest<any>(`/users/${id}`),\n\t\tcreateUser: (userData: any) =>\n\t\t\tauthenticatedRequest<any>(\"/users\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tupdateUser: (id: string, userData: any) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tdeleteUser: (id: string) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Sessions\n\t\tgetSessions: () => authenticatedRequest<Session[]>(\"/sessions\"),\n\t\tgetSession: (id: string) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`),\n\t\tcreateSession: (sessionData: CreateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(\"/sessions\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tupdateSession: (id: string, sessionData: UpdateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tdeleteSession: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Caisses\n\t\tgetCaisses: () => authenticatedRequest<Caisse[]>(\"/caisses\"),\n\t\tgetCaisse: (id: string) => authenticatedRequest<Caisse>(`/caisses/${id}`),\n\t\tcreateCaisse: (caisseData: CreateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(\"/caisses\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tupdateCaisse: (id: string, caisseData: UpdateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tdeleteCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\temargerCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}/emarger`, {\n\t\t\t\tmethod: \"POST\",\n\t\t\t}),\n\n\t\t// Réunions\n\t\tgetReunions: () => authenticatedRequest<Reunion[]>(\"/reunions\"),\n\t\tgetReunion: (id: string) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`),\n\t\tupdateReunion: (id: string, reunionData: UpdateReunionDto) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(reunionData),\n\t\t\t}),\n\n\t\t// Members\n\t\tgetMembers: () => authenticatedRequest<Member[]>(\"/members\"),\n\t\tgetMember: (id: string) => authenticatedRequest<Member>(`/members/${id}`),\n\t\tcreateMember: (memberData: CreateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(\"/members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tupdateMember: (id: string, memberData: UpdateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(`/members/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tdeleteMember: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/members/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\tgetMemberDebrief: (id: string, filters?: PaymentFilters) => {\n\t\t\tconst params = new URLSearchParams();\n\t\t\tif (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);\n\t\t\tif (filters?.dateTo) params.append('dateTo', filters.dateTo);\n\t\t\tif (filters?.sessionId) params.append('sessionId', filters.sessionId);\n\t\t\tconst query = params.toString() ? `?${params.toString()}` : '';\n\t\t\treturn authenticatedRequest<MemberDebrief>(`/members/${id}/debrief${query}`);\n\t\t},\n\n\t\t// Payments\n\t\tcreatePayment: (paymentData: CreatePaymentDto) =>\n\t\t\tauthenticatedRequest<Payment>(\"/payments\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(paymentData),\n\t\t\t}),\n\n\t\t// Session Members (inscription des membres aux sessions)\n\t\tgetSessionMembers: (sessionId: string) =>\n\t\t\tauthenticatedRequest<SessionMember[]>(`/sessions/${sessionId}/members`),\n\t\taddSessionMember: (sessionMemberData: CreateSessionMemberDto) =>\n\t\t\tauthenticatedRequest<SessionMember>(\"/session-members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionMemberData),\n\t\t\t}),\n\t\tremoveSessionMember: (sessionId: string, memberId: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${sessionId}/members/${memberId}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t};\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAqBO,SAAS;;IACf,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IAEpC,MAAM,uBAAuB,eAC5B;YACA,2EAAuB,CAAC;QAExB,IAAI,EAAC,oBAAA,8BAAA,QAAS,WAAW,GAAE;YAC1B,MAAM,IAAI,MAAM;QACjB;QAEA,OAAO,8IAAU,CAAC,oBAAoB,CACrC,UACA,QAAQ,WAAW,EACnB;IAEF;IAEA,OAAO;QACN,oDAAoD;QACpD,OAAO,8IAAU,CAAC,KAAK,CAAC,IAAI,CAAC,8IAAU;QACvC,UAAU,8IAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,8IAAU;QAE7C,yBAAyB;QACzB;QAEA,yCAAyC;QACzC,UAAU,IAAM,qBAA4B;QAC5C,SAAS,CAAC,KAAe,qBAA0B,AAAC,UAAY,OAAH;QAC7D,YAAY,CAAC,WACZ,qBAA0B,UAAU;gBACnC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,YAAY,CAAC,IAAY,WACxB,qBAA0B,AAAC,UAAY,OAAH,KAAM;gBACzC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,YAAY,CAAC,KACZ,qBAA0B,AAAC,UAAY,OAAH,KAAM;gBACzC,QAAQ;YACT;QAED,WAAW;QACX,aAAa,IAAM,qBAAgC;QACnD,YAAY,CAAC,KACZ,qBAA8B,AAAC,aAAe,OAAH;QAC5C,eAAe,CAAC,cACf,qBAA8B,aAAa;gBAC1C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,eAAe,CAAC,IAAY,cAC3B,qBAA8B,AAAC,aAAe,OAAH,KAAM;gBAChD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,eAAe,CAAC,KACf,qBAA2B,AAAC,aAAe,OAAH,KAAM;gBAC7C,QAAQ;YACT;QAED,UAAU;QACV,YAAY,IAAM,qBAA+B;QACjD,WAAW,CAAC,KAAe,qBAA6B,AAAC,YAAc,OAAH;QACpE,cAAc,CAAC,aACd,qBAA6B,YAAY;gBACxC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,IAAY,aAC1B,qBAA6B,AAAC,YAAc,OAAH,KAAM;gBAC9C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,KACd,qBAA2B,AAAC,YAAc,OAAH,KAAM;gBAC5C,QAAQ;YACT;QACD,eAAe,CAAC,KACf,qBAA6B,AAAC,YAAc,OAAH,IAAG,aAAW;gBACtD,QAAQ;YACT;QAED,WAAW;QACX,aAAa,IAAM,qBAAgC;QACnD,YAAY,CAAC,KACZ,qBAA8B,AAAC,aAAe,OAAH;QAC5C,eAAe,CAAC,IAAY,cAC3B,qBAA8B,AAAC,aAAe,OAAH,KAAM;gBAChD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QAED,UAAU;QACV,YAAY,IAAM,qBAA+B;QACjD,WAAW,CAAC,KAAe,qBAA6B,AAAC,YAAc,OAAH;QACpE,cAAc,CAAC,aACd,qBAA6B,YAAY;gBACxC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,IAAY,aAC1B,qBAA6B,AAAC,YAAc,OAAH,KAAM;gBAC9C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,KACd,qBAA2B,AAAC,YAAc,OAAH,KAAM;gBAC5C,QAAQ;YACT;QACD,kBAAkB,CAAC,IAAY;YAC9B,MAAM,SAAS,IAAI;YACnB,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YACjE,IAAI,oBAAA,8BAAA,QAAS,MAAM,EAAE,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YAC3D,IAAI,oBAAA,8BAAA,QAAS,SAAS,EAAE,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;YACpE,MAAM,QAAQ,OAAO,QAAQ,KAAK,AAAC,IAAqB,OAAlB,OAAO,QAAQ,MAAO;YAC5D,OAAO,qBAAoC,AAAC,YAAwB,OAAb,IAAG,YAAgB,OAAN;QACrE;QAEA,WAAW;QACX,eAAe,CAAC,cACf,qBAA8B,aAAa;gBAC1C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QAED,yDAAyD;QACzD,mBAAmB,CAAC,YACnB,qBAAsC,AAAC,aAAsB,OAAV,WAAU;QAC9D,kBAAkB,CAAC,oBAClB,qBAAoC,oBAAoB;gBACvD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,qBAAqB,CAAC,WAAmB,WACxC,qBAA2B,AAAC,aAAiC,OAArB,WAAU,aAAoB,OAAT,WAAY;gBACxE,QAAQ;YACT;IACF;AACD;GA7IgB;;QACW,8QAAU", "debugId": null}}, {"offset": {"line": 708, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/app/dashboard/sessions/%5Bid%5D/edit/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSession } from \"next-auth/react\";\nimport { useRouter, useParams } from \"next/navigation\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { ArrowLeft, Calendar, DollarSign, Trash2 } from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport {\n\tForm,\n\tFormControl,\n\tFormDescription,\n\tFormField,\n\tFormItem,\n\tFormLabel,\n\tFormMessage,\n} from \"@/components/ui/form\";\nimport { useApi } from \"@/hooks/use-api\";\nimport { Session, SessionForm } from \"@/types\";\n\nconst sessionSchema = z\n\t.object({\n\t\tannee: z\n\t\t\t.number()\n\t\t\t.min(2020, \"L'année doit être supérieure à 2020\")\n\t\t\t.max(2050, \"L'année doit être inférieure à 2050\"),\n\t\tdateDebut: z.string().min(1, \"La date de début est requise\"),\n\t\tdateFin: z.string().min(1, \"La date de fin est requise\"),\n\t\tpartFixe: z\n\t\t\t.number()\n\t\t\t.min(1, \"La part fixe doit être supérieure à 0\")\n\t\t\t.max(1000000, \"La part fixe ne peut pas dépasser 1,000,000 FCFA\"),\n\t})\n\t.refine(\n\t\t(data) => {\n\t\t\tconst debut = new Date(data.dateDebut);\n\t\t\tconst fin = new Date(data.dateFin);\n\t\t\treturn fin > debut;\n\t\t},\n\t\t{\n\t\t\tmessage: \"La date de fin doit être postérieure à la date de début\",\n\t\t\tpath: [\"dateFin\"],\n\t\t},\n\t);\n\nexport default function EditSessionPage() {\n\tconst { id: sessionId } = useParams();\n\tconst { data: session, status } = useSession();\n\tconst router = useRouter();\n\tconst api = useApi();\n\n\tconst [sessionData, setSessionData] = useState<Session | null>(null);\n\tconst [isLoading, setIsLoading] = useState(false);\n\tconst [isDeleting, setIsDeleting] = useState(false);\n\tconst [error, setError] = useState<string | null>(null);\n\tconst [loading, setLoading] = useState(true);\n\n\t// Vérifier les permissions\n\tconst canEditSessions =\n\t\tsession?.user && session.user.role === \"secretary_general\";\n\n\tconst form = useForm<SessionForm>({\n\t\tresolver: zodResolver(sessionSchema),\n\t\tdefaultValues: {\n\t\t\tannee: new Date().getFullYear(),\n\t\t\tdateDebut: \"\",\n\t\t\tdateFin: \"\",\n\t\t\tpartFixe: 0,\n\t\t},\n\t});\n\n\t// Charger les données de la session\n\tuseEffect(() => {\n\t\tconst loadSession = async () => {\n\t\t\tif (!sessionId || typeof sessionId !== \"string\") return;\n\n\t\t\ttry {\n\t\t\t\tsetLoading(true);\n\t\t\t\tconst data = await api.getSession(sessionId);\n\t\t\t\tsetSessionData(data);\n\n\t\t\t\t// Mettre à jour le formulaire\n\t\t\t\tform.reset({\n\t\t\t\t\tannee: data.annee,\n\t\t\t\t\tdateDebut: data.dateDebut.split(\"T\")[0], // Format YYYY-MM-DD\n\t\t\t\t\tdateFin: data.dateFin.split(\"T\")[0],\n\t\t\t\t\tpartFixe: data.partFixe,\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(\"Erreur lors du chargement:\", error);\n\t\t\t\tsetError(\"Session introuvable\");\n\t\t\t} finally {\n\t\t\t\tsetLoading(false);\n\t\t\t}\n\t\t};\n\n\t\tif (session?.accessToken) {\n\t\t\tloadSession();\n\t\t}\n\t}, [sessionId, status]);\n\n\tconst onSubmit = async (data: SessionForm) => {\n\t\tif (!canEditSessions || !sessionId || typeof sessionId !== \"string\") {\n\t\t\tsetError(\"Vous n'avez pas les permissions pour modifier cette session\");\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tsetIsLoading(true);\n\t\t\tsetError(null);\n\n\t\t\tawait api.updateSession(sessionId, data);\n\t\t\trouter.push(\"/dashboard/sessions\");\n\t\t} catch (error: any) {\n\t\t\tconsole.error(\"Erreur lors de la modification:\", error);\n\t\t\tsetError(\n\t\t\t\terror.message || \"Une erreur est survenue lors de la modification\",\n\t\t\t);\n\t\t} finally {\n\t\t\tsetIsLoading(false);\n\t\t}\n\t};\n\n\tconst handleDelete = async () => {\n\t\tif (\n\t\t\t!canEditSessions ||\n\t\t\t!sessionId ||\n\t\t\ttypeof sessionId !== \"string\" ||\n\t\t\t!sessionData\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (\n\t\t\t!confirm(\n\t\t\t\t`Êtes-vous sûr de vouloir supprimer la session ${sessionData.annee} ? Cette action supprimera également toutes les réunions associées.`,\n\t\t\t)\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tsetIsDeleting(true);\n\t\t\tawait api.deleteSession(sessionId);\n\t\t\trouter.push(\"/dashboard/sessions\");\n\t\t} catch (error: any) {\n\t\t\tconsole.error(\"Erreur lors de la suppression:\", error);\n\t\t\tsetError(\n\t\t\t\terror.message || \"Une erreur est survenue lors de la suppression\",\n\t\t\t);\n\t\t} finally {\n\t\t\tsetIsDeleting(false);\n\t\t}\n\t};\n\n\tif (loading) {\n\t\treturn (\n\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t<div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"></div>\n\t\t\t\t\t<p className=\"mt-2 text-sm text-gray-600\">\n\t\t\t\t\t\tChargement de la session...\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tif (!canEditSessions) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Button variant=\"outline\" size=\"icon\" asChild>\n\t\t\t\t\t\t<Link href=\"/dashboard/sessions\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4\" />\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</Button>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<h1 className=\"text-3xl font-bold tracking-tight\">\n\t\t\t\t\t\t\tModifier Session\n\t\t\t\t\t\t</h1>\n\t\t\t\t\t\t<p className=\"text-muted-foreground\">\n\t\t\t\t\t\t\tModifier les paramètres de la session\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<Card>\n\t\t\t\t\t<CardContent className=\"pt-6\">\n\t\t\t\t\t\t<div className=\"text-center py-8\">\n\t\t\t\t\t\t\t<p className=\"text-muted-foreground\">\n\t\t\t\t\t\t\t\tVous n'avez pas les permissions pour modifier cette session.\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t<p className=\"text-sm text-muted-foreground mt-2\">\n\t\t\t\t\t\t\t\tSeuls les administrateurs peuvent modifier les sessions.\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tif (!sessionData) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Button variant=\"outline\" size=\"icon\" asChild>\n\t\t\t\t\t\t<Link href=\"/dashboard/sessions\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4\" />\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</Button>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<h1 className=\"text-3xl font-bold tracking-tight\">\n\t\t\t\t\t\t\tSession introuvable\n\t\t\t\t\t\t</h1>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<Card>\n\t\t\t\t\t<CardContent className=\"pt-6\">\n\t\t\t\t\t\t<div className=\"text-center py-8\">\n\t\t\t\t\t\t\t<p className=\"text-muted-foreground\">\n\t\t\t\t\t\t\t\tLa session demandée n'a pas été trouvée.\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t</div>\n\t\t);\n\t}\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* En-tête */}\n\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Button variant=\"outline\" size=\"icon\" asChild>\n\t\t\t\t\t\t<Link href=\"/dashboard/sessions\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4\" />\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</Button>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<h1 className=\"text-3xl font-bold tracking-tight\">\n\t\t\t\t\t\t\tModifier Session {sessionData.annee}\n\t\t\t\t\t\t</h1>\n\t\t\t\t\t\t<p className=\"text-muted-foreground\">\n\t\t\t\t\t\t\tModifier les paramètres de la session\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<Button\n\t\t\t\t\tvariant=\"destructive\"\n\t\t\t\t\tonClick={handleDelete}\n\t\t\t\t\tdisabled={isDeleting}\n\t\t\t\t>\n\t\t\t\t\t<Trash2 className=\"mr-2 h-4 w-4\" />\n\t\t\t\t\t{isDeleting ? \"Suppression...\" : \"Supprimer\"}\n\t\t\t\t</Button>\n\t\t\t</div>\n\n\t\t\t{/* Formulaire */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle className=\"flex items-center gap-2\">\n\t\t\t\t\t\t<Calendar className=\"h-5 w-5\" />\n\t\t\t\t\t\tInformations de la session\n\t\t\t\t\t</CardTitle>\n\t\t\t\t\t<CardDescription>\n\t\t\t\t\t\tModifiez les paramètres de la session. Attention : les modifications\n\t\t\t\t\t\tpeuvent affecter les réunions existantes.\n\t\t\t\t\t</CardDescription>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<Form {...form}>\n\t\t\t\t\t\t<form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n\t\t\t\t\t\t\t{error && (\n\t\t\t\t\t\t\t\t<div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n\t\t\t\t\t\t\t\t\t<p className=\"text-sm text-red-600\">{error}</p>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t\t<div className=\"grid gap-6 md:grid-cols-2\">\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"annee\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Année</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) =>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield.onChange(parseInt(e.target.value) || 0)\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\tL'année de la session de tontine\n\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"partFixe\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t\t\t\t<DollarSign className=\"h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t\tPart Fixe (FCFA)\n\t\t\t\t\t\t\t\t\t\t\t</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) =>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield.onChange(parseInt(e.target.value) || 0)\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\tMontant de la cotisation fixe par réunion\n\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t<div className=\"grid gap-6 md:grid-cols-2\">\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"dateDebut\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Date de début</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Input type=\"date\" {...field} />\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\tDate de début de la session\n\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"dateFin\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Date de fin</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Input type=\"date\" {...field} />\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\tDate de fin de la session\n\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t<div className=\"flex justify-end gap-4\">\n\t\t\t\t\t\t\t\t<Button variant=\"outline\" asChild>\n\t\t\t\t\t\t\t\t\t<Link href=\"/dashboard/sessions\">Annuler</Link>\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t<Button type=\"submit\" disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t{isLoading ? \"Modification...\" : \"Modifier la session\"}\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</form>\n\t\t\t\t\t</Form>\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAOA;AASA;;;AA7BA;;;;;;;;;;;;;;AAgCA,MAAM,gBAAgB,iPAAC,CACrB,MAAM,CAAC;IACP,OAAO,iPAAC,CACN,MAAM,GACN,GAAG,CAAC,MAAM,uCACV,GAAG,CAAC,MAAM;IACZ,WAAW,iPAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,SAAS,iPAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,UAAU,iPAAC,CACT,MAAM,GACN,GAAG,CAAC,GAAG,yCACP,GAAG,CAAC,SAAS;AAChB,GACC,MAAM,CACN,CAAC;IACA,MAAM,QAAQ,IAAI,KAAK,KAAK,SAAS;IACrC,MAAM,MAAM,IAAI,KAAK,KAAK,OAAO;IACjC,OAAO,MAAM;AACd,GACA;IACC,SAAS;IACT,MAAM;QAAC;KAAU;AAClB;AAGa,SAAS;;IACvB,MAAM,EAAE,IAAI,SAAS,EAAE,GAAG,IAAA,6RAAS;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,IAAA,8QAAU;IAC5C,MAAM,SAAS,IAAA,6RAAS;IACxB,MAAM,MAAM,IAAA,mJAAM;IAElB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,oTAAQ,EAAiB;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,oTAAQ,EAAC;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,oTAAQ,EAAC;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,oTAAQ,EAAgB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,oTAAQ,EAAC;IAEvC,2BAA2B;IAC3B,MAAM,kBACL,CAAA,oBAAA,8BAAA,QAAS,IAAI,KAAI,QAAQ,IAAI,CAAC,IAAI,KAAK;IAExC,MAAM,OAAO,IAAA,iRAAO,EAAc;QACjC,UAAU,IAAA,mSAAW,EAAC;QACtB,eAAe;YACd,OAAO,IAAI,OAAO,WAAW;YAC7B,WAAW;YACX,SAAS;YACT,UAAU;QACX;IACD;IAEA,oCAAoC;IACpC,IAAA,qTAAS;qCAAC;YACT,MAAM;yDAAc;oBACnB,IAAI,CAAC,aAAa,OAAO,cAAc,UAAU;oBAEjD,IAAI;wBACH,WAAW;wBACX,MAAM,OAAO,MAAM,IAAI,UAAU,CAAC;wBAClC,eAAe;wBAEf,8BAA8B;wBAC9B,KAAK,KAAK,CAAC;4BACV,OAAO,KAAK,KAAK;4BACjB,WAAW,KAAK,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;4BACvC,SAAS,KAAK,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;4BACnC,UAAU,KAAK,QAAQ;wBACxB;oBACD,EAAE,OAAO,OAAO;wBACf,QAAQ,KAAK,CAAC,8BAA8B;wBAC5C,SAAS;oBACV,SAAU;wBACT,WAAW;oBACZ;gBACD;;YAEA,IAAI,oBAAA,8BAAA,QAAS,WAAW,EAAE;gBACzB;YACD;QACD;oCAAG;QAAC;QAAW;KAAO;IAEtB,MAAM,WAAW,OAAO;QACvB,IAAI,CAAC,mBAAmB,CAAC,aAAa,OAAO,cAAc,UAAU;YACpE,SAAS;YACT;QACD;QAEA,IAAI;YACH,aAAa;YACb,SAAS;YAET,MAAM,IAAI,aAAa,CAAC,WAAW;YACnC,OAAO,IAAI,CAAC;QACb,EAAE,OAAO,OAAY;YACpB,QAAQ,KAAK,CAAC,mCAAmC;YACjD,SACC,MAAM,OAAO,IAAI;QAEnB,SAAU;YACT,aAAa;QACd;IACD;IAEA,MAAM,eAAe;QACpB,IACC,CAAC,mBACD,CAAC,aACD,OAAO,cAAc,YACrB,CAAC,aACA;YACD;QACD;QAEA,IACC,CAAC,QACA,AAAC,iDAAkE,OAAlB,YAAY,KAAK,EAAC,yEAEnE;YACD;QACD;QAEA,IAAI;YACH,cAAc;YACd,MAAM,IAAI,aAAa,CAAC;YACxB,OAAO,IAAI,CAAC;QACb,EAAE,OAAO,OAAY;YACpB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SACC,MAAM,OAAO,IAAI;QAEnB,SAAU;YACT,cAAc;QACf;IACD;IAEA,IAAI,SAAS;QACZ,qBACC,wUAAC;YAAI,WAAU;sBACd,cAAA,wUAAC;gBAAI,WAAU;;kCACd,wUAAC;wBAAI,WAAU;;;;;;kCACf,wUAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAM9C;IAEA,IAAI,CAAC,iBAAiB;QACrB,qBACC,wUAAC;YAAI,WAAU;;8BACd,wUAAC;oBAAI,WAAU;;sCACd,wUAAC,2JAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAO,OAAO;sCAC5C,cAAA,wUAAC,qTAAI;gCAAC,MAAK;0CACV,cAAA,wUAAC,gUAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAGvB,wUAAC;;8CACA,wUAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAGlD,wUAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAMvC,wUAAC,uJAAI;8BACJ,cAAA,wUAAC,8JAAW;wBAAC,WAAU;kCACtB,cAAA,wUAAC;4BAAI,WAAU;;8CACd,wUAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,wUAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQxD;IAEA,IAAI,CAAC,aAAa;QACjB,qBACC,wUAAC;YAAI,WAAU;;8BACd,wUAAC;oBAAI,WAAU;;sCACd,wUAAC,2JAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAO,OAAO;sCAC5C,cAAA,wUAAC,qTAAI;gCAAC,MAAK;0CACV,cAAA,wUAAC,gUAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAGvB,wUAAC;sCACA,cAAA,wUAAC;gCAAG,WAAU;0CAAoC;;;;;;;;;;;;;;;;;8BAMpD,wUAAC,uJAAI;8BACJ,cAAA,wUAAC,8JAAW;wBAAC,WAAU;kCACtB,cAAA,wUAAC;4BAAI,WAAU;sCACd,cAAA,wUAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ3C;IAEA,qBACC,wUAAC;QAAI,WAAU;;0BAEd,wUAAC;gBAAI,WAAU;;kCACd,wUAAC;wBAAI,WAAU;;0CACd,wUAAC,2JAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAO,OAAO;0CAC5C,cAAA,wUAAC,qTAAI;oCAAC,MAAK;8CACV,cAAA,wUAAC,gUAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGvB,wUAAC;;kDACA,wUAAC;wCAAG,WAAU;;4CAAoC;4CAC/B,YAAY,KAAK;;;;;;;kDAEpC,wUAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAMvC,wUAAC,2JAAM;wBACN,SAAQ;wBACR,SAAS;wBACT,UAAU;;0CAEV,wUAAC,uTAAM;gCAAC,WAAU;;;;;;4BACjB,aAAa,mBAAmB;;;;;;;;;;;;;0BAKnC,wUAAC,uJAAI;;kCACJ,wUAAC,6JAAU;;0CACV,wUAAC,4JAAS;gCAAC,WAAU;;kDACpB,wUAAC,yTAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGjC,wUAAC,kKAAe;0CAAC;;;;;;;;;;;;kCAKlB,wUAAC,8JAAW;kCACX,cAAA,wUAAC,uJAAI;4BAAE,GAAG,IAAI;sCACb,cAAA,wUAAC;gCAAK,UAAU,KAAK,YAAY,CAAC;gCAAW,WAAU;;oCACrD,uBACA,wUAAC;wCAAI,WAAU;kDACd,cAAA,wUAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;kDAIvC,wUAAC;wCAAI,WAAU;;0DACd,wUAAC,4JAAS;gDACT,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ;wDAAC,EAAE,KAAK,EAAE;yEACjB,wUAAC,2JAAQ;;0EACR,wUAAC,4JAAS;0EAAC;;;;;;0EACX,wUAAC,8JAAW;0EACX,cAAA,wUAAC,yJAAK;oEACL,MAAK;oEACJ,GAAG,KAAK;oEACT,UAAU,CAAC,IACV,MAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;0EAI9C,wUAAC,kKAAe;0EAAC;;;;;;0EAGjB,wUAAC,8JAAW;;;;;;;;;;;;;;;;;0DAKf,wUAAC,4JAAS;gDACT,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ;wDAAC,EAAE,KAAK,EAAE;yEACjB,wUAAC,2JAAQ;;0EACR,wUAAC,4JAAS;gEAAC,WAAU;;kFACpB,wUAAC,mUAAU;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAGnC,wUAAC,8JAAW;0EACX,cAAA,wUAAC,yJAAK;oEACL,MAAK;oEACJ,GAAG,KAAK;oEACT,UAAU,CAAC,IACV,MAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;0EAI9C,wUAAC,kKAAe;0EAAC;;;;;;0EAGjB,wUAAC,8JAAW;;;;;;;;;;;;;;;;;;;;;;;kDAMhB,wUAAC;wCAAI,WAAU;;0DACd,wUAAC,4JAAS;gDACT,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ;wDAAC,EAAE,KAAK,EAAE;yEACjB,wUAAC,2JAAQ;;0EACR,wUAAC,4JAAS;0EAAC;;;;;;0EACX,wUAAC,8JAAW;0EACX,cAAA,wUAAC,yJAAK;oEAAC,MAAK;oEAAQ,GAAG,KAAK;;;;;;;;;;;0EAE7B,wUAAC,kKAAe;0EAAC;;;;;;0EAGjB,wUAAC,8JAAW;;;;;;;;;;;;;;;;;0DAKf,wUAAC,4JAAS;gDACT,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ;wDAAC,EAAE,KAAK,EAAE;yEACjB,wUAAC,2JAAQ;;0EACR,wUAAC,4JAAS;0EAAC;;;;;;0EACX,wUAAC,8JAAW;0EACX,cAAA,wUAAC,yJAAK;oEAAC,MAAK;oEAAQ,GAAG,KAAK;;;;;;;;;;;0EAE7B,wUAAC,kKAAe;0EAAC;;;;;;0EAGjB,wUAAC,8JAAW;;;;;;;;;;;;;;;;;;;;;;;kDAMhB,wUAAC;wCAAI,WAAU;;0DACd,wUAAC,2JAAM;gDAAC,SAAQ;gDAAU,OAAO;0DAChC,cAAA,wUAAC,qTAAI;oDAAC,MAAK;8DAAsB;;;;;;;;;;;0DAElC,wUAAC,2JAAM;gDAAC,MAAK;gDAAS,UAAU;0DAC9B,YAAY,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1C;GAnVwB;;QACG,6RAAS;QACD,8QAAU;QAC7B,6RAAS;QACZ,mJAAM;QAYL,iRAAO;;;KAhBG", "debugId": null}}]}