"use client";

import { useSession } from "next-auth/react";
import { useSessions, useCaisses } from "@/hooks/queries";
import { DashboardStats } from "./dashboard-stats";
import { DashboardActions } from "./dashboard-actions";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, Loader2 } from "lucide-react";

interface DashboardClientProps {
  initialSessions?: any[];
  initialCaisses?: any[];
}

export function DashboardClient({ initialSessions = [], initialCaisses = [] }: DashboardClientProps) {
  const { data: session } = useSession();
  
  // Use React Query with initial data
  const sessionsQuery = useSessions();
  const caissesQuery = useCaisses();

  // Use React Query data if available, fallback to initial data
  const sessions = sessionsQuery.data || initialSessions;
  const caisses = caissesQuery.data || initialCaisses;
  
  const isLoading = sessionsQuery.isLoading || caissesQuery.isLoading;
  const hasError = sessionsQuery.error || caissesQuery.error;

  if (!session?.user) {
    return (
      <div className="flex items-center justify-center h-64">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircle className="mx-auto h-8 w-8 text-yellow-500 mb-2" />
            <CardTitle>Session requise</CardTitle>
            <CardDescription>
              Veuillez vous connecter pour accéder au tableau de bord
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="flex items-center justify-center h-64">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircle className="mx-auto h-8 w-8 text-red-500 mb-2" />
            <CardTitle>Erreur de chargement</CardTitle>
            <CardDescription>
              Impossible de charger les données du tableau de bord
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  const userRole = (session.user as any)?.role || "";

  return (
    <div className="space-y-6">
      {/* Welcome section */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Bienvenue, {(session.user as any)?.username}!
        </h1>
        <p className="text-gray-600 mt-1">
          Voici un aperçu de votre tontine aujourd'hui.
        </p>
        {session.user && (
          <div className="mt-2 text-sm text-gray-500">
            Connecté en tant que{" "}
            <span className="font-medium">{session.user.username}</span> (
            {session.user.role})
          </div>
        )}
      </div>

      {/* Loading indicator for real-time updates */}
      {isLoading && (
        <div className="flex items-center justify-center py-4">
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
          <span className="text-sm text-gray-500">Mise à jour des données...</span>
        </div>
      )}

      {/* Stats grid */}
      <DashboardStats 
        sessions={sessions} 
        caisses={caisses} 
        userRole={userRole} 
      />

      {/* Quick actions */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Actions rapides</h2>
        <DashboardActions userRole={userRole} />
      </div>
    </div>
  );
}
