import 'reflect-metadata';
import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { ReunionsService } from '../src/reunions/reunions.service';
import { Model, Types } from 'mongoose';
import { Reunion } from '../src/reunions/schemas/reunion.schema';
import { Session } from '../src/sessions/schemas/session.schema';
import { SessionMember } from '../src/sessions/schemas/session-member.schema';
import { Member } from '../src/members/schemas/member.schema';
import { Payment } from '../src/payments/schemas/payment.schema';

const mockModel = () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  findById: jest.fn(),
  findByIdAndUpdate: jest.fn(),
  deleteMany: jest.fn(),
  create: jest.fn(),
  lean: jest.fn(),
  sort: jest.fn(),
});

describe('ReunionsService', () => {
  let service: ReunionsService;
  let reunionModel: any;
  let sessionModel: any;
  let sessionMemberModel: any;
  let memberModel: any;
  let paymentModel: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReunionsService,
        { provide: getModelToken(Reunion.name), useFactory: mockModel },
        { provide: getModelToken(Session.name), useFactory: mockModel },
        { provide: getModelToken(SessionMember.name), useFactory: mockModel },
        { provide: getModelToken(Member.name), useFactory: mockModel },
        { provide: getModelToken(Payment.name), useFactory: mockModel },
      ],
    }).compile();

    service = module.get<ReunionsService>(ReunionsService);
    reunionModel = module.get(getModelToken(Reunion.name));
    sessionModel = module.get(getModelToken(Session.name));
    sessionMemberModel = module.get(getModelToken(SessionMember.name));
    memberModel = module.get(getModelToken(Member.name));
    paymentModel = module.get(getModelToken(Payment.name));
  });

  it('nextContribution saute les réunions couvertes par avance et retourne la prochaine exigible', async () => {
    const sessionId = new Types.ObjectId();
    const memberId = new Types.ObjectId();
    const session = { _id: sessionId, partFixe: 1000 };

    sessionModel.findById.mockResolvedValue(session);
    sessionMemberModel.findOne.mockResolvedValue({
      sessionId,
      memberId,
      parts: 2,
      paidSoFar: 6000,
      expectedToDate: 2000,
    });

    const now = new Date();
    const r1 = { _id: new Types.ObjectId(), sessionId, dateReunion: new Date(now.getTime() + 1 * 86400000) };
    const r2 = { _id: new Types.ObjectId(), sessionId, dateReunion: new Date(now.getTime() + 8 * 86400000) };

    const findCursor = { sort: jest.fn().mockResolvedValue([r1, r2]) } as any;
    reunionModel.find.mockReturnValue(findCursor);

    const res = await service.nextContribution(memberId.toString(), sessionId.toString());

    // partFixe * parts = 2000 par réunion
    // advance = 6000 - 2000 = 4000 -> couvre r1 et r2 complètement, donc aucune prochaine réunion à payer
    expect(res.nextReunion).toBeNull();
    expect(res.amountDue).toBe(0);
  });
});