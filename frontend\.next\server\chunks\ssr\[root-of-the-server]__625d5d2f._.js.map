{"version": 3, "sources": ["turbopack:///[project]/frontend/src/hooks/use-api.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/dollar-sign.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-collection@_579cf419d1ba9c23c3c58a897c3f75b1/node_modules/@radix-ui/react-collection/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-direction@1_f60df28923be704a001f3f7fab7b8a5c/node_modules/@radix-ui/react-direction/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/check.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-collection@_579cf419d1ba9c23c3c58a897c3f75b1/node_modules/@radix-ui/react-collection/src/collection-legacy.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/trash-2.ts", "turbopack:///[project]/frontend/src/components/ui/badge.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/plus.ts", "turbopack:///[project]/frontend/src/components/ui/table.tsx", "turbopack:///[project]/frontend/src/app/dashboard/sessions/page.tsx"], "sourcesContent": ["import { useSession } from \"next-auth/react\";\nimport { apiService } from \"@/lib/api\";\nimport {\n\tSession,\n\tCaisse,\n\tReunion,\n\tMember,\n\tPayment,\n\tSessionMember,\n\tCreateSessionDto,\n\tUpdateSessionDto,\n\tCreateCaisseDto,\n\tUpdateCaisseDto,\n\tUpdateReunionDto,\n\tCreateMemberDto,\n\tUpdateMemberDto,\n\tCreatePaymentDto,\n\tCreateSessionMemberDto,\n\tMemberDebrief,\n\tPaymentFilters,\n} from \"@/types\";\n\nexport function useApi() {\n\tconst { data: session } = useSession();\n\n\tconst authenticatedRequest = async <T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> => {\n\t\tif (!session?.accessToken) {\n\t\t\tthrow new Error(\"Non authentifié\");\n\t\t}\n\n\t\treturn apiService.authenticatedRequest<T>(\n\t\t\tendpoint,\n\t\t\tsession.accessToken,\n\t\t\toptions,\n\t\t);\n\t};\n\n\treturn {\n\t\t// Méthodes d'authentification (pas besoin de token)\n\t\tlogin: apiService.login.bind(apiService),\n\t\tregister: apiService.register.bind(apiService),\n\n\t\t// Méthodes authentifiées\n\t\tauthenticatedRequest,\n\n\t\t// Raccourcis pour les endpoints courants\n\t\tgetUsers: () => authenticatedRequest<any[]>(\"/users\"),\n\t\tgetUser: (id: string) => authenticatedRequest<any>(`/users/${id}`),\n\t\tcreateUser: (userData: any) =>\n\t\t\tauthenticatedRequest<any>(\"/users\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tupdateUser: (id: string, userData: any) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tdeleteUser: (id: string) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Sessions\n\t\tgetSessions: () => authenticatedRequest<Session[]>(\"/sessions\"),\n\t\tgetSession: (id: string) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`),\n\t\tcreateSession: (sessionData: CreateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(\"/sessions\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tupdateSession: (id: string, sessionData: UpdateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tdeleteSession: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Caisses\n\t\tgetCaisses: () => authenticatedRequest<Caisse[]>(\"/caisses\"),\n\t\tgetCaisse: (id: string) => authenticatedRequest<Caisse>(`/caisses/${id}`),\n\t\tcreateCaisse: (caisseData: CreateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(\"/caisses\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tupdateCaisse: (id: string, caisseData: UpdateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tdeleteCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\temargerCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}/emarger`, {\n\t\t\t\tmethod: \"POST\",\n\t\t\t}),\n\n\t\t// Réunions\n\t\tgetReunions: () => authenticatedRequest<Reunion[]>(\"/reunions\"),\n\t\tgetReunion: (id: string) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`),\n\t\tupdateReunion: (id: string, reunionData: UpdateReunionDto) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(reunionData),\n\t\t\t}),\n\n\t\t// Members\n\t\tgetMembers: () => authenticatedRequest<Member[]>(\"/members\"),\n\t\tgetMember: (id: string) => authenticatedRequest<Member>(`/members/${id}`),\n\t\tcreateMember: (memberData: CreateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(\"/members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tupdateMember: (id: string, memberData: UpdateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(`/members/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tdeleteMember: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/members/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\tgetMemberDebrief: (id: string, filters?: PaymentFilters) => {\n\t\t\tconst params = new URLSearchParams();\n\t\t\tif (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);\n\t\t\tif (filters?.dateTo) params.append('dateTo', filters.dateTo);\n\t\t\tif (filters?.sessionId) params.append('sessionId', filters.sessionId);\n\t\t\tconst query = params.toString() ? `?${params.toString()}` : '';\n\t\t\treturn authenticatedRequest<MemberDebrief>(`/members/${id}/debrief${query}`);\n\t\t},\n\n\t\t// Payments\n\t\tcreatePayment: (paymentData: CreatePaymentDto) =>\n\t\t\tauthenticatedRequest<Payment>(\"/payments\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(paymentData),\n\t\t\t}),\n\n\t\t// Session Members (inscription des membres aux sessions)\n\t\tgetSessionMembers: (sessionId: string) =>\n\t\t\tauthenticatedRequest<SessionMember[]>(`/sessions/${sessionId}/members`),\n\t\taddSessionMember: (sessionMemberData: CreateSessionMemberDto) =>\n\t\t\tauthenticatedRequest<SessionMember>(\"/session-members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionMemberData),\n\t\t\t}),\n\t\tremoveSessionMember: (sessionId: string, memberId: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${sessionId}/members/${memberId}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t};\n}\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n", "\"use client\";\n\n// src/collection-legacy.tsx\nimport React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createCollection(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: /* @__PURE__ */ new Map() }\n  );\n  const CollectionProvider = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef(null);\n    const itemMap = React.useRef(/* @__PURE__ */ new Map()).current;\n    return /* @__PURE__ */ jsx(CollectionProviderImpl, { scope, itemMap, collectionRef: ref, children });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ jsx(CollectionSlotImpl, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...itemData });\n        return () => void context.itemMap.delete(ref);\n      });\n      return /* @__PURE__ */ jsx(CollectionItemSlotImpl, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useCollection(scope) {\n    const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n    return getItems;\n  }\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope\n  ];\n}\n\n// src/collection.tsx\nimport React2 from \"react\";\nimport { createContextScope as createContextScope2 } from \"@radix-ui/react-context\";\nimport { useComposedRefs as useComposedRefs2 } from \"@radix-ui/react-compose-refs\";\nimport { createSlot as createSlot2 } from \"@radix-ui/react-slot\";\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n  #keys;\n  constructor(entries) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n  set(key, value) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n  insert(index, key, value) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n    if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n    const size = this.size + (has ? 0 : 1);\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n    const keys = [...this.#keys];\n    let nextValue;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i];\n        if (keys[i] === key) {\n          nextKey = keys[i + 1];\n        }\n        if (has) {\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1];\n        const currentValue = nextValue;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n  with(index, key, value) {\n    const copy = new _OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n  before(key) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n  after(key) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n  first() {\n    return this.entryAt(0);\n  }\n  last() {\n    return this.entryAt(-1);\n  }\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n  delete(key) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n  deleteAt(index) {\n    const key = this.keyAt(index);\n    if (key !== void 0) {\n      return this.delete(key);\n    }\n    return false;\n  }\n  at(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return this.get(key);\n    }\n  }\n  entryAt(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return [key, this.get(key)];\n    }\n  }\n  indexOf(key) {\n    return this.#keys.indexOf(key);\n  }\n  keyAt(index) {\n    return at(this.#keys, index);\n  }\n  from(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n  keyFrom(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n  find(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return void 0;\n  }\n  findIndex(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n  filter(predicate, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  map(callbackfn, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  reduce(...args) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0);\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n  reduceRight(...args) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1);\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index);\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n  toSorted(compareFn) {\n    const entries = [...this.entries()].sort(compareFn);\n    return new _OrderedDict(entries);\n  }\n  toReversed() {\n    const reversed = new _OrderedDict();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n  toSpliced(...args) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new _OrderedDict(entries);\n  }\n  slice(start, end) {\n    const result = new _OrderedDict();\n    let stop = this.size - 1;\n    if (start === void 0) {\n      return result;\n    }\n    if (start < 0) {\n      start = start + this.size;\n    }\n    if (end !== void 0 && end > 0) {\n      stop = end - 1;\n    }\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      result.set(key, element);\n    }\n    return result;\n  }\n  every(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n  some(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n};\nfunction at(array, index) {\n  if (\"at\" in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n\n// src/collection.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nfunction createCollection2(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = createContextScope2(PROVIDER_NAME);\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0\n    }\n  );\n  const CollectionProvider = ({ state, ...props }) => {\n    return state ? /* @__PURE__ */ jsx2(CollectionProviderImpl, { ...props, state }) : /* @__PURE__ */ jsx2(CollectionInit, { ...props });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const CollectionInit = (props) => {\n    const state = useInitCollection();\n    return /* @__PURE__ */ jsx2(CollectionProviderImpl, { ...props, state });\n  };\n  CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n  const CollectionProviderImpl = (props) => {\n    const { scope, children, state } = props;\n    const ref = React2.useRef(null);\n    const [collectionElement, setCollectionElement] = React2.useState(\n      null\n    );\n    const composeRefs = useComposedRefs2(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n    React2.useEffect(() => {\n      if (!collectionElement) return;\n      const observer = getChildListObserver(() => {\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n    return /* @__PURE__ */ jsx2(\n      CollectionContextProvider,\n      {\n        scope,\n        itemMap,\n        setItemMap,\n        collectionRef: composeRefs,\n        collectionRefObject: ref,\n        collectionElement,\n        children\n      }\n    );\n  };\n  CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = createSlot2(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React2.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs2(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ jsx2(CollectionSlotImpl, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = createSlot2(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React2.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React2.useRef(null);\n      const [element, setElement] = React2.useState(null);\n      const composedRefs = useComposedRefs2(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      const { setItemMap } = context;\n      const itemDataRef = React2.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n      React2.useEffect(() => {\n        const itemData2 = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n          if (!map.has(element)) {\n            map.set(element, { ...itemData2, element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n          return map.set(element, { ...itemData2, element }).toSorted(sortByDocumentPosition);\n        });\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n      return /* @__PURE__ */ jsx2(CollectionItemSlotImpl, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useInitCollection() {\n    return React2.useState(new OrderedDict());\n  }\n  function useCollection(scope) {\n    const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n    return itemMap;\n  }\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection\n  };\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions\n  ];\n}\nfunction shallowEqual(a, b) {\n  if (a === b) return true;\n  if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\nfunction isElementPreceding(a, b) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n  return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === \"childList\") {\n        callback();\n        return;\n      }\n    }\n  });\n  return observer;\n}\nexport {\n  createCollection,\n  createCollection2 as unstable_createCollection\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/direction/src/direction.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DirectionContext = React.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ jsx(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\nexport {\n  DirectionProvider,\n  Provider,\n  useDirection\n};\n//# sourceMappingURL=index.mjs.map\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M20 6 9 17l-5-5', key: '1gmf2c' }]];\n\n/**\n * @component @name Check\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgNiA5IDE3bC01LTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Check = createLucideIcon('check', __iconNode);\n\nexport default Check;\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement | null>;\n    itemMap: Map<\n      React.RefObject<ItemElement | null>,\n      { ref: React.RefObject<ItemElement | null> } & ItemData\n    >;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 11v6', key: 'nco0om' }],\n  ['path', { d: 'M14 11v6', key: 'outv1u' }],\n  ['path', { d: 'M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6', key: 'miytrc' }],\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2', key: 'e791ji' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTF2NiIgLz4KICA8cGF0aCBkPSJNMTQgMTF2NiIgLz4KICA8cGF0aCBkPSJNMTkgNnYxNGEyIDIgMCAwIDEtMiAySDdhMiAyIDAgMCAxLTItMlY2IiAvPgogIDxwYXRoIGQ9Ik0zIDZoMTgiIC8+CiAgPHBhdGggZD0iTTggNlY0YTIgMiAwIDAgMSAyLTJoNGEyIDIgMCAwIDEgMiAydjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n", "\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n", "\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSession } from \"next-auth/react\";\nimport {\n\tPlus,\n\tSearch,\n\tFilter,\n\tMoreHorizontal,\n\tEdit,\n\tTrash2,\n\tCalendar,\n\tUsers,\n\tDollarSign,\n} from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport {\n\tTable,\n\tTableBody,\n\tTableCell,\n\tTableHead,\n\tTableHeader,\n\tTableRow,\n} from \"@/components/ui/table\";\nimport {\n\tDropdownMenu,\n\tDropdownMenuContent,\n\tDropdownMenuItem,\n\tDropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport {\n\tSelect,\n\tSelectContent,\n\tSelectItem,\n\tSelectTrigger,\n\tSelectValue,\n} from \"@/components/ui/select\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { useApi } from \"@/hooks/use-api\";\nimport { Session, SessionStats } from \"@/types\";\n\nexport default function SessionsPage() {\n\tconst { data: session } = useSession();\n\tconst api = useApi();\n\n\tconst [sessions, setSessions] = useState<Session[]>([]);\n\tconst [stats, setStats] = useState<SessionStats | null>(null);\n\tconst [loading, setLoading] = useState(true);\n\tconst [searchTerm, setSearchTerm] = useState(\"\");\n\tconst [yearFilter, setYearFilter] = useState<string>(\"all\");\n\n\t// Vérifier les permissions\n\tconst canManageSessions =\n\t\tsession?.user &&\n\t\t((session.user as any).role === \"secretary_general\" ||\n\t\t\t(session.user as any).role === \"controller\" ||\n\t\t\t(session.user as any).role === \"cashier\");\n\tconst canCreateSessions =\n\t\tsession?.user && (session.user as any).role === \"secretary_general\";\n\n\tconst loadData = async () => {\n\t\ttry {\n\t\t\tsetLoading(true);\n\t\t\tconst sessionsData = await api.getSessions();\n\t\t\tsetSessions(sessionsData);\n\n\t\t\t// Calculer les statistiques\n\t\t\tconst now = new Date();\n\t\t\tconst active = sessionsData.filter(\n\t\t\t\t(s) => new Date(s.dateDebut) <= now && new Date(s.dateFin) >= now\n\t\t\t).length;\n\t\t\tconst completed = sessionsData.filter(\n\t\t\t\t(s) => new Date(s.dateFin) < now\n\t\t\t).length;\n\t\t\tconst totalPartFixe = sessionsData.reduce(\n\t\t\t\t(sum, s) => sum + s.partFixe,\n\t\t\t\t0\n\t\t\t);\n\n\t\t\tsetStats({\n\t\t\t\ttotal: sessionsData.length,\n\t\t\t\tactive,\n\t\t\t\tcompleted,\n\t\t\t\ttotalPartFixe,\n\t\t\t});\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors du chargement des sessions:\", error);\n\t\t} finally {\n\t\t\tsetLoading(false);\n\t\t}\n\t};\n\n\tuseEffect(() => {\n\t\tif (session?.accessToken) {\n\t\t\tloadData();\n\t\t}\n\t}, [session]);\n\n\tconst handleDelete = async (sessionId: string) => {\n\t\tif (!confirm(\"Êtes-vous sûr de vouloir supprimer cette session ?\")) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tawait api.deleteSession(sessionId);\n\t\t\tloadData();\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors de la suppression:\", error);\n\t\t}\n\t};\n\n\t// Filtrer les sessions\n\tconst filteredSessions = sessions.filter((session) => {\n\t\tconst matchesSearch =\n\t\t\tsession.annee.toString().includes(searchTerm) ||\n\t\t\tnew Date(session.dateDebut)\n\t\t\t\t.toLocaleDateString()\n\t\t\t\t.includes(searchTerm) ||\n\t\t\tnew Date(session.dateFin).toLocaleDateString().includes(searchTerm);\n\n\t\tconst matchesYear =\n\t\t\tyearFilter === \"all\" || session.annee.toString() === yearFilter;\n\n\t\treturn matchesSearch && matchesYear;\n\t});\n\n\t// Obtenir les années uniques pour le filtre\n\tconst uniqueYears = Array.from(\n\t\tnew Set(sessions.map((s) => s.annee.toString()))\n\t).sort();\n\n\tconst getSessionStatus = (session: Session) => {\n\t\tconst now = new Date();\n\t\tconst start = new Date(session.dateDebut);\n\t\tconst end = new Date(session.dateFin);\n\n\t\tif (now < start) return { label: \"À venir\", variant: \"secondary\" as const };\n\t\tif (now > end) return { label: \"Terminée\", variant: \"outline\" as const };\n\t\treturn { label: \"Active\", variant: \"default\" as const };\n\t};\n\n\tif (loading) {\n\t\treturn (\n\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t<div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"></div>\n\t\t\t\t\t<p className=\"mt-2 text-sm text-gray-600\">\n\t\t\t\t\t\tChargement des sessions...\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* En-tête */}\n\t\t\t<div className=\"flex justify-between items-center\">\n\t\t\t\t<div>\n\t\t\t\t\t<h1 className=\"text-3xl font-bold tracking-tight\">Sessions</h1>\n\t\t\t\t\t<p className=\"text-muted-foreground\">\n\t\t\t\t\t\tGérez les sessions de tontine et leurs réunions\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t\t{canCreateSessions && (\n\t\t\t\t\t<Button asChild>\n\t\t\t\t\t\t<Link href=\"/dashboard/sessions/new\">\n\t\t\t\t\t\t\t<Plus className=\"mr-2 h-4 w-4\" />\n\t\t\t\t\t\t\tNouvelle session\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</Button>\n\t\t\t\t)}\n\t\t\t</div>\n\n\t\t\t{/* Statistiques */}\n\t\t\t{stats && (\n\t\t\t\t<div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium\">\n\t\t\t\t\t\t\t\tTotal Sessions\n\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t<Calendar className=\"h-4 w-4 text-muted-foreground\" />\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold\">{stats.total}</div>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium\">\n\t\t\t\t\t\t\t\tSessions Actives\n\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t<Users className=\"h-4 w-4 text-muted-foreground\" />\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold\">{stats.active}</div>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium\">\n\t\t\t\t\t\t\t\tSessions Terminées\n\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t<Calendar className=\"h-4 w-4 text-muted-foreground\" />\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold\">{stats.completed}</div>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium\">\n\t\t\t\t\t\t\t\tPart Fixe Totale\n\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t<DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold\">\n\t\t\t\t\t\t\t\t{stats.totalPartFixe.toLocaleString()} FCFA\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t</div>\n\t\t\t)}\n\n\t\t\t{/* Filtres */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle>Filtres</CardTitle>\n\t\t\t\t\t<CardDescription>\n\t\t\t\t\t\tRecherchez et filtrez les sessions\n\t\t\t\t\t</CardDescription>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<div className=\"flex gap-4\">\n\t\t\t\t\t\t<div className=\"flex-1\">\n\t\t\t\t\t\t\t<div className=\"relative\">\n\t\t\t\t\t\t\t\t<Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\n\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\tplaceholder=\"Rechercher par année ou date...\"\n\t\t\t\t\t\t\t\t\tvalue={searchTerm}\n\t\t\t\t\t\t\t\t\tonChange={(e) => setSearchTerm(e.target.value)}\n\t\t\t\t\t\t\t\t\tclassName=\"pl-8\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<Select value={yearFilter} onValueChange={setYearFilter}>\n\t\t\t\t\t\t\t<SelectTrigger className=\"w-[180px]\">\n\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Filtrer par année\" />\n\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t<SelectItem value=\"all\">Toutes les années</SelectItem>\n\t\t\t\t\t\t\t\t{uniqueYears.map((year) => (\n\t\t\t\t\t\t\t\t\t<SelectItem key={year} value={year}>\n\t\t\t\t\t\t\t\t\t\t{year}\n\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t</Select>\n\t\t\t\t\t</div>\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\n\t\t\t{/* Tableau des sessions */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle>Sessions ({filteredSessions.length})</CardTitle>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<Table>\n\t\t\t\t\t\t<TableHeader>\n\t\t\t\t\t\t\t<TableRow>\n\t\t\t\t\t\t\t\t<TableHead>Année</TableHead>\n\t\t\t\t\t\t\t\t<TableHead>Période</TableHead>\n\t\t\t\t\t\t\t\t<TableHead>Part Fixe</TableHead>\n\t\t\t\t\t\t\t\t<TableHead>Statut</TableHead>\n\t\t\t\t\t\t\t\t<TableHead>Créée le</TableHead>\n\t\t\t\t\t\t\t\t{canManageSessions && <TableHead>Actions</TableHead>}\n\t\t\t\t\t\t\t</TableRow>\n\t\t\t\t\t\t</TableHeader>\n\t\t\t\t\t\t<TableBody>\n\t\t\t\t\t\t\t{filteredSessions.map((session) => {\n\t\t\t\t\t\t\t\tconst status = getSessionStatus(session);\n\t\t\t\t\t\t\t\treturn (\n\t\t\t\t\t\t\t\t\t<TableRow key={session._id}>\n\t\t\t\t\t\t\t\t\t\t<TableCell className=\"font-medium\">\n\t\t\t\t\t\t\t\t\t\t\t{session.annee}\n\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t{new Date(session.dateDebut).toLocaleDateString()} -{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t{new Date(session.dateFin).toLocaleDateString()}\n\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t{session.partFixe.toLocaleString()} FCFA\n\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t<Badge variant={status.variant}>{status.label}</Badge>\n\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t{new Date(session.createdAt).toLocaleDateString()}\n\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t{canManageSessions && (\n\t\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenu>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuTrigger asChild>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MoreHorizontal className=\"h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenuTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuContent align=\"end\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuItem asChild>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thref={`/dashboard/sessions/${session._id}/edit`}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Edit className=\"mr-2 h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tModifier\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenuItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{canCreateSessions && (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuItem\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleDelete(session._id)}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"text-red-600\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Trash2 className=\"mr-2 h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tSupprimer\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenuItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenuContent>\n\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenu>\n\t\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t</TableRow>\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\t})}\n\t\t\t\t\t\t</TableBody>\n\t\t\t\t\t</Table>\n\t\t\t\t\t{filteredSessions.length === 0 && (\n\t\t\t\t\t\t<div className=\"text-center py-8\">\n\t\t\t\t\t\t\t<p className=\"text-muted-foreground\">Aucune session trouvée</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t)}\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": "gPAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAqBO,SAAS,IACf,GAAM,CAAE,KAAM,CAAO,CAAE,CAAG,CAAA,EAAA,EAAA,UAAA,AAAU,IAE9B,EAAuB,MAC5B,EACA,EAAuB,CAAC,CAAC,IAEzB,GAAI,CAAC,GAAS,YACb,CAD0B,KACpB,AAAI,MAAM,mBAGjB,OAAO,EAAA,UAAU,CAAC,oBAAoB,CACrC,EACA,EAAQ,WAAW,CACnB,EAEF,EAEA,MAAO,CAEN,MAAO,EAAA,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAA,UAAU,EACvC,SAAU,EAAA,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAA,UAAU,uBAG7C,EAGA,SAAU,IAAM,EAA4B,UAC5C,QAAS,AAAC,GAAe,EAA0B,CAAC,OAAO,EAAE,EAAA,CAAI,EACjE,WAAa,AAAD,GACX,EAA0B,SAAU,CACnC,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,WAAY,CAAC,EAAY,IACxB,EAA0B,CAAC,OAAO,EAAE,EAAA,CAAI,CAAE,CACzC,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,WAAY,AAAC,GACZ,EAA0B,CAAC,OAAO,EAAE,EAAA,CAAI,CAAE,CACzC,OAAQ,QACT,GAGD,YAAa,IAAM,EAAgC,aACnD,WAAY,AAAC,GACZ,EAA8B,CAAC,UAAU,EAAE,EAAA,CAAI,EAChD,cAAe,AAAC,GACf,EAA8B,YAAa,CAC1C,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,cAAe,CAAC,EAAY,IAC3B,EAA8B,CAAC,UAAU,EAAE,EAAA,CAAI,CAAE,CAChD,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,cAAe,AAAC,GACf,EAA2B,CAAC,UAAU,EAAE,EAAA,CAAI,CAAE,CAC7C,OAAQ,QACT,GAGD,WAAY,IAAM,EAA+B,YACjD,UAAW,AAAC,GAAe,EAA6B,CAAC,SAAS,EAAE,EAAA,CAAI,EACxE,aAAc,AAAC,GACd,EAA6B,WAAY,CACxC,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,aAAc,CAAC,EAAY,IAC1B,EAA6B,CAAC,SAAS,EAAE,EAAA,CAAI,CAAE,CAC9C,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,aAAc,AAAC,GACd,EAA2B,CAAC,SAAS,EAAE,EAAA,CAAI,CAAE,CAC5C,OAAQ,QACT,GACD,cAAe,AAAC,GACf,EAA6B,CAAC,SAAS,EAAE,EAAG,QAAQ,CAAC,CAAE,CACtD,OAAQ,MACT,GAGD,YAAa,IAAM,EAAgC,aACnD,WAAY,AAAC,GACZ,EAA8B,CAAC,UAAU,EAAE,EAAA,CAAI,EAChD,cAAe,CAAC,EAAY,IAC3B,EAA8B,CAAC,UAAU,EAAE,EAAA,CAAI,CAAE,CAChD,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GAGD,WAAY,IAAM,EAA+B,YACjD,UAAW,AAAC,GAAe,EAA6B,CAAC,SAAS,EAAE,EAAA,CAAI,EACxE,aAAc,AAAC,GACd,EAA6B,WAAY,CACxC,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,aAAc,CAAC,EAAY,IAC1B,EAA6B,CAAC,SAAS,EAAE,EAAA,CAAI,CAAE,CAC9C,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,aAAc,AAAC,GACd,EAA2B,CAAC,SAAS,EAAE,EAAA,CAAI,CAAE,CAC5C,OAAQ,QACT,GACD,iBAAkB,CAAC,EAAY,KAC9B,IAAM,EAAS,IAAI,gBACf,GAAS,UAAU,EAAO,MAAM,CAAC,WAAY,EAAQ,QAAQ,EAC7D,GAAS,QAAQ,EAAO,MAAM,CAAC,SAAU,EAAQ,MAAM,EACvD,GAAS,WAAW,EAAO,MAAM,CAAC,YAAa,EAAQ,SAAS,EACpE,IAAM,EAAQ,EAAO,QAAQ,GAAK,CAAC,CAAC,EAAE,EAAO,QAAQ,GAAA,CAAI,CAAG,GAC5D,OAAO,EAAoC,CAAC,SAAS,EAAE,EAAG,QAAQ,EAAE,EAAA,CAAO,CAC5E,EAGA,cAAe,AAAC,GACf,EAA8B,YAAa,CAC1C,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GAGD,kBAAmB,AAAC,GACnB,EAAsC,CAAC,UAAU,EAAE,EAAU,QAAQ,CAAC,EACvE,iBAAkB,AAAC,GAClB,EAAoC,mBAAoB,CACvD,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,oBAAqB,CAAC,EAAmB,IACxC,EAA2B,CAAC,UAAU,EAAE,EAAU,SAAS,EAAE,EAAA,CAAU,CAAE,CACxE,OAAQ,QACT,EACF,CACD,0DChJA,CAAA,CAAA,CAAA,CAAM,AAAN,CAAA,CAAM,CAAA,EAAa,CAAA,CAAA,CAAA,CAAA,CAAb,AAAa,CAAb,AAAa,CAAb,AAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAhBA,CAClC,AAeiD,CAAA,AAfhD,CAAA,AAegD,CAfhD,AAegD,CAfhD,AAegD,CAAA,AAfhD,CAegD,AAfhD,CAAA,AAegD,CAAA,AAfxC,AAAR,CAAQ,AAAE,AAesC,CAftC,AAegD,CAfhD,AAegD,CAf5C,CAAA,CAAA,CAAA,CAAA,CAAM,AAAN,CAAM,CAAA,CAAA,AAAI,CAAA,CAAA,CAAA,CAAA,CAAA,AAAM,EAAA,CAAA,AAAI,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,AAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACjE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAqD,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACpF,4ECHA,IAAA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,SAAS,EAAiB,CAAI,EAC5B,IAAM,EAAgB,EAAO,qBACvB,CAAC,EAAyB,EAAsB,CAAG,CAAA,EAAA,EAAA,kBAAA,AAAkB,EAAC,GACtE,CAAC,EAAwB,EAAqB,CAAG,EACrD,EACA,CAAE,cAAe,CAAE,QAAS,IAAK,EAAG,QAAyB,CAAhB,GAAoB,GAAM,GAEnE,EAAqB,AAAC,EAFgC,EAG1D,GAAM,CAAE,OAAK,CAAE,UAAQ,CAAE,CAAG,EACtB,EAAM,EAAA,OAAK,CAAC,MAAM,CAAC,MACnB,EAAU,EAAA,OAAK,CAAC,MAAM,CAAiB,AAAhB,IAAoB,KAAO,IAAd,GAAqB,CAC/D,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAwB,EAA/B,KAAiC,UAAO,EAAS,cAAe,WAAK,CAAS,EACpG,CACA,GAAmB,WAAW,CAAG,EACjC,IAAM,EAAuB,EAAO,iBAC9B,EAAqB,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,GAChC,EAAiB,EAAA,OAAK,CAAC,UAAU,CACrC,CAAC,EAAO,KACN,GAAM,OAAE,CAAK,UAAE,CAAQ,CAAE,CAAG,EACtB,EAAU,EAAqB,EAAsB,GACrD,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,EAAQ,aAAa,EACxE,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAoB,CAAE,CAA7B,GAAkC,EAAc,UAAS,EAC/E,GAEF,EAAe,WAAW,CAAG,EAC7B,IAAM,EAAiB,EAAO,qBACxB,EAAiB,6BACjB,EAAyB,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,GACpC,EAAqB,EAAA,OAAK,CAAC,UAAU,CACzC,CAAC,EAAO,KACN,GAAM,OAAE,CAAK,UAAE,CAAQ,CAAE,GAAG,EAAU,CAAG,EACnC,EAAM,EAAA,OAAK,CAAC,MAAM,CAAC,MACnB,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,GAC7C,EAAU,EAAqB,EAAgB,GAKrD,OAAO,AAJP,EAAA,OAAK,CAAC,GAIc,MAJL,CAAC,KACd,EAAQ,OAAO,CAAC,GAAG,CAAC,EAAK,CAAE,MAAK,GAAG,CAAS,AAAD,GACpC,IAAM,KAAK,EAAQ,OAAO,CAAC,MAAM,CAAC,KAEpB,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EAAwB,CAAE,GAAG,CAAE,CAAC,EAAe,CAAE,EAAG,CAAC,CAAE,IAAK,WAAc,CAAS,EAChH,UAEF,EAAmB,WAAW,CAAG,EAe1B,CACL,CAAE,SAAU,EAAoB,KAAM,EAAgB,SAAU,CAAmB,EAfrF,SAAS,AAAc,CAAK,EAC1B,IAAM,EAAU,EAAqB,EAAO,qBAAsB,GAWlE,OAAO,AAVU,EAAA,OAAK,CAAC,WAAW,CAAC,KACjC,IAAM,EAAiB,EAAQ,aAAa,CAAC,OAAO,CACpD,GAAI,CAAC,EAAgB,MAAO,EAAE,CAC9B,IAAM,EAAe,MAAM,IAAI,CAAC,EAAe,gBAAgB,CAAC,CAAC,CAAC,EAAE,EAAe,CAAC,CAAC,GAKrF,OAAO,AAHc,AADP,MAAM,IAAI,CAAC,EAAQ,OAAO,CAAC,MAAM,IACpB,IAAI,CAC7B,CAAC,EAAG,IAAM,EAAa,OAAO,CAAC,EAAE,GAAG,CAAC,OAAO,EAAI,EAAa,OAAO,CAAC,EAAE,GAAG,CAAC,OAAO,EAGtF,EAAG,CAAC,EAAQ,aAAa,CAAE,EAAQ,OAAO,CAAC,CAE7C,EAIE,EACD,AACH,CASA,IAAI,EAAiC,IAAI,QA+RzC,GA/RqB,MA+RZ,EAAG,CAAK,CAAE,CAAK,EA/RU,AAgShC,GAAI,OAAQ,MAAM,SAAS,CACzB,CAD2B,MACpB,MAAM,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,EAAO,GAExC,IAAM,EAAc,AAGtB,SAAS,AAAY,CAAK,CAAE,CAAK,EAC/B,IAAM,EAAS,EAAM,MAAM,CACrB,EAAgB,EAAc,GAC9B,EAAc,GAAiB,EAAI,EAAgB,EAAS,EAClE,OAAO,EAAc,GAAK,GAAe,EAAS,CAAC,EAAI,CACzD,EARkC,EAAO,GACvC,OAAuB,CAAC,IAAjB,EAAqB,KAAK,EAAI,CAAK,CAAC,EAAY,AACzD,CAOA,SAAS,EAAc,CAAM,EAC3B,OAAO,GAAW,GAAqB,IAAX,EAAe,EAAI,KAAK,KAAK,CAAC,EAC5D,EA7SkB,MAAM,UAAqB,KAC3C,CAAM,AAAN,AAAK,AACL,aAAY,CAAO,CAAE,CACnB,KAAK,CAAC,GACN,IAAI,EAAC,CAAK,AAAL,CAAQ,IAAI,KAAK,CAAC,OAAO,CAC9B,EAAe,GAAG,CAAC,IAAI,CAAE,GAC3B,CACA,IAAI,CAAG,CAAE,CAAK,CAAE,CASd,OARI,EAAe,GAAG,CAAC,IAAI,GAAG,CACxB,IAAI,CAAC,GAAG,CAAC,GACX,GADiB,CACb,EAAC,CAAK,AAAL,CAAM,IAAI,EAAC,CAAA,AAAK,CAAC,OAAO,CAAC,GAAK,CAAG,EAEtC,IAAI,EAAC,CAAA,AAAK,CAAC,IAAI,CAAC,IAGpB,KAAK,CAAC,IAAI,EAAK,GACR,IACT,AADa,CAEb,OAAO,CAAK,CAAE,CAAG,CAAE,CAAK,CAAE,CACxB,IAcI,EAdE,EAAM,IAAI,CAAC,GAAG,CAAC,GACf,EAAS,IAAI,EAAC,CAAA,AAAK,CAAC,MAAM,CAC1B,EAAgB,EAAc,GAChC,EAAc,GAAiB,EAAI,EAAgB,EAAS,EAC1D,EAAY,EAAc,GAAK,GAAe,EAAS,CAAC,EAAI,EAClE,GAAI,IAAc,IAAI,CAAC,IAAI,EAAI,GAAO,IAAc,IAAI,CAAC,IAAI,CAAG,GAAK,AAAc,CAAC,GAAG,GAErF,OADA,IAAI,CAAC,GAAG,CAAC,EAAK,GACP,IAAI,CAEb,IAAM,EAAO,IAAI,CAAC,IAAI,GAAG,CAAC,EACtB,EAAgB,EADY,CACT,AACrB,GAFkC,CAAC,AAIrC,IAAM,EAAO,IAAI,IAAI,EAAC,CAAA,AAAK,CAAC,CAExB,GAAa,EACjB,IAAK,IAAI,EAAI,EAAa,EAAI,EAAM,IAClC,AADuC,GACnC,IAAgB,EAAG,CACrB,IAAI,EAAU,CAAI,CAAC,EAAE,CACjB,CAAI,CAAC,EAAE,GAAK,IACd,CADmB,CACT,CAAI,CAAC,EAAI,EAAA,AAAE,EAEnB,GACF,EADO,EACH,CAAC,MAAM,CAAC,GAEd,EAAY,IAAI,CAAC,GAAG,CAAC,GACrB,IAAI,CAAC,GAAG,CAAC,EAAK,EAChB,KAAO,CACD,AAAC,GAAc,CAAI,CAAC,EAAI,EAAE,GAAK,IACjC,CADsC,EACzB,CAAA,EAEf,IAAM,EAAa,CAAI,CAAC,EAAa,EAAI,EAAI,EAAE,CACzC,EAAe,EACrB,EAAY,IAAI,CAAC,GAAG,CAAC,GACrB,IAAI,CAAC,MAAM,CAAC,GACZ,IAAI,CAAC,GAAG,CAAC,EAAY,EACvB,CAEF,OAAO,IAAI,AACb,CACA,KAAK,CAAK,CAAE,CAAG,CAAE,CAAK,CAAE,CACtB,IAAM,EAAO,IAAI,EAAa,IAAI,EAElC,OADA,EAAK,MAAM,CAAC,EAAO,EAAK,GACjB,CACT,CACA,OAAO,CAAG,CAAE,CACV,IAAM,EAAQ,IAAI,EAAC,CAAA,AAAK,CAAC,OAAO,CAAC,GAAO,EACxC,IAAI,IAAQ,EAGZ,CAHe,MAGR,IAAI,CAAC,OAAO,CAAC,EACtB,CAIA,UAAU,CAAG,CAAE,CAAM,CAAE,CAAK,CAAE,CAC5B,IAAM,EAAQ,IAAI,EAAC,CAAA,AAAK,CAAC,OAAO,CAAC,UACjC,AAAI,AAAU,CAAC,GAAG,GACT,IAAI,CAEN,IAAI,CAAC,MAAM,CAAC,EAAO,EAAQ,EACpC,CACA,MAAM,CAAG,CAAE,CACT,IAAI,EAAQ,IAAI,EAAC,CAAA,AAAK,CAAC,OAAO,CAAC,GAE/B,GAAI,AAAU,CAAC,GAAG,EADlB,EAAkB,CAAC,IAAX,GAAgB,IAAU,IAAI,CAAC,IAAI,CAAG,EAAI,CAAC,EAAI,GAAQ,EAI/D,OAAO,IAAI,CAAC,OAAO,CAAC,EACtB,CAIA,SAAS,CAAG,CAAE,CAAM,CAAE,CAAK,CAAE,CAC3B,IAAM,EAAQ,IAAI,CAAC,CAAA,CAAK,CAAC,OAAO,CAAC,UACjC,AAAc,CAAC,GAAG,CAAd,EACK,IAAI,CAEN,IAAI,CAAC,MAAM,CAAC,EAAQ,EAAG,EAAQ,EACxC,CACA,OAAQ,CACN,OAAO,IAAI,CAAC,OAAO,CAAC,EACtB,CACA,MAAO,CACL,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,EACvB,CACA,OAAQ,CAEN,OADA,IAAI,EAAC,CAAA,AAAK,CAAG,EAAE,CACR,KAAK,CAAC,OACf,CACA,OAAO,CAAG,CAAE,CACV,IAAM,EAAU,KAAK,CAAC,OAAO,GAI7B,OAHI,GACF,IAAI,EAAC,AADM,CACN,AAAK,CAAC,MAAM,CAAC,IAAI,EAAC,CAAA,AAAK,CAAC,OAAO,CAAC,GAAM,GAEtC,CACT,CACA,SAAS,CAAK,CAAE,CACd,IAAM,EAAM,IAAI,CAAC,KAAK,CAAC,UACvB,AAAY,KAAK,GAAG,CAAhB,GACK,IAAI,CAAC,MAAM,CAAC,EAGvB,CACA,GAAG,CAAK,CAAE,CACR,IAAM,EAAM,EAAG,IAAI,CAAC,CAAA,CAAK,CAAE,GAC3B,GAAY,KAAK,GAAG,CAAhB,EACF,OAAO,IAAI,CAAC,GAAG,CAAC,EAEpB,CACA,QAAQ,CAAK,CAAE,CACb,IAAM,EAAM,EAAG,IAAI,EAAC,CAAA,AAAK,CAAE,GAC3B,GAAY,KAAK,GAAG,CAAhB,EACF,MAAO,CAAC,EAAK,IAAI,CAAC,GAAG,CAAC,GAAK,AAE/B,CACA,QAAQ,CAAG,CAAE,CACX,OAAO,IAAI,EAAC,CAAA,AAAK,CAAC,OAAO,CAAC,EAC5B,CACA,MAAM,CAAK,CAAE,CACX,OAAO,EAAG,IAAI,EAAC,CAAK,AAAL,CAAO,EACxB,CACA,KAAK,CAAG,CAAE,CAAM,CAAE,CAChB,IAAM,EAAQ,IAAI,CAAC,OAAO,CAAC,GAC3B,GAAc,CAAC,GAAG,CAAd,EACF,OAAO,AAET,IAAI,CAFU,CAEH,EAAQ,EAGnB,OAFI,EAAO,IAAG,GAAO,EACjB,GAAQ,IAAI,CAAC,IAAI,GAAE,EAAO,IAAI,CAAC,IAAI,EAAG,EACnC,IAAI,CAAC,EAAE,CAAC,EACjB,CACA,QAAQ,CAAG,CAAE,CAAM,CAAE,CACnB,IAAM,EAAQ,IAAI,CAAC,OAAO,CAAC,GAC3B,GAAI,AAAU,CAAC,GAAG,GAChB,OAAO,AAET,IAAI,CAFU,CAEH,EAAQ,EAGnB,OAFI,EAAO,IAAG,GAAO,EACjB,GAAQ,IAAI,CAAC,IAAI,GAAE,EAAO,IAAI,CAAC,IAAI,EAAG,EACnC,IAAI,CAAC,KAAK,CAAC,EACpB,CACA,KAAK,CAAS,CAAE,CAAO,CAAE,CACvB,IAAI,EAAQ,EACZ,IAAK,IAAM,KAAS,IAAI,CAAE,CACxB,GAAI,QAAQ,KAAK,CAAC,EAAW,EAAS,CAAC,EAAO,EAAO,IAAI,CAAC,EACxD,CAD2D,MACpD,CAET,IACF,CAEF,CACA,UAAU,CAAS,CAAE,CAAO,CAAE,CAC5B,IAAI,EAAQ,EACZ,IAAK,IAAM,KAAS,IAAI,CAAE,CACxB,GAAI,QAAQ,KAAK,CAAC,EAAW,EAAS,CAAC,EAAO,EAAO,IAAI,CAAC,EACxD,CAD2D,MACpD,EAET,GACF,CACA,OAAO,CAAC,CACV,CACA,OAAO,CAAS,CAAE,CAAO,CAAE,CACzB,IAAM,EAAU,EAAE,CACd,EAAQ,EACZ,IAAK,IAAM,KAAS,IAAI,CAAE,AACpB,QAAQ,KAAK,CAAC,EAAW,EAAS,CAAC,EAAO,EAAO,IAAI,CAAC,GAAG,AAC3D,EAAQ,IAAI,CAAC,GAEf,IAEF,OAAO,IAAI,EAAa,EAC1B,CACA,IAAI,CAAU,CAAE,CAAO,CAAE,CACvB,IAAM,EAAU,EAAE,CACd,EAAQ,EACZ,IAAK,IAAM,KAAS,IAAI,CAAE,AACxB,EAAQ,IAAI,CAAC,CAAC,CAAK,CAAC,EAAE,CAAE,QAAQ,KAAK,CAAC,EAAY,EAAS,CAAC,EAAO,EAAO,IAAI,CAAC,EAAE,EACjF,IAEF,OAAO,IAAI,EAAa,EAC1B,CACA,OAAO,GAAG,CAAI,CAAE,CACd,GAAM,CAAC,EAAY,EAAa,CAAG,EAC/B,EAAQ,EACR,EAAc,GAAgB,IAAI,CAAC,EAAE,CAAC,GAC1C,IAAK,IAAM,KAAS,IAAI,CAAE,AAEtB,EADY,IAAV,GAA+B,GAAG,CAAnB,EAAK,MAAM,CACd,EAEA,QAAQ,KAAK,CAAC,EAAY,IAAI,CAAE,CAAC,EAAa,EAAO,EAAO,IAAI,CAAC,EAEjF,IAEF,OAAO,CACT,CACA,YAAY,GAAG,CAAI,CAAE,CACnB,GAAM,CAAC,EAAY,EAAa,CAAG,EAC/B,EAAc,GAAgB,IAAI,CAAC,EAAE,CAAC,CAAC,GAC3C,IAAK,IAAI,EAAQ,IAAI,CAAC,IAAI,CAAG,EAAG,GAAS,EAAG,IAAS,CACnD,IAAM,EAAQ,IAAI,CAAC,EAAE,CAAC,GAEpB,EADE,IAAU,IAAI,CAAC,IAAI,CAAG,GAAqB,GAAG,CAAnB,EAAK,MAAM,CAC1B,EAEA,QAAQ,KAAK,CAAC,EAAY,IAAI,CAAE,CAAC,EAAa,EAAO,EAAO,IAAI,CAAC,CAEnF,CACA,OAAO,CACT,CACA,SAAS,CAAS,CAAE,CAElB,OAAO,IAAI,EADK,IAAI,IAAI,CAAC,EACD,KADQ,GAAG,CAAC,IAAI,CAAC,GAE3C,CACA,YAAa,CACX,IAAM,EAAW,IAAI,EACrB,IAAK,IAAI,EAAQ,IAAI,CAAC,IAAI,CAAG,EAAG,GAAS,EAAG,IAAS,CACnD,IAAM,EAAM,IAAI,CAAC,KAAK,CAAC,GACjB,EAAU,IAAI,CAAC,GAAG,CAAC,GACzB,EAAS,GAAG,CAAC,EAAK,EACpB,CACA,OAAO,CACT,CACA,UAAU,GAAG,CAAI,CAAE,CACjB,IAAM,EAAU,IAAI,IAAI,CAAC,OAAO,GAAG,CAEnC,OADA,EAAQ,MAAM,IAAI,GACX,IAAI,EAAa,EAC1B,CACA,MAAM,CAAK,CAAE,CAAG,CAAE,CAChB,IAAM,EAAS,IAAI,EACf,EAAO,IAAI,CAAC,IAAI,CAAG,EACvB,GAAc,KAAK,GAAG,CAAlB,EACF,OAAO,EAEL,EAAQ,GAAG,CACb,GAAgB,IAAI,CAAZ,AAAa,IAAA,AAAI,EAEf,KAAK,IAAb,GAAkB,EAAM,GAAG,CAC7B,EAAO,GAAM,EAEf,IAAK,IAAI,EAAQ,EAAO,GAAS,EAAM,IAAS,CAC9C,IAAM,EAAM,IAAI,CAAC,KAAK,CAAC,GACjB,EAAU,IAAI,CAAC,GAAG,CAAC,GACzB,EAAO,GAAG,CAAC,EAAK,EAClB,CACA,OAAO,CACT,CACA,MAAM,CAAS,CAAE,CAAO,CAAE,CACxB,IAAI,EAAQ,EACZ,IAAK,IAAM,KAAS,IAAI,CAAE,CACxB,GAAI,CAAC,QAAQ,KAAK,CAAC,EAAW,EAAS,CAAC,EAAO,EAAO,IAAI,CAAC,EACzD,CAD4D,MACrD,EAET,GACF,CACA,OAAO,CACT,CACA,KAAK,CAAS,CAAE,CAAO,CAAE,CACvB,IAAI,EAAQ,EACZ,IAAK,IAAM,KAAS,IAAI,CAAE,CACxB,GAAI,QAAQ,KAAK,CAAC,EAAW,EAAS,CAAC,EAAO,EAAO,IAAI,CAAC,EACxD,CAD2D,MACpD,EAET,GACF,CACA,OAAO,CACT,CACF,qCCzWA,IAAI,EAAmB,EAAA,aAAmB,CAAC,KAAK,GAKhD,SAAS,EAAa,CAAQ,EAC5B,IAAM,EAAY,EAAA,UAAgB,CAAC,GACnC,OAAO,GAAY,GAAa,KAClC,gCCKA,CAAA,GAAM,EAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,OAAA,EAAiB,CAAA,CAAA,MAbK,CAAC,AAaG,CAbF,AAaE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAbM,AAaN,CAbM,AAaN,AAbQ,CAaE,CAAA,AAbC,ICmBI,cDnBe,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAA,oDEmBtF,CAAA,CAAA,CAAA,CAAM,AAAN,CAAA,CAAM,CAAA,EAAS,CAAT,AAAS,CAAT,AAAS,CAAT,AAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAnBI,CAClC,AAkByC,CAAA,AAlBxC,CAkBwC,AAlBxC,CAkBwC,AAlBxC,CAAA,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAAQ,AAkBgC,CAlBhC,AAAE,AAkB8B,CAAU,CAAA,AAlBrC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAY,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACzC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAE,AAAF,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAY,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACzC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4C,AAA5C,CAA4C,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACzE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAW,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACxC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAA0C,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACzE,gECRA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OAEA,IAAM,EAAgB,CAAA,EAAA,EAAA,GAAA,AAAG,EACvB,iZACA,CACE,SAAU,CACR,QAAS,CACP,QACE,iFACF,UACE,uFACF,YACE,4KACF,QACE,wEACJ,CACF,EACA,gBAAiB,CACf,QAAS,SACX,CACF,GAGF,SAAS,EAAM,WACb,CAAS,SACT,CAAO,CACP,WAAU,CAAK,CACf,GAAG,EAEuD,EAC1D,IAAM,EAAO,EAAU,EAAA,IAAI,CAAG,OAE9B,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,YAAU,QACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,EAAc,SAAE,CAAQ,GAAI,GACzC,GAAG,CAAK,EAGf,oDCxBA,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAAM,CAAA,CAAA,CAAA,AAAO,CAAP,AAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAhBM,CAClC,AAeoC,CAfnC,AAemC,CAfnC,AAemC,CAfnC,AAemC,CAfnC,AAemC,CAfnC,AAemC,CAfnC,AAemC,CAfnC,AAemC,CAfnC,AAAQ,AAe2B,CAf3B,AAAE,AAeyB,CAAU,CAAA,AAfhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAY,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACzC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAY,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAC3C,2JCFA,EAAA,EAAA,CAAA,CAAA,OAEA,SAAS,EAAM,WAAE,CAAS,CAAE,GAAG,EAAsC,EACnE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,YAAU,kBACV,UAAU,2CAEV,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,YAAU,QACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,gCAAiC,GAC9C,GAAG,CAAK,IAIjB,CAEA,SAAS,EAAY,WAAE,CAAS,CAAE,GAAG,EAAsC,EACzE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,YAAU,eACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,kBAAmB,GAChC,GAAG,CAAK,EAGf,CAEA,SAAS,EAAU,CAAE,WAAS,CAAE,GAAG,EAAsC,EACvE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,YAAU,aACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,6BAA8B,GAC3C,GAAG,CAAK,EAGf,CAeA,SAAS,EAAS,WAAE,CAAS,CAAE,GAAG,EAAmC,EACnE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CACC,YAAU,YACV,UAAW,CAAA,EAAA,EAAA,EAAE,AAAF,EACT,8EACA,GAED,GAAG,CAAK,EAGf,CAEA,SAAS,EAAU,WAAE,CAAS,CAAE,GAAG,EAAmC,EACpE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CACC,YAAU,aACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,qJACA,GAED,GAAG,CAAK,EAGf,CAEA,SAAS,EAAU,WAAE,CAAS,CAAE,GAAG,EAAmC,EACpE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CACC,YAAU,aACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,yGACA,GAED,GAAG,CAAK,EAGf,kECzFA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAA,OAWA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAOA,EAAA,EAAA,CAAA,CAAA,OAQA,EAAA,EAAA,CAAA,CAAA,OAMA,EAAA,EAAA,CAAA,CAAA,OAOA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAGe,SAAS,IACvB,GAAM,CAAE,KAAM,CAAO,CAAE,CAAG,CAAA,EAAA,EAAA,UAAA,AAAU,IAC9B,EAAM,CAAA,EAAA,EAAA,MAAM,AAAN,IAEN,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAY,EAAE,EAChD,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAsB,MAClD,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IACvC,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAS,OAG/C,EACL,GAAS,OACuB,CAAhC,qBAAE,EAAQ,IAAI,CAAS,IAAI,EACK,eAA9B,EAAQ,IAAI,CAAS,IAAI,EACK,YAA9B,EAAQ,IAAI,CAAS,IAAS,AAAL,CAAc,CACpC,EACL,GAAS,MAAuC,sBAA9B,EAAQ,IAAI,CAAS,IAAI,CAEtC,EAAW,UAChB,GAAI,CACH,GAAW,GACX,IAAM,EAAe,MAAM,EAAI,WAAW,GAC1C,EAAY,GAGZ,IAAM,EAAM,IAAI,KACV,EAAS,EAAa,MAAM,CACjC,AAAC,GAAM,IAAI,KAAK,EAAE,SAAS,GAAK,GAAO,IAAI,KAAK,EAAE,OAAO,GAAK,GAC7D,MAAM,CACF,EAAY,EAAa,MAAM,CACnC,AAAD,GAAO,IAAI,KAAK,EAAE,OAAO,EAAI,GAC5B,MAAM,CACF,EAAgB,EAAa,MAAM,CACxC,CAAC,EAAK,IAAM,EAAM,EAAE,QAAQ,CAC5B,GAGD,EAAS,CACR,MAAO,EAAa,MAAM,QAC1B,YACA,gBACA,CACD,EACD,CAAE,MAAO,EAAO,CACf,QAAQ,KAAK,CAAC,0CAA2C,EAC1D,QAAU,CACT,GAAW,EACZ,CACD,EAEA,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACL,GAAS,aAAa,AACzB,GAEF,EAAG,CAAC,EAAQ,EAEZ,IAAM,EAAe,MAAO,IAC3B,GAAK,CAAD,OAAS,sDAIb,CAJoE,EAIhE,CACH,MAAM,EAAI,aAAa,CAAC,GACxB,GACD,CAAE,MAAO,EAAO,CACf,QAAQ,KAAK,CAAC,iCAAkC,EACjD,CACD,EAGM,EAAmB,EAAS,MAAM,CAAC,AAAC,IACzC,IAAM,EACL,EAAQ,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAClC,IAAI,KAAK,EAAQ,SAAS,EACxB,kBAAkB,GAClB,QAAQ,CAAC,IACX,IAAI,KAAK,EAAQ,OAAO,EAAE,kBAAkB,GAAG,QAAQ,CAAC,GAEnD,EACU,QAAf,GAAwB,EAAQ,KAAK,CAAC,QAAQ,KAAO,EAEtD,OAAO,GAAiB,CACzB,GAGM,EAAc,MAAM,IAAI,CAC7B,IAAI,IAAI,EAAS,GAAG,CAAC,AAAC,GAAM,EAAE,KAAK,CAAC,QAAQ,MAC3C,IAAI,UAYN,AAAI,EAEF,CAAA,EAAA,EAAA,EAFW,CAEX,EAAC,MAAA,CAAI,UAAU,iDACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yEACf,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,oCAS7C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBAEd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6CAAoC,aAClD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,uDAIrC,GACA,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,OAAO,CAAA,CAAA,WACd,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,oCACV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,iBAAiB,2BAQpC,GACA,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qDACd,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACrB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,mBAG3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,qCAErB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BAAsB,EAAM,KAAK,QAGlD,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACrB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,qBAG3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,qCAElB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BAAsB,EAAM,MAAM,QAGnD,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACrB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,uBAG3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,qCAErB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BAAsB,EAAM,SAAS,QAGtD,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACrB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,qBAG3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,qCAEvB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+BACb,EAAM,aAAa,CAAC,cAAc,GAAG,mBAQ3C,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,WACV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,YACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,0CAIlB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uBACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kBACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qBACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,0DAClB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACL,YAAY,kCACZ,MAAO,EACP,SAAU,AAAC,GAAM,EAAc,EAAE,MAAM,CAAC,KAAK,EAC7C,UAAU,cAIb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,MAAO,EAAY,cAAe,YACzC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,CAAC,UAAU,qBACxB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,YAAY,wBAE1B,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,aAAa,CAAA,WACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,MAAM,eAAM,sBACvB,EAAY,GAAG,CAAC,AAAC,GACjB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAY,MAAO,WAC5B,GADe,kBAWvB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,UACV,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,WAAC,aAAW,EAAiB,MAAM,CAAC,SAE/C,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,WAAW,CAAA,WACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,KAAK,CAAA,WACL,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,YACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,cACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,WACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,aACV,GAAqB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,iBAGnC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UACR,EAAiB,GAAG,CAAC,AAAC,IACtB,IAAM,EAAS,CAxJG,AAAC,IACzB,IAAM,EAAM,IAAI,KACV,EAAQ,IAAI,KAAK,EAAQ,SAAS,EAClC,EAAM,IAAI,KAAK,EAAQ,OAAO,SAEpC,AAAI,EAAM,EAAc,CAAE,IAAT,EAAgB,UAAW,QAAS,WAAqB,EACtE,EAAM,EAAY,CAAE,EAAT,IAAgB,WAAY,QAAS,SAAmB,EAChE,CAAE,MAAO,SAAU,QAAS,SAAmB,EACvD,EAgJuC,GAChC,MACC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,uBACnB,EAAQ,KAAK,GAEf,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,WACR,IAAI,KAAK,EAAQ,SAAS,EAAE,kBAAkB,GAAG,KAAG,IACpD,IAAI,KAAK,EAAQ,OAAO,EAAE,kBAAkB,MAE9C,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,WACR,EAAQ,QAAQ,CAAC,cAAc,GAAG,WAEpC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UACT,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,QAAS,EAAO,OAAO,UAAG,EAAO,KAAK,KAE9C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UACR,IAAI,KAAK,EAAQ,SAAS,EAAE,kBAAkB,KAE/C,GACA,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UACT,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,YAAY,CAAA,WACZ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,mBAAmB,CAAA,CAAC,OAAO,CAAA,CAAA,WAC3B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,QAAQ,UAAU,uBACjC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,cAAc,CAAA,CAAC,UAAU,gBAG5B,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,mBAAmB,CAAA,CAAC,MAAM,gBAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,gBAAgB,CAAA,CAAC,OAAO,CAAA,CAAA,WACxB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,OAAI,CAAA,CACJ,KAAM,CAAC,oBAAoB,EAAE,EAAQ,GAAG,CAAC,KAAK,CAAC,WAE/C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,iBAAiB,gBAIlC,GACA,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,gBAAgB,CAAA,CAChB,QAAS,IAAM,EAAa,EAAQ,GAAG,EACvC,UAAU,yBAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,iBAAiB,yBAvC3B,EAAQ,GAAG,CAiD5B,QAG2B,IAA5B,EAAiB,MAAM,EACvB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACd,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,qCAO5C", "ignoreList": [1, 2, 3, 4, 5, 6, 8]}