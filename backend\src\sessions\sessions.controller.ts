import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../common/guards/role.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../common/enums/user-role.enum';
import { SessionsService } from './sessions.service';
import { CreateSessionDto } from './dto/create-session.dto';
import { UpdateSessionDto } from './dto/update-session.dto';
import { AddMemberToSessionDto } from './dto/add-member.dto';
import { PaymentFiltersDto } from '../common/dto/payment-filters.dto';

@ApiTags('sessions')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('sessions')
export class SessionsController {
  constructor(private readonly sessionsService: SessionsService) {}

  @Post()
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Créer une session', description: 'Crée une session avec génération automatique des réunions (dimanches). Calcule et définit la prochaine réunion (nextReunionId + dateProchaineReunion).' })
  create(@Body() dto: CreateSessionDto, @Req() req: any) {
    return this.sessionsService.create(dto, req.user._id);
  }

  @Get()
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER, UserRole.CASHIER)
  @ApiOperation({ summary: 'Lister les sessions' })
  findAll() {
    return this.sessionsService.findAll();
  }

  @Get(':id')
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER, UserRole.CASHIER)
  @ApiOperation({ summary: 'Détail d\'une session' })
  findOne(@Param('id') id: string) {
    return this.sessionsService.findOne(id);
  }

  @Patch(':id')
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Mettre à jour une session' })
  update(@Param('id') id: string, @Body() dto: UpdateSessionDto) {
    return this.sessionsService.update(id, dto);
  }

  @Delete(':id')
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Supprimer une session', description: 'Supprime la session et toutes ses réunions.' })
  remove(@Param('id') id: string) {
    return this.sessionsService.remove(id);
  }

  // add member to session and compute dues
  @Post(':id/members')
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Ajouter un membre à la session', description: 'Crée une affiliation avec parts, calcule totalDue (= partFixe * parts * nbReunions) et initialise les soldes de suivi.' })
  addMember(@Param('id') sessionId: string, @Body() dto: AddMemberToSessionDto) {
    return this.sessionsService.addMember(sessionId, dto);
  }

  // Debrief paiements d'une session
  @Get(':id/payments-debrief')
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER)
  @ApiOperation({ summary: 'Debrief paiements d\'une session', description: 'Totaux IN/OUT, net et agrégations pour la session. Filtrable par membre, réunion, période, direction, fonction.' })
  paymentsDebrief(@Param('id') sessionId: string, @Query() filters: PaymentFiltersDto) {
    return this.sessionsService.paymentsDebrief(sessionId, filters);
  }
}