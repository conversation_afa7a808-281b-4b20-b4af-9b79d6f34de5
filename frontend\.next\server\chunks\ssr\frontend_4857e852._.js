module.exports=[15763,(a,b,c)=>{"use strict";function d(a,b,c){if(a)for(let f of(c&&(c=c.toLowerCase()),a)){var d,e;if(b===(null==(d=f.domain)?void 0:d.split(":",1)[0].toLowerCase())||c===f.defaultLocale.toLowerCase()||(null==(e=f.locales)?void 0:e.some(a=>a.toLowerCase()===c)))return f}}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"detectDomainLocale",{enumerable:!0,get:function(){return d}})},46162,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"addPathSuffix",{enumerable:!0,get:function(){return e}});let d=a.r(23824);function e(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:e,hash:f}=(0,d.parsePath)(a);return""+c+b+e+f}},26180,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"addLocale",{enumerable:!0,get:function(){return f}});let d=a.r(5514),e=a.r(28139);function f(a,b,c,f){if(!b||b===c)return a;let g=a.toLowerCase();return!f&&((0,e.pathHasPrefix)(g,"/api")||(0,e.pathHasPrefix)(g,"/"+b.toLowerCase()))?a:(0,d.addPathPrefix)(a,"/"+b)}},62280,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"formatNextPathnameInfo",{enumerable:!0,get:function(){return h}});let d=a.r(19344),e=a.r(5514),f=a.r(46162),g=a.r(26180);function h(a){let b=(0,g.addLocale)(a.pathname,a.locale,a.buildId?void 0:a.defaultLocale,a.ignorePrefix);return(a.buildId||!a.trailingSlash)&&(b=(0,d.removeTrailingSlash)(b)),a.buildId&&(b=(0,f.addPathSuffix)((0,e.addPathPrefix)(b,"/_next/data/"+a.buildId),"/"===a.pathname?"index.json":".json")),b=(0,e.addPathPrefix)(b,a.basePath),!a.buildId&&a.trailingSlash?b.endsWith("/")?b:(0,f.addPathSuffix)(b,"/"):(0,d.removeTrailingSlash)(b)}},85750,(a,b,c)=>{"use strict";function d(a,b){let c;if((null==b?void 0:b.host)&&!Array.isArray(b.host))c=b.host.toString().split(":",1)[0];else{if(!a.hostname)return;c=a.hostname}return c.toLowerCase()}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"getHostname",{enumerable:!0,get:function(){return d}})},55036,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"normalizeLocalePath",{enumerable:!0,get:function(){return e}});let d=new WeakMap;function e(a,b){let c;if(!b)return{pathname:a};let e=d.get(b);e||(e=b.map(a=>a.toLowerCase()),d.set(b,e));let f=a.split("/",2);if(!f[1])return{pathname:a};let g=f[1].toLowerCase(),h=e.indexOf(g);return h<0?{pathname:a}:(c=b[h],{pathname:a=a.slice(c.length+1)||"/",detectedLocale:c})}},62909,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"removePathPrefix",{enumerable:!0,get:function(){return e}});let d=a.r(28139);function e(a,b){if(!(0,d.pathHasPrefix)(a,b))return a;let c=a.slice(b.length);return c.startsWith("/")?c:"/"+c}},84450,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"getNextPathnameInfo",{enumerable:!0,get:function(){return g}});let d=a.r(55036),e=a.r(62909),f=a.r(28139);function g(a,b){var c,g;let{basePath:h,i18n:i,trailingSlash:j}=null!=(c=b.nextConfig)?c:{},k={pathname:a,trailingSlash:"/"!==a?a.endsWith("/"):j};h&&(0,f.pathHasPrefix)(k.pathname,h)&&(k.pathname=(0,e.removePathPrefix)(k.pathname,h),k.basePath=h);let l=k.pathname;if(k.pathname.startsWith("/_next/data/")&&k.pathname.endsWith(".json")){let a=k.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");k.buildId=a[0],l="index"!==a[1]?"/"+a.slice(1).join("/"):"/",!0===b.parseData&&(k.pathname=l)}if(i){let a=b.i18nProvider?b.i18nProvider.analyze(k.pathname):(0,d.normalizeLocalePath)(k.pathname,i.locales);k.locale=a.detectedLocale,k.pathname=null!=(g=a.pathname)?g:k.pathname,!a.detectedLocale&&k.buildId&&(a=b.i18nProvider?b.i18nProvider.analyze(l):(0,d.normalizeLocalePath)(l,i.locales)).detectedLocale&&(k.locale=a.detectedLocale)}return k}},97397,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"NextURL",{enumerable:!0,get:function(){return k}});let d=a.r(15763),e=a.r(62280),f=a.r(85750),g=a.r(84450),h=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function i(a,b){return new URL(String(a).replace(h,"localhost"),b&&String(b).replace(h,"localhost"))}let j=Symbol("NextURLInternal");class k{constructor(a,b,c){let d,e;"object"==typeof b&&"pathname"in b||"string"==typeof b?(d=b,e=c||{}):e=c||b||{},this[j]={url:i(a,d??e.base),options:e,basePath:""},this.analyze()}analyze(){var a,b,c,e,h;let i=(0,g.getNextPathnameInfo)(this[j].url.pathname,{nextConfig:this[j].options.nextConfig,parseData:!0,i18nProvider:this[j].options.i18nProvider}),k=(0,f.getHostname)(this[j].url,this[j].options.headers);this[j].domainLocale=this[j].options.i18nProvider?this[j].options.i18nProvider.detectDomainLocale(k):(0,d.detectDomainLocale)(null==(b=this[j].options.nextConfig)||null==(a=b.i18n)?void 0:a.domains,k);let l=(null==(c=this[j].domainLocale)?void 0:c.defaultLocale)||(null==(h=this[j].options.nextConfig)||null==(e=h.i18n)?void 0:e.defaultLocale);this[j].url.pathname=i.pathname,this[j].defaultLocale=l,this[j].basePath=i.basePath??"",this[j].buildId=i.buildId,this[j].locale=i.locale??l,this[j].trailingSlash=i.trailingSlash}formatPathname(){return(0,e.formatNextPathnameInfo)({basePath:this[j].basePath,buildId:this[j].buildId,defaultLocale:this[j].options.forceLocale?void 0:this[j].defaultLocale,locale:this[j].locale,pathname:this[j].url.pathname,trailingSlash:this[j].trailingSlash})}formatSearch(){return this[j].url.search}get buildId(){return this[j].buildId}set buildId(a){this[j].buildId=a}get locale(){return this[j].locale??""}set locale(a){var b,c;if(!this[j].locale||!(null==(c=this[j].options.nextConfig)||null==(b=c.i18n)?void 0:b.locales.includes(a)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${a}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[j].locale=a}get defaultLocale(){return this[j].defaultLocale}get domainLocale(){return this[j].domainLocale}get searchParams(){return this[j].url.searchParams}get host(){return this[j].url.host}set host(a){this[j].url.host=a}get hostname(){return this[j].url.hostname}set hostname(a){this[j].url.hostname=a}get port(){return this[j].url.port}set port(a){this[j].url.port=a}get protocol(){return this[j].url.protocol}set protocol(a){this[j].url.protocol=a}get href(){let a=this.formatPathname(),b=this.formatSearch();return`${this.protocol}//${this.host}${a}${b}${this.hash}`}set href(a){this[j].url=i(a),this.analyze()}get origin(){return this[j].url.origin}get pathname(){return this[j].url.pathname}set pathname(a){this[j].url.pathname=a}get hash(){return this[j].url.hash}set hash(a){this[j].url.hash=a}get search(){return this[j].url.search}set search(a){this[j].url.search=a}get password(){return this[j].url.password}set password(a){this[j].url.password=a}get username(){return this[j].url.username}set username(a){this[j].url.username=a}get basePath(){return this[j].basePath}set basePath(a){this[j].basePath=a.startsWith("/")?a:`/${a}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new k(String(this),this[j].options)}}},90560,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ACTION_SUFFIX:function(){return p},APP_DIR_ALIAS:function(){return J},CACHE_ONE_YEAR:function(){return B},DOT_NEXT_ALIAS:function(){return H},ESLINT_DEFAULT_DIRS:function(){return ab},GSP_NO_RETURNED_VALUE:function(){return X},GSSP_COMPONENT_MEMBER_ERROR:function(){return $},GSSP_NO_RETURNED_VALUE:function(){return Y},HTML_CONTENT_TYPE_HEADER:function(){return e},INFINITE_CACHE:function(){return C},INSTRUMENTATION_HOOK_FILENAME:function(){return F},JSON_CONTENT_TYPE_HEADER:function(){return f},MATCHED_PATH_HEADER:function(){return i},MIDDLEWARE_FILENAME:function(){return D},MIDDLEWARE_LOCATION_REGEXP:function(){return E},NEXT_BODY_SUFFIX:function(){return s},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return A},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return u},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return v},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return z},NEXT_CACHE_TAGS_HEADER:function(){return t},NEXT_CACHE_TAG_MAX_ITEMS:function(){return x},NEXT_CACHE_TAG_MAX_LENGTH:function(){return y},NEXT_DATA_SUFFIX:function(){return q},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return h},NEXT_META_SUFFIX:function(){return r},NEXT_QUERY_PARAM_PREFIX:function(){return g},NEXT_RESUME_HEADER:function(){return w},NON_STANDARD_NODE_ENV:function(){return _},PAGES_DIR_ALIAS:function(){return G},PRERENDER_REVALIDATE_HEADER:function(){return j},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return k},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return R},ROOT_DIR_ALIAS:function(){return I},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return Q},RSC_ACTION_ENCRYPTION_ALIAS:function(){return P},RSC_ACTION_PROXY_ALIAS:function(){return M},RSC_ACTION_VALIDATE_ALIAS:function(){return L},RSC_CACHE_WRAPPER_ALIAS:function(){return N},RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS:function(){return O},RSC_MOD_REF_PROXY_ALIAS:function(){return K},RSC_PREFETCH_SUFFIX:function(){return l},RSC_SEGMENTS_DIR_SUFFIX:function(){return m},RSC_SEGMENT_SUFFIX:function(){return n},RSC_SUFFIX:function(){return o},SERVER_PROPS_EXPORT_ERROR:function(){return W},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return T},SERVER_PROPS_SSG_CONFLICT:function(){return U},SERVER_RUNTIME:function(){return ac},SSG_FALLBACK_EXPORT_ERROR:function(){return aa},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return S},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return V},TEXT_PLAIN_CONTENT_TYPE_HEADER:function(){return d},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return Z},WEBPACK_LAYERS:function(){return ae},WEBPACK_RESOURCE_QUERIES:function(){return af}});let d="text/plain",e="text/html; charset=utf-8",f="application/json; charset=utf-8",g="nxtP",h="nxtI",i="x-matched-path",j="x-prerender-revalidate",k="x-prerender-revalidate-if-generated",l=".prefetch.rsc",m=".segments",n=".segment.rsc",o=".rsc",p=".action",q=".json",r=".meta",s=".body",t="x-next-cache-tags",u="x-next-revalidated-tags",v="x-next-revalidate-tag-token",w="next-resume",x=128,y=256,z=1024,A="_N_T_",B=31536e3,C=0xfffffffe,D="middleware",E=`(?:src/)?${D}`,F="instrumentation",G="private-next-pages",H="private-dot-next",I="private-next-root-dir",J="private-next-app-dir",K="private-next-rsc-mod-ref-proxy",L="private-next-rsc-action-validate",M="private-next-rsc-server-reference",N="private-next-rsc-cache-wrapper",O="private-next-rsc-track-dynamic-import",P="private-next-rsc-action-encryption",Q="private-next-rsc-action-client-wrapper",R="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",S="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",T="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",U="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",V="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",W="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",X="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",Y="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",Z="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",$="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",_='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',aa="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",ab=["app","pages","components","lib","src"],ac={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},ad={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},ae={...ad,GROUP:{builtinReact:[ad.reactServerComponents,ad.actionBrowser],serverOnly:[ad.reactServerComponents,ad.actionBrowser,ad.instrument,ad.middleware],neutralTarget:[ad.apiNode,ad.apiEdge],clientOnly:[ad.serverSideRendering,ad.appPagesBrowser],bundled:[ad.reactServerComponents,ad.actionBrowser,ad.serverSideRendering,ad.appPagesBrowser,ad.shared,ad.instrument,ad.middleware],appPages:[ad.reactServerComponents,ad.serverSideRendering,ad.appPagesBrowser,ad.actionBrowser]}},af={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},93513,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{fromNodeOutgoingHttpHeaders:function(){return e},normalizeNextQueryParam:function(){return i},splitCookiesString:function(){return f},toNodeOutgoingHttpHeaders:function(){return g},validateURL:function(){return h}});let d=a.r(90560);function e(a){let b=new Headers;for(let[c,d]of Object.entries(a))for(let a of Array.isArray(d)?d:[d])void 0!==a&&("number"==typeof a&&(a=a.toString()),b.append(c,a));return b}function f(a){var b,c,d,e,f,g=[],h=0;function i(){for(;h<a.length&&/\s/.test(a.charAt(h));)h+=1;return h<a.length}for(;h<a.length;){for(b=h,f=!1;i();)if(","===(c=a.charAt(h))){for(d=h,h+=1,i(),e=h;h<a.length&&"="!==(c=a.charAt(h))&&";"!==c&&","!==c;)h+=1;h<a.length&&"="===a.charAt(h)?(f=!0,h=e,g.push(a.substring(b,d)),b=h):h=d+1}else h+=1;(!f||h>=a.length)&&g.push(a.substring(b,a.length))}return g}function g(a){let b={},c=[];if(a)for(let[d,e]of a.entries())"set-cookie"===d.toLowerCase()?(c.push(...f(e)),b[d]=1===c.length?c[0]:c):b[d]=e;return b}function h(a){try{return String(new URL(String(a)))}catch(b){throw Object.defineProperty(Error(`URL is malformed "${String(a)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:b}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}function i(a){for(let b of[d.NEXT_QUERY_PARAM_PREFIX,d.NEXT_INTERCEPTION_MARKER_PREFIX])if(a!==b&&a.startsWith(b))return a.substring(b.length);return null}},81861,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{PageSignatureError:function(){return d},RemovedPageError:function(){return e},RemovedUAError:function(){return f}});class d extends Error{constructor({page:a}){super(`The middleware "${a}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class e extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class f extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},93677,(a,b,c)=>{"use strict";var d=Object.defineProperty,e=Object.getOwnPropertyDescriptor,f=Object.getOwnPropertyNames,g=Object.prototype.hasOwnProperty,h={};function i(a){var b;let c=["path"in a&&a.path&&`Path=${a.path}`,"expires"in a&&(a.expires||0===a.expires)&&`Expires=${("number"==typeof a.expires?new Date(a.expires):a.expires).toUTCString()}`,"maxAge"in a&&"number"==typeof a.maxAge&&`Max-Age=${a.maxAge}`,"domain"in a&&a.domain&&`Domain=${a.domain}`,"secure"in a&&a.secure&&"Secure","httpOnly"in a&&a.httpOnly&&"HttpOnly","sameSite"in a&&a.sameSite&&`SameSite=${a.sameSite}`,"partitioned"in a&&a.partitioned&&"Partitioned","priority"in a&&a.priority&&`Priority=${a.priority}`].filter(Boolean),d=`${a.name}=${encodeURIComponent(null!=(b=a.value)?b:"")}`;return 0===c.length?d:`${d}; ${c.join("; ")}`}function j(a){let b=new Map;for(let c of a.split(/; */)){if(!c)continue;let a=c.indexOf("=");if(-1===a){b.set(c,"true");continue}let[d,e]=[c.slice(0,a),c.slice(a+1)];try{b.set(d,decodeURIComponent(null!=e?e:"true"))}catch{}}return b}function k(a){if(!a)return;let[[b,c],...d]=j(a),{domain:e,expires:f,httponly:g,maxage:h,path:i,samesite:k,secure:n,partitioned:o,priority:p}=Object.fromEntries(d.map(([a,b])=>[a.toLowerCase().replace(/-/g,""),b]));{var q,r,s={name:b,value:decodeURIComponent(c),domain:e,...f&&{expires:new Date(f)},...g&&{httpOnly:!0},..."string"==typeof h&&{maxAge:Number(h)},path:i,...k&&{sameSite:l.includes(q=(q=k).toLowerCase())?q:void 0},...n&&{secure:!0},...p&&{priority:m.includes(r=(r=p).toLowerCase())?r:void 0},...o&&{partitioned:!0}};let a={};for(let b in s)s[b]&&(a[b]=s[b]);return a}}((a,b)=>{for(var c in b)d(a,c,{get:b[c],enumerable:!0})})(h,{RequestCookies:()=>n,ResponseCookies:()=>o,parseCookie:()=>j,parseSetCookie:()=>k,stringifyCookie:()=>i}),b.exports=((a,b,c,h)=>{if(b&&"object"==typeof b||"function"==typeof b)for(let i of f(b))g.call(a,i)||i===c||d(a,i,{get:()=>b[i],enumerable:!(h=e(b,i))||h.enumerable});return a})(d({},"__esModule",{value:!0}),h);var l=["strict","lax","none"],m=["low","medium","high"],n=class{constructor(a){this._parsed=new Map,this._headers=a;let b=a.get("cookie");if(b)for(let[a,c]of j(b))this._parsed.set(a,{name:a,value:c})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...a){let b="string"==typeof a[0]?a[0]:a[0].name;return this._parsed.get(b)}getAll(...a){var b;let c=Array.from(this._parsed);if(!a.length)return c.map(([a,b])=>b);let d="string"==typeof a[0]?a[0]:null==(b=a[0])?void 0:b.name;return c.filter(([a])=>a===d).map(([a,b])=>b)}has(a){return this._parsed.has(a)}set(...a){let[b,c]=1===a.length?[a[0].name,a[0].value]:a,d=this._parsed;return d.set(b,{name:b,value:c}),this._headers.set("cookie",Array.from(d).map(([a,b])=>i(b)).join("; ")),this}delete(a){let b=this._parsed,c=Array.isArray(a)?a.map(a=>b.delete(a)):b.delete(a);return this._headers.set("cookie",Array.from(b).map(([a,b])=>i(b)).join("; ")),c}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a=>`${a.name}=${encodeURIComponent(a.value)}`).join("; ")}},o=class{constructor(a){var b,c,d;this._parsed=new Map,this._headers=a;let e=null!=(d=null!=(c=null==(b=a.getSetCookie)?void 0:b.call(a))?c:a.get("set-cookie"))?d:[];for(let a of Array.isArray(e)?e:function(a){if(!a)return[];var b,c,d,e,f,g=[],h=0;function i(){for(;h<a.length&&/\s/.test(a.charAt(h));)h+=1;return h<a.length}for(;h<a.length;){for(b=h,f=!1;i();)if(","===(c=a.charAt(h))){for(d=h,h+=1,i(),e=h;h<a.length&&"="!==(c=a.charAt(h))&&";"!==c&&","!==c;)h+=1;h<a.length&&"="===a.charAt(h)?(f=!0,h=e,g.push(a.substring(b,d)),b=h):h=d+1}else h+=1;(!f||h>=a.length)&&g.push(a.substring(b,a.length))}return g}(e)){let b=k(a);b&&this._parsed.set(b.name,b)}}get(...a){let b="string"==typeof a[0]?a[0]:a[0].name;return this._parsed.get(b)}getAll(...a){var b;let c=Array.from(this._parsed.values());if(!a.length)return c;let d="string"==typeof a[0]?a[0]:null==(b=a[0])?void 0:b.name;return c.filter(a=>a.name===d)}has(a){return this._parsed.has(a)}set(...a){let[b,c,d]=1===a.length?[a[0].name,a[0].value,a[0]]:a,e=this._parsed;return e.set(b,function(a={name:"",value:""}){return"number"==typeof a.expires&&(a.expires=new Date(a.expires)),a.maxAge&&(a.expires=new Date(Date.now()+1e3*a.maxAge)),(null===a.path||void 0===a.path)&&(a.path="/"),a}({name:b,value:c,...d})),function(a,b){for(let[,c]of(b.delete("set-cookie"),a)){let a=i(c);b.append("set-cookie",a)}}(e,this._headers),this}delete(...a){let[b,c]="string"==typeof a[0]?[a[0]]:[a[0].name,a[0]];return this.set({...c,name:b,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(i).join("; ")}}},98394,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{RequestCookies:function(){return d.RequestCookies},ResponseCookies:function(){return d.ResponseCookies},stringifyCookie:function(){return d.stringifyCookie}});let d=a.r(93677)},23047,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{INTERNALS:function(){return h},NextRequest:function(){return i}});let d=a.r(97397),e=a.r(93513),f=a.r(81861),g=a.r(98394),h=Symbol("internal request");class i extends Request{constructor(a,b={}){let c="string"!=typeof a&&"url"in a?a.url:String(a);(0,e.validateURL)(c),b.body&&"half"!==b.duplex&&(b.duplex="half"),a instanceof Request?super(a,b):super(c,b);let f=new d.NextURL(c,{headers:(0,e.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:b.nextConfig});this[h]={cookies:new g.RequestCookies(this.headers),nextUrl:f,url:f.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[h].cookies}get nextUrl(){return this[h].nextUrl}get page(){throw new f.RemovedPageError}get ua(){throw new f.RemovedUAError}get url(){return this[h].url}}},60363,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"ReflectAdapter",{enumerable:!0,get:function(){return d}});class d{static get(a,b,c){let d=Reflect.get(a,b,c);return"function"==typeof d?d.bind(a):d}static set(a,b,c,d){return Reflect.set(a,b,c,d)}static has(a,b){return Reflect.has(a,b)}static deleteProperty(a,b){return Reflect.deleteProperty(a,b)}}},14493,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"NextResponse",{enumerable:!0,get:function(){return l}});let d=a.r(98394),e=a.r(97397),f=a.r(93513),g=a.r(60363),h=a.r(98394),i=Symbol("internal response"),j=new Set([301,302,303,307,308]);function k(a,b){var c;if(null==a||null==(c=a.request)?void 0:c.headers){if(!(a.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let c=[];for(let[d,e]of a.request.headers)b.set("x-middleware-request-"+d,e),c.push(d);b.set("x-middleware-override-headers",c.join(","))}}class l extends Response{constructor(a,b={}){super(a,b);let c=this.headers,j=new Proxy(new h.ResponseCookies(c),{get(a,e,f){switch(e){case"delete":case"set":return(...f)=>{let g=Reflect.apply(a[e],a,f),i=new Headers(c);return g instanceof h.ResponseCookies&&c.set("x-middleware-set-cookie",g.getAll().map(a=>(0,d.stringifyCookie)(a)).join(",")),k(b,i),g};default:return g.ReflectAdapter.get(a,e,f)}}});this[i]={cookies:j,url:b.url?new e.NextURL(b.url,{headers:(0,f.toNodeOutgoingHttpHeaders)(c),nextConfig:b.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[i].cookies}static json(a,b){let c=Response.json(a,b);return new l(c.body,c)}static redirect(a,b){let c="number"==typeof b?b:(null==b?void 0:b.status)??307;if(!j.has(c))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let d="object"==typeof b?b:{},e=new Headers(null==d?void 0:d.headers);return e.set("Location",(0,f.validateURL)(a)),new l(null,{...d,headers:e,status:c})}static rewrite(a,b){let c=new Headers(null==b?void 0:b.headers);return c.set("x-middleware-rewrite",(0,f.validateURL)(a)),k(b,c),new l(null,{...b,headers:c})}static next(a){let b=new Headers(null==a?void 0:a.headers);return b.set("x-middleware-next","1"),k(a,b),new l(null,{...a,headers:b})}}},98978,(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead'),"__NEXT_ERROR_CODE",{value:"E183",enumerable:!1,configurable:!0})}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"ImageResponse",{enumerable:!0,get:function(){return d}})},96391,(a,b,c)=>{(()=>{var c={226:function(b,c){!function(d,e){"use strict";var f="function",g="undefined",h="object",i="string",j="major",k="model",l="name",m="type",n="vendor",o="version",p="architecture",q="console",r="mobile",s="tablet",t="smarttv",u="wearable",v="embedded",w="Amazon",x="Apple",y="ASUS",z="BlackBerry",A="Browser",B="Chrome",C="Firefox",D="Google",E="Huawei",F="Microsoft",G="Motorola",H="Opera",I="Samsung",J="Sharp",K="Sony",L="Xiaomi",M="Zebra",N="Facebook",O="Chromium OS",P="Mac OS",Q=function(a,b){var c={};for(var d in a)b[d]&&b[d].length%2==0?c[d]=b[d].concat(a[d]):c[d]=a[d];return c},R=function(a){for(var b={},c=0;c<a.length;c++)b[a[c].toUpperCase()]=a[c];return b},S=function(a,b){return typeof a===i&&-1!==T(b).indexOf(T(a))},T=function(a){return a.toLowerCase()},U=function(a,b){if(typeof a===i)return a=a.replace(/^\s\s*/,""),typeof b===g?a:a.substring(0,350)},V=function(a,b){for(var c,d,g,i,j,k,l=0;l<b.length&&!j;){var m=b[l],n=b[l+1];for(c=d=0;c<m.length&&!j&&m[c];)if(j=m[c++].exec(a))for(g=0;g<n.length;g++)k=j[++d],typeof(i=n[g])===h&&i.length>0?2===i.length?typeof i[1]==f?this[i[0]]=i[1].call(this,k):this[i[0]]=i[1]:3===i.length?typeof i[1]!==f||i[1].exec&&i[1].test?this[i[0]]=k?k.replace(i[1],i[2]):void 0:this[i[0]]=k?i[1].call(this,k,i[2]):void 0:4===i.length&&(this[i[0]]=k?i[3].call(this,k.replace(i[1],i[2])):e):this[i]=k||e;l+=2}},W=function(a,b){for(var c in b)if(typeof b[c]===h&&b[c].length>0){for(var d=0;d<b[c].length;d++)if(S(b[c][d],a))return"?"===c?e:c}else if(S(b[c],a))return"?"===c?e:c;return a},X={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Y={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[o,[l,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[o,[l,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[l,o],[/opios[\/ ]+([\w\.]+)/i],[o,[l,H+" Mini"]],[/\bopr\/([\w\.]+)/i],[o,[l,H]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[l,o],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[o,[l,"UC"+A]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[o,[l,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[o,[l,"WeChat"]],[/konqueror\/([\w\.]+)/i],[o,[l,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[o,[l,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[o,[l,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[l,/(.+)/,"$1 Secure "+A],o],[/\bfocus\/([\w\.]+)/i],[o,[l,C+" Focus"]],[/\bopt\/([\w\.]+)/i],[o,[l,H+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[o,[l,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[o,[l,"Dolphin"]],[/coast\/([\w\.]+)/i],[o,[l,H+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[o,[l,"MIUI "+A]],[/fxios\/([-\w\.]+)/i],[o,[l,C]],[/\bqihu|(qi?ho?o?|360)browser/i],[[l,"360 "+A]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[l,/(.+)/,"$1 "+A],o],[/(comodo_dragon)\/([\w\.]+)/i],[[l,/_/g," "],o],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[l,o],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[l],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[l,N],o],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[l,o],[/\bgsa\/([\w\.]+) .*safari\//i],[o,[l,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[o,[l,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[o,[l,B+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[l,B+" WebView"],o],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[o,[l,"Android "+A]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[l,o],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[o,[l,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[o,l],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[l,[o,W,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[l,o],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[l,"Netscape"],o],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[o,[l,C+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[l,o],[/(cobalt)\/([\w\.]+)/i],[l,[o,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[p,"amd64"]],[/(ia32(?=;))/i],[[p,T]],[/((?:i[346]|x)86)[;\)]/i],[[p,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[p,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[p,"armhf"]],[/windows (ce|mobile); ppc;/i],[[p,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[p,/ower/,"",T]],[/(sun4\w)[;\)]/i],[[p,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[p,T]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[k,[n,I],[m,s]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[k,[n,I],[m,r]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[k,[n,x],[m,r]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[k,[n,x],[m,s]],[/(macintosh);/i],[k,[n,x]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[k,[n,J],[m,r]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[k,[n,E],[m,s]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[k,[n,E],[m,r]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[k,/_/g," "],[n,L],[m,r]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[k,/_/g," "],[n,L],[m,s]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[k,[n,"OPPO"],[m,r]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[k,[n,"Vivo"],[m,r]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[k,[n,"Realme"],[m,r]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[k,[n,G],[m,r]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[k,[n,G],[m,s]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[k,[n,"LG"],[m,s]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[k,[n,"LG"],[m,r]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[k,[n,"Lenovo"],[m,s]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[k,/_/g," "],[n,"Nokia"],[m,r]],[/(pixel c)\b/i],[k,[n,D],[m,s]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[k,[n,D],[m,r]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[k,[n,K],[m,r]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[k,"Xperia Tablet"],[n,K],[m,s]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[k,[n,"OnePlus"],[m,r]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[k,[n,w],[m,s]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[k,/(.+)/g,"Fire Phone $1"],[n,w],[m,r]],[/(playbook);[-\w\),; ]+(rim)/i],[k,n,[m,s]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[k,[n,z],[m,r]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[k,[n,y],[m,s]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[k,[n,y],[m,r]],[/(nexus 9)/i],[k,[n,"HTC"],[m,s]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[n,[k,/_/g," "],[m,r]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[k,[n,"Acer"],[m,s]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[k,[n,"Meizu"],[m,r]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[n,k,[m,r]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[n,k,[m,s]],[/(surface duo)/i],[k,[n,F],[m,s]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[k,[n,"Fairphone"],[m,r]],[/(u304aa)/i],[k,[n,"AT&T"],[m,r]],[/\bsie-(\w*)/i],[k,[n,"Siemens"],[m,r]],[/\b(rct\w+) b/i],[k,[n,"RCA"],[m,s]],[/\b(venue[\d ]{2,7}) b/i],[k,[n,"Dell"],[m,s]],[/\b(q(?:mv|ta)\w+) b/i],[k,[n,"Verizon"],[m,s]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[k,[n,"Barnes & Noble"],[m,s]],[/\b(tm\d{3}\w+) b/i],[k,[n,"NuVision"],[m,s]],[/\b(k88) b/i],[k,[n,"ZTE"],[m,s]],[/\b(nx\d{3}j) b/i],[k,[n,"ZTE"],[m,r]],[/\b(gen\d{3}) b.+49h/i],[k,[n,"Swiss"],[m,r]],[/\b(zur\d{3}) b/i],[k,[n,"Swiss"],[m,s]],[/\b((zeki)?tb.*\b) b/i],[k,[n,"Zeki"],[m,s]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[n,"Dragon Touch"],k,[m,s]],[/\b(ns-?\w{0,9}) b/i],[k,[n,"Insignia"],[m,s]],[/\b((nxa|next)-?\w{0,9}) b/i],[k,[n,"NextBook"],[m,s]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[n,"Voice"],k,[m,r]],[/\b(lvtel\-)?(v1[12]) b/i],[[n,"LvTel"],k,[m,r]],[/\b(ph-1) /i],[k,[n,"Essential"],[m,r]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[k,[n,"Envizen"],[m,s]],[/\b(trio[-\w\. ]+) b/i],[k,[n,"MachSpeed"],[m,s]],[/\btu_(1491) b/i],[k,[n,"Rotor"],[m,s]],[/(shield[\w ]+) b/i],[k,[n,"Nvidia"],[m,s]],[/(sprint) (\w+)/i],[n,k,[m,r]],[/(kin\.[onetw]{3})/i],[[k,/\./g," "],[n,F],[m,r]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[k,[n,M],[m,s]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[k,[n,M],[m,r]],[/smart-tv.+(samsung)/i],[n,[m,t]],[/hbbtv.+maple;(\d+)/i],[[k,/^/,"SmartTV"],[n,I],[m,t]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[n,"LG"],[m,t]],[/(apple) ?tv/i],[n,[k,x+" TV"],[m,t]],[/crkey/i],[[k,B+"cast"],[n,D],[m,t]],[/droid.+aft(\w)( bui|\))/i],[k,[n,w],[m,t]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[k,[n,J],[m,t]],[/(bravia[\w ]+)( bui|\))/i],[k,[n,K],[m,t]],[/(mitv-\w{5}) bui/i],[k,[n,L],[m,t]],[/Hbbtv.*(technisat) (.*);/i],[n,k,[m,t]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[n,U],[k,U],[m,t]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[m,t]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[n,k,[m,q]],[/droid.+; (shield) bui/i],[k,[n,"Nvidia"],[m,q]],[/(playstation [345portablevi]+)/i],[k,[n,K],[m,q]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[k,[n,F],[m,q]],[/((pebble))app/i],[n,k,[m,u]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[k,[n,x],[m,u]],[/droid.+; (glass) \d/i],[k,[n,D],[m,u]],[/droid.+; (wt63?0{2,3})\)/i],[k,[n,M],[m,u]],[/(quest( 2| pro)?)/i],[k,[n,N],[m,u]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[n,[m,v]],[/(aeobc)\b/i],[k,[n,w],[m,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[k,[m,r]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[k,[m,s]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[m,s]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[m,r]],[/(android[-\w\. ]{0,9});.+buil/i],[k,[n,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[o,[l,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[o,[l,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[l,o],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[o,l]],os:[[/microsoft (windows) (vista|xp)/i],[l,o],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[l,[o,W,X]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[l,"Windows"],[o,W,X]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[o,/_/g,"."],[l,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[l,P],[o,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[o,l],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[l,o],[/\(bb(10);/i],[o,[l,z]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[o,[l,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[o,[l,C+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[o,[l,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[o,[l,"watchOS"]],[/crkey\/([\d\.]+)/i],[o,[l,B+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[l,O],o],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[l,o],[/(sunos) ?([\w\.\d]*)/i],[[l,"Solaris"],o],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[l,o]]},Z=function(a,b){if(typeof a===h&&(b=a,a=e),!(this instanceof Z))return new Z(a,b).getResult();var c=typeof d!==g&&d.navigator?d.navigator:e,q=a||(c&&c.userAgent?c.userAgent:""),t=c&&c.userAgentData?c.userAgentData:e,u=b?Q(Y,b):Y,v=c&&c.userAgent==q;return this.getBrowser=function(){var a,b={};return b[l]=e,b[o]=e,V.call(b,q,u.browser),b[j]=typeof(a=b[o])===i?a.replace(/[^\d\.]/g,"").split(".")[0]:e,v&&c&&c.brave&&typeof c.brave.isBrave==f&&(b[l]="Brave"),b},this.getCPU=function(){var a={};return a[p]=e,V.call(a,q,u.cpu),a},this.getDevice=function(){var a={};return a[n]=e,a[k]=e,a[m]=e,V.call(a,q,u.device),v&&!a[m]&&t&&t.mobile&&(a[m]=r),v&&"Macintosh"==a[k]&&c&&typeof c.standalone!==g&&c.maxTouchPoints&&c.maxTouchPoints>2&&(a[k]="iPad",a[m]=s),a},this.getEngine=function(){var a={};return a[l]=e,a[o]=e,V.call(a,q,u.engine),a},this.getOS=function(){var a={};return a[l]=e,a[o]=e,V.call(a,q,u.os),v&&!a[l]&&t&&"Unknown"!=t.platform&&(a[l]=t.platform.replace(/chrome os/i,O).replace(/macos/i,P)),a},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return q},this.setUA=function(a){return q=typeof a===i&&a.length>350?U(a,350):a,this},this.setUA(q),this};if(Z.VERSION="1.0.35",Z.BROWSER=R([l,o,j]),Z.CPU=R([p]),Z.DEVICE=R([k,n,m,q,r,t,s,u,v]),Z.ENGINE=Z.OS=R([l,o]),typeof c!==g)b.exports&&(c=b.exports=Z),c.UAParser=Z;else if(typeof define===f&&define.amd)a.r,void 0!==Z&&a.v(Z);else typeof d!==g&&(d.UAParser=Z);var $=typeof d!==g&&(d.jQuery||d.Zepto);if($&&!$.ua){var _=new Z;$.ua=_.getResult(),$.ua.get=function(){return _.getUA()},$.ua.set=function(a){_.setUA(a);var b=_.getResult();for(var c in b)$.ua[c]=b[c]}}}(this)}},d={};function e(a){var b=d[a];if(void 0!==b)return b.exports;var f=d[a]={exports:{}},g=!0;try{c[a].call(f.exports,f,f.exports,e),g=!1}finally{g&&delete d[a]}return f.exports}e.ab="/ROOT/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/ua-parser-js/",b.exports=e(226)})()},7452,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{isBot:function(){return e},userAgent:function(){return g},userAgentFromString:function(){return f}});let d=function(a){return a&&a.__esModule?a:{default:a}}(a.r(96391));function e(a){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(a)}function f(a){return{...(0,d.default)(a),isBot:void 0!==a&&e(a)}}function g({headers:a}){return f(a.get("user-agent")||void 0)}},47629,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"URLPattern",{enumerable:!0,get:function(){return d}});let d="undefined"==typeof URLPattern?void 0:URLPattern},30954,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"after",{enumerable:!0,get:function(){return e}});let d=a.r(56704);function e(a){let b=d.workAsyncStorage.getStore();if(!b)throw Object.defineProperty(Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context"),"__NEXT_ERROR_CODE",{value:"E468",enumerable:!1,configurable:!0});let{afterContext:c}=b;return c.after(a)}},89956,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),function(a,b){Object.keys(a).forEach(function(c){"default"===c||Object.prototype.hasOwnProperty.call(b,c)||Object.defineProperty(b,c,{enumerable:!0,get:function(){return a[c]}})})}(a.r(30954),c)},36629,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{isRequestAPICallableInsideAfter:function(){return i},throwForSearchParamsAccessInUseCache:function(){return h},throwWithStaticGenerationBailoutError:function(){return f},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return g}});let d=a.r(85720),e=a.r(24725);function f(a,b){throw Object.defineProperty(new d.StaticGenBailoutError(`Route ${a} couldn't be rendered statically because it used ${b}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function g(a,b){throw Object.defineProperty(new d.StaticGenBailoutError(`Route ${a} with \`dynamic = "error"\` couldn't be rendered statically because it used ${b}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function h(a,b){let c=Object.defineProperty(Error(`Route ${a.route} used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(c,b),a.invalidDynamicUsageError??=c,c}function i(){let a=e.afterTaskAsyncStorage.getStore();return(null==a?void 0:a.rootTaskSpawnPhase)==="action"}},52121,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"connection",{enumerable:!0,get:function(){return j}});let d=a.r(56704),e=a.r(32319),f=a.r(36687),g=a.r(85720),h=a.r(20056),i=a.r(36629);function j(){let a=d.workAsyncStorage.getStore(),b=e.workUnitAsyncStorage.getStore();if(a){if(b&&"after"===b.phase&&!(0,i.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${a.route} used "connection" inside "after(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E186",enumerable:!1,configurable:!0});if(a.forceStatic)return Promise.resolve(void 0);if(a.dynamicShouldError)throw Object.defineProperty(new g.StaticGenBailoutError(`Route ${a.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`connection\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E562",enumerable:!1,configurable:!0});if(b)switch(b.type){case"cache":{let b=Object.defineProperty(Error(`Route ${a.route} used "connection" inside "use cache". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual request, but caches must be able to be produced before a request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E752",enumerable:!1,configurable:!0});throw Error.captureStackTrace(b,j),a.invalidDynamicUsageError??=b,b}case"private-cache":{let b=Object.defineProperty(Error(`Route ${a.route} used "connection" inside "use cache: private". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual navigation request, but caches must be able to be produced before a navigation request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E753",enumerable:!1,configurable:!0});throw Error.captureStackTrace(b,j),a.invalidDynamicUsageError??=b,b}case"unstable-cache":throw Object.defineProperty(Error(`Route ${a.route} used "connection" inside a function cached with "unstable_cache(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E1",enumerable:!1,configurable:!0});case"prerender":case"prerender-client":case"prerender-runtime":return(0,h.makeHangingPromise)(b.renderSignal,a.route,"`connection()`");case"prerender-ppr":return(0,f.postponeWithTracking)(a.route,"connection",b.dynamicTracking);case"prerender-legacy":return(0,f.throwToInterruptStaticGeneration)("connection",a,b);case"request":return(0,f.trackDynamicDataInDynamicRender)(b),Promise.resolve(void 0)}}(0,e.throwForMissingRequestStore)("connection")}},92580,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{describeHasCheckingStringProperty:function(){return f},describeStringPropertyAccess:function(){return e},wellKnownProperties:function(){return g}});let d=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function e(a,b){return d.test(b)?"`"+a+"."+b+"`":"`"+a+"["+JSON.stringify(b)+"]`"}function f(a,b){let c=JSON.stringify(b);return"`Reflect.has("+a+", "+c+")`, `"+c+" in "+a+"`, or similar"}let g=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},10771,(a,b,c)=>{"use strict";var d;Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{bgBlack:function(){return B},bgBlue:function(){return F},bgCyan:function(){return H},bgGreen:function(){return D},bgMagenta:function(){return G},bgRed:function(){return C},bgWhite:function(){return I},bgYellow:function(){return E},black:function(){return r},blue:function(){return v},bold:function(){return k},cyan:function(){return y},dim:function(){return l},gray:function(){return A},green:function(){return t},hidden:function(){return p},inverse:function(){return o},italic:function(){return m},magenta:function(){return w},purple:function(){return x},red:function(){return s},reset:function(){return j},strikethrough:function(){return q},underline:function(){return n},white:function(){return z},yellow:function(){return u}});let{env:e,stdout:f}=(null==(d=globalThis)?void 0:d.process)??{},g=e&&!e.NO_COLOR&&(e.FORCE_COLOR||(null==f?void 0:f.isTTY)&&!e.CI&&"dumb"!==e.TERM),h=(a,b,c,d)=>{let e=a.substring(0,d)+c,f=a.substring(d+b.length),g=f.indexOf(b);return~g?e+h(f,b,c,g):e+f},i=(a,b,c=a)=>g?d=>{let e=""+d,f=e.indexOf(b,a.length);return~f?a+h(e,b,c,f)+b:a+e+b}:String,j=g?a=>`\x1b[0m${a}\x1b[0m`:String,k=i("\x1b[1m","\x1b[22m","\x1b[22m\x1b[1m"),l=i("\x1b[2m","\x1b[22m","\x1b[22m\x1b[2m"),m=i("\x1b[3m","\x1b[23m"),n=i("\x1b[4m","\x1b[24m"),o=i("\x1b[7m","\x1b[27m"),p=i("\x1b[8m","\x1b[28m"),q=i("\x1b[9m","\x1b[29m"),r=i("\x1b[30m","\x1b[39m"),s=i("\x1b[31m","\x1b[39m"),t=i("\x1b[32m","\x1b[39m"),u=i("\x1b[33m","\x1b[39m"),v=i("\x1b[34m","\x1b[39m"),w=i("\x1b[35m","\x1b[39m"),x=i("\x1b[38;2;173;127;168m","\x1b[39m"),y=i("\x1b[36m","\x1b[39m"),z=i("\x1b[37m","\x1b[39m"),A=i("\x1b[90m","\x1b[39m"),B=i("\x1b[40m","\x1b[49m"),C=i("\x1b[41m","\x1b[49m"),D=i("\x1b[42m","\x1b[49m"),E=i("\x1b[43m","\x1b[49m"),F=i("\x1b[44m","\x1b[49m"),G=i("\x1b[45m","\x1b[49m"),H=i("\x1b[46m","\x1b[49m"),I=i("\x1b[47m","\x1b[49m")},14119,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"LRUCache",{enumerable:!0,get:function(){return f}});class d{constructor(a,b,c){this.prev=null,this.next=null,this.key=a,this.data=b,this.size=c}}class e{constructor(){this.prev=null,this.next=null}}class f{constructor(a,b){this.cache=new Map,this.totalSize=0,this.maxSize=a,this.calculateSize=b,this.head=new e,this.tail=new e,this.head.next=this.tail,this.tail.prev=this.head}addToHead(a){a.prev=this.head,a.next=this.head.next,this.head.next.prev=a,this.head.next=a}removeNode(a){a.prev.next=a.next,a.next.prev=a.prev}moveToHead(a){this.removeNode(a),this.addToHead(a)}removeTail(){let a=this.tail.prev;return this.removeNode(a),a}set(a,b){let c=(null==this.calculateSize?void 0:this.calculateSize.call(this,b))??1;if(c>this.maxSize)return void console.warn("Single item size exceeds maxSize");let e=this.cache.get(a);if(e)e.data=b,this.totalSize=this.totalSize-e.size+c,e.size=c,this.moveToHead(e);else{let e=new d(a,b,c);this.cache.set(a,e),this.addToHead(e),this.totalSize+=c}for(;this.totalSize>this.maxSize&&this.cache.size>0;){let a=this.removeTail();this.cache.delete(a.key),this.totalSize-=a.size}}has(a){return this.cache.has(a)}get(a){let b=this.cache.get(a);if(b)return this.moveToHead(b),b.data}*[Symbol.iterator](){let a=this.head.next;for(;a&&a!==this.tail;){let b=a;yield[b.key,b.data],a=a.next}}remove(a){let b=this.cache.get(a);b&&(this.removeNode(b),this.cache.delete(a),this.totalSize-=b.size)}get size(){return this.cache.size}get currentSize(){return this.totalSize}}},24278,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{bootstrap:function(){return i},error:function(){return k},event:function(){return o},info:function(){return n},prefixes:function(){return f},ready:function(){return m},trace:function(){return p},wait:function(){return j},warn:function(){return l},warnOnce:function(){return r}});let d=a.r(10771),e=a.r(14119),f={wait:(0,d.white)((0,d.bold)("○")),error:(0,d.red)((0,d.bold)("⨯")),warn:(0,d.yellow)((0,d.bold)("⚠")),ready:"▲",info:(0,d.white)((0,d.bold)(" ")),event:(0,d.green)((0,d.bold)("✓")),trace:(0,d.magenta)((0,d.bold)("»"))},g={log:"log",warn:"warn",error:"error"};function h(a,...b){(""===b[0]||void 0===b[0])&&1===b.length&&b.shift();let c=a in g?g[a]:"log",d=f[a];0===b.length?console[c](""):1===b.length&&"string"==typeof b[0]?console[c](" "+d+" "+b[0]):console[c](" "+d,...b)}function i(...a){console.log("   "+a.join(" "))}function j(...a){h("wait",...a)}function k(...a){h("error",...a)}function l(...a){h("warn",...a)}function m(...a){h("ready",...a)}function n(...a){h("info",...a)}function o(...a){h("event",...a)}function p(...a){h("trace",...a)}let q=new e.LRUCache(1e4,a=>a.length);function r(...a){let b=a.join(" ");q.has(b)||(q.set(b,b),l(...a))}},53969,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{getRootParam:function(){return n},unstable_rootParams:function(){return m}});let d=a.r(3550),e=a.r(36687),f=a.r(56704),g=a.r(32319),h=a.r(20056),i=a.r(92580),j=a.r(20635),k=a.r(24278),l=new WeakMap;async function m(){(0,k.warnOnce)("`unstable_rootParams()` is deprecated and will be removed in an upcoming major release. Import specific root params from `next/root-params` instead.");let a=f.workAsyncStorage.getStore();if(!a)throw Object.defineProperty(new d.InvariantError("Missing workStore in unstable_rootParams"),"__NEXT_ERROR_CODE",{value:"E615",enumerable:!1,configurable:!0});let b=g.workUnitAsyncStorage.getStore();if(!b)throw Object.defineProperty(Error(`Route ${a.route} used \`unstable_rootParams()\` in Pages Router. This API is only available within App Router.`),"__NEXT_ERROR_CODE",{value:"E641",enumerable:!1,configurable:!0});switch(b.type){case"cache":case"unstable-cache":throw Object.defineProperty(Error(`Route ${a.route} used \`unstable_rootParams()\` inside \`"use cache"\` or \`unstable_cache\`. Support for this API inside cache scopes is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E642",enumerable:!1,configurable:!0});case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":return function(a,b,c){switch(c.type){case"prerender-client":{let a="`unstable_rootParams`";throw Object.defineProperty(new d.InvariantError(`${a} must not be used within a client component. Next.js should be preventing ${a} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0})}case"prerender":{let d=c.fallbackRouteParams;if(d){for(let e in a)if(d.has(e)){let d=l.get(a);if(d)return d;let e=(0,h.makeHangingPromise)(c.renderSignal,b.route,"`unstable_rootParams`");return l.set(a,e),e}}break}case"prerender-ppr":{let d=c.fallbackRouteParams;if(d){for(let f in a)if(d.has(f))return function(a,b,c,d){let f=l.get(a);if(f)return f;let g={...a},h=Promise.resolve(g);return l.set(a,h),Object.keys(a).forEach(f=>{i.wellKnownProperties.has(f)||(b.has(f)?Object.defineProperty(g,f,{get(){let a=(0,i.describeStringPropertyAccess)("unstable_rootParams",f);"prerender-ppr"===d.type?(0,e.postponeWithTracking)(c.route,a,d.dynamicTracking):(0,e.throwToInterruptStaticGeneration)(a,c,d)},enumerable:!0}):h[f]=a[f])}),h}(a,d,b,c)}}}return Promise.resolve(a)}(b.rootParams,a,b);case"private-cache":case"prerender-runtime":case"request":return Promise.resolve(b.rootParams);default:return b}}function n(a){let b=`\`import('next/root-params').${a}()\``,c=f.workAsyncStorage.getStore();if(!c)throw Object.defineProperty(new d.InvariantError(`Missing workStore in ${b}`),"__NEXT_ERROR_CODE",{value:"E764",enumerable:!1,configurable:!0});let e=g.workUnitAsyncStorage.getStore();if(!e)throw Object.defineProperty(Error(`Route ${c.route} used ${b} outside of a Server Component. This is not allowed.`),"__NEXT_ERROR_CODE",{value:"E774",enumerable:!1,configurable:!0});let i=j.actionAsyncStorage.getStore();if(i){if(i.isAppRoute)throw Object.defineProperty(Error(`Route ${c.route} used ${b} inside a Route Handler. Support for this API in Route Handlers is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E765",enumerable:!1,configurable:!0});if(i.isAction&&"action"===e.phase)throw Object.defineProperty(Error(`${b} was used inside a Server Action. This is not supported. Functions from 'next/root-params' can only be called in the context of a route.`),"__NEXT_ERROR_CODE",{value:"E766",enumerable:!1,configurable:!0})}switch(e.type){case"unstable-cache":case"cache":throw Object.defineProperty(Error(`Route ${c.route} used ${b} inside \`"use cache"\` or \`unstable_cache\`. Support for this API inside cache scopes is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E760",enumerable:!1,configurable:!0});case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":var k=a,l=c,m=e,n=b;if("prerender-client"===m.type)throw Object.defineProperty(new d.InvariantError(`${n} must not be used within a client component. Next.js should be preventing ${n} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});let p=m.rootParams;switch(m.type){case"prerender":if(m.fallbackRouteParams&&m.fallbackRouteParams.has(k))return(0,h.makeHangingPromise)(m.renderSignal,l.route,n);break;case"prerender-ppr":if(m.fallbackRouteParams&&m.fallbackRouteParams.has(k))return o(k,l,m,n)}return Promise.resolve(p[k])}return Promise.resolve(e.rootParams[a])}async function o(a,b,c,d){let f=(0,i.describeStringPropertyAccess)(d,a);switch(c.type){case"prerender-ppr":return(0,e.postponeWithTracking)(b.route,f,c.dynamicTracking);case"prerender-legacy":return(0,e.throwToInterruptStaticGeneration)(f,b,c)}}},30304,(a,b,c)=>{let d={NextRequest:a.r(23047).NextRequest,NextResponse:a.r(14493).NextResponse,ImageResponse:a.r(98978).ImageResponse,userAgentFromString:a.r(7452).userAgentFromString,userAgent:a.r(7452).userAgent,URLPattern:a.r(47629).URLPattern,after:a.r(89956).after,connection:a.r(52121).connection,unstable_rootParams:a.r(53969).unstable_rootParams};b.exports=d,c.NextRequest=d.NextRequest,c.NextResponse=d.NextResponse,c.ImageResponse=d.ImageResponse,c.userAgentFromString=d.userAgentFromString,c.userAgent=d.userAgent,c.URLPattern=d.URLPattern,c.after=d.after,c.connection=d.connection,c.unstable_rootParams=d.unstable_rootParams},991,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{MutableRequestCookiesAdapter:function(){return l},ReadonlyRequestCookiesError:function(){return g},RequestCookiesAdapter:function(){return h},appendMutableCookies:function(){return k},areCookiesMutableInCurrentPhase:function(){return n},createCookiesWithMutableAccessCheck:function(){return m},getModifiedCookieValues:function(){return j},responseCookiesToRequestCookies:function(){return p}});let d=a.r(98394),e=a.r(60363),f=a.r(56704);class g extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new g}}class h{static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"clear":case"delete":case"set":return g.callable;default:return e.ReflectAdapter.get(a,b,c)}}})}}let i=Symbol.for("next.mutated.cookies");function j(a){let b=a[i];return b&&Array.isArray(b)&&0!==b.length?b:[]}function k(a,b){let c=j(b);if(0===c.length)return!1;let e=new d.ResponseCookies(a),f=e.getAll();for(let a of c)e.set(a);for(let a of f)e.set(a);return!0}class l{static wrap(a,b){let c=new d.ResponseCookies(new Headers);for(let b of a.getAll())c.set(b);let g=[],h=new Set,j=()=>{let a=f.workAsyncStorage.getStore();if(a&&(a.pathWasRevalidated=!0),g=c.getAll().filter(a=>h.has(a.name)),b){let a=[];for(let b of g){let c=new d.ResponseCookies(new Headers);c.set(b),a.push(c.toString())}b(a)}},k=new Proxy(c,{get(a,b,c){switch(b){case i:return g;case"delete":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.delete(...b),k}finally{j()}};case"set":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.set(...b),k}finally{j()}};default:return e.ReflectAdapter.get(a,b,c)}}});return k}}function m(a){let b=new Proxy(a.mutableCookies,{get(c,d,f){switch(d){case"delete":return function(...d){return o(a,"cookies().delete"),c.delete(...d),b};case"set":return function(...d){return o(a,"cookies().set"),c.set(...d),b};default:return e.ReflectAdapter.get(c,d,f)}}});return b}function n(a){return"action"===a.phase}function o(a,b){if(!n(a))throw new g}function p(a){let b=new d.RequestCookies(new Headers);for(let c of a.getAll())b.set(c);return b}},68479,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return i}});let d=function(a,b){if(a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=e(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=f?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(d,g,h):d[g]=a[g]}return d.default=a,c&&c.set(a,d),d}(a.r(128));function e(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(e=function(a){return a?c:b})(a)}let f={current:null},g="function"==typeof d.cache?d.cache:a=>a,h=console.warn;function i(a){return function(...b){h(a(...b))}}g(a=>{try{h(f.current)}finally{f.current=null}})},53229,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"cookies",{enumerable:!0,get:function(){return n}});let d=a.r(991),e=a.r(98394),f=a.r(56704),g=a.r(32319),h=a.r(36687),i=a.r(85720),j=a.r(20056),k=a.r(68479),l=a.r(36629),m=a.r(3550);function n(){let a="cookies",b=f.workAsyncStorage.getStore(),c=g.workUnitAsyncStorage.getStore();if(b){if(c&&"after"===c.phase&&!(0,l.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(b.forceStatic)return p(d.RequestCookiesAdapter.seal(new e.RequestCookies(new Headers({}))));if(b.dynamicShouldError)throw Object.defineProperty(new i.StaticGenBailoutError(`Route ${b.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(c)switch(c.type){case"cache":let f=Object.defineProperty(Error(`Route ${b.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});throw Error.captureStackTrace(f,n),b.invalidDynamicUsageError??=f,f;case"unstable-cache":throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0});case"prerender":var k=b,q=c;let g=o.get(q);if(g)return g;let r=(0,j.makeHangingPromise)(q.renderSignal,k.route,"`cookies()`");return o.set(q,r),r;case"prerender-client":let s="`cookies`";throw Object.defineProperty(new m.InvariantError(`${s} must not be used within a client component. Next.js should be preventing ${s} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":return(0,h.postponeWithTracking)(b.route,a,c.dynamicTracking);case"prerender-legacy":return(0,h.throwToInterruptStaticGeneration)(a,b,c);case"prerender-runtime":return(0,h.delayUntilRuntimeStage)(c,function(a){let b=o.get(a);if(b)return b;let c=Promise.resolve(a);return o.set(a,c),c}(c.cookies));case"private-cache":return p(c.cookies);case"request":return(0,h.trackDynamicDataInDynamicRender)(c),p((0,d.areCookiesMutableInCurrentPhase)(c)?c.userspaceMutableCookies:c.cookies)}}(0,g.throwForMissingRequestStore)(a)}a.r(60363);let o=new WeakMap;function p(a){let b=o.get(a);if(b)return b;let c=Promise.resolve(a);return o.set(a,c),Object.defineProperties(c,{[Symbol.iterator]:{value:a[Symbol.iterator]?a[Symbol.iterator].bind(a):q.bind(a)},size:{get:()=>a.size},get:{value:a.get.bind(a)},getAll:{value:a.getAll.bind(a)},has:{value:a.has.bind(a)},set:{value:a.set.bind(a)},delete:{value:a.delete.bind(a)},clear:{value:"function"==typeof a.clear?a.clear.bind(a):r.bind(a,c)},toString:{value:a.toString.bind(a)}}),c}function q(){return this.getAll().map(a=>[a.name,a]).values()}function r(a){for(let a of this.getAll())this.delete(a.name);return a}(0,k.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})})},59533,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{HeadersAdapter:function(){return f},ReadonlyHeadersError:function(){return e}});let d=a.r(60363);class e extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new e}}class f extends Headers{constructor(a){super(),this.headers=new Proxy(a,{get(b,c,e){if("symbol"==typeof c)return d.ReflectAdapter.get(b,c,e);let f=c.toLowerCase(),g=Object.keys(a).find(a=>a.toLowerCase()===f);if(void 0!==g)return d.ReflectAdapter.get(b,g,e)},set(b,c,e,f){if("symbol"==typeof c)return d.ReflectAdapter.set(b,c,e,f);let g=c.toLowerCase(),h=Object.keys(a).find(a=>a.toLowerCase()===g);return d.ReflectAdapter.set(b,h??c,e,f)},has(b,c){if("symbol"==typeof c)return d.ReflectAdapter.has(b,c);let e=c.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0!==f&&d.ReflectAdapter.has(b,f)},deleteProperty(b,c){if("symbol"==typeof c)return d.ReflectAdapter.deleteProperty(b,c);let e=c.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0===f||d.ReflectAdapter.deleteProperty(b,f)}})}static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"append":case"delete":case"set":return e.callable;default:return d.ReflectAdapter.get(a,b,c)}}})}merge(a){return Array.isArray(a)?a.join(", "):a}static from(a){return a instanceof Headers?a:new f(a)}append(a,b){let c=this.headers[a];"string"==typeof c?this.headers[a]=[c,b]:Array.isArray(c)?c.push(b):this.headers[a]=b}delete(a){delete this.headers[a]}get(a){let b=this.headers[a];return void 0!==b?this.merge(b):null}has(a){return void 0!==this.headers[a]}set(a,b){this.headers[a]=b}forEach(a,b){for(let[c,d]of this.entries())a.call(b,d,c,this)}*entries(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase(),c=this.get(b);yield[b,c]}}*keys(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase();yield b}}*values(){for(let a of Object.keys(this.headers)){let b=this.get(a);yield b}}[Symbol.iterator](){return this.entries()}}},21888,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"headers",{enumerable:!0,get:function(){return m}});let d=a.r(59533),e=a.r(56704),f=a.r(32319),g=a.r(36687),h=a.r(85720),i=a.r(20056),j=a.r(68479),k=a.r(36629),l=a.r(3550);function m(){let a="headers",b=e.workAsyncStorage.getStore(),c=f.workUnitAsyncStorage.getStore();if(b){if(c&&"after"===c.phase&&!(0,k.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${b.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(b.forceStatic)return o(d.HeadersAdapter.seal(new Headers({})));if(c)switch(c.type){case"cache":{let a=Object.defineProperty(Error(`Route ${b.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});throw Error.captureStackTrace(a,m),b.invalidDynamicUsageError??=a,a}case"private-cache":{let a=Object.defineProperty(Error(`Route ${b.route} used "headers" inside "use cache: private". Accessing "headers" inside a private cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E742",enumerable:!1,configurable:!0});throw Error.captureStackTrace(a,m),b.invalidDynamicUsageError??=a,a}case"unstable-cache":throw Object.defineProperty(Error(`Route ${b.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(b.dynamicShouldError)throw Object.defineProperty(new h.StaticGenBailoutError(`Route ${b.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(c)switch(c.type){case"prerender":case"prerender-runtime":var j=b,p=c;let e=n.get(p);if(e)return e;let f=(0,i.makeHangingPromise)(p.renderSignal,j.route,"`headers()`");return n.set(p,f),f;case"prerender-client":let q="`headers`";throw Object.defineProperty(new l.InvariantError(`${q} must not be used within a client component. Next.js should be preventing ${q} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":return(0,g.postponeWithTracking)(b.route,a,c.dynamicTracking);case"prerender-legacy":return(0,g.throwToInterruptStaticGeneration)(a,b,c);case"request":return(0,g.trackDynamicDataInDynamicRender)(c),o(c.headers)}}(0,f.throwForMissingRequestStore)(a)}a.r(60363);let n=new WeakMap;function o(a){let b=n.get(a);if(b)return b;let c=Promise.resolve(a);return n.set(a,c),Object.defineProperties(c,{append:{value:a.append.bind(a)},delete:{value:a.delete.bind(a)},get:{value:a.get.bind(a)},has:{value:a.has.bind(a)},set:{value:a.set.bind(a)},getSetCookie:{value:a.getSetCookie.bind(a)},forEach:{value:a.forEach.bind(a)},keys:{value:a.keys.bind(a)},values:{value:a.values.bind(a)},entries:{value:a.entries.bind(a)},[Symbol.iterator]:{value:a[Symbol.iterator].bind(a)}}),c}(0,j.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})})},14648,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"draftMode",{enumerable:!0,get:function(){return k}});let d=a.r(32319),e=a.r(56704),f=a.r(36687),g=a.r(68479),h=a.r(85720),i=a.r(67596),j=a.r(3550);function k(){let a=e.workAsyncStorage.getStore(),b=d.workUnitAsyncStorage.getStore();switch((!a||!b)&&(0,d.throwForMissingRequestStore)("draftMode"),b.type){case"prerender-runtime":return(0,f.delayUntilRuntimeStage)(b,l(b.draftMode,a));case"request":return l(b.draftMode,a);case"cache":case"private-cache":case"unstable-cache":let c=(0,d.getDraftModeProviderForCacheScope)(a,b);if(c)return l(c,a);case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":return l(null,a);default:return b}}function l(a,b){let c,d=a??m,e=n.get(d);return e||(c=function(a){let b=new o(a),c=Promise.resolve(b);return Object.defineProperty(c,"isEnabled",{get:()=>b.isEnabled,enumerable:!0,configurable:!0}),c.enable=b.enable.bind(b),c.disable=b.disable.bind(b),c}(a),n.set(d,c),c)}a.r(60363);let m={},n=new WeakMap;class o{constructor(a){this._provider=a}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){p("draftMode().enable()",this.enable),null!==this._provider&&this._provider.enable()}disable(){p("draftMode().disable()",this.disable),null!==this._provider&&this._provider.disable()}}function p(a,b){let c=e.workAsyncStorage.getStore(),g=d.workUnitAsyncStorage.getStore();if(c){if((null==g?void 0:g.phase)==="after")throw Object.defineProperty(Error(`Route ${c.route} used "${a}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0});if(c.dynamicShouldError)throw Object.defineProperty(new h.StaticGenBailoutError(`Route ${c.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${a}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(g)switch(g.type){case"cache":case"private-cache":{let d=Object.defineProperty(Error(`Route ${c.route} used "${a}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});throw Error.captureStackTrace(d,b),c.invalidDynamicUsageError??=d,d}case"unstable-cache":throw Object.defineProperty(Error(`Route ${c.route} used "${a}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});case"prerender":case"prerender-runtime":{let b=Object.defineProperty(Error(`Route ${c.route} used ${a} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});return(0,f.abortAndThrowOnSynchronousRequestDataAccess)(c.route,a,b,g)}case"prerender-client":let d="`draftMode`";throw Object.defineProperty(new j.InvariantError(`${d} must not be used within a client component. Next.js should be preventing ${d} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":return(0,f.postponeWithTracking)(c.route,a,g.dynamicTracking);case"prerender-legacy":g.revalidate=0;let e=Object.defineProperty(new i.DynamicServerError(`Route ${c.route} couldn't be rendered statically because it used \`${a}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw c.dynamicUsageDescription=a,c.dynamicUsageStack=e.stack,e;case"request":(0,f.trackDynamicDataInDynamicRender)(g)}}}(0,g.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})})},39804,(a,b,c)=>{b.exports.cookies=a.r(53229).cookies,b.exports.headers=a.r(21888).headers,b.exports.draftMode=a.r(14648).draftMode},34075,a=>{"use strict";let b,c,d,e,f;a.s(["apiService",()=>fk],34075),a.i(81223);var g=function(a,b,c,d,e){if("m"===d)throw TypeError("Private method is not writable");if("a"===d&&!e)throw TypeError("Private accessor was defined without a setter");if("function"==typeof b?a!==b||!e:!b.has(a))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===d?e.call(a,c):e?e.value=c:b.set(a,c),c},h=function(a,b,c,d){if("a"===c&&!d)throw TypeError("Private accessor was defined without a getter");if("function"==typeof b?a!==b||!d:!b.has(a))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===c?d:"a"===c?d.call(a):d?d.value:b.get(a)};function i(a){let b=a?"__Secure-":"";return{sessionToken:{name:`${b}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},callbackUrl:{name:`${b}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},csrfToken:{name:`${a?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},pkceCodeVerifier:{name:`${b}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}},state:{name:`${b}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}},nonce:{name:`${b}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a}},webauthnChallenge:{name:`${b}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:a,maxAge:900}}}}class j{constructor(a,b,c){if(bN.add(this),bO.set(this,{}),bP.set(this,void 0),bQ.set(this,void 0),g(this,bQ,c,"f"),g(this,bP,a,"f"),!b)return;let{name:d}=a;for(let[a,c]of Object.entries(b))a.startsWith(d)&&c&&(h(this,bO,"f")[a]=c)}get value(){return Object.keys(h(this,bO,"f")).sort((a,b)=>parseInt(a.split(".").pop()||"0")-parseInt(b.split(".").pop()||"0")).map(a=>h(this,bO,"f")[a]).join("")}chunk(a,b){let c=h(this,bN,"m",bS).call(this);for(let d of h(this,bN,"m",bR).call(this,{name:h(this,bP,"f").name,value:a,options:{...h(this,bP,"f").options,...b}}))c[d.name]=d;return Object.values(c)}clean(){return Object.values(h(this,bN,"m",bS).call(this))}}bO=new WeakMap,bP=new WeakMap,bQ=new WeakMap,bN=new WeakSet,bR=function(a){let b=Math.ceil(a.value.length/3936);if(1===b)return h(this,bO,"f")[a.name]=a.value,[a];let c=[];for(let d=0;d<b;d++){let b=`${a.name}.${d}`,e=a.value.substr(3936*d,3936);c.push({...a,name:b,value:e}),h(this,bO,"f")[b]=e}return h(this,bQ,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:a.value.length,chunks:c.map(a=>a.value.length+160)}),c},bS=function(){let a={};for(let b in h(this,bO,"f"))delete h(this,bO,"f")?.[b],a[b]={name:b,value:"",options:{...h(this,bP,"f").options,maxAge:0}};return a};var k=a.i(89610);let l=!1;function m(a,b){try{return/^https?:/.test(new URL(a,a.startsWith("/")?b:void 0).protocol)}catch{return!1}}let n=!1,o=!1,p=!1,q=["createVerificationToken","useVerificationToken","getUserByEmail"],r=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],s=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var t=a.i(54799);"function"!=typeof t.hkdf||process.versions.electron||(b=async(...a)=>new Promise((b,c)=>{t.hkdf(...a,(a,d)=>{a?c(a):b(new Uint8Array(d))})}));let u=async(a,c,d,e,f)=>(b||((a,b,c,d,e)=>{let f=parseInt(a.substr(3),10)>>3||20,g=(0,t.createHmac)(a,c.byteLength?c:new Uint8Array(f)).update(b).digest(),h=Math.ceil(e/f),i=new Uint8Array(f*h+d.byteLength+1),j=0,k=0;for(let b=1;b<=h;b++)i.set(d,k),i[k+d.byteLength]=b,i.set((0,t.createHmac)(a,g).update(i.subarray(j,k+d.byteLength+1)).digest(),k),j=k,k+=f;return i.slice(0,e)}))(a,c,d,e,f);function v(a,b){if("string"==typeof a)return new TextEncoder().encode(a);if(!(a instanceof Uint8Array))throw TypeError(`"${b}"" must be an instance of Uint8Array or a string`);return a}async function w(a,b,c,d,e){return u(function(a){switch(a){case"sha256":case"sha384":case"sha512":case"sha1":return a;default:throw TypeError('unsupported "digest" value')}}(a),function(a){let b=v(a,"ikm");if(!b.byteLength)throw TypeError('"ikm" must be at least one byte in length');return b}(b),v(c,"salt"),function(a){let b=v(a,"info");if(b.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return b}(d),function(a,b){if("number"!=typeof a||!Number.isInteger(a)||a<1)throw TypeError('"keylen" must be a positive integer');if(a>255*(parseInt(b.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return a}(e,a))}a.s(["decode",()=>D,"encode",()=>E],82600);let x=new TextEncoder,y=new TextDecoder;function z(...a){let b=new Uint8Array(a.reduce((a,{length:b})=>a+b,0)),c=0;for(let d of a)b.set(d,c),c+=d.length;return b}function A(a,b,c){if(b<0||b>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${b}`);a.set([b>>>24,b>>>16,b>>>8,255&b],c)}function B(a){let b=Math.floor(a/0x100000000),c=new Uint8Array(8);return A(c,b,0),A(c,a%0x100000000,4),c}function C(a){let b=new Uint8Array(4);return A(b,a),b}function D(a){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof a?a:y.decode(a),{alphabet:"base64url"});let b=a;b instanceof Uint8Array&&(b=y.decode(b)),b=b.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{var c=b;if(Uint8Array.fromBase64)return Uint8Array.fromBase64(c);let a=atob(c),d=new Uint8Array(a.length);for(let b=0;b<a.length;b++)d[b]=a.charCodeAt(b);return d}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function E(a){let b=a;return("string"==typeof b&&(b=x.encode(b)),Uint8Array.prototype.toBase64)?b.toBase64({alphabet:"base64url",omitPadding:!0}):(function(a){if(Uint8Array.prototype.toBase64)return a.toBase64();let b=[];for(let c=0;c<a.length;c+=32768)b.push(String.fromCharCode.apply(null,a.subarray(c,c+32768)));return btoa(b.join(""))})(b).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}let F=Symbol();class G extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(a,b){super(a,b),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class H extends G{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(a,b,c="unspecified",d="unspecified"){super(a,{cause:{claim:c,reason:d,payload:b}}),this.claim=c,this.reason=d,this.payload=b}}class I extends G{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(a,b,c="unspecified",d="unspecified"){super(a,{cause:{claim:c,reason:d,payload:b}}),this.claim=c,this.reason=d,this.payload=b}}class J extends G{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class K extends G{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class L extends G{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(a="decryption operation failed",b){super(a,b)}}class M extends G{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class N extends G{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class O extends G{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class P extends G{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(a="multiple matching keys found in the JSON Web Key Set",b){super(a,b)}}function Q(a){switch(a){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new K(`Unsupported JWE Algorithm: ${a}`)}}let R=(a,b)=>{if(b.length<<3!==Q(a))throw new M("Invalid Initialization Vector length")},S=(a,b)=>{let c=a.byteLength<<3;if(c!==b)throw new M(`Invalid Content Encryption Key length. Expected ${b} bits, got ${c} bits`)};function T(a,b="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${b} must be ${a}`)}function U(a,b){return a.name===b}function V(a,b,c){switch(b){case"A128GCM":case"A192GCM":case"A256GCM":{if(!U(a.algorithm,"AES-GCM"))throw T("AES-GCM");let c=parseInt(b.slice(1,4),10);if(a.algorithm.length!==c)throw T(c,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!U(a.algorithm,"AES-KW"))throw T("AES-KW");let c=parseInt(b.slice(1,4),10);if(a.algorithm.length!==c)throw T(c,"algorithm.length");break}case"ECDH":switch(a.algorithm.name){case"ECDH":case"X25519":break;default:throw T("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!U(a.algorithm,"PBKDF2"))throw T("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!U(a.algorithm,"RSA-OAEP"))throw T("RSA-OAEP");let c=parseInt(b.slice(9),10)||1;if(parseInt(a.algorithm.hash.name.slice(4),10)!==c)throw T(`SHA-${c}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}if(c&&!a.usages.includes(c))throw TypeError(`CryptoKey does not support this operation, its usages must include ${c}.`)}function W(a,b,...c){if((c=c.filter(Boolean)).length>2){let b=c.pop();a+=`one of type ${c.join(", ")}, or ${b}.`}else 2===c.length?a+=`one of type ${c[0]} or ${c[1]}.`:a+=`of type ${c[0]}.`;return null==b?a+=` Received ${b}`:"function"==typeof b&&b.name?a+=` Received function ${b.name}`:"object"==typeof b&&null!=b&&b.constructor?.name&&(a+=` Received an instance of ${b.constructor.name}`),a}let X=(a,...b)=>W("Key must be ",a,...b);function Y(a,b,...c){return W(`Key for the ${a} algorithm must be `,b,...c)}function Z(a){if(!$(a))throw Error("CryptoKey instance expected")}function $(a){return a?.[Symbol.toStringTag]==="CryptoKey"}function _(a){return a?.[Symbol.toStringTag]==="KeyObject"}let aa=a=>$(a)||_(a);async function ab(a,b,c,d,e){if(!(c instanceof Uint8Array))throw TypeError(X(c,"Uint8Array"));let f=parseInt(a.slice(1,4),10),g=await crypto.subtle.importKey("raw",c.subarray(f>>3),"AES-CBC",!1,["encrypt"]),h=await crypto.subtle.importKey("raw",c.subarray(0,f>>3),{hash:`SHA-${f<<1}`,name:"HMAC"},!1,["sign"]),i=new Uint8Array(await crypto.subtle.encrypt({iv:d,name:"AES-CBC"},g,b)),j=z(e,d,i,B(e.length<<3));return{ciphertext:i,tag:new Uint8Array((await crypto.subtle.sign("HMAC",h,j)).slice(0,f>>3)),iv:d}}async function ac(a,b,c,d,e){let f;c instanceof Uint8Array?f=await crypto.subtle.importKey("raw",c,"AES-GCM",!1,["encrypt"]):(V(c,a,"encrypt"),f=c);let g=new Uint8Array(await crypto.subtle.encrypt({additionalData:e,iv:d,name:"AES-GCM",tagLength:128},f,b)),h=g.slice(-16);return{ciphertext:g.slice(0,-16),tag:h,iv:d}}let ad=async(a,b,c,d,e)=>{if(!$(c)&&!(c instanceof Uint8Array))throw TypeError(X(c,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(d)R(a,d);else d=crypto.getRandomValues(new Uint8Array(Q(a)>>3));switch(a){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return c instanceof Uint8Array&&S(c,parseInt(a.slice(-3),10)),ab(a,b,c,d,e);case"A128GCM":case"A192GCM":case"A256GCM":return c instanceof Uint8Array&&S(c,parseInt(a.slice(1,4),10)),ac(a,b,c,d,e);default:throw new K("Unsupported JWE Content Encryption Algorithm")}};function ae(a,b){if(a.algorithm.length!==parseInt(b.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${b}`)}function af(a,b,c){return a instanceof Uint8Array?crypto.subtle.importKey("raw",a,"AES-KW",!0,[c]):(V(a,b,c),a)}async function ag(a,b,c){let d=await af(b,a,"wrapKey");ae(d,a);let e=await crypto.subtle.importKey("raw",c,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",e,d,"AES-KW"))}async function ah(a,b,c){let d=await af(b,a,"unwrapKey");ae(d,a);let e=await crypto.subtle.unwrapKey("raw",c,d,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",e))}let ai=async(a,b)=>{let c=`SHA-${a.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(c,b))};function aj(a){return z(C(a.length),a)}async function ak(a,b,c){let d=b>>3,e=Math.ceil(d/32),f=new Uint8Array(32*e);for(let b=1;b<=e;b++){let d=new Uint8Array(4+a.length+c.length);d.set(C(b),0),d.set(a,4),d.set(c,4+a.length);let e=await ai("sha256",d);f.set(e,(b-1)*32)}return f.slice(0,d)}async function al(a,b,c,d,e=new Uint8Array(0),f=new Uint8Array(0)){var g;V(a,"ECDH"),V(b,"ECDH","deriveBits");let h=aj(x.encode(c)),i=aj(e),j=aj(f),k=z(h,i,j,C(d),new Uint8Array(0));return ak(new Uint8Array(await crypto.subtle.deriveBits({name:a.algorithm.name,public:a},b,"X25519"===(g=a).algorithm.name?256:Math.ceil(parseInt(g.algorithm.namedCurve.slice(-3),10)/8)<<3)),d,k)}function am(a){switch(a.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===a.algorithm.name}}async function an(a,b,c,d){if(!(a instanceof Uint8Array)||a.length<8)throw new M("PBES2 Salt Input must be 8 or more octets");let e=z(x.encode(b),new Uint8Array([0]),a),f=parseInt(b.slice(13,16),10),g={hash:`SHA-${b.slice(8,11)}`,iterations:c,name:"PBKDF2",salt:e},h=await (d instanceof Uint8Array?crypto.subtle.importKey("raw",d,"PBKDF2",!1,["deriveBits"]):(V(d,b,"deriveBits"),d));return new Uint8Array(await crypto.subtle.deriveBits(g,h,f))}async function ao(a,b,c,d=2048,e=crypto.getRandomValues(new Uint8Array(16))){let f=await an(e,a,d,b);return{encryptedKey:await ag(a.slice(-6),f,c),p2c:d,p2s:E(e)}}async function ap(a,b,c,d,e){let f=await an(e,a,d,b);return ah(a.slice(-6),f,c)}let aq=(a,b)=>{if(a.startsWith("RS")||a.startsWith("PS")){let{modulusLength:c}=b.algorithm;if("number"!=typeof c||c<2048)throw TypeError(`${a} requires key modulusLength to be 2048 bits or larger`)}},ar=a=>{switch(a){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new K(`alg ${a} is not supported either by JOSE or your javascript runtime`)}};async function as(a,b,c){return V(b,a,"encrypt"),aq(a,b),new Uint8Array(await crypto.subtle.encrypt(ar(a),b,c))}async function at(a,b,c){return V(b,a,"decrypt"),aq(a,b),new Uint8Array(await crypto.subtle.decrypt(ar(a),b,c))}let au=a=>{if(!function(a){return"object"==typeof a&&null!==a}(a)||"[object Object]"!==Object.prototype.toString.call(a))return!1;if(null===Object.getPrototypeOf(a))return!0;let b=a;for(;null!==Object.getPrototypeOf(b);)b=Object.getPrototypeOf(b);return Object.getPrototypeOf(a)===b};function av(a){return au(a)&&"string"==typeof a.kty}let aw=async a=>{if(!a.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:b,keyUsages:c}=function(a){let b,c;switch(a.kty){case"AKP":switch(a.alg){case"ML-DSA-44":case"ML-DSA-65":case"ML-DSA-87":b={name:a.alg},c=a.priv?["sign"]:["verify"];break;default:throw new K('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"RSA":switch(a.alg){case"PS256":case"PS384":case"PS512":b={name:"RSA-PSS",hash:`SHA-${a.alg.slice(-3)}`},c=a.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":b={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${a.alg.slice(-3)}`},c=a.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":b={name:"RSA-OAEP",hash:`SHA-${parseInt(a.alg.slice(-3),10)||1}`},c=a.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new K('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(a.alg){case"ES256":b={name:"ECDSA",namedCurve:"P-256"},c=a.d?["sign"]:["verify"];break;case"ES384":b={name:"ECDSA",namedCurve:"P-384"},c=a.d?["sign"]:["verify"];break;case"ES512":b={name:"ECDSA",namedCurve:"P-521"},c=a.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":b={name:"ECDH",namedCurve:a.crv},c=a.d?["deriveBits"]:[];break;default:throw new K('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(a.alg){case"Ed25519":case"EdDSA":b={name:"Ed25519"},c=a.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":b={name:a.crv},c=a.d?["deriveBits"]:[];break;default:throw new K('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new K('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:b,keyUsages:c}}(a),d={...a};return"AKP"!==d.kty&&delete d.alg,delete d.use,crypto.subtle.importKey("jwk",d,b,a.ext??(!a.d&&!a.priv),a.key_ops??c)},ax=async(a,b,d,e=!1)=>{let f=(c||=new WeakMap).get(a);if(f?.[d])return f[d];let g=await aw({...b,alg:d});return e&&Object.freeze(a),f?f[d]=g:c.set(a,{[d]:g}),g},ay=async(a,b)=>{if(a instanceof Uint8Array||$(a))return a;if(_(a)){if("secret"===a.type)return a.export();if("toCryptoKey"in a&&"function"==typeof a.toCryptoKey)try{return((a,b)=>{let d,e=(c||=new WeakMap).get(a);if(e?.[b])return e[b];let f="public"===a.type,g=!!f;if("x25519"===a.asymmetricKeyType){switch(b){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}d=a.toCryptoKey(a.asymmetricKeyType,g,f?[]:["deriveBits"])}if("ed25519"===a.asymmetricKeyType){if("EdDSA"!==b&&"Ed25519"!==b)throw TypeError("given KeyObject instance cannot be used for this algorithm");d=a.toCryptoKey(a.asymmetricKeyType,g,[f?"verify":"sign"])}switch(a.asymmetricKeyType){case"ml-dsa-44":case"ml-dsa-65":case"ml-dsa-87":if(b!==a.asymmetricKeyType.toUpperCase())throw TypeError("given KeyObject instance cannot be used for this algorithm");d=a.toCryptoKey(a.asymmetricKeyType,g,[f?"verify":"sign"])}if("rsa"===a.asymmetricKeyType){let c;switch(b){case"RSA-OAEP":c="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":c="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":c="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":c="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(b.startsWith("RSA-OAEP"))return a.toCryptoKey({name:"RSA-OAEP",hash:c},g,f?["encrypt"]:["decrypt"]);d=a.toCryptoKey({name:b.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:c},g,[f?"verify":"sign"])}if("ec"===a.asymmetricKeyType){let c=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(a.asymmetricKeyDetails?.namedCurve);if(!c)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===b&&"P-256"===c&&(d=a.toCryptoKey({name:"ECDSA",namedCurve:c},g,[f?"verify":"sign"])),"ES384"===b&&"P-384"===c&&(d=a.toCryptoKey({name:"ECDSA",namedCurve:c},g,[f?"verify":"sign"])),"ES512"===b&&"P-521"===c&&(d=a.toCryptoKey({name:"ECDSA",namedCurve:c},g,[f?"verify":"sign"])),b.startsWith("ECDH-ES")&&(d=a.toCryptoKey({name:"ECDH",namedCurve:c},g,f?[]:["deriveBits"]))}if(!d)throw TypeError("given KeyObject instance cannot be used for this algorithm");return e?e[b]=d:c.set(a,{[b]:d}),d})(a,b)}catch(a){if(a instanceof TypeError)throw a}let d=a.export({format:"jwk"});return ax(a,d,b)}if(av(a))return a.k?D(a.k):ax(a,a,b,!0);throw Error("unreachable")};function az(a){switch(a){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new K(`Unsupported JWE Algorithm: ${a}`)}}let aA=a=>crypto.getRandomValues(new Uint8Array(az(a)>>3));async function aB(a){if(_(a))if("secret"!==a.type)return a.export({format:"jwk"});else a=a.export();if(a instanceof Uint8Array)return{kty:"oct",k:E(a)};if(!$(a))throw TypeError(X(a,"CryptoKey","KeyObject","Uint8Array"));if(!a.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:b,key_ops:c,alg:d,use:e,...f}=await crypto.subtle.exportKey("jwk",a);return"AKP"===f.kty&&(f.alg=d),f}async function aC(a){return aB(a)}async function aD(a,b){if(!(a instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(b instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let c={name:"HMAC",hash:"SHA-256"},d=await crypto.subtle.generateKey(c,!1,["sign"]),e=new Uint8Array(await crypto.subtle.sign(c,d,a)),f=new Uint8Array(await crypto.subtle.sign(c,d,b)),g=0,h=-1;for(;++h<32;)g|=e[h]^f[h];return 0===g}async function aE(a,b,c,d,e,f){let g,h;if(!(b instanceof Uint8Array))throw TypeError(X(b,"Uint8Array"));let i=parseInt(a.slice(1,4),10),j=await crypto.subtle.importKey("raw",b.subarray(i>>3),"AES-CBC",!1,["decrypt"]),k=await crypto.subtle.importKey("raw",b.subarray(0,i>>3),{hash:`SHA-${i<<1}`,name:"HMAC"},!1,["sign"]),l=z(f,d,c,B(f.length<<3)),m=new Uint8Array((await crypto.subtle.sign("HMAC",k,l)).slice(0,i>>3));try{g=await aD(e,m)}catch{}if(!g)throw new L;try{h=new Uint8Array(await crypto.subtle.decrypt({iv:d,name:"AES-CBC"},j,c))}catch{}if(!h)throw new L;return h}async function aF(a,b,c,d,e,f){let g;b instanceof Uint8Array?g=await crypto.subtle.importKey("raw",b,"AES-GCM",!1,["decrypt"]):(V(b,a,"decrypt"),g=b);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:f,iv:d,name:"AES-GCM",tagLength:128},g,z(c,e)))}catch{throw new L}}let aG=async(a,b,c,d,e,f)=>{if(!$(b)&&!(b instanceof Uint8Array))throw TypeError(X(b,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!d)throw new M("JWE Initialization Vector missing");if(!e)throw new M("JWE Authentication Tag missing");switch(R(a,d),a){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return b instanceof Uint8Array&&S(b,parseInt(a.slice(-3),10)),aE(a,b,c,d,e,f);case"A128GCM":case"A192GCM":case"A256GCM":return b instanceof Uint8Array&&S(b,parseInt(a.slice(1,4),10)),aF(a,b,c,d,e,f);default:throw new K("Unsupported JWE Content Encryption Algorithm")}};async function aH(a,b,c,d){let e=a.slice(0,7),f=await ad(e,c,b,d,new Uint8Array(0));return{encryptedKey:f.ciphertext,iv:E(f.iv),tag:E(f.tag)}}async function aI(a,b,c,d,e){return aG(a.slice(0,7),b,c,d,e,new Uint8Array(0))}let aJ=async(a,b,c,d,e={})=>{let f,g,h;switch(a){case"dir":h=c;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i;if(Z(c),!am(c))throw new K("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:j,apv:k}=e;i=e.epk?await ay(e.epk,a):(await crypto.subtle.generateKey(c.algorithm,!0,["deriveBits"])).privateKey;let{x:l,y:m,crv:n,kty:o}=await aC(i),p=await al(c,i,"ECDH-ES"===a?b:a,"ECDH-ES"===a?az(b):parseInt(a.slice(-5,-2),10),j,k);if(g={epk:{x:l,crv:n,kty:o}},"EC"===o&&(g.epk.y=m),j&&(g.apu=E(j)),k&&(g.apv=E(k)),"ECDH-ES"===a){h=p;break}h=d||aA(b);let q=a.slice(-6);f=await ag(q,p,h);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":h=d||aA(b),Z(c),f=await as(a,c,h);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{h=d||aA(b);let{p2c:i,p2s:j}=e;({encryptedKey:f,...g}=await ao(a,c,h,i,j));break}case"A128KW":case"A192KW":case"A256KW":h=d||aA(b),f=await ag(a,c,h);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{h=d||aA(b);let{iv:i}=e;({encryptedKey:f,...g}=await aH(a,c,h,i));break}default:throw new K('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:h,encryptedKey:f,parameters:g}},aK=(...a)=>{let b,c=a.filter(Boolean);if(0===c.length||1===c.length)return!0;for(let a of c){let c=Object.keys(a);if(!b||0===b.size){b=new Set(c);continue}for(let a of c){if(b.has(a))return!1;b.add(a)}}return!0},aL=(a,b,c,d,e)=>{let f;if(void 0!==e.crit&&d?.crit===void 0)throw new a('"crit" (Critical) Header Parameter MUST be integrity protected');if(!d||void 0===d.crit)return new Set;if(!Array.isArray(d.crit)||0===d.crit.length||d.crit.some(a=>"string"!=typeof a||0===a.length))throw new a('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let g of(f=void 0!==c?new Map([...Object.entries(c),...b.entries()]):b,d.crit)){if(!f.has(g))throw new K(`Extension Header Parameter "${g}" is not recognized`);if(void 0===e[g])throw new a(`Extension Header Parameter "${g}" is missing`);if(f.get(g)&&void 0===d[g])throw new a(`Extension Header Parameter "${g}" MUST be integrity protected`)}return new Set(d.crit)},aM=a=>a?.[Symbol.toStringTag],aN=(a,b,c)=>{if(void 0!==b.use){let a;switch(c){case"sign":case"verify":a="sig";break;case"encrypt":case"decrypt":a="enc"}if(b.use!==a)throw TypeError(`Invalid key for this operation, its "use" must be "${a}" when present`)}if(void 0!==b.alg&&b.alg!==a)throw TypeError(`Invalid key for this operation, its "alg" must be "${a}" when present`);if(Array.isArray(b.key_ops)){let d;switch(!0){case"sign"===c||"verify"===c:case"dir"===a:case a.includes("CBC-HS"):d=c;break;case a.startsWith("PBES2"):d="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(a):d=!a.includes("GCM")&&a.endsWith("KW")?"encrypt"===c?"wrapKey":"unwrapKey":c;break;case"encrypt"===c&&a.startsWith("RSA"):d="wrapKey";break;case"decrypt"===c:d=a.startsWith("RSA")?"unwrapKey":"deriveBits"}if(d&&b.key_ops?.includes?.(d)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${d}" when present`)}return!0},aO=(a,b,c)=>{a.startsWith("HS")||"dir"===a||a.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(a)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(a)?((a,b,c)=>{if(!(b instanceof Uint8Array)){if(av(b)){if(function(a){return"oct"===a.kty&&"string"==typeof a.k}(b)&&aN(a,b,c))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!aa(b))throw TypeError(Y(a,b,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==b.type)throw TypeError(`${aM(b)} instances for symmetric algorithms must be of type "secret"`)}})(a,b,c):((a,b,c)=>{if(av(b))switch(c){case"decrypt":case"sign":if(function(a){return"oct"!==a.kty&&("AKP"===a.kty&&"string"==typeof a.priv||"string"==typeof a.d)}(b)&&aN(a,b,c))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(a){return"oct"!==a.kty&&void 0===a.d&&void 0===a.priv}(b)&&aN(a,b,c))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!aa(b))throw TypeError(Y(a,b,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===b.type)throw TypeError(`${aM(b)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===b.type)switch(c){case"sign":throw TypeError(`${aM(b)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${aM(b)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===b.type)switch(c){case"verify":throw TypeError(`${aM(b)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${aM(b)} instances for asymmetric algorithm encryption must be of type "public"`)}})(a,b,c)};class aP{#a;#b;#c;#d;#e;#f;#g;#h;constructor(a){if(!(a instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this.#a=a}setKeyManagementParameters(a){if(this.#h)throw TypeError("setKeyManagementParameters can only be called once");return this.#h=a,this}setProtectedHeader(a){if(this.#b)throw TypeError("setProtectedHeader can only be called once");return this.#b=a,this}setSharedUnprotectedHeader(a){if(this.#c)throw TypeError("setSharedUnprotectedHeader can only be called once");return this.#c=a,this}setUnprotectedHeader(a){if(this.#d)throw TypeError("setUnprotectedHeader can only be called once");return this.#d=a,this}setAdditionalAuthenticatedData(a){return this.#e=a,this}setContentEncryptionKey(a){if(this.#f)throw TypeError("setContentEncryptionKey can only be called once");return this.#f=a,this}setInitializationVector(a){if(this.#g)throw TypeError("setInitializationVector can only be called once");return this.#g=a,this}async encrypt(a,b){let c,d,e,f,g;if(!this.#b&&!this.#d&&!this.#c)throw new M("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!aK(this.#b,this.#d,this.#c))throw new M("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let h={...this.#b,...this.#d,...this.#c};if(aL(M,new Map,b?.crit,this.#b,h),void 0!==h.zip)throw new K('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:i,enc:j}=h;if("string"!=typeof i||!i)throw new M('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof j||!j)throw new M('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this.#f&&("dir"===i||"ECDH-ES"===i))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${i}`);aO("dir"===i?j:i,a,"encrypt");{let e,f=await ay(a,i);({cek:d,encryptedKey:c,parameters:e}=await aJ(i,j,f,this.#f,this.#h)),e&&(b&&F in b?this.#d?this.#d={...this.#d,...e}:this.setUnprotectedHeader(e):this.#b?this.#b={...this.#b,...e}:this.setProtectedHeader(e))}f=this.#b?x.encode(E(JSON.stringify(this.#b))):x.encode(""),this.#e?(g=E(this.#e),e=z(f,x.encode("."),x.encode(g))):e=f;let{ciphertext:k,tag:l,iv:m}=await ad(j,this.#a,d,this.#g,e),n={ciphertext:E(k)};return m&&(n.iv=E(m)),l&&(n.tag=E(l)),c&&(n.encrypted_key=E(c)),g&&(n.aad=g),this.#b&&(n.protected=y.decode(f)),this.#c&&(n.unprotected=this.#c),this.#d&&(n.header=this.#d),n}}class aQ{#i;constructor(a){this.#i=new aP(a)}setContentEncryptionKey(a){return this.#i.setContentEncryptionKey(a),this}setInitializationVector(a){return this.#i.setInitializationVector(a),this}setProtectedHeader(a){return this.#i.setProtectedHeader(a),this}setKeyManagementParameters(a){return this.#i.setKeyManagementParameters(a),this}async encrypt(a,b){let c=await this.#i.encrypt(a,b);return[c.protected,c.encrypted_key,c.iv,c.ciphertext,c.tag].join(".")}}let aR=a=>Math.floor(a.getTime()/1e3),aS=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,aT=a=>{let b,c=aS.exec(a);if(!c||c[4]&&c[1])throw TypeError("Invalid time period format");let d=parseFloat(c[2]);switch(c[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":b=Math.round(d);break;case"minute":case"minutes":case"min":case"mins":case"m":b=Math.round(60*d);break;case"hour":case"hours":case"hr":case"hrs":case"h":b=Math.round(3600*d);break;case"day":case"days":case"d":b=Math.round(86400*d);break;case"week":case"weeks":case"w":b=Math.round(604800*d);break;default:b=Math.round(0x1e187e0*d)}return"-"===c[1]||"ago"===c[4]?-b:b};function aU(a,b){if(!Number.isFinite(b))throw TypeError(`Invalid ${a} input`);return b}let aV=a=>a.includes("/")?a.toLowerCase():`application/${a.toLowerCase()}`;class aW{#j;constructor(a){if(!au(a))throw TypeError("JWT Claims Set MUST be an object");this.#j=structuredClone(a)}data(){return x.encode(JSON.stringify(this.#j))}get iss(){return this.#j.iss}set iss(a){this.#j.iss=a}get sub(){return this.#j.sub}set sub(a){this.#j.sub=a}get aud(){return this.#j.aud}set aud(a){this.#j.aud=a}set jti(a){this.#j.jti=a}set nbf(a){"number"==typeof a?this.#j.nbf=aU("setNotBefore",a):a instanceof Date?this.#j.nbf=aU("setNotBefore",aR(a)):this.#j.nbf=aR(new Date)+aT(a)}set exp(a){"number"==typeof a?this.#j.exp=aU("setExpirationTime",a):a instanceof Date?this.#j.exp=aU("setExpirationTime",aR(a)):this.#j.exp=aR(new Date)+aT(a)}set iat(a){void 0===a?this.#j.iat=aR(new Date):a instanceof Date?this.#j.iat=aU("setIssuedAt",aR(a)):"string"==typeof a?this.#j.iat=aU("setIssuedAt",aR(new Date)+aT(a)):this.#j.iat=aU("setIssuedAt",a)}}class aX{#f;#g;#h;#b;#k;#l;#m;#n;constructor(a={}){this.#n=new aW(a)}setIssuer(a){return this.#n.iss=a,this}setSubject(a){return this.#n.sub=a,this}setAudience(a){return this.#n.aud=a,this}setJti(a){return this.#n.jti=a,this}setNotBefore(a){return this.#n.nbf=a,this}setExpirationTime(a){return this.#n.exp=a,this}setIssuedAt(a){return this.#n.iat=a,this}setProtectedHeader(a){if(this.#b)throw TypeError("setProtectedHeader can only be called once");return this.#b=a,this}setKeyManagementParameters(a){if(this.#h)throw TypeError("setKeyManagementParameters can only be called once");return this.#h=a,this}setContentEncryptionKey(a){if(this.#f)throw TypeError("setContentEncryptionKey can only be called once");return this.#f=a,this}setInitializationVector(a){if(this.#g)throw TypeError("setInitializationVector can only be called once");return this.#g=a,this}replicateIssuerAsHeader(){return this.#k=!0,this}replicateSubjectAsHeader(){return this.#l=!0,this}replicateAudienceAsHeader(){return this.#m=!0,this}async encrypt(a,b){let c=new aQ(this.#n.data());return this.#b&&(this.#k||this.#l||this.#m)&&(this.#b={...this.#b,iss:this.#k?this.#n.iss:void 0,sub:this.#l?this.#n.sub:void 0,aud:this.#m?this.#n.aud:void 0}),c.setProtectedHeader(this.#b),this.#g&&c.setInitializationVector(this.#g),this.#f&&c.setContentEncryptionKey(this.#f),this.#h&&c.setKeyManagementParameters(this.#h),c.encrypt(a,b)}}var aY=a.i(82600),aY=aY;let aZ=(a,b)=>{if("string"!=typeof a||!a)throw new O(`${b} missing or invalid`)};async function a$(a,b){let c,d;if(av(a))c=a;else if(aa(a))c=await aC(a);else throw TypeError(X(a,"CryptoKey","KeyObject","JSON Web Key"));if("sha256"!==(b??="sha256")&&"sha384"!==b&&"sha512"!==b)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(c.kty){case"AKP":aZ(c.alg,'"alg" (Algorithm) Parameter'),aZ(c.pub,'"pub" (Public key) Parameter'),d={alg:c.alg,kty:c.kty,pub:c.pub};break;case"EC":aZ(c.crv,'"crv" (Curve) Parameter'),aZ(c.x,'"x" (X Coordinate) Parameter'),aZ(c.y,'"y" (Y Coordinate) Parameter'),d={crv:c.crv,kty:c.kty,x:c.x,y:c.y};break;case"OKP":aZ(c.crv,'"crv" (Subtype of Key Pair) Parameter'),aZ(c.x,'"x" (Public Key) Parameter'),d={crv:c.crv,kty:c.kty,x:c.x};break;case"RSA":aZ(c.e,'"e" (Exponent) Parameter'),aZ(c.n,'"n" (Modulus) Parameter'),d={e:c.e,kty:c.kty,n:c.n};break;case"oct":aZ(c.k,'"k" (Key Value) Parameter'),d={k:c.k,kty:c.kty};break;default:throw new K('"kty" (Key Type) Parameter missing or unsupported')}let e=x.encode(JSON.stringify(d));return E(await ai(b,e))}async function a_(a,b,c){let d;if(!au(a))throw TypeError("JWK must be an object");switch(b??=a.alg,d??=c?.extractable??a.ext,a.kty){case"oct":if("string"!=typeof a.k||!a.k)throw TypeError('missing "k" (Key Value) Parameter value');return D(a.k);case"RSA":if("oth"in a&&void 0!==a.oth)throw new K('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');return aw({...a,alg:b,ext:d});case"AKP":if("string"!=typeof a.alg||!a.alg)throw TypeError('missing "alg" (Algorithm) Parameter value');if(void 0!==b&&b!==a.alg)throw TypeError("JWK alg and alg option value mismatch");return aw({...a,ext:d});case"EC":case"OKP":return aw({...a,alg:b,ext:d});default:throw new K('Unsupported "kty" (Key Type) Parameter value')}}let a0=async(a,b,c,d,e)=>{switch(a){case"dir":if(void 0!==c)throw new M("Encountered unexpected JWE Encrypted Key");return b;case"ECDH-ES":if(void 0!==c)throw new M("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let e,f;if(!au(d.epk))throw new M('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(Z(b),!am(b))throw new K("ECDH with the provided key is not allowed or not supported by your javascript runtime");let g=await a_(d.epk,a);if(Z(g),void 0!==d.apu){if("string"!=typeof d.apu)throw new M('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{e=D(d.apu)}catch{throw new M("Failed to base64url decode the apu")}}if(void 0!==d.apv){if("string"!=typeof d.apv)throw new M('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{f=D(d.apv)}catch{throw new M("Failed to base64url decode the apv")}}let h=await al(g,b,"ECDH-ES"===a?d.enc:a,"ECDH-ES"===a?az(d.enc):parseInt(a.slice(-5,-2),10),e,f);if("ECDH-ES"===a)return h;if(void 0===c)throw new M("JWE Encrypted Key missing");return ah(a.slice(-6),h,c)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===c)throw new M("JWE Encrypted Key missing");return Z(b),at(a,b,c);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let f;if(void 0===c)throw new M("JWE Encrypted Key missing");if("number"!=typeof d.p2c)throw new M('JOSE Header "p2c" (PBES2 Count) missing or invalid');let g=e?.maxPBES2Count||1e4;if(d.p2c>g)throw new M('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof d.p2s)throw new M('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{f=D(d.p2s)}catch{throw new M("Failed to base64url decode the p2s")}return ap(a,b,c,d.p2c,f)}case"A128KW":case"A192KW":case"A256KW":if(void 0===c)throw new M("JWE Encrypted Key missing");return ah(a,b,c);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let e,f;if(void 0===c)throw new M("JWE Encrypted Key missing");if("string"!=typeof d.iv)throw new M('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof d.tag)throw new M('JOSE Header "tag" (Authentication Tag) missing or invalid');try{e=D(d.iv)}catch{throw new M("Failed to base64url decode the iv")}try{f=D(d.tag)}catch{throw new M("Failed to base64url decode the tag")}return aI(a,b,c,e,f)}default:throw new K('Invalid or unsupported "alg" (JWE Algorithm) header value')}},a1=(a,b)=>{if(void 0!==b&&(!Array.isArray(b)||b.some(a=>"string"!=typeof a)))throw TypeError(`"${a}" option must be an array of strings`);if(b)return new Set(b)};async function a2(a,b,c){let d,e,f,g,h,i,j;if(!au(a))throw new M("Flattened JWE must be an object");if(void 0===a.protected&&void 0===a.header&&void 0===a.unprotected)throw new M("JOSE Header missing");if(void 0!==a.iv&&"string"!=typeof a.iv)throw new M("JWE Initialization Vector incorrect type");if("string"!=typeof a.ciphertext)throw new M("JWE Ciphertext missing or incorrect type");if(void 0!==a.tag&&"string"!=typeof a.tag)throw new M("JWE Authentication Tag incorrect type");if(void 0!==a.protected&&"string"!=typeof a.protected)throw new M("JWE Protected Header incorrect type");if(void 0!==a.encrypted_key&&"string"!=typeof a.encrypted_key)throw new M("JWE Encrypted Key incorrect type");if(void 0!==a.aad&&"string"!=typeof a.aad)throw new M("JWE AAD incorrect type");if(void 0!==a.header&&!au(a.header))throw new M("JWE Shared Unprotected Header incorrect type");if(void 0!==a.unprotected&&!au(a.unprotected))throw new M("JWE Per-Recipient Unprotected Header incorrect type");if(a.protected)try{let b=D(a.protected);d=JSON.parse(y.decode(b))}catch{throw new M("JWE Protected Header is invalid")}if(!aK(d,a.header,a.unprotected))throw new M("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let k={...d,...a.header,...a.unprotected};if(aL(M,new Map,c?.crit,d,k),void 0!==k.zip)throw new K('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:l,enc:m}=k;if("string"!=typeof l||!l)throw new M("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof m||!m)throw new M("missing JWE Encryption Algorithm (enc) in JWE Header");let n=c&&a1("keyManagementAlgorithms",c.keyManagementAlgorithms),o=c&&a1("contentEncryptionAlgorithms",c.contentEncryptionAlgorithms);if(n&&!n.has(l)||!n&&l.startsWith("PBES2"))throw new J('"alg" (Algorithm) Header Parameter value not allowed');if(o&&!o.has(m))throw new J('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==a.encrypted_key)try{e=D(a.encrypted_key)}catch{throw new M("Failed to base64url decode the encrypted_key")}let p=!1;"function"==typeof b&&(b=await b(d,a),p=!0),aO("dir"===l?m:l,b,"decrypt");let q=await ay(b,l);try{f=await a0(l,q,e,k,c)}catch(a){if(a instanceof TypeError||a instanceof M||a instanceof K)throw a;f=aA(m)}if(void 0!==a.iv)try{g=D(a.iv)}catch{throw new M("Failed to base64url decode the iv")}if(void 0!==a.tag)try{h=D(a.tag)}catch{throw new M("Failed to base64url decode the tag")}let r=x.encode(a.protected??"");i=void 0!==a.aad?z(r,x.encode("."),x.encode(a.aad)):r;try{j=D(a.ciphertext)}catch{throw new M("Failed to base64url decode the ciphertext")}let s={plaintext:await aG(m,f,j,g,h,i)};if(void 0!==a.protected&&(s.protectedHeader=d),void 0!==a.aad)try{s.additionalAuthenticatedData=D(a.aad)}catch{throw new M("Failed to base64url decode the aad")}return(void 0!==a.unprotected&&(s.sharedUnprotectedHeader=a.unprotected),void 0!==a.header&&(s.unprotectedHeader=a.header),p)?{...s,key:q}:s}async function a3(a,b,c){if(a instanceof Uint8Array&&(a=y.decode(a)),"string"!=typeof a)throw new M("Compact JWE must be a string or Uint8Array");let{0:d,1:e,2:f,3:g,4:h,length:i}=a.split(".");if(5!==i)throw new M("Invalid Compact JWE");let j=await a2({ciphertext:g,iv:f||void 0,protected:d,tag:h||void 0,encrypted_key:e||void 0},b,c),k={plaintext:j.plaintext,protectedHeader:j.protectedHeader};return"function"==typeof b?{...k,key:j.key}:k}async function a4(a,b,c){let d=await a3(a,b,c),e=function(a,b,c={}){var d,e;let f,g;try{f=JSON.parse(y.decode(b))}catch{}if(!au(f))throw new N("JWT Claims Set must be a top-level JSON object");let{typ:h}=c;if(h&&("string"!=typeof a.typ||aV(a.typ)!==aV(h)))throw new H('unexpected "typ" JWT header value',f,"typ","check_failed");let{requiredClaims:i=[],issuer:j,subject:k,audience:l,maxTokenAge:m}=c,n=[...i];for(let a of(void 0!==m&&n.push("iat"),void 0!==l&&n.push("aud"),void 0!==k&&n.push("sub"),void 0!==j&&n.push("iss"),new Set(n.reverse())))if(!(a in f))throw new H(`missing required "${a}" claim`,f,a,"missing");if(j&&!(Array.isArray(j)?j:[j]).includes(f.iss))throw new H('unexpected "iss" claim value',f,"iss","check_failed");if(k&&f.sub!==k)throw new H('unexpected "sub" claim value',f,"sub","check_failed");if(l&&(d=f.aud,e="string"==typeof l?[l]:l,"string"==typeof d?!e.includes(d):!(Array.isArray(d)&&e.some(Set.prototype.has.bind(new Set(d))))))throw new H('unexpected "aud" claim value',f,"aud","check_failed");switch(typeof c.clockTolerance){case"string":g=aT(c.clockTolerance);break;case"number":g=c.clockTolerance;break;case"undefined":g=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:o}=c,p=aR(o||new Date);if((void 0!==f.iat||m)&&"number"!=typeof f.iat)throw new H('"iat" claim must be a number',f,"iat","invalid");if(void 0!==f.nbf){if("number"!=typeof f.nbf)throw new H('"nbf" claim must be a number',f,"nbf","invalid");if(f.nbf>p+g)throw new H('"nbf" claim timestamp check failed',f,"nbf","check_failed")}if(void 0!==f.exp){if("number"!=typeof f.exp)throw new H('"exp" claim must be a number',f,"exp","invalid");if(f.exp<=p-g)throw new I('"exp" claim timestamp check failed',f,"exp","check_failed")}if(m){let a=p-f.iat;if(a-g>("number"==typeof m?m:aT(m)))throw new I('"iat" claim timestamp check failed (too far in the past)',f,"iat","check_failed");if(a<0-g)throw new H('"iat" claim timestamp check failed (it should be in the past)',f,"iat","check_failed")}return f}(d.protectedHeader,d.plaintext,c),{protectedHeader:f}=d;if(void 0!==f.iss&&f.iss!==e.iss)throw new H('replicated "iss" claim header parameter mismatch',e,"iss","mismatch");if(void 0!==f.sub&&f.sub!==e.sub)throw new H('replicated "sub" claim header parameter mismatch',e,"sub","mismatch");if(void 0!==f.aud&&JSON.stringify(f.aud)!==JSON.stringify(e.aud))throw new H('replicated "aud" claim header parameter mismatch',e,"aud","mismatch");let g={payload:e,protectedHeader:f};return"function"==typeof b?{...g,key:d.key}:g}a.s(["parse",()=>bb,"serialize",()=>be],69641);let a5=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,a6=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,a7=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,a8=/^[\u0020-\u003A\u003D-\u007E]*$/,a9=Object.prototype.toString,ba=(()=>{let a=function(){};return a.prototype=Object.create(null),a})();function bb(a,b){let c=new ba,d=a.length;if(d<2)return c;let e=b?.decode||bf,f=0;do{let b=a.indexOf("=",f);if(-1===b)break;let g=a.indexOf(";",f),h=-1===g?d:g;if(b>h){f=a.lastIndexOf(";",b-1)+1;continue}let i=bc(a,f,b),j=bd(a,b,i),k=a.slice(i,j);if(void 0===c[k]){let d=bc(a,b+1,h),f=bd(a,h,d),g=e(a.slice(d,f));c[k]=g}f=h+1}while(f<d)return c}function bc(a,b,c){do{let c=a.charCodeAt(b);if(32!==c&&9!==c)return b}while(++b<c)return c}function bd(a,b,c){for(;b>c;){let c=a.charCodeAt(--b);if(32!==c&&9!==c)return b+1}return c}function be(a,b,c){let d=c?.encode||encodeURIComponent;if(!a5.test(a))throw TypeError(`argument name is invalid: ${a}`);let e=d(b);if(!a6.test(e))throw TypeError(`argument val is invalid: ${b}`);let f=a+"="+e;if(!c)return f;if(void 0!==c.maxAge){if(!Number.isInteger(c.maxAge))throw TypeError(`option maxAge is invalid: ${c.maxAge}`);f+="; Max-Age="+c.maxAge}if(c.domain){if(!a7.test(c.domain))throw TypeError(`option domain is invalid: ${c.domain}`);f+="; Domain="+c.domain}if(c.path){if(!a8.test(c.path))throw TypeError(`option path is invalid: ${c.path}`);f+="; Path="+c.path}if(c.expires){var g;if(g=c.expires,"[object Date]"!==a9.call(g)||!Number.isFinite(c.expires.valueOf()))throw TypeError(`option expires is invalid: ${c.expires}`);f+="; Expires="+c.expires.toUTCString()}if(c.httpOnly&&(f+="; HttpOnly"),c.secure&&(f+="; Secure"),c.partitioned&&(f+="; Partitioned"),c.priority)switch("string"==typeof c.priority?c.priority.toLowerCase():void 0){case"low":f+="; Priority=Low";break;case"medium":f+="; Priority=Medium";break;case"high":f+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${c.priority}`)}if(c.sameSite)switch("string"==typeof c.sameSite?c.sameSite.toLowerCase():c.sameSite){case!0:case"strict":f+="; SameSite=Strict";break;case"lax":f+="; SameSite=Lax";break;case"none":f+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${c.sameSite}`)}return f}function bf(a){if(-1===a.indexOf("%"))return a;try{return decodeURIComponent(a)}catch(b){return a}}var bg=a.i(69641);let{parse:bh}=bg,bi="A256CBC-HS512";async function bj(a){let{token:b={},secret:c,maxAge:d=2592e3,salt:e}=a,f=Array.isArray(c)?c:[c],g=await bl(bi,f[0],e),h=await a$({kty:"oct",k:aY.encode(g)},`sha${g.byteLength<<3}`);return await new aX(b).setProtectedHeader({alg:"dir",enc:bi,kid:h}).setIssuedAt().setExpirationTime((Date.now()/1e3|0)+d).setJti(crypto.randomUUID()).encrypt(g)}async function bk(a){let{token:b,secret:c,salt:d}=a,e=Array.isArray(c)?c:[c];if(!b)return null;let{payload:f}=await a4(b,async({kid:a,enc:b})=>{for(let c of e){let e=await bl(b,c,d);if(void 0===a||a===await a$({kty:"oct",k:aY.encode(e)},`sha${e.byteLength<<3}`))return e}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[bi,"A256GCM"]});return f}async function bl(a,b,c){let d;switch(a){case"A256CBC-HS512":d=64;break;case"A256GCM":d=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await w("sha256",b,c,`Auth.js Generated Encryption Key (${c})`,d)}async function bm({options:a,paramValue:b,cookieValue:c}){let{url:d,callbacks:e}=a,f=d.origin;return b?f=await e.redirect({url:b,baseUrl:d.origin}):c&&(f=await e.redirect({url:c,baseUrl:d.origin})),{callbackUrl:f,callbackUrlCookie:f!==c?f:void 0}}let bn="\x1b[31m",bo="\x1b[0m",bp={error(a){let b=a instanceof k.AuthError?a.type:a.name;if(console.error(`${bn}[auth][error]${bo} ${b}: ${a.message}`),a.cause&&"object"==typeof a.cause&&"err"in a.cause&&a.cause.err instanceof Error){let{err:b,...c}=a.cause;console.error(`${bn}[auth][cause]${bo}:`,b.stack),c&&console.error(`${bn}[auth][details]${bo}:`,JSON.stringify(c,null,2))}else a.stack&&console.error(a.stack.replace(/.*/,"").substring(1))},warn(a){console.warn(`\x1b[33m[auth][warn][${a}]${bo}`,"Read more: https://warnings.authjs.dev")},debug(a,b){console.log(`\x1b[90m[auth][debug]:${bo} ${a}`,JSON.stringify(b,null,2))}};function bq(a){let b={...bp};return a.debug||(b.debug=()=>{}),a.logger?.error&&(b.error=a.logger.error),a.logger?.warn&&(b.warn=a.logger.warn),a.logger?.debug&&(b.debug=a.logger.debug),a.logger??(a.logger=b),b}let br=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{parse:bs,serialize:bt}=bg;async function bu(a){if(!("body"in a)||!a.body||"POST"!==a.method)return;let b=a.headers.get("content-type");return b?.includes("application/json")?await a.json():b?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await a.text())):void 0}async function bv(a,b){try{if("GET"!==a.method&&"POST"!==a.method)throw new k.UnknownAction("Only GET and POST requests are supported");b.basePath??(b.basePath="/auth");let c=new URL(a.url),{action:d,providerId:e}=function(a,b){let c=a.match(RegExp(`^${b}(.+)`));if(null===c)throw new k.UnknownAction(`Cannot parse action at ${a}`);let d=c.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==d.length&&2!==d.length)throw new k.UnknownAction(`Cannot parse action at ${a}`);let[e,f]=d;if(!br.includes(e)||f&&!["signin","callback","webauthn-options"].includes(e))throw new k.UnknownAction(`Cannot parse action at ${a}`);return{action:e,providerId:"undefined"==f?void 0:f}}(c.pathname,b.basePath);return{url:c,action:d,providerId:e,method:a.method,headers:Object.fromEntries(a.headers),body:a.body?await bu(a):void 0,cookies:bs(a.headers.get("cookie")??"")??{},error:c.searchParams.get("error")??void 0,query:Object.fromEntries(c.searchParams)}}catch(d){let c=bq(b);c.error(d),c.debug("request",a)}}function bw(a){let b=new Headers(a.headers);a.cookies?.forEach(a=>{let{name:c,value:d,options:e}=a,f=bt(c,d,e);b.has("Set-Cookie")?b.append("Set-Cookie",f):b.set("Set-Cookie",f)});let c=a.body;"application/json"===b.get("content-type")?c=JSON.stringify(a.body):"application/x-www-form-urlencoded"===b.get("content-type")&&(c=new URLSearchParams(a.body).toString());let d=new Response(c,{headers:b,status:a.redirect?302:a.status??200});return a.redirect&&d.headers.set("Location",a.redirect),d}async function bx(a){let b=new TextEncoder().encode(a);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",b))).map(a=>a.toString(16).padStart(2,"0")).join("").toString()}function by(a){return Array.from(crypto.getRandomValues(new Uint8Array(a))).reduce((a,b)=>a+("0"+b.toString(16)).slice(-2),"")}async function bz({options:a,cookieValue:b,isPost:c,bodyValue:d}){if(b){let[e,f]=b.split("|");if(f===await bx(`${e}${a.secret}`))return{csrfTokenVerified:c&&e===d,csrfToken:e}}let e=by(32),f=await bx(`${e}${a.secret}`);return{cookie:`${e}|${f}`,csrfToken:e}}function bA(a,b){if(!b)throw new k.MissingCSRF(`CSRF token was missing during an action ${a}`)}function bB(a){return null!==a&&"object"==typeof a}function bC(a,...b){if(!b.length)return a;let c=b.shift();if(bB(a)&&bB(c))for(let b in c)bB(c[b])?(bB(a[b])||(a[b]=Array.isArray(c[b])?[]:{}),bC(a[b],c[b])):void 0!==c[b]&&(a[b]=c[b]);return bC(a,...b)}let bD=Symbol("skip-csrf-check"),bE=Symbol("return-type-raw"),bF=Symbol("custom-fetch"),bG=Symbol("conform-internal"),bH=a=>bJ({id:a.sub??a.id??crypto.randomUUID(),name:a.name??a.nickname??a.preferred_username,email:a.email,image:a.picture}),bI=a=>bJ({access_token:a.access_token,id_token:a.id_token,refresh_token:a.refresh_token,expires_at:a.expires_at,scope:a.scope,token_type:a.token_type,session_state:a.session_state});function bJ(a){let b={};for(let[c,d]of Object.entries(a))void 0!==d&&(b[c]=d);return b}function bK(a,b){if(!a&&b)return;if("string"==typeof a)return{url:new URL(a)};let c=new URL(a?.url??"https://authjs.dev");if(a?.params!=null)for(let[b,d]of Object.entries(a.params))"claims"===b&&(d=JSON.stringify(d)),c.searchParams.set(b,String(d));return{url:c,request:a?.request,conform:a?.conform,...a?.clientPrivateKey?{clientPrivateKey:a?.clientPrivateKey}:null}}let bL={signIn:()=>!0,redirect:({url:a,baseUrl:b})=>a.startsWith("/")?`${b}${a}`:new URL(a).origin===b?a:b,session:({session:a})=>({user:{name:a.user?.name,email:a.user?.email,image:a.user?.image},expires:a.expires?.toISOString?.()??a.expires}),jwt:({token:a})=>a};async function bM({authOptions:a,providerId:b,action:c,url:d,cookies:e,callbackUrl:f,csrfToken:g,csrfDisabled:h,isPost:j}){var l,m;let n=bq(a),{providers:o,provider:p}=function(a){let{providerId:b,config:c}=a,d=new URL(c.basePath??"/auth",a.url.origin),e=c.providers.map(a=>{let b="function"==typeof a?a():a,{options:e,...f}=b,g=e?.id??f.id,h=bC(f,e,{signinUrl:`${d}/signin/${g}`,callbackUrl:`${d}/callback/${g}`});if("oauth"===b.type||"oidc"===b.type){h.redirectProxyUrl??(h.redirectProxyUrl=e?.redirectProxyUrl??c.redirectProxyUrl);let a=function(a){a.issuer&&(a.wellKnown??(a.wellKnown=`${a.issuer}/.well-known/openid-configuration`));let b=bK(a.authorization,a.issuer);b&&!b.url?.searchParams.has("scope")&&b.url.searchParams.set("scope","openid profile email");let c=bK(a.token,a.issuer),d=bK(a.userinfo,a.issuer),e=a.checks??["pkce"];return a.redirectProxyUrl&&(e.includes("state")||e.push("state"),a.redirectProxyUrl=`${a.redirectProxyUrl}/callback/${a.id}`),{...a,authorization:b,token:c,checks:e,userinfo:d,profile:a.profile??bH,account:a.account??bI}}(h);return a.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete a.redirectProxyUrl,a[bF]??(a[bF]=e?.[bF]),a}return h}),f=e.find(({id:a})=>a===b);if(b&&!f){let a=e.map(a=>a.id).join(", ");throw Error(`Provider with id "${b}" not found. Available providers: [${a}].`)}return{providers:e,provider:f}}({url:d,providerId:b,config:a}),q=!1;if((p?.type==="oauth"||p?.type==="oidc")&&p.redirectProxyUrl)try{q=new URL(p.redirectProxyUrl).origin===d.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${p.redirectProxyUrl}`)}let r={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...a,url:d,action:c,provider:p,cookies:bC(i(a.useSecureCookies??"https:"===d.protocol),a.cookies),providers:o,session:{strategy:a.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...a.session},jwt:{secret:a.secret,maxAge:a.session?.maxAge??2592e3,encode:bj,decode:bk,...a.jwt},events:(l=a.events??{},m=n,Object.keys(l).reduce((a,b)=>(a[b]=async(...a)=>{try{let c=l[b];return await c(...a)}catch(a){m.error(new k.EventError(a))}},a),{})),adapter:function(a,b){if(a)return Object.keys(a).reduce((c,d)=>(c[d]=async(...c)=>{try{b.debug(`adapter_${d}`,{args:c});let e=a[d];return await e(...c)}catch(c){let a=new k.AdapterError(c);throw b.error(a),a}},c),{})}(a.adapter,n),callbacks:{...bL,...a.callbacks},logger:n,callbackUrl:d.origin,isOnRedirectProxy:q,experimental:{...a.experimental}},s=[];if(h)r.csrfTokenVerified=!0;else{let{csrfToken:a,cookie:b,csrfTokenVerified:c}=await bz({options:r,cookieValue:e?.[r.cookies.csrfToken.name],isPost:j,bodyValue:g});r.csrfToken=a,r.csrfTokenVerified=c,b&&s.push({name:r.cookies.csrfToken.name,value:b,options:r.cookies.csrfToken.options})}let{callbackUrl:t,callbackUrlCookie:u}=await bm({options:r,cookieValue:e?.[r.cookies.callbackUrl.name],paramValue:f});return r.callbackUrl=t,u&&s.push({name:r.cookies.callbackUrl.name,value:u,options:r.cookies.callbackUrl.options}),{options:r,cookies:s}}var bN,bO,bP,bQ,bR,bS,bT,bU,bV,bW,bX,bY,bZ,b$,b_,b0,b1={},b2=[],b3=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,b4=Array.isArray;function b5(a,b){for(var c in b)a[c]=b[c];return a}function b6(a){a&&a.parentNode&&a.parentNode.removeChild(a)}function b7(a,b,c,d,e){var f={type:a,props:b,key:c,ref:d,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==e?++bV:e,__i:-1,__u:0};return null==e&&null!=bU.vnode&&bU.vnode(f),f}function b8(a){return a.children}function b9(a,b){this.props=a,this.context=b}function ca(a,b){if(null==b)return a.__?ca(a.__,a.__i+1):null;for(var c;b<a.__k.length;b++)if(null!=(c=a.__k[b])&&null!=c.__e)return c.__e;return"function"==typeof a.type?ca(a):null}function cb(a){(!a.__d&&(a.__d=!0)&&bW.push(a)&&!cc.__r++||bX!==bU.debounceRendering)&&((bX=bU.debounceRendering)||bY)(cc)}function cc(){var a,b,c,d,e,f,g,h;for(bW.sort(bZ);a=bW.shift();)a.__d&&(b=bW.length,d=void 0,f=(e=(c=a).__v).__e,g=[],h=[],c.__P&&((d=b5({},e)).__v=e.__v+1,bU.vnode&&bU.vnode(d),ch(c.__P,d,e,c.__n,c.__P.namespaceURI,32&e.__u?[f]:null,g,null==f?ca(e):f,!!(32&e.__u),h),d.__v=e.__v,d.__.__k[d.__i]=d,function(a,b,c){b.__d=void 0;for(var d=0;d<c.length;d++)ci(c[d],c[++d],c[++d]);bU.__c&&bU.__c(b,a),a.some(function(b){try{a=b.__h,b.__h=[],a.some(function(a){a.call(b)})}catch(a){bU.__e(a,b.__v)}})}(g,d,h),d.__e!=f&&function a(b){var c,d;if(null!=(b=b.__)&&null!=b.__c){for(b.__e=b.__c.base=null,c=0;c<b.__k.length;c++)if(null!=(d=b.__k[c])&&null!=d.__e){b.__e=b.__c.base=d.__e;break}return a(b)}}(d)),bW.length>b&&bW.sort(bZ));cc.__r=0}function cd(a,b,c,d,e,f,g,h,i,j,k){var l,m,n,o,p,q=d&&d.__k||b2,r=b.length;for(c.__d=i,function(a,b,c){var d,e,f,g,h,i=b.length,j=c.length,k=j,l=0;for(a.__k=[],d=0;d<i;d++)null!=(e=b[d])&&"boolean"!=typeof e&&"function"!=typeof e?(g=d+l,(e=a.__k[d]="string"==typeof e||"number"==typeof e||"bigint"==typeof e||e.constructor==String?b7(null,e,null,null,null):b4(e)?b7(b8,{children:e},null,null,null):void 0===e.constructor&&e.__b>0?b7(e.type,e.props,e.key,e.ref?e.ref:null,e.__v):e).__=a,e.__b=a.__b+1,f=null,-1!==(h=e.__i=function(a,b,c,d){var e=a.key,f=a.type,g=c-1,h=c+1,i=b[c];if(null===i||i&&e==i.key&&f===i.type&&0==(131072&i.__u))return c;if(d>+(null!=i&&0==(131072&i.__u)))for(;g>=0||h<b.length;){if(g>=0){if((i=b[g])&&0==(131072&i.__u)&&e==i.key&&f===i.type)return g;g--}if(h<b.length){if((i=b[h])&&0==(131072&i.__u)&&e==i.key&&f===i.type)return h;h++}}return -1}(e,c,g,k))&&(k--,(f=c[h])&&(f.__u|=131072)),null==f||null===f.__v?(-1==h&&l--,"function"!=typeof e.type&&(e.__u|=65536)):h!==g&&(h==g-1?l--:h==g+1?l++:(h>g?l--:l++,e.__u|=65536))):e=a.__k[d]=null;if(k)for(d=0;d<j;d++)null!=(f=c[d])&&0==(131072&f.__u)&&(f.__e==a.__d&&(a.__d=ca(f)),function a(b,c,d){var e,f;if(bU.unmount&&bU.unmount(b),(e=b.ref)&&(e.current&&e.current!==b.__e||ci(e,null,c)),null!=(e=b.__c)){if(e.componentWillUnmount)try{e.componentWillUnmount()}catch(a){bU.__e(a,c)}e.base=e.__P=null}if(e=b.__k)for(f=0;f<e.length;f++)e[f]&&a(e[f],c,d||"function"!=typeof b.type);d||b6(b.__e),b.__c=b.__=b.__e=b.__d=void 0}(f,f))}(c,b,q),i=c.__d,l=0;l<r;l++)null!=(n=c.__k[l])&&(m=-1===n.__i?b1:q[n.__i]||b1,n.__i=l,ch(a,n,m,e,f,g,h,i,j,k),o=n.__e,n.ref&&m.ref!=n.ref&&(m.ref&&ci(m.ref,null,n),k.push(n.ref,n.__c||o,n)),null==p&&null!=o&&(p=o),65536&n.__u||m.__k===n.__k?i=function a(b,c,d){var e,f;if("function"==typeof b.type){for(e=b.__k,f=0;e&&f<e.length;f++)e[f]&&(e[f].__=b,c=a(e[f],c,d));return c}b.__e!=c&&(c&&b.type&&!d.contains(c)&&(c=ca(b)),d.insertBefore(b.__e,c||null),c=b.__e);do c=c&&c.nextSibling;while(null!=c&&8===c.nodeType)return c}(n,i,a):"function"==typeof n.type&&void 0!==n.__d?i=n.__d:o&&(i=o.nextSibling),n.__d=void 0,n.__u&=-196609);c.__d=i,c.__e=p}function ce(a,b,c){"-"===b[0]?a.setProperty(b,null==c?"":c):a[b]=null==c?"":"number"!=typeof c||b3.test(b)?c:c+"px"}function cf(a,b,c,d,e){var f;a:if("style"===b)if("string"==typeof c)a.style.cssText=c;else{if("string"==typeof d&&(a.style.cssText=d=""),d)for(b in d)c&&b in c||ce(a.style,b,"");if(c)for(b in c)d&&c[b]===d[b]||ce(a.style,b,c[b])}else if("o"===b[0]&&"n"===b[1])f=b!==(b=b.replace(/(PointerCapture)$|Capture$/i,"$1")),b=b.toLowerCase()in a||"onFocusOut"===b||"onFocusIn"===b?b.toLowerCase().slice(2):b.slice(2),a.l||(a.l={}),a.l[b+f]=c,c?d?c.u=d.u:(c.u=b$,a.addEventListener(b,f?b0:b_,f)):a.removeEventListener(b,f?b0:b_,f);else{if("http://www.w3.org/2000/svg"==e)b=b.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=b&&"height"!=b&&"href"!=b&&"list"!=b&&"form"!=b&&"tabIndex"!=b&&"download"!=b&&"rowSpan"!=b&&"colSpan"!=b&&"role"!=b&&"popover"!=b&&b in a)try{a[b]=null==c?"":c;break a}catch(a){}"function"==typeof c||(null==c||!1===c&&"-"!==b[4]?a.removeAttribute(b):a.setAttribute(b,"popover"==b&&1==c?"":c))}}function cg(a){return function(b){if(this.l){var c=this.l[b.type+a];if(null==b.t)b.t=b$++;else if(b.t<c.u)return;return c(bU.event?bU.event(b):b)}}}function ch(a,b,c,d,e,f,g,h,i,j){var k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A=b.type;if(void 0!==b.constructor)return null;128&c.__u&&(i=!!(32&c.__u),f=[h=b.__e=c.__e]),(k=bU.__b)&&k(b);a:if("function"==typeof A)try{if(r=b.props,s="prototype"in A&&A.prototype.render,t=(k=A.contextType)&&d[k.__c],u=k?t?t.props.value:k.__:d,c.__c?q=(l=b.__c=c.__c).__=l.__E:(s?b.__c=l=new A(r,u):(b.__c=l=new b9(r,u),l.constructor=A,l.render=cj),t&&t.sub(l),l.props=r,l.state||(l.state={}),l.context=u,l.__n=d,m=l.__d=!0,l.__h=[],l._sb=[]),s&&null==l.__s&&(l.__s=l.state),s&&null!=A.getDerivedStateFromProps&&(l.__s==l.state&&(l.__s=b5({},l.__s)),b5(l.__s,A.getDerivedStateFromProps(r,l.__s))),n=l.props,o=l.state,l.__v=b,m)s&&null==A.getDerivedStateFromProps&&null!=l.componentWillMount&&l.componentWillMount(),s&&null!=l.componentDidMount&&l.__h.push(l.componentDidMount);else{if(s&&null==A.getDerivedStateFromProps&&r!==n&&null!=l.componentWillReceiveProps&&l.componentWillReceiveProps(r,u),!l.__e&&(null!=l.shouldComponentUpdate&&!1===l.shouldComponentUpdate(r,l.__s,u)||b.__v===c.__v)){for(b.__v!==c.__v&&(l.props=r,l.state=l.__s,l.__d=!1),b.__e=c.__e,b.__k=c.__k,b.__k.some(function(a){a&&(a.__=b)}),v=0;v<l._sb.length;v++)l.__h.push(l._sb[v]);l._sb=[],l.__h.length&&g.push(l);break a}null!=l.componentWillUpdate&&l.componentWillUpdate(r,l.__s,u),s&&null!=l.componentDidUpdate&&l.__h.push(function(){l.componentDidUpdate(n,o,p)})}if(l.context=u,l.props=r,l.__P=a,l.__e=!1,w=bU.__r,x=0,s){for(l.state=l.__s,l.__d=!1,w&&w(b),k=l.render(l.props,l.state,l.context),y=0;y<l._sb.length;y++)l.__h.push(l._sb[y]);l._sb=[]}else do l.__d=!1,w&&w(b),k=l.render(l.props,l.state,l.context),l.state=l.__s;while(l.__d&&++x<25)l.state=l.__s,null!=l.getChildContext&&(d=b5(b5({},d),l.getChildContext())),s&&!m&&null!=l.getSnapshotBeforeUpdate&&(p=l.getSnapshotBeforeUpdate(n,o)),cd(a,b4(z=null!=k&&k.type===b8&&null==k.key?k.props.children:k)?z:[z],b,c,d,e,f,g,h,i,j),l.base=b.__e,b.__u&=-161,l.__h.length&&g.push(l),q&&(l.__E=l.__=null)}catch(a){if(b.__v=null,i||null!=f){for(b.__u|=i?160:128;h&&8===h.nodeType&&h.nextSibling;)h=h.nextSibling;f[f.indexOf(h)]=null,b.__e=h}else b.__e=c.__e,b.__k=c.__k;bU.__e(a,b,c)}else null==f&&b.__v===c.__v?(b.__k=c.__k,b.__e=c.__e):b.__e=function(a,b,c,d,e,f,g,h,i){var j,k,l,m,n,o,p,q=c.props,r=b.props,s=b.type;if("svg"===s?e="http://www.w3.org/2000/svg":"math"===s?e="http://www.w3.org/1998/Math/MathML":e||(e="http://www.w3.org/1999/xhtml"),null!=f){for(j=0;j<f.length;j++)if((n=f[j])&&"setAttribute"in n==!!s&&(s?n.localName===s:3===n.nodeType)){a=n,f[j]=null;break}}if(null==a){if(null===s)return document.createTextNode(r);a=document.createElementNS(e,s,r.is&&r),h&&(bU.__m&&bU.__m(b,f),h=!1),f=null}if(null===s)q===r||h&&a.data===r||(a.data=r);else{if(f=f&&bT.call(a.childNodes),q=c.props||b1,!h&&null!=f)for(q={},j=0;j<a.attributes.length;j++)q[(n=a.attributes[j]).name]=n.value;for(j in q)if(n=q[j],"children"==j);else if("dangerouslySetInnerHTML"==j)l=n;else if(!(j in r)){if("value"==j&&"defaultValue"in r||"checked"==j&&"defaultChecked"in r)continue;cf(a,j,null,n,e)}for(j in r)n=r[j],"children"==j?m=n:"dangerouslySetInnerHTML"==j?k=n:"value"==j?o=n:"checked"==j?p=n:h&&"function"!=typeof n||q[j]===n||cf(a,j,n,q[j],e);if(k)h||l&&(k.__html===l.__html||k.__html===a.innerHTML)||(a.innerHTML=k.__html),b.__k=[];else if(l&&(a.innerHTML=""),cd(a,b4(m)?m:[m],b,c,d,"foreignObject"===s?"http://www.w3.org/1999/xhtml":e,f,g,f?f[0]:c.__k&&ca(c,0),h,i),null!=f)for(j=f.length;j--;)b6(f[j]);h||(j="value","progress"===s&&null==o?a.removeAttribute("value"):void 0===o||o===a[j]&&("progress"!==s||o)&&("option"!==s||o===q[j])||cf(a,j,o,q[j],e),j="checked",void 0!==p&&p!==a[j]&&cf(a,j,p,q[j],e))}return a}(c.__e,b,c,d,e,f,g,i,j);(k=bU.diffed)&&k(b)}function ci(a,b,c){try{if("function"==typeof a){var d="function"==typeof a.__u;d&&a.__u(),d&&null==b||(a.__u=a(b))}else a.current=b}catch(a){bU.__e(a,c)}}function cj(a,b,c){return this.constructor(a,c)}bT=b2.slice,bU={__e:function(a,b,c,d){for(var e,f,g;b=b.__;)if((e=b.__c)&&!e.__)try{if((f=e.constructor)&&null!=f.getDerivedStateFromError&&(e.setState(f.getDerivedStateFromError(a)),g=e.__d),null!=e.componentDidCatch&&(e.componentDidCatch(a,d||{}),g=e.__d),g)return e.__E=e}catch(b){a=b}throw a}},bV=0,b9.prototype.setState=function(a,b){var c;c=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=b5({},this.state),"function"==typeof a&&(a=a(b5({},c),this.props)),a&&b5(c,a),null!=a&&this.__v&&(b&&this._sb.push(b),cb(this))},b9.prototype.forceUpdate=function(a){this.__v&&(this.__e=!0,a&&this.__h.push(a),cb(this))},b9.prototype.render=b8,bW=[],bY="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,bZ=function(a,b){return a.__v.__b-b.__v.__b},cc.__r=0,b$=0,b_=cg(!1),b0=cg(!0);var ck=/[\s\n\\/='"\0<>]/,cl=/^(xlink|xmlns|xml)([A-Z])/,cm=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,cn=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,co=new Set(["draggable","spellcheck"]),cp=/["&<]/;function cq(a){if(0===a.length||!1===cp.test(a))return a;for(var b=0,c=0,d="",e="";c<a.length;c++){switch(a.charCodeAt(c)){case 34:e="&quot;";break;case 38:e="&amp;";break;case 60:e="&lt;";break;default:continue}c!==b&&(d+=a.slice(b,c)),d+=e,b=c+1}return c!==b&&(d+=a.slice(b,c)),d}var cr={},cs=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),ct=/[A-Z]/g;function cu(){this.__d=!0}function cv(a,b,c){if(!a.s){if(c instanceof cA){if(!c.s)return void(c.o=cv.bind(null,a,b));1&b&&(b=c.s),c=c.v}if(c&&c.then)return void c.then(cv.bind(null,a,b),cv.bind(null,a,2));a.s=b,a.v=c;let d=a.o;d&&d(a)}}var cw,cx,cy,cz,cA=function(){function a(){}return a.prototype.then=function(b,c){var d=new a,e=this.s;if(e){var f=1&e?b:c;if(f){try{cv(d,1,f(this.v))}catch(a){cv(d,2,a)}return d}return this}return this.o=function(a){try{var e=a.v;1&a.s?cv(d,1,b?b(e):e):c?cv(d,1,c(e)):cv(d,2,e)}catch(a){cv(d,2,a)}},d},a}(),cB={},cC=[],cD=Array.isArray,cE=Object.assign;function cF(a,b){var c,d=a.type,e=!0;return a.__c?(e=!1,(c=a.__c).state=c.__s):c=new d(a.props,b),a.__c=c,c.__v=a,c.props=a.props,c.context=b,c.__d=!0,null==c.state&&(c.state=cB),null==c.__s&&(c.__s=c.state),d.getDerivedStateFromProps?c.state=cE({},c.state,d.getDerivedStateFromProps(c.props,c.state)):e&&c.componentWillMount?(c.componentWillMount(),c.state=c.__s!==c.state?c.__s:c.state):!e&&c.componentWillUpdate&&c.componentWillUpdate(),cy&&cy(a),c.render(c.props,c.state,b)}var cG=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),cH=0;function cI(a,b,c,d,e,f){b||(b={});var g,h,i=b;"ref"in b&&(g=b.ref,delete b.ref);var j={type:a,props:i,key:c,ref:g,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--cH,__i:-1,__u:0,__source:e,__self:f};if("function"==typeof a&&(g=a.defaultProps))for(h in g)void 0===i[h]&&(i[h]=g[h]);return bU.vnode&&bU.vnode(j),j}async function cJ(a,b){let c=window.SimpleWebAuthnBrowser;async function d(c){let d=new URL(`${a}/webauthn-options/${b}`);c&&d.searchParams.append("action",c),f().forEach(a=>{d.searchParams.append(a.name,a.value)});let e=await fetch(d);return e.ok?e.json():void console.error("Failed to fetch options",e)}function e(){let a=`#${b}-form`,c=document.querySelector(a);if(!c)throw Error(`Form '${a}' not found`);return c}function f(){return Array.from(e().querySelectorAll("input[data-form-field]"))}async function g(a,b){let c=e();if(a){let b=document.createElement("input");b.type="hidden",b.name="action",b.value=a,c.appendChild(b)}if(b){let a=document.createElement("input");a.type="hidden",a.name="data",a.value=JSON.stringify(b),c.appendChild(a)}return c.submit()}async function h(a,b){let d=await c.startAuthentication(a,b);return await g("authenticate",d)}async function i(a){f().forEach(a=>{if(a.required&&!a.value)throw Error(`Missing required field: ${a.name}`)});let b=await c.startRegistration(a);return await g("register",b)}async function j(){if(!c.browserSupportsWebAuthnAutofill())return;let a=await d("authenticate");if(!a)return void console.error("Failed to fetch option for autofill authentication");try{await h(a.options,!0)}catch(a){console.error(a)}}(async function(){let a=e();if(!c.browserSupportsWebAuthn()){a.style.display="none";return}a&&a.addEventListener("submit",async a=>{a.preventDefault();let b=await d(void 0);if(!b)return void console.error("Failed to fetch options for form submission");if("authenticate"===b.action)try{await h(b.options,!1)}catch(a){console.error(a)}else if("register"===b.action)try{await i(b.options)}catch(a){console.error(a)}})})(),j()}let cK={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},cL=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function cM({html:a,title:b,status:c,cookies:d,theme:e,headTags:f}){return{cookies:d,status:c,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${cL}</style><title>${b}</title>${f??""}</head><body class="__next-auth-theme-${e?.colorScheme??"auto"}"><div class="page">${function(a,b,c){var d=bU.__s;bU.__s=!0,cw=bU.__b,cx=bU.diffed,cy=bU.__r,cz=bU.unmount;var e=function(a,b,c){var d,e,f,g={};for(f in b)"key"==f?d=b[f]:"ref"==f?e=b[f]:g[f]=b[f];if(arguments.length>2&&(g.children=arguments.length>3?bT.call(arguments,2):c),"function"==typeof a&&null!=a.defaultProps)for(f in a.defaultProps)void 0===g[f]&&(g[f]=a.defaultProps[f]);return b7(a,g,d,e,null)}(b8,null);e.__k=[a];try{var f=function a(b,c,d,e,f,g,h){if(null==b||!0===b||!1===b||""===b)return"";var i=typeof b;if("object"!=i)return"function"==i?"":"string"==i?cq(b):b+"";if(cD(b)){var j,k="";f.__k=b;for(var l=0;l<b.length;l++){var m=b[l];if(null!=m&&"boolean"!=typeof m){var n,o=a(m,c,d,e,f,g,h);"string"==typeof o?k+=o:(j||(j=[]),k&&j.push(k),k="",cD(o)?(n=j).push.apply(n,o):j.push(o))}}return j?(k&&j.push(k),j):k}if(void 0!==b.constructor)return"";b.__=f,cw&&cw(b);var p=b.type,q=b.props;if("function"==typeof p){var r,s,t,u=c;if(p===b8){if("tpl"in q){for(var v="",w=0;w<q.tpl.length;w++)if(v+=q.tpl[w],q.exprs&&w<q.exprs.length){var x=q.exprs[w];if(null==x)continue;"object"==typeof x&&(void 0===x.constructor||cD(x))?v+=a(x,c,d,e,b,g,h):v+=x}return v}if("UNSTABLE_comment"in q)return"<!--"+cq(q.UNSTABLE_comment)+"-->";s=q.children}else{if(null!=(r=p.contextType)){var y=c[r.__c];u=y?y.props.value:r.__}var z=p.prototype&&"function"==typeof p.prototype.render;if(z)s=cF(b,u),t=b.__c;else{b.__c=t={__v:b,context:u,props:b.props,setState:cu,forceUpdate:cu,__d:!0,__h:[]};for(var A=0;t.__d&&A++<25;)t.__d=!1,cy&&cy(b),s=p.call(t,q,u);t.__d=!0}if(null!=t.getChildContext&&(c=cE({},c,t.getChildContext())),z&&bU.errorBoundaries&&(p.getDerivedStateFromError||t.componentDidCatch)){s=null!=s&&s.type===b8&&null==s.key&&null==s.props.tpl?s.props.children:s;try{return a(s,c,d,e,b,g,h)}catch(f){return p.getDerivedStateFromError&&(t.__s=p.getDerivedStateFromError(f)),t.componentDidCatch&&t.componentDidCatch(f,cB),t.__d?(s=cF(b,c),null!=(t=b.__c).getChildContext&&(c=cE({},c,t.getChildContext())),a(s=null!=s&&s.type===b8&&null==s.key&&null==s.props.tpl?s.props.children:s,c,d,e,b,g,h)):""}finally{cx&&cx(b),b.__=null,cz&&cz(b)}}}s=null!=s&&s.type===b8&&null==s.key&&null==s.props.tpl?s.props.children:s;try{var B=a(s,c,d,e,b,g,h);return cx&&cx(b),b.__=null,bU.unmount&&bU.unmount(b),B}catch(f){if(!g&&h&&h.onError){var C=h.onError(f,b,function(f){return a(f,c,d,e,b,g,h)});if(void 0!==C)return C;var D=bU.__e;return D&&D(f,b),""}if(!g||!f||"function"!=typeof f.then)throw f;return f.then(function f(){try{return a(s,c,d,e,b,g,h)}catch(i){if(!i||"function"!=typeof i.then)throw i;return i.then(function(){return a(s,c,d,e,b,g,h)},f)}})}}var E,F="<"+p,G="";for(var H in q){var I=q[H];if("function"!=typeof I||"class"===H||"className"===H){switch(H){case"children":E=I;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in q)continue;H="for";break;case"className":if("class"in q)continue;H="class";break;case"defaultChecked":H="checked";break;case"defaultSelected":H="selected";break;case"defaultValue":case"value":switch(H="value",p){case"textarea":E=I;continue;case"select":e=I;continue;case"option":e!=I||"selected"in q||(F+=" selected")}break;case"dangerouslySetInnerHTML":G=I&&I.__html;continue;case"style":"object"==typeof I&&(I=function(a){var b="";for(var c in a){var d=a[c];if(null!=d&&""!==d){var e="-"==c[0]?c:cr[c]||(cr[c]=c.replace(ct,"-$&").toLowerCase()),f=";";"number"!=typeof d||e.startsWith("--")||cs.has(e)||(f="px;"),b=b+e+":"+d+f}}return b||void 0}(I));break;case"acceptCharset":H="accept-charset";break;case"httpEquiv":H="http-equiv";break;default:if(cl.test(H))H=H.replace(cl,"$1:$2").toLowerCase();else{if(ck.test(H))continue;("-"===H[4]||co.has(H))&&null!=I?I+="":d?cn.test(H)&&(H="panose1"===H?"panose-1":H.replace(/([A-Z])/g,"-$1").toLowerCase()):cm.test(H)&&(H=H.toLowerCase())}}null!=I&&!1!==I&&(F=!0===I||""===I?F+" "+H:F+" "+H+'="'+("string"==typeof I?cq(I):I+"")+'"')}}if(ck.test(p))throw Error(p+" is not a valid HTML tag name in "+F+">");if(G||("string"==typeof E?G=cq(E):null!=E&&!1!==E&&!0!==E&&(G=a(E,c,"svg"===p||"foreignObject"!==p&&d,e,b,g,h))),cx&&cx(b),b.__=null,cz&&cz(b),!G&&cG.has(p))return F+"/>";var J="</"+p+">",K=F+">";return cD(G)?[K].concat(G,[J]):"string"!=typeof G?[K,G,J]:K+G+J}(a,cB,!1,void 0,e,!1,void 0);return cD(f)?f.join(""):f}catch(a){if(a.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw a}finally{bU.__c&&bU.__c(a,cC),bU.__s=d,cC.length=0}}(a)}</div></body></html>`}}function cN(a){let{url:b,theme:c,query:d,cookies:e,pages:f,providers:g}=a;return{csrf:(a,b,c)=>a?(b.logger.warn("csrf-disabled"),c.push({name:b.cookies.csrfToken.name,value:"",options:{...b.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:c}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:b.csrfToken},cookies:c},providers:a=>({headers:{"Content-Type":"application/json"},body:a.reduce((a,{id:b,name:c,type:d,signinUrl:e,callbackUrl:f})=>(a[b]={id:b,name:c,type:d,signinUrl:e,callbackUrl:f},a),{})}),signin(b,h){if(b)throw new k.UnknownAction("Unsupported action");if(f?.signIn){let b=`${f.signIn}${f.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:a.callbackUrl??"/"})}`;return h&&(b=`${b}&${new URLSearchParams({error:h})}`),{redirect:b,cookies:e}}let i=g?.find(a=>"webauthn"===a.type&&a.enableConditionalUI&&!!a.simpleWebAuthnBrowserVersion),j="";if(i){let{simpleWebAuthnBrowserVersion:a}=i;j=`<script src="https://unpkg.com/@simplewebauthn/browser@${a}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return cM({cookies:e,theme:c,html:function(a){let{csrfToken:b,providers:c=[],callbackUrl:d,theme:e,email:f,error:g}=a;"undefined"!=typeof document&&e?.brandColor&&document.documentElement.style.setProperty("--brand-color",e.brandColor),"undefined"!=typeof document&&e?.buttonText&&document.documentElement.style.setProperty("--button-text-color",e.buttonText);let h=g&&(cK[g]??cK.default),i=c.find(a=>"webauthn"===a.type&&a.enableConditionalUI)?.id;return cI("div",{className:"signin",children:[e?.brandColor&&cI("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${e.brandColor}}`}}),e?.buttonText&&cI("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${e.buttonText}
        }
      `}}),cI("div",{className:"card",children:[h&&cI("div",{className:"error",children:cI("p",{children:h})}),e?.logo&&cI("img",{src:e.logo,alt:"Logo",className:"logo"}),c.map((a,e)=>{let g,h,i;("oauth"===a.type||"oidc"===a.type)&&({bg:g="#fff",brandColor:h,logo:i=`https://authjs.dev/img/providers/${a.id}.svg`}=a.style??{});let j=h??g??"#fff";return cI("div",{className:"provider",children:["oauth"===a.type||"oidc"===a.type?cI("form",{action:a.signinUrl,method:"POST",children:[cI("input",{type:"hidden",name:"csrfToken",value:b}),d&&cI("input",{type:"hidden",name:"callbackUrl",value:d}),cI("button",{type:"submit",className:"button",style:{"--provider-brand-color":j},tabIndex:0,children:[cI("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",a.name]}),i&&cI("img",{loading:"lazy",height:24,src:i})]})]}):null,("email"===a.type||"credentials"===a.type||"webauthn"===a.type)&&e>0&&"email"!==c[e-1].type&&"credentials"!==c[e-1].type&&"webauthn"!==c[e-1].type&&cI("hr",{}),"email"===a.type&&cI("form",{action:a.signinUrl,method:"POST",children:[cI("input",{type:"hidden",name:"csrfToken",value:b}),cI("label",{className:"section-header",htmlFor:`input-email-for-${a.id}-provider`,children:"Email"}),cI("input",{id:`input-email-for-${a.id}-provider`,autoFocus:!0,type:"email",name:"email",value:f,placeholder:"<EMAIL>",required:!0}),cI("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",a.name]})]}),"credentials"===a.type&&cI("form",{action:a.callbackUrl,method:"POST",children:[cI("input",{type:"hidden",name:"csrfToken",value:b}),Object.keys(a.credentials).map(b=>cI("div",{children:[cI("label",{className:"section-header",htmlFor:`input-${b}-for-${a.id}-provider`,children:a.credentials[b].label??b}),cI("input",{name:b,id:`input-${b}-for-${a.id}-provider`,type:a.credentials[b].type??"text",placeholder:a.credentials[b].placeholder??"",...a.credentials[b]})]},`input-group-${a.id}`)),cI("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",a.name]})]}),"webauthn"===a.type&&cI("form",{action:a.callbackUrl,method:"POST",id:`${a.id}-form`,children:[cI("input",{type:"hidden",name:"csrfToken",value:b}),Object.keys(a.formFields).map(b=>cI("div",{children:[cI("label",{className:"section-header",htmlFor:`input-${b}-for-${a.id}-provider`,children:a.formFields[b].label??b}),cI("input",{name:b,"data-form-field":!0,id:`input-${b}-for-${a.id}-provider`,type:a.formFields[b].type??"text",placeholder:a.formFields[b].placeholder??"",...a.formFields[b]})]},`input-group-${a.id}`)),cI("button",{id:`submitButton-${a.id}`,type:"submit",tabIndex:0,children:["Sign in with ",a.name]})]}),("email"===a.type||"credentials"===a.type||"webauthn"===a.type)&&e+1<c.length&&cI("hr",{})]},a.id)})]}),i&&cI(b8,{children:cI("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${cJ})(authURL, "${i}");
`}})})]})}({csrfToken:a.csrfToken,providers:a.providers?.filter(a=>["email","oauth","oidc"].includes(a.type)||"credentials"===a.type&&a.credentials||"webauthn"===a.type&&a.formFields||!1),callbackUrl:a.callbackUrl,theme:a.theme,error:h,...d}),title:"Sign In",headTags:j})},signout:()=>f?.signOut?{redirect:f.signOut,cookies:e}:cM({cookies:e,theme:c,html:function(a){let{url:b,csrfToken:c,theme:d}=a;return cI("div",{className:"signout",children:[d?.brandColor&&cI("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${d.brandColor}
        }
      `}}),d?.buttonText&&cI("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${d.buttonText}
        }
      `}}),cI("div",{className:"card",children:[d?.logo&&cI("img",{src:d.logo,alt:"Logo",className:"logo"}),cI("h1",{children:"Signout"}),cI("p",{children:"Are you sure you want to sign out?"}),cI("form",{action:b?.toString(),method:"POST",children:[cI("input",{type:"hidden",name:"csrfToken",value:c}),cI("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:a.csrfToken,url:b,theme:c}),title:"Sign Out"}),verifyRequest:a=>f?.verifyRequest?{redirect:`${f.verifyRequest}${b?.search??""}`,cookies:e}:cM({cookies:e,theme:c,html:function(a){let{url:b,theme:c}=a;return cI("div",{className:"verify-request",children:[c.brandColor&&cI("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${c.brandColor}
        }
      `}}),cI("div",{className:"card",children:[c.logo&&cI("img",{src:c.logo,alt:"Logo",className:"logo"}),cI("h1",{children:"Check your email"}),cI("p",{children:"A sign in link has been sent to your email address."}),cI("p",{children:cI("a",{className:"site",href:b.origin,children:b.host})})]})]})}({url:b,theme:c,...a}),title:"Verify Request"}),error:a=>f?.error?{redirect:`${f.error}${f.error.includes("?")?"&":"?"}error=${a}`,cookies:e}:cM({cookies:e,theme:c,...function(a){let{url:b,error:c="default",theme:d}=a,e=`${b}/signin`,f={default:{status:200,heading:"Error",message:cI("p",{children:cI("a",{className:"site",href:b?.origin,children:b?.host})})},Configuration:{status:500,heading:"Server error",message:cI("div",{children:[cI("p",{children:"There is a problem with the server configuration."}),cI("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:cI("div",{children:[cI("p",{children:"You do not have permission to sign in."}),cI("p",{children:cI("a",{className:"button",href:e,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:cI("div",{children:[cI("p",{children:"The sign in link is no longer valid."}),cI("p",{children:"It may have been used already or it may have expired."})]}),signin:cI("a",{className:"button",href:e,children:"Sign in"})}},{status:g,heading:h,message:i,signin:j}=f[c]??f.default;return{status:g,html:cI("div",{className:"error",children:[d?.brandColor&&cI("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${d?.brandColor}
        }
      `}}),cI("div",{className:"card",children:[d?.logo&&cI("img",{src:d?.logo,alt:"Logo",className:"logo"}),cI("h1",{children:h}),cI("div",{className:"message",children:i}),j]})]})}}({url:b,theme:c,error:a}),title:"Error"})}}function cO(a,b=Date.now()){return new Date(b+1e3*a)}async function cP(a,b,c,d){if(!c?.providerAccountId||!c.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(c.type))throw Error("Provider not supported");let{adapter:e,jwt:f,events:g,session:{strategy:h,generateSessionToken:i}}=d;if(!e)return{user:b,account:c};let j=c,{createUser:l,updateUser:m,getUser:n,getUserByAccount:o,getUserByEmail:p,linkAccount:q,createSession:r,getSessionAndUser:s,deleteSession:t}=e,u=null,v=null,w=!1,x="jwt"===h;if(a)if(x)try{let b=d.cookies.sessionToken.name;(u=await f.decode({...f,token:a,salt:b}))&&"sub"in u&&u.sub&&(v=await n(u.sub))}catch{}else{let b=await s(a);b&&(u=b.session,v=b.user)}if("email"===j.type){let c=await p(b.email);return c?(v?.id!==c.id&&!x&&a&&await t(a),v=await m({id:c.id,emailVerified:new Date}),await g.updateUser?.({user:v})):(v=await l({...b,emailVerified:new Date}),await g.createUser?.({user:v}),w=!0),{session:u=x?{}:await r({sessionToken:i(),userId:v.id,expires:cO(d.session.maxAge)}),user:v,isNewUser:w}}if("webauthn"===j.type){let a=await o({providerAccountId:j.providerAccountId,provider:j.provider});if(a){if(v){if(a.id===v.id){let a={...j,userId:v.id};return{session:u,user:v,isNewUser:w,account:a}}throw new k.AccountNotLinked("The account is already associated with another user",{provider:j.provider})}u=x?{}:await r({sessionToken:i(),userId:a.id,expires:cO(d.session.maxAge)});let b={...j,userId:a.id};return{session:u,user:a,isNewUser:w,account:b}}{if(v){await q({...j,userId:v.id}),await g.linkAccount?.({user:v,account:j,profile:b});let a={...j,userId:v.id};return{session:u,user:v,isNewUser:w,account:a}}if(b.email?await p(b.email):null)throw new k.AccountNotLinked("Another account already exists with the same e-mail address",{provider:j.provider});v=await l({...b}),await g.createUser?.({user:v}),await q({...j,userId:v.id}),await g.linkAccount?.({user:v,account:j,profile:b}),u=x?{}:await r({sessionToken:i(),userId:v.id,expires:cO(d.session.maxAge)});let a={...j,userId:v.id};return{session:u,user:v,isNewUser:!0,account:a}}}let y=await o({providerAccountId:j.providerAccountId,provider:j.provider});if(y){if(v){if(y.id===v.id)return{session:u,user:v,isNewUser:w};throw new k.OAuthAccountNotLinked("The account is already associated with another user",{provider:j.provider})}return{session:u=x?{}:await r({sessionToken:i(),userId:y.id,expires:cO(d.session.maxAge)}),user:y,isNewUser:w}}{let{provider:a}=d,{type:c,provider:e,providerAccountId:f,userId:h,...m}=j;if(j=Object.assign(a.account(m)??{},{providerAccountId:f,provider:e,type:c,userId:h}),v)return await q({...j,userId:v.id}),await g.linkAccount?.({user:v,account:j,profile:b}),{session:u,user:v,isNewUser:w};let n=b.email?await p(b.email):null;if(n){let a=d.provider;if(a?.allowDangerousEmailAccountLinking)v=n,w=!1;else throw new k.OAuthAccountNotLinked("Another account already exists with the same e-mail address",{provider:j.provider})}else v=await l({...b,emailVerified:null}),w=!0;return await g.createUser?.({user:v}),await q({...j,userId:v.id}),await g.linkAccount?.({user:v,account:j,profile:b}),{session:u=x?{}:await r({sessionToken:i(),userId:v.id,expires:cO(d.session.maxAge)}),user:v,isNewUser:w}}}function cQ(a,b){if(null==a)return!1;try{return a instanceof b||Object.getPrototypeOf(a)[Symbol.toStringTag]===b.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(d="oauth4webapi/v3.8.1");let cR="ERR_INVALID_ARG_VALUE",cS="ERR_INVALID_ARG_TYPE";function cT(a,b,c){let d=TypeError(a,{cause:c});return Object.assign(d,{code:b}),d}let cU=Symbol(),cV=Symbol(),cW=Symbol(),cX=Symbol(),cY=Symbol(),cZ=Symbol();Symbol();let c$=new TextEncoder,c_=new TextDecoder;function c0(a){return"string"==typeof a?c$.encode(a):c_.decode(a)}function c1(a){return"string"==typeof a?f(a):e(a)}e=Uint8Array.prototype.toBase64?a=>(a instanceof ArrayBuffer&&(a=new Uint8Array(a)),a.toBase64({alphabet:"base64url",omitPadding:!0})):a=>{a instanceof ArrayBuffer&&(a=new Uint8Array(a));let b=[];for(let c=0;c<a.byteLength;c+=32768)b.push(String.fromCharCode.apply(null,a.subarray(c,c+32768)));return btoa(b.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},f=Uint8Array.fromBase64?a=>{try{return Uint8Array.fromBase64(a,{alphabet:"base64url"})}catch(a){throw cT("The input to be decoded is not correctly encoded.",cR,a)}}:a=>{try{let b=atob(a.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),c=new Uint8Array(b.length);for(let a=0;a<b.length;a++)c[a]=b.charCodeAt(a);return c}catch(a){throw cT("The input to be decoded is not correctly encoded.",cR,a)}};class c2 extends Error{code;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=d4,Error.captureStackTrace?.(this,this.constructor)}}class c3 extends Error{code;constructor(a,b){super(a,b),this.name=this.constructor.name,b?.code&&(this.code=b?.code),Error.captureStackTrace?.(this,this.constructor)}}function c4(a,b,c){return new c3(a,{code:b,cause:c})}function c5(a){return!(null===a||"object"!=typeof a||Array.isArray(a))}function c6(a){cQ(a,Headers)&&(a=Object.fromEntries(a.entries()));let b=new Headers(a??{});if(d&&!b.has("user-agent")&&b.set("user-agent",d),b.has("authorization"))throw cT('"options.headers" must not include the "authorization" header name',cR);return b}function c7(a,b){if(void 0!==b){if("function"==typeof b&&(b=b(a.href)),!(b instanceof AbortSignal))throw cT('"options.signal" must return or be an instance of AbortSignal',cS);return b}}function c8(a){return a.includes("//")?a.replace("//","/"):a}async function c9(a,b,c,d){if(!(a instanceof URL))throw cT(`"${b}" must be an instance of URL`,cS);dq(a,d?.[cU]!==!0);let e=c(new URL(a.href)),f=c6(d?.headers);return f.set("accept","application/json"),(d?.[cX]||fetch)(e.href,{body:void 0,headers:Object.fromEntries(f.entries()),method:"GET",redirect:"manual",signal:c7(e,d?.signal)})}async function da(a,b){return c9(a,"issuerIdentifier",a=>{switch(b?.algorithm){case void 0:case"oidc":a.pathname=c8(`${a.pathname}/.well-known/openid-configuration`);break;case"oauth2":!function(a,b,c=!1){"/"===a.pathname?a.pathname=b:a.pathname=c8(`${b}/${c?a.pathname:a.pathname.replace(/(\/)$/,"")}`)}(a,".well-known/oauth-authorization-server");break;default:throw cT('"options.algorithm" must be "oidc" (default), or "oauth2"',cR)}return a},b)}function db(a,b,c,d,e){try{if("number"!=typeof a||!Number.isFinite(a))throw cT(`${c} must be a number`,cS,e);if(a>0)return;if(b){if(0!==a)throw cT(`${c} must be a non-negative number`,cR,e);return}throw cT(`${c} must be a positive number`,cR,e)}catch(a){if(d)throw c4(a.message,d,e);throw a}}function dc(a,b,c,d){try{if("string"!=typeof a)throw cT(`${b} must be a string`,cS,d);if(0===a.length)throw cT(`${b} must not be empty`,cR,d)}catch(a){if(c)throw c4(a.message,c,d);throw a}}async function dd(a,b){if(!(a instanceof URL)&&a!==eq)throw cT('"expectedIssuerIdentifier" must be an instance of URL',cS);if(!cQ(b,Response))throw cT('"response" must be an instance of Response',cS);if(200!==b.status)throw c4('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',ea,b);ei(b);let c=await ep(b);if(dc(c.issuer,'"response" body "issuer" property',d8,{body:c}),a!==eq&&new URL(c.issuer).href!==a.href)throw c4('"response" body "issuer" property does not match the expected value',ef,{expected:a.href,body:c,attribute:"issuer"});return c}function de(a){var b=a,c="application/json";if(dH(b)!==c)throw function(a,...b){let c='"response" content-type must be ';if(b.length>2){let a=b.pop();c+=`${b.join(", ")}, or ${a}`}else 2===b.length?c+=`${b[0]} or ${b[1]}`:c+=b[0];return c4(c,d9,a)}(b,c)}function df(){return c1(crypto.getRandomValues(new Uint8Array(32)))}async function dg(a){return dc(a,"codeVerifier"),c1(await crypto.subtle.digest("SHA-256",c0(a)))}function dh(a){let b=a?.[cV];return"number"==typeof b&&Number.isFinite(b)?b:0}function di(a){let b=a?.[cW];return"number"==typeof b&&Number.isFinite(b)&&-1!==Math.sign(b)?b:30}function dj(){return Math.floor(Date.now()/1e3)}function dk(a){if("object"!=typeof a||null===a)throw cT('"as" must be an object',cS);dc(a.issuer,'"as.issuer"')}function dl(a){if("object"!=typeof a||null===a)throw cT('"client" must be an object',cS);dc(a.client_id,'"client.client_id"')}function dm(a,b){let c=dj()+dh(b);return{jti:df(),aud:a.issuer,exp:c+60,iat:c,nbf:c,iss:b.client_id,sub:b.client_id}}async function dn(a,b,c){if(!c.usages.includes("sign"))throw cT('CryptoKey instances used for signing assertions must include "sign" in their "usages"',cR);let d=`${c1(c0(JSON.stringify(a)))}.${c1(c0(JSON.stringify(b)))}`,e=c1(await crypto.subtle.sign(function(a){switch(a.algorithm.name){case"ECDSA":return{name:a.algorithm.name,hash:function(a){let{algorithm:b}=a;switch(b.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new c2("unsupported ECDSA namedCurve",{cause:a})}}(a)};case"RSA-PSS":switch(ej(a),a.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:a.algorithm.name,saltLength:parseInt(a.algorithm.hash.name.slice(-3),10)>>3};default:throw new c2("unsupported RSA-PSS hash name",{cause:a})}case"RSASSA-PKCS1-v1_5":return ej(a),a.algorithm.name;case"ML-DSA-44":case"ML-DSA-65":case"ML-DSA-87":case"Ed25519":return a.algorithm.name}throw new c2("unsupported CryptoKey algorithm name",{cause:a})}(c),c,c0(d)));return`${d}.${e}`}let dp=URL.parse?(a,b)=>URL.parse(a,b):(a,b)=>{try{return new URL(a,b)}catch{return null}};function dq(a,b){if(b&&"https:"!==a.protocol)throw c4("only requests to HTTPS are allowed",eb,a);if("https:"!==a.protocol&&"http:"!==a.protocol)throw c4("only HTTP and HTTPS requests are allowed",ec,a)}function dr(a,b,c,d){let e;if("string"!=typeof a||!(e=dp(a)))throw c4(`authorization server metadata does not contain a valid ${c?`"as.mtls_endpoint_aliases.${b}"`:`"as.${b}"`}`,void 0===a?eg:eh,{attribute:c?`mtls_endpoint_aliases.${b}`:b});return dq(e,d),e}function ds(a,b,c,d){return c&&a.mtls_endpoint_aliases&&b in a.mtls_endpoint_aliases?dr(a.mtls_endpoint_aliases[b],b,c,d):dr(a[b],b,c,d)}class dt extends Error{cause;code;error;status;error_description;response;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=d3,this.cause=b.cause,this.error=b.cause.error,this.status=b.response.status,this.error_description=b.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:b.response}),Error.captureStackTrace?.(this,this.constructor)}}class du extends Error{cause;code;error;error_description;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=d5,this.cause=b.cause,this.error=b.cause.get("error"),this.error_description=b.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class dv extends Error{cause;code;response;status;constructor(a,b){super(a,b),this.name=this.constructor.name,this.code=d2,this.cause=b.cause,this.status=b.response.status,this.response=b.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let dw="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",dx=RegExp("^[,\\s]*("+dw+")\\s(.*)"),dy=RegExp("^[,\\s]*("+dw+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),dz=RegExp("^[,\\s]*"+("("+dw+")\\s*=\\s*(")+dw+")[,\\s]*(.*)"),dA=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function dB(a){if(a.status>399&&a.status<500){ei(a),de(a);try{let b=await a.clone().json();if(c5(b)&&"string"==typeof b.error&&b.error.length)return b}catch{}}}async function dC(a,b,c){if(a.status!==b){let b;if(dP(a),b=await dB(a))throw await a.body?.cancel(),new dt("server responded with an error in the response body",{cause:b,response:a});throw c4(`"response" is not a conform ${c} response (unexpected HTTP status code)`,ea,a)}}function dD(a){if(!dU.has(a))throw cT('"options.DPoP" is not a valid DPoPHandle',cR)}async function dE(a,b,c,d,e,f){if(dc(a,'"accessToken"'),!(c instanceof URL))throw cT('"url" must be an instance of URL',cS);dq(c,f?.[cU]!==!0),d=c6(d),f?.DPoP&&(dD(f.DPoP),await f.DPoP.addProof(c,d,b.toUpperCase(),a)),d.set("authorization",`${d.has("dpop")?"DPoP":"Bearer"} ${a}`);let g=await (f?.[cX]||fetch)(c.href,{body:e,headers:Object.fromEntries(d.entries()),method:b,redirect:"manual",signal:c7(c,f?.signal)});return f?.DPoP?.cacheNonce(g),g}async function dF(a,b,c,d){dk(a),dl(b);let e=ds(a,"userinfo_endpoint",b.use_mtls_endpoint_aliases,d?.[cU]!==!0),f=c6(d?.headers);return b.userinfo_signed_response_alg?f.set("accept","application/jwt"):(f.set("accept","application/json"),f.append("accept","application/jwt")),dE(c,"GET",e,f,null,{...d,[cV]:dh(b)})}let dG=Symbol();function dH(a){return a.headers.get("content-type")?.split(";")[0]}async function dI(a,b,c,d,e){let f;if(dk(a),dl(b),!cQ(d,Response))throw cT('"response" must be an instance of Response',cS);if(dP(d),200!==d.status)throw c4('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',ea,d);if(ei(d),"application/jwt"===dH(d)){let{claims:c,jwt:g}=await ek(await d.text(),el.bind(void 0,b.userinfo_signed_response_alg,a.userinfo_signing_alg_values_supported,void 0),dh(b),di(b),e?.[cZ]).then(dQ.bind(void 0,b.client_id)).then(dS.bind(void 0,a));dM.set(d,g),f=c}else{if(b.userinfo_signed_response_alg)throw c4("JWT UserInfo Response expected",d6,d);f=await ep(d)}if(dc(f.sub,'"response" body "sub" property',d8,{body:f}),c===dG);else if(dc(c,'"expectedSubject"'),f.sub!==c)throw c4('unexpected "response" body "sub" property value',ef,{expected:c,body:f,attribute:"sub"});return f}async function dJ(a,b,c,d,e,f,g){return await c(a,b,e,f),f.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(g?.[cX]||fetch)(d.href,{body:e,headers:Object.fromEntries(f.entries()),method:"POST",redirect:"manual",signal:c7(d,g?.signal)})}async function dK(a,b,c,d,e,f){let g=ds(a,"token_endpoint",b.use_mtls_endpoint_aliases,f?.[cU]!==!0);e.set("grant_type",d);let h=c6(f?.headers);h.set("accept","application/json"),f?.DPoP!==void 0&&(dD(f.DPoP),await f.DPoP.addProof(g,h,"POST"));let i=await dJ(a,b,c,g,e,h,f);return f?.DPoP?.cacheNonce(i),i}let dL=new WeakMap,dM=new WeakMap;function dN(a){if(!a.id_token)return;let b=dL.get(a);if(!b)throw cT('"ref" was already garbage collected or did not resolve from the proper sources',cR);return b}async function dO(a,b,c,d,e,f){if(dk(a),dl(b),!cQ(c,Response))throw cT('"response" must be an instance of Response',cS);await dC(c,200,"Token Endpoint"),ei(c);let g=await ep(c);if(dc(g.access_token,'"response" body "access_token" property',d8,{body:g}),dc(g.token_type,'"response" body "token_type" property',d8,{body:g}),g.token_type=g.token_type.toLowerCase(),void 0!==g.expires_in){let a="number"!=typeof g.expires_in?parseFloat(g.expires_in):g.expires_in;db(a,!0,'"response" body "expires_in" property',d8,{body:g}),g.expires_in=a}if(void 0!==g.refresh_token&&dc(g.refresh_token,'"response" body "refresh_token" property',d8,{body:g}),void 0!==g.scope&&"string"!=typeof g.scope)throw c4('"response" body "scope" property must be a string',d8,{body:g});if(void 0!==g.id_token){dc(g.id_token,'"response" body "id_token" property',d8,{body:g});let f=["aud","exp","iat","iss","sub"];!0===b.require_auth_time&&f.push("auth_time"),void 0!==b.default_max_age&&(db(b.default_max_age,!0,'"client.default_max_age"'),f.push("auth_time")),d?.length&&f.push(...d);let{claims:h,jwt:i}=await ek(g.id_token,el.bind(void 0,b.id_token_signed_response_alg,a.id_token_signing_alg_values_supported,"RS256"),dh(b),di(b),e).then(dY.bind(void 0,f)).then(dT.bind(void 0,a)).then(dR.bind(void 0,b.client_id));if(Array.isArray(h.aud)&&1!==h.aud.length){if(void 0===h.azp)throw c4('ID Token "aud" (audience) claim includes additional untrusted audiences',ee,{claims:h,claim:"aud"});if(h.azp!==b.client_id)throw c4('unexpected ID Token "azp" (authorized party) claim value',ee,{expected:b.client_id,claims:h,claim:"azp"})}void 0!==h.auth_time&&db(h.auth_time,!0,'ID Token "auth_time" (authentication time)',d8,{claims:h}),dM.set(c,i),dL.set(g,h)}if(f?.[g.token_type]!==void 0)f[g.token_type](c,g);else if("dpop"!==g.token_type&&"bearer"!==g.token_type)throw new c2("unsupported `token_type` value",{cause:{body:g}});return g}function dP(a){let b;if(b=function(a){if(!cQ(a,Response))throw cT('"response" must be an instance of Response',cS);let b=a.headers.get("www-authenticate");if(null===b)return;let c=[],d=b;for(;d;){let a,b=d.match(dx),e=b?.["1"].toLowerCase();if(d=b?.["2"],!e)return;let f={};for(;d;){let c,e;if(b=d.match(dy)){if([,c,e,d]=b,e.includes("\\"))try{e=JSON.parse(`"${e}"`)}catch{}f[c.toLowerCase()]=e;continue}if(b=d.match(dz)){[,c,e,d]=b,f[c.toLowerCase()]=e;continue}if(b=d.match(dA)){if(Object.keys(f).length)break;[,a,d]=b;break}return}let g={scheme:e,parameters:f};a&&(g.token68=a),c.push(g)}if(c.length)return c}(a))throw new dv("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:b,response:a})}function dQ(a,b){return void 0!==b.claims.aud?dR(a,b):b}function dR(a,b){if(Array.isArray(b.claims.aud)){if(!b.claims.aud.includes(a))throw c4('unexpected JWT "aud" (audience) claim value',ee,{expected:a,claims:b.claims,claim:"aud"})}else if(b.claims.aud!==a)throw c4('unexpected JWT "aud" (audience) claim value',ee,{expected:a,claims:b.claims,claim:"aud"});return b}function dS(a,b){return void 0!==b.claims.iss?dT(a,b):b}function dT(a,b){let c=a[er]?.(b)??a.issuer;if(b.claims.iss!==c)throw c4('unexpected JWT "iss" (issuer) claim value',ee,{expected:c,claims:b.claims,claim:"iss"});return b}let dU=new WeakSet,dV=Symbol();async function dW(a,b,c,d,e,f,g){if(dk(a),dl(b),!dU.has(d))throw cT('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',cR);dc(e,'"redirectUri"');let h=em(d,"code");if(!h)throw c4('no authorization code in "callbackParameters"',d8);let i=new URLSearchParams(g?.additionalParameters);return i.set("redirect_uri",e),i.set("code",h),f!==dV&&(dc(f,'"codeVerifier"'),i.set("code_verifier",f)),dK(a,b,c,"authorization_code",i,g)}let dX={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function dY(a,b){for(let c of a)if(void 0===b.claims[c])throw c4(`JWT "${c}" (${dX[c]}) claim missing`,d8,{claims:b.claims});return b}let dZ=Symbol(),d$=Symbol();async function d_(a,b,c,d){return"string"==typeof d?.expectedNonce||"number"==typeof d?.maxAge||d?.requireIdToken?d0(a,b,c,d.expectedNonce,d.maxAge,d[cZ],d.recognizedTokenTypes):d1(a,b,c,d?.[cZ],d?.recognizedTokenTypes)}async function d0(a,b,c,d,e,f,g){let h=[];switch(d){case void 0:d=dZ;break;case dZ:break;default:dc(d,'"expectedNonce" argument'),h.push("nonce")}switch(e??=b.default_max_age){case void 0:e=d$;break;case d$:break;default:db(e,!0,'"maxAge" argument'),h.push("auth_time")}let i=await dO(a,b,c,h,f,g);dc(i.id_token,'"response" body "id_token" property',d8,{body:i});let j=dN(i);if(e!==d$){let a=dj()+dh(b),c=di(b);if(j.auth_time+e<a-c)throw c4("too much time has elapsed since the last End-User authentication",ed,{claims:j,now:a,tolerance:c,claim:"auth_time"})}if(d===dZ){if(void 0!==j.nonce)throw c4('unexpected ID Token "nonce" claim value',ee,{expected:void 0,claims:j,claim:"nonce"})}else if(j.nonce!==d)throw c4('unexpected ID Token "nonce" claim value',ee,{expected:d,claims:j,claim:"nonce"});return i}async function d1(a,b,c,d,e){let f=await dO(a,b,c,void 0,d,e),g=dN(f);if(g){if(void 0!==b.default_max_age){db(b.default_max_age,!0,'"client.default_max_age"');let a=dj()+dh(b),c=di(b);if(g.auth_time+b.default_max_age<a-c)throw c4("too much time has elapsed since the last End-User authentication",ed,{claims:g,now:a,tolerance:c,claim:"auth_time"})}if(void 0!==g.nonce)throw c4('unexpected ID Token "nonce" claim value',ee,{expected:void 0,claims:g,claim:"nonce"})}return f}let d2="OAUTH_WWW_AUTHENTICATE_CHALLENGE",d3="OAUTH_RESPONSE_BODY_ERROR",d4="OAUTH_UNSUPPORTED_OPERATION",d5="OAUTH_AUTHORIZATION_RESPONSE_ERROR",d6="OAUTH_JWT_USERINFO_EXPECTED",d7="OAUTH_PARSE_ERROR",d8="OAUTH_INVALID_RESPONSE",d9="OAUTH_RESPONSE_IS_NOT_JSON",ea="OAUTH_RESPONSE_IS_NOT_CONFORM",eb="OAUTH_HTTP_REQUEST_FORBIDDEN",ec="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",ed="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",ee="OAUTH_JWT_CLAIM_COMPARISON_FAILED",ef="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",eg="OAUTH_MISSING_SERVER_METADATA",eh="OAUTH_INVALID_SERVER_METADATA";function ei(a){if(a.bodyUsed)throw cT('"response" body has been used already',cR)}function ej(a){let{algorithm:b}=a;if("number"!=typeof b.modulusLength||b.modulusLength<2048)throw new c2(`unsupported ${b.name} modulusLength`,{cause:a})}async function ek(a,b,c,d,e){let f,g,{0:h,1:i,length:j}=a.split(".");if(5===j)if(void 0!==e)a=await e(a),{0:h,1:i,length:j}=a.split(".");else throw new c2("JWE decryption is not configured",{cause:a});if(3!==j)throw c4("Invalid JWT",d8,a);try{f=JSON.parse(c0(c1(h)))}catch(a){throw c4("failed to parse JWT Header body as base64url encoded JSON",d7,a)}if(!c5(f))throw c4("JWT Header must be a top level object",d8,a);if(b(f),void 0!==f.crit)throw new c2('no JWT "crit" header parameter extensions are supported',{cause:{header:f}});try{g=JSON.parse(c0(c1(i)))}catch(a){throw c4("failed to parse JWT Payload body as base64url encoded JSON",d7,a)}if(!c5(g))throw c4("JWT Payload must be a top level object",d8,a);let k=dj()+c;if(void 0!==g.exp){if("number"!=typeof g.exp)throw c4('unexpected JWT "exp" (expiration time) claim type',d8,{claims:g});if(g.exp<=k-d)throw c4('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',ed,{claims:g,now:k,tolerance:d,claim:"exp"})}if(void 0!==g.iat&&"number"!=typeof g.iat)throw c4('unexpected JWT "iat" (issued at) claim type',d8,{claims:g});if(void 0!==g.iss&&"string"!=typeof g.iss)throw c4('unexpected JWT "iss" (issuer) claim type',d8,{claims:g});if(void 0!==g.nbf){if("number"!=typeof g.nbf)throw c4('unexpected JWT "nbf" (not before) claim type',d8,{claims:g});if(g.nbf>k+d)throw c4('unexpected JWT "nbf" (not before) claim value',ed,{claims:g,now:k,tolerance:d,claim:"nbf"})}if(void 0!==g.aud&&"string"!=typeof g.aud&&!Array.isArray(g.aud))throw c4('unexpected JWT "aud" (audience) claim type',d8,{claims:g});return{header:f,claims:g,jwt:a}}function el(a,b,c,d){if(void 0!==a){if("string"==typeof a?d.alg!==a:!a.includes(d.alg))throw c4('unexpected JWT "alg" header parameter',d8,{header:d,expected:a,reason:"client configuration"});return}if(Array.isArray(b)){if(!b.includes(d.alg))throw c4('unexpected JWT "alg" header parameter',d8,{header:d,expected:b,reason:"authorization server metadata"});return}if(void 0!==c){if("string"==typeof c?d.alg!==c:"function"==typeof c?!c(d.alg):!c.includes(d.alg))throw c4('unexpected JWT "alg" header parameter',d8,{header:d,expected:c,reason:"default value"});return}throw c4('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:a,issuer:b,fallback:c})}function em(a,b){let{0:c,length:d}=a.getAll(b);if(d>1)throw c4(`"${b}" parameter must be provided only once`,d8);return c}let en=Symbol(),eo=Symbol();async function ep(a,b=de){let c;try{c=await a.json()}catch(c){throw b(a),c4('failed to parse "response" body as JSON',d7,c)}if(!c5(c))throw c4('"response" body must be a top level object',d8,{body:c});return c}let eq=Symbol(),er=Symbol();async function es(a,b,c){let{cookies:d,logger:e}=c,f=d[a],g=new Date;g.setTime(g.getTime()+9e5),e.debug(`CREATE_${a.toUpperCase()}`,{name:f.name,payload:b,COOKIE_TTL:900,expires:g});let h=await bj({...c.jwt,maxAge:900,token:{value:b},salt:f.name}),i={...f.options,expires:g};return{name:f.name,value:h,options:i}}async function et(a,b,c){try{let{logger:d,cookies:e,jwt:f}=c;if(d.debug(`PARSE_${a.toUpperCase()}`,{cookie:b}),!b)throw new k.InvalidCheck(`${a} cookie was missing`);let g=await bk({...f,token:b,salt:e[a].name});if(g?.value)return g.value;throw Error("Invalid cookie")}catch(b){throw new k.InvalidCheck(`${a} value could not be parsed`,{cause:b})}}function eu(a,b,c){let{logger:d,cookies:e}=b,f=e[a];d.debug(`CLEAR_${a.toUpperCase()}`,{cookie:f}),c.push({name:f.name,value:"",options:{...e[a].options,maxAge:0}})}function ev(a,b){return async function(c,d,e){let{provider:f,logger:g}=e;if(!f?.checks?.includes(a))return;let h=c?.[e.cookies[b].name];g.debug(`USE_${b.toUpperCase()}`,{value:h});let i=await et(b,h,e);return eu(b,e,d),i}}let ew={async create(a){let b=df(),c=await dg(b);return{cookie:await es("pkceCodeVerifier",b,a),value:c}},use:ev("pkce","pkceCodeVerifier")},ex="encodedState",ey={async create(a,b){let{provider:c}=a;if(!c.checks.includes("state")){if(b)throw new k.InvalidCheck("State data was provided but the provider is not configured to use state");return}let d={origin:b,random:df()},e=await bj({secret:a.jwt.secret,token:d,salt:ex,maxAge:900});return{cookie:await es("state",e,a),value:e}},use:ev("state","state"),async decode(a,b){try{b.logger.debug("DECODE_STATE",{state:a});let c=await bk({secret:b.jwt.secret,token:a,salt:ex});if(c)return c;throw Error("Invalid state")}catch(a){throw new k.InvalidCheck("State could not be decoded",{cause:a})}}},ez={async create(a){if(!a.provider.checks.includes("nonce"))return;let b=df();return{cookie:await es("nonce",b,a),value:b}},use:ev("nonce","nonce")},eA="encodedWebauthnChallenge",eB={create:async(a,b,c)=>({cookie:await es("webauthnChallenge",await bj({secret:a.jwt.secret,token:{challenge:b,registerData:c},salt:eA,maxAge:900}),a)}),async use(a,b,c){let d=b?.[a.cookies.webauthnChallenge.name],e=await et("webauthnChallenge",d,a),f=await bk({secret:a.jwt.secret,token:e,salt:eA});if(eu("webauthnChallenge",a,c),!f)throw new k.InvalidCheck("WebAuthn challenge was missing");return f}};function eC(a){return encodeURIComponent(a).replace(/%20/g,"+")}async function eD(a,b,c){let d,e,f,{logger:g,provider:h}=c,{token:i,userinfo:j}=h;if(i?.url&&"authjs.dev"!==i.url.host||j?.url&&"authjs.dev"!==j.url.host)d={issuer:h.issuer??"https://authjs.dev",token_endpoint:i?.url.toString(),userinfo_endpoint:j?.url.toString()};else{let a=new URL(h.issuer),b=await da(a,{[cU]:!0,[cX]:h[bF]});if(!(d=await dd(a,b)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!d.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let l={client_id:h.clientId,...h.client};switch(l.token_endpoint_auth_method){case void 0:case"client_secret_basic":e=(a,b,c,d)=>{d.set("authorization",function(a,b){let c=eC(a),d=eC(b),e=btoa(`${c}:${d}`);return`Basic ${e}`}(h.clientId,h.clientSecret))};break;case"client_secret_post":var m;dc(m=h.clientSecret,'"clientSecret"'),e=(a,b,c,d)=>{c.set("client_id",b.client_id),c.set("client_secret",m)};break;case"client_secret_jwt":e=function(a,b){let c;dc(a,'"clientSecret"');let d=void 0;return async(b,e,f,g)=>{c||=await crypto.subtle.importKey("raw",c0(a),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let h={alg:"HS256"},i=dm(b,e);d?.(h,i);let j=`${c1(c0(JSON.stringify(h)))}.${c1(c0(JSON.stringify(i)))}`,k=await crypto.subtle.sign(c.algorithm,c,c0(j));f.set("client_id",e.client_id),f.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),f.set("client_assertion",`${j}.${c1(new Uint8Array(k))}`)}}(h.clientSecret);break;case"private_key_jwt":e=function(a,b){let{key:c,kid:d}=a instanceof CryptoKey?{key:a}:a?.key instanceof CryptoKey?(void 0!==a.kid&&dc(a.kid,'"kid"'),{key:a.key,kid:a.kid}):{};var e='"clientPrivateKey.key"';if(!(c instanceof CryptoKey))throw cT(`${e} must be a CryptoKey`,cS);if("private"!==c.type)throw cT(`${e} must be a private CryptoKey`,cR);return async(a,e,f,g)=>{let h={alg:function(a){switch(a.algorithm.name){case"RSA-PSS":switch(a.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new c2("unsupported RsaHashedKeyAlgorithm hash name",{cause:a})}case"RSASSA-PKCS1-v1_5":switch(a.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new c2("unsupported RsaHashedKeyAlgorithm hash name",{cause:a})}case"ECDSA":switch(a.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new c2("unsupported EcKeyAlgorithm namedCurve",{cause:a})}case"Ed25519":case"ML-DSA-44":case"ML-DSA-65":case"ML-DSA-87":return a.algorithm.name;case"EdDSA":return"Ed25519";default:throw new c2("unsupported CryptoKey algorithm name",{cause:a})}}(c),kid:d},i=dm(a,e);b?.[cY]?.(h,i),f.set("client_id",e.client_id),f.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),f.set("client_assertion",await dn(h,i,c))}}(h.token.clientPrivateKey,{[cY](a,b){b.aud=[d.issuer,d.token_endpoint]}});break;case"none":e=(a,b,c,d)=>{c.set("client_id",b.client_id)};break;default:throw Error("unsupported client authentication method")}let n=[],o=await ey.use(b,n,c);try{f=function(a,b,c,d){var e;if(dk(a),dl(b),c instanceof URL&&(c=c.searchParams),!(c instanceof URLSearchParams))throw cT('"parameters" must be an instance of URLSearchParams, or URL',cS);if(em(c,"response"))throw c4('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',d8,{parameters:c});let f=em(c,"iss"),g=em(c,"state");if(!f&&a.authorization_response_iss_parameter_supported)throw c4('response parameter "iss" (issuer) missing',d8,{parameters:c});if(f&&f!==a.issuer)throw c4('unexpected "iss" (issuer) response parameter value',d8,{expected:a.issuer,parameters:c});switch(d){case void 0:case eo:if(void 0!==g)throw c4('unexpected "state" response parameter encountered',d8,{expected:void 0,parameters:c});break;case en:break;default:if(dc(d,'"expectedState" argument'),g!==d)throw c4(void 0===g?'response parameter "state" missing':'unexpected "state" response parameter value',d8,{expected:d,parameters:c})}if(em(c,"error"))throw new du("authorization response from the server is an error",{cause:c});let h=em(c,"id_token"),i=em(c,"token");if(void 0!==h||void 0!==i)throw new c2("implicit and hybrid flows are not supported");return e=new URLSearchParams(c),dU.add(e),e}(d,l,new URLSearchParams(a),h.checks.includes("state")?o:en)}catch(a){if(a instanceof du){let b={providerId:h.id,...Object.fromEntries(a.cause.entries())};throw g.debug("OAuthCallbackError",b),new k.OAuthCallbackError("OAuth Provider returned an error",b)}throw a}let p=await ew.use(b,n,c),q=h.callbackUrl;!c.isOnRedirectProxy&&h.redirectProxyUrl&&(q=h.redirectProxyUrl);let r=await dW(d,l,e,f,q,p??"decoy",{[cU]:!0,[cX]:(...a)=>(h.checks.includes("pkce")||a[1].body.delete("code_verifier"),(h[bF]??fetch)(...a))});h.token?.conform&&(r=await h.token.conform(r.clone())??r);let s={},t="oidc"===h.type;if(h[bG])switch(h.id){case"microsoft-entra-id":case"azure-ad":{let a=await r.clone().json();if(a.error){let b={providerId:h.id,...a};throw new k.OAuthCallbackError(`OAuth Provider returned an error: ${a.error}`,b)}let{tid:b}=function(a){let b,c;if("string"!=typeof a)throw new N("JWTs must use Compact JWS serialization, JWT must be a string");let{1:d,length:e}=a.split(".");if(5===e)throw new N("Only JWTs using Compact JWS serialization can be decoded");if(3!==e)throw new N("Invalid JWT");if(!d)throw new N("JWTs must contain a payload");try{b=D(d)}catch{throw new N("Failed to base64url decode the payload")}try{c=JSON.parse(y.decode(b))}catch{throw new N("Failed to parse the decoded payload as JSON")}if(!au(c))throw new N("Invalid JWT Claims Set");return c}(a.id_token);if("string"==typeof b){let a=d.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",c=new URL(d.issuer.replace(a,b)),e=await da(c,{[cX]:h[bF]});d=await dd(c,e)}}}let u=await d_(d,l,r,{expectedNonce:await ez.use(b,n,c),requireIdToken:t});if(t){let b=dN(u);if(s=b,h[bG]&&"apple"===h.id)try{s.user=JSON.parse(a?.user)}catch{}if(!1===h.idToken){let a=await dF(d,l,u.access_token,{[cX]:h[bF],[cU]:!0});s=await dI(d,l,b.sub,a)}}else if(j?.request){let a=await j.request({tokens:u,provider:h});a instanceof Object&&(s=a)}else if(j?.url){let a=await dF(d,l,u.access_token,{[cX]:h[bF],[cU]:!0});s=await a.json()}else throw TypeError("No userinfo endpoint configured");return u.expires_in&&(u.expires_at=Math.floor(Date.now()/1e3)+Number(u.expires_in)),{...await eE(s,h,u,g),profile:s,cookies:n}}async function eE(a,b,c,d){try{let d=await b.profile(a,c);return{user:{...d,id:crypto.randomUUID(),email:d.email?.toLowerCase()},account:{...c,provider:b.id,type:b.type,providerAccountId:d.id??crypto.randomUUID()}}}catch(c){d.debug("getProfile error details",a),d.error(new k.OAuthProfileParseError(c,{provider:b.id}))}}async function eF(a,b,c,d){let e=await eK(a,b,c),{cookie:f}=await eB.create(a,e.challenge,c);return{status:200,cookies:[...d??[],f],body:{action:"register",options:e},headers:{"Content-Type":"application/json"}}}async function eG(a,b,c,d){let e=await eJ(a,b,c),{cookie:f}=await eB.create(a,e.challenge);return{status:200,cookies:[...d??[],f],body:{action:"authenticate",options:e},headers:{"Content-Type":"application/json"}}}async function eH(a,b,c){let d,{adapter:e,provider:f}=a,g=b.body&&"string"==typeof b.body.data?JSON.parse(b.body.data):void 0;if(!g||"object"!=typeof g||!("id"in g)||"string"!=typeof g.id)throw new k.AuthError("Invalid WebAuthn Authentication response");let h=eN(eM(g.id)),i=await e.getAuthenticator(h);if(!i)throw new k.AuthError(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:h})}`);let{challenge:j}=await eB.use(a,b.cookies,c);try{var l;let c=f.getRelayingParty(a,b);d=await f.simpleWebAuthn.verifyAuthenticationResponse({...f.verifyAuthenticationOptions,expectedChallenge:j,response:g,authenticator:{...l=i,credentialDeviceType:l.credentialDeviceType,transports:eO(l.transports),credentialID:eM(l.credentialID),credentialPublicKey:eM(l.credentialPublicKey)},expectedOrigin:c.origin,expectedRPID:c.id})}catch(a){throw new k.WebAuthnVerificationError(a)}let{verified:m,authenticationInfo:n}=d;if(!m)throw new k.WebAuthnVerificationError("WebAuthn authentication response could not be verified");try{let{newCounter:a}=n;await e.updateAuthenticatorCounter(i.credentialID,a)}catch(a){throw new k.AdapterError(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:h,oldCounter:i.counter,newCounter:n.newCounter})}`,a)}let o=await e.getAccount(i.providerAccountId,f.id);if(!o)throw new k.AuthError(`WebAuthn account not found in database: ${JSON.stringify({credentialID:h,providerAccountId:i.providerAccountId})}`);let p=await e.getUser(o.userId);if(!p)throw new k.AuthError(`WebAuthn user not found in database: ${JSON.stringify({credentialID:h,providerAccountId:i.providerAccountId,userID:o.userId})}`);return{account:o,user:p}}async function eI(a,b,c){var d;let e,{provider:f}=a,g=b.body&&"string"==typeof b.body.data?JSON.parse(b.body.data):void 0;if(!g||"object"!=typeof g||!("id"in g)||"string"!=typeof g.id)throw new k.AuthError("Invalid WebAuthn Registration response");let{challenge:h,registerData:i}=await eB.use(a,b.cookies,c);if(!i)throw new k.AuthError("Missing user registration data in WebAuthn challenge cookie");try{let c=f.getRelayingParty(a,b);e=await f.simpleWebAuthn.verifyRegistrationResponse({...f.verifyRegistrationOptions,expectedChallenge:h,response:g,expectedOrigin:c.origin,expectedRPID:c.id})}catch(a){throw new k.WebAuthnVerificationError(a)}if(!e.verified||!e.registrationInfo)throw new k.WebAuthnVerificationError("WebAuthn registration response could not be verified");let j={providerAccountId:eN(e.registrationInfo.credentialID),provider:a.provider.id,type:f.type},l={providerAccountId:j.providerAccountId,counter:e.registrationInfo.counter,credentialID:eN(e.registrationInfo.credentialID),credentialPublicKey:eN(e.registrationInfo.credentialPublicKey),credentialBackedUp:e.registrationInfo.credentialBackedUp,credentialDeviceType:e.registrationInfo.credentialDeviceType,transports:(d=g.response.transports,d?.join(","))};return{user:i,account:j,authenticator:l}}async function eJ(a,b,c){let{provider:d,adapter:e}=a,f=c&&c.id?await e.listAuthenticatorsByUserId(c.id):null,g=d.getRelayingParty(a,b);return await d.simpleWebAuthn.generateAuthenticationOptions({...d.authenticationOptions,rpID:g.id,allowCredentials:f?.map(a=>({id:eM(a.credentialID),type:"public-key",transports:eO(a.transports)}))})}async function eK(a,b,c){let{provider:d,adapter:e}=a,f=c.id?await e.listAuthenticatorsByUserId(c.id):null,g=by(32),h=d.getRelayingParty(a,b);return await d.simpleWebAuthn.generateRegistrationOptions({...d.registrationOptions,userID:g,userName:c.email,userDisplayName:c.name??void 0,rpID:h.id,rpName:h.name,excludeCredentials:f?.map(a=>({id:eM(a.credentialID),type:"public-key",transports:eO(a.transports)}))})}function eL(a){let{provider:b,adapter:c}=a;if(!c)throw new k.MissingAdapter("An adapter is required for the WebAuthn provider");if(!b||"webauthn"!==b.type)throw new k.InvalidProvider("Provider must be WebAuthn");return{...a,provider:b,adapter:c}}function eM(a){return new Uint8Array(Buffer.from(a,"base64"))}function eN(a){return Buffer.from(a).toString("base64")}function eO(a){return a?a.split(","):void 0}async function eP(a,b,c,d){if(!b.provider)throw new k.InvalidProvider("Callback route called without provider");let{query:e,body:f,method:g,headers:h}=a,{provider:i,adapter:j,url:l,callbackUrl:m,pages:n,jwt:o,events:p,callbacks:q,session:{strategy:r,maxAge:s},logger:t}=b,u="jwt"===r;try{if("oauth"===i.type||"oidc"===i.type){let g,h=i.authorization?.url.searchParams.get("response_mode")==="form_post"?f:e;if(b.isOnRedirectProxy&&h?.state){let a=await ey.decode(h.state,b);if(a?.origin&&new URL(a.origin).origin!==b.url.origin){let b=`${a.origin}?${new URLSearchParams(h)}`;return t.debug("Proxy redirecting to",b),{redirect:b,cookies:d}}}let k=await eD(h,a.cookies,b);k.cookies.length&&d.push(...k.cookies),t.debug("authorization result",k);let{user:r,account:v,profile:w}=k;if(!r||!v||!w)return{redirect:`${l}/signin`,cookies:d};if(j){let{getUserByAccount:a}=j;g=await a({providerAccountId:v.providerAccountId,provider:i.id})}let x=await eQ({user:g??r,account:v,profile:w},b);if(x)return{redirect:x,cookies:d};let{user:y,session:z,isNewUser:A}=await cP(c.value,r,v,b);if(u){let a={name:y.name,email:y.email,picture:y.image,sub:y.id?.toString()},e=await q.jwt({token:a,user:y,account:v,profile:w,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===e)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,f=await o.encode({...o,token:e,salt:a}),g=new Date;g.setTime(g.getTime()+1e3*s);let h=c.chunk(f,{expires:g});d.push(...h)}}else d.push({name:b.cookies.sessionToken.name,value:z.sessionToken,options:{...b.cookies.sessionToken.options,expires:z.expires}});if(await p.signIn?.({user:y,account:v,profile:w,isNewUser:A}),A&&n.newUser)return{redirect:`${n.newUser}${n.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:m})}`,cookies:d};return{redirect:m,cookies:d}}if("email"===i.type){let a=e?.token,f=e?.email;if(!a){let b=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!a}});throw b.name="Configuration",b}let g=i.secret??b.secret,h=await j.useVerificationToken({identifier:f,token:await bx(`${a}${g}`)}),l=!!h,r=l&&h.expires.valueOf()<Date.now();if(!l||r||f&&h.identifier!==f)throw new k.Verification({hasInvite:l,expired:r});let{identifier:t}=h,v=await j.getUserByEmail(t)??{id:crypto.randomUUID(),email:t,emailVerified:null},w={providerAccountId:v.email,userId:v.id,type:"email",provider:i.id},x=await eQ({user:v,account:w},b);if(x)return{redirect:x,cookies:d};let{user:y,session:z,isNewUser:A}=await cP(c.value,v,w,b);if(u){let a={name:y.name,email:y.email,picture:y.image,sub:y.id?.toString()},e=await q.jwt({token:a,user:y,account:w,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===e)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,f=await o.encode({...o,token:e,salt:a}),g=new Date;g.setTime(g.getTime()+1e3*s);let h=c.chunk(f,{expires:g});d.push(...h)}}else d.push({name:b.cookies.sessionToken.name,value:z.sessionToken,options:{...b.cookies.sessionToken.options,expires:z.expires}});if(await p.signIn?.({user:y,account:w,isNewUser:A}),A&&n.newUser)return{redirect:`${n.newUser}${n.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:m})}`,cookies:d};return{redirect:m,cookies:d}}if("credentials"===i.type&&"POST"===g){let a=f??{};Object.entries(e??{}).forEach(([a,b])=>l.searchParams.set(a,b));let j=await i.authorize(a,new Request(l,{headers:h,method:g,body:JSON.stringify(f)}));if(j)j.id=j.id?.toString()??crypto.randomUUID();else throw new k.CredentialsSignin;let n={providerAccountId:j.id,type:"credentials",provider:i.id},r=await eQ({user:j,account:n,credentials:a},b);if(r)return{redirect:r,cookies:d};let t={name:j.name,email:j.email,picture:j.image,sub:j.id},u=await q.jwt({token:t,user:j,account:n,isNewUser:!1,trigger:"signIn"});if(null===u)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,e=await o.encode({...o,token:u,salt:a}),f=new Date;f.setTime(f.getTime()+1e3*s);let g=c.chunk(e,{expires:f});d.push(...g)}return await p.signIn?.({user:j,account:n}),{redirect:m,cookies:d}}else if("webauthn"===i.type&&"POST"===g){let e,f,g,h=a.body?.action;if("string"!=typeof h||"authenticate"!==h&&"register"!==h)throw new k.AuthError("Invalid action parameter");let i=eL(b);switch(h){case"authenticate":{let b=await eH(i,a,d);e=b.user,f=b.account;break}case"register":{let c=await eI(b,a,d);e=c.user,f=c.account,g=c.authenticator}}await eQ({user:e,account:f},b);let{user:j,isNewUser:l,session:r,account:t}=await cP(c.value,e,f,b);if(!t)throw new k.AuthError("Error creating or finding account");if(g&&j.id&&await i.adapter.createAuthenticator({...g,userId:j.id}),u){let a={name:j.name,email:j.email,picture:j.image,sub:j.id?.toString()},e=await q.jwt({token:a,user:j,account:t,isNewUser:l,trigger:l?"signUp":"signIn"});if(null===e)d.push(...c.clean());else{let a=b.cookies.sessionToken.name,f=await o.encode({...o,token:e,salt:a}),g=new Date;g.setTime(g.getTime()+1e3*s);let h=c.chunk(f,{expires:g});d.push(...h)}}else d.push({name:b.cookies.sessionToken.name,value:r.sessionToken,options:{...b.cookies.sessionToken.options,expires:r.expires}});if(await p.signIn?.({user:j,account:t,isNewUser:l}),l&&n.newUser)return{redirect:`${n.newUser}${n.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:m})}`,cookies:d};return{redirect:m,cookies:d}}throw new k.InvalidProvider(`Callback for provider type (${i.type}) is not supported`)}catch(b){if(b instanceof k.AuthError)throw b;let a=new k.CallbackRouteError(b,{provider:i.id});throw t.debug("callback route error details",{method:g,query:e,body:f}),a}}async function eQ(a,b){let c,{signIn:d,redirect:e}=b.callbacks;try{c=await d(a)}catch(a){if(a instanceof k.AuthError)throw a;throw new k.AccessDenied(a)}if(!c)throw new k.AccessDenied("AccessDenied");if("string"==typeof c)return await e({url:c,baseUrl:b.url.origin})}async function eR(a,b,c,d,e){let{adapter:f,jwt:g,events:h,callbacks:i,logger:j,session:{strategy:l,maxAge:m}}=a,n={body:null,headers:{"Content-Type":"application/json",...!d&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:c},o=b.value;if(!o)return n;if("jwt"===l){try{let c=a.cookies.sessionToken.name,f=await g.decode({...g,token:o,salt:c});if(!f)throw Error("Invalid JWT");let j=await i.jwt({token:f,...d&&{trigger:"update"},session:e}),k=cO(m);if(null!==j){let a={user:{name:j.name,email:j.email,image:j.picture},expires:k.toISOString()},d=await i.session({session:a,token:j});n.body=d;let e=await g.encode({...g,token:j,salt:c}),f=b.chunk(e,{expires:k});n.cookies?.push(...f),await h.session?.({session:d,token:j})}else n.cookies?.push(...b.clean())}catch(a){j.error(new k.JWTSessionError(a)),n.cookies?.push(...b.clean())}return n}try{let{getSessionAndUser:c,deleteSession:g,updateSession:j}=f,k=await c(o);if(k&&k.session.expires.valueOf()<Date.now()&&(await g(o),k=null),k){let{user:b,session:c}=k,f=a.session.updateAge,g=c.expires.valueOf()-1e3*m+1e3*f,l=cO(m);g<=Date.now()&&await j({sessionToken:o,expires:l});let p=await i.session({session:{...c,user:b},user:b,newSession:e,...d?{trigger:"update"}:{}});n.body=p,n.cookies?.push({name:a.cookies.sessionToken.name,value:o,options:{...a.cookies.sessionToken.options,expires:l}}),await h.session?.({session:p})}else o&&n.cookies?.push(...b.clean())}catch(a){j.error(new k.SessionTokenError(a))}return n}async function eS(a,b){let c,d,{logger:e,provider:f}=b,g=f.authorization?.url;if(!g||"authjs.dev"===g.host){let a=new URL(f.issuer),b=await da(a,{[cX]:f[bF],[cU]:!0}),c=await dd(a,b).catch(b=>{if(!(b instanceof TypeError)||"Invalid URL"!==b.message)throw b;throw TypeError(`Discovery request responded with an invalid issuer. expected: ${a}`)});if(!c.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");g=new URL(c.authorization_endpoint)}let h=g.searchParams,i=f.callbackUrl;!b.isOnRedirectProxy&&f.redirectProxyUrl&&(i=f.redirectProxyUrl,d=f.callbackUrl,e.debug("using redirect proxy",{redirect_uri:i,data:d}));let j=Object.assign({response_type:"code",client_id:f.clientId,redirect_uri:i,...f.authorization?.params},Object.fromEntries(f.authorization?.url.searchParams??[]),a);for(let a in j)h.set(a,j[a]);let k=[];f.authorization?.url.searchParams.get("response_mode")==="form_post"&&(b.cookies.state.options.sameSite="none",b.cookies.state.options.secure=!0,b.cookies.nonce.options.sameSite="none",b.cookies.nonce.options.secure=!0);let l=await ey.create(b,d);if(l&&(h.set("state",l.value),k.push(l.cookie)),f.checks?.includes("pkce"))if(c&&!c.code_challenge_methods_supported?.includes("S256"))"oidc"===f.type&&(f.checks=["nonce"]);else{let{value:a,cookie:c}=await ew.create(b);h.set("code_challenge",a),h.set("code_challenge_method","S256"),k.push(c)}let m=await ez.create(b);return m&&(h.set("nonce",m.value),k.push(m.cookie)),"oidc"!==f.type||g.searchParams.has("scope")||g.searchParams.set("scope","openid profile email"),e.debug("authorization url is ready",{url:g,cookies:k,provider:f}),{redirect:g.toString(),cookies:k}}async function eT(a,b){let c,{body:d}=a,{provider:e,callbacks:f,adapter:g}=b,h=(e.normalizeIdentifier??function(a){if(!a)throw Error("Missing email from request body.");let[b,c]=a.toLowerCase().trim().split("@");return c=c.split(",")[0],`${b}@${c}`})(d?.email),i={id:crypto.randomUUID(),email:h,emailVerified:null},j=await g.getUserByEmail(h)??i,l={providerAccountId:h,userId:j.id,type:"email",provider:e.id};try{c=await f.signIn({user:j,account:l,email:{verificationRequest:!0}})}catch(a){throw new k.AccessDenied(a)}if(!c)throw new k.AccessDenied("AccessDenied");if("string"==typeof c)return{redirect:await f.redirect({url:c,baseUrl:b.url.origin})};let{callbackUrl:m,theme:n}=b,o=await e.generateVerificationToken?.()??by(32),p=new Date(Date.now()+(e.maxAge??86400)*1e3),q=e.secret??b.secret,r=new URL(b.basePath,b.url.origin),s=e.sendVerificationRequest({identifier:h,token:o,expires:p,url:`${r}/callback/${e.id}?${new URLSearchParams({callbackUrl:m,token:o,email:h})}`,provider:e,theme:n,request:new Request(a.url,{headers:a.headers,method:a.method,body:"POST"===a.method?JSON.stringify(a.body??{}):void 0})}),t=g.createVerificationToken?.({identifier:h,token:await bx(`${o}${q}`),expires:p});return await Promise.all([s,t]),{redirect:`${r}/verify-request?${new URLSearchParams({provider:e.id,type:e.type})}`}}async function eU(a,b,c){let d=`${c.url.origin}${c.basePath}/signin`;if(!c.provider)return{redirect:d,cookies:b};switch(c.provider.type){case"oauth":case"oidc":{let{redirect:d,cookies:e}=await eS(a.query,c);return e&&b.push(...e),{redirect:d,cookies:b}}case"email":return{...await eT(a,c),cookies:b};default:return{redirect:d,cookies:b}}}async function eV(a,b,c){let{jwt:d,events:e,callbackUrl:f,logger:g,session:h}=c,i=b.value;if(!i)return{redirect:f,cookies:a};try{if("jwt"===h.strategy){let a=c.cookies.sessionToken.name,b=await d.decode({...d,token:i,salt:a});await e.signOut?.({token:b})}else{let a=await c.adapter?.deleteSession(i);await e.signOut?.({session:a})}}catch(a){g.error(new k.SignOutError(a))}return a.push(...b.clean()),{redirect:f,cookies:a}}async function eW(a,b){let{adapter:c,jwt:d,session:{strategy:e}}=a,f=b.value;if(!f)return null;if("jwt"===e){let b=a.cookies.sessionToken.name,c=await d.decode({...d,token:f,salt:b});if(c&&c.sub)return{id:c.sub,name:c.name,email:c.email,image:c.picture}}else{let a=await c?.getSessionAndUser(f);if(a)return a.user}return null}async function eX(a,b,c,d){let e=eL(b),{provider:f}=e,{action:g}=a.query??{};if("register"!==g&&"authenticate"!==g&&void 0!==g)return{status:400,body:{error:"Invalid action"},cookies:d,headers:{"Content-Type":"application/json"}};let h=await eW(b,c),i=h?{user:h,exists:!0}:await f.getUserInfo(b,a),j=i?.user;switch(function(a,b,c){let{user:d,exists:e=!1}=c??{};switch(a){case"authenticate":return"authenticate";case"register":if(d&&b===e)return"register";break;case void 0:if(!b)if(!d)return"authenticate";else if(e)return"authenticate";else return"register"}return null}(g,!!h,i)){case"authenticate":return eG(e,a,j,d);case"register":if("string"==typeof j?.email)return eF(e,a,j,d);break;default:return{status:400,body:{error:"Invalid request"},cookies:d,headers:{"Content-Type":"application/json"}}}}async function eY(a,b){let{action:c,providerId:d,error:e,method:f}=a,g=b.skipCSRFCheck===bD,{options:h,cookies:i}=await bM({authOptions:b,action:c,providerId:d,url:a.url,callbackUrl:a.body?.callbackUrl??a.query?.callbackUrl,csrfToken:a.body?.csrfToken,cookies:a.cookies,isPost:"POST"===f,csrfDisabled:g}),l=new j(h.cookies.sessionToken,a.cookies,h.logger);if("GET"===f){let b=cN({...h,query:a.query,cookies:i});switch(c){case"callback":return await eP(a,h,l,i);case"csrf":return b.csrf(g,h,i);case"error":return b.error(e);case"providers":return b.providers(h.providers);case"session":return await eR(h,l,i);case"signin":return b.signin(d,e);case"signout":return b.signout();case"verify-request":return b.verifyRequest();case"webauthn-options":return await eX(a,h,l,i)}}else{let{csrfTokenVerified:b}=h;switch(c){case"callback":return"credentials"===h.provider.type&&bA(c,b),await eP(a,h,l,i);case"session":return bA(c,b),await eR(h,l,i,!0,a.body?.data);case"signin":return bA(c,b),await eU(a,i,h);case"signout":return bA(c,b),await eV(i,l,h)}}throw new k.UnknownAction(`Cannot handle action: ${c}`)}function eZ(a,b,c,d,e){let f,g=e?.basePath,h=d.AUTH_URL??d.NEXTAUTH_URL;if(h)f=new URL(h),g&&"/"!==g&&"/"!==f.pathname&&(f.pathname!==g&&bq(e).warn("env-url-basepath-mismatch"),f.pathname="/");else{let a=c.get("x-forwarded-host")??c.get("host"),d=c.get("x-forwarded-proto")??b??"https",e=d.endsWith(":")?d:d+":";f=new URL(`${e}//${a}`)}let i=f.toString().replace(/\/$/,"");if(g){let b=g?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${i}/${b}/${a}`)}return new URL(`${i}/${a}`)}async function e$(a,b){let c=bq(b),d=await bv(a,b);if(!d)return Response.json("Bad request.",{status:400});let e=function(a,b){let{url:c}=a,d=[];if(!l&&b.debug&&d.push("debug-enabled"),!b.trustHost)return new k.UntrustedHost(`Host must be trusted. URL was: ${a.url}`);if(!b.secret?.length)return new k.MissingSecret("Please define a `secret`");let e=a.query?.callbackUrl;if(e&&!m(e,c.origin))return new k.InvalidCallbackUrl(`Invalid callback URL. Received: ${e}`);let{callbackUrl:f}=i(b.useSecureCookies??"https:"===c.protocol),g=a.cookies?.[b.cookies?.callbackUrl?.name??f.name];if(g&&!m(g,c.origin))return new k.InvalidCallbackUrl(`Invalid callback URL. Received: ${g}`);let h=!1;for(let a of b.providers){let b="function"==typeof a?a():a;if(("oauth"===b.type||"oidc"===b.type)&&!(b.issuer??b.options?.issuer)){let a,{authorization:c,token:d,userinfo:e}=b;if("string"==typeof c||c?.url?"string"==typeof d||d?.url?"string"==typeof e||e?.url||(a="userinfo"):a="token":a="authorization",a)return new k.InvalidEndpoints(`Provider "${b.id}" is missing both \`issuer\` and \`${a}\` endpoint config. At least one of them is required`)}if("credentials"===b.type)n=!0;else if("email"===b.type)o=!0;else if("webauthn"===b.type){var j;if(p=!0,b.simpleWebAuthnBrowserVersion&&(j=b.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(j)))return new k.AuthError(`Invalid provider config for "${b.id}": simpleWebAuthnBrowserVersion "${b.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(b.enableConditionalUI){if(h)return new k.DuplicateConditionalUI("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(h=!0,!Object.values(b.formFields).some(a=>a.autocomplete&&a.autocomplete.toString().indexOf("webauthn")>-1))return new k.MissingWebAuthnAutocomplete(`Provider "${b.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(n){let a=b.session?.strategy==="database",c=!b.providers.some(a=>"credentials"!==("function"==typeof a?a():a).type);if(a&&c)return new k.UnsupportedStrategy("Signing in with credentials only supported if JWT strategy is enabled");if(b.providers.some(a=>{let b="function"==typeof a?a():a;return"credentials"===b.type&&!b.authorize}))return new k.MissingAuthorize("Must define an authorize() handler to use credentials authentication provider")}let{adapter:t,session:u}=b,v=[];if(o||u?.strategy==="database"||!u?.strategy&&t)if(o){if(!t)return new k.MissingAdapter("Email login requires an adapter");v.push(...q)}else{if(!t)return new k.MissingAdapter("Database session requires an adapter");v.push(...r)}if(p){if(!b.experimental?.enableWebAuthn)return new k.ExperimentalFeatureNotEnabled("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(d.push("experimental-webauthn"),!t)return new k.MissingAdapter("WebAuthn requires an adapter");v.push(...s)}if(t){let a=v.filter(a=>!(a in t));if(a.length)return new k.MissingAdapterMethods(`Required adapter methods were missing: ${a.join(", ")}`)}return l||(l=!0),d}(d,b);if(Array.isArray(e))e.forEach(c.warn);else if(e){if(c.error(e),!new Set(["signin","signout","error","verify-request"]).has(d.action)||"GET"!==d.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:a,theme:f}=b,g=a?.error&&d.url.searchParams.get("callbackUrl")?.startsWith(a.error);if(!a?.error||g)return g&&c.error(new k.ErrorPageLoop(`The error page ${a?.error} should not require authentication`)),bw(cN({theme:f}).error("Configuration"));let h=`${d.url.origin}${a.error}?error=Configuration`;return Response.redirect(h)}let f=a.headers?.has("X-Auth-Return-Redirect"),g=b.raw===bE;try{let a=await eY(d,b);if(g)return a;let c=bw(a),e=c.headers.get("Location");if(!f||!e)return c;return Response.json({url:e},{headers:c.headers})}catch(m){c.error(m);let e=m instanceof k.AuthError;if(e&&g&&!f)throw m;if("POST"===a.method&&"session"===d.action)return Response.json(null,{status:400});let h=new URLSearchParams({error:(0,k.isClientError)(m)?m.type:"Configuration"});m instanceof k.CredentialsSignin&&h.set("code",m.code);let i=e&&m.kind||"error",j=b.pages?.[i]??`${b.basePath}/${i.toLowerCase()}`,l=`${d.url.origin}${j}?${h}`;if(f)return Response.json({url:l});return Response.redirect(l)}}var e_=a.i(30304);function e0(a){let b=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!b)return a;let{origin:c}=new URL(b),{href:d,origin:e}=a.nextUrl;return new e_.NextRequest(d.replace(e,c),a)}function e1(a){try{a.secret??(a.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let b=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!b)return;let{pathname:c}=new URL(b);if("/"===c)return;a.basePath||(a.basePath=c)}catch{}finally{a.basePath||(a.basePath="/api/auth"),function(a,b,c=!1){try{let d=a.AUTH_URL;d&&(b.basePath?c||bq(b).warn("env-url-basepath-redundant"):b.basePath=new URL(d).pathname)}catch{}finally{b.basePath??(b.basePath="/auth")}if(!b.secret?.length){b.secret=[];let c=a.AUTH_SECRET;for(let d of(c&&b.secret.push(c),[1,2,3])){let c=a[`AUTH_SECRET_${d}`];c&&b.secret.unshift(c)}}b.redirectProxyUrl??(b.redirectProxyUrl=a.AUTH_REDIRECT_PROXY_URL),b.trustHost??(b.trustHost=!!(a.AUTH_URL??a.AUTH_TRUST_HOST??a.VERCEL??a.CF_PAGES??"production"!==a.NODE_ENV)),b.providers=b.providers.map(b=>{let{id:c}="function"==typeof b?b({}):b,d=c.toUpperCase().replace(/-/g,"_"),e=a[`AUTH_${d}_ID`],f=a[`AUTH_${d}_SECRET`],g=a[`AUTH_${d}_ISSUER`],h=a[`AUTH_${d}_KEY`],i="function"==typeof b?b({clientId:e,clientSecret:f,issuer:g,apiKey:h}):b;return"oauth"===i.type||"oidc"===i.type?(i.clientId??(i.clientId=e),i.clientSecret??(i.clientSecret=f),i.issuer??(i.issuer=g)):"email"===i.type&&(i.apiKey??(i.apiKey=h)),i})}(process.env,a,!0)}}var e2=a.i(39804);async function e3(a,b){return e$(new Request(eZ("session",a.get("x-forwarded-proto"),a,process.env,b),{headers:{cookie:a.get("cookie")??""}}),{...b,callbacks:{...b.callbacks,async session(...a){let c=await b.callbacks?.session?.(...a)??{...a[0].session,expires:a[0].session.expires?.toISOString?.()??a[0].session.expires};return{user:a[0].user??a[0].token,...c}}}})}function e4(a){return"function"==typeof a}function e5(a,b){return"function"==typeof a?async(...c)=>{if(!c.length){let c=await (0,e2.headers)(),d=await a(void 0);return b?.(d),e3(c,d).then(a=>a.json())}if(c[0]instanceof Request){let d=c[0],e=c[1],f=await a(d);return b?.(f),e6([d,e],f)}if(e4(c[0])){let d=c[0];return async(...c)=>{let e=await a(c[0]);return b?.(e),e6(c,e,d)}}let d="req"in c[0]?c[0].req:c[0],e="res"in c[0]?c[0].res:c[1],f=await a(d);return b?.(f),e3(new Headers(d.headers),f).then(async a=>{let b=await a.json();for(let b of a.headers.getSetCookie())"headers"in e?e.headers.append("set-cookie",b):e.appendHeader("set-cookie",b);return b})}:(...b)=>{if(!b.length)return Promise.resolve((0,e2.headers)()).then(b=>e3(b,a).then(a=>a.json()));if(b[0]instanceof Request)return e6([b[0],b[1]],a);if(e4(b[0])){let c=b[0];return async(...b)=>e6(b,a,c).then(a=>a)}let c="req"in b[0]?b[0].req:b[0],d="res"in b[0]?b[0].res:b[1];return e3(new Headers(c.headers),a).then(async a=>{let b=await a.json();for(let b of a.headers.getSetCookie())"headers"in d?d.headers.append("set-cookie",b):d.appendHeader("set-cookie",b);return b})}}async function e6(a,b,c){let d=e0(a[0]),e=await e3(d.headers,b),f=await e.json(),g=!0;b.callbacks?.authorized&&(g=await b.callbacks.authorized({request:d,auth:f}));let h=e_.NextResponse.next?.();if(g instanceof Response){h=g;let a=g.headers.get("Location"),{pathname:c}=d.nextUrl;a&&function(a,b,c){let d=b.replace(`${a}/`,""),e=Object.values(c.pages??{});return(e7.has(d)||e.includes(b))&&b===a}(c,new URL(a).pathname,b)&&(g=!0)}else if(c)d.auth=f,h=await c(d,a[1])??e_.NextResponse.next();else if(!g){let a=b.pages?.signIn??`${b.basePath}/signin`;if(d.nextUrl.pathname!==a){let b=d.nextUrl.clone();b.pathname=a,b.searchParams.set("callbackUrl",d.nextUrl.href),h=e_.NextResponse.redirect(b)}}let i=new Response(h?.body,h);for(let a of e.headers.getSetCookie())i.headers.append("set-cookie",a);return i}let e7=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var e8=a.i(50395);async function e9(a,b={},c,d){let e=new Headers(await (0,e2.headers)()),{redirect:f=!0,redirectTo:g,...h}=b instanceof FormData?Object.fromEntries(b):b,i=g?.toString()??e.get("Referer")??"/",j=eZ("signin",e.get("x-forwarded-proto"),e,process.env,d);if(!a)return j.searchParams.append("callbackUrl",i),f&&(0,e8.redirect)(j.toString()),j.toString();let k=`${j}/${a}?${new URLSearchParams(c)}`,l={};for(let b of d.providers){let{options:c,...d}="function"==typeof b?b():b,e=c?.id??d.id;if(e===a){l={id:e,type:c?.type??d.type};break}}if(!l.id){let a=`${j}?${new URLSearchParams({callbackUrl:i})}`;return f&&(0,e8.redirect)(a),a}"credentials"===l.type&&(k=k.replace("signin","callback")),e.set("Content-Type","application/x-www-form-urlencoded");let m=new Request(k,{method:"POST",headers:e,body:new URLSearchParams({...h,callbackUrl:i})}),n=await e$(m,{...d,raw:bE,skipCSRFCheck:bD}),o=await (0,e2.cookies)();for(let a of n?.cookies??[])o.set(a.name,a.value,a.options);let p=(n instanceof Response?n.headers.get("Location"):n.redirect)??k;return f?(0,e8.redirect)(p):p}async function fa(a,b){let c=new Headers(await (0,e2.headers)());c.set("Content-Type","application/x-www-form-urlencoded");let d=eZ("signout",c.get("x-forwarded-proto"),c,process.env,b),e=new URLSearchParams({callbackUrl:a?.redirectTo??c.get("Referer")??"/"}),f=new Request(d,{method:"POST",headers:c,body:e}),g=await e$(f,{...b,raw:bE,skipCSRFCheck:bD}),h=await (0,e2.cookies)();for(let a of g?.cookies??[])h.set(a.name,a.value,a.options);return a?.redirect??!0?(0,e8.redirect)(g.redirect):g}async function fb(a,b){let c=new Headers(await (0,e2.headers)());c.set("Content-Type","application/json");let d=new Request(eZ("session",c.get("x-forwarded-proto"),c,process.env,b),{method:"POST",headers:c,body:JSON.stringify({data:a})}),e=await e$(d,{...b,raw:bE,skipCSRFCheck:bD}),f=await (0,e2.cookies)();for(let a of e?.cookies??[])f.set(a.name,a.value,a.options);return e.body}var fc=a.i(66446),fd=a.i(22171);let fe=fc.z.object({username:fc.z.string().min(1,"Username is required"),password:fc.z.string().min(1,"Password is required")}),{handlers:ff,signIn:fg,signOut:fh,auth:fi}=function(a){if("function"==typeof a){let b=async b=>{let c=await a(b);return e1(c),e$(e0(b),c)};return{handlers:{GET:b,POST:b},auth:e5(a,a=>e1(a)),signIn:async(b,c,d)=>{let e=await a(void 0);return e1(e),e9(b,c,d,e)},signOut:async b=>{let c=await a(void 0);return e1(c),fa(b,c)},unstable_update:async b=>{let c=await a(void 0);return e1(c),fb(b,c)}}}e1(a);let b=b=>e$(e0(b),a);return{handlers:{GET:b,POST:b},auth:e5(a),signIn:(b,c,d)=>e9(b,c,d,a),signOut:b=>fa(b,a),unstable_update:b=>fb(b,a)}}({providers:[{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:{name:"credentials",credentials:{username:{label:"Username",type:"text"},password:{label:"Password",type:"password"}},async authorize(a){try{let{username:b,password:c}=fe.parse(a),d=await fk.login({username:b,password:c}),e=(0,fd.decodeJwt)(d.access_token);if(!e)return null;let f=e.payload;return{id:f.sub,username:f.username,role:f.role,accessToken:d.access_token}}catch(a){return console.error("Erreur d'authentification:",a),null}}}}],pages:{signIn:"/auth/signin"},callbacks:{authorized:({auth:a})=>!!a,jwt:async({token:a,user:b})=>(b&&(a.username=b.username,a.role=b.role,a.accessToken=b.accessToken),a),session:async({session:a,token:b})=>(b&&(a.user.id=b.sub||"",a.user.username=b.username,a.user.role=b.role,a.accessToken=b.accessToken),a)},session:{strategy:"jwt"}}),fj=process.env.NEXT_PUBLIC_API_URL||"http://localhost:4000",fk=new class{baseUrl;constructor(a=fj){this.baseUrl=a}async request(a,b={}){let c=`${this.baseUrl}${a}`,d={headers:{"Content-Type":"application/json",...b.headers},...b};try{let a=await fetch(c,d);if(!a.ok){let b=await a.json().catch(()=>({message:"Une erreur est survenue",statusCode:a.status}));throw 401===a.status&&await fh({redirectTo:"/auth/signin"}),Error(b.message||`HTTP ${a.status}`)}return await a.json()}catch(a){if(console.log(a),a instanceof Error)throw a;throw Error("Erreur de connexion au serveur")}}async login(a){return this.request("/auth/login",{method:"POST",body:JSON.stringify(a)})}async register(a){return this.request("/auth/register",{method:"POST",body:JSON.stringify(a)})}async authenticatedRequest(a,b,c={}){return this.request(a,{...c,headers:{...c.headers,"Content-Type":"application/json",Authorization:`Bearer ${b}`}})}async getUsers(a){return this.authenticatedRequest("/users",a)}async getUser(a,b){return this.authenticatedRequest(`/users/${a}`,b)}}}];

//# sourceMappingURL=frontend_4857e852._.js.map