(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,57223,e=>{"use strict";e.s(["DollarSign",()=>t],57223);let t=(0,e.i(44571).default)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},65429,e=>{"use strict";e.s(["Label",()=>i],65429);var t=e.i(4051),r=e.i(38477),s=e.i(38909),n=r.forwardRef((e,r)=>(0,t.jsx)(s.Primitive.label,{...e,ref:r,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var a=e.i(41428);function i(e){let{className:r,...s}=e;return(0,t.jsx)(n,{"data-slot":"label",className:(0,a.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...s})}},44640,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ReflectAdapter",{enumerable:!0,get:function(){return s}});class s{static get(e,t,r){let s=Reflect.get(e,t,r);return"function"==typeof s?s.bind(e):s}static set(e,t,r,s){return Reflect.set(e,t,r,s)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},8356,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorageInstance",{enumerable:!0,get:function(){return s}});let s=(0,e.r(97918).createAsyncLocalStorage)()},62355,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorage",{enumerable:!0,get:function(){return s.afterTaskAsyncStorageInstance}});let s=e.r(8356)},17939,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return o},throwWithStaticGenerationBailoutError:function(){return a},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return i}});let s=e.r(85115),n=e.r(62355);function a(e,t){throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(e," couldn't be rendered statically because it used ").concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function i(e,t){throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(e,' with `dynamic = "error"` couldn\'t be rendered statically because it used ').concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function o(e,t){let r=Object.defineProperty(Error("Route ".concat(e.route,' used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(r,t),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=r),r}function c(){let e=n.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},93168,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{describeHasCheckingStringProperty:function(){return a},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return i}});let s=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return s.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function a(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let i=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},63470,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var o=a?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(s,i,o):s[i]=e[i]}return s.default=e,r&&r.set(e,s),s}(e.r(38477));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}let a={current:null},i="function"==typeof s.cache?s.cache:e=>e,o=console.warn;function c(e){return function(){for(var t=arguments.length,r=Array(t),s=0;s<t;s++)r[s]=arguments[s];o(e(...r))}}i(e=>{try{o(a.current)}finally{a.current=null}})},4467,e=>{"use strict";e.s(["useApi",()=>s]);var t=e.i(1269),r=e.i(1831);function s(){let{data:e}=(0,t.useSession)(),s=async function(t){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(null==e?void 0:e.accessToken))throw Error("Non authentifié");return r.apiService.authenticatedRequest(t,e.accessToken,s)};return{login:r.apiService.login.bind(r.apiService),register:r.apiService.register.bind(r.apiService),authenticatedRequest:s,getUsers:()=>s("/users"),getUser:e=>s("/users/".concat(e)),createUser:e=>s("/users",{method:"POST",body:JSON.stringify(e)}),updateUser:(e,t)=>s("/users/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteUser:e=>s("/users/".concat(e),{method:"DELETE"}),getSessions:()=>s("/sessions"),getSession:e=>s("/sessions/".concat(e)),createSession:e=>s("/sessions",{method:"POST",body:JSON.stringify(e)}),updateSession:(e,t)=>s("/sessions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteSession:e=>s("/sessions/".concat(e),{method:"DELETE"}),getCaisses:()=>s("/caisses"),getCaisse:e=>s("/caisses/".concat(e)),createCaisse:e=>s("/caisses",{method:"POST",body:JSON.stringify(e)}),updateCaisse:(e,t)=>s("/caisses/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteCaisse:e=>s("/caisses/".concat(e),{method:"DELETE"}),emargerCaisse:e=>s("/caisses/".concat(e,"/emarger"),{method:"POST"}),getReunions:()=>s("/reunions"),getReunion:e=>s("/reunions/".concat(e)),updateReunion:(e,t)=>s("/reunions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),getMembers:()=>s("/members"),getMember:e=>s("/members/".concat(e)),createMember:e=>s("/members",{method:"POST",body:JSON.stringify(e)}),updateMember:(e,t)=>s("/members/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteMember:e=>s("/members/".concat(e),{method:"DELETE"}),getMemberDebrief:(e,t)=>{let r=new URLSearchParams;(null==t?void 0:t.dateFrom)&&r.append("dateFrom",t.dateFrom),(null==t?void 0:t.dateTo)&&r.append("dateTo",t.dateTo),(null==t?void 0:t.sessionId)&&r.append("sessionId",t.sessionId);let n=r.toString()?"?".concat(r.toString()):"";return s("/members/".concat(e,"/debrief").concat(n))},createPayment:e=>s("/payments",{method:"POST",body:JSON.stringify(e)}),getSessionMembers:e=>s("/sessions/".concat(e,"/members")),addSessionMember:e=>s("/session-members",{method:"POST",body:JSON.stringify(e)}),removeSessionMember:(e,t)=>s("/sessions/".concat(e,"/members/").concat(t),{method:"DELETE"})}}},14545,e=>{"use strict";e.s(["ArrowLeft",()=>t],14545);let t=(0,e.i(44571).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},57494,e=>{"use strict";e.s(["default",()=>x]);var t=e.i(4051),r=e.i(38477),s=e.i(1269),n=e.i(57691),a=e.i(67967),i=e.i(78381),o=e.i(45086),c=e.i(14545),d=e.i(67435),l=e.i(57223),u=e.i(5085),m=e.i(85205),p=e.i(96134),h=e.i(75680),f=e.i(5647),g=e.i(4467);let b=o.z.object({annee:o.z.number().min(2020,"L'année doit être supérieure à 2020").max(2050,"L'année doit être inférieure à 2050"),dateDebut:o.z.string().min(1,"La date de début est requise"),dateFin:o.z.string().min(1,"La date de fin est requise"),partFixe:o.z.number().min(1,"La part fixe doit être supérieure à 0").max(1e6,"La part fixe ne peut pas dépasser 1,000,000 FCFA")}).refine(e=>{let t=new Date(e.dateDebut);return new Date(e.dateFin)>t},{message:"La date de fin doit être postérieure à la date de début",path:["dateFin"]});function x(){let{data:e}=(0,s.useSession)(),o=(0,n.useRouter)(),x=(0,g.useApi)(),[y,j]=(0,r.useState)(!1),[v,S]=(0,r.useState)(null),O=(null==e?void 0:e.user)&&"secretary_general"===e.user.role,C=(0,a.useForm)({resolver:(0,i.zodResolver)(b),defaultValues:{annee:new Date().getFullYear(),dateDebut:"",dateFin:"",partFixe:0}}),P=async e=>{if(!O)return void S("Vous n'avez pas les permissions pour créer une session");try{j(!0),S(null),await x.createSession(e),o.push("/dashboard/sessions")}catch(e){console.error("Erreur lors de la création:",e),S(e.message||"Une erreur est survenue lors de la création")}finally{j(!1)}};return O?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(m.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,t.jsx)(u.default,{href:"/dashboard/sessions",children:(0,t.jsx)(c.ArrowLeft,{className:"h-4 w-4"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Nouvelle Session"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Créer une nouvelle session de tontine"})]})]}),(0,t.jsxs)(h.Card,{children:[(0,t.jsxs)(h.CardHeader,{children:[(0,t.jsxs)(h.CardTitle,{className:"flex items-center gap-2",children:[(0,t.jsx)(d.Calendar,{className:"h-5 w-5"}),"Informations de la session"]}),(0,t.jsx)(h.CardDescription,{children:"Définissez les paramètres de la nouvelle session. Les réunions seront automatiquement générées pour chaque dimanche de la période."})]}),(0,t.jsx)(h.CardContent,{children:(0,t.jsx)(f.Form,{...C,children:(0,t.jsxs)("form",{onSubmit:C.handleSubmit(P),className:"space-y-6",children:[v&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,t.jsx)("p",{className:"text-sm text-red-600",children:v})}),(0,t.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,t.jsx)(f.FormField,{control:C.control,name:"annee",render:e=>{let{field:r}=e;return(0,t.jsxs)(f.FormItem,{children:[(0,t.jsx)(f.FormLabel,{children:"Année"}),(0,t.jsx)(f.FormControl,{children:(0,t.jsx)(p.Input,{type:"number",...r,onChange:e=>r.onChange(parseInt(e.target.value)||0)})}),(0,t.jsx)(f.FormDescription,{children:"L'année de la session de tontine"}),(0,t.jsx)(f.FormMessage,{})]})}}),(0,t.jsx)(f.FormField,{control:C.control,name:"partFixe",render:e=>{let{field:r}=e;return(0,t.jsxs)(f.FormItem,{children:[(0,t.jsxs)(f.FormLabel,{className:"flex items-center gap-2",children:[(0,t.jsx)(l.DollarSign,{className:"h-4 w-4"}),"Part Fixe (FCFA)"]}),(0,t.jsx)(f.FormControl,{children:(0,t.jsx)(p.Input,{type:"number",...r,onChange:e=>r.onChange(parseInt(e.target.value)||0)})}),(0,t.jsx)(f.FormDescription,{children:"Montant de la cotisation fixe par réunion"}),(0,t.jsx)(f.FormMessage,{})]})}})]}),(0,t.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,t.jsx)(f.FormField,{control:C.control,name:"dateDebut",render:e=>{let{field:r}=e;return(0,t.jsxs)(f.FormItem,{children:[(0,t.jsx)(f.FormLabel,{children:"Date de début"}),(0,t.jsx)(f.FormControl,{children:(0,t.jsx)(p.Input,{type:"date",...r})}),(0,t.jsx)(f.FormDescription,{children:"Date de début de la session"}),(0,t.jsx)(f.FormMessage,{})]})}}),(0,t.jsx)(f.FormField,{control:C.control,name:"dateFin",render:e=>{let{field:r}=e;return(0,t.jsxs)(f.FormItem,{children:[(0,t.jsx)(f.FormLabel,{children:"Date de fin"}),(0,t.jsx)(f.FormControl,{children:(0,t.jsx)(p.Input,{type:"date",...r})}),(0,t.jsx)(f.FormDescription,{children:"Date de fin de la session"}),(0,t.jsx)(f.FormMessage,{})]})}})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,t.jsx)(m.Button,{variant:"outline",asChild:!0,children:(0,t.jsx)(u.default,{href:"/dashboard/sessions",children:"Annuler"})}),(0,t.jsx)(m.Button,{type:"submit",disabled:y,children:y?"Création...":"Créer la session"})]})]})})})]}),(0,t.jsxs)(h.Card,{children:[(0,t.jsx)(h.CardHeader,{children:(0,t.jsx)(h.CardTitle,{children:"À propos des sessions"})}),(0,t.jsx)(h.CardContent,{children:(0,t.jsxs)("div",{className:"space-y-2 text-sm text-muted-foreground",children:[(0,t.jsx)("p",{children:"• Les réunions seront automatiquement créées pour chaque dimanche dans la période définie"}),(0,t.jsx)("p",{children:"• La part fixe représente le montant que chaque membre doit cotiser à chaque réunion"}),(0,t.jsx)("p",{children:"• Une fois créée, la session ne peut être modifiée que par un administrateur"}),(0,t.jsx)("p",{children:"• La suppression d'une session supprimera également toutes les réunions associées"})]})})]})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(m.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,t.jsx)(u.default,{href:"/dashboard/sessions",children:(0,t.jsx)(c.ArrowLeft,{className:"h-4 w-4"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Nouvelle Session"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Créer une nouvelle session de tontine"})]})]}),(0,t.jsx)(h.Card,{children:(0,t.jsx)(h.CardContent,{className:"pt-6",children:(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Vous n'avez pas les permissions pour créer une session."}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Seuls les administrateurs peuvent créer des sessions."})]})})})]})}}]);