import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

export enum PaymentDirection {
  IN = 'IN',
  OUT = 'OUT',
}

export enum PaymentFunction {
  CONTRIBUTION = 'cotisation',
  TRANSFER = 'transfert',
  EXTERNAL = 'exterieur',
}

export type PaymentDocument = HydratedDocument<Payment>;

@Schema({ timestamps: true })
export class Payment {
  // direction (incoming/outgoing)
  @Prop({ required: true, enum: [PaymentDirection.IN, PaymentDirection.OUT] })
  direction!: PaymentDirection;

  // business function (cotisation, transfert, exterieur)
  @Prop({ required: true, enum: [PaymentFunction.CONTRIBUTION, PaymentFunction.TRANSFER, PaymentFunction.EXTERNAL] })
  func!: PaymentFunction;

  // amount
  @Prop({ type: Number, required: true, min: 0 })
  amount!: number;

  // payment date (for traceability)
  @Prop({ type: Date, required: true, default: () => new Date() })
  date!: Date;

  // linked session if applicable
  @Prop({ type: Types.ObjectId, ref: 'Session', required: false })
  sessionId?: Types.ObjectId;

  // linked reunion if applicable
  @Prop({ type: Types.ObjectId, ref: 'Reunion', required: false })
  reunionId?: Types.ObjectId;

  // linked caisse (always required)
  @Prop({ type: Types.ObjectId, ref: 'Caisse', required: true })
  caisseId!: Types.ObjectId;

  // receiving caisse in case of transfert (optional)
  @Prop({ type: Types.ObjectId, ref: 'Caisse', required: false })
  receivingCaisseId?: Types.ObjectId;

  // optional member (contribution case)
  @Prop({ type: Types.ObjectId, ref: 'Member', required: false })
  memberId?: Types.ObjectId;

  // optional reason for external out payments
  @Prop({ type: String, required: false })
  reason?: string;

  // optional link to an exit order
  @Prop({ type: Types.ObjectId, ref: 'ExitOrder', required: false })
  exitOrderId?: Types.ObjectId;

  // created by user
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  createdBy!: Types.ObjectId;
}

export const PaymentSchema = SchemaFactory.createForClass(Payment);