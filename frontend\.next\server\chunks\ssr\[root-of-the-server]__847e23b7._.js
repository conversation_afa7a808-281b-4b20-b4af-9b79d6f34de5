module.exports=[56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},24361,(a,b,c)=>{b.exports=a.x("util",()=>require("util"))},874,(a,b,c)=>{b.exports=a.x("buffer",()=>require("buffer"))},88947,(a,b,c)=>{b.exports=a.x("stream",()=>require("stream"))},54799,(a,b,c)=>{b.exports=a.x("crypto",()=>require("crypto"))},9262,(a,b,c)=>{"use strict";b.exports=a.r(39665).vendored.contexts.AppRouterContext},37928,(a,b,c)=>{"use strict";b.exports=a.r(39665).vendored.contexts.HooksClientContext},91364,(a,b,c)=>{"use strict";b.exports=a.r(39665).vendored.contexts.ServerInsertedHtml},48986,(a,b,c)=>{"use strict";b.exports=a.r(39665).vendored["react-ssr"].ReactServerDOMTurbopackClient},60443,(a,b,c)=>{"use strict";b.exports=a.r(39665).vendored["react-ssr"].ReactDOM},70909,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HandleISRError",{enumerable:!0,get:function(){return e}});let d=a.r(56704).workAsyncStorage;function e(a){let{error:b}=a;if(d){let a=d.getStore();if((null==a?void 0:a.isRevalidate)||(null==a?void 0:a.isStaticGeneration))throw console.error(b),b}return null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},2590,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return g}});let d=a.r(68116),e=a.r(70909),f={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}},g=function(a){let{error:b}=a,c=null==b?void 0:b.digest;return(0,d.jsxs)("html",{id:"__next_error__",children:[(0,d.jsx)("head",{}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.HandleISRError,{error:b}),(0,d.jsx)("div",{style:f.error,children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{style:f.text,children:["Application error: a ",c?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",c?"server logs":"browser console"," for more information)."]}),c?(0,d.jsx)("p",{style:f.text,children:"Digest: "+c}):null]})})]})]})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},78184,a=>{"use strict";a.s(["Input",()=>d]);var b=a.i(68116),c=a.i(22171);function d({className:a,type:d,...e}){return(0,b.jsx)("input",{type:d,"data-slot":"input",className:(0,c.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...e})}},48206,a=>{"use strict";a.s(["Primitive",()=>f,"dispatchDiscreteCustomEvent",()=>g]);var b=a.i(128),c=a.i(60443),d=a.i(85689),e=a.i(68116),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,c)=>{let f=(0,d.createSlot)(`Primitive.${c}`),g=b.forwardRef((a,b)=>{let{asChild:d,...g}=a;return(0,e.jsx)(d?f:c,{...g,ref:b})});return g.displayName=`Primitive.${c}`,{...a,[c]:g}},{});function g(a,b){a&&c.flushSync(()=>a.dispatchEvent(b))}},41405,a=>{"use strict";a.s(["Label",()=>g],41405);var b=a.i(68116),c=a.i(128),d=a.i(48206),e=c.forwardRef((a,c)=>(0,b.jsx)(d.Primitive.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));e.displayName="Label";var f=a.i(22171);function g({className:a,...c}){return(0,b.jsx)(e,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...c})}},8064,a=>{"use strict";a.s(["default",()=>o]);var b=a.i(68116),c=a.i(128),d=a.i(81223),e=a.i(50395),f=a.i(64853),g=a.i(54636),h=a.i(66446),i=a.i(33055),j=a.i(2979),k=a.i(78184),l=a.i(75780),m=a.i(60563);let n=h.z.object({username:h.z.string().min(1,"Username is required"),password:h.z.string().min(4,"Password is required")});function o(){let[a,h]=(0,c.useState)(!1),[o,p]=(0,c.useState)(null),q=(0,e.useRouter)(),r=(0,f.useForm)({resolver:(0,g.zodResolver)(n),defaultValues:{username:"",password:""}}),s=async a=>{h(!0),p(null);try{let b=await (0,d.signIn)("credentials",{username:a.username,password:a.password,redirect:!1});b?.error?p("Nom d'utilisateur ou mot de passe incorrect"):q.push("/dashboard")}catch(a){console.error("Erreur de connexion:",a),p("Erreur de connexion au serveur. Veuillez réessayer.")}finally{h(!1)}};return(0,b.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,b.jsxs)(l.Card,{className:"w-full max-w-md",children:[(0,b.jsxs)(l.CardHeader,{className:"space-y-1",children:[(0,b.jsx)(l.CardTitle,{className:"text-2xl font-bold text-center",children:"Connexion"}),(0,b.jsx)(l.CardDescription,{className:"text-center",children:"Connectez-vous à votre compte Tontine"})]}),(0,b.jsxs)(l.CardContent,{children:[(0,b.jsx)(m.Form,{...r,children:(0,b.jsxs)("form",{onSubmit:r.handleSubmit(s),className:"space-y-4",children:[(0,b.jsx)(m.FormField,{control:r.control,name:"username",render:({field:c})=>(0,b.jsxs)(m.FormItem,{children:[(0,b.jsx)(m.FormLabel,{children:"Nom d'utilisateur"}),(0,b.jsx)(m.FormControl,{children:(0,b.jsx)(k.Input,{placeholder:"Entrez votre nom d'utilisateur",...c,disabled:a})}),(0,b.jsx)(m.FormMessage,{})]})}),(0,b.jsx)(m.FormField,{control:r.control,name:"password",render:({field:c})=>(0,b.jsxs)(m.FormItem,{children:[(0,b.jsx)(m.FormLabel,{children:"Mot de passe"}),(0,b.jsx)(m.FormControl,{children:(0,b.jsx)(k.Input,{type:"password",placeholder:"Entrez votre mot de passe",...c,disabled:a})}),(0,b.jsx)(m.FormMessage,{})]})}),o&&(0,b.jsx)("div",{className:"text-red-600 text-sm text-center",children:o}),(0,b.jsx)(j.Button,{type:"submit",className:"w-full",disabled:a,children:a?"Connexion...":"Se connecter"})]})}),(0,b.jsxs)("div",{className:"mt-4 text-center text-sm text-gray-600",children:["Pas encore de compte ?"," ",(0,b.jsx)(i.default,{href:"/auth/register",className:"text-blue-600 hover:underline",children:"Créer un compte"}),(0,b.jsx)("div",{className:"text-xs text-orange-600 mt-1",children:"(Temporaire - pour les tests)"})]})]})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__847e23b7._.js.map