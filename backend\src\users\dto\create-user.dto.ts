import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { UserRole } from '../../common/enums/user-role.enum';

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  readonly username!: string;

  @IsString()
  @MinLength(6)
  readonly password!: string;

  @IsEnum(UserRole)
  readonly role!: UserRole;
}