import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UserRole } from '../../common/enums/user-role.enum';
import { IsEnum, IsNotEmpty, IsString, MinLength, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateUserDto {
  @IsString()
  @ApiPropertyOptional({ description: 'Mot de passe (laisser vide si non modifié)', example: '' })
  password?: string;

  @IsEnum(UserRole)
  @IsNotEmpty()
  @ApiProperty({ enum: UserRole, description: 'Rôle de l\'utilisateur' })
  role!: UserRole;
}

export class UpdateUserWrapperDto {
  @IsString()
  @ApiProperty({ example: '64f8c2b7e1a2b3c4d5e6f7a8' })
  readonly _id!: string;

  @ValidateNested()
  @Type(() => UpdateUserDto)
  @ApiProperty({ type: UpdateUserDto })
  readonly data!: UpdateUserDto;
}