"use client";

import { useSession } from "next-auth/react";
import { Bell, Search } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SidebarTrigger } from "@/components/ui/sidebar";

export function Header() {
	const { data: session } = useSession();

	return (
		<header className="bg-white border-b border-gray-200 px-6 py-4">
			<div className="flex items-center justify-between">
				<div className="flex items-center space-x-4">
					<SidebarTrigger />
					<h2 className="text-lg font-semibold text-gray-900">Dashboard</h2>
				</div>

				<div className="flex items-center space-x-4">
					{/* Search */}
					<div className="relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<Input placeholder="Search..." className="pl-10 w-64" />
					</div>

					{/* Notifications */}
					<Button variant="ghost" size="icon">
						<Bell className="h-5 w-5" />
					</Button>

					{/* User info */}
					{session?.user && (
						<div className="flex items-center space-x-2">
							<div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
								<span className="text-white text-sm font-medium">
									{session.user.username?.charAt(0) || "U"}
								</span>
							</div>
							<span className="text-sm font-medium text-gray-700">
								{session.user.username}
							</span>
						</div>
					)}
				</div>
			</div>
		</header>
	);
}
