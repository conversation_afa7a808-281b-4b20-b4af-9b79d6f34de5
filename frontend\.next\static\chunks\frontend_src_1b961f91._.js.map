{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/auth.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from \"next-auth/providers/credentials\";\nimport { z } from \"zod\";\nimport { apiService } from \"./api\";\nimport { decodeJwt } from \"./utils\";\n\nconst loginSchema = z.object({\n\tusername: z.string().min(1, \"Username is required\"),\n\tpassword: z.string().min(1, \"Password is required\"),\n});\n\nexport const { handlers, signIn, signOut, auth } = NextAuth({\n\tproviders: [\n\t\tCredentialsProvider({\n\t\t\tname: \"credentials\",\n\t\t\tcredentials: {\n\t\t\t\tusername: { label: \"Username\", type: \"text\" },\n\t\t\t\tpassword: { label: \"Password\", type: \"password\" },\n\t\t\t},\n\t\t\tasync authorize(credentials) {\n\t\t\t\ttry {\n\t\t\t\t\tconst { username, password } = loginSchema.parse(credentials);\n\n\t\t\t\t\t// Authentification avec l'API backend\n\t\t\t\t\tconst response = await apiService.login({ username, password });\n\t\t\t\t\tconst jwt = decodeJwt(response.access_token);\n\t\t\t\t\tif(!jwt) return null;\n\t\t\t\t\tconst jwtPayload = jwt.payload as unknown as { sub: string; username: string; role:  'secretary_general' | 'controller' | 'cashier' };\n\t\t\t\t\t\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tid: jwtPayload.sub,\n\t\t\t\t\t\t\tusername: jwtPayload.username,\n\t\t\t\t\t\t\trole: jwtPayload.role,\n\t\t\t\t\t\t\taccessToken: response.access_token,\n\t\t\t\t\t}\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"Erreur d'authentification:\", error);\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\t\t}),\n\t],\n\tpages: {\n\t\tsignIn: \"/auth/signin\",\n\t},\n\tcallbacks: {\n\t\tauthorized: ({ auth }) => !!auth,\n\t\tasync jwt({ token, user }) {\n\t\t\tif (user) {\n\t\t\t\ttoken.username = user.username;\n\t\t\t\ttoken.role = user.role;\n\t\t\t\ttoken.accessToken = user.accessToken;\n\t\t\t}\n\t\t\treturn token;\n\t\t},\n\t\tasync session({ session, token }) {\n\t\t\tif (token) {\n\t\t\t\tsession.user.id = token.sub || \"\";\n\t\t\t\tsession.user.username = token.username as string;\n\t\t\t\tsession.user.role = token.role as  'secretary_general' | 'controller' | 'cashier';\n\t\t\t\tsession.accessToken = token.accessToken as string;\n\t\t\t}\n\t\t\treturn session;\n\t\t},\n\t},\n\tsession: {\n\t\tstrategy: \"jwt\",\n\t},\n});\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,cAAc,iPAAC,CAAC,MAAM,CAAC;IAC5B,UAAU,iPAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,UAAU,iPAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC7B;AAEO,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAA,2RAAQ,EAAC;IAC3D,WAAW;QACV,IAAA,gPAAmB,EAAC;YACnB,MAAM;YACN,aAAa;gBACZ,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAO;gBAC5C,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YACjD;YACA,MAAM,WAAU,WAAW;gBAC1B,IAAI;oBACH,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,YAAY,KAAK,CAAC;oBAEjD,sCAAsC;oBACtC,MAAM,WAAW,MAAM,8IAAU,CAAC,KAAK,CAAC;wBAAE;wBAAU;oBAAS;oBAC7D,MAAM,MAAM,IAAA,+IAAS,EAAC,SAAS,YAAY;oBAC3C,IAAG,CAAC,KAAK,OAAO;oBAChB,MAAM,aAAa,IAAI,OAAO;oBAE7B,OAAO;wBACN,IAAI,WAAW,GAAG;wBAClB,UAAU,WAAW,QAAQ;wBAC7B,MAAM,WAAW,IAAI;wBACrB,aAAa,SAAS,YAAY;oBACpC;gBAED,EAAE,OAAO,OAAO;oBACf,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,OAAO;gBACR;YACD;QACD;KACA;IACD,OAAO;QACN,QAAQ;IACT;IACA,WAAW;QACV,YAAY;gBAAC,EAAE,IAAI,EAAE;mBAAK,CAAC,CAAC;;QAC5B,MAAM,KAAI,KAAe;gBAAf,EAAE,KAAK,EAAE,IAAI,EAAE,GAAf;YACT,IAAI,MAAM;gBACT,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,WAAW,GAAG,KAAK,WAAW;YACrC;YACA,OAAO;QACR;QACA,MAAM,SAAQ,KAAkB;gBAAlB,EAAE,OAAO,EAAE,KAAK,EAAE,GAAlB;YACb,IAAI,OAAO;gBACV,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI;gBAC/B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,WAAW,GAAG,MAAM,WAAW;YACxC;YACA,OAAO;QACR;IACD;IACA,SAAS;QACR,UAAU;IACX;AACD", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/api.ts"], "sourcesContent": ["import { signOut } from \"next-auth/react\";\nimport { signOut as ServerSignOut } from \"./auth\";\nimport { Caisse, Session } from \"@/types\";\n\n// Configuration de l'API\nexport const API_BASE_URL =\n\tprocess.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000\";\n\n// Types pour l'authentification\nexport interface LoginRequest {\n\tusername: string;\n\tpassword: string;\n}\n\nexport interface LoginResponse {\n\taccess_token: string;\n\tuser: {\n\t\tid: string;\n\t\tusername: string;\n\t\tnom: string;\n\t\tprenom: string;\n\t\temail: string;\n\t\trole: \"admin\" | \"tresorier\" | \"membre\";\n\t\tstatut: \"actif\" | \"en_attente\" | \"suspendu\";\n\t};\n}\n\nexport interface RegisterRequest {\n\tusername: string;\n\tpassword: string;\n\trole: string;\n\t// statut?: \"actif\" | \"en_attente\" | \"suspendu\";\n}\n\nexport interface ApiError {\n\tmessage: string;\n\tstatusCode: number;\n\terror?: string;\n}\n\n// Service API\nexport class ApiService {\n\tprivate baseUrl: string;\n\n\tconstructor(baseUrl: string = API_BASE_URL) {\n\t\tthis.baseUrl = baseUrl;\n\t}\n\n\tprivate async request<T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\tconst url = `${this.baseUrl}${endpoint}`;\n\n\t\tconst config: RequestInit = {\n\t\t\theaders: {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t...options.headers,\n\t\t\t},\n\t\t\t...options,\n\t\t};\n\n\t\ttry {\n\t\t\tconst response = await fetch(url, config);\n\n\t\t\tif (!response.ok) {\n\t\t\t\tconst errorData: ApiError = await response.json().catch(() => ({\n\t\t\t\t\tmessage: \"Une erreur est survenue\",\n\t\t\t\t\tstatusCode: response.status,\n\t\t\t\t}));\n\n\t\t\t\t// 🚨 Gestion du cas 401\n\t\t\t\tif (response.status === 401) {\n\t\t\t\t\t// Si côté client → on déconnecte\n\t\t\t\t\tif (typeof window !== \"undefined\") {\n\t\t\t\t\t\tawait signOut({ callbackUrl: \"/auth/signin\" });\n\t\t\t\t\t} else {\n\t\t\t\t\t\tawait ServerSignOut({ redirectTo: \"/auth/signin\" });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthrow new Error(errorData.message || `HTTP ${response.status}`);\n\t\t\t}\n\n\t\t\treturn await response.json();\n\t\t} catch (error) {\n\t\t\tconsole.log(error);\n\n\t\t\tif (error instanceof Error) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t\tthrow new Error(\"Erreur de connexion au serveur\");\n\t\t}\n\t}\n\n\t// Authentification\n\tasync login(credentials: LoginRequest): Promise<LoginResponse> {\n\t\treturn this.request<LoginResponse>(\"/auth/login\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(credentials),\n\t\t});\n\t}\n\n\tasync register(\n\t\tuserData: RegisterRequest,\n\t): Promise<{ message: string; user: any }> {\n\t\treturn this.request<{ message: string; user: any }>(\"/auth/register\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(userData),\n\t\t});\n\t}\n\n\t// Méthodes avec authentification\n\tasync authenticatedRequest<T>(\n\t\tendpoint: string,\n\t\ttoken: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\treturn this.request<T>(endpoint, {\n\t\t\t...options,\n\t\t\theaders: {\n\t\t\t\t...options.headers,\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\tAuthorization: `Bearer ${token}`,\n\t\t\t},\n\t\t});\n\t}\n\n\t// Utilisateurs\n\tasync getUsers(token: string): Promise<any[]> {\n\t\treturn this.authenticatedRequest<any[]>(\"/users\", token);\n\t}\n\n\tasync getUser(id: string, token: string): Promise<any> {\n\t\treturn this.authenticatedRequest<any>(`/users/${id}`, token);\n\t}\n\n\tasync getSessions(token: string): Promise<Session[]> {\n\t\treturn this.authenticatedRequest<Session[]>(\"/sessions\", token);\n\t}\n\n\tasync getCaisses(token: string): Promise<Caisse[]> {\n\t\treturn this.authenticatedRequest<Caisse[]>(\"/caisses\", token);\n\t}\n}\n\n// Instance par défaut\nexport const apiService = new ApiService();\n"], "names": [], "mappings": ";;;;;;;;AAMC;;AAND;AACA;;;;AAIO,MAAM,eACZ,sTAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAmC7B,MAAM;IAOZ,MAAc,QACb,QAAgB,EAEH;YADb,UAAA,iEAAuB,CAAC;QAExB,MAAM,MAAM,AAAC,GAAiB,OAAf,IAAI,CAAC,OAAO,EAAY,OAAT;QAE9B,MAAM,SAAsB;YAC3B,SAAS;gBACR,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACnB;YACA,GAAG,OAAO;QACX;QAEA,IAAI;YACH,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACjB,MAAM,YAAsB,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;wBAC9D,SAAS;wBACT,YAAY,SAAS,MAAM;oBAC5B,CAAC;gBAED,wBAAwB;gBACxB,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC5B,iCAAiC;oBACjC,wCAAmC;wBAClC,MAAM,IAAA,2QAAO,EAAC;4BAAE,aAAa;wBAAe;oBAC7C;;gBAGD;gBACA,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,QAAuB,OAAhB,SAAS,MAAM;YAC7D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC3B,EAAE,OAAO,OAAO;YACf,QAAQ,GAAG,CAAC;YAEZ,IAAI,iBAAiB,OAAO;gBAC3B,MAAM;YACP;YACA,MAAM,IAAI,MAAM;QACjB;IACD;IAEA,mBAAmB;IACnB,MAAM,MAAM,WAAyB,EAA0B;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAgB,eAAe;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,MAAM,SACL,QAAyB,EACiB;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAiC,kBAAkB;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,iCAAiC;IACjC,MAAM,qBACL,QAAgB,EAChB,KAAa,EAEA;YADb,UAAA,iEAAuB,CAAC;QAExB,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAChC,GAAG,OAAO;YACV,SAAS;gBACR,GAAG,QAAQ,OAAO;gBAClB,gBAAgB;gBAChB,eAAe,AAAC,UAAe,OAAN;YAC1B;QACD;IACD;IAEA,eAAe;IACf,MAAM,SAAS,KAAa,EAAkB;QAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAQ,UAAU;IACnD;IAEA,MAAM,QAAQ,EAAU,EAAE,KAAa,EAAgB;QACtD,OAAO,IAAI,CAAC,oBAAoB,CAAM,AAAC,UAAY,OAAH,KAAM;IACvD;IAEA,MAAM,YAAY,KAAa,EAAsB;QACpD,OAAO,IAAI,CAAC,oBAAoB,CAAY,aAAa;IAC1D;IAEA,MAAM,WAAW,KAAa,EAAqB;QAClD,OAAO,IAAI,CAAC,oBAAoB,CAAW,YAAY;IACxD;IAlGA,YAAY,UAAkB,YAAY,CAAE;QAF5C,yPAAQ,WAAR,KAAA;QAGC,IAAI,CAAC,OAAO,GAAG;IAChB;AAiGD;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/use-api.ts"], "sourcesContent": ["import { useSession } from \"next-auth/react\";\nimport { apiService } from \"@/lib/api\";\nimport {\n\tSession,\n\tCaisse,\n\tReunion,\n\tMember,\n\tPayment,\n\tSessionMember,\n\tCreateSessionDto,\n\tUpdateSessionDto,\n\tCreateCaisseDto,\n\tUpdateCaisseDto,\n\tUpdateReunionDto,\n\tCreateMemberDto,\n\tUpdateMemberDto,\n\tCreatePaymentDto,\n\tCreateSessionMemberDto,\n\tMemberDebrief,\n\tPaymentFilters,\n} from \"@/types\";\n\nexport function useApi() {\n\tconst { data: session } = useSession();\n\n\tconst authenticatedRequest = async <T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> => {\n\t\tif (!session?.accessToken) {\n\t\t\tthrow new Error(\"Non authentifié\");\n\t\t}\n\n\t\treturn apiService.authenticatedRequest<T>(\n\t\t\tendpoint,\n\t\t\tsession.accessToken,\n\t\t\toptions,\n\t\t);\n\t};\n\n\treturn {\n\t\t// Méthodes d'authentification (pas besoin de token)\n\t\tlogin: apiService.login.bind(apiService),\n\t\tregister: apiService.register.bind(apiService),\n\n\t\t// Méthodes authentifiées\n\t\tauthenticatedRequest,\n\n\t\t// Raccourcis pour les endpoints courants\n\t\tgetUsers: () => authenticatedRequest<any[]>(\"/users\"),\n\t\tgetUser: (id: string) => authenticatedRequest<any>(`/users/${id}`),\n\t\tcreateUser: (userData: any) =>\n\t\t\tauthenticatedRequest<any>(\"/users\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tupdateUser: (id: string, userData: any) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tdeleteUser: (id: string) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Sessions\n\t\tgetSessions: () => authenticatedRequest<Session[]>(\"/sessions\"),\n\t\tgetSession: (id: string) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`),\n\t\tcreateSession: (sessionData: CreateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(\"/sessions\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tupdateSession: (id: string, sessionData: UpdateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tdeleteSession: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Caisses\n\t\tgetCaisses: () => authenticatedRequest<Caisse[]>(\"/caisses\"),\n\t\tgetCaisse: (id: string) => authenticatedRequest<Caisse>(`/caisses/${id}`),\n\t\tcreateCaisse: (caisseData: CreateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(\"/caisses\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tupdateCaisse: (id: string, caisseData: UpdateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tdeleteCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\temargerCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}/emarger`, {\n\t\t\t\tmethod: \"POST\",\n\t\t\t}),\n\n\t\t// Réunions\n\t\tgetReunions: () => authenticatedRequest<Reunion[]>(\"/reunions\"),\n\t\tgetReunion: (id: string) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`),\n\t\tupdateReunion: (id: string, reunionData: UpdateReunionDto) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(reunionData),\n\t\t\t}),\n\n\t\t// Members\n\t\tgetMembers: () => authenticatedRequest<Member[]>(\"/members\"),\n\t\tgetMember: (id: string) => authenticatedRequest<Member>(`/members/${id}`),\n\t\tcreateMember: (memberData: CreateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(\"/members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tupdateMember: (id: string, memberData: UpdateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(`/members/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tdeleteMember: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/members/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\tgetMemberDebrief: (id: string, filters?: PaymentFilters) => {\n\t\t\tconst params = new URLSearchParams();\n\t\t\tif (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);\n\t\t\tif (filters?.dateTo) params.append('dateTo', filters.dateTo);\n\t\t\tif (filters?.sessionId) params.append('sessionId', filters.sessionId);\n\t\t\tconst query = params.toString() ? `?${params.toString()}` : '';\n\t\t\treturn authenticatedRequest<MemberDebrief>(`/members/${id}/debrief${query}`);\n\t\t},\n\n\t\t// Payments\n\t\tcreatePayment: (paymentData: CreatePaymentDto) =>\n\t\t\tauthenticatedRequest<Payment>(\"/payments\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(paymentData),\n\t\t\t}),\n\n\t\t// Session Members (inscription des membres aux sessions)\n\t\tgetSessionMembers: (sessionId: string) =>\n\t\t\tauthenticatedRequest<SessionMember[]>(`/sessions/${sessionId}/members`),\n\t\taddSessionMember: (sessionMemberData: CreateSessionMemberDto) =>\n\t\t\tauthenticatedRequest<SessionMember>(\"/session-members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionMemberData),\n\t\t\t}),\n\t\tremoveSessionMember: (sessionId: string, memberId: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${sessionId}/members/${memberId}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t};\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAqBO,SAAS;;IACf,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IAEpC,MAAM,uBAAuB,eAC5B;YACA,2EAAuB,CAAC;QAExB,IAAI,EAAC,oBAAA,8BAAA,QAAS,WAAW,GAAE;YAC1B,MAAM,IAAI,MAAM;QACjB;QAEA,OAAO,8IAAU,CAAC,oBAAoB,CACrC,UACA,QAAQ,WAAW,EACnB;IAEF;IAEA,OAAO;QACN,oDAAoD;QACpD,OAAO,8IAAU,CAAC,KAAK,CAAC,IAAI,CAAC,8IAAU;QACvC,UAAU,8IAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,8IAAU;QAE7C,yBAAyB;QACzB;QAEA,yCAAyC;QACzC,UAAU,IAAM,qBAA4B;QAC5C,SAAS,CAAC,KAAe,qBAA0B,AAAC,UAAY,OAAH;QAC7D,YAAY,CAAC,WACZ,qBAA0B,UAAU;gBACnC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,YAAY,CAAC,IAAY,WACxB,qBAA0B,AAAC,UAAY,OAAH,KAAM;gBACzC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,YAAY,CAAC,KACZ,qBAA0B,AAAC,UAAY,OAAH,KAAM;gBACzC,QAAQ;YACT;QAED,WAAW;QACX,aAAa,IAAM,qBAAgC;QACnD,YAAY,CAAC,KACZ,qBAA8B,AAAC,aAAe,OAAH;QAC5C,eAAe,CAAC,cACf,qBAA8B,aAAa;gBAC1C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,eAAe,CAAC,IAAY,cAC3B,qBAA8B,AAAC,aAAe,OAAH,KAAM;gBAChD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,eAAe,CAAC,KACf,qBAA2B,AAAC,aAAe,OAAH,KAAM;gBAC7C,QAAQ;YACT;QAED,UAAU;QACV,YAAY,IAAM,qBAA+B;QACjD,WAAW,CAAC,KAAe,qBAA6B,AAAC,YAAc,OAAH;QACpE,cAAc,CAAC,aACd,qBAA6B,YAAY;gBACxC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,IAAY,aAC1B,qBAA6B,AAAC,YAAc,OAAH,KAAM;gBAC9C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,KACd,qBAA2B,AAAC,YAAc,OAAH,KAAM;gBAC5C,QAAQ;YACT;QACD,eAAe,CAAC,KACf,qBAA6B,AAAC,YAAc,OAAH,IAAG,aAAW;gBACtD,QAAQ;YACT;QAED,WAAW;QACX,aAAa,IAAM,qBAAgC;QACnD,YAAY,CAAC,KACZ,qBAA8B,AAAC,aAAe,OAAH;QAC5C,eAAe,CAAC,IAAY,cAC3B,qBAA8B,AAAC,aAAe,OAAH,KAAM;gBAChD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QAED,UAAU;QACV,YAAY,IAAM,qBAA+B;QACjD,WAAW,CAAC,KAAe,qBAA6B,AAAC,YAAc,OAAH;QACpE,cAAc,CAAC,aACd,qBAA6B,YAAY;gBACxC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,IAAY,aAC1B,qBAA6B,AAAC,YAAc,OAAH,KAAM;gBAC9C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,KACd,qBAA2B,AAAC,YAAc,OAAH,KAAM;gBAC5C,QAAQ;YACT;QACD,kBAAkB,CAAC,IAAY;YAC9B,MAAM,SAAS,IAAI;YACnB,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YACjE,IAAI,oBAAA,8BAAA,QAAS,MAAM,EAAE,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YAC3D,IAAI,oBAAA,8BAAA,QAAS,SAAS,EAAE,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;YACpE,MAAM,QAAQ,OAAO,QAAQ,KAAK,AAAC,IAAqB,OAAlB,OAAO,QAAQ,MAAO;YAC5D,OAAO,qBAAoC,AAAC,YAAwB,OAAb,IAAG,YAAgB,OAAN;QACrE;QAEA,WAAW;QACX,eAAe,CAAC,cACf,qBAA8B,aAAa;gBAC1C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QAED,yDAAyD;QACzD,mBAAmB,CAAC,YACnB,qBAAsC,AAAC,aAAsB,OAAV,WAAU;QAC9D,kBAAkB,CAAC,oBAClB,qBAAoC,oBAAoB;gBACvD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,qBAAqB,CAAC,WAAmB,WACxC,qBAA2B,AAAC,aAAiC,OAArB,WAAU,aAAoB,OAAT,WAAY;gBACxE,QAAQ;YACT;IACF;AACD;GA7IgB;;QACW,8QAAU", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/queries/use-sessions.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\nimport { useSession } from \"next-auth/react\";\nimport { useApi } from \"@/hooks/use-api\";\nimport type { Session, CreateSessionDto, UpdateSessionDto } from \"@/types\";\n\n// Query Keys\nexport const sessionKeys = {\n\tall: [\"sessions\"] as const,\n\tlists: () => [...sessionKeys.all, \"list\"] as const,\n\tlist: (filters: Record<string, any>) =>\n\t\t[...sessionKeys.lists(), { filters }] as const,\n\tdetails: () => [...sessionKeys.all, \"detail\"] as const,\n\tdetail: (id: string) => [...sessionKeys.details(), id] as const,\n\tmembers: (id: string) => [...sessionKeys.detail(id), \"members\"] as const,\n};\n\n// Hooks\nexport function useSessions() {\n\tconst { data: session } = useSession();\n\tconst api = useApi();\n\n\treturn useQuery({\n\t\tqueryKey: sessionKeys.lists(),\n\t\tqueryFn: () => api.getSessions(),\n\t\tenabled: !!session?.accessToken,\n\t\tstaleTime: 5 * 60 * 1000, // 5 minutes\n\t});\n}\n\nexport function useSessionDetail(sessionId: string) {\n\tconst { data: session } = useSession();\n\tconst api = useApi();\n\n\treturn useQuery({\n\t\tqueryKey: sessionKeys.detail(sessionId),\n\t\tqueryFn: () => api.getSession(sessionId),\n\t\tenabled: !!session?.accessToken && !!sessionId,\n\t\tstaleTime: 5 * 60 * 1000, // 5 minutes\n\t});\n}\n\nexport function useSessionMembers(sessionId: string) {\n\tconst { data: session } = useSession();\n\tconst api = useApi();\n\n\treturn useQuery({\n\t\tqueryKey: sessionKeys.members(sessionId),\n\t\tqueryFn: () => api.getSessionMembers(sessionId),\n\t\tenabled: !!session?.accessToken && !!sessionId,\n\t\tstaleTime: 2 * 60 * 1000, // 2 minutes\n\t});\n}\n\n// Mutations\nexport function useCreateSession() {\n\tconst queryClient = useQueryClient();\n\tconst api = useApi();\n\n\treturn useMutation({\n\t\tmutationFn: (data: CreateSessionDto) => api.createSession(data),\n\t\tonSuccess: () => {\n\t\t\t// Invalidate and refetch sessions list\n\t\t\tqueryClient.invalidateQueries({ queryKey: sessionKeys.lists() });\n\t\t},\n\t\tonError: (error) => {\n\t\t\tconsole.error(\"Error creating session:\", error);\n\t\t},\n\t});\n}\n\nexport function useUpdateSession() {\n\tconst queryClient = useQueryClient();\n\tconst api = useApi();\n\n\treturn useMutation({\n\t\tmutationFn: ({ id, data }: { id: string; data: UpdateSessionDto }) =>\n\t\t\tapi.updateSession(id, data),\n\t\tonSuccess: (_, { id }) => {\n\t\t\t// Invalidate specific session and sessions list\n\t\t\tqueryClient.invalidateQueries({ queryKey: sessionKeys.detail(id) });\n\t\t\tqueryClient.invalidateQueries({ queryKey: sessionKeys.lists() });\n\t\t},\n\t\tonError: (error) => {\n\t\t\tconsole.error(\"Error updating session:\", error);\n\t\t},\n\t});\n}\n\nexport function useDeleteSession() {\n\tconst queryClient = useQueryClient();\n\tconst api = useApi();\n\n\treturn useMutation({\n\t\tmutationFn: (sessionId: string) => api.deleteSession(sessionId),\n\t\tonSuccess: (_, sessionId) => {\n\t\t\t// Remove from cache and invalidate lists\n\t\t\tqueryClient.removeQueries({ queryKey: sessionKeys.detail(sessionId) });\n\t\t\tqueryClient.invalidateQueries({ queryKey: sessionKeys.lists() });\n\t\t},\n\t\tonError: (error) => {\n\t\t\tconsole.error(\"Error deleting session:\", error);\n\t\t},\n\t});\n}\n\nexport function useAddSessionMember() {\n\tconst queryClient = useQueryClient();\n\tconst api = useApi();\n\n\treturn useMutation({\n\t\tmutationFn: (data: {\n\t\t\tsessionId: string;\n\t\t\tmemberId: string;\n\t\t\tpartFixe: number;\n\t\t}) =>\n\t\t\tapi.addSessionMember({\n\t\t\t\tsessionId: data.sessionId,\n\t\t\t\tmemberId: data.memberId,\n\t\t\t\tparts: data.partFixe,\n\t\t\t}),\n\t\tonSuccess: (_, { sessionId }) => {\n\t\t\t// Invalidate session members\n\t\t\tqueryClient.invalidateQueries({\n\t\t\t\tqueryKey: sessionKeys.members(sessionId),\n\t\t\t});\n\t\t},\n\t\tonError: (error) => {\n\t\t\tconsole.error(\"Error adding session member:\", error);\n\t\t},\n\t});\n}\n\nexport function useRemoveSessionMember() {\n\tconst queryClient = useQueryClient();\n\tconst api = useApi();\n\n\treturn useMutation({\n\t\tmutationFn: ({\n\t\t\tsessionId,\n\t\t\tmemberId,\n\t\t}: {\n\t\t\tsessionId: string;\n\t\t\tmemberId: string;\n\t\t}) => api.removeSessionMember(sessionId, memberId),\n\t\tonSuccess: (_, { sessionId }) => {\n\t\t\t// Invalidate session members\n\t\t\tqueryClient.invalidateQueries({\n\t\t\t\tqueryKey: sessionKeys.members(sessionId),\n\t\t\t});\n\t\t},\n\t\tonError: (error) => {\n\t\t\tconsole.error(\"Error removing session member:\", error);\n\t\t},\n\t});\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;;;;;AAIO,MAAM,cAAc;IAC1B,KAAK;QAAC;KAAW;IACjB,OAAO,IAAM;eAAI,YAAY,GAAG;YAAE;SAAO;IACzC,MAAM,CAAC,UACN;eAAI,YAAY,KAAK;YAAI;gBAAE;YAAQ;SAAE;IACtC,SAAS,IAAM;eAAI,YAAY,GAAG;YAAE;SAAS;IAC7C,QAAQ,CAAC,KAAe;eAAI,YAAY,OAAO;YAAI;SAAG;IACtD,SAAS,CAAC,KAAe;eAAI,YAAY,MAAM,CAAC;YAAK;SAAU;AAChE;AAGO,SAAS;;IACf,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,sSAAQ,EAAC;QACf,UAAU,YAAY,KAAK;QAC3B,OAAO;oCAAE,IAAM,IAAI,WAAW;;QAC9B,SAAS,CAAC,EAAC,oBAAA,8BAAA,QAAS,WAAW;QAC/B,WAAW,IAAI,KAAK;IACrB;AACD;GAVgB;;QACW,8QAAU;QACxB,mJAAM;QAEX,sSAAQ;;;AAQT,SAAS,iBAAiB,SAAiB;;IACjD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,sSAAQ,EAAC;QACf,UAAU,YAAY,MAAM,CAAC;QAC7B,OAAO;yCAAE,IAAM,IAAI,UAAU,CAAC;;QAC9B,SAAS,CAAC,EAAC,oBAAA,8BAAA,QAAS,WAAW,KAAI,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACrB;AACD;IAVgB;;QACW,8QAAU;QACxB,mJAAM;QAEX,sSAAQ;;;AAQT,SAAS,kBAAkB,SAAiB;;IAClD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,sSAAQ,EAAC;QACf,UAAU,YAAY,OAAO,CAAC;QAC9B,OAAO;0CAAE,IAAM,IAAI,iBAAiB,CAAC;;QACrC,SAAS,CAAC,EAAC,oBAAA,8BAAA,QAAS,WAAW,KAAI,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACrB;AACD;IAVgB;;QACW,8QAAU;QACxB,mJAAM;QAEX,sSAAQ;;;AAST,SAAS;;IACf,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QAClB,UAAU;4CAAE,CAAC,OAA2B,IAAI,aAAa,CAAC;;QAC1D,SAAS;4CAAE;gBACV,uCAAuC;gBACvC,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,KAAK;gBAAG;YAC/D;;QACA,OAAO;4CAAE,CAAC;gBACT,QAAQ,KAAK,CAAC,2BAA2B;YAC1C;;IACD;AACD;IAdgB;;QACK,uTAAc;QACtB,mJAAM;QAEX,4SAAW;;;AAYZ,SAAS;;IACf,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QAClB,UAAU;4CAAE;oBAAC,EAAE,EAAE,EAAE,IAAI,EAA0C;uBAChE,IAAI,aAAa,CAAC,IAAI;;;QACvB,SAAS;4CAAE,CAAC;oBAAG,EAAE,EAAE,EAAE;gBACpB,gDAAgD;gBAChD,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,MAAM,CAAC;gBAAI;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,KAAK;gBAAG;YAC/D;;QACA,OAAO;4CAAE,CAAC;gBACT,QAAQ,KAAK,CAAC,2BAA2B;YAC1C;;IACD;AACD;IAhBgB;;QACK,uTAAc;QACtB,mJAAM;QAEX,4SAAW;;;AAcZ,SAAS;;IACf,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QAClB,UAAU;4CAAE,CAAC,YAAsB,IAAI,aAAa,CAAC;;QACrD,SAAS;4CAAE,CAAC,GAAG;gBACd,yCAAyC;gBACzC,YAAY,aAAa,CAAC;oBAAE,UAAU,YAAY,MAAM,CAAC;gBAAW;gBACpE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,KAAK;gBAAG;YAC/D;;QACA,OAAO;4CAAE,CAAC;gBACT,QAAQ,KAAK,CAAC,2BAA2B;YAC1C;;IACD;AACD;IAfgB;;QACK,uTAAc;QACtB,mJAAM;QAEX,4SAAW;;;AAaZ,SAAS;;IACf,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QAClB,UAAU;+CAAE,CAAC,OAKZ,IAAI,gBAAgB,CAAC;oBACpB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,OAAO,KAAK,QAAQ;gBACrB;;QACD,SAAS;+CAAE,CAAC;oBAAG,EAAE,SAAS,EAAE;gBAC3B,6BAA6B;gBAC7B,YAAY,iBAAiB,CAAC;oBAC7B,UAAU,YAAY,OAAO,CAAC;gBAC/B;YACD;;QACA,OAAO;+CAAE,CAAC;gBACT,QAAQ,KAAK,CAAC,gCAAgC;YAC/C;;IACD;AACD;IAzBgB;;QACK,uTAAc;QACtB,mJAAM;QAEX,4SAAW;;;AAuBZ,SAAS;;IACf,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QAClB,UAAU;kDAAE;oBAAC,EACZ,SAAS,EACT,QAAQ,EAIR;uBAAK,IAAI,mBAAmB,CAAC,WAAW;;;QACzC,SAAS;kDAAE,CAAC;oBAAG,EAAE,SAAS,EAAE;gBAC3B,6BAA6B;gBAC7B,YAAY,iBAAiB,CAAC;oBAC7B,UAAU,YAAY,OAAO,CAAC;gBAC/B;YACD;;QACA,OAAO;kDAAE,CAAC;gBACT,QAAQ,KAAK,CAAC,kCAAkC;YACjD;;IACD;AACD;IAtBgB;;QACK,uTAAc;QACtB,mJAAM;QAEX,4SAAW", "debugId": null}}, {"offset": {"line": 631, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/queries/use-members.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { useSession } from 'next-auth/react';\nimport { useApi } from '@/hooks/use-api';\nimport type { Member, CreateMemberDto, UpdateMemberDto, MemberDebrief, PaymentFilters } from '@/types';\n\n// Query Keys\nexport const memberKeys = {\n  all: ['members'] as const,\n  lists: () => [...memberKeys.all, 'list'] as const,\n  list: (filters: Record<string, any>) => [...memberKeys.lists(), { filters }] as const,\n  details: () => [...memberKeys.all, 'detail'] as const,\n  detail: (id: string) => [...memberKeys.details(), id] as const,\n  debrief: (id: string, filters?: PaymentFilters) => [...memberKeys.detail(id), 'debrief', { filters }] as const,\n};\n\n// Hooks\nexport function useMembers(searchTerm?: string) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: memberKeys.list({ searchTerm }),\n    queryFn: () => api.getMembers(),\n    enabled: !!session?.accessToken,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n    select: (data) => {\n      // Client-side filtering for search\n      if (!searchTerm) return data;\n      const search = searchTerm.toLowerCase();\n      return data.filter(\n        (member) =>\n          member.firstName.toLowerCase().includes(search) ||\n          member.lastName.toLowerCase().includes(search) ||\n          member.email?.toLowerCase().includes(search) ||\n          member.phone?.includes(search)\n      );\n    },\n  });\n}\n\nexport function useMember(memberId: string) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: memberKeys.detail(memberId),\n    queryFn: () => api.getMember(memberId),\n    enabled: !!session?.accessToken && !!memberId,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\nexport function useMemberDebrief(memberId: string, filters?: PaymentFilters) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: memberKeys.debrief(memberId, filters),\n    queryFn: () => api.getMemberDebrief(memberId, filters),\n    enabled: !!session?.accessToken && !!memberId,\n    staleTime: 2 * 60 * 1000, // 2 minutes\n  });\n}\n\n// Mutations\nexport function useCreateMember() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (data: CreateMemberDto) => api.createMember(data),\n    onSuccess: () => {\n      // Invalidate and refetch members list\n      queryClient.invalidateQueries({ queryKey: memberKeys.lists() });\n    },\n    onError: (error) => {\n      console.error('Error creating member:', error);\n    },\n  });\n}\n\nexport function useUpdateMember() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: UpdateMemberDto }) => \n      api.updateMember(id, data),\n    onSuccess: (_, { id }) => {\n      // Invalidate specific member and members list\n      queryClient.invalidateQueries({ queryKey: memberKeys.detail(id) });\n      queryClient.invalidateQueries({ queryKey: memberKeys.lists() });\n      // Also invalidate debrief data\n      queryClient.invalidateQueries({ queryKey: memberKeys.debrief(id) });\n    },\n    onError: (error) => {\n      console.error('Error updating member:', error);\n    },\n  });\n}\n\nexport function useDeleteMember() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (memberId: string) => api.deleteMember(memberId),\n    onSuccess: (_, memberId) => {\n      // Remove from cache and invalidate lists\n      queryClient.removeQueries({ queryKey: memberKeys.detail(memberId) });\n      queryClient.removeQueries({ queryKey: memberKeys.debrief(memberId) });\n      queryClient.invalidateQueries({ queryKey: memberKeys.lists() });\n    },\n    onError: (error) => {\n      console.error('Error deleting member:', error);\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;;;;;AAIO,MAAM,aAAa;IACxB,KAAK;QAAC;KAAU;IAChB,OAAO,IAAM;eAAI,WAAW,GAAG;YAAE;SAAO;IACxC,MAAM,CAAC,UAAiC;eAAI,WAAW,KAAK;YAAI;gBAAE;YAAQ;SAAE;IAC5E,SAAS,IAAM;eAAI,WAAW,GAAG;YAAE;SAAS;IAC5C,QAAQ,CAAC,KAAe;eAAI,WAAW,OAAO;YAAI;SAAG;IACrD,SAAS,CAAC,IAAY,UAA6B;eAAI,WAAW,MAAM,CAAC;YAAK;YAAW;gBAAE;YAAQ;SAAE;AACvG;AAGO,SAAS,WAAW,UAAmB;;IAC5C,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,sSAAQ,EAAC;QACd,UAAU,WAAW,IAAI,CAAC;YAAE;QAAW;QACvC,OAAO;mCAAE,IAAM,IAAI,UAAU;;QAC7B,SAAS,CAAC,EAAC,oBAAA,8BAAA,QAAS,WAAW;QAC/B,WAAW,IAAI,KAAK;QACpB,MAAM;mCAAE,CAAC;gBACP,mCAAmC;gBACnC,IAAI,CAAC,YAAY,OAAO;gBACxB,MAAM,SAAS,WAAW,WAAW;gBACrC,OAAO,KAAK,MAAM;2CAChB,CAAC;4BAGC,eACA;+BAHA,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WACxC,OAAO,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,aACvC,gBAAA,OAAO,KAAK,cAAZ,oCAAA,cAAc,WAAW,GAAG,QAAQ,CAAC,cACrC,gBAAA,OAAO,KAAK,cAAZ,oCAAA,cAAc,QAAQ,CAAC;;;YAE7B;;IACF;AACF;GAtBgB;;QACY,8QAAU;QACxB,mJAAM;QAEX,sSAAQ;;;AAoBV,SAAS,UAAU,QAAgB;;IACxC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,sSAAQ,EAAC;QACd,UAAU,WAAW,MAAM,CAAC;QAC5B,OAAO;kCAAE,IAAM,IAAI,SAAS,CAAC;;QAC7B,SAAS,CAAC,EAAC,oBAAA,8BAAA,QAAS,WAAW,KAAI,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB;AACF;IAVgB;;QACY,8QAAU;QACxB,mJAAM;QAEX,sSAAQ;;;AAQV,SAAS,iBAAiB,QAAgB,EAAE,OAAwB;;IACzE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,sSAAQ,EAAC;QACd,UAAU,WAAW,OAAO,CAAC,UAAU;QACvC,OAAO;yCAAE,IAAM,IAAI,gBAAgB,CAAC,UAAU;;QAC9C,SAAS,CAAC,EAAC,oBAAA,8BAAA,QAAS,WAAW,KAAI,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB;AACF;IAVgB;;QACY,8QAAU;QACxB,mJAAM;QAEX,sSAAQ;;;AASV,SAAS;;IACd,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QACjB,UAAU;2CAAE,CAAC,OAA0B,IAAI,YAAY,CAAC;;QACxD,SAAS;2CAAE;gBACT,sCAAsC;gBACtC,YAAY,iBAAiB,CAAC;oBAAE,UAAU,WAAW,KAAK;gBAAG;YAC/D;;QACA,OAAO;2CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;;IACF;AACF;IAdgB;;QACM,uTAAc;QACtB,mJAAM;QAEX,4SAAW;;;AAYb,SAAS;;IACd,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QACjB,UAAU;2CAAE;oBAAC,EAAE,EAAE,EAAE,IAAI,EAAyC;uBAC9D,IAAI,YAAY,CAAC,IAAI;;;QACvB,SAAS;2CAAE,CAAC;oBAAG,EAAE,EAAE,EAAE;gBACnB,8CAA8C;gBAC9C,YAAY,iBAAiB,CAAC;oBAAE,UAAU,WAAW,MAAM,CAAC;gBAAI;gBAChE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,WAAW,KAAK;gBAAG;gBAC7D,+BAA+B;gBAC/B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,WAAW,OAAO,CAAC;gBAAI;YACnE;;QACA,OAAO;2CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;;IACF;AACF;IAlBgB;;QACM,uTAAc;QACtB,mJAAM;QAEX,4SAAW;;;AAgBb,SAAS;;IACd,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QACjB,UAAU;2CAAE,CAAC,WAAqB,IAAI,YAAY,CAAC;;QACnD,SAAS;2CAAE,CAAC,GAAG;gBACb,yCAAyC;gBACzC,YAAY,aAAa,CAAC;oBAAE,UAAU,WAAW,MAAM,CAAC;gBAAU;gBAClE,YAAY,aAAa,CAAC;oBAAE,UAAU,WAAW,OAAO,CAAC;gBAAU;gBACnE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,WAAW,KAAK;gBAAG;YAC/D;;QACA,OAAO;2CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;;IACF;AACF;IAhBgB;;QACM,uTAAc;QACtB,mJAAM;QAEX,4SAAW", "debugId": null}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/queries/use-caisses.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { useSession } from 'next-auth/react';\nimport { useApi } from '@/hooks/use-api';\nimport type { Caisse, CreateCaisseDto, UpdateCaisseDto } from '@/types';\n\n// Query Keys\nexport const caisseKeys = {\n  all: ['caisses'] as const,\n  lists: () => [...caisseKeys.all, 'list'] as const,\n  list: (filters: Record<string, any>) => [...caisseKeys.lists(), { filters }] as const,\n  details: () => [...caisseKeys.all, 'detail'] as const,\n  detail: (id: string) => [...caisseKeys.details(), id] as const,\n};\n\n// Hooks\nexport function useCaisses() {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: caisseKeys.lists(),\n    queryFn: () => api.getCaisses(),\n    enabled: !!session?.accessToken,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\nexport function useCaisse(caisseId: string) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: caisseKeys.detail(caisseId),\n    queryFn: () => api.getCaisse(caisseId),\n    enabled: !!session?.accessToken && !!caisseId,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n// Mutations\nexport function useCreateCaisse() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (data: CreateCaisseDto) => api.createCaisse(data),\n    onSuccess: () => {\n      // Invalidate and refetch caisses list\n      queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });\n    },\n    onError: (error) => {\n      console.error('Error creating caisse:', error);\n    },\n  });\n}\n\nexport function useUpdateCaisse() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: UpdateCaisseDto }) => \n      api.updateCaisse(id, data),\n    onSuccess: (_, { id }) => {\n      // Invalidate specific caisse and caisses list\n      queryClient.invalidateQueries({ queryKey: caisseKeys.detail(id) });\n      queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });\n    },\n    onError: (error) => {\n      console.error('Error updating caisse:', error);\n    },\n  });\n}\n\nexport function useDeleteCaisse() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (caisseId: string) => api.deleteCaisse(caisseId),\n    onSuccess: (_, caisseId) => {\n      // Remove from cache and invalidate lists\n      queryClient.removeQueries({ queryKey: caisseKeys.detail(caisseId) });\n      queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });\n    },\n    onError: (error) => {\n      console.error('Error deleting caisse:', error);\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;;;;;AAIO,MAAM,aAAa;IACxB,KAAK;QAAC;KAAU;IAChB,OAAO,IAAM;eAAI,WAAW,GAAG;YAAE;SAAO;IACxC,MAAM,CAAC,UAAiC;eAAI,WAAW,KAAK;YAAI;gBAAE;YAAQ;SAAE;IAC5E,SAAS,IAAM;eAAI,WAAW,GAAG;YAAE;SAAS;IAC5C,QAAQ,CAAC,KAAe;eAAI,WAAW,OAAO;YAAI;SAAG;AACvD;AAGO,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,sSAAQ,EAAC;QACd,UAAU,WAAW,KAAK;QAC1B,OAAO;mCAAE,IAAM,IAAI,UAAU;;QAC7B,SAAS,CAAC,EAAC,oBAAA,8BAAA,QAAS,WAAW;QAC/B,WAAW,IAAI,KAAK;IACtB;AACF;GAVgB;;QACY,8QAAU;QACxB,mJAAM;QAEX,sSAAQ;;;AAQV,SAAS,UAAU,QAAgB;;IACxC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,sSAAQ,EAAC;QACd,UAAU,WAAW,MAAM,CAAC;QAC5B,OAAO;kCAAE,IAAM,IAAI,SAAS,CAAC;;QAC7B,SAAS,CAAC,EAAC,oBAAA,8BAAA,QAAS,WAAW,KAAI,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB;AACF;IAVgB;;QACY,8QAAU;QACxB,mJAAM;QAEX,sSAAQ;;;AASV,SAAS;;IACd,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QACjB,UAAU;2CAAE,CAAC,OAA0B,IAAI,YAAY,CAAC;;QACxD,SAAS;2CAAE;gBACT,sCAAsC;gBACtC,YAAY,iBAAiB,CAAC;oBAAE,UAAU,WAAW,KAAK;gBAAG;YAC/D;;QACA,OAAO;2CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;;IACF;AACF;IAdgB;;QACM,uTAAc;QACtB,mJAAM;QAEX,4SAAW;;;AAYb,SAAS;;IACd,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QACjB,UAAU;2CAAE;oBAAC,EAAE,EAAE,EAAE,IAAI,EAAyC;uBAC9D,IAAI,YAAY,CAAC,IAAI;;;QACvB,SAAS;2CAAE,CAAC;oBAAG,EAAE,EAAE,EAAE;gBACnB,8CAA8C;gBAC9C,YAAY,iBAAiB,CAAC;oBAAE,UAAU,WAAW,MAAM,CAAC;gBAAI;gBAChE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,WAAW,KAAK;gBAAG;YAC/D;;QACA,OAAO;2CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;;IACF;AACF;IAhBgB;;QACM,uTAAc;QACtB,mJAAM;QAEX,4SAAW;;;AAcb,SAAS;;IACd,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QACjB,UAAU;2CAAE,CAAC,WAAqB,IAAI,YAAY,CAAC;;QACnD,SAAS;2CAAE,CAAC,GAAG;gBACb,yCAAyC;gBACzC,YAAY,aAAa,CAAC;oBAAE,UAAU,WAAW,MAAM,CAAC;gBAAU;gBAClE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,WAAW,KAAK;gBAAG;YAC/D;;QACA,OAAO;2CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;;IACF;AACF;IAfgB;;QACM,uTAAc;QACtB,mJAAM;QAEX,4SAAW", "debugId": null}}, {"offset": {"line": 1068, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/queries/use-users.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { useSession } from 'next-auth/react';\nimport { useApi } from '@/hooks/use-api';\n\n// Query Keys\nexport const userKeys = {\n  all: ['users'] as const,\n  lists: () => [...userKeys.all, 'list'] as const,\n  list: (filters: Record<string, any>) => [...userKeys.lists(), { filters }] as const,\n  details: () => [...userKeys.all, 'detail'] as const,\n  detail: (id: string) => [...userKeys.details(), id] as const,\n  stats: () => [...userKeys.all, 'stats'] as const,\n  byRole: (role: string) => [...userKeys.all, 'by-role', role] as const,\n};\n\n// Hooks\nexport function useUsers() {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: userKeys.lists(),\n    queryFn: () => api.getUsers(),\n    enabled: !!session?.accessToken,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\nexport function useUser(userId: string) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: userKeys.detail(userId),\n    queryFn: () => api.getUser(userId),\n    enabled: !!session?.accessToken && !!userId,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\nexport function useUserStats() {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: userKeys.stats(),\n    queryFn: () => api.authenticatedRequest('/users/stats'),\n    enabled: !!session?.accessToken,\n    staleTime: 10 * 60 * 1000, // 10 minutes - stats don't change frequently\n  });\n}\n\nexport function useUsersByRole(role: string) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: userKeys.byRole(role),\n    queryFn: () => api.authenticatedRequest(`/users/by-role/${role}`),\n    enabled: !!session?.accessToken && !!role,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n// Mutations\nexport function useCreateUser() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (userData: any) => api.createUser(userData),\n    onSuccess: () => {\n      // Invalidate and refetch users list and stats\n      queryClient.invalidateQueries({ queryKey: userKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userKeys.stats() });\n    },\n    onError: (error) => {\n      console.error('Error creating user:', error);\n    },\n  });\n}\n\nexport function useUpdateUser() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: ({ id, userData }: { id: string; userData: any }) => \n      api.updateUser(id, userData),\n    onSuccess: (_, { id }) => {\n      // Invalidate specific user, users list, and stats\n      queryClient.invalidateQueries({ queryKey: userKeys.detail(id) });\n      queryClient.invalidateQueries({ queryKey: userKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userKeys.stats() });\n      // Invalidate all by-role queries since role might have changed\n      queryClient.invalidateQueries({ queryKey: [...userKeys.all, 'by-role'] });\n    },\n    onError: (error) => {\n      console.error('Error updating user:', error);\n    },\n  });\n}\n\nexport function useDeleteUser() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (userId: string) => api.deleteUser(userId),\n    onSuccess: (_, userId) => {\n      // Remove from cache and invalidate lists\n      queryClient.removeQueries({ queryKey: userKeys.detail(userId) });\n      queryClient.invalidateQueries({ queryKey: userKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userKeys.stats() });\n      // Invalidate all by-role queries\n      queryClient.invalidateQueries({ queryKey: [...userKeys.all, 'by-role'] });\n    },\n    onError: (error) => {\n      console.error('Error deleting user:', error);\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;;;;;AAGO,MAAM,WAAW;IACtB,KAAK;QAAC;KAAQ;IACd,OAAO,IAAM;eAAI,SAAS,GAAG;YAAE;SAAO;IACtC,MAAM,CAAC,UAAiC;eAAI,SAAS,KAAK;YAAI;gBAAE;YAAQ;SAAE;IAC1E,SAAS,IAAM;eAAI,SAAS,GAAG;YAAE;SAAS;IAC1C,QAAQ,CAAC,KAAe;eAAI,SAAS,OAAO;YAAI;SAAG;IACnD,OAAO,IAAM;eAAI,SAAS,GAAG;YAAE;SAAQ;IACvC,QAAQ,CAAC,OAAiB;eAAI,SAAS,GAAG;YAAE;YAAW;SAAK;AAC9D;AAGO,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,sSAAQ,EAAC;QACd,UAAU,SAAS,KAAK;QACxB,OAAO;iCAAE,IAAM,IAAI,QAAQ;;QAC3B,SAAS,CAAC,EAAC,oBAAA,8BAAA,QAAS,WAAW;QAC/B,WAAW,IAAI,KAAK;IACtB;AACF;GAVgB;;QACY,8QAAU;QACxB,mJAAM;QAEX,sSAAQ;;;AAQV,SAAS,QAAQ,MAAc;;IACpC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,sSAAQ,EAAC;QACd,UAAU,SAAS,MAAM,CAAC;QAC1B,OAAO;gCAAE,IAAM,IAAI,OAAO,CAAC;;QAC3B,SAAS,CAAC,EAAC,oBAAA,8BAAA,QAAS,WAAW,KAAI,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB;AACF;IAVgB;;QACY,8QAAU;QACxB,mJAAM;QAEX,sSAAQ;;;AAQV,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,sSAAQ,EAAC;QACd,UAAU,SAAS,KAAK;QACxB,OAAO;qCAAE,IAAM,IAAI,oBAAoB,CAAC;;QACxC,SAAS,CAAC,EAAC,oBAAA,8BAAA,QAAS,WAAW;QAC/B,WAAW,KAAK,KAAK;IACvB;AACF;IAVgB;;QACY,8QAAU;QACxB,mJAAM;QAEX,sSAAQ;;;AAQV,SAAS,eAAe,IAAY;;IACzC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,sSAAQ,EAAC;QACd,UAAU,SAAS,MAAM,CAAC;QAC1B,OAAO;uCAAE,IAAM,IAAI,oBAAoB,CAAC,AAAC,kBAAsB,OAAL;;QAC1D,SAAS,CAAC,EAAC,oBAAA,8BAAA,QAAS,WAAW,KAAI,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB;AACF;IAVgB;;QACY,8QAAU;QACxB,mJAAM;QAEX,sSAAQ;;;AASV,SAAS;;IACd,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QACjB,UAAU;yCAAE,CAAC,WAAkB,IAAI,UAAU,CAAC;;QAC9C,SAAS;yCAAE;gBACT,8CAA8C;gBAC9C,YAAY,iBAAiB,CAAC;oBAAE,UAAU,SAAS,KAAK;gBAAG;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU,SAAS,KAAK;gBAAG;YAC7D;;QACA,OAAO;yCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,wBAAwB;YACxC;;IACF;AACF;IAfgB;;QACM,uTAAc;QACtB,mJAAM;QAEX,4SAAW;;;AAab,SAAS;;IACd,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QACjB,UAAU;yCAAE;oBAAC,EAAE,EAAE,EAAE,QAAQ,EAAiC;uBAC1D,IAAI,UAAU,CAAC,IAAI;;;QACrB,SAAS;yCAAE,CAAC;oBAAG,EAAE,EAAE,EAAE;gBACnB,kDAAkD;gBAClD,YAAY,iBAAiB,CAAC;oBAAE,UAAU,SAAS,MAAM,CAAC;gBAAI;gBAC9D,YAAY,iBAAiB,CAAC;oBAAE,UAAU,SAAS,KAAK;gBAAG;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU,SAAS,KAAK;gBAAG;gBAC3D,+DAA+D;gBAC/D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;2BAAI,SAAS,GAAG;wBAAE;qBAAU;gBAAC;YACzE;;QACA,OAAO;yCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,wBAAwB;YACxC;;IACF;AACF;IAnBgB;;QACM,uTAAc;QACtB,mJAAM;QAEX,4SAAW;;;AAiBb,SAAS;;IACd,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QACjB,UAAU;yCAAE,CAAC,SAAmB,IAAI,UAAU,CAAC;;QAC/C,SAAS;yCAAE,CAAC,GAAG;gBACb,yCAAyC;gBACzC,YAAY,aAAa,CAAC;oBAAE,UAAU,SAAS,MAAM,CAAC;gBAAQ;gBAC9D,YAAY,iBAAiB,CAAC;oBAAE,UAAU,SAAS,KAAK;gBAAG;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU,SAAS,KAAK;gBAAG;gBAC3D,iCAAiC;gBACjC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;2BAAI,SAAS,GAAG;wBAAE;qBAAU;gBAAC;YACzE;;QACA,OAAO;yCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,wBAAwB;YACxC;;IACF;AACF;IAlBgB;;QACM,uTAAc;QACtB,mJAAM;QAEX,4SAAW", "debugId": null}}, {"offset": {"line": 1337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/queries/use-payments.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { useSession } from 'next-auth/react';\nimport { useApi } from '@/hooks/use-api';\nimport type { Payment, CreatePaymentDto, PaymentFilters } from '@/types';\nimport { memberKeys } from './use-members';\nimport { caisseKeys } from './use-caisses';\n\n// Query Keys\nexport const paymentKeys = {\n  all: ['payments'] as const,\n  lists: () => [...paymentKeys.all, 'list'] as const,\n  list: (filters: PaymentFilters) => [...paymentKeys.lists(), { filters }] as const,\n  details: () => [...paymentKeys.all, 'detail'] as const,\n  detail: (id: string) => [...paymentKeys.details(), id] as const,\n};\n\n// Hooks\nexport function usePayments(filters?: PaymentFilters) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: paymentKeys.list(filters || {}),\n    queryFn: () => api.getPayments(filters),\n    enabled: !!session?.accessToken,\n    staleTime: 2 * 60 * 1000, // 2 minutes - payments change more frequently\n  });\n}\n\nexport function usePayment(paymentId: string) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: paymentKeys.detail(paymentId),\n    queryFn: () => api.getPayment(paymentId),\n    enabled: !!session?.accessToken && !!paymentId,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n// Mutations\nexport function useCreatePayment() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (data: CreatePaymentDto) => api.createPayment(data),\n    onSuccess: (newPayment) => {\n      // Invalidate payments lists\n      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });\n      \n      // Invalidate related member debrief if memberId is available\n      if (newPayment.memberId) {\n        queryClient.invalidateQueries({ \n          queryKey: memberKeys.debrief(newPayment.memberId) \n        });\n      }\n      \n      // Invalidate related caisse if caisseId is available\n      if (newPayment.caisseId) {\n        queryClient.invalidateQueries({ \n          queryKey: caisseKeys.detail(newPayment.caisseId) \n        });\n        queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });\n      }\n    },\n    onError: (error) => {\n      console.error('Error creating payment:', error);\n    },\n  });\n}\n\nexport function useUpdatePayment() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: Partial<CreatePaymentDto> }) => \n      api.updatePayment(id, data),\n    onSuccess: (updatedPayment, { id }) => {\n      // Invalidate specific payment and payments lists\n      queryClient.invalidateQueries({ queryKey: paymentKeys.detail(id) });\n      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });\n      \n      // Invalidate related member debrief\n      if (updatedPayment.memberId) {\n        queryClient.invalidateQueries({ \n          queryKey: memberKeys.debrief(updatedPayment.memberId) \n        });\n      }\n      \n      // Invalidate related caisse\n      if (updatedPayment.caisseId) {\n        queryClient.invalidateQueries({ \n          queryKey: caisseKeys.detail(updatedPayment.caisseId) \n        });\n        queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });\n      }\n    },\n    onError: (error) => {\n      console.error('Error updating payment:', error);\n    },\n  });\n}\n\nexport function useDeletePayment() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (paymentId: string) => api.deletePayment(paymentId),\n    onSuccess: (_, paymentId) => {\n      // Remove from cache and invalidate lists\n      queryClient.removeQueries({ queryKey: paymentKeys.detail(paymentId) });\n      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });\n      \n      // Invalidate all member debriefs and caisses since we don't know which ones were affected\n      queryClient.invalidateQueries({ queryKey: memberKeys.all });\n      queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });\n    },\n    onError: (error) => {\n      console.error('Error deleting payment:', error);\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;;;;;;;AAGO,MAAM,cAAc;IACzB,KAAK;QAAC;KAAW;IACjB,OAAO,IAAM;eAAI,YAAY,GAAG;YAAE;SAAO;IACzC,MAAM,CAAC,UAA4B;eAAI,YAAY,KAAK;YAAI;gBAAE;YAAQ;SAAE;IACxE,SAAS,IAAM;eAAI,YAAY,GAAG;YAAE;SAAS;IAC7C,QAAQ,CAAC,KAAe;eAAI,YAAY,OAAO;YAAI;SAAG;AACxD;AAGO,SAAS,YAAY,OAAwB;;IAClD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,sSAAQ,EAAC;QACd,UAAU,YAAY,IAAI,CAAC,WAAW,CAAC;QACvC,OAAO;oCAAE,IAAM,IAAI,WAAW,CAAC;;QAC/B,SAAS,CAAC,EAAC,oBAAA,8BAAA,QAAS,WAAW;QAC/B,WAAW,IAAI,KAAK;IACtB;AACF;GAVgB;;QACY,8QAAU;QACxB,mJAAM;QAEX,sSAAQ;;;AAQV,SAAS,WAAW,SAAiB;;IAC1C,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,sSAAQ,EAAC;QACd,UAAU,YAAY,MAAM,CAAC;QAC7B,OAAO;mCAAE,IAAM,IAAI,UAAU,CAAC;;QAC9B,SAAS,CAAC,EAAC,oBAAA,8BAAA,QAAS,WAAW,KAAI,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB;AACF;IAVgB;;QACY,8QAAU;QACxB,mJAAM;QAEX,sSAAQ;;;AASV,SAAS;;IACd,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QACjB,UAAU;4CAAE,CAAC,OAA2B,IAAI,aAAa,CAAC;;QAC1D,SAAS;4CAAE,CAAC;gBACV,4BAA4B;gBAC5B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,KAAK;gBAAG;gBAE9D,6DAA6D;gBAC7D,IAAI,WAAW,QAAQ,EAAE;oBACvB,YAAY,iBAAiB,CAAC;wBAC5B,UAAU,sKAAU,CAAC,OAAO,CAAC,WAAW,QAAQ;oBAClD;gBACF;gBAEA,qDAAqD;gBACrD,IAAI,WAAW,QAAQ,EAAE;oBACvB,YAAY,iBAAiB,CAAC;wBAC5B,UAAU,sKAAU,CAAC,MAAM,CAAC,WAAW,QAAQ;oBACjD;oBACA,YAAY,iBAAiB,CAAC;wBAAE,UAAU,sKAAU,CAAC,KAAK;oBAAG;gBAC/D;YACF;;QACA,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;;IACF;AACF;IA7BgB;;QACM,uTAAc;QACtB,mJAAM;QAEX,4SAAW;;;AA2Bb,SAAS;;IACd,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QACjB,UAAU;4CAAE;oBAAC,EAAE,EAAE,EAAE,IAAI,EAAmD;uBACxE,IAAI,aAAa,CAAC,IAAI;;;QACxB,SAAS;4CAAE,CAAC;oBAAgB,EAAE,EAAE,EAAE;gBAChC,iDAAiD;gBACjD,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,MAAM,CAAC;gBAAI;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,KAAK;gBAAG;gBAE9D,oCAAoC;gBACpC,IAAI,eAAe,QAAQ,EAAE;oBAC3B,YAAY,iBAAiB,CAAC;wBAC5B,UAAU,sKAAU,CAAC,OAAO,CAAC,eAAe,QAAQ;oBACtD;gBACF;gBAEA,4BAA4B;gBAC5B,IAAI,eAAe,QAAQ,EAAE;oBAC3B,YAAY,iBAAiB,CAAC;wBAC5B,UAAU,sKAAU,CAAC,MAAM,CAAC,eAAe,QAAQ;oBACrD;oBACA,YAAY,iBAAiB,CAAC;wBAAE,UAAU,sKAAU,CAAC,KAAK;oBAAG;gBAC/D;YACF;;QACA,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;;IACF;AACF;IA/BgB;;QACM,uTAAc;QACtB,mJAAM;QAEX,4SAAW;;;AA6Bb,SAAS;;IACd,MAAM,cAAc,IAAA,uTAAc;IAClC,MAAM,MAAM,IAAA,mJAAM;IAElB,OAAO,IAAA,4SAAW,EAAC;QACjB,UAAU;4CAAE,CAAC,YAAsB,IAAI,aAAa,CAAC;;QACrD,SAAS;4CAAE,CAAC,GAAG;gBACb,yCAAyC;gBACzC,YAAY,aAAa,CAAC;oBAAE,UAAU,YAAY,MAAM,CAAC;gBAAW;gBACpE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,YAAY,KAAK;gBAAG;gBAE9D,0FAA0F;gBAC1F,YAAY,iBAAiB,CAAC;oBAAE,UAAU,sKAAU,CAAC,GAAG;gBAAC;gBACzD,YAAY,iBAAiB,CAAC;oBAAE,UAAU,sKAAU,CAAC,KAAK;gBAAG;YAC/D;;QACA,OAAO;4CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;;IACF;AACF;IAnBgB;;QACM,uTAAc;QACtB,mJAAM;QAEX,4SAAW", "debugId": null}}, {"offset": {"line": 1571, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/queries/index.ts"], "sourcesContent": ["// Export all query hooks for easy importing\nexport * from './use-sessions';\nexport * from './use-members';\nexport * from './use-caisses';\nexport * from './use-users';\nexport * from './use-payments';\n\n// Export query keys for external use if needed\nexport { sessionKeys } from './use-sessions';\nexport { memberKeys } from './use-members';\nexport { caisseKeys } from './use-caisses';\nexport { userKeys } from './use-users';\nexport { paymentKeys } from './use-payments';\n"], "names": [], "mappings": "AAAA,4CAA4C;;AAC5C;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1595, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/types/index.ts"], "sourcesContent": ["// Types pour l'application Tontine\n\n// Enums\nexport enum CaisseType {\n  PRINCIPALE = 'PRINCIPALE',\n  REUNION = 'REUNION',\n}\n\nexport enum UserRole {\n  SECRETARY_GENERAL = 'secretary_general',\n  CONTROLLER = 'controller',\n  CASHIER = 'cashier',\n}\n\nexport enum UserStatus {\n  ACTIF = 'actif',\n  EN_ATTENTE = 'en_attente',\n  SUSPENDU = 'suspendu',\n}\n\nexport enum PaymentDirection {\n  IN = 'IN',\n  OUT = 'OUT',\n}\n\nexport enum PaymentFunction {\n  CONTRIBUTION = 'cotisation',\n  TRANSFER = 'transfert',\n  EXTERNAL = 'exterieur',\n}\n\n// Interfaces principales\nexport interface User {\n  _id: string;\n  username: string;\n  role: UserRole;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Session {\n  _id: string;\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  dateProchaineReunion?: string;\n  nextReunionId?: string;\n  partFixe: number;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Reunion {\n  _id: string;\n  dateReunion: string;\n  lieu?: string;\n  caissePrincipale?: string;\n  sessionId: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Caisse {\n  _id: string;\n  nom: string;\n  type: CaisseType;\n  soldeActuel: number;\n  sessionId?: string;\n  createdBy: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: Member (différent de User)\nexport interface Member {\n  _id: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: SessionMember (inscription d'un membre à une session)\nexport interface SessionMember {\n  _id: string;\n  sessionId: string;\n  memberId: string;\n  parts: number;\n  totalDue: number;\n  paidSoFar: number;\n  expectedToDate: number;\n  overdueAmount: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: Payment\nexport interface Payment {\n  _id: string;\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  date: string;\n  sessionId?: string;\n  reunionId?: string;\n  caisseId: string;\n  receivingCaisseId?: string;\n  memberId?: string;\n  reason?: string;\n  exitOrderId?: string;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// DTOs pour les formulaires\nexport interface CreateSessionDto {\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  partFixe: number;\n}\n\nexport interface UpdateSessionDto {\n  annee?: number;\n  dateDebut?: string;\n  dateFin?: string;\n  partFixe?: number;\n}\n\nexport interface CreateCaisseDto {\n  nom: string;\n  type: CaisseType;\n  soldeActuel?: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface UpdateCaisseDto {\n  nom?: string;\n  type?: CaisseType;\n  soldeActuel?: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface UpdateReunionDto {\n  dateReunion?: string;\n  lieu?: string;\n  caissePrincipale?: string;\n}\n\n// Nouveaux DTOs\nexport interface CreateMemberDto {\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface UpdateMemberDto {\n  firstName?: string;\n  lastName?: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface CreatePaymentDto {\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  caisseId: string;\n  receivingCaisseId?: string;\n  sessionId?: string;\n  reunionId?: string;\n  memberId?: string;\n  reason?: string;\n  exitOrderId?: string;\n}\n\nexport interface CreateSessionMemberDto {\n  sessionId: string;\n  memberId: string;\n  parts: number;\n}\n\n// Types pour les statistiques\nexport interface SessionStats {\n  total: number;\n  active: number;\n  completed: number;\n  totalPartFixe: number;\n}\n\nexport interface CaisseStats {\n  total: number;\n  principales: number;\n  reunions: number;\n  soldeTotal: number;\n  soldePrincipales: number;\n  soldeReunions: number;\n}\n\nexport interface MemberStats {\n  total: number;\n  withEmail: number;\n  withPhone: number;\n  withAddress: number;\n}\n\nexport interface PaymentStats {\n  totalIn: number;\n  totalOut: number;\n  netAmount: number;\n  contributionsTotal: number;\n  transfersTotal: number;\n  externalTotal: number;\n}\n\n// Types pour les formulaires\nexport interface SessionForm {\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  partFixe: number;\n}\n\nexport interface CaisseForm {\n  nom: string;\n  type: CaisseType;\n  soldeActuel: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface MemberForm {\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface PaymentForm {\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  caisseId: string;\n  receivingCaisseId?: string;\n  sessionId?: string;\n  reunionId?: string;\n  memberId?: string;\n  reason?: string;\n}\n\n// Types pour les réponses API\nexport interface ApiResponse<T> {\n  data: T;\n  message?: string;\n  success: boolean;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n// Types pour les filtres\nexport interface SessionFilters {\n  annee?: number;\n  status?: 'active' | 'completed' | 'all';\n}\n\nexport interface CaisseFilters {\n  type?: CaisseType | 'all';\n  sessionId?: string;\n}\n\nexport interface MemberFilters {\n  search?: string;\n  hasEmail?: boolean;\n  hasPhone?: boolean;\n}\n\nexport interface PaymentFilters {\n  direction?: PaymentDirection | 'all';\n  func?: PaymentFunction | 'all';\n  caisseId?: string;\n  sessionId?: string;\n  memberId?: string;\n  dateFrom?: string;\n  dateTo?: string;\n}\n\n// Types pour les réponses spéciales\nexport interface MemberDebrief {\n  member: Member;\n  totalIn: number;\n  totalOut: number;\n  netAmount: number;\n  contributionsTotal: number;\n  transfersTotal: number;\n  externalTotal: number;\n  payments: Payment[];\n}\n"], "names": [], "mappings": "AAAA,mCAAmC;AAEnC,QAAQ;;;;;;;;;;;;;AACD,IAAA,AAAK,oCAAA;;;WAAA;;AAKL,IAAA,AAAK,kCAAA;;;;WAAA;;AAML,IAAA,AAAK,oCAAA;;;;WAAA;;AAML,IAAA,AAAK,0CAAA;;;WAAA;;AAKL,IAAA,AAAK,yCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 1644, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/dashboard/dashboard-stats.tsx"], "sourcesContent": ["import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/card\";\nimport { Calendar, DollarSign, Wallet } from \"lucide-react\";\nimport { type Session, type Caisse, CaisseType } from \"@/types\";\n\ninterface DashboardStatsProps {\n  sessions: Session[];\n  caisses: Caisse[];\n  userRole: string;\n}\n\nexport function DashboardStats({ sessions, caisses, userRole }: DashboardStatsProps) {\n  // Calculate statistics\n  const activeSessions = sessions.filter((s) => s.statut === \"active\");\n  const totalContributions = sessions.reduce((sum, session) => sum + session.montantContribution, 0);\n  const caissesPrincipales = caisses.filter((c) => c.type === CaisseType.PRINCIPALE);\n  const caissesReunions = caisses.filter((c) => c.type === CaisseType.REUNION);\n\n  // Permission checks\n  const canViewSessions = [\"secretary_general\", \"controller\", \"cashier\"].includes(userRole);\n  const canViewCaisses = [\"secretary_general\", \"controller\", \"cashier\"].includes(userRole);\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n      {canViewSessions && (\n        <>\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Sessions Totales</CardTitle>\n              <Calendar className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{sessions.length}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                {activeSessions.length} actives\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Contributions Totales</CardTitle>\n              <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {totalContributions.toLocaleString()} FCFA\n              </div>\n              <p className=\"text-xs text-muted-foreground\">\n                Toutes sessions confondues\n              </p>\n            </CardContent>\n          </Card>\n        </>\n      )}\n\n      {canViewCaisses && (\n        <>\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Caisses</CardTitle>\n              <Wallet className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{caisses.length}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                {caissesPrincipales.length} principales, {caissesReunions.length} réunions\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Solde Total</CardTitle>\n              <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {caisses.reduce((sum, c) => sum + c.solde, 0).toLocaleString()} FCFA\n              </div>\n              <p className=\"text-xs text-muted-foreground\">\n                Toutes caisses confondues\n              </p>\n            </CardContent>\n          </Card>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AACA;;;;;AAQO,SAAS,eAAe,KAAoD;QAApD,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAuB,GAApD;IAC7B,uBAAuB;IACvB,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK;IAC3D,MAAM,qBAAqB,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,mBAAmB,EAAE;IAChG,MAAM,qBAAqB,QAAQ,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,kJAAU,CAAC,UAAU;IACjF,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,kJAAU,CAAC,OAAO;IAE3E,oBAAoB;IACpB,MAAM,kBAAkB;QAAC;QAAqB;QAAc;KAAU,CAAC,QAAQ,CAAC;IAChF,MAAM,iBAAiB;QAAC;QAAqB;QAAc;KAAU,CAAC,QAAQ,CAAC;IAE/E,qBACE,wUAAC;QAAI,WAAU;;YACZ,iCACC;;kCACE,wUAAC,uJAAI;;0CACH,wUAAC,6JAAU;gCAAC,WAAU;;kDACpB,wUAAC,4JAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,wUAAC,yTAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,wUAAC,8JAAW;;kDACV,wUAAC;wCAAI,WAAU;kDAAsB,SAAS,MAAM;;;;;;kDACpD,wUAAC;wCAAE,WAAU;;4CACV,eAAe,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAK7B,wUAAC,uJAAI;;0CACH,wUAAC,6JAAU;gCAAC,WAAU;;kDACpB,wUAAC,4JAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,wUAAC,mUAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,wUAAC,8JAAW;;kDACV,wUAAC;wCAAI,WAAU;;4CACZ,mBAAmB,cAAc;4CAAG;;;;;;;kDAEvC,wUAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;YAQpD,gCACC;;kCACE,wUAAC,uJAAI;;0CACH,wUAAC,6JAAU;gCAAC,WAAU;;kDACpB,wUAAC,4JAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,wUAAC,mTAAM;wCAAC,WAAU;;;;;;;;;;;;0CAEpB,wUAAC,8JAAW;;kDACV,wUAAC;wCAAI,WAAU;kDAAsB,QAAQ,MAAM;;;;;;kDACnD,wUAAC;wCAAE,WAAU;;4CACV,mBAAmB,MAAM;4CAAC;4CAAe,gBAAgB,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAKvE,wUAAC,uJAAI;;0CACH,wUAAC,6JAAU;gCAAC,WAAU;;kDACpB,wUAAC,4JAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,wUAAC,mUAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,wUAAC,8JAAW;;kDACV,wUAAC;wCAAI,WAAU;;4CACZ,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,EAAE,GAAG,cAAc;4CAAG;;;;;;;kDAEjE,wUAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3D;KA9EgB", "debugId": null}}, {"offset": {"line": 1942, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/dashboard/dashboard-actions.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Plus, ArrowRight } from \"lucide-react\";\n\ninterface DashboardActionsProps {\n  userRole: string;\n}\n\nexport function DashboardActions({ userRole }: DashboardActionsProps) {\n  // Permission checks\n  const canCreateSessions = userRole === \"secretary_general\";\n  const canCreateCaisses = userRole === \"secretary_general\";\n  const canManageMembers = [\"secretary_general\", \"controller\"].includes(userRole);\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n      {canCreateSessions && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Nouvelle Session</CardTitle>\n            <CardDescription>\n              Créer une nouvelle session de tontine\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <Link href=\"/dashboard/sessions/new\">\n              <Button className=\"w-full\">\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Créer une session\n              </Button>\n            </Link>\n          </CardContent>\n        </Card>\n      )}\n\n      {canCreateCaisses && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Nouvelle Caisse</CardTitle>\n            <CardDescription>\n              Créer une nouvelle caisse pour la tontine\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <Link href=\"/dashboard/caisses/new\">\n              <Button className=\"w-full\">\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Créer une caisse\n              </Button>\n            </Link>\n          </CardContent>\n        </Card>\n      )}\n\n      {canManageMembers && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Gestion des Membres</CardTitle>\n            <CardDescription>\n              Ajouter et gérer les membres de la tontine\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <Link href=\"/dashboard/members/new\">\n                <Button className=\"w-full\">\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Nouveau membre\n                </Button>\n              </Link>\n              <Link href=\"/dashboard/members\">\n                <Button variant=\"outline\" className=\"w-full\">\n                  <ArrowRight className=\"h-4 w-4 mr-2\" />\n                  Voir tous les membres\n                </Button>\n              </Link>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAWO,SAAS,iBAAiB,KAAmC;QAAnC,EAAE,QAAQ,EAAyB,GAAnC;IAC/B,oBAAoB;IACpB,MAAM,oBAAoB,aAAa;IACvC,MAAM,mBAAmB,aAAa;IACtC,MAAM,mBAAmB;QAAC;QAAqB;KAAa,CAAC,QAAQ,CAAC;IAEtE,qBACE,wUAAC;QAAI,WAAU;;YACZ,mCACC,wUAAC,uJAAI;;kCACH,wUAAC,6JAAU;;0CACT,wUAAC,4JAAS;gCAAC,WAAU;0CAAU;;;;;;0CAC/B,wUAAC,kKAAe;0CAAC;;;;;;;;;;;;kCAInB,wUAAC,8JAAW;wBAAC,WAAU;kCACrB,cAAA,wUAAC,qTAAI;4BAAC,MAAK;sCACT,cAAA,wUAAC,2JAAM;gCAAC,WAAU;;kDAChB,wUAAC,6SAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;YAQ1C,kCACC,wUAAC,uJAAI;;kCACH,wUAAC,6JAAU;;0CACT,wUAAC,4JAAS;gCAAC,WAAU;0CAAU;;;;;;0CAC/B,wUAAC,kKAAe;0CAAC;;;;;;;;;;;;kCAInB,wUAAC,8JAAW;wBAAC,WAAU;kCACrB,cAAA,wUAAC,qTAAI;4BAAC,MAAK;sCACT,cAAA,wUAAC,2JAAM;gCAAC,WAAU;;kDAChB,wUAAC,6SAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;YAQ1C,kCACC,wUAAC,uJAAI;;kCACH,wUAAC,6JAAU;;0CACT,wUAAC,4JAAS;gCAAC,WAAU;0CAAU;;;;;;0CAC/B,wUAAC,kKAAe;0CAAC;;;;;;;;;;;;kCAInB,wUAAC,8JAAW;wBAAC,WAAU;kCACrB,cAAA,wUAAC;4BAAI,WAAU;;8CACb,wUAAC,qTAAI;oCAAC,MAAK;8CACT,cAAA,wUAAC,2JAAM;wCAAC,WAAU;;0DAChB,wUAAC,6SAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAIrC,wUAAC,qTAAI;oCAAC,MAAK;8CACT,cAAA,wUAAC,2JAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,wUAAC,mUAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzD;KA1EgB", "debugId": null}}, {"offset": {"line": 2207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/dashboard/dashboard-client.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useSession } from \"next-auth/react\";\nimport { useSessions, useCaisses } from \"@/hooks/queries\";\nimport { DashboardStats } from \"./dashboard-stats\";\nimport { DashboardActions } from \"./dashboard-actions\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { AlertCircle, Loader2 } from \"lucide-react\";\n\ninterface DashboardClientProps {\n  initialSessions?: any[];\n  initialCaisses?: any[];\n}\n\nexport function DashboardClient({ initialSessions = [], initialCaisses = [] }: DashboardClientProps) {\n  const { data: session } = useSession();\n  \n  // Use React Query with initial data\n  const sessionsQuery = useSessions();\n  const caissesQuery = useCaisses();\n\n  // Use React Query data if available, fallback to initial data\n  const sessions = sessionsQuery.data || initialSessions;\n  const caisses = caissesQuery.data || initialCaisses;\n  \n  const isLoading = sessionsQuery.isLoading || caissesQuery.isLoading;\n  const hasError = sessionsQuery.error || caissesQuery.error;\n\n  if (!session?.user) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <Card className=\"w-full max-w-md\">\n          <CardHeader className=\"text-center\">\n            <AlertCircle className=\"mx-auto h-8 w-8 text-yellow-500 mb-2\" />\n            <CardTitle>Session requise</CardTitle>\n            <CardDescription>\n              Veuillez vous connecter pour accéder au tableau de bord\n            </CardDescription>\n          </CardHeader>\n        </Card>\n      </div>\n    );\n  }\n\n  if (hasError) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <Card className=\"w-full max-w-md\">\n          <CardHeader className=\"text-center\">\n            <AlertCircle className=\"mx-auto h-8 w-8 text-red-500 mb-2\" />\n            <CardTitle>Erreur de chargement</CardTitle>\n            <CardDescription>\n              Impossible de charger les données du tableau de bord\n            </CardDescription>\n          </CardHeader>\n        </Card>\n      </div>\n    );\n  }\n\n  const userRole = (session.user as any)?.role || \"\";\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome section */}\n      <div>\n        <h1 className=\"text-2xl font-bold text-gray-900\">\n          Bienvenue, {(session.user as any)?.username}!\n        </h1>\n        <p className=\"text-gray-600 mt-1\">\n          Voici un aperçu de votre tontine aujourd'hui.\n        </p>\n        {session.user && (\n          <div className=\"mt-2 text-sm text-gray-500\">\n            Connecté en tant que{\" \"}\n            <span className=\"font-medium\">{session.user.username}</span> (\n            {session.user.role})\n          </div>\n        )}\n      </div>\n\n      {/* Loading indicator for real-time updates */}\n      {isLoading && (\n        <div className=\"flex items-center justify-center py-4\">\n          <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n          <span className=\"text-sm text-gray-500\">Mise à jour des données...</span>\n        </div>\n      )}\n\n      {/* Stats grid */}\n      <DashboardStats \n        sessions={sessions} \n        caisses={caisses} \n        userRole={userRole} \n      />\n\n      {/* Quick actions */}\n      <div>\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Actions rapides</h2>\n        <DashboardActions userRole={userRole} />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;;;AAPA;;;;;;;AAcO,SAAS,gBAAgB,KAAmE;QAAnE,EAAE,kBAAkB,EAAE,EAAE,iBAAiB,EAAE,EAAwB,GAAnE;QA8Cb,OAOG;;IApDpB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IAEpC,oCAAoC;IACpC,MAAM,gBAAgB,IAAA,wKAAW;IACjC,MAAM,eAAe,IAAA,sKAAU;IAE/B,8DAA8D;IAC9D,MAAM,WAAW,cAAc,IAAI,IAAI;IACvC,MAAM,UAAU,aAAa,IAAI,IAAI;IAErC,MAAM,YAAY,cAAc,SAAS,IAAI,aAAa,SAAS;IACnE,MAAM,WAAW,cAAc,KAAK,IAAI,aAAa,KAAK;IAE1D,IAAI,EAAC,oBAAA,8BAAA,QAAS,IAAI,GAAE;QAClB,qBACE,wUAAC;YAAI,WAAU;sBACb,cAAA,wUAAC,uJAAI;gBAAC,WAAU;0BACd,cAAA,wUAAC,6JAAU;oBAAC,WAAU;;sCACpB,wUAAC,sUAAW;4BAAC,WAAU;;;;;;sCACvB,wUAAC,4JAAS;sCAAC;;;;;;sCACX,wUAAC,kKAAe;sCAAC;;;;;;;;;;;;;;;;;;;;;;IAO3B;IAEA,IAAI,UAAU;QACZ,qBACE,wUAAC;YAAI,WAAU;sBACb,cAAA,wUAAC,uJAAI;gBAAC,WAAU;0BACd,cAAA,wUAAC,6JAAU;oBAAC,WAAU;;sCACpB,wUAAC,sUAAW;4BAAC,WAAU;;;;;;sCACvB,wUAAC,4JAAS;sCAAC;;;;;;sCACX,wUAAC,kKAAe;sCAAC;;;;;;;;;;;;;;;;;;;;;;IAO3B;IAEA,MAAM,WAAW,EAAA,QAAC,QAAQ,IAAI,cAAb,4BAAA,MAAuB,IAAI,KAAI;IAEhD,qBACE,wUAAC;QAAI,WAAU;;0BAEb,wUAAC;;kCACC,wUAAC;wBAAG,WAAU;;4BAAmC;6BACnC,SAAC,QAAQ,IAAI,cAAb,6BAAA,OAAuB,QAAQ;4BAAC;;;;;;;kCAE9C,wUAAC;wBAAE,WAAU;kCAAqB;;;;;;oBAGjC,QAAQ,IAAI,kBACX,wUAAC;wBAAI,WAAU;;4BAA6B;4BACrB;0CACrB,wUAAC;gCAAK,WAAU;0CAAe,QAAQ,IAAI,CAAC,QAAQ;;;;;;4BAAQ;4BAC3D,QAAQ,IAAI,CAAC,IAAI;4BAAC;;;;;;;;;;;;;YAMxB,2BACC,wUAAC;gBAAI,WAAU;;kCACb,wUAAC,+TAAO;wBAAC,WAAU;;;;;;kCACnB,wUAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;0BAK5C,wUAAC,sLAAc;gBACb,UAAU;gBACV,SAAS;gBACT,UAAU;;;;;;0BAIZ,wUAAC;;kCACC,wUAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,wUAAC,0LAAgB;wBAAC,UAAU;;;;;;;;;;;;;;;;;;AAIpC;GAzFgB;;QACY,8QAAU;QAGd,wKAAW;QACZ,sKAAU;;;KALjB", "debugId": null}}]}