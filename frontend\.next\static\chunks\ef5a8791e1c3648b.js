(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,44640,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ReflectAdapter",{enumerable:!0,get:function(){return s}});class s{static get(e,t,r){let s=Reflect.get(e,t,r);return"function"==typeof s?s.bind(e):s}static set(e,t,r,s){return Reflect.set(e,t,r,s)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},8356,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorageInstance",{enumerable:!0,get:function(){return s}});let s=(0,e.r(97918).createAsyncLocalStorage)()},62355,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorage",{enumerable:!0,get:function(){return s.afterTaskAsyncStorageInstance}});let s=e.r(8356)},17939,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{isRequestAPICallableInsideAfter:function(){return o},throwForSearchParamsAccessInUseCache:function(){return c},throwWithStaticGenerationBailoutError:function(){return n},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return i}});let s=e.r(85115),a=e.r(62355);function n(e,t){throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(e," couldn't be rendered statically because it used ").concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function i(e,t){throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(e,' with `dynamic = "error"` couldn\'t be rendered statically because it used ').concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function c(e,t){let r=Object.defineProperty(Error("Route ".concat(e.route,' used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(r,t),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=r),r}function o(){let e=a.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},93168,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{describeHasCheckingStringProperty:function(){return n},describeStringPropertyAccess:function(){return a},wellKnownProperties:function(){return i}});let s=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function a(e,t){return s.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function n(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let i=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},63470,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return o}});let s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var c=n?Object.getOwnPropertyDescriptor(e,i):null;c&&(c.get||c.set)?Object.defineProperty(s,i,c):s[i]=e[i]}return s.default=e,r&&r.set(e,s),s}(e.r(38477));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}let n={current:null},i="function"==typeof s.cache?s.cache:e=>e,c=console.warn;function o(e){return function(){for(var t=arguments.length,r=Array(t),s=0;s<t;s++)r[s]=arguments[s];c(e(...r))}}i(e=>{try{c(n.current)}finally{n.current=null}})},4467,e=>{"use strict";e.s(["useApi",()=>s]);var t=e.i(1269),r=e.i(1831);function s(){let{data:e}=(0,t.useSession)(),s=async function(t){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(null==e?void 0:e.accessToken))throw Error("Non authentifié");return r.apiService.authenticatedRequest(t,e.accessToken,s)};return{login:r.apiService.login.bind(r.apiService),register:r.apiService.register.bind(r.apiService),authenticatedRequest:s,getUsers:()=>s("/users"),getUser:e=>s("/users/".concat(e)),createUser:e=>s("/users",{method:"POST",body:JSON.stringify(e)}),updateUser:(e,t)=>s("/users/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteUser:e=>s("/users/".concat(e),{method:"DELETE"}),getSessions:()=>s("/sessions"),getSession:e=>s("/sessions/".concat(e)),createSession:e=>s("/sessions",{method:"POST",body:JSON.stringify(e)}),updateSession:(e,t)=>s("/sessions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteSession:e=>s("/sessions/".concat(e),{method:"DELETE"}),getCaisses:()=>s("/caisses"),getCaisse:e=>s("/caisses/".concat(e)),createCaisse:e=>s("/caisses",{method:"POST",body:JSON.stringify(e)}),updateCaisse:(e,t)=>s("/caisses/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteCaisse:e=>s("/caisses/".concat(e),{method:"DELETE"}),emargerCaisse:e=>s("/caisses/".concat(e,"/emarger"),{method:"POST"}),getReunions:()=>s("/reunions"),getReunion:e=>s("/reunions/".concat(e)),updateReunion:(e,t)=>s("/reunions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),getMembers:()=>s("/members"),getMember:e=>s("/members/".concat(e)),createMember:e=>s("/members",{method:"POST",body:JSON.stringify(e)}),updateMember:(e,t)=>s("/members/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteMember:e=>s("/members/".concat(e),{method:"DELETE"}),getMemberDebrief:(e,t)=>{let r=new URLSearchParams;(null==t?void 0:t.dateFrom)&&r.append("dateFrom",t.dateFrom),(null==t?void 0:t.dateTo)&&r.append("dateTo",t.dateTo),(null==t?void 0:t.sessionId)&&r.append("sessionId",t.sessionId);let a=r.toString()?"?".concat(r.toString()):"";return s("/members/".concat(e,"/debrief").concat(a))},createPayment:e=>s("/payments",{method:"POST",body:JSON.stringify(e)}),getSessionMembers:e=>s("/sessions/".concat(e,"/members")),addSessionMember:e=>s("/session-members",{method:"POST",body:JSON.stringify(e)}),removeSessionMember:(e,t)=>s("/sessions/".concat(e,"/members/").concat(t),{method:"DELETE"})}}},12058,e=>{"use strict";e.s(["CaisseType",()=>t,"PaymentDirection",()=>s,"PaymentFunction",()=>a,"UserRole",()=>r]);var t=function(e){return e.PRINCIPALE="PRINCIPALE",e.REUNION="REUNION",e}({}),r=function(e){return e.SECRETARY_GENERAL="secretary_general",e.CONTROLLER="controller",e.CASHIER="cashier",e}({}),s=function(e){return e.IN="IN",e.OUT="OUT",e}({}),a=function(e){return e.CONTRIBUTION="cotisation",e.TRANSFER="transfert",e.EXTERNAL="exterieur",e}({})},81787,e=>{"use strict";e.s(["Plus",()=>t],81787);let t=(0,e.i(44571).default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},2470,e=>{"use strict";e.s(["Badge",()=>i]);var t=e.i(4051),r=e.i(81221),s=e.i(62244),a=e.i(41428);let n=(0,s.cva)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:i,asChild:c=!1,...o}=e,l=c?r.Slot:"span";return(0,t.jsx)(l,{"data-slot":"badge",className:(0,a.cn)(n({variant:i}),s),...o})}},27973,e=>{"use strict";e.s(["Trash2",()=>t],27973);let t=(0,e.i(44571).default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},41850,e=>{"use strict";e.s(["Table",()=>s,"TableBody",()=>n,"TableCell",()=>o,"TableHead",()=>c,"TableHeader",()=>a,"TableRow",()=>i]);var t=e.i(4051),r=e.i(41428);function s(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",s),...a})})}function a(e){let{className:s,...a}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",s),...a})}function n(e){let{className:s,...a}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",s),...a})}function i(e){let{className:s,...a}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}function o(e){let{className:s,...a}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}},33617,e=>{"use strict";e.s(["default",()=>b]);var t=e.i(4051),r=e.i(38477),s=e.i(1269),a=e.i(81787),n=e.i(60019),i=e.i(27973),c=e.i(5085),o=e.i(85205),l=e.i(96134),d=e.i(75680),u=e.i(41850),h=e.i(2470),m=e.i(4467),f=e.i(12058);function b(){let{data:e}=(0,s.useSession)(),b=(0,m.useApi)(),[p,g]=(0,r.useState)([]),[x,y]=(0,r.useState)(!0),[v,j]=(0,r.useState)(""),N=(null==e?void 0:e.user)&&e.user.role===f.UserRole.SECRETARY_GENERAL;(0,r.useEffect)(()=>{(null==e?void 0:e.accessToken)&&S()},[e]);let S=async()=>{try{y(!0);let e=await b.getUsers();g(e)}catch(e){console.error("Erreur lors du chargement des utilisateurs:",e)}finally{y(!1)}},T=async e=>{if(confirm("Êtes-vous sûr de vouloir supprimer cet utilisateur ?"))try{await b.deleteUser(e),await S()}catch(e){console.error("Erreur lors de la suppression:",e)}},C=p.filter(e=>e.username.toLowerCase().includes(v.toLowerCase()));return N?x?(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"flex justify-center py-8",children:(0,t.jsx)("div",{className:"text-gray-500",children:"Chargement..."})})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Utilisateurs"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Gestion des comptes d'accès à l'application"})]}),(0,t.jsx)(c.default,{href:"/auth/register",children:(0,t.jsxs)(o.Button,{children:[(0,t.jsx)(a.Plus,{className:"h-4 w-4 mr-2"}),"Nouvel utilisateur"]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Total utilisateurs"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:p.length})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Secrétaires Généraux"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:p.filter(e=>e.role===f.UserRole.SECRETARY_GENERAL).length})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Contrôleurs"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:p.filter(e=>e.role===f.UserRole.CONTROLLER).length})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Caissiers"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:p.filter(e=>e.role===f.UserRole.CASHIER).length})})]})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(d.CardTitle,{children:"Liste des utilisateurs"}),(0,t.jsxs)(d.CardDescription,{children:[C.length," utilisateur(s)"]})]}),(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.Search,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(l.Input,{placeholder:"Rechercher un utilisateur...",value:v,onChange:e=>j(e.target.value),className:"pl-10 w-64"})]})})]})}),(0,t.jsx)(d.CardContent,{children:C.length>0?(0,t.jsxs)(u.Table,{children:[(0,t.jsx)(u.TableHeader,{children:(0,t.jsxs)(u.TableRow,{children:[(0,t.jsx)(u.TableHead,{children:"Nom d'utilisateur"}),(0,t.jsx)(u.TableHead,{children:"Rôle"}),(0,t.jsx)(u.TableHead,{children:"Créé le"}),(0,t.jsx)(u.TableHead,{children:"Actions"})]})}),(0,t.jsx)(u.TableBody,{children:C.map(e=>(0,t.jsxs)(u.TableRow,{children:[(0,t.jsx)(u.TableCell,{children:(0,t.jsx)("div",{className:"font-medium",children:e.username})}),(0,t.jsx)(u.TableCell,{children:(0,t.jsx)(h.Badge,{className:(e=>{switch(e){case f.UserRole.SECRETARY_GENERAL:return"bg-purple-100 text-purple-800";case f.UserRole.CONTROLLER:return"bg-blue-100 text-blue-800";case f.UserRole.CASHIER:return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}})(e.role),children:(e=>{switch(e){case f.UserRole.SECRETARY_GENERAL:return"Secrétaire Général";case f.UserRole.CONTROLLER:return"Contrôleur";case f.UserRole.CASHIER:return"Caissier";default:return e}})(e.role)})}),(0,t.jsx)(u.TableCell,{className:"text-gray-600",children:new Date(e.createdAt).toLocaleDateString("fr-FR",{year:"numeric",month:"short",day:"numeric"})}),(0,t.jsx)(u.TableCell,{children:(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsx)(o.Button,{variant:"ghost",size:"sm",onClick:()=>T(e._id),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(i.Trash2,{className:"h-4 w-4"})})})})]},e._id))})]}):(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:v?"Aucun utilisateur trouvé":"Aucun utilisateur"})})]})]}):(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Accès refusé"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Seul le Secrétaire Général peut accéder à cette page."})]})})})}}]);