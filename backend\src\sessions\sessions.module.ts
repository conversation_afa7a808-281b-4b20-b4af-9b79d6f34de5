import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import { SessionsController } from './sessions.controller';
import { SessionsService } from './sessions.service';
import { SessionsScheduler } from './sessions.scheduler';
import { Session, SessionSchema } from './schemas/session.schema';
import { SessionMember, SessionMemberSchema } from './schemas/session-member.schema';
import { Reunion, ReunionSchema } from '../reunions/schemas/reunion.schema';
import { Payment, PaymentSchema } from '../payments/schemas/payment.schema';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    MongooseModule.forFeature([
      { name: Session.name, schema: SessionSchema },
      { name: SessionMember.name, schema: SessionMemberSchema },
      { name: Reunion.name, schema: ReunionSchema },
      { name: Payment.name, schema: PaymentSchema },
    ]),
  ],
  controllers: [SessionsController],
  providers: [SessionsService, SessionsScheduler],
  exports: [SessionsService],
})
export class SessionsModule {}