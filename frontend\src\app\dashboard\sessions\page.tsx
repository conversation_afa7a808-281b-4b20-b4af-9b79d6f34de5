"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import {
	Plus,
	Search,
	Filter,
	MoreHorizontal,
	Edit,
	Trash2,
	Calendar,
	Users,
	DollarSign,
} from "lucide-react";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useApi } from "@/hooks/use-api";
import { Session, SessionStats } from "@/types";

export default function SessionsPage() {
	const { data: session } = useSession();
	const api = useApi();

	const [sessions, setSessions] = useState<Session[]>([]);
	const [stats, setStats] = useState<SessionStats | null>(null);
	const [loading, setLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [yearFilter, setYearFilter] = useState<string>("all");

	// Vérifier les permissions
	const canManageSessions =
		session?.user &&
		((session.user as any).role === "secretary_general" ||
			(session.user as any).role === "controller" ||
			(session.user as any).role === "cashier");
	const canCreateSessions =
		session?.user && (session.user as any).role === "secretary_general";

	const loadData = async () => {
		try {
			setLoading(true);
			const sessionsData = await api.getSessions();
			setSessions(sessionsData);

			// Calculer les statistiques
			const now = new Date();
			const active = sessionsData.filter(
				(s) => new Date(s.dateDebut) <= now && new Date(s.dateFin) >= now
			).length;
			const completed = sessionsData.filter(
				(s) => new Date(s.dateFin) < now
			).length;
			const totalPartFixe = sessionsData.reduce(
				(sum, s) => sum + s.partFixe,
				0
			);

			setStats({
				total: sessionsData.length,
				active,
				completed,
				totalPartFixe,
			});
		} catch (error) {
			console.error("Erreur lors du chargement des sessions:", error);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (session?.accessToken) {
			loadData();
		}
	}, [session]);

	const handleDelete = async (sessionId: string) => {
		if (!confirm("Êtes-vous sûr de vouloir supprimer cette session ?")) {
			return;
		}

		try {
			await api.deleteSession(sessionId);
			loadData();
		} catch (error) {
			console.error("Erreur lors de la suppression:", error);
		}
	};

	// Filtrer les sessions
	const filteredSessions = sessions.filter((session) => {
		const matchesSearch =
			session.annee.toString().includes(searchTerm) ||
			new Date(session.dateDebut)
				.toLocaleDateString()
				.includes(searchTerm) ||
			new Date(session.dateFin).toLocaleDateString().includes(searchTerm);

		const matchesYear =
			yearFilter === "all" || session.annee.toString() === yearFilter;

		return matchesSearch && matchesYear;
	});

	// Obtenir les années uniques pour le filtre
	const uniqueYears = Array.from(
		new Set(sessions.map((s) => s.annee.toString()))
	).sort();

	const getSessionStatus = (session: Session) => {
		const now = new Date();
		const start = new Date(session.dateDebut);
		const end = new Date(session.dateFin);

		if (now < start) return { label: "À venir", variant: "secondary" as const };
		if (now > end) return { label: "Terminée", variant: "outline" as const };
		return { label: "Active", variant: "default" as const };
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
					<p className="mt-2 text-sm text-gray-600">
						Chargement des sessions...
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-3xl font-bold tracking-tight">Sessions</h1>
					<p className="text-muted-foreground">
						Gérez les sessions de tontine et leurs réunions
					</p>
				</div>
				{canCreateSessions && (
					<Button asChild>
						<Link href="/dashboard/sessions/new">
							<Plus className="mr-2 h-4 w-4" />
							Nouvelle session
						</Link>
					</Button>
				)}
			</div>

			{/* Statistiques */}
			{stats && (
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Total Sessions
							</CardTitle>
							<Calendar className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{stats.total}</div>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Sessions Actives
							</CardTitle>
							<Users className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{stats.active}</div>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Sessions Terminées
							</CardTitle>
							<Calendar className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{stats.completed}</div>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Part Fixe Totale
							</CardTitle>
							<DollarSign className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{stats.totalPartFixe.toLocaleString()} FCFA
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Filtres */}
			<Card>
				<CardHeader>
					<CardTitle>Filtres</CardTitle>
					<CardDescription>
						Recherchez et filtrez les sessions
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="flex gap-4">
						<div className="flex-1">
							<div className="relative">
								<Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
								<Input
									placeholder="Rechercher par année ou date..."
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className="pl-8"
								/>
							</div>
						</div>
						<Select value={yearFilter} onValueChange={setYearFilter}>
							<SelectTrigger className="w-[180px]">
								<SelectValue placeholder="Filtrer par année" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Toutes les années</SelectItem>
								{uniqueYears.map((year) => (
									<SelectItem key={year} value={year}>
										{year}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
				</CardContent>
			</Card>

			{/* Tableau des sessions */}
			<Card>
				<CardHeader>
					<CardTitle>Sessions ({filteredSessions.length})</CardTitle>
				</CardHeader>
				<CardContent>
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Année</TableHead>
								<TableHead>Période</TableHead>
								<TableHead>Part Fixe</TableHead>
								<TableHead>Statut</TableHead>
								<TableHead>Créée le</TableHead>
								{canManageSessions && <TableHead>Actions</TableHead>}
							</TableRow>
						</TableHeader>
						<TableBody>
							{filteredSessions.map((session) => {
								const status = getSessionStatus(session);
								return (
									<TableRow key={session._id}>
										<TableCell className="font-medium">
											{session.annee}
										</TableCell>
										<TableCell>
											{new Date(session.dateDebut).toLocaleDateString()} -{" "}
											{new Date(session.dateFin).toLocaleDateString()}
										</TableCell>
										<TableCell>
											{session.partFixe.toLocaleString()} FCFA
										</TableCell>
										<TableCell>
											<Badge variant={status.variant}>{status.label}</Badge>
										</TableCell>
										<TableCell>
											{new Date(session.createdAt).toLocaleDateString()}
										</TableCell>
										{canManageSessions && (
											<TableCell>
												<DropdownMenu>
													<DropdownMenuTrigger asChild>
														<Button variant="ghost" className="h-8 w-8 p-0">
															<MoreHorizontal className="h-4 w-4" />
														</Button>
													</DropdownMenuTrigger>
													<DropdownMenuContent align="end">
														<DropdownMenuItem asChild>
															<Link
																href={`/dashboard/sessions/${session._id}/edit`}
															>
																<Edit className="mr-2 h-4 w-4" />
																Modifier
															</Link>
														</DropdownMenuItem>
														{canCreateSessions && (
															<DropdownMenuItem
																onClick={() => handleDelete(session._id)}
																className="text-red-600"
															>
																<Trash2 className="mr-2 h-4 w-4" />
																Supprimer
															</DropdownMenuItem>
														)}
													</DropdownMenuContent>
												</DropdownMenu>
											</TableCell>
										)}
									</TableRow>
								);
							})}
						</TableBody>
					</Table>
					{filteredSessions.length === 0 && (
						<div className="text-center py-8">
							<p className="text-muted-foreground">Aucune session trouvée</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
