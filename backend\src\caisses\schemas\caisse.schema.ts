import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

export enum CaisseType {
  PRINCIPALE = 'PRINCIPALE',
  REUNION = 'REUNION',
}

export enum CaisseStatus {
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
}

export type CaisseDocument = HydratedDocument<Caisse>;

@Schema({ timestamps: true })
export class Caisse {
  @Prop({ required: true })
  nom!: string;
 
  @Prop({ required: true, enum: [CaisseType.PRINCIPALE, CaisseType.REUNION] })
  type!: CaisseType;

  @Prop({ type: Number, default: 0 })
  soldeActuel!: number;

  // Only for reunion-type caisses lifecycle
  @Prop({ type: Types.ObjectId, ref: 'Session', required: false })
  sessionId?: Types.ObjectId;

  // creator
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  createdBy!: Types.ObjectId;

  // cashier assigned (only for REUNION type)
  @Prop({ type: Types.ObjectId, ref: 'User', required: false })
  cashierId?: Types.ObjectId;

  // principal caisse linked for emargement
  @Prop({ type: Types.ObjectId, ref: 'Caisse', required: false })
  caissePrincipaleId?: Types.ObjectId;

  // status
  @Prop({ required: true, enum: [CaisseStatus.OPEN, CaisseStatus.CLOSED], default: CaisseStatus.CLOSED })
  status!: CaisseStatus;

  // open/close timestamps (for traceability)
  @Prop({ type: Date, required: false })
  lastOpenedAt?: Date;

  @Prop({ type: Date, required: false })
  lastClosedAt?: Date;
}

export const CaisseSchema = SchemaFactory.createForClass(Caisse);