module.exports=[78184,a=>{"use strict";a.s(["Input",()=>d]);var b=a.i(68116),c=a.i(22171);function d({className:a,type:d,...e}){return(0,b.jsx)("input",{type:d,"data-slot":"input",className:(0,c.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...e})}},48206,a=>{"use strict";a.s(["Primitive",()=>f,"dispatchDiscreteCustomEvent",()=>g]);var b=a.i(128),c=a.i(60443),d=a.i(85689),e=a.i(68116),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,c)=>{let f=(0,d.createSlot)(`Primitive.${c}`),g=b.forwardRef((a,b)=>{let{asChild:d,...g}=a;return(0,e.jsx)(d?f:c,{...g,ref:b})});return g.displayName=`Primitive.${c}`,{...a,[c]:g}},{});function g(a,b){a&&c.flushSync(()=>a.dispatchEvent(b))}},41405,a=>{"use strict";a.s(["Label",()=>g],41405);var b=a.i(68116),c=a.i(128),d=a.i(48206),e=c.forwardRef((a,c)=>(0,b.jsx)(d.Primitive.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));e.displayName="Label";var f=a.i(22171);function g({className:a,...c}){return(0,b.jsx)(e,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...c})}},31933,a=>{"use strict";a.s(["default",()=>q]);var b=a.i(68116),c=a.i(128),d=a.i(50395),e=a.i(64853),f=a.i(54636),g=a.i(66446),h=a.i(33055),i=a.i(2979),j=a.i(78184),k=a.i(75780),l=a.i(60563),m=a.i(44932),n=a.i(34075),o=a.i(78655);let p=g.z.object({username:g.z.string().min(3,"Le nom d'utilisateur doit contenir au moins 3 caractères"),password:g.z.string().min(6,"Le mot de passe doit contenir au moins 6 caractères"),role:g.z.nativeEnum(o.UserRole)});function q(){let[a,g]=(0,c.useState)(!1),[q,r]=(0,c.useState)(null),[s,t]=(0,c.useState)(!1),u=(0,d.useRouter)(),v=(0,e.useForm)({resolver:(0,f.zodResolver)(p),defaultValues:{username:"",password:"",role:o.UserRole.CASHIER}}),w=async a=>{g(!0),r(null);try{let b=await n.apiService.register({...a});console.log("Inscription réussie:",b),t(!0),setTimeout(()=>{u.push("/auth/signin")},2e3)}catch(a){console.error("Erreur d'inscription:",a),a instanceof Error?r(a.message):r("Erreur lors de la création du compte. Veuillez réessayer.")}finally{g(!1)}};return s?(0,b.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,b.jsx)(k.Card,{className:"w-full max-w-md",children:(0,b.jsxs)(k.CardHeader,{className:"text-center",children:[(0,b.jsx)(k.CardTitle,{className:"text-2xl font-bold text-green-600",children:"Compte créé avec succès !"}),(0,b.jsx)(k.CardDescription,{children:"Vous allez être redirigé vers la page de connexion..."})]})})}):(0,b.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,b.jsxs)(k.Card,{className:"w-full max-w-md",children:[(0,b.jsxs)(k.CardHeader,{className:"space-y-1",children:[(0,b.jsx)(k.CardTitle,{className:"text-2xl font-bold text-center",children:"Créer un compte"}),(0,b.jsx)(k.CardDescription,{className:"text-center",children:"Créez votre compte Tontine"}),(0,b.jsx)("div",{className:"text-xs text-orange-600 text-center bg-orange-50 p-2 rounded",children:"⚠️ Page temporaire - Sera supprimée en production"})]}),(0,b.jsxs)(k.CardContent,{children:[(0,b.jsx)(l.Form,{...v,children:(0,b.jsxs)("form",{onSubmit:v.handleSubmit(w),className:"space-y-4",children:[(0,b.jsx)(l.FormField,{control:v.control,name:"username",render:({field:c})=>(0,b.jsxs)(l.FormItem,{children:[(0,b.jsx)(l.FormLabel,{children:"Nom d'utilisateur"}),(0,b.jsx)(l.FormControl,{children:(0,b.jsx)(j.Input,{placeholder:"Nom d'utilisateur unique",...c,disabled:a})}),(0,b.jsx)(l.FormMessage,{})]})}),(0,b.jsx)(l.FormField,{control:v.control,name:"password",render:({field:c})=>(0,b.jsxs)(l.FormItem,{children:[(0,b.jsx)(l.FormLabel,{children:"Mot de passe"}),(0,b.jsx)(l.FormControl,{children:(0,b.jsx)(j.Input,{type:"password",placeholder:"Minimum 6 caractères",...c,disabled:a})}),(0,b.jsx)(l.FormMessage,{})]})}),(0,b.jsx)(l.FormField,{control:v.control,name:"role",render:({field:c})=>(0,b.jsxs)(l.FormItem,{children:[(0,b.jsx)(l.FormLabel,{children:"Rôle"}),(0,b.jsxs)(m.Select,{onValueChange:c.onChange,defaultValue:c.value,children:[(0,b.jsx)(l.FormControl,{children:(0,b.jsx)(m.SelectTrigger,{disabled:a,children:(0,b.jsx)(m.SelectValue,{placeholder:"Sélectionnez un rôle"})})}),(0,b.jsxs)(m.SelectContent,{children:[(0,b.jsx)(m.SelectItem,{value:o.UserRole.CASHIER,children:"Caissier"}),(0,b.jsx)(m.SelectItem,{value:o.UserRole.CONTROLLER,children:"Contrôleur"}),(0,b.jsx)(m.SelectItem,{value:o.UserRole.SECRETARY_GENERAL,children:"Secrétaire Général"})]})]}),(0,b.jsx)(l.FormMessage,{})]})}),q&&(0,b.jsx)("div",{className:"text-red-600 text-sm text-center bg-red-50 p-2 rounded",children:q}),(0,b.jsx)(i.Button,{type:"submit",className:"w-full",disabled:a,children:a?"Création du compte...":"Créer le compte"})]})}),(0,b.jsxs)("div",{className:"mt-4 text-center text-sm text-gray-600",children:["Vous avez déjà un compte ?"," ",(0,b.jsx)(h.default,{href:"/auth/signin",className:"text-blue-600 hover:underline",children:"Se connecter"})]})]})]})})}}];

//# sourceMappingURL=frontend_0a23aa32._.js.map