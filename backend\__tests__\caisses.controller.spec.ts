import 'reflect-metadata';
import { Test } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { CaissesController } from '../src/caisses/caisses.controller';
import { CaissesService } from '../src/caisses/caisses.service';
import { AppTestingModule } from './app-testing.module';

const serviceMock = {
  create: jest.fn().mockResolvedValue({ _id: 'c1' }),
  findAll: jest.fn().mockResolvedValue([{ _id: 'c1' }]),
  findOne: jest.fn().mockResolvedValue({ _id: 'c1' }),
  update: jest.fn().mockResolvedValue({ _id: 'c1' }),
  remove: jest.fn().mockResolvedValue({}),
  open: jest.fn().mockResolvedValue({ ok: true }),
  close: jest.fn().mockResolvedValue({ ok: true }),
  createExitOrder: jest.fn().mockResolvedValue({ _id: 'eo1' }),
  listExitOrders: jest.fn().mockResolvedValue([{ _id: 'eo1' }]),
  listCashierExitOrders: jest.fn().mockResolvedValue([{ _id: 'eo1' }]),
  removeExitOrder: jest.fn().mockResolvedValue({}),
  closeExitOrder: jest.fn().mockResolvedValue({ ok: true }),
  emargerCaisse: jest.fn().mockResolvedValue({ ok: true }),
  renflouerCaisse: jest.fn().mockResolvedValue({ ok: true }),
  extractCaisse: jest.fn().mockResolvedValue({ ok: true }),
  memberContribute: jest.fn().mockResolvedValue({ ok: true }),
  nextContribution: jest.fn().mockResolvedValue({ nextReunion: null, amountDue: 0 }),
  debriefCaisse: jest.fn().mockResolvedValue({ totalIn: 0, totalOut: 0 }),
  myDebrief: jest.fn().mockResolvedValue({ totalIn: 0, totalOut: 0 }),
};

describe('CaissesController', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      controllers: [CaissesController],
      providers: [{ provide: CaissesService, useValue: serviceMock }],
    })
      .overrideGuard(require('../src/auth/jwt-auth.guard').JwtAuthGuard)
      .useClass(require('./utils/mock-guards').MockJwtAuthGuard)
      .overrideGuard(require('../src/common/guards/role.guard').RolesGuard)
      .useClass(require('./utils/mock-guards').MockRolesGuard)
      .compile();

    app = moduleRef.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('POST /caisses', async () => {
    await request(app.getHttpServer()).post('/caisses').set('x-user-role','secretary_general').send({ nom: 'X', type: 'REUNION' }).expect(201);
  });

  it('GET /caisses', async () => {
    await request(app.getHttpServer()).get('/caisses').set('x-user-role','cashier').expect(200);
  });

  it('GET /caisses/:id', async () => {
    await request(app.getHttpServer()).get('/caisses/c1').set('x-user-role','controller').expect(200);
  });

  it('PATCH /caisses/:id', async () => {
    await request(app.getHttpServer()).patch('/caisses/c1').set('x-user-role','secretary_general').send({ nom: 'Y' }).expect(200);
  });

  it('DELETE /caisses/:id', async () => {
    await request(app.getHttpServer()).delete('/caisses/c1').set('x-user-role','secretary_general').expect(200);
  });

  it('POST /caisses/:id/open', async () => {
    await request(app.getHttpServer()).post('/caisses/c1/open').set('x-user-role','cashier').set('x-user-id','u1').expect(201);
  });

  it('POST /caisses/:id/close', async () => {
    await request(app.getHttpServer()).post('/caisses/c1/close').set('x-user-role','controller').expect(201);
  });

  it('POST /caisses/:id/exit-orders', async () => {
    await request(app.getHttpServer()).post('/caisses/c1/exit-orders').set('x-user-role','secretary_general').send({ amount: 1000 }).expect(201);
  });

  it('GET /caisses/:id/exit-orders', async () => {
    await request(app.getHttpServer()).get('/caisses/c1/exit-orders').set('x-user-role','controller').expect(200);
  });

  it('GET /caisses/:id/my-exit-orders', async () => {
    await request(app.getHttpServer()).get('/caisses/c1/my-exit-orders').set('x-user-role','cashier').set('x-user-id','u1').expect(200);
  });

  it('DELETE /caisses/exit-orders/:exitOrderId', async () => {
    await request(app.getHttpServer()).delete('/caisses/exit-orders/eo1').set('x-user-role','secretary_general').set('x-user-id','u1').expect(200);
  });

  it('POST /caisses/:id/exit-orders/close', async () => {
    await request(app.getHttpServer()).post('/caisses/c1/exit-orders/close').set('x-user-role','cashier').set('x-user-id','u1').send({ exitOrderId: 'eo1' }).expect(201);
  });

  it('POST /caisses/:id/emarger', async () => {
    await request(app.getHttpServer()).post('/caisses/c1/emarger').set('x-user-role','secretary_general').expect(201);
  });

  it('POST /caisses/:id/renflouer', async () => {
    await request(app.getHttpServer()).post('/caisses/c1/renflouer').set('x-user-role','secretary_general').send({ amount: 1000 }).expect(201);
  });

  it('POST /caisses/extraction', async () => {
    await request(app.getHttpServer()).post('/caisses/extraction').set('x-user-role','secretary_general').send({ amount: 1000 }).expect(201);
  });

  it('POST /caisses/:id/cotiser', async () => {
    await request(app.getHttpServer()).post('/caisses/c1/cotiser').set('x-user-role','cashier').set('x-user-id','u1').send({ memberId: 'm1', amount: 2000 }).expect(201);
  });

  it('GET /caisses/:id/next-contribution/:sessionId/:memberId', async () => {
    await request(app.getHttpServer()).get('/caisses/c1/next-contribution/s1/m1').set('x-user-role','controller').expect(200);
  });

  it('GET /caisses/:id/debrief', async () => {
    await request(app.getHttpServer()).get('/caisses/c1/debrief').set('x-user-role','controller').expect(200);
  });

  it('GET /caisses/:id/mydebrief', async () => {
    await request(app.getHttpServer()).get('/caisses/c1/mydebrief').set('x-user-role','cashier').set('x-user-id','u1').expect(200);
  });
});