{"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-label@2.1.7_261ca6dc9b795d3e6e9f99d20849d772/node_modules/@radix-ui/react-label/dist/index.mjs", "turbopack:///[project]/frontend/src/components/ui/label.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/arrow-left.ts", "turbopack:///[project]/frontend/src/components/ui/textarea.tsx", "turbopack:///[project]/frontend/src/app/dashboard/payments/new/page.tsx"], "sourcesContent": ["\"use client\";\n\n// src/label.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Label\";\nvar Label = React.forwardRef((props, forwardedRef) => {\n  return /* @__PURE__ */ jsx(\n    Primitive.label,\n    {\n      ...props,\n      ref: forwardedRef,\n      onMouseDown: (event) => {\n        const target = event.target;\n        if (target.closest(\"button, input, select, textarea\")) return;\n        props.onMouseDown?.(event);\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }\n    }\n  );\n});\nLabel.displayName = NAME;\nvar Root = Label;\nexport {\n  Label,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\n\nimport { cn } from '@/lib/utils';\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot='label'\n      className={cn(\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Label };\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n", "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n", "\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useR<PERSON>er, useSearchPara<PERSON> } from \"next/navigation\";\nimport { useSession } from \"next-auth/react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { ArrowLeft } from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport {\n\tForm,\n\tFormControl,\n\tFormField,\n\tFormItem,\n\tFormLabel,\n\tFormMessage,\n} from \"@/components/ui/form\";\nimport {\n\tSelect,\n\tSelectContent,\n\tSelectItem,\n\tSelectTrigger,\n\tSelectValue,\n} from \"@/components/ui/select\";\nimport { useApi } from \"@/hooks/use-api\";\nimport { \n\tUserRole, \n\tPaymentDirection, \n\tPaymentFunction, \n\tCaisse, \n\tMember, \n\tSession,\n\tPaymentForm \n} from \"@/types\";\n\nconst paymentSchema = z.object({\n\tdirection: z.nativeEnum(PaymentDirection),\n\tfunc: z.nativeEnum(PaymentFunction),\n\tamount: z.number().positive(\"Le montant doit être positif\"),\n\tcaisseId: z.string().min(1, \"Veuillez sélectionner une caisse\"),\n\treceivingCaisseId: z.string().optional(),\n\tsessionId: z.string().optional(),\n\treunionId: z.string().optional(),\n\tmemberId: z.string().optional(),\n\treason: z.string().optional(),\n});\n\ntype PaymentFormData = z.infer<typeof paymentSchema>;\n\nexport default function NewPaymentPage() {\n\tconst { data: session } = useSession();\n\tconst router = useRouter();\n\tconst searchParams = useSearchParams();\n\tconst api = useApi();\n\n\tconst [isLoading, setIsLoading] = useState(false);\n\tconst [error, setError] = useState<string | null>(null);\n\tconst [caisses, setCaisses] = useState<Caisse[]>([]);\n\tconst [members, setMembers] = useState<Member[]>([]);\n\tconst [sessions, setSessions] = useState<Session[]>([]);\n\n\t// Type de paiement pré-sélectionné depuis l'URL\n\tconst preselectedType = searchParams.get('type');\n\n\t// Vérifier les permissions\n\tconst canCreatePayments =\n\t\tsession?.user &&\n\t\t((session.user as any).role === UserRole.SECRETARY_GENERAL ||\n\t\t\t(session.user as any).role === UserRole.CONTROLLER ||\n\t\t\t(session.user as any).role === UserRole.CASHIER);\n\n\tconst form = useForm<PaymentFormData>({\n\t\tresolver: zodResolver(paymentSchema),\n\t\tdefaultValues: {\n\t\t\tdirection: PaymentDirection.IN,\n\t\t\tfunc: preselectedType === 'contribution' ? PaymentFunction.CONTRIBUTION :\n\t\t\t\t  preselectedType === 'transfer' ? PaymentFunction.TRANSFER :\n\t\t\t\t  preselectedType === 'external' ? PaymentFunction.EXTERNAL :\n\t\t\t\t  PaymentFunction.CONTRIBUTION,\n\t\t\tamount: 0,\n\t\t\tcaisseId: \"\",\n\t\t\treceivingCaisseId: \"\",\n\t\t\tsessionId: \"\",\n\t\t\treunionId: \"\",\n\t\t\tmemberId: \"\",\n\t\t\treason: \"\",\n\t\t},\n\t});\n\n\tconst watchedFunc = form.watch(\"func\");\n\tconst watchedDirection = form.watch(\"direction\");\n\n\tuseEffect(() => {\n\t\tif (session?.accessToken) {\n\t\t\tloadData();\n\t\t}\n\t}, [session]);\n\n\t// Ajuster la direction selon la fonction\n\tuseEffect(() => {\n\t\tif (watchedFunc === PaymentFunction.CONTRIBUTION) {\n\t\t\tform.setValue(\"direction\", PaymentDirection.IN);\n\t\t}\n\t}, [watchedFunc, form]);\n\n\tconst loadData = async () => {\n\t\ttry {\n\t\t\tconst [caissesData, membersData, sessionsData] = await Promise.all([\n\t\t\t\tapi.getCaisses(),\n\t\t\t\tapi.getMembers(),\n\t\t\t\tapi.getSessions(),\n\t\t\t]);\n\n\t\t\tsetCaisses(caissesData);\n\t\t\tsetMembers(membersData);\n\t\t\tsetSessions(sessionsData);\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors du chargement des données:\", error);\n\t\t}\n\t};\n\n\tconst onSubmit = async (data: PaymentFormData) => {\n\t\tsetIsLoading(true);\n\t\tsetError(null);\n\n\t\ttry {\n\t\t\t// Nettoyer les données selon le type de paiement\n\t\t\tconst cleanData: any = {\n\t\t\t\tdirection: data.direction,\n\t\t\t\tfunc: data.func,\n\t\t\t\tamount: data.amount,\n\t\t\t\tcaisseId: data.caisseId,\n\t\t\t};\n\n\t\t\t// Ajouter les champs conditionnels\n\t\t\tif (data.receivingCaisseId) cleanData.receivingCaisseId = data.receivingCaisseId;\n\t\t\tif (data.sessionId) cleanData.sessionId = data.sessionId;\n\t\t\tif (data.reunionId) cleanData.reunionId = data.reunionId;\n\t\t\tif (data.memberId) cleanData.memberId = data.memberId;\n\t\t\tif (data.reason) cleanData.reason = data.reason;\n\n\t\t\tawait api.createPayment(cleanData);\n\t\t\trouter.push(\"/dashboard/payments\");\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors de la création du paiement:\", error);\n\t\t\tif (error instanceof Error) {\n\t\t\t\tsetError(error.message);\n\t\t\t} else {\n\t\t\t\tsetError(\"Erreur lors de la création du paiement. Veuillez réessayer.\");\n\t\t\t}\n\t\t} finally {\n\t\t\tsetIsLoading(false);\n\t\t}\n\t};\n\n\tif (!canCreatePayments) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Link href=\"/dashboard/payments\">\n\t\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tRetour\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\t\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<h2 className=\"text-lg font-semibold text-gray-900\">Accès refusé</h2>\n\t\t\t\t\t\t<p className=\"text-gray-600\">\n\t\t\t\t\t\t\tVous n'avez pas les permissions pour créer des paiements.\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* En-tête */}\n\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t<Link href=\"/dashboard/payments\">\n\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\tRetour\n\t\t\t\t\t</Button>\n\t\t\t\t</Link>\n\t\t\t\t<div>\n\t\t\t\t\t<h1 className=\"text-2xl font-bold text-gray-900\">Nouveau Paiement</h1>\n\t\t\t\t\t<p className=\"text-gray-600\">Enregistrer un nouveau paiement</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t{/* Formulaire */}\n\t\t\t<Card className=\"max-w-2xl\">\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle>Informations du paiement</CardTitle>\n\t\t\t\t\t<CardDescription>\n\t\t\t\t\t\tRemplissez les informations du paiement à enregistrer\n\t\t\t\t\t</CardDescription>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<Form {...form}>\n\t\t\t\t\t\t<form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n\t\t\t\t\t\t\t{/* Type de paiement */}\n\t\t\t\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"func\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Fonction *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\t\tonValueChange={field.onChange}\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={field.value}\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Type de paiement\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value={PaymentFunction.CONTRIBUTION}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tCotisation\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value={PaymentFunction.TRANSFER}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tTransfert\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value={PaymentFunction.EXTERNAL}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tExterne\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"direction\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Direction *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\t\tonValueChange={field.onChange}\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={field.value}\n\t\t\t\t\t\t\t\t\t\t\t\tdisabled={watchedFunc === PaymentFunction.CONTRIBUTION}\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Direction\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value={PaymentDirection.IN}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tEntrée\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value={PaymentDirection.OUT}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tSortie\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t{/* Montant */}\n\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\tname=\"amount\"\n\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t<FormLabel>Montant *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"0\"\n\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => field.onChange(Number(e.target.value))}\n\t\t\t\t\t\t\t\t\t\t\t\tdisabled={isLoading}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t{/* Caisse */}\n\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\tname=\"caisseId\"\n\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t<FormLabel>Caisse *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\tonValueChange={field.onChange}\n\t\t\t\t\t\t\t\t\t\t\tvalue={field.value}\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Sélectionner une caisse\" />\n\t\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t{caisses.map((caisse) => (\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem key={caisse._id} value={caisse._id}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{caisse.nom} ({caisse.type})\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t{/* Champs conditionnels */}\n\t\t\t\t\t\t\t{watchedFunc === PaymentFunction.TRANSFER && (\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"receivingCaisseId\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Caisse de destination *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\t\tonValueChange={field.onChange}\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={field.value}\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Caisse de destination\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{caisses.map((caisse) => (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem key={caisse._id} value={caisse._id}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{caisse.nom} ({caisse.type})\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t\t{watchedFunc === PaymentFunction.CONTRIBUTION && (\n\t\t\t\t\t\t\t\t<>\n\t\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\t\tname=\"memberId\"\n\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Membre *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\t\t\tonValueChange={field.onChange}\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={field.value}\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Sélectionner un membre\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{members.map((member) => (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem key={member._id} value={member._id}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{member.firstName} {member.lastName}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\t\tname=\"sessionId\"\n\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Session</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\t\t\tonValueChange={field.onChange}\n\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={field.value}\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Sélectionner une session\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{sessions.map((session) => (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem key={session._id} value={session._id}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tSession {session.annee}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</>\n\t\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t\t{watchedFunc === PaymentFunction.EXTERNAL && watchedDirection === PaymentDirection.OUT && (\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"reason\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Motif *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Textarea\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Motif du paiement externe\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled={isLoading}\n\t\t\t\t\t\t\t\t\t\t\t\t\trows={3}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t\t{/* Message d'erreur */}\n\t\t\t\t\t\t\t{error && (\n\t\t\t\t\t\t\t\t<div className=\"text-red-600 text-sm bg-red-50 p-3 rounded\">\n\t\t\t\t\t\t\t\t\t{error}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t\t{/* Actions */}\n\t\t\t\t\t\t\t<div className=\"flex justify-end gap-4 pt-6\">\n\t\t\t\t\t\t\t\t<Link href=\"/dashboard/payments\">\n\t\t\t\t\t\t\t\t\t<Button variant=\"outline\" disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t\tAnnuler\n\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t\t\t<Button type=\"submit\" disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t{isLoading ? \"Enregistrement...\" : \"Enregistrer le paiement\"}\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</form>\n\t\t\t\t\t</Form>\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": "sQAGA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OAGI,EAAQ,EAAA,UAAgB,CAAC,CAAC,EAAO,IACZ,CAAA,EAAA,EAAA,GAAA,AAAG,EACxB,EAAA,SAAS,CAAC,KAAK,CACf,CACE,GAAG,CAAK,CACR,IAAK,EACL,YAAa,AAAC,IACG,AACX,EADiB,MAAM,CAChB,OAAO,CAAC,oCAAoC,CACvD,EAAM,WAAW,GAAG,GAChB,CAAC,EAAM,gBAAgB,EAAI,EAAM,MAAM,CAAG,GAAG,EAAM,cAAc,GACvE,CACF,GAGJ,GAAM,WAAW,CAhBN,EAgBS,MCjBpB,IAAA,EAAA,EAAA,CAAA,CAAA,OAEA,SAAS,EAAM,WACb,CAAS,CACT,GAAG,EAC8C,EACjD,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,ADWM,ECXN,CACC,YAAU,QACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,sNACA,GAED,GAAG,CAAK,EAGf,uDCFA,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAAM,CAAA,EAAY,CAAA,CAAA,CAAA,CAAA,AAAZ,CAAA,AAAY,CAAZ,AAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAhBC,CAClC,AAe+C,CAf9C,AAe8C,CAf9C,AAe8C,CAf9C,AAe8C,CAf9C,AAe8C,CAf9C,AAe8C,CAf9C,AAe8C,CAf9C,AAe8C,CAf9C,AAAQ,AAesC,CAfpC,AAAF,AAesC,CAAU,CAAA,AAf3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,AAAlB,CAAkB,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAC/C,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAY,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAC3C,mECJA,EAAA,EAAA,CAAA,CAAA,OAEA,SAAS,EAAS,WAAE,CAAS,CAAE,GAAG,EAAyC,EACzE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,WAAA,CACC,YAAU,WACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,scACA,GAED,GAAG,CAAK,EAGf,kECbA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAOA,EAAA,EAAA,CAAA,CAAA,OAQA,EAAA,EAAA,CAAA,CAAA,OAOA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAUA,IAAM,EAAgB,EAAA,CAAC,CAAC,MAAM,CAAC,CAC9B,UAAW,EAAA,CAAC,CAAC,UAAU,CAAC,EAAA,gBAAgB,EACxC,KAAM,EAAA,CAAC,CAAC,UAAU,CAAC,EAAA,eAAe,EAClC,OAAQ,EAAA,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,gCAC5B,SAAU,EAAA,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,EAAG,oCAC5B,kBAAmB,EAAA,CAAC,CAAC,MAAM,GAAG,QAAQ,GACtC,UAAW,EAAA,CAAC,CAAC,MAAM,GAAG,QAAQ,GAC9B,UAAW,EAAA,CAAC,CAAC,MAAM,GAAG,QAAQ,GAC9B,SAAU,EAAA,CAAC,CAAC,MAAM,GAAG,QAAQ,GAC7B,OAAQ,EAAA,CAAC,CAAC,MAAM,GAAG,QAAQ,EAC5B,GAIe,SAAS,IACvB,GAAM,CAAE,KAAM,CAAO,CAAE,CAAG,CAAA,EAAA,EAAA,UAAU,AAAV,IACpB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,IAC9B,EAAM,CAAA,EAAA,EAAA,MAAA,AAAM,IAEZ,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACrC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAgB,MAC5C,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAW,EAAE,EAC7C,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAW,EAAE,EAC7C,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAoB,EAAE,EAGhD,EAAkB,EAAa,GAAG,CAAC,QAGnC,EACL,GAAS,OACP,CAAF,CAAU,IAAI,CAAS,IAAI,GAAK,EAAA,QAAQ,CAAC,iBAAiB,EACxD,EAAQ,IAAI,CAAS,IAAI,GAAK,EAAA,QAAQ,CAAC,UAAU,EACjD,EAAQ,IAAI,CAAS,IAAI,GAAK,EAAA,QAAQ,CAAC,OAAA,AAAO,EAE3C,EAAO,CAAA,EAAA,EAAA,OAAA,AAAO,EAAkB,CACrC,SAAU,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,GACtB,cAAe,CACd,UAAW,EAAA,gBAAgB,CAAC,EAAE,CAC9B,KAA0B,iBAApB,EAAqC,EAAA,eAAe,CAAC,YAAY,CAChD,aAApB,EAAiC,EAAA,eAAe,CAAC,QAAQ,CACrC,aAApB,EAAiC,EAAA,eAAe,CAAC,QAAQ,CACzD,EAAA,eAAe,CAAC,YAAY,CAC/B,OAAQ,EACR,SAAU,GACV,kBAAmB,GACnB,UAAW,GACX,UAAW,GACX,SAAU,GACV,OAAQ,EACT,CACD,GAEM,EAAc,EAAK,KAAK,CAAC,QACzB,EAAmB,EAAK,KAAK,CAAC,aAEpC,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACL,GAAS,aAAa,AACzB,GAEF,EAAG,CAAC,EAAQ,EAGZ,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACL,IAAgB,EAAA,eAAe,CAAC,YAAY,EAAE,AACjD,EAAK,QAAQ,CAAC,YAAa,EAAA,gBAAgB,CAAC,EAAE,CAEhD,EAAG,CAAC,EAAa,EAAK,EAEtB,IAAM,EAAW,UAChB,GAAI,CACH,GAAM,CAAC,EAAa,EAAa,EAAa,CAAG,MAAM,QAAQ,GAAG,CAAC,CAClE,EAAI,UAAU,GACd,EAAI,UAAU,GACd,EAAI,WAAW,GACf,EAED,EAAW,GACX,EAAW,GACX,EAAY,EACb,CAAE,MAAO,EAAO,CACf,QAAQ,KAAK,CAAC,yCAA0C,EACzD,CACD,EAEM,EAAW,MAAO,IACvB,GAAa,GACb,EAAS,MAET,GAAI,CAEH,IAAM,EAAiB,CACtB,UAAW,EAAK,SAAS,CACzB,KAAM,EAAK,IAAI,CACf,OAAQ,EAAK,MAAM,CACnB,SAAU,EAAK,QAAQ,AACxB,EAGI,EAAK,iBAAiB,GAAE,EAAU,iBAAiB,CAAG,EAAK,iBAAA,AAAiB,EAC5E,EAAK,SAAS,GAAE,EAAU,SAAS,CAAG,EAAK,SAAS,AAAT,EAC3C,EAAK,SAAS,GAAE,EAAU,SAAS,CAAG,EAAK,SAAA,AAAS,EACpD,EAAK,QAAQ,GAAE,EAAU,QAAQ,CAAG,EAAK,QAAA,AAAQ,EACjD,EAAK,MAAM,EAAE,GAAU,MAAM,CAAG,EAAK,MAAA,AAAM,EAE/C,MAAM,EAAI,aAAa,CAAC,GACxB,EAAO,IAAI,CAAC,sBACb,CAAE,MAAO,EAAO,CACf,QAAQ,KAAK,CAAC,0CAA2C,GACrD,aAAiB,MACpB,CAD2B,CAClB,EAAM,OAAO,EAEtB,EAAS,8DAEX,QAAU,CACT,GAAa,EACd,CACD,SAEA,AAAK,EAwBJ,CAAA,CAxBG,CAwBH,EAAA,IAAA,EAAC,MAxBsB,AAwBtB,CAAI,UAAU,sBAEd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,+BACV,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,QAAQ,KAAK,eAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,iBAAiB,cAIxC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4CAAmC,qBACjD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,0CAK/B,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,sBACf,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,WACV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,6BACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,6DAIlB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAE,GAAG,CAAI,UACb,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SAAU,EAAK,YAAY,CAAC,GAAW,UAAU,sBAEtD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,OACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,eACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CACN,cAAe,EAAM,QAAQ,CAC7B,MAAO,EAAM,KAAK,WAElB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,CAAC,SAAU,WACxB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,YAAY,yBAG3B,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,aAAa,CAAA,WACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,MAAO,EAAA,eAAe,CAAC,YAAY,UAAE,eAGjD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,MAAO,EAAA,eAAe,CAAC,QAAQ,UAAE,cAG7C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,MAAO,EAAA,eAAe,CAAC,QAAQ,UAAE,kBAK/C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,QAKf,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,YACL,OAAQ,CAAC,CAAE,OAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,gBACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CACN,cAAe,EAAM,QAAQ,CAC7B,MAAO,EAAM,KAAK,CAClB,SAAU,IAAgB,EAAA,eAAe,CAAC,YAAY,WAEtD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,CAAC,SAAU,WACxB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,YAAY,kBAG3B,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,aAAa,CAAA,WACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,MAAO,EAAA,gBAAgB,CAAC,EAAE,UAAE,WAGxC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,MAAO,EAAA,gBAAgB,CAAC,GAAG,UAAE,iBAK3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,WAOhB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,SACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,cACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACL,KAAK,SACL,YAAY,IACX,GAAG,CAAK,CACT,SAAU,AAAC,GAAM,EAAM,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK,GACrD,SAAU,MAGZ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,QAMf,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,WACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,aACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CACN,cAAe,EAAM,QAAQ,CAC7B,MAAO,EAAM,KAAK,WAElB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,CAAC,SAAU,WACxB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,YAAY,gCAG3B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,UACZ,EAAQ,GAAG,CAAC,AAAC,GACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAkB,MAAO,EAAO,GAAG,WAC5C,EAAO,GAAG,CAAC,KAAG,EAAO,IAAI,CAAC,MADX,EAAO,GAAG,QAM9B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,QAMd,IAAgB,EAAA,eAAe,CAAC,QAAQ,EACxC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,oBACL,OAAQ,CAAC,CAAE,OAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,4BACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CACN,cAAe,EAAM,QAAQ,CAC7B,MAAO,EAAM,KAAK,WAElB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,CAAC,SAAU,WACxB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,YAAY,8BAG3B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,UACZ,EAAQ,GAAG,CAAC,AAAC,GACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAkB,MAAO,EAAO,GAAG,WAC5C,EAAO,GAAG,CAAC,KAAG,EAAO,IAAI,CAAC,MADX,EAAO,GAAG,QAM9B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,QAMf,IAAgB,EAAA,eAAe,CAAC,YAAY,EAC5C,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,WACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,aACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CACN,cAAe,EAAM,QAAQ,CAC7B,MAAO,EAAM,KAAK,WAElB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,CAAC,SAAU,WACxB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,YAAY,+BAG3B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,UACZ,EAAQ,GAAG,CAAC,AAAC,GACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAkB,MAAO,EAAO,GAAG,WAC5C,EAAO,SAAS,CAAC,IAAE,EAAO,QAAQ,GADnB,EAAO,GAAG,QAM9B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,QAKf,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,YACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,YACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CACN,cAAe,EAAM,QAAQ,CAC7B,MAAO,EAAM,KAAK,WAElB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,CAAC,SAAU,WACxB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,YAAY,iCAG3B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,aAAa,CAAA,UACZ,EAAS,GAAG,CAAC,AAAC,GACd,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAmB,MAAO,EAAQ,GAAG,WAAE,WACxC,EAAQ,KAAK,GADN,EAAQ,GAAG,QAM/B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,WAOhB,IAAgB,EAAA,eAAe,CAAC,QAAQ,EAAI,IAAqB,EAAA,gBAAgB,CAAC,GAAG,EACrF,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,SACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,YACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CACR,YAAY,4BACX,GAAG,CAAK,CACT,SAAU,EACV,KAAM,MAGR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,QAOf,GACA,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sDACb,IAKH,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,+BACV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,SAAU,WAAW,cAIhD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,KAAK,SAAS,SAAU,WAC9B,EAAY,oBAAsB,4CAlSzC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,+BACV,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,QAAQ,KAAK,eAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,iBAAiB,gBAKzC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACd,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,+CAAsC,iBACpD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,qEA+RnC", "ignoreList": [0, 2]}