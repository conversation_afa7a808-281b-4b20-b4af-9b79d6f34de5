(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,44640,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,s){let r=Reflect.get(e,t,s);return"function"==typeof r?r.bind(e):r}static set(e,t,s,r){return Reflect.set(e,t,s,r)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},8356,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"afterTaskAsyncStorageInstance",{enumerable:!0,get:function(){return r}});let r=(0,e.r(97918).createAsyncLocalStorage)()},62355,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"afterTaskAsyncStorage",{enumerable:!0,get:function(){return r.afterTaskAsyncStorageInstance}});let r=e.r(8356)},17939,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(s,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return o},throwWithStaticGenerationBailoutError:function(){return a},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return i}});let r=e.r(85115),n=e.r(62355);function a(e,t){throw Object.defineProperty(new r.StaticGenBailoutError("Route ".concat(e," couldn't be rendered statically because it used ").concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function i(e,t){throw Object.defineProperty(new r.StaticGenBailoutError("Route ".concat(e,' with `dynamic = "error"` couldn\'t be rendered statically because it used ').concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function o(e,t){let s=Object.defineProperty(Error("Route ".concat(e.route,' used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(s,t),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=s),s}function c(){let e=n.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},93168,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(s,{describeHasCheckingStringProperty:function(){return a},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return i}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function a(e,t){let s=JSON.stringify(t);return"`Reflect.has("+e+", "+s+")`, `"+s+" in "+e+"`, or similar"}let i=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},63470,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let r=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var s=n(t);if(s&&s.has(e))return s.get(e);var r={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var o=a?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(r,i,o):r[i]=e[i]}return r.default=e,s&&s.set(e,r),r}(e.r(38477));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,s=new WeakMap;return(n=function(e){return e?s:t})(e)}let a={current:null},i="function"==typeof r.cache?r.cache:e=>e,o=console.warn;function c(e){return function(){for(var t=arguments.length,s=Array(t),r=0;r<t;r++)s[r]=arguments[r];o(e(...s))}}i(e=>{try{o(a.current)}finally{a.current=null}})},4467,e=>{"use strict";e.s(["useApi",()=>r]);var t=e.i(1269),s=e.i(1831);function r(){let{data:e}=(0,t.useSession)(),r=async function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(null==e?void 0:e.accessToken))throw Error("Non authentifié");return s.apiService.authenticatedRequest(t,e.accessToken,r)};return{login:s.apiService.login.bind(s.apiService),register:s.apiService.register.bind(s.apiService),authenticatedRequest:r,getUsers:()=>r("/users"),getUser:e=>r("/users/".concat(e)),createUser:e=>r("/users",{method:"POST",body:JSON.stringify(e)}),updateUser:(e,t)=>r("/users/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteUser:e=>r("/users/".concat(e),{method:"DELETE"}),getSessions:()=>r("/sessions"),getSession:e=>r("/sessions/".concat(e)),createSession:e=>r("/sessions",{method:"POST",body:JSON.stringify(e)}),updateSession:(e,t)=>r("/sessions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteSession:e=>r("/sessions/".concat(e),{method:"DELETE"}),getCaisses:()=>r("/caisses"),getCaisse:e=>r("/caisses/".concat(e)),createCaisse:e=>r("/caisses",{method:"POST",body:JSON.stringify(e)}),updateCaisse:(e,t)=>r("/caisses/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteCaisse:e=>r("/caisses/".concat(e),{method:"DELETE"}),emargerCaisse:e=>r("/caisses/".concat(e,"/emarger"),{method:"POST"}),getReunions:()=>r("/reunions"),getReunion:e=>r("/reunions/".concat(e)),updateReunion:(e,t)=>r("/reunions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),getMembers:()=>r("/members"),getMember:e=>r("/members/".concat(e)),createMember:e=>r("/members",{method:"POST",body:JSON.stringify(e)}),updateMember:(e,t)=>r("/members/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteMember:e=>r("/members/".concat(e),{method:"DELETE"}),getMemberDebrief:(e,t)=>{let s=new URLSearchParams;(null==t?void 0:t.dateFrom)&&s.append("dateFrom",t.dateFrom),(null==t?void 0:t.dateTo)&&s.append("dateTo",t.dateTo),(null==t?void 0:t.sessionId)&&s.append("sessionId",t.sessionId);let n=s.toString()?"?".concat(s.toString()):"";return r("/members/".concat(e,"/debrief").concat(n))},createPayment:e=>r("/payments",{method:"POST",body:JSON.stringify(e)}),getSessionMembers:e=>r("/sessions/".concat(e,"/members")),addSessionMember:e=>r("/session-members",{method:"POST",body:JSON.stringify(e)}),removeSessionMember:(e,t)=>r("/sessions/".concat(e,"/members/").concat(t),{method:"DELETE"})}}},57223,e=>{"use strict";e.s(["DollarSign",()=>t],57223);let t=(0,e.i(44571).default)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},27973,e=>{"use strict";e.s(["Trash2",()=>t],27973);let t=(0,e.i(44571).default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},65429,e=>{"use strict";e.s(["Label",()=>i],65429);var t=e.i(4051),s=e.i(38477),r=e.i(38909),n=s.forwardRef((e,s)=>(0,t.jsx)(r.Primitive.label,{...e,ref:s,onMouseDown:t=>{var s;t.target.closest("button, input, select, textarea")||(null==(s=e.onMouseDown)||s.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var a=e.i(41428);function i(e){let{className:s,...r}=e;return(0,t.jsx)(n,{"data-slot":"label",className:(0,a.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...r})}},14545,e=>{"use strict";e.s(["ArrowLeft",()=>t],14545);let t=(0,e.i(44571).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9900,e=>{"use strict";e.s(["default",()=>y]);var t=e.i(4051),s=e.i(38477),r=e.i(1269),n=e.i(57691),a=e.i(67967),i=e.i(78381),o=e.i(45086),c=e.i(14545),d=e.i(67435),l=e.i(57223),u=e.i(27973),m=e.i(5085),f=e.i(85205),p=e.i(96134),h=e.i(75680),g=e.i(5647),x=e.i(4467);let b=o.z.object({annee:o.z.number().min(2020,"L'année doit être supérieure à 2020").max(2050,"L'année doit être inférieure à 2050"),dateDebut:o.z.string().min(1,"La date de début est requise"),dateFin:o.z.string().min(1,"La date de fin est requise"),partFixe:o.z.number().min(1,"La part fixe doit être supérieure à 0").max(1e6,"La part fixe ne peut pas dépasser 1,000,000 FCFA")}).refine(e=>{let t=new Date(e.dateDebut);return new Date(e.dateFin)>t},{message:"La date de fin doit être postérieure à la date de début",path:["dateFin"]});function y(){let{id:e}=(0,n.useParams)(),{data:o,status:y}=(0,r.useSession)(),j=(0,n.useRouter)(),v=(0,x.useApi)(),[S,N]=(0,s.useState)(null),[O,F]=(0,s.useState)(!1),[P,C]=(0,s.useState)(!1),[w,E]=(0,s.useState)(null),[M,T]=(0,s.useState)(!0),D=(null==o?void 0:o.user)&&"secretary_general"===o.user.role,_=(0,a.useForm)({resolver:(0,i.zodResolver)(b),defaultValues:{annee:new Date().getFullYear(),dateDebut:"",dateFin:"",partFixe:0}});(0,s.useEffect)(()=>{let t=async()=>{if(e&&"string"==typeof e)try{T(!0);let t=await v.getSession(e);N(t),_.reset({annee:t.annee,dateDebut:t.dateDebut.split("T")[0],dateFin:t.dateFin.split("T")[0],partFixe:t.partFixe})}catch(e){console.error("Erreur lors du chargement:",e),E("Session introuvable")}finally{T(!1)}};(null==o?void 0:o.accessToken)&&t()},[e,y]);let A=async t=>{if(!D||!e||"string"!=typeof e)return void E("Vous n'avez pas les permissions pour modifier cette session");try{F(!0),E(null),await v.updateSession(e,t),j.push("/dashboard/sessions")}catch(e){console.error("Erreur lors de la modification:",e),E(e.message||"Une erreur est survenue lors de la modification")}finally{F(!1)}},R=async()=>{if(D&&e&&"string"==typeof e&&S&&confirm("Êtes-vous sûr de vouloir supprimer la session ".concat(S.annee," ? Cette action supprimera également toutes les réunions associées.")))try{C(!0),await v.deleteSession(e),j.push("/dashboard/sessions")}catch(e){console.error("Erreur lors de la suppression:",e),E(e.message||"Une erreur est survenue lors de la suppression")}finally{C(!1)}};return M?(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Chargement de la session..."})]})}):D?S?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(f.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,t.jsx)(m.default,{href:"/dashboard/sessions",children:(0,t.jsx)(c.ArrowLeft,{className:"h-4 w-4"})})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold tracking-tight",children:["Modifier Session ",S.annee]}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Modifier les paramètres de la session"})]})]}),(0,t.jsxs)(f.Button,{variant:"destructive",onClick:R,disabled:P,children:[(0,t.jsx)(u.Trash2,{className:"mr-2 h-4 w-4"}),P?"Suppression...":"Supprimer"]})]}),(0,t.jsxs)(h.Card,{children:[(0,t.jsxs)(h.CardHeader,{children:[(0,t.jsxs)(h.CardTitle,{className:"flex items-center gap-2",children:[(0,t.jsx)(d.Calendar,{className:"h-5 w-5"}),"Informations de la session"]}),(0,t.jsx)(h.CardDescription,{children:"Modifiez les paramètres de la session. Attention : les modifications peuvent affecter les réunions existantes."})]}),(0,t.jsx)(h.CardContent,{children:(0,t.jsx)(g.Form,{..._,children:(0,t.jsxs)("form",{onSubmit:_.handleSubmit(A),className:"space-y-6",children:[w&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,t.jsx)("p",{className:"text-sm text-red-600",children:w})}),(0,t.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,t.jsx)(g.FormField,{control:_.control,name:"annee",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.FormItem,{children:[(0,t.jsx)(g.FormLabel,{children:"Année"}),(0,t.jsx)(g.FormControl,{children:(0,t.jsx)(p.Input,{type:"number",...s,onChange:e=>s.onChange(parseInt(e.target.value)||0)})}),(0,t.jsx)(g.FormDescription,{children:"L'année de la session de tontine"}),(0,t.jsx)(g.FormMessage,{})]})}}),(0,t.jsx)(g.FormField,{control:_.control,name:"partFixe",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.FormItem,{children:[(0,t.jsxs)(g.FormLabel,{className:"flex items-center gap-2",children:[(0,t.jsx)(l.DollarSign,{className:"h-4 w-4"}),"Part Fixe (FCFA)"]}),(0,t.jsx)(g.FormControl,{children:(0,t.jsx)(p.Input,{type:"number",...s,onChange:e=>s.onChange(parseInt(e.target.value)||0)})}),(0,t.jsx)(g.FormDescription,{children:"Montant de la cotisation fixe par réunion"}),(0,t.jsx)(g.FormMessage,{})]})}})]}),(0,t.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,t.jsx)(g.FormField,{control:_.control,name:"dateDebut",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.FormItem,{children:[(0,t.jsx)(g.FormLabel,{children:"Date de début"}),(0,t.jsx)(g.FormControl,{children:(0,t.jsx)(p.Input,{type:"date",...s})}),(0,t.jsx)(g.FormDescription,{children:"Date de début de la session"}),(0,t.jsx)(g.FormMessage,{})]})}}),(0,t.jsx)(g.FormField,{control:_.control,name:"dateFin",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.FormItem,{children:[(0,t.jsx)(g.FormLabel,{children:"Date de fin"}),(0,t.jsx)(g.FormControl,{children:(0,t.jsx)(p.Input,{type:"date",...s})}),(0,t.jsx)(g.FormDescription,{children:"Date de fin de la session"}),(0,t.jsx)(g.FormMessage,{})]})}})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,t.jsx)(f.Button,{variant:"outline",asChild:!0,children:(0,t.jsx)(m.default,{href:"/dashboard/sessions",children:"Annuler"})}),(0,t.jsx)(f.Button,{type:"submit",disabled:O,children:O?"Modification...":"Modifier la session"})]})]})})})]})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(f.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,t.jsx)(m.default,{href:"/dashboard/sessions",children:(0,t.jsx)(c.ArrowLeft,{className:"h-4 w-4"})})}),(0,t.jsx)("div",{children:(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Session introuvable"})})]}),(0,t.jsx)(h.Card,{children:(0,t.jsx)(h.CardContent,{className:"pt-6",children:(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"La session demandée n'a pas été trouvée."})})})})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(f.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,t.jsx)(m.default,{href:"/dashboard/sessions",children:(0,t.jsx)(c.ArrowLeft,{className:"h-4 w-4"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Modifier Session"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Modifier les paramètres de la session"})]})]}),(0,t.jsx)(h.Card,{children:(0,t.jsx)(h.CardContent,{className:"pt-6",children:(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Vous n'avez pas les permissions pour modifier cette session."}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Seuls les administrateurs peuvent modifier les sessions."})]})})})]})}}]);