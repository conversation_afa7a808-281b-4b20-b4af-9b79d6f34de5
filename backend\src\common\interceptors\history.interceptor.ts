import { CallHandler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Observable, tap } from 'rxjs';
import { History, HistoryAction, HistoryDocument } from '../../history/schemas/history.schema';

// Interceptor global pour enregistrer les actions après exécution
@Injectable()
export class HistoryInterceptor implements NestInterceptor {
  constructor(@InjectModel(History.name) private historyModel: Model<HistoryDocument>) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const req = context.switchToHttp().getRequest();
    const userId = req?.user?._id ? new Types.ObjectId(req.user._id) : undefined;
    const path = req?.route?.path || '';
    const method = req?.method || '';

    return next.handle().pipe(
      tap(async (result) => {
        try {
          if (!userId) return; // Ne logue que si user connecté
          const actionMap: Record<string, HistoryAction> = {
            POST: HistoryAction.CREATE,
            PATCH: HistoryAction.UPDATE,
            DELETE: HistoryAction.DELETE,
          };
          const action = actionMap[method] ?? HistoryAction.UPDATE;

          // IDs courants détectés dans result
          const detectId = (obj: any, key: string): string | undefined => {
            const v = obj?.[key];
            if (!v) return undefined;
            if (typeof v === 'string') return v;
            if (typeof v === 'object' && v._id) return v._id.toString();
            return undefined;
          };

          // Prépare les refs si détectables
          const refs: any = {};
          const maybeAdd = (k: string, v?: string) => {
            if (v) refs[k] = new Types.ObjectId(v);
          };
          maybeAdd('paymentId', detectId(result, '_id'));
          maybeAdd('caisseId', detectId(result, 'caisseId'));
          maybeAdd('sessionId', detectId(result, 'sessionId'));
          maybeAdd('reunionId', detectId(result, 'reunionId'));
          maybeAdd('memberId', detectId(result, 'memberId'));

          const contextPayload = {
            path,
            method,
            bodyKeys: Object.keys(req.body || {}),
            params: req.params || {},
            query: req.query || {},
          };

          await this.historyModel.create({ userId, action, ...refs, context: contextPayload });
        } catch (e) {
          // ne bloque pas la requête en cas d'erreur de log
        }
      }),
    );
  }
}