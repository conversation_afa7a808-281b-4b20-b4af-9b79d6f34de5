(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,3840,4818,35369,e=>{"use strict";e.s(["MoreHorizontal",()=>t],3840);var r=e.i(44571);let t=(0,r.default)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);e.s(["Edit",()=>n],4818);let n=(0,r.default)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);e.s(["DropdownMenu",()=>e9,"DropdownMenuContent",()=>re,"DropdownMenuItem",()=>rr,"DropdownMenuLabel",()=>rt,"DropdownMenuSeparator",()=>rn,"DropdownMenuTrigger",()=>e7],35369);var o=e.i(4051),a=e.i(38477),l=e.i(94798),i=e.i(44636),s=e.i(1767),u=e.i(26183),d=e.i(38909),c=e.i(74332),p=e.i(28189),f=e.i(76355),v=e.i(15589),m=e.i(7535),h=e.i(33348),g=e.i(50724),w=e.i(37439),x=e.i(90253),y=e.i(48365),b="rovingFocusGroup.onEntryFocus",C={bubbles:!1,cancelable:!0},R="RovingFocusGroup",[M,D,j]=(0,c.createCollection)(R),[E,k]=(0,s.createContextScope)(R,[j]),[P,_]=E(R),I=a.forwardRef((e,r)=>(0,o.jsx)(M.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,o.jsx)(M.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,o.jsx)(S,{...e,ref:r})})}));I.displayName=R;var S=a.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:n,loop:s=!1,dir:c,currentTabStopId:f,defaultCurrentTabStopId:v,onCurrentTabStopIdChange:m,onEntryFocus:h,preventScrollOnEntryFocus:g=!1,...w}=e,x=a.useRef(null),M=(0,i.useComposedRefs)(r,x),j=(0,p.useDirection)(c),[E,k]=(0,u.useControllableState)({prop:f,defaultProp:null!=v?v:null,onChange:m,caller:R}),[_,I]=a.useState(!1),S=(0,y.useCallbackRef)(h),T=D(t),F=a.useRef(!1),[N,H]=a.useState(0);return a.useEffect(()=>{let e=x.current;if(e)return e.addEventListener(b,S),()=>e.removeEventListener(b,S)},[S]),(0,o.jsx)(P,{scope:t,orientation:n,dir:j,loop:s,currentTabStopId:E,onItemFocus:a.useCallback(e=>k(e),[k]),onItemShiftTab:a.useCallback(()=>I(!0),[]),onFocusableItemAdd:a.useCallback(()=>H(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>H(e=>e-1),[]),children:(0,o.jsx)(d.Primitive.div,{tabIndex:_||0===N?-1:0,"data-orientation":n,...w,ref:M,style:{outline:"none",...e.style},onMouseDown:(0,l.composeEventHandlers)(e.onMouseDown,()=>{F.current=!0}),onFocus:(0,l.composeEventHandlers)(e.onFocus,e=>{let r=!F.current;if(e.target===e.currentTarget&&r&&!_){let r=new CustomEvent(b,C);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=T().filter(e=>e.focusable);A([e.find(e=>e.active),e.find(e=>e.id===E),...e].filter(Boolean).map(e=>e.ref.current),g)}}F.current=!1}),onBlur:(0,l.composeEventHandlers)(e.onBlur,()=>I(!1))})})}),T="RovingFocusGroupItem",F=a.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:n=!0,active:i=!1,tabStopId:s,children:u,...c}=e,p=(0,h.useId)(),f=s||p,v=_(T,t),m=v.currentTabStopId===f,g=D(t),{onFocusableItemAdd:w,onFocusableItemRemove:x,currentTabStopId:y}=v;return a.useEffect(()=>{if(n)return w(),()=>x()},[n,w,x]),(0,o.jsx)(M.ItemSlot,{scope:t,id:f,focusable:n,active:i,children:(0,o.jsx)(d.Primitive.span,{tabIndex:m?0:-1,"data-orientation":v.orientation,...c,ref:r,onMouseDown:(0,l.composeEventHandlers)(e.onMouseDown,e=>{n?v.onItemFocus(f):e.preventDefault()}),onFocus:(0,l.composeEventHandlers)(e.onFocus,()=>v.onItemFocus(f)),onKeyDown:(0,l.composeEventHandlers)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,t){var n;let o=(n=e.key,"rtl"!==t?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return N[o]}(e,v.orientation,v.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let n=t.indexOf(e.currentTarget);t=v.loop?function(e,r){return e.map((t,n)=>e[(r+n)%e.length])}(t,n+1):t.slice(n+1)}setTimeout(()=>A(t))}}),children:"function"==typeof u?u({isCurrentTabStop:m,hasTabStop:null!=y}):u})})});F.displayName=T;var N={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function A(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=document.activeElement;for(let n of e)if(n===t||(n.focus({preventScroll:r}),document.activeElement!==t))return}var H=e.i(81221),O=e.i(23952),L=e.i(54574),K=["Enter"," "],G=["ArrowUp","PageDown","End"],U=["ArrowDown","PageUp","Home",...G],z={ltr:[...K,"ArrowRight"],rtl:[...K,"ArrowLeft"]},B={ltr:["ArrowLeft"],rtl:["ArrowRight"]},V="Menu",[X,q,Y]=(0,c.createCollection)(V),[W,Z]=(0,s.createContextScope)(V,[Y,g.createPopperScope,k]),J=(0,g.createPopperScope)(),Q=k(),[$,ee]=W(V),[er,et]=W(V),en=e=>{let{__scopeMenu:r,open:t=!1,children:n,dir:l,onOpenChange:i,modal:s=!0}=e,u=J(r),[d,c]=a.useState(null),f=a.useRef(!1),v=(0,y.useCallbackRef)(i),m=(0,p.useDirection)(l);return a.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,o.jsx)(g.Root,{...u,children:(0,o.jsx)($,{scope:r,open:t,onOpenChange:v,content:d,onContentChange:c,children:(0,o.jsx)(er,{scope:r,onClose:a.useCallback(()=>v(!1),[v]),isUsingKeyboardRef:f,dir:m,modal:s,children:n})})})};en.displayName=V;var eo=a.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e,a=J(t);return(0,o.jsx)(g.Anchor,{...a,...n,ref:r})});eo.displayName="MenuAnchor";var ea="MenuPortal",[el,ei]=W(ea,{forceMount:void 0}),es=e=>{let{__scopeMenu:r,forceMount:t,children:n,container:a}=e,l=ee(ea,r);return(0,o.jsx)(el,{scope:r,forceMount:t,children:(0,o.jsx)(x.Presence,{present:t||l.open,children:(0,o.jsx)(w.Portal,{asChild:!0,container:a,children:n})})})};es.displayName=ea;var eu="MenuContent",[ed,ec]=W(eu),ep=a.forwardRef((e,r)=>{let t=ei(eu,e.__scopeMenu),{forceMount:n=t.forceMount,...a}=e,l=ee(eu,e.__scopeMenu),i=et(eu,e.__scopeMenu);return(0,o.jsx)(X.Provider,{scope:e.__scopeMenu,children:(0,o.jsx)(x.Presence,{present:n||l.open,children:(0,o.jsx)(X.Slot,{scope:e.__scopeMenu,children:i.modal?(0,o.jsx)(ef,{...a,ref:r}):(0,o.jsx)(ev,{...a,ref:r})})})})}),ef=a.forwardRef((e,r)=>{let t=ee(eu,e.__scopeMenu),n=a.useRef(null),s=(0,i.useComposedRefs)(r,n);return a.useEffect(()=>{let e=n.current;if(e)return(0,O.hideOthers)(e)},[]),(0,o.jsx)(eh,{...e,ref:s,trapFocus:t.open,disableOutsidePointerEvents:t.open,disableOutsideScroll:!0,onFocusOutside:(0,l.composeEventHandlers)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>t.onOpenChange(!1)})}),ev=a.forwardRef((e,r)=>{let t=ee(eu,e.__scopeMenu);return(0,o.jsx)(eh,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>t.onOpenChange(!1)})}),em=(0,H.createSlot)("MenuContent.ScrollLock"),eh=a.forwardRef((e,r)=>{let{__scopeMenu:t,loop:n=!1,trapFocus:s,onOpenAutoFocus:u,onCloseAutoFocus:d,disableOutsidePointerEvents:c,onEntryFocus:p,onEscapeKeyDown:h,onPointerDownOutside:w,onFocusOutside:x,onInteractOutside:y,onDismiss:b,disableOutsideScroll:C,...R}=e,M=ee(eu,t),D=et(eu,t),j=J(t),E=Q(t),k=q(t),[P,_]=a.useState(null),S=a.useRef(null),T=(0,i.useComposedRefs)(r,S,M.onContentChange),F=a.useRef(0),N=a.useRef(""),A=a.useRef(0),H=a.useRef(null),O=a.useRef("right"),K=a.useRef(0),z=C?L.RemoveScroll:a.Fragment;a.useEffect(()=>()=>window.clearTimeout(F.current),[]),(0,v.useFocusGuards)();let B=a.useCallback(e=>{var r,t;return O.current===(null==(r=H.current)?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:t,y:n}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let l=r[e],i=r[a],s=l.x,u=l.y,d=i.x,c=i.y;u>n!=c>n&&t<(d-s)*(n-u)/(c-u)+s&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null==(t=H.current)?void 0:t.area)},[]);return(0,o.jsx)(ed,{scope:t,searchRef:N,onItemEnter:a.useCallback(e=>{B(e)&&e.preventDefault()},[B]),onItemLeave:a.useCallback(e=>{var r;B(e)||(null==(r=S.current)||r.focus(),_(null))},[B]),onTriggerLeave:a.useCallback(e=>{B(e)&&e.preventDefault()},[B]),pointerGraceTimerRef:A,onPointerGraceIntentChange:a.useCallback(e=>{H.current=e},[]),children:(0,o.jsx)(z,{...C?{as:em,allowPinchZoom:!0}:void 0,children:(0,o.jsx)(m.FocusScope,{asChild:!0,trapped:s,onMountAutoFocus:(0,l.composeEventHandlers)(u,e=>{var r;e.preventDefault(),null==(r=S.current)||r.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:(0,o.jsx)(f.DismissableLayer,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:h,onPointerDownOutside:w,onFocusOutside:x,onInteractOutside:y,onDismiss:b,children:(0,o.jsx)(I,{asChild:!0,...E,dir:D.dir,orientation:"vertical",loop:n,currentTabStopId:P,onCurrentTabStopIdChange:_,onEntryFocus:(0,l.composeEventHandlers)(p,e=>{D.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,o.jsx)(g.Content,{role:"menu","aria-orientation":"vertical","data-state":eU(M.open),"data-radix-menu-content":"",dir:D.dir,...j,...R,ref:T,style:{outline:"none",...R.style},onKeyDown:(0,l.composeEventHandlers)(R.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,t=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!t&&n&&(e=>{var r,t;let n=N.current+e,o=k().filter(e=>!e.disabled),a=document.activeElement,l=null==(r=o.find(e=>e.ref.current===a))?void 0:r.textValue,i=function(e,r,t){var n;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=t?e.indexOf(t):-1,l=(n=Math.max(a,0),e.map((r,t)=>e[(n+t)%e.length]));1===o.length&&(l=l.filter(e=>e!==t));let i=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==t?i:void 0}(o.map(e=>e.textValue),n,l),s=null==(t=o.find(e=>e.textValue===i))?void 0:t.ref.current;!function e(r){N.current=r,window.clearTimeout(F.current),""!==r&&(F.current=window.setTimeout(()=>e(""),1e3))}(n),s&&setTimeout(()=>s.focus())})(e.key));let o=S.current;if(e.target!==o||!U.includes(e.key))return;e.preventDefault();let a=k().filter(e=>!e.disabled).map(e=>e.ref.current);G.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let t of e)if(t===r||(t.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,l.composeEventHandlers)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(F.current),N.current="")}),onPointerMove:(0,l.composeEventHandlers)(e.onPointerMove,eV(e=>{let r=e.target,t=K.current!==e.clientX;e.currentTarget.contains(r)&&t&&(O.current=e.clientX>K.current?"right":"left",K.current=e.clientX)}))})})})})})})});ep.displayName=eu;var eg=a.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,o.jsx)(d.Primitive.div,{role:"group",...n,ref:r})});eg.displayName="MenuGroup";var ew=a.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,o.jsx)(d.Primitive.div,{...n,ref:r})});ew.displayName="MenuLabel";var ex="MenuItem",ey="menu.itemSelect",eb=a.forwardRef((e,r)=>{let{disabled:t=!1,onSelect:n,...s}=e,u=a.useRef(null),c=et(ex,e.__scopeMenu),p=ec(ex,e.__scopeMenu),f=(0,i.useComposedRefs)(r,u),v=a.useRef(!1);return(0,o.jsx)(eC,{...s,ref:f,disabled:t,onClick:(0,l.composeEventHandlers)(e.onClick,()=>{let e=u.current;if(!t&&e){let r=new CustomEvent(ey,{bubbles:!0,cancelable:!0});e.addEventListener(ey,e=>null==n?void 0:n(e),{once:!0}),(0,d.dispatchDiscreteCustomEvent)(e,r),r.defaultPrevented?v.current=!1:c.onClose()}}),onPointerDown:r=>{var t;null==(t=e.onPointerDown)||t.call(e,r),v.current=!0},onPointerUp:(0,l.composeEventHandlers)(e.onPointerUp,e=>{var r;v.current||null==(r=e.currentTarget)||r.click()}),onKeyDown:(0,l.composeEventHandlers)(e.onKeyDown,e=>{let r=""!==p.searchRef.current;t||r&&" "===e.key||K.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eb.displayName=ex;var eC=a.forwardRef((e,r)=>{let{__scopeMenu:t,disabled:n=!1,textValue:s,...u}=e,c=ec(ex,t),p=Q(t),f=a.useRef(null),v=(0,i.useComposedRefs)(r,f),[m,h]=a.useState(!1),[g,w]=a.useState("");return a.useEffect(()=>{let e=f.current;if(e){var r;w((null!=(r=e.textContent)?r:"").trim())}},[u.children]),(0,o.jsx)(X.ItemSlot,{scope:t,disabled:n,textValue:null!=s?s:g,children:(0,o.jsx)(F,{asChild:!0,...p,focusable:!n,children:(0,o.jsx)(d.Primitive.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":n||void 0,"data-disabled":n?"":void 0,...u,ref:v,onPointerMove:(0,l.composeEventHandlers)(e.onPointerMove,eV(e=>{n?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,l.composeEventHandlers)(e.onPointerLeave,eV(e=>c.onItemLeave(e))),onFocus:(0,l.composeEventHandlers)(e.onFocus,()=>h(!0)),onBlur:(0,l.composeEventHandlers)(e.onBlur,()=>h(!1))})})})}),eR=a.forwardRef((e,r)=>{let{checked:t=!1,onCheckedChange:n,...a}=e;return(0,o.jsx)(eI,{scope:e.__scopeMenu,checked:t,children:(0,o.jsx)(eb,{role:"menuitemcheckbox","aria-checked":ez(t)?"mixed":t,...a,ref:r,"data-state":eB(t),onSelect:(0,l.composeEventHandlers)(a.onSelect,()=>null==n?void 0:n(!!ez(t)||!t),{checkForDefaultPrevented:!1})})})});eR.displayName="MenuCheckboxItem";var eM="MenuRadioGroup",[eD,ej]=W(eM,{value:void 0,onValueChange:()=>{}}),eE=a.forwardRef((e,r)=>{let{value:t,onValueChange:n,...a}=e,l=(0,y.useCallbackRef)(n);return(0,o.jsx)(eD,{scope:e.__scopeMenu,value:t,onValueChange:l,children:(0,o.jsx)(eg,{...a,ref:r})})});eE.displayName=eM;var ek="MenuRadioItem",eP=a.forwardRef((e,r)=>{let{value:t,...n}=e,a=ej(ek,e.__scopeMenu),i=t===a.value;return(0,o.jsx)(eI,{scope:e.__scopeMenu,checked:i,children:(0,o.jsx)(eb,{role:"menuitemradio","aria-checked":i,...n,ref:r,"data-state":eB(i),onSelect:(0,l.composeEventHandlers)(n.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,t)},{checkForDefaultPrevented:!1})})})});eP.displayName=ek;var e_="MenuItemIndicator",[eI,eS]=W(e_,{checked:!1}),eT=a.forwardRef((e,r)=>{let{__scopeMenu:t,forceMount:n,...a}=e,l=eS(e_,t);return(0,o.jsx)(x.Presence,{present:n||ez(l.checked)||!0===l.checked,children:(0,o.jsx)(d.Primitive.span,{...a,ref:r,"data-state":eB(l.checked)})})});eT.displayName=e_;var eF=a.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e;return(0,o.jsx)(d.Primitive.div,{role:"separator","aria-orientation":"horizontal",...n,ref:r})});eF.displayName="MenuSeparator";var eN=a.forwardRef((e,r)=>{let{__scopeMenu:t,...n}=e,a=J(t);return(0,o.jsx)(g.Arrow,{...a,...n,ref:r})});eN.displayName="MenuArrow";var[eA,eH]=W("MenuSub"),eO="MenuSubTrigger",eL=a.forwardRef((e,r)=>{let t=ee(eO,e.__scopeMenu),n=et(eO,e.__scopeMenu),s=eH(eO,e.__scopeMenu),u=ec(eO,e.__scopeMenu),d=a.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:p}=u,f={__scopeMenu:e.__scopeMenu},v=a.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return a.useEffect(()=>v,[v]),a.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),p(null)}},[c,p]),(0,o.jsx)(eo,{asChild:!0,...f,children:(0,o.jsx)(eC,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":t.open,"aria-controls":s.contentId,"data-state":eU(t.open),...e,ref:(0,i.composeRefs)(r,s.onTriggerChange),onClick:r=>{var n;null==(n=e.onClick)||n.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),t.open||t.onOpenChange(!0))},onPointerMove:(0,l.composeEventHandlers)(e.onPointerMove,eV(r=>{u.onItemEnter(r),!r.defaultPrevented&&(e.disabled||t.open||d.current||(u.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{t.onOpenChange(!0),v()},100)))})),onPointerLeave:(0,l.composeEventHandlers)(e.onPointerLeave,eV(e=>{var r,n;v();let o=null==(r=t.content)?void 0:r.getBoundingClientRect();if(o){let r=null==(n=t.content)?void 0:n.dataset.side,a="right"===r,l=o[a?"left":"right"],i=o[a?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:i,y:o.top},{x:i,y:o.bottom},{x:l,y:o.bottom}],side:r}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,l.composeEventHandlers)(e.onKeyDown,r=>{let o=""!==u.searchRef.current;if(!e.disabled&&(!o||" "!==r.key)&&z[n.dir].includes(r.key)){var a;t.onOpenChange(!0),null==(a=t.content)||a.focus(),r.preventDefault()}})})})});eL.displayName=eO;var eK="MenuSubContent",eG=a.forwardRef((e,r)=>{let t=ei(eu,e.__scopeMenu),{forceMount:n=t.forceMount,...s}=e,u=ee(eu,e.__scopeMenu),d=et(eu,e.__scopeMenu),c=eH(eK,e.__scopeMenu),p=a.useRef(null),f=(0,i.useComposedRefs)(r,p);return(0,o.jsx)(X.Provider,{scope:e.__scopeMenu,children:(0,o.jsx)(x.Presence,{present:n||u.open,children:(0,o.jsx)(X.Slot,{scope:e.__scopeMenu,children:(0,o.jsx)(eh,{id:c.contentId,"aria-labelledby":c.triggerId,...s,ref:f,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;d.isUsingKeyboardRef.current&&(null==(r=p.current)||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,l.composeEventHandlers)(e.onFocusOutside,e=>{e.target!==c.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,l.composeEventHandlers)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,l.composeEventHandlers)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),t=B[d.dir].includes(e.key);if(r&&t){var n;u.onOpenChange(!1),null==(n=c.trigger)||n.focus(),e.preventDefault()}})})})})})});function eU(e){return e?"open":"closed"}function ez(e){return"indeterminate"===e}function eB(e){return ez(e)?"indeterminate":e?"checked":"unchecked"}function eV(e){return r=>"mouse"===r.pointerType?e(r):void 0}eG.displayName=eK;var eX="DropdownMenu",[eq,eY]=(0,s.createContextScope)(eX,[Z]),eW=Z(),[eZ,eJ]=eq(eX),eQ=e=>{let{__scopeDropdownMenu:r,children:t,dir:n,open:l,defaultOpen:i,onOpenChange:s,modal:d=!0}=e,c=eW(r),p=a.useRef(null),[f,v]=(0,u.useControllableState)({prop:l,defaultProp:null!=i&&i,onChange:s,caller:eX});return(0,o.jsx)(eZ,{scope:r,triggerId:(0,h.useId)(),triggerRef:p,contentId:(0,h.useId)(),open:f,onOpenChange:v,onOpenToggle:a.useCallback(()=>v(e=>!e),[v]),modal:d,children:(0,o.jsx)(en,{...c,open:f,onOpenChange:v,dir:n,modal:d,children:t})})};eQ.displayName=eX;var e$="DropdownMenuTrigger",e0=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,disabled:n=!1,...a}=e,s=eJ(e$,t),u=eW(t);return(0,o.jsx)(eo,{asChild:!0,...u,children:(0,o.jsx)(d.Primitive.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...a,ref:(0,i.composeRefs)(r,s.triggerRef),onPointerDown:(0,l.composeEventHandlers)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(s.onOpenToggle(),s.open||e.preventDefault())}),onKeyDown:(0,l.composeEventHandlers)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&s.onOpenToggle(),"ArrowDown"===e.key&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e0.displayName=e$;var e1=e=>{let{__scopeDropdownMenu:r,...t}=e,n=eW(r);return(0,o.jsx)(es,{...n,...t})};e1.displayName="DropdownMenuPortal";var e2="DropdownMenuContent",e5=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,i=eJ(e2,t),s=eW(t),u=a.useRef(!1);return(0,o.jsx)(ep,{id:i.contentId,"aria-labelledby":i.triggerId,...s,...n,ref:r,onCloseAutoFocus:(0,l.composeEventHandlers)(e.onCloseAutoFocus,e=>{var r;u.current||null==(r=i.triggerRef.current)||r.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,l.composeEventHandlers)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey,n=2===r.button||t;(!i.modal||n)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e5.displayName=e2,a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,a=eW(t);return(0,o.jsx)(eg,{...a,...n,ref:r})}).displayName="DropdownMenuGroup";var e3=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,a=eW(t);return(0,o.jsx)(ew,{...a,...n,ref:r})});e3.displayName="DropdownMenuLabel";var e8=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,a=eW(t);return(0,o.jsx)(eb,{...a,...n,ref:r})});e8.displayName="DropdownMenuItem",a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,a=eW(t);return(0,o.jsx)(eR,{...a,...n,ref:r})}).displayName="DropdownMenuCheckboxItem",a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,a=eW(t);return(0,o.jsx)(eE,{...a,...n,ref:r})}).displayName="DropdownMenuRadioGroup",a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,a=eW(t);return(0,o.jsx)(eP,{...a,...n,ref:r})}).displayName="DropdownMenuRadioItem",a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,a=eW(t);return(0,o.jsx)(eT,{...a,...n,ref:r})}).displayName="DropdownMenuItemIndicator";var e4=a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,a=eW(t);return(0,o.jsx)(eF,{...a,...n,ref:r})});e4.displayName="DropdownMenuSeparator",a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,a=eW(t);return(0,o.jsx)(eN,{...a,...n,ref:r})}).displayName="DropdownMenuArrow",a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,a=eW(t);return(0,o.jsx)(eL,{...a,...n,ref:r})}).displayName="DropdownMenuSubTrigger",a.forwardRef((e,r)=>{let{__scopeDropdownMenu:t,...n}=e,a=eW(t);return(0,o.jsx)(eG,{...a,...n,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent",e.i(89683),(0,r.default)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),(0,r.default)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var e6=e.i(41428);function e9(e){let{...r}=e;return(0,o.jsx)(eQ,{"data-slot":"dropdown-menu",...r})}function e7(e){let{...r}=e;return(0,o.jsx)(e0,{"data-slot":"dropdown-menu-trigger",...r})}function re(e){let{className:r,sideOffset:t=4,...n}=e;return(0,o.jsx)(e1,{children:(0,o.jsx)(e5,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,e6.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",r),...n})})}function rr(e){let{className:r,inset:t,variant:n="default",...a}=e;return(0,o.jsx)(e8,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:(0,e6.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",r),...a})}function rt(e){let{className:r,inset:t,...n}=e;return(0,o.jsx)(e3,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,e6.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",r),...n})}function rn(e){let{className:r,...t}=e;return(0,o.jsx)(e4,{"data-slot":"dropdown-menu-separator",className:(0,e6.cn)("bg-border -mx-1 my-1 h-px",r),...t})}}]);