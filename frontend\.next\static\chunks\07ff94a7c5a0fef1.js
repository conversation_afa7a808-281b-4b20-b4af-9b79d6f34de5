(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,17961,e=>{"use strict";e.s(["Sidebar",()=>ef,"SidebarContent",()=>ej,"SidebarFooter",()=>ey,"SidebarGroup",()=>eN,"SidebarGroupAction",()=>eC,"SidebarGroupContent",()=>ek,"SidebarGroupLabel",()=>eS,"SidebarHeader",()=>ev,"SidebarInput",()=>eg,"SidebarInset",()=>ex,"SidebarMenu",()=>eE,"SidebarMenuAction",()=>eL,"SidebarMenuBadge",()=>eM,"SidebarMenuButton",()=>ez,"SidebarMenuItem",()=>eR,"SidebarMenuSkeleton",()=>eA,"SidebarMenuSub",()=>e_,"SidebarMenuSubButton",()=>eO,"SidebarMenuSubItem",()=>eP,"SidebarProvider",()=>eb,"SidebarRail",()=>em,"SidebarSeparator",()=>ew,"SidebarTrigger",()=>eh,"useSidebar",()=>ep],17961);var t=e.i(4051),a=e.i(38477),r=e.i(81221),n=e.i(62244);let i=(0,e.i(44571).default)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]);var s=e.i(41428),o=e.i(85205),l=e.i(96134),d=e.i(38909),c="horizontal",u=["horizontal","vertical"],p=a.forwardRef((e,a)=>{var r;let{decorative:n,orientation:i=c,...s}=e,o=(r=i,u.includes(r))?i:c;return(0,t.jsx)(d.Primitive.div,{"data-orientation":o,...n?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...s,ref:a})});function b(e){let{className:a,orientation:r="horizontal",decorative:n=!0,...i}=e;return(0,t.jsx)(p,{"data-slot":"separator-root",decorative:n,orientation:r,className:(0,s.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...i})}p.displayName="Separator";var f=e.i(28149),h=e.i(19203),h=h;function m(e){let{...a}=e;return(0,t.jsx)(f.Root,{"data-slot":"sheet",...a})}function x(e){let{...a}=e;return(0,t.jsx)(f.Portal,{"data-slot":"sheet-portal",...a})}function g(e){let{className:a,...r}=e;return(0,t.jsx)(f.Overlay,{"data-slot":"sheet-overlay",className:(0,s.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...r})}function v(e){let{className:a,children:r,side:n="right",...i}=e;return(0,t.jsxs)(x,{children:[(0,t.jsx)(g,{}),(0,t.jsxs)(f.Content,{"data-slot":"sheet-content",className:(0,s.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===n&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===n&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===n&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===n&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",a),...i,children:[r,(0,t.jsxs)(f.Close,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,t.jsx)(h.default,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function y(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"sheet-header",className:(0,s.cn)("flex flex-col gap-1.5 p-4",a),...r})}function w(e){let{className:a,...r}=e;return(0,t.jsx)(f.Title,{"data-slot":"sheet-title",className:(0,s.cn)("text-foreground font-semibold",a),...r})}function j(e){let{className:a,...r}=e;return(0,t.jsx)(f.Description,{"data-slot":"sheet-description",className:(0,s.cn)("text-muted-foreground text-sm",a),...r})}function N(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"skeleton",className:(0,s.cn)("bg-accent animate-pulse rounded-md",a),...r})}var S=e.i(94798),C=e.i(44636),k=e.i(1767),E=e.i(76355),R=e.i(33348),T=e.i(50724),z=e.i(37439),L=e.i(90253),M=e.i(26183),A=e.i(65023),[_,P]=(0,k.createContextScope)("Tooltip",[T.createPopperScope]),O=(0,T.createPopperScope)(),H="TooltipProvider",I="tooltip.open",[D,G]=_(H),B=e=>{let{__scopeTooltip:r,delayDuration:n=700,skipDelayDuration:i=300,disableHoverableContent:s=!1,children:o}=e,l=a.useRef(!0),d=a.useRef(!1),c=a.useRef(0);return a.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,t.jsx)(D,{scope:r,isOpenDelayedRef:l,delayDuration:n,onOpen:a.useCallback(()=>{window.clearTimeout(c.current),l.current=!1},[]),onClose:a.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.current=!0,i)},[i]),isPointerInTransitRef:d,onPointerInTransitChange:a.useCallback(e=>{d.current=e},[]),disableHoverableContent:s,children:o})};B.displayName=H;var Y="Tooltip",[U,q]=_(Y),F=e=>{let{__scopeTooltip:r,children:n,open:i,defaultOpen:s,onOpenChange:o,disableHoverableContent:l,delayDuration:d}=e,c=G(Y,e.__scopeTooltip),u=O(r),[p,b]=a.useState(null),f=(0,R.useId)(),h=a.useRef(0),m=null!=l?l:c.disableHoverableContent,x=null!=d?d:c.delayDuration,g=a.useRef(!1),[v,y]=(0,M.useControllableState)({prop:i,defaultProp:null!=s&&s,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(I))):c.onClose(),null==o||o(e)},caller:Y}),w=a.useMemo(()=>v?g.current?"delayed-open":"instant-open":"closed",[v]),j=a.useCallback(()=>{window.clearTimeout(h.current),h.current=0,g.current=!1,y(!0)},[y]),N=a.useCallback(()=>{window.clearTimeout(h.current),h.current=0,y(!1)},[y]),S=a.useCallback(()=>{window.clearTimeout(h.current),h.current=window.setTimeout(()=>{g.current=!0,y(!0),h.current=0},x)},[x,y]);return a.useEffect(()=>()=>{h.current&&(window.clearTimeout(h.current),h.current=0)},[]),(0,t.jsx)(T.Root,{...u,children:(0,t.jsx)(U,{scope:r,contentId:f,open:v,stateAttribute:w,trigger:p,onTriggerChange:b,onTriggerEnter:a.useCallback(()=>{c.isOpenDelayedRef.current?S():j()},[c.isOpenDelayedRef,S,j]),onTriggerLeave:a.useCallback(()=>{m?N():(window.clearTimeout(h.current),h.current=0)},[N,m]),onOpen:j,onClose:N,disableHoverableContent:m,children:n})})};F.displayName=Y;var K="TooltipTrigger",V=a.forwardRef((e,r)=>{let{__scopeTooltip:n,...i}=e,s=q(K,n),o=G(K,n),l=O(n),c=a.useRef(null),u=(0,C.useComposedRefs)(r,c,s.onTriggerChange),p=a.useRef(!1),b=a.useRef(!1),f=a.useCallback(()=>p.current=!1,[]);return a.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),(0,t.jsx)(T.Anchor,{asChild:!0,...l,children:(0,t.jsx)(d.Primitive.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...i,ref:u,onPointerMove:(0,S.composeEventHandlers)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(b.current||o.isPointerInTransitRef.current||(s.onTriggerEnter(),b.current=!0))}),onPointerLeave:(0,S.composeEventHandlers)(e.onPointerLeave,()=>{s.onTriggerLeave(),b.current=!1}),onPointerDown:(0,S.composeEventHandlers)(e.onPointerDown,()=>{s.open&&s.onClose(),p.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:(0,S.composeEventHandlers)(e.onFocus,()=>{p.current||s.onOpen()}),onBlur:(0,S.composeEventHandlers)(e.onBlur,s.onClose),onClick:(0,S.composeEventHandlers)(e.onClick,s.onClose)})})});V.displayName=K;var W="TooltipPortal",[X,J]=_(W,{forceMount:void 0}),Q=e=>{let{__scopeTooltip:a,forceMount:r,children:n,container:i}=e,s=q(W,a);return(0,t.jsx)(X,{scope:a,forceMount:r,children:(0,t.jsx)(L.Presence,{present:r||s.open,children:(0,t.jsx)(z.Portal,{asChild:!0,container:i,children:n})})})};Q.displayName=W;var Z="TooltipContent",$=a.forwardRef((e,a)=>{let r=J(Z,e.__scopeTooltip),{forceMount:n=r.forceMount,side:i="top",...s}=e,o=q(Z,e.__scopeTooltip);return(0,t.jsx)(L.Presence,{present:n||o.open,children:o.disableHoverableContent?(0,t.jsx)(en,{side:i,...s,ref:a}):(0,t.jsx)(ee,{side:i,...s,ref:a})})}),ee=a.forwardRef((e,r)=>{let n=q(Z,e.__scopeTooltip),i=G(Z,e.__scopeTooltip),s=a.useRef(null),o=(0,C.useComposedRefs)(r,s),[l,d]=a.useState(null),{trigger:c,onClose:u}=n,p=s.current,{onPointerInTransitChange:b}=i,f=a.useCallback(()=>{d(null),b(!1)},[b]),h=a.useCallback((e,t)=>{let a=e.currentTarget,r={x:e.clientX,y:e.clientY},n=function(e,t){let a=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),n=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(a,r,n,i)){case i:return"left";case n:return"right";case a:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,a.getBoundingClientRect());d(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let a=0;a<e.length;a++){let r=e[a];for(;t.length>=2;){let e=t[t.length-1],a=t[t.length-2];if((e.x-a.x)*(r.y-a.y)>=(e.y-a.y)*(r.x-a.x))t.pop();else break}t.push(r)}t.pop();let a=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;a.length>=2;){let e=a[a.length-1],t=a[a.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))a.pop();else break}a.push(r)}return(a.pop(),1===t.length&&1===a.length&&t[0].x===a[0].x&&t[0].y===a[0].y)?t:t.concat(a)}(t)}([...function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-a,y:e.y+a},{x:e.x+a,y:e.y+a});break;case"bottom":r.push({x:e.x-a,y:e.y-a},{x:e.x+a,y:e.y-a});break;case"left":r.push({x:e.x+a,y:e.y-a},{x:e.x+a,y:e.y+a});break;case"right":r.push({x:e.x-a,y:e.y-a},{x:e.x-a,y:e.y+a})}return r}(r,n),...function(e){let{top:t,right:a,bottom:r,left:n}=e;return[{x:n,y:t},{x:a,y:t},{x:a,y:r},{x:n,y:r}]}(t.getBoundingClientRect())])),b(!0)},[b]);return a.useEffect(()=>()=>f(),[f]),a.useEffect(()=>{if(c&&p){let e=e=>h(e,p),t=e=>h(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,h,f]),a.useEffect(()=>{if(l){let e=e=>{let t=e.target,a={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==p?void 0:p.contains(t)),n=!function(e,t){let{x:a,y:r}=e,n=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let s=t[e],o=t[i],l=s.x,d=s.y,c=o.x,u=o.y;d>r!=u>r&&a<(c-l)*(r-d)/(u-d)+l&&(n=!n)}return n}(a,l);r?f():n&&(f(),u())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,l,u,f]),(0,t.jsx)(en,{...e,ref:o})}),[et,ea]=_(Y,{isInside:!1}),er=(0,r.createSlottable)("TooltipContent"),en=a.forwardRef((e,r)=>{let{__scopeTooltip:n,children:i,"aria-label":s,onEscapeKeyDown:o,onPointerDownOutside:l,...d}=e,c=q(Z,n),u=O(n),{onClose:p}=c;return a.useEffect(()=>(document.addEventListener(I,p),()=>document.removeEventListener(I,p)),[p]),a.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(c.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,p]),(0,t.jsx)(E.DismissableLayer,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,t.jsxs)(T.Content,{"data-state":c.stateAttribute,...u,...d,ref:r,style:{...d.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,t.jsx)(er,{children:i}),(0,t.jsx)(et,{scope:n,isInside:!0,children:(0,t.jsx)(A.Root,{id:c.contentId,role:"tooltip",children:s||i})})]})})});$.displayName=Z;var ei="TooltipArrow",es=a.forwardRef((e,a)=>{let{__scopeTooltip:r,...n}=e,i=O(r);return ea(ei,r).isInside?null:(0,t.jsx)(T.Arrow,{...i,...n,ref:a})});function eo(e){let{delayDuration:a=0,...r}=e;return(0,t.jsx)(B,{"data-slot":"tooltip-provider",delayDuration:a,...r})}function el(e){let{...a}=e;return(0,t.jsx)(eo,{children:(0,t.jsx)(F,{"data-slot":"tooltip",...a})})}function ed(e){let{...a}=e;return(0,t.jsx)(V,{"data-slot":"tooltip-trigger",...a})}function ec(e){let{className:a,sideOffset:r=0,children:n,...i}=e;return(0,t.jsx)(Q,{children:(0,t.jsxs)($,{"data-slot":"tooltip-content",sideOffset:r,className:(0,s.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",a),...i,children:[n,(0,t.jsx)(es,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}es.displayName=ei;let eu=a.createContext(null);function ep(){let e=a.useContext(eu);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function eb(e){let{defaultOpen:r=!0,open:n,onOpenChange:i,className:o,style:l,children:d,...c}=e,u=function(){let[e,t]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),e}(),[p,b]=a.useState(!1),[f,h]=a.useState(r),m=null!=n?n:f,x=a.useCallback(e=>{let t="function"==typeof e?e(m):e;i?i(t):h(t),document.cookie="".concat("sidebar_state","=").concat(t,"; path=/; max-age=").concat(604800)},[i,m]),g=a.useCallback(()=>u?b(e=>!e):x(e=>!e),[u,x,b]);a.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),g())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[g]);let v=m?"expanded":"collapsed",y=a.useMemo(()=>({state:v,open:m,setOpen:x,isMobile:u,openMobile:p,setOpenMobile:b,toggleSidebar:g}),[v,m,x,u,p,b,g]);return(0,t.jsx)(eu.Provider,{value:y,children:(0,t.jsx)(eo,{delayDuration:0,children:(0,t.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...l},className:(0,s.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",o),...c,children:d})})})}function ef(e){let{side:a="left",variant:r="sidebar",collapsible:n="offcanvas",className:i,children:o,...l}=e,{isMobile:d,state:c,openMobile:u,setOpenMobile:p}=ep();return"none"===n?(0,t.jsx)("div",{"data-slot":"sidebar",className:(0,s.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",i),...l,children:o}):d?(0,t.jsx)(m,{open:u,onOpenChange:p,...l,children:(0,t.jsxs)(v,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:a,children:[(0,t.jsxs)(y,{className:"sr-only",children:[(0,t.jsx)(w,{children:"Sidebar"}),(0,t.jsx)(j,{children:"Displays the mobile sidebar."})]}),(0,t.jsx)("div",{className:"flex h-full w-full flex-col",children:o})]})}):(0,t.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":"collapsed"===c?n:"","data-variant":r,"data-side":a,"data-slot":"sidebar",children:[(0,t.jsx)("div",{"data-slot":"sidebar-gap",className:(0,s.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===r||"inset"===r?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,t.jsx)("div",{"data-slot":"sidebar-container",className:(0,s.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===a?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===r||"inset"===r?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",i),...l,children:(0,t.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:o})})]})}function eh(e){let{className:a,onClick:r,...n}=e,{toggleSidebar:l}=ep();return(0,t.jsxs)(o.Button,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,s.cn)("size-7",a),onClick:e=>{null==r||r(e),l()},...n,children:[(0,t.jsx)(i,{}),(0,t.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function em(e){let{className:a,...r}=e,{toggleSidebar:n}=ep();return(0,t.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:n,title:"Toggle Sidebar",className:(0,s.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",a),...r})}function ex(e){let{className:a,...r}=e;return(0,t.jsx)("main",{"data-slot":"sidebar-inset",className:(0,s.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",a),...r})}function eg(e){let{className:a,...r}=e;return(0,t.jsx)(l.Input,{"data-slot":"sidebar-input","data-sidebar":"input",className:(0,s.cn)("bg-background h-8 w-full shadow-none",a),...r})}function ev(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,s.cn)("flex flex-col gap-2 p-2",a),...r})}function ey(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,s.cn)("flex flex-col gap-2 p-2",a),...r})}function ew(e){let{className:a,...r}=e;return(0,t.jsx)(b,{"data-slot":"sidebar-separator","data-sidebar":"separator",className:(0,s.cn)("bg-sidebar-border mx-2 w-auto",a),...r})}function ej(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,s.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",a),...r})}function eN(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,s.cn)("relative flex w-full min-w-0 flex-col p-2",a),...r})}function eS(e){let{className:a,asChild:n=!1,...i}=e,o=n?r.Slot:"div";return(0,t.jsx)(o,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,s.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",a),...i})}function eC(e){let{className:a,asChild:n=!1,...i}=e,o=n?r.Slot:"button";return(0,t.jsx)(o,{"data-slot":"sidebar-group-action","data-sidebar":"group-action",className:(0,s.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 md:after:hidden","group-data-[collapsible=icon]:hidden",a),...i})}function ek(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,s.cn)("w-full text-sm",a),...r})}function eE(e){let{className:a,...r}=e;return(0,t.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,s.cn)("flex w-full min-w-0 flex-col gap-1",a),...r})}function eR(e){let{className:a,...r}=e;return(0,t.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,s.cn)("group/menu-item relative",a),...r})}let eT=(0,n.cva)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function ez(e){let{asChild:a=!1,isActive:n=!1,variant:i="default",size:o="default",tooltip:l,className:d,...c}=e,u=a?r.Slot:"button",{isMobile:p,state:b}=ep(),f=(0,t.jsx)(u,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":o,"data-active":n,className:(0,s.cn)(eT({variant:i,size:o}),d),...c});return l?("string"==typeof l&&(l={children:l}),(0,t.jsxs)(el,{children:[(0,t.jsx)(ed,{asChild:!0,children:f}),(0,t.jsx)(ec,{side:"right",align:"center",hidden:"collapsed"!==b||p,...l})]})):f}function eL(e){let{className:a,asChild:n=!1,showOnHover:i=!1,...o}=e,l=n?r.Slot:"button";return(0,t.jsx)(l,{"data-slot":"sidebar-menu-action","data-sidebar":"menu-action",className:(0,s.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 md:after:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",i&&"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0",a),...o})}function eM(e){let{className:a,...r}=e;return(0,t.jsx)("div",{"data-slot":"sidebar-menu-badge","data-sidebar":"menu-badge",className:(0,s.cn)("text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",a),...r})}function eA(e){let{className:r,showIcon:n=!1,...i}=e,o=a.useMemo(()=>"".concat(Math.floor(40*Math.random())+50,"%"),[]);return(0,t.jsxs)("div",{"data-slot":"sidebar-menu-skeleton","data-sidebar":"menu-skeleton",className:(0,s.cn)("flex h-8 items-center gap-2 rounded-md px-2",r),...i,children:[n&&(0,t.jsx)(N,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,t.jsx)(N,{className:"h-4 max-w-(--skeleton-width) flex-1","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":o}})]})}function e_(e){let{className:a,...r}=e;return(0,t.jsx)("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:(0,s.cn)("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",a),...r})}function eP(e){let{className:a,...r}=e;return(0,t.jsx)("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:(0,s.cn)("group/menu-sub-item relative",a),...r})}function eO(e){let{asChild:a=!1,size:n="md",isActive:i=!1,className:o,...l}=e,d=a?r.Slot:"a";return(0,t.jsx)(d,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":n,"data-active":i,className:(0,s.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===n&&"text-xs","md"===n&&"text-sm","group-data-[collapsible=icon]:hidden",o),...l})}},57089,e=>{"use strict";e.s(["Header",()=>l],57089);var t=e.i(4051),a=e.i(1269);let r=(0,e.i(44571).default)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);var n=e.i(60019),i=e.i(85205),s=e.i(96134),o=e.i(17961);function l(){var e;let{data:l}=(0,a.useSession)();return(0,t.jsx)("header",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(o.SidebarTrigger,{}),(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Dashboard"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.Search,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(s.Input,{placeholder:"Search...",className:"pl-10 w-64"})]}),(0,t.jsx)(i.Button,{variant:"ghost",size:"icon",children:(0,t.jsx)(r,{className:"h-5 w-5"})}),(null==l?void 0:l.user)&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white text-sm font-medium",children:(null==(e=l.user.username)?void 0:e.charAt(0))||"U"})}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:l.user.username})]})]})]})})}},57026,e=>{"use strict";e.s(["AppSidebar",()=>g],57026);var t=e.i(4051),a=e.i(5085),r=e.i(57691),n=e.i(1269),i=e.i(44571);let s=(0,i.default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var o=e.i(1827);let l=(0,i.default)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),d=(0,i.default)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);var c=e.i(67435),u=e.i(42633);let p=(0,i.default)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),b=(0,i.default)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),f=(0,i.default)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var h=e.i(85205),m=e.i(17961);let x=[{name:"Dashboard",href:"/dashboard",icon:s},{name:"Membres",href:"/dashboard/members",icon:f,roles:["SECRETARY_GENERAL","CONTROLLER"]},{name:"Sessions",href:"/dashboard/sessions",icon:c.Calendar,roles:["SECRETARY_GENERAL","CONTROLLER","CASHIER"]},{name:"Caisses",href:"/dashboard/caisses",icon:u.Wallet,roles:["SECRETARY_GENERAL","CONTROLLER"]},{name:"Paiements",href:"/dashboard/payments",icon:b,roles:["SECRETARY_GENERAL","CONTROLLER","CASHIER"]},{name:"Utilisateurs",href:"/dashboard/users",icon:o.Users,roles:["SECRETARY_GENERAL"]},{name:"Rapports",href:"/dashboard/reports",icon:p,roles:["SECRETARY_GENERAL","CONTROLLER"]},{name:"Paramètres",href:"/dashboard/settings",icon:l,roles:["SECRETARY_GENERAL"]}];function g(){var e;let{state:i}=(0,m.useSidebar)(),s=(0,r.usePathname)(),{data:o}=(0,n.useSession)(),l=x.filter(e=>{var t,a;if(!e.roles)return!0;let r=null==(a=null==o?void 0:o.user)||null==(t=a.role)?void 0:t.toUpperCase();return r&&e.roles.includes(r)});return(0,t.jsxs)(m.Sidebar,{collapsible:"icon",children:[(0,t.jsx)(m.SidebarHeader,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground",children:(0,t.jsx)(u.Wallet,{className:"size-4"})}),(0,t.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,t.jsx)("span",{className:"truncate font-semibold",children:"Tontine"}),(0,t.jsx)("span",{className:"truncate text-xs",children:"Gestion de tontines"})]})]})}),(0,t.jsx)(m.SidebarContent,{children:(0,t.jsxs)(m.SidebarGroup,{children:[(0,t.jsx)(m.SidebarGroupLabel,{children:"Navigation"}),(0,t.jsx)(m.SidebarGroupContent,{children:(0,t.jsx)(m.SidebarMenu,{children:l.map(e=>{let r=e.icon,n=s===e.href||s.startsWith("".concat(e.href,"/"));return(0,t.jsx)(m.SidebarMenuItem,{children:(0,t.jsx)(m.SidebarMenuButton,{asChild:!0,isActive:n,children:(0,t.jsxs)(a.default,{href:e.href,children:[(0,t.jsx)(r,{}),(0,t.jsx)("span",{children:e.name})]})})},e.name)})})})]})}),(0,t.jsx)(m.SidebarFooter,{children:(null==o?void 0:o.user)&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-accent text-sidebar-accent-foreground",children:(0,t.jsx)("span",{className:"text-xs font-medium",children:null==(e=o.user.username)?void 0:e.charAt(0).toUpperCase()})}),(0,t.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,t.jsx)("span",{className:"truncate font-semibold",children:o.user.username}),(0,t.jsx)("span",{className:"truncate text-xs",children:o.user.role})]})]}),(0,t.jsxs)(h.Button,{variant:"destructive",onClick:()=>{(0,n.signOut)({callbackUrl:"/auth/signin"})},className:"w-full justify-start",children:[(0,t.jsx)(d,{className:"mr-2 h-4 w-4"}),"expanded"===i?"Sign out":null]})]})})]})}}]);