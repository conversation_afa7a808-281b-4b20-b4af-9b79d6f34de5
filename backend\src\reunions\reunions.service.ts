import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, Types } from 'mongoose';
import { Reunion, ReunionDocument } from './schemas/reunion.schema';
import { UpdateReunionDto } from './dto/update-reunion.dto';
import { Session, SessionDocument } from '../sessions/schemas/session.schema';
import { SessionMember, SessionMemberDocument } from '../sessions/schemas/session-member.schema';
import { Member, MemberDocument } from '../members/schemas/member.schema';
import { ExpectedPaymentItem } from './dto/expected-payments.dto';
import { Payment, PaymentDirection, PaymentDocument, PaymentFunction } from '../payments/schemas/payment.schema';
import { PaymentFiltersDto } from '../common/dto/payment-filters.dto';

@Injectable()
export class ReunionsService {
  constructor(
    @InjectModel(Reunion.name) private reunionModel: Model<ReunionDocument>,
    @InjectModel(Session.name) private sessionModel: Model<SessionDocument>,
    @InjectModel(SessionMember.name) private sessionMemberModel: Model<SessionMemberDocument>,
    @InjectModel(Member.name) private memberModel: Model<MemberDocument>,
    @InjectModel(Payment.name) private paymentModel: Model<PaymentDocument>,
  ) {}

  async findAll(): Promise<Reunion[]> {
    return this.reunionModel.find().sort({ dateReunion: 1 }).lean();
  }

  async findOne(id: string): Promise<Reunion | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Réunion introuvable');
    return this.reunionModel.findById(id).lean();
  }

  async update(id: string, dto: UpdateReunionDto): Promise<Reunion | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Réunion introuvable');
    const update: any = {};
    if (dto.dateReunion) update.dateReunion = new Date(dto.dateReunion);
    if (dto.lieu !== undefined) update.lieu = dto.lieu;
    if (dto.caissePrincipale) update.caissePrincipale = new Types.ObjectId(dto.caissePrincipale);

    const updated = await this.reunionModel.findByIdAndUpdate(
      id,
      { $set: update },
      { new: true },
    );
    // precaution: update session's next meeting when a reunion changes
    const updatedObj = updated?.toObject() ?? null;
    if (updatedObj) {
      await this.updateSessionNext(updatedObj.sessionId.toString());
    }
    return updatedObj;
  }

  private async updateSessionNext(sessionId: string) {
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const nextReunion = await this.reunionModel
      .findOne({ sessionId: new Types.ObjectId(sessionId), dateReunion: { $gte: todayStart } })
      .sort({ dateReunion: 1 });
    if (nextReunion) {
      await this.sessionModel.findByIdAndUpdate(sessionId, {
        $set: { dateProchaineReunion: nextReunion.dateReunion, nextReunionId: nextReunion._id },
      });
    } else {
      await this.sessionModel.findByIdAndUpdate(sessionId, {
        $unset: { dateProchaineReunion: '', nextReunionId: '' },
      });
    }
  }

  // Compute expected payments JSON for a reunion
  async expectedPayments(id: string): Promise<ExpectedPaymentItem[]> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Réunion introuvable');
    const reunion = await this.reunionModel.findById(id);
    if (!reunion) throw new NotFoundException('Réunion introuvable');

    const session = await this.sessionModel.findById(reunion.sessionId);
    if (!session) throw new NotFoundException('Session introuvable');

    const sessionMembers = await this.sessionMemberModel.find({ sessionId: session._id }).lean();
    const membersIds = sessionMembers.map((sm) => sm.memberId);
    const members = await this.memberModel.find({ _id: { $in: membersIds } }).lean();
    const memberMap = new Map(members.map((m) => [m._id.toString(), m]));

    // montant par réunion pour chaque membre = partFixe * parts
    return sessionMembers.map<ExpectedPaymentItem>((sm) => {
      const m = memberMap.get(sm.memberId.toString());
      const fullName = m ? `${m.firstName} ${m.lastName}` : 'Unknown';
      const perMeetingDue = session.partFixe * sm.parts;
      return {
        memberId: sm.memberId.toString(),
        fullName,
        parts: sm.parts,
        perMeetingDue,
        expectedAmount: perMeetingDue, // pour cette réunion uniquement
        paidSoFar: sm.paidSoFar || 0,
        overdueAmount: sm.overdueAmount || 0,
      };
    });
  }

  // Debrief financier d'une réunion (agrégé) avec filtres optionnels
  async debrief(id: string, filters: PaymentFiltersDto) {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Réunion introuvable');
    const reunion = await this.reunionModel.findById(id);
    if (!reunion) throw new NotFoundException('Réunion introuvable');

    const q: any = { reunionId: new Types.ObjectId(id) };
    if (filters.memberId) q.memberId = new Types.ObjectId(filters.memberId);
    if (filters.direction) q.direction = filters.direction;
    if (filters.func) q.func = filters.func;
    if (filters.startDate || filters.endDate) {
      q.date = {} as any;
      if (filters.startDate) q.date.$gte = new Date(filters.startDate);
      if (filters.endDate) q.date.$lte = new Date(filters.endDate);
    }

    const payments = await this.paymentModel.find(q).lean();
    const totalIn = payments.filter(p => p.direction === PaymentDirection.IN).reduce((s,p)=>s+p.amount, 0);
    const totalOut = payments.filter(p => p.direction === PaymentDirection.OUT).reduce((s,p)=>s+p.amount, 0);

    const byFunc = payments.reduce((acc:any, p:any)=>{ acc[p.func]=(acc[p.func]||0)+p.amount; return acc; },{});

    // état attendu/actuel
    const session = await this.sessionModel.findById(reunion.sessionId);
    if (!session) throw new NotFoundException('Session introuvable');
    const sessionMembers = await this.sessionMemberModel.find({ sessionId: reunion.sessionId }).lean();
    const expectedPerMember = sessionMembers.map(sm => session.partFixe * sm.parts);
    const expectedTotal = expectedPerMember.reduce((s,v)=>s+v, 0);
    const receivedContrib = payments.filter(p => p.direction === PaymentDirection.IN && p.func === PaymentFunction.CONTRIBUTION)
      .reduce((s,p)=>s+p.amount, 0);

    return {
      reunionId: id,
      filters,
      totalIn,
      totalOut,
      net: totalIn-totalOut,
      byFunc,
      count: payments.length,
      state: {
        membersExpected: sessionMembers.length,
        expectedTotal,
        receivedContrib,
        gap: expectedTotal - receivedContrib,
      },
    };
  }

  // Prochaine date de cotisation en tenant compte des avances: on saute les réunions déjà entièrement couvertes
  async nextContribution(memberId: string, sessionId: string) {
    if (!Types.ObjectId.isValid(memberId)) throw new NotFoundException('Membre invalide');
    if (!Types.ObjectId.isValid(sessionId)) throw new NotFoundException('Session invalide');

    const session = await this.sessionModel.findById(sessionId);
    if (!session) throw new NotFoundException('Session introuvable');

    const affiliation = await this.sessionMemberModel.findOne({ sessionId: session._id, memberId: new Types.ObjectId(memberId) });
    if (!affiliation) throw new NotFoundException('Membre non inscrit à la session');

    const perMeetingDue = (session.partFixe || 0) * (affiliation.parts || 0);
    const now = new Date();
    const futureReunions = await this.reunionModel.find({ sessionId: session._id, dateReunion: { $gte: now } }).sort({ dateReunion: 1 });
    if (futureReunions.length === 0) {
      return { memberId, sessionId, nextReunion: null, amountDue: 0, note: 'Aucune réunion future' };
    }

    // Calcule l'avance disponible (global) et cherche la 1ère réunion non couverte
    let availableAdvance = Math.max(0, (affiliation.paidSoFar || 0) - (affiliation.expectedToDate || 0));
    for (const r of futureReunions) {
      if (availableAdvance >= perMeetingDue) {
        availableAdvance -= perMeetingDue; // cette réunion est déjà couverte par l'avance
        continue; // passer à la prochaine
      }
      // Cette réunion n'est pas entièrement couverte: il reste à payer perMeetingDue - availableAdvance
      const amountDue = Math.max(0, perMeetingDue - availableAdvance);
      return {
        memberId,
        sessionId,
        nextReunion: { id: r._id.toString(), date: r.dateReunion },
        perMeetingDue,
        amountDue,
      };
    }

    // Si toutes les réunions futures sont couvertes par l'avance
    return { memberId, sessionId, nextReunion: null, amountDue: 0, note: 'Toutes les réunions futures sont déjà couvertes par l\'avance' };
  }
}