module.exports=[76337,a=>{"use strict";a.s(["Header",()=>b]);let b=(0,a.i(73070).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/layout/header.tsx <module evaluation>","Header")},25079,a=>{"use strict";a.s(["Header",()=>b]);let b=(0,a.i(73070).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Head<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/layout/header.tsx","Header")},67348,a=>{"use strict";a.i(76337);var b=a.i(25079);a.n(b)},71923,a=>{"use strict";a.s(["AppSidebar",()=>b]);let b=(0,a.i(73070).registerClientReference)(function(){throw Error("Attempted to call AppSidebar() from the server but AppSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/layout/sidebar.tsx <module evaluation>","AppSidebar")},24153,a=>{"use strict";a.s(["AppSidebar",()=>b]);let b=(0,a.i(73070).registerClientReference)(function(){throw Error("Attempted to call AppSidebar() from the server but AppSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/layout/sidebar.tsx","AppSidebar")},62778,a=>{"use strict";a.i(71923);var b=a.i(24153);a.n(b)},14124,a=>{"use strict";a.s(["Sidebar",()=>c,"SidebarContent",()=>d,"SidebarFooter",()=>e,"SidebarGroup",()=>f,"SidebarGroupAction",()=>g,"SidebarGroupContent",()=>h,"SidebarGroupLabel",()=>i,"SidebarHeader",()=>j,"SidebarInput",()=>k,"SidebarInset",()=>l,"SidebarMenu",()=>m,"SidebarMenuAction",()=>n,"SidebarMenuBadge",()=>o,"SidebarMenuButton",()=>p,"SidebarMenuItem",()=>q,"SidebarMenuSkeleton",()=>r,"SidebarMenuSub",()=>s,"SidebarMenuSubButton",()=>t,"SidebarMenuSubItem",()=>u,"SidebarProvider",()=>v,"SidebarRail",()=>w,"SidebarSeparator",()=>x,"SidebarTrigger",()=>y,"useSidebar",()=>z]);var b=a.i(73070);let c=(0,b.registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","Sidebar"),d=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarContent"),e=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarFooter"),f=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarGroup"),g=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarGroupAction"),h=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarGroupContent"),i=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarGroupLabel"),j=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarHeader"),k=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarInput"),l=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarInset"),m=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarMenu"),n=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarMenuAction"),o=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarMenuBadge"),p=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarMenuButton"),q=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarMenuItem"),r=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarMenuSkeleton"),s=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarMenuSub"),t=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarMenuSubButton"),u=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarMenuSubItem"),v=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarProvider"),w=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarRail"),x=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarSeparator"),y=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","SidebarTrigger"),z=(0,b.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx <module evaluation>","useSidebar")},7278,a=>{"use strict";a.s(["Sidebar",()=>c,"SidebarContent",()=>d,"SidebarFooter",()=>e,"SidebarGroup",()=>f,"SidebarGroupAction",()=>g,"SidebarGroupContent",()=>h,"SidebarGroupLabel",()=>i,"SidebarHeader",()=>j,"SidebarInput",()=>k,"SidebarInset",()=>l,"SidebarMenu",()=>m,"SidebarMenuAction",()=>n,"SidebarMenuBadge",()=>o,"SidebarMenuButton",()=>p,"SidebarMenuItem",()=>q,"SidebarMenuSkeleton",()=>r,"SidebarMenuSub",()=>s,"SidebarMenuSubButton",()=>t,"SidebarMenuSubItem",()=>u,"SidebarProvider",()=>v,"SidebarRail",()=>w,"SidebarSeparator",()=>x,"SidebarTrigger",()=>y,"useSidebar",()=>z]);var b=a.i(73070);let c=(0,b.registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","Sidebar"),d=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarContent"),e=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarFooter"),f=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarGroup"),g=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarGroupAction"),h=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarGroupContent"),i=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarGroupLabel"),j=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarHeader"),k=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarInput"),l=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarInset"),m=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarMenu"),n=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarMenuAction"),o=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarMenuBadge"),p=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarMenuButton"),q=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarMenuItem"),r=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarMenuSkeleton"),s=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarMenuSub"),t=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarMenuSubButton"),u=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarMenuSubItem"),v=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarProvider"),w=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarRail"),x=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarSeparator"),y=(0,b.registerClientReference)(function(){throw Error("Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","SidebarTrigger"),z=(0,b.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/frontend/src/components/ui/sidebar.tsx","useSidebar")},61700,a=>{"use strict";a.i(14124);var b=a.i(7278);a.n(b)},69397,a=>{"use strict";a.s(["default",()=>f]);var b=a.i(70541),c=a.i(67348),d=a.i(62778),e=a.i(61700);function f({children:a}){return(0,b.jsxs)(e.SidebarProvider,{defaultOpen:!0,children:[(0,b.jsx)(d.AppSidebar,{}),(0,b.jsxs)(e.SidebarInset,{children:[(0,b.jsx)(c.Header,{}),(0,b.jsx)("main",{className:"flex-1 p-6",children:a})]})]})}}];

//# sourceMappingURL=frontend_src_4dab10f1._.js.map