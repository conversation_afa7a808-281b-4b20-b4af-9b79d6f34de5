import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Member, MemberDocument } from './schemas/member.schema';
import { CreateMemberDto } from './dto/create-member.dto';
import { UpdateMemberDto } from './dto/update-member.dto';
import { Payment, PaymentDocument, PaymentDirection, PaymentFunction } from '../payments/schemas/payment.schema';
import { PaymentFiltersDto } from '../common/dto/payment-filters.dto';

@Injectable()
export class MembersService {
  constructor(
    @InjectModel(Member.name) private memberModel: Model<MemberDocument>,
    @InjectModel(Payment.name) private paymentModel: Model<PaymentDocument>,
  ) {}

  async create(dto: CreateMemberDto): Promise<Member> {
    const member = await this.memberModel.create(dto);
    return member.toObject();
  }

  async findAll(): Promise<Member[]> {
    return this.memberModel.find().sort({ lastName: 1, firstName: 1 }).lean();
  }

  async findOne(id: string): Promise<Member | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Membre introuvable');
    return this.memberModel.findById(id).lean();
  }

  async update(id: string, dto: UpdateMemberDto): Promise<Member | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Membre introuvable');
    const updated = await this.memberModel.findByIdAndUpdate(id, { $set: dto }, { new: true });
    return updated?.toObject() ?? null;
  }

  async remove(id: string): Promise<void> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Membre introuvable');
    await this.memberModel.findByIdAndDelete(id);
  }

  // Debrief financier d'un membre avec filtres
  async paymentsDebrief(memberId: string, filters: PaymentFiltersDto) {
    if (!Types.ObjectId.isValid(memberId)) throw new NotFoundException('Membre introuvable');
    const q: any = { memberId: new Types.ObjectId(memberId) };
    if (filters.sessionId) q.sessionId = new Types.ObjectId(filters.sessionId);
    if (filters.reunionId) q.reunionId = new Types.ObjectId(filters.reunionId);
    if (filters.direction) q.direction = filters.direction;
    if (filters.func) q.func = filters.func;
    if (filters.startDate || filters.endDate) {
      q.date = {} as any;
      if (filters.startDate) q.date.$gte = new Date(filters.startDate);
      if (filters.endDate) q.date.$lte = new Date(filters.endDate);
    }

    const payments = await this.paymentModel.find(q).lean();
    const totalIn = payments.filter(p => p.direction === PaymentDirection.IN).reduce((s,p)=>s+p.amount, 0);
    const totalOut = payments.filter(p => p.direction === PaymentDirection.OUT).reduce((s,p)=>s+p.amount, 0);
    const byFunc = payments.reduce((acc:any, p:any)=>{ acc[p.func]=(acc[p.func]||0)+p.amount; return acc; },{});

    return { memberId, filters, totalIn, totalOut, net: totalIn-totalOut, byFunc, count: payments.length };
  }
}