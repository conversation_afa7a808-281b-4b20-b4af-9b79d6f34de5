{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/frontend_edge-wrapper_294b6812.js", "server/edge/chunks/[root-of-the-server]__0f6a7f2d._.js", "server/edge/chunks/turbopack-frontend_edge-wrapper_09e5e36f.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "12L33YVBTsNybcYA2to9d", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "apf+SPZMzT5yW82p9rfjV4S0C00MQFYgvHSGcDkfCl0=", "__NEXT_PREVIEW_MODE_ID": "2c7253ebfb266b07ad059397ade705bb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0af73b623fe1a93b0a8d34138d654e4da66e47f93b9b87e3ebfed057f801e058", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c9e2b4ed5c2f4f1f8b933c0d64be466e491e939d4cf591eb782476e22ccc7d02"}}}, "sortedMiddleware": ["/"], "functions": {}}