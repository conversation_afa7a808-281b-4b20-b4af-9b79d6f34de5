{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/frontend_a8ef3cd8._.js", "server/edge/chunks/07131_@auth_core_b6d1e115._.js", "server/edge/chunks/836f6_jose_dist_webapi_9125ab7e._.js", "server/edge/chunks/948cc_zod_v4_de5cece9._.js", "server/edge/chunks/3a74a_tailwind-merge_dist_bundle-mjs_mjs_6b04a0b0._.js", "server/edge/chunks/9e883__pnpm_43258dde._.js", "server/edge/chunks/[root-of-the-server]__3a3d429a._.js", "server/edge/chunks/turbopack-frontend_edge-wrapper_03e5c9f9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "apf+SPZMzT5yW82p9rfjV4S0C00MQFYgvHSGcDkfCl0=", "__NEXT_PREVIEW_MODE_ID": "c3037c38420ebf0fd2875ce1d18e465c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "dea5f130a8b52edfb2f77cd9209d7d30ce338d3b2c7ced9eb1451cb94560e5c1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cd4ce4863a750565d3890266a80d0b62db61296ad16e42ab4d36abb91376d9b3"}}}, "sortedMiddleware": ["/"], "functions": {}}