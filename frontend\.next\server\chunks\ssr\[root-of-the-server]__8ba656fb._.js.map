{"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/next-auth@5.0.0-beta.29_nex_626fd582465e254ed3dcaa950f0f297e/node_modules/next-auth/react.js", "turbopack:///[project]/frontend/node_modules/.pnpm/@auth+core@0.40.0/node_modules/@auth/core/errors.js", "turbopack:///[project]/frontend/node_modules/.pnpm/next-auth@5.0.0-beta.29_nex_626fd582465e254ed3dcaa950f0f297e/node_modules/next-auth/lib/client.js", "turbopack:///[project]/frontend/node_modules/.pnpm/@tanstack+query-core@5.87.4/node_modules/@tanstack/query-core/build/modern/queryClient.js", "turbopack:///[project]/frontend/src/components/provider.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/@tanstack+query-core@5.87.4/node_modules/@tanstack/query-core/build/modern/queryCache.js", "turbopack:///[project]/frontend/node_modules/.pnpm/@tanstack+query-core@5.87.4/node_modules/@tanstack/query-core/build/modern/mutationCache.js", "turbopack:///[project]/frontend/node_modules/.pnpm/@tanstack+query-core@5.87.4/node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js", "turbopack:///[project]/frontend/src/lib/query-client.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/@tanstack+react-query-devto_e27081f7abbbd06cce143348b4a4937d/node_modules/@tanstack/react-query-devtools/build/modern/index.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxRuntime\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n", "/**\n *\n * NextAuth.js is the official integration of Auth.js for Next.js applications. It supports both\n * [Client Components](https://nextjs.org/docs/app/building-your-application/rendering/client-components) and the\n * [Pages Router](https://nextjs.org/docs/pages). It includes methods for signing in, signing out, hooks, and a React\n * Context provider to wrap your application and make session data available anywhere.\n *\n * For use in [Server Actions](https://nextjs.org/docs/app/api-reference/functions/server-actions), check out [these methods](https://authjs.dev/guides/upgrade-to-v5#methods)\n *\n * @module react\n */\n\"use client\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport * as React from \"react\";\nimport { apiBaseUrl, ClientSessionError, fetchData, now, parseUrl, useOnline, } from \"./lib/client.js\";\n// This behaviour mirrors the default behaviour for getting the site name that\n// happens server side in server/index.js\n// 1. An empty value is legitimate when the code is being invoked client side as\n//    relative URLs are valid in that context and so defaults to empty.\n// 2. When invoked server side the value is picked up from an environment\n//    variable and defaults to 'http://localhost:3000'.\nexport const __NEXTAUTH = {\n    baseUrl: parseUrl(process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePath: parseUrl(process.env.NEXTAUTH_URL).path,\n    baseUrlServer: parseUrl(process.env.NEXTAUTH_URL_INTERNAL ??\n        process.env.NEXTAUTH_URL ??\n        process.env.VERCEL_URL).origin,\n    basePathServer: parseUrl(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL).path,\n    _lastSync: 0,\n    _session: undefined,\n    _getSession: () => { },\n};\n// https://github.com/nextauthjs/next-auth/pull/10762\nlet broadcastChannel = null;\nfunction getNewBroadcastChannel() {\n    if (typeof BroadcastChannel === \"undefined\") {\n        return {\n            postMessage: () => { },\n            addEventListener: () => { },\n            removeEventListener: () => { },\n            name: \"next-auth\",\n            onmessage: null,\n            onmessageerror: null,\n            close: () => { },\n            dispatchEvent: () => false,\n        };\n    }\n    return new BroadcastChannel(\"next-auth\");\n}\nfunction broadcast() {\n    if (broadcastChannel === null) {\n        broadcastChannel = getNewBroadcastChannel();\n    }\n    return broadcastChannel;\n}\n// TODO:\nconst logger = {\n    debug: console.debug,\n    error: console.error,\n    warn: console.warn,\n};\nexport const SessionContext = React.createContext?.(undefined);\n/**\n * React Hook that gives you access to the logged in user's session data and lets you modify it.\n *\n * :::info\n * `useSession` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */\nexport function useSession(options) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    // @ts-expect-error Satisfy TS if branch on line below\n    const value = React.useContext(SessionContext);\n    if (!value && process.env.NODE_ENV !== \"production\") {\n        throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n    }\n    const { required, onUnauthenticated } = options ?? {};\n    const requiredAndNotLoading = required && value.status === \"unauthenticated\";\n    React.useEffect(() => {\n        if (requiredAndNotLoading) {\n            const url = `${__NEXTAUTH.basePath}/signin?${new URLSearchParams({\n                error: \"SessionRequired\",\n                callbackUrl: window.location.href,\n            })}`;\n            if (onUnauthenticated)\n                onUnauthenticated();\n            else\n                window.location.href = url;\n        }\n    }, [requiredAndNotLoading, onUnauthenticated]);\n    if (requiredAndNotLoading) {\n        return {\n            data: value.data,\n            update: value.update,\n            status: \"loading\",\n        };\n    }\n    return value;\n}\nexport async function getSession(params) {\n    const session = await fetchData(\"session\", __NEXTAUTH, logger, params);\n    if (params?.broadcast ?? true) {\n        // https://github.com/nextauthjs/next-auth/pull/11470\n        getNewBroadcastChannel().postMessage({\n            event: \"session\",\n            data: { trigger: \"getSession\" },\n        });\n    }\n    return session;\n}\n/**\n * Returns the current Cross-Site Request Forgery Token (CSRF Token)\n * required to make requests that changes state. (e.g. signing in or out, or updating the session).\n *\n * [CSRF Prevention: Double Submit Cookie](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html#double-submit-cookie)\n */\nexport async function getCsrfToken() {\n    const response = await fetchData(\"csrf\", __NEXTAUTH, logger);\n    return response?.csrfToken ?? \"\";\n}\nexport async function getProviders() {\n    return fetchData(\"providers\", __NEXTAUTH, logger);\n}\nexport async function signIn(provider, options, authorizationParams) {\n    const { callbackUrl, ...rest } = options ?? {};\n    const { redirect = true, redirectTo = callbackUrl ?? window.location.href, ...signInParams } = rest;\n    const baseUrl = apiBaseUrl(__NEXTAUTH);\n    const providers = await getProviders();\n    if (!providers) {\n        const url = `${baseUrl}/error`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    if (!provider || !providers[provider]) {\n        const url = `${baseUrl}/signin?${new URLSearchParams({\n            callbackUrl: redirectTo,\n        })}`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    const providerType = providers[provider].type;\n    if (providerType === \"webauthn\") {\n        // TODO: Add docs link with explanation\n        throw new TypeError([\n            `Provider id \"${provider}\" refers to a WebAuthn provider.`,\n            'Please use `import { signIn } from \"next-auth/webauthn\"` instead.',\n        ].join(\"\\n\"));\n    }\n    const signInUrl = `${baseUrl}/${providerType === \"credentials\" ? \"callback\" : \"signin\"}/${provider}`;\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${signInUrl}?${new URLSearchParams(authorizationParams)}`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\",\n        },\n        body: new URLSearchParams({\n            ...signInParams,\n            csrfToken,\n            callbackUrl: redirectTo,\n        }),\n    });\n    const data = await res.json();\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\"))\n            window.location.reload();\n        return;\n    }\n    const error = new URL(data.url).searchParams.get(\"error\") ?? undefined;\n    const code = new URL(data.url).searchParams.get(\"code\") ?? undefined;\n    if (res.ok) {\n        await __NEXTAUTH._getSession({ event: \"storage\" });\n    }\n    return {\n        error,\n        code,\n        status: res.status,\n        ok: res.ok,\n        url: error ? null : data.url,\n    };\n}\nexport async function signOut(options) {\n    const { redirect = true, redirectTo = options?.callbackUrl ?? window.location.href, } = options ?? {};\n    const baseUrl = apiBaseUrl(__NEXTAUTH);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${baseUrl}/signout`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\",\n        },\n        body: new URLSearchParams({ csrfToken, callbackUrl: redirectTo }),\n    });\n    const data = await res.json();\n    broadcast().postMessage({ event: \"session\", data: { trigger: \"signout\" } });\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\"))\n            window.location.reload();\n        return;\n    }\n    await __NEXTAUTH._getSession({ event: \"storage\" });\n    return data;\n}\n/**\n * [React Context](https://react.dev/learn/passing-data-deeply-with-context) provider to wrap the app (`pages/`) to make session data available anywhere.\n *\n * When used, the session state is automatically synchronized across all open tabs/windows and they are all updated whenever they gain or lose focus\n * or the state changes (e.g. a user signs in or out) when {@link SessionProviderProps.refetchOnWindowFocus} is `true`.\n *\n * :::info\n * `SessionProvider` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */\nexport function SessionProvider(props) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    const { children, basePath, refetchInterval, refetchWhenOffline } = props;\n    if (basePath)\n        __NEXTAUTH.basePath = basePath;\n    /**\n     * If session was `null`, there was an attempt to fetch it,\n     * but it failed, but we still treat it as a valid initial value.\n     */\n    const hasInitialSession = props.session !== undefined;\n    /** If session was passed, initialize as already synced */\n    __NEXTAUTH._lastSync = hasInitialSession ? now() : 0;\n    const [session, setSession] = React.useState(() => {\n        if (hasInitialSession)\n            __NEXTAUTH._session = props.session;\n        return props.session;\n    });\n    /** If session was passed, initialize as not loading */\n    const [loading, setLoading] = React.useState(!hasInitialSession);\n    React.useEffect(() => {\n        __NEXTAUTH._getSession = async ({ event } = {}) => {\n            try {\n                const storageEvent = event === \"storage\";\n                // We should always update if we don't have a client session yet\n                // or if there are events from other tabs/windows\n                if (storageEvent || __NEXTAUTH._session === undefined) {\n                    __NEXTAUTH._lastSync = now();\n                    __NEXTAUTH._session = await getSession({\n                        broadcast: !storageEvent,\n                    });\n                    setSession(__NEXTAUTH._session);\n                    return;\n                }\n                if (\n                // If there is no time defined for when a session should be considered\n                // stale, then it's okay to use the value we have until an event is\n                // triggered which updates it\n                !event ||\n                    // If the client doesn't have a session then we don't need to call\n                    // the server to check if it does (if they have signed in via another\n                    // tab or window that will come through as a \"stroage\" event\n                    // event anyway)\n                    __NEXTAUTH._session === null ||\n                    // Bail out early if the client session is not stale yet\n                    now() < __NEXTAUTH._lastSync) {\n                    return;\n                }\n                // An event or session staleness occurred, update the client session.\n                __NEXTAUTH._lastSync = now();\n                __NEXTAUTH._session = await getSession();\n                setSession(__NEXTAUTH._session);\n            }\n            catch (error) {\n                logger.error(new ClientSessionError(error.message, error));\n            }\n            finally {\n                setLoading(false);\n            }\n        };\n        __NEXTAUTH._getSession();\n        return () => {\n            __NEXTAUTH._lastSync = 0;\n            __NEXTAUTH._session = undefined;\n            __NEXTAUTH._getSession = () => { };\n        };\n    }, []);\n    React.useEffect(() => {\n        const handle = () => __NEXTAUTH._getSession({ event: \"storage\" });\n        // Listen for storage events and update session if event fired from\n        // another window (but suppress firing another event to avoid a loop)\n        // Fetch new session data but tell it to not to fire another event to\n        // avoid an infinite loop.\n        // Note: We could pass session data through and do something like\n        // `setData(message.data)` but that can cause problems depending\n        // on how the session object is being used in the client; it is\n        // more robust to have each window/tab fetch it's own copy of the\n        // session object rather than share it across instances.\n        broadcast().addEventListener(\"message\", handle);\n        return () => broadcast().removeEventListener(\"message\", handle);\n    }, []);\n    React.useEffect(() => {\n        const { refetchOnWindowFocus = true } = props;\n        // Listen for when the page is visible, if the user switches tabs\n        // and makes our tab visible again, re-fetch the session, but only if\n        // this feature is not disabled.\n        const visibilityHandler = () => {\n            if (refetchOnWindowFocus && document.visibilityState === \"visible\")\n                __NEXTAUTH._getSession({ event: \"visibilitychange\" });\n        };\n        document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n        return () => document.removeEventListener(\"visibilitychange\", visibilityHandler, false);\n    }, [props.refetchOnWindowFocus]);\n    const isOnline = useOnline();\n    // TODO: Flip this behavior in next major version\n    const shouldRefetch = refetchWhenOffline !== false || isOnline;\n    React.useEffect(() => {\n        if (refetchInterval && shouldRefetch) {\n            const refetchIntervalTimer = setInterval(() => {\n                if (__NEXTAUTH._session) {\n                    __NEXTAUTH._getSession({ event: \"poll\" });\n                }\n            }, refetchInterval * 1000);\n            return () => clearInterval(refetchIntervalTimer);\n        }\n    }, [refetchInterval, shouldRefetch]);\n    const value = React.useMemo(() => ({\n        data: session,\n        status: loading\n            ? \"loading\"\n            : session\n                ? \"authenticated\"\n                : \"unauthenticated\",\n        async update(data) {\n            if (loading)\n                return;\n            setLoading(true);\n            const newSession = await fetchData(\"session\", __NEXTAUTH, logger, typeof data === \"undefined\"\n                ? undefined\n                : { body: { csrfToken: await getCsrfToken(), data } });\n            setLoading(false);\n            if (newSession) {\n                setSession(newSession);\n                broadcast().postMessage({\n                    event: \"session\",\n                    data: { trigger: \"getSession\" },\n                });\n            }\n            return newSession;\n        },\n    }), [session, loading]);\n    return (\n    // @ts-expect-error\n    _jsx(SessionContext.Provider, { value: value, children: children }));\n}\n", "/**\n * Base error class for all Auth.js errors.\n * It's optimized to be printed in the server logs in a nicely formatted way\n * via the [`logger.error`](https://authjs.dev/reference/core#logger) option.\n * @noInheritDoc\n */\nexport class AuthError extends Error {\n    /** @internal */\n    constructor(message, errorOptions) {\n        if (message instanceof Error) {\n            super(undefined, {\n                cause: { err: message, ...message.cause, ...errorOptions },\n            });\n        }\n        else if (typeof message === \"string\") {\n            if (errorOptions instanceof Error) {\n                errorOptions = { err: errorOptions, ...errorOptions.cause };\n            }\n            super(message, errorOptions);\n        }\n        else {\n            super(undefined, message);\n        }\n        this.name = this.constructor.name;\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.type = this.constructor.type ?? \"AuthError\";\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.kind = this.constructor.kind ?? \"error\";\n        Error.captureStackTrace?.(this, this.constructor);\n        const url = `https://errors.authjs.dev#${this.type.toLowerCase()}`;\n        this.message += `${this.message ? \". \" : \"\"}Read more at ${url}`;\n    }\n}\n/**\n * Thrown when the user's sign-in attempt failed.\n * @noInheritDoc\n */\nexport class SignInError extends AuthError {\n}\n/** @internal */\nSignInError.kind = \"signIn\";\n/**\n * One of the database [`Adapter` methods](https://authjs.dev/reference/core/adapters#methods)\n * failed during execution.\n *\n * :::tip\n * If `debug: true` is set, you can check out `[auth][debug]` in the logs to learn more about the failed adapter method execution.\n * @example\n * ```sh\n * [auth][debug]: adapter_getUserByEmail\n * { \"args\": [undefined] }\n * ```\n * :::\n * @noInheritDoc\n */\nexport class AdapterError extends AuthError {\n}\nAdapterError.type = \"AdapterError\";\n/**\n * Thrown when the execution of the [`signIn` callback](https://authjs.dev/reference/core/types#signin) fails\n * or if it returns `false`.\n * @noInheritDoc\n */\nexport class AccessDenied extends AuthError {\n}\nAccessDenied.type = \"AccessDenied\";\n/**\n * This error occurs when the user cannot finish login.\n * Depending on the provider type, this could have happened for multiple reasons.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n *\n * For an [OAuth provider](https://authjs.dev/getting-started/authentication/oauth), possible causes are:\n * - The user denied access to the application\n * - There was an error parsing the OAuth Profile:\n *   Check out the provider's `profile` or `userinfo.request` method to make sure\n *   it correctly fetches the user's profile.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * For an [Email provider](https://authjs.dev/getting-started/authentication/email), possible causes are:\n * - The provided email/token combination was invalid/missing:\n *   Check if the provider's `sendVerificationRequest` method correctly sends the email.\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n *\n * For a [Credentials provider](https://authjs.dev/getting-started/authentication/credentials), possible causes are:\n * - The `authorize` method threw an uncaught error:\n *   Check the provider's `authorize` method.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * :::tip\n * Check out `[auth][cause]` in the error message for more details.\n * It will show the original stack trace.\n * :::\n * @noInheritDoc\n */\nexport class CallbackRouteError extends AuthError {\n}\nCallbackRouteError.type = \"CallbackRouteError\";\n/**\n * Thrown when Auth.js is misconfigured and accidentally tried to require authentication on a custom error page.\n * To prevent an infinite loop, Auth.js will instead render its default error page.\n *\n * To fix this, make sure that the `error` page does not require authentication.\n *\n * Learn more at [Guide: Error pages](https://authjs.dev/guides/pages/error)\n * @noInheritDoc\n */\nexport class ErrorPageLoop extends AuthError {\n}\nErrorPageLoop.type = \"ErrorPageLoop\";\n/**\n * One of the [`events` methods](https://authjs.dev/reference/core/types#eventcallbacks)\n * failed during execution.\n *\n * Make sure that the `events` methods are implemented correctly and uncaught errors are handled.\n *\n * Learn more at [`events`](https://authjs.dev/reference/core/types#eventcallbacks)\n * @noInheritDoc\n */\nexport class EventError extends AuthError {\n}\nEventError.type = \"EventError\";\n/**\n * Thrown when Auth.js is unable to verify a `callbackUrl` value.\n * The browser either disabled cookies or the `callbackUrl` is not a valid URL.\n *\n * Somebody might have tried to manipulate the callback URL that Auth.js uses to redirect the user back to the configured `callbackUrl`/page.\n * This could be a malicious hacker trying to redirect the user to a phishing site.\n * To prevent this, Auth.js checks if the callback URL is valid and throws this error if it is not.\n *\n * There is no action required, but it might be an indicator that somebody is trying to attack your application.\n * @noInheritDoc\n */\nexport class InvalidCallbackUrl extends AuthError {\n}\nInvalidCallbackUrl.type = \"InvalidCallbackUrl\";\n/**\n * Can be thrown from the `authorize` callback of the Credentials provider.\n * When an error occurs during the `authorize` callback, two things can happen:\n * 1. The user is redirected to the signin page, with `error=CredentialsSignin&code=credentials` in the URL. `code` is configurable.\n * 2. If you throw this error in a framework that handles form actions server-side, this error is thrown, instead of redirecting the user, so you'll need to handle.\n * @noInheritDoc\n */\nexport class CredentialsSignin extends SignInError {\n    constructor() {\n        super(...arguments);\n        /**\n         * The error code that is set in the `code` query parameter of the redirect URL.\n         *\n         *\n         * ⚠ NOTE: This property is going to be included in the URL, so make sure it does not hint at sensitive errors.\n         *\n         * The full error is always logged on the server, if you need to debug.\n         *\n         * Generally, we don't recommend hinting specifically if the user had either a wrong username or password specifically,\n         * try rather something like \"Invalid credentials\".\n         */\n        this.code = \"credentials\";\n    }\n}\nCredentialsSignin.type = \"CredentialsSignin\";\n/**\n * One of the configured OAuth or OIDC providers is missing the `authorization`, `token` or `userinfo`, or `issuer` configuration.\n * To perform OAuth or OIDC sign in, at least one of these endpoints is required.\n *\n * Learn more at [`OAuth2Config`](https://authjs.dev/reference/core/providers#oauth2configprofile) or [Guide: OAuth Provider](https://authjs.dev/guides/configuring-oauth-providers)\n * @noInheritDoc\n */\nexport class InvalidEndpoints extends AuthError {\n}\nInvalidEndpoints.type = \"InvalidEndpoints\";\n/**\n * Thrown when a PKCE, state or nonce OAuth check could not be performed.\n * This could happen if the OAuth provider is configured incorrectly or if the browser is blocking cookies.\n *\n * Learn more at [`checks`](https://authjs.dev/reference/core/providers#checks)\n * @noInheritDoc\n */\nexport class InvalidCheck extends AuthError {\n}\nInvalidCheck.type = \"InvalidCheck\";\n/**\n * Logged on the server when Auth.js could not decode or encode a JWT-based (`strategy: \"jwt\"`) session.\n *\n * Possible causes are either a misconfigured `secret` or a malformed JWT or `encode/decode` methods.\n *\n * :::note\n * When this error is logged, the session cookie is destroyed.\n * :::\n *\n * Learn more at [`secret`](https://authjs.dev/reference/core#secret), [`jwt.encode`](https://authjs.dev/reference/core/jwt#encode-1) or [`jwt.decode`](https://authjs.dev/reference/core/jwt#decode-2) for more information.\n * @noInheritDoc\n */\nexport class JWTSessionError extends AuthError {\n}\nJWTSessionError.type = \"JWTSessionError\";\n/**\n * Thrown if Auth.js is misconfigured. This could happen if you configured an Email provider but did not set up a database adapter,\n * or tried using a `strategy: \"database\"` session without a database adapter.\n * In both cases, make sure you either remove the configuration or add the missing adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database), [Email provider](https://authjs.dev/getting-started/authentication/email) or [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database-session)\n * @noInheritDoc\n */\nexport class MissingAdapter extends AuthError {\n}\nMissingAdapter.type = \"MissingAdapter\";\n/**\n * Thrown similarily to [`MissingAdapter`](https://authjs.dev/reference/core/errors#missingadapter), but only some required methods were missing.\n *\n * Make sure you either remove the configuration or add the missing methods to the adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database)\n * @noInheritDoc\n */\nexport class MissingAdapterMethods extends AuthError {\n}\nMissingAdapterMethods.type = \"MissingAdapterMethods\";\n/**\n * Thrown when a Credentials provider is missing the `authorize` configuration.\n * To perform credentials sign in, the `authorize` method is required.\n *\n * Learn more at [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n * @noInheritDoc\n */\nexport class MissingAuthorize extends AuthError {\n}\nMissingAuthorize.type = \"MissingAuthorize\";\n/**\n * Auth.js requires a secret or multiple secrets to be set, but none was not found. This is used to encrypt cookies, JWTs and other sensitive data.\n *\n * :::note\n * If you are using a framework like Next.js, we try to automatically infer the secret from the `AUTH_SECRET`, `AUTH_SECRET_1`, etc. environment variables.\n * Alternatively, you can also explicitly set the [`AuthConfig.secret`](https://authjs.dev/reference/core#secret) option.\n * :::\n *\n *\n * :::tip\n * To generate a random string, you can use the Auth.js CLI: `npx auth secret`\n * :::\n * @noInheritDoc\n */\nexport class MissingSecret extends AuthError {\n}\nMissingSecret.type = \"MissingSecret\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an OAuth account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link OAuth accounts to existing accounts if the user is not signed in.\n *\n * :::tip\n * If you trust the OAuth provider to have verified the user's email address,\n * you can enable automatic account linking by setting [`allowDangerousEmailAccountLinking: true`](https://authjs.dev/reference/core/providers#allowdangerousemailaccountlinking)\n * in the provider configuration.\n * :::\n * @noInheritDoc\n */\nexport class OAuthAccountNotLinked extends SignInError {\n}\nOAuthAccountNotLinked.type = \"OAuthAccountNotLinked\";\n/**\n * Thrown when an OAuth provider returns an error during the sign in process.\n * This could happen for example if the user denied access to the application or there was a configuration error.\n *\n * For a full list of possible reasons, check out the specification [Authorization Code Grant: Error Response](https://www.rfc-editor.org/rfc/rfc6749#section-*******)\n * @noInheritDoc\n */\nexport class OAuthCallbackError extends SignInError {\n}\nOAuthCallbackError.type = \"OAuthCallbackError\";\n/**\n * This error occurs during an OAuth sign in attempt when the provider's\n * response could not be parsed. This could for example happen if the provider's API\n * changed, or the [`OAuth2Config.profile`](https://authjs.dev/reference/core/providers#oauth2configprofile) method is not implemented correctly.\n * @noInheritDoc\n */\nexport class OAuthProfileParseError extends AuthError {\n}\nOAuthProfileParseError.type = \"OAuthProfileParseError\";\n/**\n * Logged on the server when Auth.js could not retrieve a session from the database (`strategy: \"database\"`).\n *\n * The database adapter might be misconfigured or the database is not reachable.\n *\n * Learn more at [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database)\n * @noInheritDoc\n */\nexport class SessionTokenError extends AuthError {\n}\nSessionTokenError.type = \"SessionTokenError\";\n/**\n * Happens when login by [OAuth](https://authjs.dev/getting-started/authentication/oauth) could not be started.\n *\n * Possible causes are:\n * - The Authorization Server is not compliant with the [OAuth 2.0](https://www.ietf.org/rfc/rfc6749.html) or the [OIDC](https://openid.net/specs/openid-connect-core-1_0.html) specification.\n *   Check the details in the error message.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n * @noInheritDoc\n */\nexport class OAuthSignInError extends SignInError {\n}\nOAuthSignInError.type = \"OAuthSignInError\";\n/**\n * Happens when the login by an [Email provider](https://authjs.dev/getting-started/authentication/email) could not be started.\n *\n * Possible causes are:\n * - The email sent from the client is invalid, could not be normalized by [`EmailConfig.normalizeIdentifier`](https://authjs.dev/reference/core/providers/email#normalizeidentifier)\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n * @noInheritDoc\n */\nexport class EmailSignInError extends SignInError {\n}\nEmailSignInError.type = \"EmailSignInError\";\n/**\n * Represents an error that occurs during the sign-out process. This error\n * is logged when there are issues in terminating a user's session, either\n * by failing to delete the session from the database (in database session\n * strategies) or encountering issues during other parts of the sign-out\n * process, such as emitting sign-out events or clearing session cookies.\n *\n * The session cookie(s) are emptied even if this error is logged.\n * @noInheritDoc\n */\nexport class SignOutError extends AuthError {\n}\nSignOutError.type = \"SignOutError\";\n/**\n * Auth.js was requested to handle an operation that it does not support.\n *\n * See [`AuthAction`](https://authjs.dev/reference/core/types#authaction) for the supported actions.\n * @noInheritDoc\n */\nexport class UnknownAction extends AuthError {\n}\nUnknownAction.type = \"UnknownAction\";\n/**\n * Thrown when a Credentials provider is present but the JWT strategy (`strategy: \"jwt\"`) is not enabled.\n *\n * Learn more at [`strategy`](https://authjs.dev/reference/core#strategy) or [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n * @noInheritDoc\n */\nexport class UnsupportedStrategy extends AuthError {\n}\nUnsupportedStrategy.type = \"UnsupportedStrategy\";\n/**\n * Thrown when an endpoint was incorrectly called without a provider, or with an unsupported provider.\n * @noInheritDoc\n */\nexport class InvalidProvider extends AuthError {\n}\nInvalidProvider.type = \"InvalidProvider\";\n/**\n * Thrown when the `trustHost` option was not set to `true`.\n *\n * Auth.js requires the `trustHost` option to be set to `true` since it's relying on the request headers' `host` value.\n *\n * :::note\n * Official Auth.js libraries might attempt to automatically set the `trustHost` option to `true` if the request is coming from a trusted host on a trusted platform.\n * :::\n *\n * Learn more at [`trustHost`](https://authjs.dev/reference/core#trusthost) or [Guide: Deployment](https://authjs.dev/getting-started/deployment)\n * @noInheritDoc\n */\nexport class UntrustedHost extends AuthError {\n}\nUntrustedHost.type = \"UntrustedHost\";\n/**\n * The user's email/token combination was invalid.\n * This could be because the email/token combination was not found in the database,\n * or because the token has expired. Ask the user to log in again.\n * @noInheritDoc\n */\nexport class Verification extends AuthError {\n}\nVerification.type = \"Verification\";\n/**\n * Error for missing CSRF tokens in client-side actions (`signIn`, `signOut`, `useSession#update`).\n * Thrown when actions lack the double submit cookie, essential for CSRF protection.\n *\n * CSRF ([Cross-Site Request Forgery](https://owasp.org/www-community/attacks/csrf))\n * is an attack leveraging authenticated user credentials for unauthorized actions.\n *\n * Double submit cookie pattern, a CSRF defense, requires matching values in a cookie\n * and request parameter. More on this at [MDN Web Docs](https://developer.mozilla.org/en-US/docs/Glossary/CSRF).\n * @noInheritDoc\n */\nexport class MissingCSRF extends SignInError {\n}\nMissingCSRF.type = \"MissingCSRF\";\nconst clientErrors = new Set([\n    \"CredentialsSignin\",\n    \"OAuthAccountNotLinked\",\n    \"OAuthCallbackError\",\n    \"AccessDenied\",\n    \"Verification\",\n    \"MissingCSRF\",\n    \"AccountNotLinked\",\n    \"WebAuthnVerificationError\",\n]);\n/**\n * Used to only allow sending a certain subset of errors to the client.\n * Errors are always logged on the server, but to prevent leaking sensitive information,\n * only a subset of errors are sent to the client as-is.\n * @internal\n */\nexport function isClientError(error) {\n    if (error instanceof AuthError)\n        return clientErrors.has(error.type);\n    return false;\n}\n/**\n * Thrown when multiple providers have `enableConditionalUI` set to `true`.\n * Only one provider can have this option enabled at a time.\n * @noInheritDoc\n */\nexport class DuplicateConditionalUI extends AuthError {\n}\nDuplicateConditionalUI.type = \"DuplicateConditionalUI\";\n/**\n * Thrown when a WebAuthn provider has `enableConditionalUI` set to `true` but no formField has `webauthn` in its autocomplete param.\n *\n * The `webauthn` autocomplete param is required for conditional UI to work.\n * @noInheritDoc\n */\nexport class MissingWebAuthnAutocomplete extends AuthError {\n}\nMissingWebAuthnAutocomplete.type = \"MissingWebAuthnAutocomplete\";\n/**\n * Thrown when a WebAuthn provider fails to verify a client response.\n * @noInheritDoc\n */\nexport class WebAuthnVerificationError extends AuthError {\n}\nWebAuthnVerificationError.type = \"WebAuthnVerificationError\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link accounts to existing accounts if the user is not signed in.\n * @noInheritDoc\n */\nexport class AccountNotLinked extends SignInError {\n}\nAccountNotLinked.type = \"AccountNotLinked\";\n/**\n * Thrown when an experimental feature is used but not enabled.\n * @noInheritDoc\n */\nexport class ExperimentalFeatureNotEnabled extends AuthError {\n}\nExperimentalFeatureNotEnabled.type = \"ExperimentalFeatureNotEnabled\";\n", "\"use client\";\nimport * as React from \"react\";\nimport { AuthError } from \"@auth/core/errors\";\n/** @todo */\nclass ClientFetchError extends AuthError {\n}\n/** @todo */\nexport class ClientSessionError extends AuthError {\n}\n// ------------------------ Internal ------------------------\n/**\n * If passed 'appContext' via getInitialProps() in _app.js\n * then get the req object from ctx and use that for the\n * req value to allow `fetchData` to\n * work seemlessly in getInitialProps() on server side\n * pages *and* in _app.js.\n * @internal\n */\nexport async function fetchData(path, __NEXTAUTH, logger, req = {}) {\n    const url = `${apiBaseUrl(__NEXTAUTH)}/${path}`;\n    try {\n        const options = {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...(req?.headers?.cookie ? { cookie: req.headers.cookie } : {}),\n            },\n        };\n        if (req?.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n        }\n        const res = await fetch(url, options);\n        const data = await res.json();\n        if (!res.ok)\n            throw data;\n        return data;\n    }\n    catch (error) {\n        logger.error(new ClientFetchError(error.message, error));\n        return null;\n    }\n}\n/** @internal */\nexport function apiBaseUrl(__NEXTAUTH) {\n    if (typeof window === \"undefined\") {\n        // Return absolute path when called server side\n        return `${__NEXTAUTH.baseUrlServer}${__NEXTAUTH.basePathServer}`;\n    }\n    // Return relative path when called client side\n    return __NEXTAUTH.basePath;\n}\n/** @internal  */\nexport function useOnline() {\n    const [isOnline, setIsOnline] = React.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false);\n    const setOnline = () => setIsOnline(true);\n    const setOffline = () => setIsOnline(false);\n    React.useEffect(() => {\n        window.addEventListener(\"online\", setOnline);\n        window.addEventListener(\"offline\", setOffline);\n        return () => {\n            window.removeEventListener(\"online\", setOnline);\n            window.removeEventListener(\"offline\", setOffline);\n        };\n    }, []);\n    return isOnline;\n}\n/**\n * Returns the number of seconds elapsed since January 1, 1970 00:00:00 UTC.\n * @internal\n */\nexport function now() {\n    return Math.floor(Date.now() / 1000);\n}\n/**\n * Returns an `URL` like object to make requests/redirects from server-side\n * @internal\n */\nexport function parseUrl(url) {\n    const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n    if (url && !url.startsWith(\"http\")) {\n        url = `https://${url}`;\n    }\n    const _url = new URL(url || defaultUrl);\n    const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname)\n        // Remove trailing slash\n        .replace(/\\/$/, \"\");\n    const base = `${_url.origin}${path}`;\n    return {\n        origin: _url.origin,\n        host: _url.host,\n        path,\n        base,\n        toString: () => base,\n    };\n}\n", "// src/queryClient.ts\nimport {\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  noop,\n  partialMatchKey,\n  resolveStaleTime,\n  skipToken\n} from \"./utils.js\";\nimport { QueryCache } from \"./queryCache.js\";\nimport { MutationCache } from \"./mutationCache.js\";\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { infiniteQueryBehavior } from \"./infiniteQueryBehavior.js\";\nvar QueryClient = class {\n  #queryCache;\n  #mutationCache;\n  #defaultOptions;\n  #queryDefaults;\n  #mutationDefaults;\n  #mountCount;\n  #unsubscribeFocus;\n  #unsubscribeOnline;\n  constructor(config = {}) {\n    this.#queryCache = config.queryCache || new QueryCache();\n    this.#mutationCache = config.mutationCache || new MutationCache();\n    this.#defaultOptions = config.defaultOptions || {};\n    this.#queryDefaults = /* @__PURE__ */ new Map();\n    this.#mutationDefaults = /* @__PURE__ */ new Map();\n    this.#mountCount = 0;\n  }\n  mount() {\n    this.#mountCount++;\n    if (this.#mountCount !== 1) return;\n    this.#unsubscribeFocus = focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations();\n        this.#queryCache.onFocus();\n      }\n    });\n    this.#unsubscribeOnline = onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations();\n        this.#queryCache.onOnline();\n      }\n    });\n  }\n  unmount() {\n    this.#mountCount--;\n    if (this.#mountCount !== 0) return;\n    this.#unsubscribeFocus?.();\n    this.#unsubscribeFocus = void 0;\n    this.#unsubscribeOnline?.();\n    this.#unsubscribeOnline = void 0;\n  }\n  isFetching(filters) {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: \"fetching\" }).length;\n  }\n  isMutating(filters) {\n    return this.#mutationCache.findAll({ ...filters, status: \"pending\" }).length;\n  }\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state.data;\n  }\n  ensureQueryData(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    const query = this.#queryCache.build(this, defaultedOptions);\n    const cachedData = query.state.data;\n    if (cachedData === void 0) {\n      return this.fetchQuery(options);\n    }\n    if (options.revalidateIfStale && query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query))) {\n      void this.prefetchQuery(defaultedOptions);\n    }\n    return Promise.resolve(cachedData);\n  }\n  getQueriesData(filters) {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n  setQueryData(queryKey, updater, options) {\n    const defaultedOptions = this.defaultQueryOptions({ queryKey });\n    const query = this.#queryCache.get(\n      defaultedOptions.queryHash\n    );\n    const prevData = query?.state.data;\n    const data = functionalUpdate(updater, prevData);\n    if (data === void 0) {\n      return void 0;\n    }\n    return this.#queryCache.build(this, defaultedOptions).setData(data, { ...options, manual: true });\n  }\n  setQueriesData(filters, updater, options) {\n    return notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map(({ queryKey }) => [\n        queryKey,\n        this.setQueryData(queryKey, updater, options)\n      ])\n    );\n  }\n  getQueryState(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(\n      options.queryHash\n    )?.state;\n  }\n  removeQueries(filters) {\n    const queryCache = this.#queryCache;\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query);\n      });\n    });\n  }\n  resetQueries(filters, options) {\n    const queryCache = this.#queryCache;\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset();\n      });\n      return this.refetchQueries(\n        {\n          type: \"active\",\n          ...filters\n        },\n        options\n      );\n    });\n  }\n  cancelQueries(filters, cancelOptions = {}) {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions };\n    const promises = notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map((query) => query.cancel(defaultedCancelOptions))\n    );\n    return Promise.all(promises).then(noop).catch(noop);\n  }\n  invalidateQueries(filters, options = {}) {\n    return notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate();\n      });\n      if (filters?.refetchType === \"none\") {\n        return Promise.resolve();\n      }\n      return this.refetchQueries(\n        {\n          ...filters,\n          type: filters?.refetchType ?? filters?.type ?? \"active\"\n        },\n        options\n      );\n    });\n  }\n  refetchQueries(filters, options = {}) {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true\n    };\n    const promises = notifyManager.batch(\n      () => this.#queryCache.findAll(filters).filter((query) => !query.isDisabled() && !query.isStatic()).map((query) => {\n        let promise = query.fetch(void 0, fetchOptions);\n        if (!fetchOptions.throwOnError) {\n          promise = promise.catch(noop);\n        }\n        return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n      })\n    );\n    return Promise.all(promises).then(noop);\n  }\n  fetchQuery(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    if (defaultedOptions.retry === void 0) {\n      defaultedOptions.retry = false;\n    }\n    const query = this.#queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(\n      resolveStaleTime(defaultedOptions.staleTime, query)\n    ) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n  prefetchQuery(options) {\n    return this.fetchQuery(options).then(noop).catch(noop);\n  }\n  fetchInfiniteQuery(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.fetchQuery(options);\n  }\n  prefetchInfiniteQuery(options) {\n    return this.fetchInfiniteQuery(options).then(noop).catch(noop);\n  }\n  ensureInfiniteQueryData(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.ensureQueryData(options);\n  }\n  resumePausedMutations() {\n    if (onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations();\n    }\n    return Promise.resolve();\n  }\n  getQueryCache() {\n    return this.#queryCache;\n  }\n  getMutationCache() {\n    return this.#mutationCache;\n  }\n  getDefaultOptions() {\n    return this.#defaultOptions;\n  }\n  setDefaultOptions(options) {\n    this.#defaultOptions = options;\n  }\n  setQueryDefaults(queryKey, options) {\n    this.#queryDefaults.set(hashKey(queryKey), {\n      queryKey,\n      defaultOptions: options\n    });\n  }\n  getQueryDefaults(queryKey) {\n    const defaults = [...this.#queryDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  setMutationDefaults(mutationKey, options) {\n    this.#mutationDefaults.set(hashKey(mutationKey), {\n      mutationKey,\n      defaultOptions: options\n    });\n  }\n  getMutationDefaults(mutationKey) {\n    const defaults = [...this.#mutationDefaults.values()];\n    const result = {};\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  defaultQueryOptions(options) {\n    if (options._defaulted) {\n      return options;\n    }\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions\n      );\n    }\n    if (defaultedOptions.refetchOnReconnect === void 0) {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n    }\n    if (defaultedOptions.throwOnError === void 0) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n    }\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = \"offlineFirst\";\n    }\n    if (defaultedOptions.queryFn === skipToken) {\n      defaultedOptions.enabled = false;\n    }\n    return defaultedOptions;\n  }\n  defaultMutationOptions(options) {\n    if (options?._defaulted) {\n      return options;\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n  clear() {\n    this.#queryCache.clear();\n    this.#mutationCache.clear();\n  }\n};\nexport {\n  QueryClient\n};\n//# sourceMappingURL=queryClient.js.map", "\"use client\";\r\n\r\nimport { SessionProvider } from \"next-auth/react\";\r\nimport { QueryClientProvider } from \"@tanstack/react-query\";\r\nimport { ReactQueryDevtools } from \"@tanstack/react-query-devtools\";\r\nimport { type PropsWithChildren, useState } from \"react\";\r\nimport { getQueryClient } from \"@/lib/query-client\";\r\n\r\nfunction Providers({ children }: PropsWithChildren) {\r\n\t// NOTE: Avoid useState when initializing the query client if you don't\r\n\t// have a suspense boundary between this and the code that may\r\n\t// suspend because <PERSON><PERSON> will throw away the client on the initial\r\n\t// render if it suspends and there is no boundary\r\n\tconst [queryClient] = useState(() => getQueryClient());\r\n\r\n\treturn (\r\n\t\t<QueryClientProvider client={queryClient}>\r\n\t\t\t<SessionProvider>\r\n\t\t\t\t{children}\r\n\t\t\t\t{/* React Query DevTools - only shows in development */}\r\n\t\t\t\t<ReactQueryDevtools\r\n\t\t\t\t\tinitialIsOpen={false}\r\n\t\t\t\t\tbuttonPosition=\"bottom-right\"\r\n\t\t\t\t/>\r\n\t\t\t</SessionProvider>\r\n\t\t</QueryClientProvider>\r\n\t);\r\n}\r\n\r\nexport default Providers;\r\n", "// src/queryCache.ts\nimport { hashQueryKeyByOptions, matchQuery } from \"./utils.js\";\nimport { Query } from \"./query.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar QueryCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#queries = /* @__PURE__ */ new Map();\n  }\n  #queries;\n  build(client, options, state) {\n    const queryKey = options.queryKey;\n    const queryHash = options.queryHash ?? hashQueryKeyByOptions(queryKey, options);\n    let query = this.get(queryHash);\n    if (!query) {\n      query = new Query({\n        client,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n    return query;\n  }\n  add(query) {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query);\n      this.notify({\n        type: \"added\",\n        query\n      });\n    }\n  }\n  remove(query) {\n    const queryInMap = this.#queries.get(query.queryHash);\n    if (queryInMap) {\n      query.destroy();\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash);\n      }\n      this.notify({ type: \"removed\", query });\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query);\n      });\n    });\n  }\n  get(queryHash) {\n    return this.#queries.get(queryHash);\n  }\n  getAll() {\n    return [...this.#queries.values()];\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (query) => matchQuery(defaultedFilters, query)\n    );\n  }\n  findAll(filters = {}) {\n    const queries = this.getAll();\n    return Object.keys(filters).length > 0 ? queries.filter((query) => matchQuery(filters, query)) : queries;\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  onFocus() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus();\n      });\n    });\n  }\n  onOnline() {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline();\n      });\n    });\n  }\n};\nexport {\n  QueryCache\n};\n//# sourceMappingURL=queryCache.js.map", "// src/mutationCache.ts\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Mutation } from \"./mutation.js\";\nimport { matchMutation, noop } from \"./utils.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar MutationCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#mutations = /* @__PURE__ */ new Set();\n    this.#scopes = /* @__PURE__ */ new Map();\n    this.#mutationId = 0;\n  }\n  #mutations;\n  #scopes;\n  #mutationId;\n  build(client, options, state) {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state\n    });\n    this.add(mutation);\n    return mutation;\n  }\n  add(mutation) {\n    this.#mutations.add(mutation);\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const scopedMutations = this.#scopes.get(scope);\n      if (scopedMutations) {\n        scopedMutations.push(mutation);\n      } else {\n        this.#scopes.set(scope, [mutation]);\n      }\n    }\n    this.notify({ type: \"added\", mutation });\n  }\n  remove(mutation) {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation);\n      if (typeof scope === \"string\") {\n        const scopedMutations = this.#scopes.get(scope);\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation);\n            if (index !== -1) {\n              scopedMutations.splice(index, 1);\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope);\n          }\n        }\n      }\n    }\n    this.notify({ type: \"removed\", mutation });\n  }\n  canRun(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const mutationsWithSameScope = this.#scopes.get(scope);\n      const firstPendingMutation = mutationsWithSameScope?.find(\n        (m) => m.state.status === \"pending\"\n      );\n      return !firstPendingMutation || firstPendingMutation === mutation;\n    } else {\n      return true;\n    }\n  }\n  runNext(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const foundMutation = this.#scopes.get(scope)?.find((m) => m !== mutation && m.state.isPaused);\n      return foundMutation?.continue() ?? Promise.resolve();\n    } else {\n      return Promise.resolve();\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.#mutations.forEach((mutation) => {\n        this.notify({ type: \"removed\", mutation });\n      });\n      this.#mutations.clear();\n      this.#scopes.clear();\n    });\n  }\n  getAll() {\n    return Array.from(this.#mutations);\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (mutation) => matchMutation(defaultedFilters, mutation)\n    );\n  }\n  findAll(filters = {}) {\n    return this.getAll().filter((mutation) => matchMutation(filters, mutation));\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  resumePausedMutations() {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused);\n    return notifyManager.batch(\n      () => Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(noop))\n      )\n    );\n  }\n};\nfunction scopeFor(mutation) {\n  return mutation.options.scope?.id;\n}\nexport {\n  MutationCache\n};\n//# sourceMappingURL=mutationCache.js.map", "// src/infiniteQueryBehavior.ts\nimport { addToEnd, addToStart, ensureQueryFn } from \"./utils.js\";\nfunction infiniteQueryBehavior(pages) {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options;\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n      const oldPages = context.state.data?.pages || [];\n      const oldPageParams = context.state.data?.pageParams || [];\n      let result = { pages: [], pageParams: [] };\n      let currentPage = 0;\n      const fetchFn = async () => {\n        let cancelled = false;\n        const addSignalProperty = (object) => {\n          Object.defineProperty(object, \"signal\", {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true;\n              } else {\n                context.signal.addEventListener(\"abort\", () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        };\n        const queryFn = ensureQueryFn(context.options, context.fetchOptions);\n        const fetchPage = async (data, param, previous) => {\n          if (cancelled) {\n            return Promise.reject();\n          }\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data);\n          }\n          const createQueryFnContext = () => {\n            const queryFnContext2 = {\n              client: context.client,\n              queryKey: context.queryKey,\n              pageParam: param,\n              direction: previous ? \"backward\" : \"forward\",\n              meta: context.options.meta\n            };\n            addSignalProperty(queryFnContext2);\n            return queryFnContext2;\n          };\n          const queryFnContext = createQueryFnContext();\n          const page = await queryFn(queryFnContext);\n          const { maxPages } = context.options;\n          const addTo = previous ? addToStart : addToEnd;\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          };\n        };\n        if (direction && oldPages.length) {\n          const previous = direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams\n          };\n          const param = pageParamFn(options, oldData);\n          result = await fetchPage(oldData, param, previous);\n        } else {\n          const remainingPages = pages ?? oldPages.length;\n          do {\n            const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n            if (currentPage > 0 && param == null) {\n              break;\n            }\n            result = await fetchPage(result, param);\n            currentPage++;\n          } while (currentPage < remainingPages);\n        }\n        return result;\n      };\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn,\n            {\n              client: context.client,\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal\n            },\n            query\n          );\n        };\n      } else {\n        context.fetchFn = fetchFn;\n      }\n    }\n  };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n  const lastIndex = pages.length - 1;\n  return pages.length > 0 ? options.getNextPageParam(\n    pages[lastIndex],\n    pages,\n    pageParams[lastIndex],\n    pageParams\n  ) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n  return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n  if (!data) return false;\n  return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n  if (!data || !options.getPreviousPageParam) return false;\n  return getPreviousPageParam(options, data) != null;\n}\nexport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior\n};\n//# sourceMappingURL=infiniteQueryBehavior.js.map", "import { QueryClient } from '@tanstack/react-query';\n\n// Create a function that returns a new QueryClient instance\n// This ensures we get a fresh instance for each request in SSR\nexport function makeQueryClient() {\n  return new QueryClient({\n    defaultOptions: {\n      queries: {\n        // With SSR, we usually want to set some default staleTime\n        // above 0 to avoid refetching immediately on the client\n        staleTime: 60 * 1000, // 1 minute\n        gcTime: 5 * 60 * 1000, // 5 minutes (formerly cacheTime)\n        retry: (failureCount, error) => {\n          // Don't retry on 4xx errors (client errors)\n          if (error instanceof Error && 'status' in error) {\n            const status = (error as any).status;\n            if (status >= 400 && status < 500) {\n              return false;\n            }\n          }\n          // Retry up to 3 times for other errors\n          return failureCount < 3;\n        },\n        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),\n        refetchOnWindowFocus: false, // Disable refetch on window focus for better UX\n        refetchOnReconnect: true, // Refetch when connection is restored\n      },\n      mutations: {\n        retry: false, // Don't retry mutations by default\n        onError: (error) => {\n          console.error('Mutation error:', error);\n        },\n      },\n    },\n  });\n}\n\n// Global QueryClient instance for client-side\nlet browserQueryClient: QueryClient | undefined = undefined;\n\nexport function getQueryClient() {\n  if (typeof window === 'undefined') {\n    // Server: always make a new query client\n    return makeQueryClient();\n  } else {\n    // Browser: make a new query client if we don't already have one\n    // This is very important, so we don't re-make a new client if React\n    // suspends during the initial render. This may not be needed if we\n    // have a suspense boundary BELOW the creation of the query client\n    if (!browserQueryClient) browserQueryClient = makeQueryClient();\n    return browserQueryClient;\n  }\n}\n", "\"use client\";\n\n// src/index.ts\nimport * as Devtools from \"./ReactQueryDevtools.js\";\nimport * as DevtoolsPanel from \"./ReactQueryDevtoolsPanel.js\";\nvar ReactQueryDevtools2 = process.env.NODE_ENV !== \"development\" ? function() {\n  return null;\n} : Devtools.ReactQueryDevtools;\nvar ReactQueryDevtoolsPanel2 = process.env.NODE_ENV !== \"development\" ? function() {\n  return null;\n} : DevtoolsPanel.ReactQueryDevtoolsPanel;\nexport {\n  ReactQueryDevtools2 as ReactQueryDevtools,\n  ReactQueryDevtoolsPanel2 as ReactQueryDevtoolsPanel\n};\n//# sourceMappingURL=index.js.map"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "vendored", "ReactJsxRuntime", "React"], "mappings": "0NA0BQG,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,iCC1BjCF,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEC,eAAe,4BCFxCP,GAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEE,KAAK,+NCU9B,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,0zBCPO,OAAM,UAAkB,MAE3B,YAAY,CAAO,CAAE,CAAY,CAAE,CAC3B,aAAmB,MACnB,CAD0B,IACrB,MAAC,EAAW,CACb,MAAO,CAAE,IAAK,EAAS,GAAG,EAAQ,KAAK,CAAE,GAAG,CAAY,AAAC,CAC7D,GAEwB,UAAnB,AAA6B,OAAtB,GACR,aAAwB,OAAO,CAC/B,EAAe,CAAE,IAAK,EAAc,GAAG,EAAa,KAAK,CAAC,EAE9D,KAAK,CAAC,EAAS,IAGf,KAAK,MAAC,EAAW,GAErB,IAAI,CAAC,IAAI,CAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAEjC,IAAI,CAAC,IAAI,CAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAI,YAErC,IAAI,CAAC,IAAI,CAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAI,QACrC,MAAM,iBAAiB,GAAG,IAAI,CAAE,IAAI,CAAC,WAAW,EAChD,IAAM,EAAM,CAAC,0BAA0B,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAA,CAAI,CAClE,IAAI,CAAC,OAAO,EAAI,CAAA,EAAG,IAAI,CAAC,OAAO,CAAG,KAAO,GAAG,aAAa,EAAE,EAAA,CAAK,AACpE,CACJ,CAKO,MAAM,UAAoB,EACjC,CAEA,EAAY,IAAI,CAAG,QAeZ,OAAM,UAAqB,EAClC,CACA,EAAa,IAAI,CAAG,cAMb,OAAM,UAAqB,EAClC,CACA,EAAa,IAAI,CAAG,cAyCb,OAAM,UAA2B,EACxC,CACA,EAAmB,IAAI,CAAG,oBAUnB,OAAM,UAAsB,EACnC,CACA,EAAc,IAAI,CAAG,eAUd,OAAM,UAAmB,EAChC,CACA,EAAW,IAAI,CAAG,YAYX,OAAM,UAA2B,EACxC,CACA,EAAmB,IAAI,CAAG,oBAQnB,OAAM,UAA0B,EACnC,aAAc,CACV,KAAK,IAAI,WAYT,IAAI,CAAC,IAAI,CAAG,aAChB,CACJ,CACA,EAAkB,IAAI,CAAG,mBAQlB,OAAM,UAAyB,EACtC,CACA,EAAiB,IAAI,CAAG,kBAQjB,OAAM,UAAqB,EAClC,CACA,EAAa,IAAI,CAAG,cAab,OAAM,UAAwB,EACrC,CACA,EAAgB,IAAI,CAAG,iBAShB,OAAM,UAAuB,EACpC,CACA,EAAe,IAAI,CAAG,gBASf,OAAM,UAA8B,EAC3C,CACA,EAAsB,IAAI,CAAG,uBAQtB,OAAM,UAAyB,EACtC,CACA,EAAiB,IAAI,CAAG,kBAejB,OAAM,UAAsB,EACnC,CACA,EAAc,IAAI,CAAG,eAcd,OAAM,UAA8B,EAC3C,CACA,EAAsB,IAAI,CAAG,uBAQtB,OAAM,UAA2B,EACxC,CACA,EAAmB,IAAI,CAAG,oBAOnB,OAAM,UAA+B,EAC5C,CACA,EAAuB,IAAI,CAAG,wBASvB,OAAM,UAA0B,EACvC,CACA,EAAkB,IAAI,CAAG,mBA4ClB,OAAM,UAAqB,EAClC,CACA,EAAa,IAAI,CAAG,cAOb,OAAM,UAAsB,EACnC,CACA,EAAc,IAAI,CAAG,eAOd,OAAM,UAA4B,EACzC,CACA,EAAoB,IAAI,CAAG,qBAKpB,OAAM,UAAwB,EACrC,CACA,EAAgB,IAAI,CAAG,iBAahB,OAAM,UAAsB,EACnC,CACA,EAAc,IAAI,CAAG,eAOd,OAAM,UAAqB,EAClC,CACA,EAAa,IAAI,CAAG,cAYb,OAAM,UAAoB,EACjC,CACA,EAAY,IAAI,CAAG,cACnB,IAAM,EAAe,IAAI,IAAI,CACzB,oBACA,wBACA,qBACA,eACA,eACA,cACA,mBACA,4BACH,EAOM,SAAS,EAAc,CAAK,SAC/B,AAAI,aAAiB,GACV,EAAa,GAAG,CAAC,EAAM,AAA9B,IAAkC,CAE1C,CAMO,MAAM,UAA+B,EAC5C,CACA,EAAuB,IAAI,CAAG,wBAOvB,OAAM,UAAoC,EACjD,CACA,EAA4B,IAAI,CAAG,6BAK5B,OAAM,UAAkC,EAC/C,CACA,EAA0B,IAAI,CAAG,2BAQ1B,OAAM,UAAyB,EACtC,CACA,EAAiB,IAAI,CAAG,kBAKjB,OAAM,UAAsC,EACnD,CACA,EAA8B,IAAI,CAAG,+BCpdrC,OAAM,UAAyB,EAC/B,CAEO,MAAM,UAA2B,EACxC,CAUO,eAAe,EAAU,CAAI,CAAE,CAAU,CAAE,CAAM,CAAE,EAAM,CAAC,CAAC,EAC9D,IAAM,EAAM,CAAA,EAAG,EAAW,GAAY,CAAC,EAAE,EAAA,CAAM,CAC/C,GAAI,CACA,IAAM,EAAU,CACZ,QAAS,CACL,eAAgB,mBAChB,GAAI,GAAK,SAAS,OAAS,CAAE,OAAQ,EAAI,OAAO,CAAC,MAAM,AAAC,EAAI,CAAC,CAAC,AAClE,CACJ,EACI,GAAK,MAAM,CACX,EAAQ,IAAI,CAAG,KAAK,SAAS,CAAC,EAAI,IAAI,EACtC,EAAQ,MAAM,CAAG,QAErB,IAAM,EAAM,MAAM,MAAM,EAAK,GACvB,EAAO,MAAM,EAAI,IAAI,GAC3B,GAAI,CAAC,EAAI,EAAE,CACP,MAAM,EACV,OAAO,CACX,CACA,MAAO,EAAO,CAEV,OADA,EAAO,KAAK,CAAC,IAAI,EAAiB,EAAM,OAAO,CAAE,IAC1C,IACX,CACJ,CAEO,SAAS,EAAW,CAAU,EAG7B,MAAO,CAAA,EAAG,EAAW,aAAa,CAAA,EAAG,EAAW,cAAc,CAAA,CAAE,AAIxE,CAoBO,SAAS,IACZ,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,GAAK,IACnC,CAKO,SAAS,EAAS,CAAG,EACxB,IAAM,EAAa,IAAI,IAAI,kCACvB,GAAO,CAAC,EAAI,UAAU,CAAC,SAAS,CAChC,EAAM,CAAC,QAAQ,EAAE,EAAA,CAAK,AAAL,EAErB,IAAM,EAAO,IAAI,IAAI,GAAO,GACtB,EAAO,CAAmB,MAAlB,EAAK,QAAQ,CAAW,EAAW,QAAQ,CAAG,EAAK,QAAA,AAAQ,CACrE,CACC,OAAO,CAAC,MAAO,IACd,EAAO,CAAA,EAAG,AAFY,EAEP,MAAM,CAAA,EAAG,EAAA,CAAM,CACpC,MAAO,CACH,OAAQ,EAAK,MAAM,CACnB,KAAM,EAAK,IAAI,MACf,OACA,EACA,SAAU,IAAM,CACpB,CACJ,CFzEO,IAAM,EAAa,CACtB,QAAS,EAAS,QAAQ,GAAG,CAAC,YAAY,EAAI,QAAQ,GAAG,CAAC,UAAU,EAAE,MAAM,CAC5E,SAAU,EAAS,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI,CACjD,cAAe,EAAS,QAAQ,GAAG,CAAC,qBAAqB,EACrD,QAAQ,GAAG,CAAC,YAAY,EACxB,QAAQ,GAAG,CAAC,UAAU,EAAE,MAAM,CAClC,eAAgB,EAAS,QAAQ,GAAG,CAAC,qBAAqB,EAAI,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI,CAC5F,UAAW,EACX,cAAU,EACV,YAAa,KAAQ,CACzB,EAEI,EAAmB,KACvB,SAAS,UACL,AAAgC,aAA5B,AAAyC,OAAlC,iBACA,CACH,YAAa,KAAQ,EACrB,iBAAkB,KAAQ,EAC1B,oBAAqB,KAAQ,EAC7B,KAAM,YACN,UAAW,KACX,eAAgB,KAChB,MAAO,KAAQ,EACf,cAAe,KAAM,CACzB,EAEG,IAAI,iBAAiB,YAChC,CACA,SAAS,IAIL,OAHyB,MAAM,CAA3B,IACA,EAAmB,GAAA,EAEhB,CACX,CAEA,IAAM,EAAS,CACX,MAAO,QAAQ,KAAK,CACpB,MAAO,QAAQ,KAAK,CACpB,KAAM,QAAQ,IAAI,AACtB,EACa,EAAiB,EAAA,aAAmB,QAAG,GAQ7C,SAAS,EAAW,CAAO,EAC9B,GAAI,CAAC,EACD,MAAM,AAAI,MAAM,EADC,mDAIrB,IAAM,EAAQ,EAAA,UAAgB,CAAC,GAIzB,CAAE,UAAQ,mBAAE,CAAiB,CAAE,CAAG,GAAW,CAAC,EAC9C,EAAwB,GAA6B,oBAAjB,EAAM,MAAM,OAatD,CAZA,EAAA,SAAe,CAAC,KACZ,GAAI,EAAuB,CACvB,IAAM,EAAM,CAAA,EAAG,EAAW,QAAQ,CAAC,QAAQ,EAAE,IAAI,gBAAgB,CAC7D,MAAO,kBACP,YAAa,OAAO,QAAQ,CAAC,IAAI,AACrC,GAAA,CAAI,CACA,EACA,IAEA,OAAO,QAAQ,CAAC,IAAI,CAAG,CAC/B,CACJ,EAAG,CAAC,EAAuB,EAAkB,EACzC,GACO,CACH,KAAM,EAAM,IAAI,CAChB,OAHmB,AAGX,EAAM,MAAM,CACpB,OAAQ,SACZ,EAEG,CACX,CACO,eAAe,EAAW,CAAM,EACnC,IAAM,EAAU,MAAM,EAAU,UAAW,EAAY,EAAQ,GAQ/D,OAPI,GAAQ,YAAa,CAAA,GAAM,AAE3B,IAAyB,WAAW,CAAC,CACjC,MAAO,UACP,KAAM,CAAE,QAAS,YAAa,CAClC,GAEG,CACX,CAOO,eAAe,IAClB,IAAM,EAAW,MAAM,EAAU,OAAQ,EAAY,GACrD,OAAO,GAAU,WAAa,EAClC,CACO,eAAe,IAClB,OAAO,EAAU,YAAa,EAAY,EAC9C,CACO,eAAe,EAAO,CAAQ,CAAE,CAAO,CAAE,CAAmB,EAC/D,GAAM,aAAE,CAAW,CAAE,GAAG,EAAM,CAAG,GAAW,CAAC,EACvC,CAAE,YAAW,CAAI,YAAE,EAAa,GAAe,OAAO,QAAQ,CAAC,IAAI,CAAE,GAAG,EAAc,CAAG,EACzF,EAAU,EAAW,GACrB,EAAY,MAAM,IACxB,GAAI,CAAC,EAAW,CACZ,IAAM,EAAM,CAAA,EAAG,EAAQ,MAAM,CAAC,CAC9B,OAAO,QAAQ,CAAC,IAAI,CAAG,EACvB,MACJ,CACA,CAFY,EAER,CAAC,GAAY,CAAC,CAAS,CAAC,EAAS,CAAE,CACnC,IAAM,EAAM,CAAA,EAAG,EAAQ,QAAQ,EAAE,IAAI,IAHa,YAGG,CACjD,YAAa,CACjB,GAAA,CAAI,CACJ,OAAO,QAAQ,CAAC,IAAI,CAAG,EACvB,MACJ,CACA,CAFY,GAEN,EAAe,CAAS,CAAC,EAAS,CAAC,IAAI,CAC7C,GAAqB,YAAY,CAA7B,EAEA,MAAM,AAAI,GALwC,OAK9B,gBACA,SAAS,gCAAgC,CAAC;oEAE7D,CAAC,CAEN,GAFU,CAAC,AAEL,EAAY,CAAA,EAAG,EAAQ,CAAC,EAAmB,gBAAjB,EAAiC,WAAa,SAAS,CAAC,EAAE,EAAA,CAAU,CAC9F,EAAY,MAAM,IAClB,EAAM,MAAM,MAAM,CAAA,EAAG,EAAU,CAAC,EAAE,IAAI,gBAAgB,GAAA,CAAsB,CAAE,CAChF,OAAQ,OACR,QAAS,CACL,eAAgB,oCAChB,yBAA0B,GAC9B,EACA,KAAM,IAAI,gBAAgB,CACtB,GAAG,CAAY,WACf,EACA,YAAa,CACjB,EACJ,GACM,EAAO,MAAM,EAAI,IAAI,GAC3B,GAAI,EAAU,CACV,IAAM,EAAM,EAAK,GAAG,EAAI,EACxB,OAAO,QAAQ,CAAC,IAAI,CAAG,EAEnB,EAAI,QAAQ,CAAC,MACb,OAAO,QAAQ,CAAC,MAAM,GAC1B,MACJ,CACA,IAAM,EAAQ,IAAI,IAAI,EAAK,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,eAAY,EACvD,EAAO,IAAI,IAAI,EAAK,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,cAAW,EAI3D,OAHI,EAAI,EAAE,EAAE,AACR,MAAM,EAAW,WAAW,CAAC,CAAE,MAAO,SAAU,GAE7C,OACH,OACA,EACA,OAAQ,EAAI,MAAM,CAClB,GAAI,EAAI,EAAE,CACV,IAAK,EAAQ,KAAO,EAAK,GAAG,AAChC,CACJ,CACO,eAAe,GAAQ,CAAO,EACjC,GAAM,UAAE,GAAW,CAAI,YAAE,EAAa,GAAS,aAAe,OAAO,QAAQ,CAAC,IAAI,CAAG,CAAG,GAAW,CAAC,EAC9F,EAAU,EAAW,GACrB,EAAY,MAAM,IAClB,EAAM,MAAM,MAAM,CAAA,EAAG,EAAQ,QAAQ,CAAC,CAAE,CAC1C,OAAQ,OACR,QAAS,CACL,eAAgB,oCAChB,yBAA0B,GAC9B,EACA,KAAM,IAAI,gBAAgB,CAAE,YAAW,YAAa,CAAW,EACnE,GACM,EAAO,MAAM,EAAI,IAAI,GAE3B,GADA,IAAY,WAAW,CAAC,CAAE,MAAO,UAAW,KAAM,CAAE,QAAS,SAAU,CAAE,GACrE,EAAU,CACV,IAAM,EAAM,EAAK,GAAG,EAAI,EACxB,OAAO,QAAQ,CAAC,IAAI,CAAG,EAEnB,EAAI,QAAQ,CAAC,MACb,OAAO,QAAQ,CAAC,MAAM,GAC1B,MACJ,CAEA,OADA,MAAM,EAAW,WAAW,CAAC,CAAE,MAAO,SAAU,GACzC,CACX,CAWO,SAAS,GAAgB,CAAK,EACjC,GAAI,CAAC,EACD,MAAM,AAAI,MAAM,EADC,mDAGrB,GAAM,UAAE,CAAQ,CAAE,UAAQ,iBAAE,CAAe,oBAAE,CAAkB,CAAE,CAAG,EAChE,IACA,EAAW,QAAQ,CAAG,CAAA,EAK1B,IAAM,OAAsC,IAAlB,EAAM,OAAO,CAEvC,EAAW,SAAS,CAAG,EAAoB,IAAQ,EACnD,GAAM,CAAC,EAAS,EAAW,CAAG,EAAA,QAAc,CAAC,KACrC,GACA,GAAW,QAAQ,CAAG,EAAM,OAAA,AAAO,EAChC,EAAM,OAAO,GAGlB,CAAC,EAAS,EAAW,CAAG,EAAA,QAAc,CAAC,CAAC,GAC9C,EAAA,SAAe,CAAC,KACZ,EAAW,WAAW,CAAG,MAAO,OAAE,CAAK,CAAE,CAAG,CAAC,CAAC,IAC1C,GAAI,CACA,IAAM,EAAyB,AAAV,cAGrB,GAAI,QAAwC,IAAxB,EAAW,QAAQ,CAAgB,CACnD,EAAW,SAAS,CAAG,IACvB,EAAW,QAAQ,CAAG,MAAM,EAAW,CACnC,UAAW,CAAC,CAChB,GACA,EAAW,EAAW,QAAQ,EAC9B,MACJ,CACA,GAIA,CAHA,AAGC,GAK2B,MAJxB,CAIA,CACA,CADW,QAAQ,EAEnB,IAAQ,EAAW,SAAS,CAC5B,CAD8B,MAIlC,EAAW,SAAS,CAAG,IACvB,EAAW,IANiD,GATU,CAenD,CAAG,GAXgD,GAW1C,IAC5B,EAAW,EAAW,QAAQ,CAClC,CACA,MAAO,EAAO,CACV,EAAO,KAAK,CAAC,IAAI,EAAmB,EAAM,OAAO,CAAE,GACvD,QACQ,CACJ,GAAW,EACf,CACJ,EACA,EAAW,WAAW,GACf,KACH,EAAW,SAAS,CAAG,EACvB,EAAW,QAAQ,MAAG,EACtB,EAAW,WAAW,CAAG,KAAQ,CACrC,GACD,EAAE,EACL,EAAA,SAAe,CAAC,KACZ,IAAM,EAAS,IAAM,EAAW,WAAW,CAAC,CAAE,MAAO,SAAU,GAW/D,OADA,IAAY,gBAAgB,CAAC,UAAW,GACjC,IAAM,IAAY,mBAAmB,CAAC,UAAW,EAC5D,EAAG,EAAE,EACL,EAAA,SAAe,CAAC,KACZ,GAAM,sBAAE,GAAuB,CAAI,CAAE,CAAG,EAIlC,EAAoB,KAClB,GAAqD,YAA7B,SAAS,eAAe,EAChD,EAAW,WAAW,CAAC,CAAE,MAAO,kBAAmB,EAC3D,EAEA,OADA,SAAS,gBAAgB,CAAC,mBAAoB,EAAmB,IAC1D,IAAM,SAAS,mBAAmB,CAAC,mBAAoB,GAAmB,EACrF,EAAG,CAAC,EAAM,oBAAoB,CAAC,EAC/B,IAAM,EEvQH,AFuQc,SEvQL,EACZ,GAAM,CAAC,EAAU,EAAY,CAAG,EAAA,QAAc,CAAC,AAAqB,oBAAd,WAA4B,UAAU,MAAM,EAC5F,CAD+F,CACnF,IAAM,GAAY,GAC9B,EAAa,IAAM,GAAY,GASrC,OARA,EAAA,SAAe,CAAC,KACZ,OAAO,gBAAgB,CAAC,SAAU,GAClC,OAAO,gBAAgB,CAAC,UAAW,GAC5B,KACH,OAAO,mBAAmB,CAAC,SAAU,GACrC,OAAO,mBAAmB,CAAC,UAAW,EAC1C,GACD,EAAE,EACE,CACX,IF4PU,GAAuC,IAAvB,GAAgC,EACtD,EAAA,SAAe,CAAC,KACZ,GAAI,GAAmB,EAAe,CAClC,IAAM,EAAuB,YAAY,KACjC,EAAW,QAAQ,EAAE,AACrB,EAAW,WAAW,CAAC,CAAE,MAAO,MAAO,EAE/C,EAAG,AAAkB,OACrB,MAAO,IAAM,cAAc,EAC/B,CACJ,EAAG,CAAC,EAAiB,EAAc,EACnC,IAAM,EAAQ,EAAA,OAAa,CAAC,IAAM,CAAC,CAC/B,KAAM,EACN,OAAQ,EACF,UACA,EACI,gBACA,kBACV,MAAM,OAAO,CAAI,EACb,GAAI,EACA,OACJ,GAAW,GACX,IAAM,EAAa,MAAM,EAAU,UAAW,EAAY,EAAQ,AAAgB,SAAT,OACnE,EACA,CAAE,KAAM,CAAE,UAAW,MAAM,SAAgB,CAAK,CAAE,GASxD,OARA,GAAW,GACP,IACA,EAAW,GACX,GAFY,CAEA,WAAW,CAAC,CACpB,MAAO,UACP,KAAM,CAAE,QAAS,YAAa,CAClC,IAEG,CACX,EACJ,CAAC,CAAG,CAAC,EAAS,EAAQ,EACtB,MAEA,CADA,AACA,EAAA,EAAA,GAAA,AAAI,EAAC,EAAe,QADD,AACS,CAAE,CAAE,MAAO,EAAO,SAAU,CAAS,EACrE,wEIlWA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OKEI,EAA+D,WACjE,OAAO,EADiB,EAE1B,ELFA,EKEI,ALFJ,EAAA,CAAA,CAAA,KDJA,EAAA,EAAA,CAAA,CAAA,OECA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACI,EAAa,cAAc,EAAA,YAAY,CACzC,YAAY,EAAS,CAAC,CAAC,CAAE,CACvB,KAAK,GACL,IAAI,CAAC,MAAM,CAAG,EACd,IAAI,EAAC,CAAA,AAAQ,CAAmB,EAAhB,EAAoB,GACtC,EACA,CAAA,AAAQ,AAAC,CACT,IAH+B,EAGzB,CAAM,CAAE,CAAO,CAAE,CAAK,CAAE,CAC5B,IAAM,EAAW,EAAQ,QAAQ,CAC3B,EAAY,EAAQ,SAAS,EAAI,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAU,GACnE,EAAQ,IAAI,CAAC,GAAG,CAAC,GAYrB,OAXK,IACH,EAAQ,CADE,GACE,EAAA,KAAK,CAAC,QAChB,WACA,YACA,EACA,QAAS,EAAO,mBAAmB,CAAC,GACpC,QACA,eAAgB,EAAO,gBAAgB,CAAC,EAC1C,GACA,IAAI,CAAC,GAAG,CAAC,IAEJ,CACT,CACA,IAAI,CAAK,CAAE,CACJ,IAAI,EAAC,CAAA,AAAQ,CAAC,GAAG,CAAC,EAAM,SAAS,GAAG,CACvC,IAAI,EAAC,CAAA,AAAQ,CAAC,GAAG,CAAC,EAAM,SAAS,CAAE,GACnC,IAAI,CAAC,MAAM,CAAC,CACV,KAAM,cACN,CACF,GAEJ,CACA,OAAO,CAAK,CAAE,CACZ,IAAM,EAAa,IAAI,EAAC,CAAA,AAAQ,CAAC,GAAG,CAAC,EAAM,SAAS,EAChD,IACF,EAAM,MADQ,CACD,GACT,IAAe,GACjB,IADwB,AACpB,EAAC,CAAA,AAAQ,CAAC,MAAM,CAAC,EAAM,SAAS,EAEtC,IAAI,CAAC,MAAM,CAAC,CAAE,KAAM,gBAAW,CAAM,GAEzC,CACA,OAAQ,CACN,EAAA,aAAa,CAAC,KAAK,CAAC,KAClB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,AAAC,IACrB,IAAI,CAAC,MAAM,CAAC,EACd,EACF,EACF,CACA,IAAI,CAAS,CAAE,CACb,OAAO,IAAI,EAAC,CAAA,AAAQ,CAAC,GAAG,CAAC,EAC3B,CACA,QAAS,CACP,MAAO,IAAI,IAAI,EAAC,CAAA,AAAQ,CAAC,MAAM,GAAG,AACpC,CACA,KAAK,CAAO,CAAE,CACZ,IAAM,EAAmB,CAAE,OAAO,EAAM,GAAG,CAAO,AAAC,EACnD,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CACvB,AAAC,GAAU,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,EAAkB,GAE5C,CACA,QAAQ,EAAU,CAAC,CAAC,CAAE,CACpB,IAAM,EAAU,IAAI,CAAC,MAAM,GAC3B,OAAO,OAAO,IAAI,CAAC,GAAS,MAAM,CAAG,EAAI,EAAQ,MAAM,CAAC,AAAC,GAAU,CAAA,EAAA,EAAA,UAAU,AAAV,EAAW,EAAS,IAAU,CACnG,CACA,OAAO,CAAK,CAAE,CACZ,EAAA,aAAa,CAAC,KAAK,CAAC,KAClB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,AAAC,IACtB,EAAS,EACX,EACF,EACF,CACA,SAAU,CACR,EAAA,aAAa,CAAC,KAAK,CAAC,KAClB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,AAAC,IACrB,EAAM,OAAO,EACf,EACF,EACF,CACA,UAAW,CACT,EAAA,aAAa,CAAC,KAAK,CAAC,KAClB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,AAAC,IACrB,EAAM,QAAQ,EAChB,EACF,EACF,CACF,EC1FA,EAAA,EAAA,CAAA,CAAA,WAGI,EAAgB,cAAc,EAAA,YAAY,CAC5C,YAAY,EAAS,CAAC,CAAC,CAAE,CACvB,KAAK,GACL,IAAI,CAAC,MAAM,CAAG,EACd,IAAI,EAAC,CAAA,AAAU,CAAmB,EAAhB,EAAoB,IACtC,IAAI,EAAC,CAD0B,AAC1B,AAAO,CAAmB,EAAhB,EAAoB,IACnC,IAAI,CAAC,CAAA,CADuB,AACZ,CAAG,CACrB,EACA,CAAU,AAAV,AAAW,EACX,CAAQ,AAAD,AAAP,EACA,CAAA,AAAW,AAAC,CACZ,MAAM,CAAM,CAAE,CAAO,CAAE,CAAK,CAAE,CAC5B,IAAM,EAAW,IAAI,EAAA,QAAQ,CAAC,CAC5B,cAAe,IAAI,CACnB,WAAY,EAAE,IAAI,EAAC,CAAA,AAAW,CAC9B,QAAS,EAAO,sBAAsB,CAAC,SACvC,CACF,GAEA,OADA,IAAI,CAAC,GAAG,CAAC,GACF,CACT,CACA,IAAI,CAAQ,CAAE,CACZ,IAAI,EAAC,CAAA,AAAU,CAAC,GAAG,CAAC,GACpB,IAAM,EAAQ,EAAS,GACvB,GAAqB,UAAjB,OAAO,EAAoB,CAC7B,IAAM,EAAkB,IAAI,EAAC,CAAA,AAAO,CAAC,GAAG,CAAC,GACrC,EACF,EAAgB,IAAI,CAAC,GAErB,IAAI,CAHe,CAGd,CAAA,AAAO,CAAC,GAAG,CAAC,EAAO,CAAC,EAAS,CAEtC,CACA,IAAI,CAAC,MAAM,CAAC,CAAE,KAAM,iBAAS,CAAS,EACxC,CACA,OAAO,CAAQ,CAAE,CACf,GAAI,IAAI,EAAC,CAAA,AAAU,CAAC,MAAM,CAAC,GAAW,CACpC,IAAM,EAAQ,EAAS,GACvB,GAAqB,UAAjB,OAAO,EAAoB,CAC7B,IAAM,EAAkB,IAAI,EAAC,CAAA,AAAO,CAAC,GAAG,CAAC,GACzC,GAAI,EACF,GAAI,EAAgB,MAAM,CAAG,EAAG,CADb,AAEjB,IAAM,EAAQ,EAAgB,OAAO,CAAC,GACxB,CAAC,GAAG,CAAd,GACF,EAAgB,MAAM,CAAC,EAAO,EAElC,MAAW,CAAJ,AAAmB,CAAC,EAAE,GAAK,GAChC,IAAI,EAAC,CADqC,AACrC,AAAO,CAAC,MAAM,CAAC,EAG1B,CACF,CACA,IAAI,CAAC,MAAM,CAAC,CAAE,KAAM,mBAAW,CAAS,EAC1C,CACA,OAAO,CAAQ,CAAE,CACf,IAAM,EAAQ,EAAS,GACvB,GAAqB,UAAjB,OAAO,EAOT,MAAO,EAPsB,EAC7B,IAAM,EAAyB,IAAI,EAAC,CAAA,AAAO,CAAC,GAAG,CAAC,GAC1C,EAAuB,GAAwB,KACnD,AAAC,GAAyB,YAAnB,EAAE,KAAK,CAAC,MAAM,EAEvB,MAAO,CAAC,GAAwB,IAAyB,CAC3D,CAGF,CACA,KAJS,GAID,CAAQ,CAAE,CAChB,IAAM,EAAQ,EAAS,GACvB,GAAqB,UAAjB,OAAO,EAIT,OAAO,QAAQ,OAAO,EAJO,EAC7B,IAAM,EAAgB,IAAI,EAAC,CAAA,AAAO,CAAC,GAAG,CAAC,IAAQ,KAAK,AAAC,GAAM,IAAM,GAAY,EAAE,KAAK,CAAC,QAAQ,EAC7F,OAAO,GAAe,YAAc,QAAQ,OAAO,EACrD,CAGF,CACA,KAJS,EAID,CACN,EAAA,aAAa,CAAC,KAAK,CAAC,KAClB,IAAI,EAAC,CAAA,AAAU,CAAC,OAAO,CAAC,AAAC,IACvB,IAAI,CAAC,MAAM,CAAC,CAAE,KAAM,mBAAW,CAAS,EAC1C,GACA,IAAI,EAAC,CAAA,AAAU,CAAC,KAAK,GACrB,IAAI,EAAC,CAAA,AAAO,CAAC,KAAK,EACpB,EACF,CACA,QAAS,CACP,OAAO,MAAM,IAAI,CAAC,IAAI,EAAC,CAAA,AAAU,CACnC,CACA,KAAK,CAAO,CAAE,CACZ,IAAM,EAAmB,CAAE,OAAO,EAAM,GAAG,CAAQ,AAAD,EAClD,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CACvB,AAAC,GAAa,CAAA,EAAA,EAAA,aAAA,AAAa,EAAC,EAAkB,GAElD,CACA,QAAQ,EAAU,CAAC,CAAC,CAAE,CACpB,OAAO,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,AAAC,GAAa,CAAA,EAAA,EAAA,aAAA,AAAa,EAAC,EAAS,GACnE,CACA,OAAO,CAAK,CAAE,CACZ,EAAA,aAAa,CAAC,KAAK,CAAC,KAClB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,AAAC,IACtB,EAAS,EACX,EACF,EACF,CACA,uBAAwB,CACtB,IAAM,EAAkB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,AAAC,GAAM,EAAE,KAAK,CAAC,QAAQ,EACpE,OAAO,EAAA,aAAa,CAAC,KAAK,CACxB,IAAM,QAAQ,GAAG,CACf,EAAgB,GAAG,CAAC,AAAC,GAAa,EAAS,QAAQ,GAAG,KAAK,CAAC,EAAA,IAAI,IAGtE,CACF,EACA,SAAS,EAAS,CAAQ,EACxB,OAAO,EAAS,OAAO,CAAC,KAAK,EAAE,EACjC,CH1GA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OIXA,SAAS,EAAsB,CAAK,EAClC,MAAO,CACL,QAAS,CAAC,EAAS,KACjB,IAAM,EAAU,EAAQ,OAAO,CACzB,EAAY,EAAQ,YAAY,EAAE,MAAM,WAAW,UACnD,EAAW,EAAQ,KAAK,CAAC,IAAI,EAAE,OAAS,EAAE,CAC1C,EAAgB,EAAQ,KAAK,CAAC,IAAI,EAAE,YAAc,EAAE,CACtD,EAAS,CAAE,MAAO,EAAE,CAAE,WAAY,EAAE,AAAC,EACrC,EAAc,EACZ,EAAU,UACd,IAAI,EAAY,GAgBV,EAAU,CAAA,EAAA,EAAA,aAAA,AAAa,EAAC,EAAQ,OAAO,CAAE,EAAQ,YAAY,EAC7D,EAAY,MAAO,EAAM,EAAO,KACpC,GAAI,EACF,OAAO,EADM,MACE,MAAM,GAEvB,GAAa,MAAT,GAAiB,EAAK,KAAK,CAAC,MAAM,CACpC,CADsC,MAC/B,QAAQ,OAAO,CAAC,GAazB,IAAM,EAAiB,CAXM,KAC3B,IAAM,EAAkB,CACtB,OAAQ,EAAQ,MAAM,CACtB,SAAU,EAAQ,QAAQ,CAC1B,UAAW,EACX,UAAW,EAAW,WAAa,UACnC,KAAM,EAAQ,OAAO,CAAC,IAAI,AAC5B,EAEA,OA/BF,OAAO,cAAc,CAAC,AA8BF,EA9BU,SAAU,CACtC,YAAY,EACZ,IAAK,KACC,EAAQ,MAAM,CAAC,OAAO,CACxB,CAD0B,EACd,EAEZ,EAAQ,MAAM,CAAC,gBAAgB,CAAC,QAAS,KACvC,GAAY,CACd,GAEK,EAAQ,MAAM,CAEzB,GAmBS,EACT,IAEM,EAAO,MAAM,EAAQ,GACrB,CAAE,UAAQ,CAAE,CAAG,EAAQ,OAAO,CAC9B,EAAQ,EAAW,EAAA,UAAU,CAAG,EAAA,QAAQ,CAC9C,MAAO,CACL,MAAO,EAAM,EAAK,KAAK,CAAE,EAAM,GAC/B,WAAY,EAAM,EAAK,UAAU,CAAE,EAAO,EAC5C,CACF,EACA,GAAI,GAAa,EAAS,MAAM,CAAE,CAChC,IAAM,EAAW,AAAc,eAEzB,EAAU,CACd,MAAO,EACP,WAAY,CACd,EACM,EAAQ,CALM,EAgD9B,SAhDyC,AAgDhC,AAAqB,CAAO,CAAE,OAAE,CAAK,YAAE,CAAU,CAAE,EAC1D,OAAO,EAAM,MAAM,CAAG,EAAI,EAAQ,oBAAoB,GAAG,CAAK,CAAC,EAAE,CAAE,EAAO,CAAU,CAAC,EAAE,CAAE,GAAc,KAAK,CAC9G,EAlDgE,CAAA,EAK5B,EAAS,GACnC,EAAS,MAAM,EAAU,EAAS,EAAO,EAC3C,KAAO,CACL,IAAM,EAAiB,GAAS,EAAS,MAAM,CAC/C,EAAG,CACD,IAAM,EAAwB,IAAhB,EAAoB,CAAa,CAAC,EAAE,EAAI,EAAQ,gBAAgB,CAAG,EAAiB,EAAS,GAC3G,GAAI,EAAc,GAAc,MAAM,AAAf,EACrB,MAEF,EAAS,MAAM,EAAU,EAAQ,GACjC,GACF,OAAS,EAAc,EACzB,AADyC,CAEzC,OAAO,CACT,EACI,EAAQ,OAAO,CAAC,SAAS,CAC3B,CAD6B,CACrB,OAAO,CAAG,IACT,EAAQ,OAAO,CAAC,SAAS,GAC9B,EACA,CACE,OAAQ,EAAQ,MAAM,CACtB,SAAU,EAAQ,QAAQ,CAC1B,KAAM,EAAQ,OAAO,CAAC,IAAI,CAC1B,OAAQ,EAAQ,MAClB,AADwB,EAExB,GAIJ,EAAQ,OAAO,CAAG,CAEtB,CACF,CACF,CACA,SAAS,EAAiB,CAAO,CAAE,OAAE,CAAK,YAAE,CAAU,CAAE,EACtD,IAAM,EAAY,EAAM,MAAM,CAAG,EACjC,OAAO,EAAM,MAAM,CAAG,EAAI,EAAQ,gBAAgB,CAChD,CAAK,CAAC,EAAU,CAChB,EACA,CAAU,CAAC,EAAU,CACrB,GACE,KAAK,CACX,CJzFA,IAAI,EAAc,OAChB,CAAW,AACX,AADA,AAAY,CACZ,EAAc,AAAC,EACf,CAAA,AAAe,AAAC,EAChB,CAAA,AAAc,AAAC,EACf,CAAA,AAAiB,AAAC,EAClB,CAAA,AAAW,AAAC,EACZ,CAAA,AAAiB,AAAC,EAClB,CAAA,AAAkB,AAAC,AACnB,aAAY,EAAS,CAAC,CAAC,CAAE,CACvB,IAAI,EAAC,CAAA,AAAW,CAAG,EAAO,UAAU,EAAI,IAAI,EAC5C,IAAI,EAAC,CAAA,AAAc,CAAG,EAAO,aAAa,EAAI,IAAI,EAClD,IAAI,EAAC,CAAA,AAAe,CAAG,EAAO,cAAc,EAAI,CAAC,EACjD,IAAI,EAAC,CAAA,AAAc,CAAmB,EAAhB,EAAoB,IAC1C,IAAI,CAAC,CAAA,CAD8B,AACb,CAAmB,EAAhB,EAAoB,IAC7C,IAAI,EAAC,CADiC,AACjC,AAAW,CAAG,CACrB,CACA,OAAQ,CACN,IAAI,EAAC,CAAA,AAAW,GACS,GAAG,CAAxB,IAAI,EAAC,CAAW,AAAX,GACT,IAAI,EAAC,CAAiB,AAAjB,CAAoB,EAAA,YAAY,CAAC,SAAS,CAAC,MAAO,IACjD,IACF,KADW,CACL,IAAI,CAAC,qBAAqB,GAChC,IAAI,EAAC,CAAA,AAAW,CAAC,OAAO,GAE5B,GACA,IAAI,EAAC,CAAA,AAAkB,CAAG,EAAA,aAAa,CAAC,SAAS,CAAC,MAAO,IACnD,IACF,IADU,EACJ,IAAI,CAAC,qBAAqB,GAChC,IAAI,EAAC,CAAA,AAAW,CAAC,QAAQ,GAE7B,GACF,CACA,SAAU,CACR,IAAI,CAAC,CAAA,CAAW,GACS,GAAG,CAAxB,IAAI,EAAC,CAAA,AAAW,GACpB,IAAI,EAAC,CAAA,AAAiB,KACtB,IAAI,EAAC,CAAA,AAAiB,CAAG,KAAK,EAC9B,IAAI,EAAC,CAAA,AAAkB,KACvB,IAAI,EAAC,CAAA,AAAkB,CAAG,KAAK,EACjC,CACA,WAAW,CAAO,CAAE,CAClB,OAAO,IAAI,EAAC,CAAW,AAAX,CAAY,OAAO,CAAC,CAAE,GAAG,CAAO,CAAE,YAAa,UAAW,GAAG,MAAM,AACjF,CACA,WAAW,CAAO,CAAE,CAClB,OAAO,IAAI,CAAC,CAAA,CAAc,CAAC,OAAO,CAAC,CAAE,GAAG,CAAO,CAAE,OAAQ,SAAU,GAAG,MAAM,AAC9E,CAQA,aAAa,CAAQ,CAAE,CACrB,IAAM,EAAU,IAAI,CAAC,mBAAmB,CAAC,UAAE,CAAS,GACpD,OAAO,IAAI,EAAC,CAAA,AAAW,CAAC,GAAG,CAAC,EAAQ,SAAS,GAAG,MAAM,IACxD,CACA,gBAAgB,CAAO,CAAE,CACvB,IAAM,EAAmB,IAAI,CAAC,mBAAmB,CAAC,GAC5C,EAAQ,IAAI,EAAC,CAAW,AAAX,CAAY,KAAK,CAAC,IAAI,CAAE,GACrC,EAAa,EAAM,KAAK,CAAC,IAAI,QACnC,AAAI,AAAe,KAAK,GAAG,GAClB,IAAI,CAAC,UAAU,CAAC,IAErB,EAAQ,iBAAiB,EAAI,EAAM,aAAa,CAAC,CAAA,EAAA,EAAA,gBAAgB,AAAhB,EAAiB,EAAiB,SAAS,CAAE,KAC3F,IADoG,AAChG,CAAC,aAAa,CAAC,GAEnB,QAAQ,OAAO,CAAC,GACzB,CACA,eAAe,CAAO,CAAE,CACtB,OAAO,IAAI,EAAC,CAAA,AAAW,CAAC,OAAO,CAAC,GAAS,GAAG,CAAC,CAAC,UAAE,CAAQ,OAAE,CAAK,CAAE,GAExD,CAAC,EADK,EAAM,IAAI,CACA,CAE3B,CACA,aAAa,CAAQ,CAAE,CAAO,CAAE,CAAO,CAAE,CACvC,IAAM,EAAmB,IAAI,CAAC,mBAAmB,CAAC,UAAE,CAAS,GACvD,EAAQ,IAAI,EAAC,CAAA,AAAW,CAAC,GAAG,CAChC,EAAiB,SAAS,EAEtB,EAAW,GAAO,MAAM,KACxB,EAAO,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,EAAS,GACvC,GAAI,AAAS,KAAK,GAAG,GAGrB,OAAO,IAAI,EAAC,CAAA,AAAW,CAAC,KAAK,CAAC,IAAI,CAAE,GAAkB,OAAO,CAAC,EAAM,CAAE,GAAG,CAAO,CAAE,QAAQ,CAAK,EACjG,CACA,eAAe,CAAO,CAAE,CAAO,CAAE,CAAO,CAAE,CACxC,OAAO,EAAA,aAAa,CAAC,KAAK,CACxB,IAAM,IAAI,EAAC,CAAA,AAAW,CAAC,OAAO,CAAC,GAAS,GAAG,CAAC,CAAC,UAAE,CAAQ,CAAE,GAAK,CAC5D,EACA,IAAI,CAAC,YAAY,CAAC,EAAU,EAAS,GACtC,EAEL,CACA,cAAc,CAAQ,CAAE,CACtB,IAAM,EAAU,IAAI,CAAC,mBAAmB,CAAC,UAAE,CAAS,GACpD,OAAO,IAAI,EAAC,CAAA,AAAW,CAAC,GAAG,CACzB,EAAQ,SAAS,GAChB,KACL,CACA,cAAc,CAAO,CAAE,CACrB,IAAM,EAAa,IAAI,EAAC,CAAA,AAAW,CACnC,EAAA,aAAa,CAAC,KAAK,CAAC,KAClB,EAAW,OAAO,CAAC,GAAS,OAAO,CAAC,AAAC,IACnC,EAAW,MAAM,CAAC,EACpB,EACF,EACF,CACA,aAAa,CAAO,CAAE,CAAO,CAAE,CAC7B,IAAM,EAAa,IAAI,EAAC,CAAA,AAAW,CACnC,OAAO,EAAA,aAAa,CAAC,KAAK,CAAC,KACzB,EAAW,OAAO,CAAC,GAAS,OAAO,CAAC,AAAC,IACnC,EAAM,KAAK,EACb,GACO,IAAI,CAAC,cAAc,CACxB,CACE,KAAM,SACN,GAAG,CAAO,AACZ,EACA,IAGN,CACA,cAAc,CAAO,CAAE,EAAgB,CAAC,CAAC,CAAE,CACzC,IAAM,EAAyB,CAAE,QAAQ,EAAM,GAAG,CAAa,AAAC,EAIhE,OAAO,QAAQ,GAAG,CAAC,AAHF,EAAA,aAAa,CAAC,KAAK,CAClC,IAAM,IAAI,EAAC,CAAA,AAAW,CAAC,OAAO,CAAC,GAAS,GAAG,CAAC,AAAC,GAAU,EAAM,MAAM,CAAC,MAEzC,IAAI,CAAC,EAAA,IAAI,EAAE,KAAK,CAAC,EAAA,IAAI,CACpD,CACA,kBAAkB,CAAO,CAAE,EAAU,CAAC,CAAC,CAAE,CACvC,OAAO,EAAA,aAAa,CAAC,KAAK,CAAC,IAIzB,CAHA,IAAI,EAAC,CAAA,AAAW,CAAC,OAAO,CAAC,GAAS,OAAO,CAAC,AAAC,IACzC,EAAM,UAAU,EAClB,GACI,GAAS,cAAgB,QACpB,AAD4B,QACpB,OAAO,GAEjB,IAAI,CAAC,cAAc,CACxB,CACE,GAAG,CAAO,CACV,KAAM,GAAS,aAAe,GAAS,MAAQ,QACjD,EACA,GAGN,CACA,eAAe,CAAO,CAAE,EAAU,CAAC,CAAC,CAAE,CACpC,IAAM,EAAe,CACnB,GAAG,CAAO,CACV,cAAe,EAAQ,aAAa,GAAI,CAC1C,EAUA,OAAO,QAAQ,GAAG,CATD,AASE,EATF,aAAa,CAAC,KAAK,CAClC,IAAM,IAAI,EAAC,CAAA,AAAW,CAAC,OAAO,CAAC,GAAS,MAAM,CAAC,AAAC,GAAU,CAAC,EAAM,UAAU,IAAM,CAAC,EAAM,QAAQ,IAAI,GAAG,CAAC,AAAC,IACvG,IAAI,EAAU,EAAM,KAAK,CAAC,KAAK,EAAG,GAIlC,OAHI,AAAC,EAAa,YAAY,EAAE,CAC9B,EAAU,EAAQ,KAAK,CAAC,EAAA,KAAI,EAEK,WAA5B,EAAM,KAAK,CAAC,WAAW,CAAgB,QAAQ,OAAO,GAAK,CACpE,KAE2B,IAAI,CAAC,EAAA,IAAI,CACxC,CACA,WAAW,CAAO,CAAE,CAClB,IAAM,EAAmB,IAAI,CAAC,mBAAmB,CAAC,EACnB,MAAK,GAAG,CAAnC,EAAiB,KAAK,GACxB,EAAiB,KAAK,CAAG,EAAA,EAE3B,IAAM,EAAQ,IAAI,EAAC,CAAA,AAAW,CAAC,KAAK,CAAC,IAAI,CAAE,GAC3C,OAAO,EAAM,aAAa,CACxB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,EAAiB,SAAS,CAAE,IAC3C,EAAM,KAAK,CAAC,GAAoB,QAAQ,OAAO,CAAC,EAAM,KAAK,CAAC,IAAI,CACtE,CACA,cAAc,CAAO,CAAE,CACrB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAS,IAAI,CAAC,EAAA,IAAI,EAAE,KAAK,CAAC,EAAA,IAAI,CACvD,CACA,mBAAmB,CAAO,CAAE,CAE1B,OADA,EAAQ,QAAQ,CAAG,EAAsB,EAAQ,KAAK,EAC/C,IAAI,CAAC,UAAU,CAAC,EACzB,CACA,sBAAsB,CAAO,CAAE,CAC7B,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAS,IAAI,CAAC,EAAA,IAAI,EAAE,KAAK,CAAC,EAAA,IAAI,CAC/D,CACA,wBAAwB,CAAO,CAAE,CAE/B,OADA,EAAQ,QAAQ,CAAG,EAAsB,EAAQ,KAAK,EAC/C,IAAI,CAAC,eAAe,CAAC,EAC9B,CACA,uBAAwB,QACtB,AAAI,EAAA,aAAa,CAAC,QAAQ,GACjB,CADqB,GACjB,EAAC,CAAA,AAAc,CAAC,qBAAqB,GAE3C,QAAQ,OAAO,EACxB,CACA,eAAgB,CACd,OAAO,IAAI,EAAC,CAAA,AAAW,AACzB,CACA,kBAAmB,CACjB,OAAO,IAAI,EAAC,CAAA,AAAc,AAC5B,CACA,mBAAoB,CAClB,OAAO,IAAI,EAAC,CACd,AAD6B,AAAf,CAEd,kBAAkB,CAAO,CAAE,CACzB,IAAI,EAAC,CAAA,AAAe,CAAG,CACzB,CACA,iBAAiB,CAAQ,CAAE,CAAO,CAAE,CAClC,IAAI,EAAC,CAAA,AAAc,CAAC,GAAG,CAAC,CAAA,EAAA,EAAA,OAAA,AAAO,EAAC,GAAW,UACzC,EACA,eAAgB,CAClB,EACF,CACA,iBAAiB,CAAQ,CAAE,CACzB,IAAM,EAAW,IAAI,IAAI,EAAC,CAAc,AAAd,CAAe,MAAM,GAAG,CAC5C,EAAS,CAAC,EAMhB,OALA,EAAS,OAAO,CAAC,AAAC,IACZ,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAU,EAAa,QAAQ,GAAG,AACpD,OAAO,MAAM,CAAC,EAAQ,EAAa,cAAc,CAErD,GACO,CACT,CACA,oBAAoB,CAAW,CAAE,CAAO,CAAE,CACxC,IAAI,EAAC,CAAiB,AAAjB,CAAkB,GAAG,CAAC,CAAA,EAAA,EAAA,OAAA,AAAO,EAAC,GAAc,CAC/C,cACA,eAAgB,CAClB,EACF,CACA,oBAAoB,CAAW,CAAE,CAC/B,IAAM,EAAW,IAAI,IAAI,CAAC,CAAA,CAAiB,CAAC,MAAM,GAAG,CAC/C,EAAS,CAAC,EAMhB,OALA,EAAS,OAAO,CAAC,AAAC,IACZ,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAa,EAAa,WAAW,GAAG,AAC1D,OAAO,MAAM,CAAC,EAAQ,EAAa,cAAc,CAErD,GACO,CACT,CACA,oBAAoB,CAAO,CAAE,CAC3B,GAAI,EAAQ,UAAU,CACpB,CADsB,MACf,EAET,IAAM,EAAmB,CACvB,GAAG,IAAI,EAAC,CAAA,AAAe,CAAC,OAAO,CAC/B,GAAG,IAAI,CAAC,gBAAgB,CAAC,EAAQ,QAAQ,CAAC,CAC1C,GAAG,CAAO,CACV,YAAY,CACd,EAmBA,OAlBI,AAAC,EAAiB,SAAS,EAAE,CAC/B,EAAiB,SAAS,CAAG,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAChD,EAAiB,QAAQ,CACzB,EAAA,EAGwC,KAAK,GAAG,CAAhD,EAAiB,kBAAkB,GACrC,EAAiB,kBAAkB,CAAoC,WAAjC,EAAiB,WAAW,AAAK,EAErE,AAAkC,KAAK,GAAG,GAAzB,YAAY,GAC/B,EAAiB,YAAY,CAAG,CAAC,CAAC,EAAiB,QAAA,AAAQ,EAEzD,CAAC,EAAiB,WAAW,EAAI,EAAiB,SAAS,EAAE,AAC/D,GAAiB,WAAW,CAAG,cAAA,EAE7B,EAAiB,OAAO,GAAK,EAAA,SAAS,EAAE,CAC1C,EAAiB,OAAO,EAAG,CAAA,EAEtB,CACT,CACA,uBAAuB,CAAO,CAAE,QAC9B,AAAI,GAAS,WACJ,CADgB,CAGlB,CACL,GAAG,IAAI,EAAC,CAAA,AAAe,CAAC,SAAS,CACjC,GAAG,GAAS,aAAe,IAAI,CAAC,mBAAmB,CAAC,EAAQ,WAAW,CAAC,CACxE,GAAG,CAAO,CACV,YAAY,CACd,CACF,CACA,OAAQ,CACN,IAAI,EAAC,CAAA,AAAW,CAAC,KAAK,GACtB,IAAI,EAAC,CAAA,AAAc,CAAC,KAAK,EAC3B,CACF,QCpSA,SAAS,AAAU,CAAE,UAAQ,CAAqB,EAKjD,GAAM,CAAC,EAAY,CAAG,CAAA,AAgBR,EAhBQ,EAAA,QAAA,AAAQ,EAAC,IIRvB,AJQ6B,IIRzB,EAAY,CACrB,eAAgB,CACd,QAAS,CAGP,UAAW,IACX,CADgB,MACR,IAAI,AACZ,KADiB,CACV,CAAC,EAAc,KAEpB,GAAI,aAAiB,OAAS,WAAY,EAAO,CAC/C,IAAM,EAAU,EAAc,MAAM,CACpC,GAAI,GAAU,KAAO,EAAS,IAC5B,CADiC,MAC1B,CAEX,CAEA,OAAO,EAAe,CACxB,EACA,WAAY,AAAC,GAAiB,KAAK,GAAG,CAAC,IAAO,GAAK,EAAc,KACjE,sBAAsB,EACtB,oBAAoB,CACtB,EACA,UAAW,CACT,OAAO,EACP,QAAS,AAAC,IACR,QAAQ,KAAK,CAAC,kBAAmB,EACnC,CACF,CACF,CACF,IJnBD,MACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,mBAAmB,CAAA,CAAC,OAAQ,WAC5B,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,eAAe,CAAA,WACd,EAED,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACA,eAAe,EACf,eAAe,qBAKpB", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 8, 9, 10, 12]}