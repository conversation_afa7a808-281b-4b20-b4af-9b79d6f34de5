import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Calendar, DollarSign, Wallet } from "lucide-react";
import { type Session, type Caisse, CaisseType } from "@/types";

interface DashboardStatsProps {
  sessions: Session[];
  caisses: Caisse[];
  userRole: string;
}

export function DashboardStats({ sessions, caisses, userRole }: DashboardStatsProps) {
  // Calculate statistics
  const activeSessions = sessions.filter((s) => s.statut === "active");
  const totalContributions = sessions.reduce((sum, session) => sum + session.montantContribution, 0);
  const caissesPrincipales = caisses.filter((c) => c.type === CaisseType.PRINCIPALE);
  const caissesReunions = caisses.filter((c) => c.type === CaisseType.REUNION);

  // Permission checks
  const canViewSessions = ["secretary_general", "controller", "cashier"].includes(userRole);
  const canViewCaisses = ["secretary_general", "controller", "cashier"].includes(userRole);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {canViewSessions && (
        <>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Sessions Totales</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{sessions.length}</div>
              <p className="text-xs text-muted-foreground">
                {activeSessions.length} actives
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Contributions Totales</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {totalContributions.toLocaleString()} FCFA
              </div>
              <p className="text-xs text-muted-foreground">
                Toutes sessions confondues
              </p>
            </CardContent>
          </Card>
        </>
      )}

      {canViewCaisses && (
        <>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Caisses</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{caisses.length}</div>
              <p className="text-xs text-muted-foreground">
                {caissesPrincipales.length} principales, {caissesReunions.length} réunions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Solde Total</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {caisses.reduce((sum, c) => sum + c.solde, 0).toLocaleString()} FCFA
              </div>
              <p className="text-xs text-muted-foreground">
                Toutes caisses confondues
              </p>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
