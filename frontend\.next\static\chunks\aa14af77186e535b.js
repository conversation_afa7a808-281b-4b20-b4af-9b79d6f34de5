(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,44640,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ReflectAdapter",{enumerable:!0,get:function(){return s}});class s{static get(e,t,r){let s=Reflect.get(e,t,r);return"function"==typeof s?s.bind(e):s}static set(e,t,r,s){return Reflect.set(e,t,r,s)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},8356,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorageInstance",{enumerable:!0,get:function(){return s}});let s=(0,e.r(97918).createAsyncLocalStorage)()},62355,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorage",{enumerable:!0,get:function(){return s.afterTaskAsyncStorageInstance}});let s=e.r(8356)},17939,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{isRequestAPICallableInsideAfter:function(){return l},throwForSearchParamsAccessInUseCache:function(){return o},throwWithStaticGenerationBailoutError:function(){return n},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return i}});let s=e.r(85115),a=e.r(62355);function n(e,t){throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(e," couldn't be rendered statically because it used ").concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function i(e,t){throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(e,' with `dynamic = "error"` couldn\'t be rendered statically because it used ').concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function o(e,t){let r=Object.defineProperty(Error("Route ".concat(e.route,' used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(r,t),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=r),r}function l(){let e=a.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},93168,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{describeHasCheckingStringProperty:function(){return n},describeStringPropertyAccess:function(){return a},wellKnownProperties:function(){return i}});let s=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function a(e,t){return s.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function n(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let i=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},63470,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return l}});let s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var o=n?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(s,i,o):s[i]=e[i]}return s.default=e,r&&r.set(e,s),s}(e.r(38477));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}let n={current:null},i="function"==typeof s.cache?s.cache:e=>e,o=console.warn;function l(e){return function(){for(var t=arguments.length,r=Array(t),s=0;s<t;s++)r[s]=arguments[s];o(e(...r))}}i(e=>{try{o(n.current)}finally{n.current=null}})},4467,e=>{"use strict";e.s(["useApi",()=>s]);var t=e.i(1269),r=e.i(1831);function s(){let{data:e}=(0,t.useSession)(),s=async function(t){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(null==e?void 0:e.accessToken))throw Error("Non authentifié");return r.apiService.authenticatedRequest(t,e.accessToken,s)};return{login:r.apiService.login.bind(r.apiService),register:r.apiService.register.bind(r.apiService),authenticatedRequest:s,getUsers:()=>s("/users"),getUser:e=>s("/users/".concat(e)),createUser:e=>s("/users",{method:"POST",body:JSON.stringify(e)}),updateUser:(e,t)=>s("/users/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteUser:e=>s("/users/".concat(e),{method:"DELETE"}),getSessions:()=>s("/sessions"),getSession:e=>s("/sessions/".concat(e)),createSession:e=>s("/sessions",{method:"POST",body:JSON.stringify(e)}),updateSession:(e,t)=>s("/sessions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteSession:e=>s("/sessions/".concat(e),{method:"DELETE"}),getCaisses:()=>s("/caisses"),getCaisse:e=>s("/caisses/".concat(e)),createCaisse:e=>s("/caisses",{method:"POST",body:JSON.stringify(e)}),updateCaisse:(e,t)=>s("/caisses/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteCaisse:e=>s("/caisses/".concat(e),{method:"DELETE"}),emargerCaisse:e=>s("/caisses/".concat(e,"/emarger"),{method:"POST"}),getReunions:()=>s("/reunions"),getReunion:e=>s("/reunions/".concat(e)),updateReunion:(e,t)=>s("/reunions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),getMembers:()=>s("/members"),getMember:e=>s("/members/".concat(e)),createMember:e=>s("/members",{method:"POST",body:JSON.stringify(e)}),updateMember:(e,t)=>s("/members/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteMember:e=>s("/members/".concat(e),{method:"DELETE"}),getMemberDebrief:(e,t)=>{let r=new URLSearchParams;(null==t?void 0:t.dateFrom)&&r.append("dateFrom",t.dateFrom),(null==t?void 0:t.dateTo)&&r.append("dateTo",t.dateTo),(null==t?void 0:t.sessionId)&&r.append("sessionId",t.sessionId);let a=r.toString()?"?".concat(r.toString()):"";return s("/members/".concat(e,"/debrief").concat(a))},createPayment:e=>s("/payments",{method:"POST",body:JSON.stringify(e)}),getSessionMembers:e=>s("/sessions/".concat(e,"/members")),addSessionMember:e=>s("/session-members",{method:"POST",body:JSON.stringify(e)}),removeSessionMember:(e,t)=>s("/sessions/".concat(e,"/members/").concat(t),{method:"DELETE"})}}},57223,e=>{"use strict";e.s(["DollarSign",()=>t],57223);let t=(0,e.i(44571).default)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},2470,e=>{"use strict";e.s(["Badge",()=>i]);var t=e.i(4051),r=e.i(81221),s=e.i(62244),a=e.i(41428);let n=(0,s.cva)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:i,asChild:o=!1,...l}=e,c=o?r.Slot:"span";return(0,t.jsx)(c,{"data-slot":"badge",className:(0,a.cn)(n({variant:i}),s),...l})}},27973,e=>{"use strict";e.s(["Trash2",()=>t],27973);let t=(0,e.i(44571).default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},81787,e=>{"use strict";e.s(["Plus",()=>t],81787);let t=(0,e.i(44571).default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},41850,e=>{"use strict";e.s(["Table",()=>s,"TableBody",()=>n,"TableCell",()=>l,"TableHead",()=>o,"TableHeader",()=>a,"TableRow",()=>i]);var t=e.i(4051),r=e.i(41428);function s(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",s),...a})})}function a(e){let{className:s,...a}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",s),...a})}function n(e){let{className:s,...a}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",s),...a})}function i(e){let{className:s,...a}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...a})}function o(e){let{className:s,...a}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}function l(e){let{className:s,...a}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}},15235,e=>{"use strict";e.s(["default",()=>v]);var t=e.i(4051),r=e.i(38477),s=e.i(1269),a=e.i(81787),n=e.i(60019),i=e.i(3840),o=e.i(4818),l=e.i(27973),c=e.i(67435),d=e.i(1827),u=e.i(57223),h=e.i(5085),m=e.i(85205),f=e.i(96134),b=e.i(75680),x=e.i(41850),p=e.i(35369),g=e.i(83194),y=e.i(2470),j=e.i(4467);function v(){let{data:e}=(0,s.useSession)(),v=(0,j.useApi)(),[S,w]=(0,r.useState)([]),[T,C]=(0,r.useState)(null),[N,O]=(0,r.useState)(!0),[P,D]=(0,r.useState)(""),[E,_]=(0,r.useState)("all"),M=(null==e?void 0:e.user)&&("secretary_general"===e.user.role||"controller"===e.user.role||"cashier"===e.user.role),k=(null==e?void 0:e.user)&&"secretary_general"===e.user.role,A=async()=>{try{O(!0);let e=await v.getSessions();w(e);let t=new Date,r=e.filter(e=>new Date(e.dateDebut)<=t&&new Date(e.dateFin)>=t).length,s=e.filter(e=>new Date(e.dateFin)<t).length,a=e.reduce((e,t)=>e+t.partFixe,0);C({total:e.length,active:r,completed:s,totalPartFixe:a})}catch(e){console.error("Erreur lors du chargement des sessions:",e)}finally{O(!1)}};(0,r.useEffect)(()=>{(null==e?void 0:e.accessToken)&&A()},[e]);let R=async e=>{if(confirm("Êtes-vous sûr de vouloir supprimer cette session ?"))try{await v.deleteSession(e),A()}catch(e){console.error("Erreur lors de la suppression:",e)}},H=S.filter(e=>{let t=e.annee.toString().includes(P)||new Date(e.dateDebut).toLocaleDateString().includes(P)||new Date(e.dateFin).toLocaleDateString().includes(P),r="all"===E||e.annee.toString()===E;return t&&r}),F=Array.from(new Set(S.map(e=>e.annee.toString()))).sort();return N?(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Chargement des sessions..."})]})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Sessions"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Gérez les sessions de tontine et leurs réunions"})]}),k&&(0,t.jsx)(m.Button,{asChild:!0,children:(0,t.jsxs)(h.default,{href:"/dashboard/sessions/new",children:[(0,t.jsx)(a.Plus,{className:"mr-2 h-4 w-4"}),"Nouvelle session"]})})]}),T&&(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsxs)(b.Card,{children:[(0,t.jsxs)(b.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(b.CardTitle,{className:"text-sm font-medium",children:"Total Sessions"}),(0,t.jsx)(c.Calendar,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsx)(b.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:T.total})})]}),(0,t.jsxs)(b.Card,{children:[(0,t.jsxs)(b.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(b.CardTitle,{className:"text-sm font-medium",children:"Sessions Actives"}),(0,t.jsx)(d.Users,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsx)(b.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:T.active})})]}),(0,t.jsxs)(b.Card,{children:[(0,t.jsxs)(b.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(b.CardTitle,{className:"text-sm font-medium",children:"Sessions Terminées"}),(0,t.jsx)(c.Calendar,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsx)(b.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:T.completed})})]}),(0,t.jsxs)(b.Card,{children:[(0,t.jsxs)(b.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(b.CardTitle,{className:"text-sm font-medium",children:"Part Fixe Totale"}),(0,t.jsx)(u.DollarSign,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsx)(b.CardContent,{children:(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[T.totalPartFixe.toLocaleString()," FCFA"]})})]})]}),(0,t.jsxs)(b.Card,{children:[(0,t.jsxs)(b.CardHeader,{children:[(0,t.jsx)(b.CardTitle,{children:"Filtres"}),(0,t.jsx)(b.CardDescription,{children:"Recherchez et filtrez les sessions"})]}),(0,t.jsx)(b.CardContent,{children:(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.Search,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(f.Input,{placeholder:"Rechercher par année ou date...",value:P,onChange:e=>D(e.target.value),className:"pl-8"})]})}),(0,t.jsxs)(g.Select,{value:E,onValueChange:_,children:[(0,t.jsx)(g.SelectTrigger,{className:"w-[180px]",children:(0,t.jsx)(g.SelectValue,{placeholder:"Filtrer par année"})}),(0,t.jsxs)(g.SelectContent,{children:[(0,t.jsx)(g.SelectItem,{value:"all",children:"Toutes les années"}),F.map(e=>(0,t.jsx)(g.SelectItem,{value:e,children:e},e))]})]})]})})]}),(0,t.jsxs)(b.Card,{children:[(0,t.jsx)(b.CardHeader,{children:(0,t.jsxs)(b.CardTitle,{children:["Sessions (",H.length,")"]})}),(0,t.jsxs)(b.CardContent,{children:[(0,t.jsxs)(x.Table,{children:[(0,t.jsx)(x.TableHeader,{children:(0,t.jsxs)(x.TableRow,{children:[(0,t.jsx)(x.TableHead,{children:"Année"}),(0,t.jsx)(x.TableHead,{children:"Période"}),(0,t.jsx)(x.TableHead,{children:"Part Fixe"}),(0,t.jsx)(x.TableHead,{children:"Statut"}),(0,t.jsx)(x.TableHead,{children:"Créée le"}),M&&(0,t.jsx)(x.TableHead,{children:"Actions"})]})}),(0,t.jsx)(x.TableBody,{children:H.map(e=>{let r=(e=>{let t=new Date,r=new Date(e.dateDebut),s=new Date(e.dateFin);return t<r?{label:"À venir",variant:"secondary"}:t>s?{label:"Terminée",variant:"outline"}:{label:"Active",variant:"default"}})(e);return(0,t.jsxs)(x.TableRow,{children:[(0,t.jsx)(x.TableCell,{className:"font-medium",children:e.annee}),(0,t.jsxs)(x.TableCell,{children:[new Date(e.dateDebut).toLocaleDateString()," -"," ",new Date(e.dateFin).toLocaleDateString()]}),(0,t.jsxs)(x.TableCell,{children:[e.partFixe.toLocaleString()," FCFA"]}),(0,t.jsx)(x.TableCell,{children:(0,t.jsx)(y.Badge,{variant:r.variant,children:r.label})}),(0,t.jsx)(x.TableCell,{children:new Date(e.createdAt).toLocaleDateString()}),M&&(0,t.jsx)(x.TableCell,{children:(0,t.jsxs)(p.DropdownMenu,{children:[(0,t.jsx)(p.DropdownMenuTrigger,{asChild:!0,children:(0,t.jsx)(m.Button,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,t.jsx)(i.MoreHorizontal,{className:"h-4 w-4"})})}),(0,t.jsxs)(p.DropdownMenuContent,{align:"end",children:[(0,t.jsx)(p.DropdownMenuItem,{asChild:!0,children:(0,t.jsxs)(h.default,{href:"/dashboard/sessions/".concat(e._id,"/edit"),children:[(0,t.jsx)(o.Edit,{className:"mr-2 h-4 w-4"}),"Modifier"]})}),k&&(0,t.jsxs)(p.DropdownMenuItem,{onClick:()=>R(e._id),className:"text-red-600",children:[(0,t.jsx)(l.Trash2,{className:"mr-2 h-4 w-4"}),"Supprimer"]})]})]})})]},e._id)})})]}),0===H.length&&(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"Aucune session trouvée"})})]})]})]})}}]);