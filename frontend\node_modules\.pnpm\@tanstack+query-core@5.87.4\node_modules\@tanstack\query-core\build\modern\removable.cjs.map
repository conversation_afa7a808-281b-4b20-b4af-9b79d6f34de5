{"version": 3, "sources": ["../../src/removable.ts"], "sourcesContent": ["import { timeoutManager } from './timeoutManager'\nimport { isServer, isValidTimeout } from './utils'\nimport type { ManagedTimerId } from './timeoutManager'\n\nexport abstract class Removable {\n  gcTime!: number\n  #gcTimeout?: ManagedTimerId\n\n  destroy(): void {\n    this.clearGcTimeout()\n  }\n\n  protected scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.gcTime)) {\n      this.#gcTimeout = timeoutManager.setTimeout(() => {\n        this.optionalRemove()\n      }, this.gcTime)\n    }\n  }\n\n  protected updateGcTime(newGcTime: number | undefined): void {\n    // Default to 5 minutes (Infinity for server-side) if no gcTime is set\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (isServer ? Infinity : 5 * 60 * 1000),\n    )\n  }\n\n  protected clearGcTimeout() {\n    if (this.#gcTimeout) {\n      timeoutManager.clearTimeout(this.#gcTimeout)\n      this.#gcTimeout = undefined\n    }\n  }\n\n  protected abstract optionalRemove(): void\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4BAA+B;AAC/B,mBAAyC;AAGlC,IAAe,YAAf,MAAyB;AAAA,EAE9B;AAAA,EAEA,UAAgB;AACd,SAAK,eAAe;AAAA,EACtB;AAAA,EAEU,aAAmB;AAC3B,SAAK,eAAe;AAEpB,YAAI,6BAAe,KAAK,MAAM,GAAG;AAC/B,WAAK,aAAa,qCAAe,WAAW,MAAM;AAChD,aAAK,eAAe;AAAA,MACtB,GAAG,KAAK,MAAM;AAAA,IAChB;AAAA,EACF;AAAA,EAEU,aAAa,WAAqC;AAE1D,SAAK,SAAS,KAAK;AAAA,MACjB,KAAK,UAAU;AAAA,MACf,cAAc,wBAAW,WAAW,IAAI,KAAK;AAAA,IAC/C;AAAA,EACF;AAAA,EAEU,iBAAiB;AACzB,QAAI,KAAK,YAAY;AACnB,2CAAe,aAAa,KAAK,UAAU;AAC3C,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAGF;", "names": []}