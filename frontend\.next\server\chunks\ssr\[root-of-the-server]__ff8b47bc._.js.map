{"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/dollar-sign.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/trash-2.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-label@2.1.7_261ca6dc9b795d3e6e9f99d20849d772/node_modules/@radix-ui/react-label/dist/index.mjs", "turbopack:///[project]/frontend/src/components/ui/label.tsx", "turbopack:///[project]/frontend/src/hooks/use-api.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/arrow-left.ts", "turbopack:///[project]/frontend/src/app/dashboard/sessions/[id]/edit/page.tsx"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 11v6', key: 'nco0om' }],\n  ['path', { d: 'M14 11v6', key: 'outv1u' }],\n  ['path', { d: 'M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6', key: 'miytrc' }],\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2', key: 'e791ji' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTF2NiIgLz4KICA8cGF0aCBkPSJNMTQgMTF2NiIgLz4KICA8cGF0aCBkPSJNMTkgNnYxNGEyIDIgMCAwIDEtMiAySDdhMiAyIDAgMCAxLTItMlY2IiAvPgogIDxwYXRoIGQ9Ik0zIDZoMTgiIC8+CiAgPHBhdGggZD0iTTggNlY0YTIgMiAwIDAgMSAyLTJoNGEyIDIgMCAwIDEgMiAydjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n", "\"use client\";\n\n// src/label.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Label\";\nvar Label = React.forwardRef((props, forwardedRef) => {\n  return /* @__PURE__ */ jsx(\n    Primitive.label,\n    {\n      ...props,\n      ref: forwardedRef,\n      onMouseDown: (event) => {\n        const target = event.target;\n        if (target.closest(\"button, input, select, textarea\")) return;\n        props.onMouseDown?.(event);\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }\n    }\n  );\n});\nLabel.displayName = NAME;\nvar Root = Label;\nexport {\n  Label,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\n\nimport { cn } from '@/lib/utils';\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot='label'\n      className={cn(\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Label };\n", "import { useSession } from \"next-auth/react\";\nimport { apiService } from \"@/lib/api\";\nimport {\n\tSession,\n\tCaisse,\n\tReunion,\n\tMember,\n\tPayment,\n\tSessionMember,\n\tCreateSessionDto,\n\tUpdateSessionDto,\n\tCreateCaisseDto,\n\tUpdateCaisseDto,\n\tUpdateReunionDto,\n\tCreateMemberDto,\n\tUpdateMemberDto,\n\tCreatePaymentDto,\n\tCreateSessionMemberDto,\n\tMemberDebrief,\n\tPaymentFilters,\n} from \"@/types\";\n\nexport function useApi() {\n\tconst { data: session } = useSession();\n\n\tconst authenticatedRequest = async <T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> => {\n\t\tif (!session?.accessToken) {\n\t\t\tthrow new Error(\"Non authentifié\");\n\t\t}\n\n\t\treturn apiService.authenticatedRequest<T>(\n\t\t\tendpoint,\n\t\t\tsession.accessToken,\n\t\t\toptions,\n\t\t);\n\t};\n\n\treturn {\n\t\t// Méthodes d'authentification (pas besoin de token)\n\t\tlogin: apiService.login.bind(apiService),\n\t\tregister: apiService.register.bind(apiService),\n\n\t\t// Méthodes authentifiées\n\t\tauthenticatedRequest,\n\n\t\t// Raccourcis pour les endpoints courants\n\t\tgetUsers: () => authenticatedRequest<any[]>(\"/users\"),\n\t\tgetUser: (id: string) => authenticatedRequest<any>(`/users/${id}`),\n\t\tcreateUser: (userData: any) =>\n\t\t\tauthenticatedRequest<any>(\"/users\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tupdateUser: (id: string, userData: any) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tdeleteUser: (id: string) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Sessions\n\t\tgetSessions: () => authenticatedRequest<Session[]>(\"/sessions\"),\n\t\tgetSession: (id: string) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`),\n\t\tcreateSession: (sessionData: CreateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(\"/sessions\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tupdateSession: (id: string, sessionData: UpdateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tdeleteSession: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Caisses\n\t\tgetCaisses: () => authenticatedRequest<Caisse[]>(\"/caisses\"),\n\t\tgetCaisse: (id: string) => authenticatedRequest<Caisse>(`/caisses/${id}`),\n\t\tcreateCaisse: (caisseData: CreateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(\"/caisses\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tupdateCaisse: (id: string, caisseData: UpdateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tdeleteCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\temargerCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}/emarger`, {\n\t\t\t\tmethod: \"POST\",\n\t\t\t}),\n\n\t\t// Réunions\n\t\tgetReunions: () => authenticatedRequest<Reunion[]>(\"/reunions\"),\n\t\tgetReunion: (id: string) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`),\n\t\tupdateReunion: (id: string, reunionData: UpdateReunionDto) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(reunionData),\n\t\t\t}),\n\n\t\t// Members\n\t\tgetMembers: () => authenticatedRequest<Member[]>(\"/members\"),\n\t\tgetMember: (id: string) => authenticatedRequest<Member>(`/members/${id}`),\n\t\tcreateMember: (memberData: CreateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(\"/members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tupdateMember: (id: string, memberData: UpdateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(`/members/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tdeleteMember: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/members/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\tgetMemberDebrief: (id: string, filters?: PaymentFilters) => {\n\t\t\tconst params = new URLSearchParams();\n\t\t\tif (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);\n\t\t\tif (filters?.dateTo) params.append('dateTo', filters.dateTo);\n\t\t\tif (filters?.sessionId) params.append('sessionId', filters.sessionId);\n\t\t\tconst query = params.toString() ? `?${params.toString()}` : '';\n\t\t\treturn authenticatedRequest<MemberDebrief>(`/members/${id}/debrief${query}`);\n\t\t},\n\n\t\t// Payments\n\t\tcreatePayment: (paymentData: CreatePaymentDto) =>\n\t\t\tauthenticatedRequest<Payment>(\"/payments\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(paymentData),\n\t\t\t}),\n\n\t\t// Session Members (inscription des membres aux sessions)\n\t\tgetSessionMembers: (sessionId: string) =>\n\t\t\tauthenticatedRequest<SessionMember[]>(`/sessions/${sessionId}/members`),\n\t\taddSessionMember: (sessionMemberData: CreateSessionMemberDto) =>\n\t\t\tauthenticatedRequest<SessionMember>(\"/session-members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionMemberData),\n\t\t\t}),\n\t\tremoveSessionMember: (sessionId: string, memberId: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${sessionId}/members/${memberId}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t};\n}\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n", "\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSession } from \"next-auth/react\";\nimport { useRouter, useParams } from \"next/navigation\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { ArrowLeft, Calendar, DollarSign, Trash2 } from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport {\n\tForm,\n\tFormControl,\n\tFormDescription,\n\tFormField,\n\tFormItem,\n\tFormLabel,\n\tFormMessage,\n} from \"@/components/ui/form\";\nimport { useApi } from \"@/hooks/use-api\";\nimport { Session, SessionForm } from \"@/types\";\n\nconst sessionSchema = z\n\t.object({\n\t\tannee: z\n\t\t\t.number()\n\t\t\t.min(2020, \"L'année doit être supérieure à 2020\")\n\t\t\t.max(2050, \"L'année doit être inférieure à 2050\"),\n\t\tdateDebut: z.string().min(1, \"La date de début est requise\"),\n\t\tdateFin: z.string().min(1, \"La date de fin est requise\"),\n\t\tpartFixe: z\n\t\t\t.number()\n\t\t\t.min(1, \"La part fixe doit être supérieure à 0\")\n\t\t\t.max(1000000, \"La part fixe ne peut pas dépasser 1,000,000 FCFA\"),\n\t})\n\t.refine(\n\t\t(data) => {\n\t\t\tconst debut = new Date(data.dateDebut);\n\t\t\tconst fin = new Date(data.dateFin);\n\t\t\treturn fin > debut;\n\t\t},\n\t\t{\n\t\t\tmessage: \"La date de fin doit être postérieure à la date de début\",\n\t\t\tpath: [\"dateFin\"],\n\t\t},\n\t);\n\nexport default function EditSessionPage() {\n\tconst { id: sessionId } = useParams();\n\tconst { data: session, status } = useSession();\n\tconst router = useRouter();\n\tconst api = useApi();\n\n\tconst [sessionData, setSessionData] = useState<Session | null>(null);\n\tconst [isLoading, setIsLoading] = useState(false);\n\tconst [isDeleting, setIsDeleting] = useState(false);\n\tconst [error, setError] = useState<string | null>(null);\n\tconst [loading, setLoading] = useState(true);\n\n\t// Vérifier les permissions\n\tconst canEditSessions =\n\t\tsession?.user && session.user.role === \"secretary_general\";\n\n\tconst form = useForm<SessionForm>({\n\t\tresolver: zodResolver(sessionSchema),\n\t\tdefaultValues: {\n\t\t\tannee: new Date().getFullYear(),\n\t\t\tdateDebut: \"\",\n\t\t\tdateFin: \"\",\n\t\t\tpartFixe: 0,\n\t\t},\n\t});\n\n\t// Charger les données de la session\n\tuseEffect(() => {\n\t\tconst loadSession = async () => {\n\t\t\tif (!sessionId || typeof sessionId !== \"string\") return;\n\n\t\t\ttry {\n\t\t\t\tsetLoading(true);\n\t\t\t\tconst data = await api.getSession(sessionId);\n\t\t\t\tsetSessionData(data);\n\n\t\t\t\t// Mettre à jour le formulaire\n\t\t\t\tform.reset({\n\t\t\t\t\tannee: data.annee,\n\t\t\t\t\tdateDebut: data.dateDebut.split(\"T\")[0], // Format YYYY-MM-DD\n\t\t\t\t\tdateFin: data.dateFin.split(\"T\")[0],\n\t\t\t\t\tpartFixe: data.partFixe,\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(\"Erreur lors du chargement:\", error);\n\t\t\t\tsetError(\"Session introuvable\");\n\t\t\t} finally {\n\t\t\t\tsetLoading(false);\n\t\t\t}\n\t\t};\n\n\t\tif (session?.accessToken) {\n\t\t\tloadSession();\n\t\t}\n\t}, [sessionId, status]);\n\n\tconst onSubmit = async (data: SessionForm) => {\n\t\tif (!canEditSessions || !sessionId || typeof sessionId !== \"string\") {\n\t\t\tsetError(\"Vous n'avez pas les permissions pour modifier cette session\");\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tsetIsLoading(true);\n\t\t\tsetError(null);\n\n\t\t\tawait api.updateSession(sessionId, data);\n\t\t\trouter.push(\"/dashboard/sessions\");\n\t\t} catch (error: any) {\n\t\t\tconsole.error(\"Erreur lors de la modification:\", error);\n\t\t\tsetError(\n\t\t\t\terror.message || \"Une erreur est survenue lors de la modification\",\n\t\t\t);\n\t\t} finally {\n\t\t\tsetIsLoading(false);\n\t\t}\n\t};\n\n\tconst handleDelete = async () => {\n\t\tif (\n\t\t\t!canEditSessions ||\n\t\t\t!sessionId ||\n\t\t\ttypeof sessionId !== \"string\" ||\n\t\t\t!sessionData\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (\n\t\t\t!confirm(\n\t\t\t\t`Êtes-vous sûr de vouloir supprimer la session ${sessionData.annee} ? Cette action supprimera également toutes les réunions associées.`,\n\t\t\t)\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tsetIsDeleting(true);\n\t\t\tawait api.deleteSession(sessionId);\n\t\t\trouter.push(\"/dashboard/sessions\");\n\t\t} catch (error: any) {\n\t\t\tconsole.error(\"Erreur lors de la suppression:\", error);\n\t\t\tsetError(\n\t\t\t\terror.message || \"Une erreur est survenue lors de la suppression\",\n\t\t\t);\n\t\t} finally {\n\t\t\tsetIsDeleting(false);\n\t\t}\n\t};\n\n\tif (loading) {\n\t\treturn (\n\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t<div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"></div>\n\t\t\t\t\t<p className=\"mt-2 text-sm text-gray-600\">\n\t\t\t\t\t\tChargement de la session...\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tif (!canEditSessions) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Button variant=\"outline\" size=\"icon\" asChild>\n\t\t\t\t\t\t<Link href=\"/dashboard/sessions\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4\" />\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</Button>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<h1 className=\"text-3xl font-bold tracking-tight\">\n\t\t\t\t\t\t\tModifier Session\n\t\t\t\t\t\t</h1>\n\t\t\t\t\t\t<p className=\"text-muted-foreground\">\n\t\t\t\t\t\t\tModifier les paramètres de la session\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<Card>\n\t\t\t\t\t<CardContent className=\"pt-6\">\n\t\t\t\t\t\t<div className=\"text-center py-8\">\n\t\t\t\t\t\t\t<p className=\"text-muted-foreground\">\n\t\t\t\t\t\t\t\tVous n'avez pas les permissions pour modifier cette session.\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t<p className=\"text-sm text-muted-foreground mt-2\">\n\t\t\t\t\t\t\t\tSeuls les administrateurs peuvent modifier les sessions.\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tif (!sessionData) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Button variant=\"outline\" size=\"icon\" asChild>\n\t\t\t\t\t\t<Link href=\"/dashboard/sessions\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4\" />\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</Button>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<h1 className=\"text-3xl font-bold tracking-tight\">\n\t\t\t\t\t\t\tSession introuvable\n\t\t\t\t\t\t</h1>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<Card>\n\t\t\t\t\t<CardContent className=\"pt-6\">\n\t\t\t\t\t\t<div className=\"text-center py-8\">\n\t\t\t\t\t\t\t<p className=\"text-muted-foreground\">\n\t\t\t\t\t\t\t\tLa session demandée n'a pas été trouvée.\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t</div>\n\t\t);\n\t}\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* En-tête */}\n\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Button variant=\"outline\" size=\"icon\" asChild>\n\t\t\t\t\t\t<Link href=\"/dashboard/sessions\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4\" />\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</Button>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<h1 className=\"text-3xl font-bold tracking-tight\">\n\t\t\t\t\t\t\tModifier Session {sessionData.annee}\n\t\t\t\t\t\t</h1>\n\t\t\t\t\t\t<p className=\"text-muted-foreground\">\n\t\t\t\t\t\t\tModifier les paramètres de la session\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<Button\n\t\t\t\t\tvariant=\"destructive\"\n\t\t\t\t\tonClick={handleDelete}\n\t\t\t\t\tdisabled={isDeleting}\n\t\t\t\t>\n\t\t\t\t\t<Trash2 className=\"mr-2 h-4 w-4\" />\n\t\t\t\t\t{isDeleting ? \"Suppression...\" : \"Supprimer\"}\n\t\t\t\t</Button>\n\t\t\t</div>\n\n\t\t\t{/* Formulaire */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle className=\"flex items-center gap-2\">\n\t\t\t\t\t\t<Calendar className=\"h-5 w-5\" />\n\t\t\t\t\t\tInformations de la session\n\t\t\t\t\t</CardTitle>\n\t\t\t\t\t<CardDescription>\n\t\t\t\t\t\tModifiez les paramètres de la session. Attention : les modifications\n\t\t\t\t\t\tpeuvent affecter les réunions existantes.\n\t\t\t\t\t</CardDescription>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<Form {...form}>\n\t\t\t\t\t\t<form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n\t\t\t\t\t\t\t{error && (\n\t\t\t\t\t\t\t\t<div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n\t\t\t\t\t\t\t\t\t<p className=\"text-sm text-red-600\">{error}</p>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t\t<div className=\"grid gap-6 md:grid-cols-2\">\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"annee\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Année</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) =>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield.onChange(parseInt(e.target.value) || 0)\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\tL'année de la session de tontine\n\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"partFixe\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t\t\t\t<DollarSign className=\"h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t\tPart Fixe (FCFA)\n\t\t\t\t\t\t\t\t\t\t\t</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) =>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield.onChange(parseInt(e.target.value) || 0)\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\tMontant de la cotisation fixe par réunion\n\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t<div className=\"grid gap-6 md:grid-cols-2\">\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"dateDebut\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Date de début</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Input type=\"date\" {...field} />\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\tDate de début de la session\n\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"dateFin\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Date de fin</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Input type=\"date\" {...field} />\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\tDate de fin de la session\n\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t<div className=\"flex justify-end gap-4\">\n\t\t\t\t\t\t\t\t<Button variant=\"outline\" asChild>\n\t\t\t\t\t\t\t\t\t<Link href=\"/dashboard/sessions\">Annuler</Link>\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t<Button type=\"submit\" disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t{isLoading ? \"Modification...\" : \"Modifier la session\"}\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</form>\n\t\t\t\t\t</Form>\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": "0PAmBA,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAAM,CAAA,EAAa,CAAA,CAAA,CAAA,CAAA,CAAb,AAAa,CAAb,AAAa,CAAb,AAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAhBA,CAgBe,AAfjD,CAeiD,AAfhD,CAegD,AAfhD,CAAA,AAegD,CAfhD,AAegD,CAAA,AAfhD,CAAA,AAegD,CAAA,AAfhD,CAAQ,AAewC,AAfhD,CAegD,AAfxC,AAAE,CAAA,AAegD,CAfhD,AAegD,CAf5C,CAAA,CAAA,CAAA,CAAA,CAAA,AAAM,CAAA,CAAA,CAAA,AAAI,CAAA,CAAA,CAAA,CAAA,CAAA,AAAM,EAAA,CAAA,AAAI,CAAA,CAAA,CAAA,CAAK,AAAL,CAAK,CAAA,CAAA,AAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACjE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAqD,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACpF,oDCgBA,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAAM,CAAA,EAAS,CAAA,AAAT,CAAS,AAAT,CAAS,AAAT,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAnBI,CAClC,AAkByC,CAlBxC,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAAQ,AAkBgC,CAlBhC,AAAE,AAkB8B,CAAU,CAAA,AAlBrC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAY,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACzC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAY,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACzC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAA4C,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACzE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,AAAX,CAAW,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACxC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAA0C,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACzE,sECNA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OAGI,EAAQ,EAAA,UAAgB,CAAC,CAAC,EAAO,IACZ,CAAA,EAAA,EAAA,GAAA,AAAG,EACxB,EAAA,SAAS,CAAC,KAAK,CACf,CACE,GAAG,CAAK,CACR,IAAK,EACL,YAAa,AAAC,IACG,AACX,EADiB,MAAM,CAChB,OAAO,CAAC,oCAAoC,CACvD,EAAM,WAAW,GAAG,GAChB,CAAC,EAAM,gBAAgB,EAAI,EAAM,MAAM,CAAG,GAAG,EAAM,cAAc,GACvE,CACF,IAGJ,EAAM,WAAW,CAhBN,EAgBS,MCjBpB,IAAA,EAAA,EAAA,CAAA,CAAA,OAEA,SAAS,EAAM,WACb,CAAS,CACT,GAAG,EAC8C,EACjD,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,ADWM,ECXN,CACC,YAAU,QACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,sNACA,GAED,GAAG,CAAK,EAGf,gDCrBA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAqBO,SAAS,IACf,GAAM,CAAE,KAAM,CAAO,CAAE,CAAG,CAAA,EAAA,EAAA,UAAA,AAAU,IAE9B,EAAuB,MAC5B,EACA,EAAuB,CAAC,CAAC,IAEzB,GAAI,CAAC,GAAS,YACb,CAD0B,KACpB,AAAI,MAAM,mBAGjB,OAAO,EAAA,UAAU,CAAC,oBAAoB,CACrC,EACA,EAAQ,WAAW,CACnB,EAEF,EAEA,MAAO,CAEN,MAAO,EAAA,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAA,UAAU,EACvC,SAAU,EAAA,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAA,UAAU,EAG7C,uBAGA,SAAU,IAAM,EAA4B,UAC5C,QAAS,AAAC,GAAe,EAA0B,CAAC,OAAO,EAAE,EAAA,CAAI,EACjE,WAAY,AAAC,GACZ,EAA0B,SAAU,CACnC,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,WAAY,CAAC,EAAY,IACxB,EAA0B,CAAC,OAAO,EAAE,EAAA,CAAI,CAAE,CACzC,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,WAAY,AAAC,GACZ,EAA0B,CAAC,OAAO,EAAE,EAAA,CAAI,CAAE,CACzC,OAAQ,QACT,GAGD,YAAa,IAAM,EAAgC,aACnD,WAAY,AAAC,GACZ,EAA8B,CAAC,UAAU,EAAE,EAAA,CAAI,EAChD,cAAe,AAAC,GACf,EAA8B,YAAa,CAC1C,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,cAAe,CAAC,EAAY,IAC3B,EAA8B,CAAC,UAAU,EAAE,EAAA,CAAI,CAAE,CAChD,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,cAAe,AAAC,GACf,EAA2B,CAAC,UAAU,EAAE,EAAA,CAAI,CAAE,CAC7C,OAAQ,QACT,GAGD,WAAY,IAAM,EAA+B,YACjD,UAAW,AAAC,GAAe,EAA6B,CAAC,SAAS,EAAE,EAAA,CAAI,EACxE,aAAc,AAAC,GACd,EAA6B,WAAY,CACxC,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,aAAc,CAAC,EAAY,IAC1B,EAA6B,CAAC,SAAS,EAAE,EAAA,CAAI,CAAE,CAC9C,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,aAAc,AAAC,GACd,EAA2B,CAAC,SAAS,EAAE,EAAA,CAAI,CAAE,CAC5C,OAAQ,QACT,GACD,cAAe,AAAC,GACf,EAA6B,CAAC,SAAS,EAAE,EAAG,QAAQ,CAAC,CAAE,CACtD,OAAQ,MACT,GAGD,YAAa,IAAM,EAAgC,aACnD,WAAY,AAAC,GACZ,EAA8B,CAAC,UAAU,EAAE,EAAA,CAAI,EAChD,cAAe,CAAC,EAAY,IAC3B,EAA8B,CAAC,UAAU,EAAE,EAAA,CAAI,CAAE,CAChD,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GAGD,WAAY,IAAM,EAA+B,YACjD,UAAW,AAAC,GAAe,EAA6B,CAAC,SAAS,EAAE,EAAA,CAAI,EACxE,aAAc,AAAC,GACd,EAA6B,WAAY,CACxC,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,aAAc,CAAC,EAAY,IAC1B,EAA6B,CAAC,SAAS,EAAE,EAAA,CAAI,CAAE,CAC9C,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,aAAc,AAAC,GACd,EAA2B,CAAC,SAAS,EAAE,EAAA,CAAI,CAAE,CAC5C,OAAQ,QACT,GACD,iBAAkB,CAAC,EAAY,KAC9B,IAAM,EAAS,IAAI,gBACf,GAAS,UAAU,EAAO,MAAM,CAAC,WAAY,EAAQ,QAAQ,EAC7D,GAAS,QAAQ,EAAO,MAAM,CAAC,SAAU,EAAQ,MAAM,EACvD,GAAS,WAAW,EAAO,MAAM,CAAC,YAAa,EAAQ,SAAS,EACpE,IAAM,EAAQ,EAAO,QAAQ,GAAK,CAAC,CAAC,EAAE,EAAO,QAAQ,GAAA,CAAI,CAAG,GAC5D,OAAO,EAAoC,CAAC,SAAS,EAAE,EAAG,QAAQ,EAAE,EAAA,CAAO,CAC5E,EAGA,cAAe,AAAC,GACf,EAA8B,YAAa,CAC1C,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GAGD,kBAAmB,AAAC,GACnB,EAAsC,CAAC,UAAU,EAAE,EAAU,QAAQ,CAAC,EACvE,iBAAkB,AAAC,GAClB,EAAoC,mBAAoB,CACvD,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,oBAAqB,CAAC,EAAmB,IACxC,EAA2B,CAAC,UAAU,EAAE,EAAU,SAAS,EAAE,EAAA,CAAU,CAAE,CACxE,OAAQ,QACT,EACF,CACD,uDChJA,CAAA,CAAA,CAAA,CAAM,AAAN,CAAA,CAAM,CAAA,EAAY,CAAA,CAAA,CAAA,CAAZ,AAAY,CAAZ,AAAY,CAAZ,AAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAhBC,CAClC,AAe+C,CAf9C,AAe8C,CAf9C,AAe8C,CAf9C,AAe8C,CAAA,AAf9C,CAAA,AAe8C,CAf9C,AAe8C,CAf9C,AAe8C,CAf9C,AAAQ,AAesC,CAftC,AAAE,AAeoC,CAAU,CAf3C,AAe2C,CAf3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAkB,CAAA,CAAA,CAAA,CAAK,AAAL,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAC/C,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAY,CAAA,CAAA,CAAA,CAAK,AAAL,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAC3C,iECJA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAOA,EAAA,EAAA,CAAA,CAAA,OASA,EAAA,EAAA,CAAA,CAAA,OAGA,IAAM,EAAgB,EAAA,CAAC,CACrB,MAAM,CAAC,CACP,MAAO,EAAA,CAAC,CACN,MAAM,GACN,GAAG,CAAC,KAAM,uCACV,GAAG,CAAC,KAAM,uCACZ,UAAW,EAAA,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,EAAG,gCAC7B,QAAS,EAAA,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,EAAG,8BAC3B,SAAU,EAAA,CAAC,CACT,MAAM,GACN,GAAG,CAAC,EAAG,yCACP,GAAG,CAAC,IAAS,mDAChB,GACC,MAAM,CACL,AAAD,IACC,IAAM,EAAQ,IAAI,KAAK,EAAK,SAAS,EAErC,OADY,AACL,IADS,KAAK,EAAK,OAAO,EACpB,CACd,EACA,CACC,QAAS,0DACT,KAAM,CAAC,UACR,AADkB,GAIL,SAAS,IACvB,GAAM,CAAE,GAAI,CAAS,CAAE,CAAG,CAAA,EAAA,EAAA,SAAS,AAAT,IACpB,CAAE,KAAM,CAAO,QAAE,CAAM,CAAE,CAAG,CAAA,EAAA,EAAA,UAAA,AAAU,IACtC,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAM,CAAA,EAAA,EAAA,MAAA,AAAM,IAEZ,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAiB,MACzD,CAAC,EAAW,EAAa,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACrC,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACvC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAwB,MAC5C,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAGjC,EACL,GAAS,MAA8B,sBAAtB,EAAQ,IAAI,CAAC,IAAI,CAE7B,EAAO,CAAA,EAAA,EAAA,OAAA,AAAO,EAAc,CACjC,SAAU,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,GACtB,cAAe,CACd,MAAO,IAAI,OAAO,WAAW,GAC7B,UAAW,GACX,QAAS,GACT,SAAU,CACX,CACD,GAGA,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACT,IAAM,EAAc,UACnB,GAAI,AAAC,GAAkC,UAArB,AAA+B,OAAxB,EAEzB,GAAI,CACH,GAAW,GACX,IAAM,EAAO,MAAM,EAAI,UAAU,CAAC,GAClC,EAAe,GAGf,EAAK,KAAK,CAAC,CACV,MAAO,EAAK,KAAK,CACjB,UAAW,EAAK,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CACvC,QAAS,EAAK,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CACnC,SAAU,EAAK,QAAQ,AACxB,EACD,CAAE,MAAO,EAAO,CACf,QAAQ,KAAK,CAAC,6BAA8B,GAC5C,EAAS,sBACV,QAAU,CACT,GAAW,EACZ,CACD,EAEI,GAAS,aACZ,AADyB,GAG3B,EAAG,CAAC,EAAW,EAAO,EAEtB,IAAM,EAAW,MAAO,IACvB,GAAI,CAAC,GAAmB,CAAC,GAAkC,UAArB,OAAO,EAAwB,YACpE,EAAS,+DAIV,GAAI,CACH,GAAa,GACb,EAAS,MAET,MAAM,EAAI,aAAa,CAAC,EAAW,GACnC,EAAO,IAAI,CAAC,sBACb,CAAE,MAAO,EAAY,CACpB,QAAQ,KAAK,CAAC,kCAAmC,GACjD,EACC,EAAM,OAAO,EAAI,kDAEnB,QAAU,CACT,GAAa,EACd,CACD,EAEM,EAAe,UACpB,GACC,AAAC,GACA,GACoB,UAArB,EACA,CAFA,IACO,GACN,GAMA,QACA,CAAC,CAND,mDAM+C,EAAE,EAAY,KAAK,CAAC,4EAAmE,CAAC,EAMzI,CAJE,EAIE,CACH,GAAc,GACd,MAAM,EAAI,aAAa,CAAC,GACxB,EAAO,IAAI,CAAC,sBACb,CAAE,MAAO,EAAY,CACpB,QAAQ,KAAK,CAAC,iCAAkC,GAChD,EACC,EAAM,OAAO,EAAI,iDAEnB,QAAU,CACT,EAAc,GACf,CACD,SAEA,AAAI,EAEF,CAAA,EAAA,EAAA,EAFW,CAEX,EAAC,MAAA,CAAI,UAAU,iDACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yEACf,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,qCAQzC,EAmCA,EA8BJ,CAAA,EAAA,EAAA,IAAA,EA9BiB,AA8BhB,EAjEoB,IAiEpB,CAAI,UAAU,sBAEd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,KAAK,OAAO,OAAO,CAAA,CAAA,WAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,+BACV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,gBAGvB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,8CAAoC,oBAC/B,EAAY,KAAK,IAEpC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,gDAMvC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CACN,QAAQ,cACR,QAAS,EACT,SAAU,YAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,iBACjB,EAAa,iBAAmB,kBAKnC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,WACV,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,oCACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,YAAY,gCAGjC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,sHAKlB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAE,GAAG,CAAI,UACb,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SAAU,EAAK,YAAY,CAAC,GAAW,UAAU,sBACrD,GACA,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0DACd,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,gCAAwB,MAIvC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,QACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACL,KAAK,SACJ,GAAG,CAAK,CACT,SAAU,AAAC,GACV,EAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,GAAK,OAI9C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,qCAGjB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,QAKf,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,WACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,oCACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,YAAY,sBAGnC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACL,KAAK,SACJ,GAAG,CAAK,CACT,SAAU,AAAC,GACV,EAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,GAAK,OAI9C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,8CAGjB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,WAMhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,YACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,kBACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,KAAK,OAAQ,GAAG,CAAK,KAE7B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,gCAGjB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,QAKf,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CACT,QAAS,EAAK,OAAO,CACrB,KAAK,UACL,OAAQ,CAAC,OAAE,CAAK,CAAE,GACjB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,gBACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,KAAK,OAAQ,GAAG,CAAK,KAE7B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,eAAe,CAAA,UAAC,8BAGjB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAA,WAMhB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,OAAO,CAAA,CAAA,WAChC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,+BAAsB,cAElC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,KAAK,SAAS,SAAU,WAC9B,EAAY,kBAAoB,wCA1KvC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,KAAK,OAAO,OAAO,CAAA,CAAA,WAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,+BACV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,gBAGvB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,UACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6CAAoC,6BAMpD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,UACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,gBACtB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACd,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,sDApDzC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,KAAK,OAAO,OAAO,CAAA,CAAA,WAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,+BACV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,gBAGvB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6CAAoC,qBAGlD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,gDAMvC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,UACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,gBACtB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACd,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,iEAGrC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8CAAqC,sEA+LzD", "ignoreList": [0, 1, 2, 5]}