{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card'\n      className={cn(\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-header'\n      className={cn(\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-title'\n      className={cn('leading-none font-semibold', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-description'\n      className={cn('text-muted-foreground text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-action'\n      className={cn(\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-content'\n      className={cn('px-6', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-footer'\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,wUAAC;QACC,aAAU;QACV,WAAW,IAAA,wIAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\n\nimport { cn } from '@/lib/utils';\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot='label'\n      className={cn(\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Label };\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,wUAAC,qSAAmB;QAClB,aAAU;QACV,WAAW,IAAA,wIAAE,EACX,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/form.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { Slot } from '@radix-ui/react-slot';\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues\n} from 'react-hook-form';\n\nimport { cn } from '@/lib/utils';\nimport { Label } from '@/components/ui/label';\n\nconst Form = FormProvider;\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n> = {\n  name: TName;\n};\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n);\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  );\n};\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext);\n  const itemContext = React.useContext(FormItemContext);\n  const { getFieldState } = useFormContext();\n  const formState = useFormState({ name: fieldContext.name });\n  const fieldState = getFieldState(fieldContext.name, formState);\n\n  if (!fieldContext) {\n    throw new Error('useFormField should be used within <FormField>');\n  }\n\n  const { id } = itemContext;\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState\n  };\n};\n\ntype FormItemContextValue = {\n  id: string;\n};\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n);\n\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\n  const id = React.useId();\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot='form-item'\n        className={cn('grid gap-2', className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  );\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField();\n\n  return (\n    <Label\n      data-slot='form-label'\n      data-error={!!error}\n      className={cn('data-[error=true]:text-destructive', className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  );\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } =\n    useFormField();\n\n  return (\n    <Slot\n      data-slot='form-control'\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  );\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\n  const { formDescriptionId } = useFormField();\n\n  return (\n    <p\n      data-slot='form-description'\n      id={formDescriptionId}\n      className={cn('text-muted-foreground text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\n  const { error, formMessageId } = useFormField();\n  const body = error ? String(error?.message ?? '') : props.children;\n\n  if (!body) {\n    return null;\n  }\n\n  return (\n    <p\n      data-slot='form-message'\n      id={formMessageId}\n      className={cn('text-destructive text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,sRAAY;AASzB,MAAM,iCAAmB,yTAAmB,CAC1C,CAAC;AAGH,MAAM,YAAY;QAGhB,EACA,GAAG,OACkC;IACrC,qBACE,wUAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,wUAAC,oRAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,sTAAgB,CAAC;IACtC,MAAM,cAAc,sTAAgB,CAAC;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,IAAA,wRAAc;IACxC,MAAM,YAAY,IAAA,sRAAY,EAAC;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,AAAC,GAAK,OAAH,IAAG;QAClB,mBAAmB,AAAC,GAAK,OAAH,IAAG;QACzB,eAAe,AAAC,GAAK,OAAH,IAAG;QACrB,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,wRAAc;QACtB,sRAAY;;;AAuBhC,MAAM,gCAAkB,yTAAmB,CACzC,CAAC;AAGH,SAAS,SAAS,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;;IAChB,MAAM,KAAK,iTAAW;IAEtB,qBACE,wUAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,wUAAC;YACC,aAAU;YACV,WAAW,IAAA,wIAAE,EAAC,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,KAGgC;QAHhC,EACjB,SAAS,EACT,GAAG,OAC8C,GAHhC;;IAIjB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,wUAAC,yJAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,IAAA,wIAAE,EAAC,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,KAA+C;QAA/C,EAAE,GAAG,OAA0C,GAA/C;;IACnB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAC3D;IAEF,qBACE,wUAAC,4TAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,AAAC,GAAoB,OAAlB,qBACH,AAAC,GAAuB,OAArB,mBAAkB,KAAiB,OAAd;QAE9B,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAjBS;;QAEL;;;MAFK;AAmBT,SAAS,gBAAgB,KAAkD;QAAlD,EAAE,SAAS,EAAE,GAAG,OAAkC,GAAlD;;IACvB,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,wUAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,IAAA,wIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,KAAkD;QAAlD,EAAE,SAAS,EAAE,GAAG,OAAkC,GAAlD;;IACnB,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;QACL;IAA5B,MAAM,OAAO,QAAQ,OAAO,CAAA,iBAAA,kBAAA,4BAAA,MAAO,OAAO,cAAd,4BAAA,iBAAkB,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,wUAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,IAAA,wIAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;IAhBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/auth.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from \"next-auth/providers/credentials\";\nimport { z } from \"zod\";\nimport { apiService } from \"./api\";\nimport { decodeJwt } from \"./utils\";\n\nconst loginSchema = z.object({\n\tusername: z.string().min(1, \"Username is required\"),\n\tpassword: z.string().min(1, \"Password is required\"),\n});\n\nexport const { handlers, signIn, signOut, auth } = NextAuth({\n\tproviders: [\n\t\tCredentialsProvider({\n\t\t\tname: \"credentials\",\n\t\t\tcredentials: {\n\t\t\t\tusername: { label: \"Username\", type: \"text\" },\n\t\t\t\tpassword: { label: \"Password\", type: \"password\" },\n\t\t\t},\n\t\t\tasync authorize(credentials) {\n\t\t\t\ttry {\n\t\t\t\t\tconst { username, password } = loginSchema.parse(credentials);\n\n\t\t\t\t\t// Authentification avec l'API backend\n\t\t\t\t\tconst response = await apiService.login({ username, password });\n\t\t\t\t\tconst jwt = decodeJwt(response.access_token);\n\t\t\t\t\tif(!jwt) return null;\n\t\t\t\t\tconst jwtPayload = jwt.payload as unknown as { sub: string; username: string; role:  'secretary_general' | 'controller' | 'cashier' };\n\t\t\t\t\t\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tid: jwtPayload.sub,\n\t\t\t\t\t\t\tusername: jwtPayload.username,\n\t\t\t\t\t\t\trole: jwtPayload.role,\n\t\t\t\t\t\t\taccessToken: response.access_token,\n\t\t\t\t\t}\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"Erreur d'authentification:\", error);\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\t\t}),\n\t],\n\tpages: {\n\t\tsignIn: \"/auth/signin\",\n\t},\n\tcallbacks: {\n\t\tauthorized: ({ auth }) => !!auth,\n\t\tasync jwt({ token, user }) {\n\t\t\tif (user) {\n\t\t\t\ttoken.username = user.username;\n\t\t\t\ttoken.role = user.role;\n\t\t\t\ttoken.accessToken = user.accessToken;\n\t\t\t}\n\t\t\treturn token;\n\t\t},\n\t\tasync session({ session, token }) {\n\t\t\tif (token) {\n\t\t\t\tsession.user.id = token.sub || \"\";\n\t\t\t\tsession.user.username = token.username as string;\n\t\t\t\tsession.user.role = token.role as  'secretary_general' | 'controller' | 'cashier';\n\t\t\t\tsession.accessToken = token.accessToken as string;\n\t\t\t}\n\t\t\treturn session;\n\t\t},\n\t},\n\tsession: {\n\t\tstrategy: \"jwt\",\n\t},\n});\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,cAAc,iPAAC,CAAC,MAAM,CAAC;IAC5B,UAAU,iPAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,UAAU,iPAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC7B;AAEO,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAA,2RAAQ,EAAC;IAC3D,WAAW;QACV,IAAA,gPAAmB,EAAC;YACnB,MAAM;YACN,aAAa;gBACZ,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAO;gBAC5C,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YACjD;YACA,MAAM,WAAU,WAAW;gBAC1B,IAAI;oBACH,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,YAAY,KAAK,CAAC;oBAEjD,sCAAsC;oBACtC,MAAM,WAAW,MAAM,8IAAU,CAAC,KAAK,CAAC;wBAAE;wBAAU;oBAAS;oBAC7D,MAAM,MAAM,IAAA,+IAAS,EAAC,SAAS,YAAY;oBAC3C,IAAG,CAAC,KAAK,OAAO;oBAChB,MAAM,aAAa,IAAI,OAAO;oBAE7B,OAAO;wBACN,IAAI,WAAW,GAAG;wBAClB,UAAU,WAAW,QAAQ;wBAC7B,MAAM,WAAW,IAAI;wBACrB,aAAa,SAAS,YAAY;oBACpC;gBAED,EAAE,OAAO,OAAO;oBACf,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,OAAO;gBACR;YACD;QACD;KACA;IACD,OAAO;QACN,QAAQ;IACT;IACA,WAAW;QACV,YAAY;gBAAC,EAAE,IAAI,EAAE;mBAAK,CAAC,CAAC;;QAC5B,MAAM,KAAI,KAAe;gBAAf,EAAE,KAAK,EAAE,IAAI,EAAE,GAAf;YACT,IAAI,MAAM;gBACT,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,WAAW,GAAG,KAAK,WAAW;YACrC;YACA,OAAO;QACR;QACA,MAAM,SAAQ,KAAkB;gBAAlB,EAAE,OAAO,EAAE,KAAK,EAAE,GAAlB;YACb,IAAI,OAAO;gBACV,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI;gBAC/B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,WAAW,GAAG,MAAM,WAAW;YACxC;YACA,OAAO;QACR;IACD;IACA,SAAS;QACR,UAAU;IACX;AACD", "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/api.ts"], "sourcesContent": ["import { signOut } from \"next-auth/react\";\nimport { signOut as ServerSignOut } from \"./auth\";\n\n// Configuration de l'API\nexport const API_BASE_URL =\n\tprocess.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000\";\n\n// Types pour l'authentification\nexport interface LoginRequest {\n\tusername: string;\n\tpassword: string;\n}\n\nexport interface LoginResponse {\n\taccess_token: string;\n\tuser: {\n\t\tid: string;\n\t\tusername: string;\n\t\tnom: string;\n\t\tprenom: string;\n\t\temail: string;\n\t\trole: \"admin\" | \"tresorier\" | \"membre\";\n\t\tstatut: \"actif\" | \"en_attente\" | \"suspendu\";\n\t};\n}\n\nexport interface RegisterRequest {\n\tusername: string;\n\tpassword: string;\n\trole: string;\n\t// statut?: \"actif\" | \"en_attente\" | \"suspendu\";\n}\n\nexport interface ApiError {\n\tmessage: string;\n\tstatusCode: number;\n\terror?: string;\n}\n\n// Service API\nexport class ApiService {\n\tprivate baseUrl: string;\n\n\tconstructor(baseUrl: string = API_BASE_URL) {\n\t\tthis.baseUrl = baseUrl;\n\t}\n\n\tprivate async request<T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\tconst url = `${this.baseUrl}${endpoint}`;\n\n\t\tconst config: RequestInit = {\n\t\t\theaders: {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t...options.headers,\n\t\t\t},\n\t\t\t...options,\n\t\t};\n\n\t\ttry {\n\t\t\tconst response = await fetch(url, config);\n\n\t\t\tif (!response.ok) {\n\t\t\t\tconst errorData: ApiError = await response.json().catch(() => ({\n\t\t\t\t\tmessage: \"Une erreur est survenue\",\n\t\t\t\t\tstatusCode: response.status,\n\t\t\t\t}));\n\n\t\t\t\t// 🚨 Gestion du cas 401\n\t\t\t\tif (response.status === 401) {\n\t\t\t\t\t// Si côté client → on déconnecte\n\t\t\t\t\tif (typeof window !== \"undefined\") {\n\t\t\t\t\t\tawait signOut({ callbackUrl: \"/auth/signin\" });\n\t\t\t\t\t} else {\n\t\t\t\t\t\tawait ServerSignOut({ redirectTo: \"/auth/signin\" });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthrow new Error(errorData.message || `HTTP ${response.status}`);\n\t\t\t}\n\n\t\t\treturn await response.json();\n\t\t} catch (error) {\n\t\t\tconsole.log(error);\n\n\t\t\tif (error instanceof Error) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t\tthrow new Error(\"Erreur de connexion au serveur\");\n\t\t}\n\t}\n\n\t// Authentification\n\tasync login(credentials: LoginRequest): Promise<LoginResponse> {\n\t\treturn this.request<LoginResponse>(\"/auth/login\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(credentials),\n\t\t});\n\t}\n\n\tasync register(\n\t\tuserData: RegisterRequest,\n\t): Promise<{ message: string; user: any }> {\n\t\treturn this.request<{ message: string; user: any }>(\"/auth/register\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(userData),\n\t\t});\n\t}\n\n\t// Méthodes avec authentification\n\tasync authenticatedRequest<T>(\n\t\tendpoint: string,\n\t\ttoken: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\treturn this.request<T>(endpoint, {\n\t\t\t...options,\n\t\t\theaders: {\n\t\t\t\t...options.headers,\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\tAuthorization: `Bearer ${token}`,\n\t\t\t},\n\t\t});\n\t}\n\n\t// Utilisateurs\n\tasync getUsers(token: string): Promise<any[]> {\n\t\treturn this.authenticatedRequest<any[]>(\"/users\", token);\n\t}\n\n\tasync getUser(id: string, token: string): Promise<any> {\n\t\treturn this.authenticatedRequest<any>(`/users/${id}`, token);\n\t}\n}\n\n// Instance par défaut\nexport const apiService = new ApiService();\n"], "names": [], "mappings": ";;;;;;;;AAKC;;AALD;AACA;;;;AAGO,MAAM,eACZ,sTAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAmC7B,MAAM;IAOZ,MAAc,QACb,QAAgB,EAEH;YADb,UAAA,iEAAuB,CAAC;QAExB,MAAM,MAAM,AAAC,GAAiB,OAAf,IAAI,CAAC,OAAO,EAAY,OAAT;QAE9B,MAAM,SAAsB;YAC3B,SAAS;gBACR,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACnB;YACA,GAAG,OAAO;QACX;QAEA,IAAI;YACH,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACjB,MAAM,YAAsB,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;wBAC9D,SAAS;wBACT,YAAY,SAAS,MAAM;oBAC5B,CAAC;gBAED,wBAAwB;gBACxB,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC5B,iCAAiC;oBACjC,wCAAmC;wBAClC,MAAM,IAAA,2QAAO,EAAC;4BAAE,aAAa;wBAAe;oBAC7C;;gBAGD;gBACA,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,AAAC,QAAuB,OAAhB,SAAS,MAAM;YAC7D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC3B,EAAE,OAAO,OAAO;YACf,QAAQ,GAAG,CAAC;YAEZ,IAAI,iBAAiB,OAAO;gBAC3B,MAAM;YACP;YACA,MAAM,IAAI,MAAM;QACjB;IACD;IAEA,mBAAmB;IACnB,MAAM,MAAM,WAAyB,EAA0B;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAgB,eAAe;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,MAAM,SACL,QAAyB,EACiB;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAiC,kBAAkB;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,iCAAiC;IACjC,MAAM,qBACL,QAAgB,EAChB,KAAa,EAEA;YADb,UAAA,iEAAuB,CAAC;QAExB,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAChC,GAAG,OAAO;YACV,SAAS;gBACR,GAAG,QAAQ,OAAO;gBAClB,gBAAgB;gBAChB,eAAe,AAAC,UAAe,OAAN;YAC1B;QACD;IACD;IAEA,eAAe;IACf,MAAM,SAAS,KAAa,EAAkB;QAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAQ,UAAU;IACnD;IAEA,MAAM,QAAQ,EAAU,EAAE,KAAa,EAAgB;QACtD,OAAO,IAAI,CAAC,oBAAoB,CAAM,AAAC,UAAY,OAAH,KAAM;IACvD;IA1FA,YAAY,UAAkB,YAAY,CAAE;QAF5C,yPAAQ,WAAR,KAAA;QAGC,IAAI,CAAC,OAAO,GAAG;IAChB;AAyFD;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/use-api.ts"], "sourcesContent": ["import { useSession } from \"next-auth/react\";\nimport { apiService } from \"@/lib/api\";\nimport {\n\tSession,\n\tCaisse,\n\tReunion,\n\tMember,\n\tPayment,\n\tSessionMember,\n\tCreateSessionDto,\n\tUpdateSessionDto,\n\tCreateCaisseDto,\n\tUpdateCaisseDto,\n\tUpdateReunionDto,\n\tCreateMemberDto,\n\tUpdateMemberDto,\n\tCreatePaymentDto,\n\tCreateSessionMemberDto,\n\tMemberDebrief,\n\tPaymentFilters,\n} from \"@/types\";\n\nexport function useApi() {\n\tconst { data: session } = useSession();\n\n\tconst authenticatedRequest = async <T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> => {\n\t\tif (!session?.accessToken) {\n\t\t\tthrow new Error(\"Non authentifié\");\n\t\t}\n\n\t\treturn apiService.authenticatedRequest<T>(\n\t\t\tendpoint,\n\t\t\tsession.accessToken,\n\t\t\toptions,\n\t\t);\n\t};\n\n\treturn {\n\t\t// Méthodes d'authentification (pas besoin de token)\n\t\tlogin: apiService.login.bind(apiService),\n\t\tregister: apiService.register.bind(apiService),\n\n\t\t// Méthodes authentifiées\n\t\tauthenticatedRequest,\n\n\t\t// Raccourcis pour les endpoints courants\n\t\tgetUsers: () => authenticatedRequest<any[]>(\"/users\"),\n\t\tgetUser: (id: string) => authenticatedRequest<any>(`/users/${id}`),\n\t\tcreateUser: (userData: any) =>\n\t\t\tauthenticatedRequest<any>(\"/users\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tupdateUser: (id: string, userData: any) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tdeleteUser: (id: string) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Sessions\n\t\tgetSessions: () => authenticatedRequest<Session[]>(\"/sessions\"),\n\t\tgetSession: (id: string) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`),\n\t\tcreateSession: (sessionData: CreateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(\"/sessions\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tupdateSession: (id: string, sessionData: UpdateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tdeleteSession: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Caisses\n\t\tgetCaisses: () => authenticatedRequest<Caisse[]>(\"/caisses\"),\n\t\tgetCaisse: (id: string) => authenticatedRequest<Caisse>(`/caisses/${id}`),\n\t\tcreateCaisse: (caisseData: CreateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(\"/caisses\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tupdateCaisse: (id: string, caisseData: UpdateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tdeleteCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\temargerCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}/emarger`, {\n\t\t\t\tmethod: \"POST\",\n\t\t\t}),\n\n\t\t// Réunions\n\t\tgetReunions: () => authenticatedRequest<Reunion[]>(\"/reunions\"),\n\t\tgetReunion: (id: string) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`),\n\t\tupdateReunion: (id: string, reunionData: UpdateReunionDto) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(reunionData),\n\t\t\t}),\n\n\t\t// Members\n\t\tgetMembers: () => authenticatedRequest<Member[]>(\"/members\"),\n\t\tgetMember: (id: string) => authenticatedRequest<Member>(`/members/${id}`),\n\t\tcreateMember: (memberData: CreateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(\"/members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tupdateMember: (id: string, memberData: UpdateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(`/members/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tdeleteMember: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/members/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\tgetMemberDebrief: (id: string, filters?: PaymentFilters) => {\n\t\t\tconst params = new URLSearchParams();\n\t\t\tif (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);\n\t\t\tif (filters?.dateTo) params.append('dateTo', filters.dateTo);\n\t\t\tif (filters?.sessionId) params.append('sessionId', filters.sessionId);\n\t\t\tconst query = params.toString() ? `?${params.toString()}` : '';\n\t\t\treturn authenticatedRequest<MemberDebrief>(`/members/${id}/debrief${query}`);\n\t\t},\n\n\t\t// Payments\n\t\tcreatePayment: (paymentData: CreatePaymentDto) =>\n\t\t\tauthenticatedRequest<Payment>(\"/payments\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(paymentData),\n\t\t\t}),\n\n\t\t// Session Members (inscription des membres aux sessions)\n\t\tgetSessionMembers: (sessionId: string) =>\n\t\t\tauthenticatedRequest<SessionMember[]>(`/sessions/${sessionId}/members`),\n\t\taddSessionMember: (sessionMemberData: CreateSessionMemberDto) =>\n\t\t\tauthenticatedRequest<SessionMember>(\"/session-members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionMemberData),\n\t\t\t}),\n\t\tremoveSessionMember: (sessionId: string, memberId: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${sessionId}/members/${memberId}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t};\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAqBO,SAAS;;IACf,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IAEpC,MAAM,uBAAuB,eAC5B;YACA,2EAAuB,CAAC;QAExB,IAAI,EAAC,oBAAA,8BAAA,QAAS,WAAW,GAAE;YAC1B,MAAM,IAAI,MAAM;QACjB;QAEA,OAAO,8IAAU,CAAC,oBAAoB,CACrC,UACA,QAAQ,WAAW,EACnB;IAEF;IAEA,OAAO;QACN,oDAAoD;QACpD,OAAO,8IAAU,CAAC,KAAK,CAAC,IAAI,CAAC,8IAAU;QACvC,UAAU,8IAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,8IAAU;QAE7C,yBAAyB;QACzB;QAEA,yCAAyC;QACzC,UAAU,IAAM,qBAA4B;QAC5C,SAAS,CAAC,KAAe,qBAA0B,AAAC,UAAY,OAAH;QAC7D,YAAY,CAAC,WACZ,qBAA0B,UAAU;gBACnC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,YAAY,CAAC,IAAY,WACxB,qBAA0B,AAAC,UAAY,OAAH,KAAM;gBACzC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,YAAY,CAAC,KACZ,qBAA0B,AAAC,UAAY,OAAH,KAAM;gBACzC,QAAQ;YACT;QAED,WAAW;QACX,aAAa,IAAM,qBAAgC;QACnD,YAAY,CAAC,KACZ,qBAA8B,AAAC,aAAe,OAAH;QAC5C,eAAe,CAAC,cACf,qBAA8B,aAAa;gBAC1C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,eAAe,CAAC,IAAY,cAC3B,qBAA8B,AAAC,aAAe,OAAH,KAAM;gBAChD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,eAAe,CAAC,KACf,qBAA2B,AAAC,aAAe,OAAH,KAAM;gBAC7C,QAAQ;YACT;QAED,UAAU;QACV,YAAY,IAAM,qBAA+B;QACjD,WAAW,CAAC,KAAe,qBAA6B,AAAC,YAAc,OAAH;QACpE,cAAc,CAAC,aACd,qBAA6B,YAAY;gBACxC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,IAAY,aAC1B,qBAA6B,AAAC,YAAc,OAAH,KAAM;gBAC9C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,KACd,qBAA2B,AAAC,YAAc,OAAH,KAAM;gBAC5C,QAAQ;YACT;QACD,eAAe,CAAC,KACf,qBAA6B,AAAC,YAAc,OAAH,IAAG,aAAW;gBACtD,QAAQ;YACT;QAED,WAAW;QACX,aAAa,IAAM,qBAAgC;QACnD,YAAY,CAAC,KACZ,qBAA8B,AAAC,aAAe,OAAH;QAC5C,eAAe,CAAC,IAAY,cAC3B,qBAA8B,AAAC,aAAe,OAAH,KAAM;gBAChD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QAED,UAAU;QACV,YAAY,IAAM,qBAA+B;QACjD,WAAW,CAAC,KAAe,qBAA6B,AAAC,YAAc,OAAH;QACpE,cAAc,CAAC,aACd,qBAA6B,YAAY;gBACxC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,IAAY,aAC1B,qBAA6B,AAAC,YAAc,OAAH,KAAM;gBAC9C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,KACd,qBAA2B,AAAC,YAAc,OAAH,KAAM;gBAC5C,QAAQ;YACT;QACD,kBAAkB,CAAC,IAAY;YAC9B,MAAM,SAAS,IAAI;YACnB,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YACjE,IAAI,oBAAA,8BAAA,QAAS,MAAM,EAAE,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YAC3D,IAAI,oBAAA,8BAAA,QAAS,SAAS,EAAE,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;YACpE,MAAM,QAAQ,OAAO,QAAQ,KAAK,AAAC,IAAqB,OAAlB,OAAO,QAAQ,MAAO;YAC5D,OAAO,qBAAoC,AAAC,YAAwB,OAAb,IAAG,YAAgB,OAAN;QACrE;QAEA,WAAW;QACX,eAAe,CAAC,cACf,qBAA8B,aAAa;gBAC1C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QAED,yDAAyD;QACzD,mBAAmB,CAAC,YACnB,qBAAsC,AAAC,aAAsB,OAAV,WAAU;QAC9D,kBAAkB,CAAC,oBAClB,qBAAoC,oBAAoB;gBACvD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,qBAAqB,CAAC,WAAmB,WACxC,qBAA2B,AAAC,aAAiC,OAArB,WAAU,aAAoB,OAAT,WAAY;gBACxE,QAAQ;YACT;IACF;AACD;GA7IgB;;QACW,8QAAU", "debugId": null}}, {"offset": {"line": 739, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/types/index.ts"], "sourcesContent": ["// Types pour l'application Tontine\n\n// Enums\nexport enum CaisseType {\n  PRINCIPALE = 'PRINCIPALE',\n  REUNION = 'REUNION',\n}\n\nexport enum UserRole {\n  SECRETARY_GENERAL = 'secretary_general',\n  CONTROLLER = 'controller',\n  CASHIER = 'cashier',\n}\n\nexport enum UserStatus {\n  ACTIF = 'actif',\n  EN_ATTENTE = 'en_attente',\n  SUSPENDU = 'suspendu',\n}\n\nexport enum PaymentDirection {\n  IN = 'IN',\n  OUT = 'OUT',\n}\n\nexport enum PaymentFunction {\n  CONTRIBUTION = 'cotisation',\n  TRANSFER = 'transfert',\n  EXTERNAL = 'exterieur',\n}\n\n// Interfaces principales\nexport interface User {\n  _id: string;\n  username: string;\n  role: UserRole;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Session {\n  _id: string;\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  dateProchaineReunion?: string;\n  nextReunionId?: string;\n  partFixe: number;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Reunion {\n  _id: string;\n  dateReunion: string;\n  lieu?: string;\n  caissePrincipale?: string;\n  sessionId: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Caisse {\n  _id: string;\n  nom: string;\n  type: CaisseType;\n  soldeActuel: number;\n  sessionId?: string;\n  createdBy: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: Member (différent de User)\nexport interface Member {\n  _id: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: SessionMember (inscription d'un membre à une session)\nexport interface SessionMember {\n  _id: string;\n  sessionId: string;\n  memberId: string;\n  parts: number;\n  totalDue: number;\n  paidSoFar: number;\n  expectedToDate: number;\n  overdueAmount: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: Payment\nexport interface Payment {\n  _id: string;\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  date: string;\n  sessionId?: string;\n  reunionId?: string;\n  caisseId: string;\n  receivingCaisseId?: string;\n  memberId?: string;\n  reason?: string;\n  exitOrderId?: string;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// DTOs pour les formulaires\nexport interface CreateSessionDto {\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  partFixe: number;\n}\n\nexport interface UpdateSessionDto {\n  annee?: number;\n  dateDebut?: string;\n  dateFin?: string;\n  partFixe?: number;\n}\n\nexport interface CreateCaisseDto {\n  nom: string;\n  type: CaisseType;\n  soldeActuel?: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface UpdateCaisseDto {\n  nom?: string;\n  type?: CaisseType;\n  soldeActuel?: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface UpdateReunionDto {\n  dateReunion?: string;\n  lieu?: string;\n  caissePrincipale?: string;\n}\n\n// Nouveaux DTOs\nexport interface CreateMemberDto {\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface UpdateMemberDto {\n  firstName?: string;\n  lastName?: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface CreatePaymentDto {\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  caisseId: string;\n  receivingCaisseId?: string;\n  sessionId?: string;\n  reunionId?: string;\n  memberId?: string;\n  reason?: string;\n  exitOrderId?: string;\n}\n\nexport interface CreateSessionMemberDto {\n  sessionId: string;\n  memberId: string;\n  parts: number;\n}\n\n// Types pour les statistiques\nexport interface SessionStats {\n  total: number;\n  active: number;\n  completed: number;\n  totalPartFixe: number;\n}\n\nexport interface CaisseStats {\n  total: number;\n  principales: number;\n  reunions: number;\n  soldeTotal: number;\n  soldePrincipales: number;\n  soldeReunions: number;\n}\n\nexport interface MemberStats {\n  total: number;\n  withEmail: number;\n  withPhone: number;\n  withAddress: number;\n}\n\nexport interface PaymentStats {\n  totalIn: number;\n  totalOut: number;\n  netAmount: number;\n  contributionsTotal: number;\n  transfersTotal: number;\n  externalTotal: number;\n}\n\n// Types pour les formulaires\nexport interface SessionForm {\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  partFixe: number;\n}\n\nexport interface CaisseForm {\n  nom: string;\n  type: CaisseType;\n  soldeActuel: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface MemberForm {\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface PaymentForm {\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  caisseId: string;\n  receivingCaisseId?: string;\n  sessionId?: string;\n  reunionId?: string;\n  memberId?: string;\n  reason?: string;\n}\n\n// Types pour les réponses API\nexport interface ApiResponse<T> {\n  data: T;\n  message?: string;\n  success: boolean;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n// Types pour les filtres\nexport interface SessionFilters {\n  annee?: number;\n  status?: 'active' | 'completed' | 'all';\n}\n\nexport interface CaisseFilters {\n  type?: CaisseType | 'all';\n  sessionId?: string;\n}\n\nexport interface MemberFilters {\n  search?: string;\n  hasEmail?: boolean;\n  hasPhone?: boolean;\n}\n\nexport interface PaymentFilters {\n  direction?: PaymentDirection | 'all';\n  func?: PaymentFunction | 'all';\n  caisseId?: string;\n  sessionId?: string;\n  memberId?: string;\n  dateFrom?: string;\n  dateTo?: string;\n}\n\n// Types pour les réponses spéciales\nexport interface MemberDebrief {\n  member: Member;\n  totalIn: number;\n  totalOut: number;\n  netAmount: number;\n  contributionsTotal: number;\n  transfersTotal: number;\n  externalTotal: number;\n  payments: Payment[];\n}\n"], "names": [], "mappings": "AAAA,mCAAmC;AAEnC,QAAQ;;;;;;;;;;;;;AACD,IAAA,AAAK,oCAAA;;;WAAA;;AAKL,IAAA,AAAK,kCAAA;;;;WAAA;;AAML,IAAA,AAAK,oCAAA;;;;WAAA;;AAML,IAAA,AAAK,0CAAA;;;WAAA;;AAKL,IAAA,AAAK,yCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/app/dashboard/members/new/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { useRout<PERSON> } from \"next/navigation\";\nimport { useSession } from \"next-auth/react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { ArrowLeft } from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport {\n\tForm,\n\tFormControl,\n\tFormField,\n\tFormItem,\n\tFormLabel,\n\tFormMessage,\n} from \"@/components/ui/form\";\nimport { useApi } from \"@/hooks/use-api\";\nimport { UserRole } from \"@/types\";\n\nconst memberSchema = z.object({\n\tfirstName: z.string().min(2, \"Le prénom doit contenir au moins 2 caractères\"),\n\tlastName: z.string().min(2, \"Le nom doit contenir au moins 2 caractères\"),\n\temail: z.string().email(\"Adresse email invalide\").optional().or(z.literal(\"\")),\n\tphone: z.string().optional(),\n\taddress: z.string().optional(),\n});\n\ntype MemberForm = z.infer<typeof memberSchema>;\n\nexport default function NewMemberPage() {\n\tconst { data: session } = useSession();\n\tconst router = useRouter();\n\tconst api = useApi();\n\n\tconst [isLoading, setIsLoading] = useState(false);\n\tconst [error, setError] = useState<string | null>(null);\n\n\t// Vérifier les permissions\n\tconst canCreateMembers =\n\t\tsession?.user && (session.user as any).role === UserRole.SECRETARY_GENERAL;\n\n\tconst form = useForm<MemberForm>({\n\t\tresolver: zodResolver(memberSchema),\n\t\tdefaultValues: {\n\t\t\tfirstName: \"\",\n\t\t\tlastName: \"\",\n\t\t\temail: \"\",\n\t\t\tphone: \"\",\n\t\t\taddress: \"\",\n\t\t},\n\t});\n\n\tconst onSubmit = async (data: MemberForm) => {\n\t\tsetIsLoading(true);\n\t\tsetError(null);\n\n\t\ttry {\n\t\t\t// Nettoyer les champs vides\n\t\t\tconst cleanData = {\n\t\t\t\tfirstName: data.firstName,\n\t\t\t\tlastName: data.lastName,\n\t\t\t\t...(data.email && { email: data.email }),\n\t\t\t\t...(data.phone && { phone: data.phone }),\n\t\t\t\t...(data.address && { address: data.address }),\n\t\t\t};\n\n\t\t\tawait api.createMember(cleanData);\n\t\t\trouter.push(\"/dashboard/members\");\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors de la création du membre:\", error);\n\t\t\tif (error instanceof Error) {\n\t\t\t\tsetError(error.message);\n\t\t\t} else {\n\t\t\t\tsetError(\"Erreur lors de la création du membre. Veuillez réessayer.\");\n\t\t\t}\n\t\t} finally {\n\t\t\tsetIsLoading(false);\n\t\t}\n\t};\n\n\tif (!canCreateMembers) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Link href=\"/dashboard/members\">\n\t\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tRetour\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\t\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<h2 className=\"text-lg font-semibold text-gray-900\">\n\t\t\t\t\t\t\tAccès refusé\n\t\t\t\t\t\t</h2>\n\t\t\t\t\t\t<p className=\"text-gray-600\">\n\t\t\t\t\t\t\tSeuls les administrateurs peuvent créer des membres.\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* En-tête */}\n\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t<Link href=\"/dashboard/members\">\n\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\tRetour\n\t\t\t\t\t</Button>\n\t\t\t\t</Link>\n\t\t\t\t<div>\n\t\t\t\t\t<h1 className=\"text-2xl font-bold text-gray-900\">Nouveau Membre</h1>\n\t\t\t\t\t<p className=\"text-gray-600\">Créer un nouveau membre de la tontine</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t{/* Formulaire */}\n\t\t\t<Card className=\"max-w-2xl\">\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle>Informations du membre</CardTitle>\n\t\t\t\t\t<CardDescription>\n\t\t\t\t\t\tRemplissez tous les champs pour créer un nouveau membre\n\t\t\t\t\t</CardDescription>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<Form {...form}>\n\t\t\t\t\t\t<form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n\t\t\t\t\t\t\t{/* Informations personnelles */}\n\t\t\t\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"firstName\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Prénom *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Prénom\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled={isLoading}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"lastName\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Nom *</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Nom de famille\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled={isLoading}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t{/* Informations de contact */}\n\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\tname=\"email\"\n\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t<FormLabel>Email</FormLabel>\n\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"email\"\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"<EMAIL>\"\n\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\tdisabled={isLoading}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\tname=\"phone\"\n\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t<FormLabel>Téléphone</FormLabel>\n\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"+237123456789\"\n\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\tdisabled={isLoading}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\tname=\"address\"\n\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t<FormLabel>Adresse</FormLabel>\n\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<Textarea\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Adresse complète\"\n\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\tdisabled={isLoading}\n\t\t\t\t\t\t\t\t\t\t\t\trows={3}\n\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t/>\n\n\n\n\t\t\t\t\t\t\t{/* Message d'erreur */}\n\t\t\t\t\t\t\t{error && (\n\t\t\t\t\t\t\t\t<div className=\"text-red-600 text-sm bg-red-50 p-3 rounded\">\n\t\t\t\t\t\t\t\t\t{error}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t\t{/* Actions */}\n\t\t\t\t\t\t\t<div className=\"flex justify-end gap-4 pt-6\">\n\t\t\t\t\t\t\t\t<Link href=\"/dashboard/members\">\n\t\t\t\t\t\t\t\t\t<Button variant=\"outline\" disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t\tAnnuler\n\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t\t\t<Button type=\"submit\" disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t{isLoading ? \"Création...\" : \"Créer le membre\"}\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</form>\n\t\t\t\t\t</Form>\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAOA;AAQA;AACA;;;AA9BA;;;;;;;;;;;;;;;;AAgCA,MAAM,eAAe,iPAAC,CAAC,MAAM,CAAC;IAC7B,WAAW,iPAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,UAAU,iPAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,iPAAC,CAAC,MAAM,GAAG,KAAK,CAAC,0BAA0B,QAAQ,GAAG,EAAE,CAAC,iPAAC,CAAC,OAAO,CAAC;IAC1E,OAAO,iPAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,SAAS,iPAAC,CAAC,MAAM,GAAG,QAAQ;AAC7B;AAIe,SAAS;;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,8QAAU;IACpC,MAAM,SAAS,IAAA,6RAAS;IACxB,MAAM,MAAM,IAAA,mJAAM;IAElB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,oTAAQ,EAAC;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,oTAAQ,EAAgB;IAElD,2BAA2B;IAC3B,MAAM,mBACL,CAAA,oBAAA,8BAAA,QAAS,IAAI,KAAI,AAAC,QAAQ,IAAI,CAAS,IAAI,KAAK,gJAAQ,CAAC,iBAAiB;IAE3E,MAAM,OAAO,IAAA,iRAAO,EAAa;QAChC,UAAU,IAAA,mSAAW,EAAC;QACtB,eAAe;YACd,WAAW;YACX,UAAU;YACV,OAAO;YACP,OAAO;YACP,SAAS;QACV;IACD;IAEA,MAAM,WAAW,OAAO;QACvB,aAAa;QACb,SAAS;QAET,IAAI;YACH,4BAA4B;YAC5B,MAAM,YAAY;gBACjB,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,GAAI,KAAK,KAAK,IAAI;oBAAE,OAAO,KAAK,KAAK;gBAAC,CAAC;gBACvC,GAAI,KAAK,KAAK,IAAI;oBAAE,OAAO,KAAK,KAAK;gBAAC,CAAC;gBACvC,GAAI,KAAK,OAAO,IAAI;oBAAE,SAAS,KAAK,OAAO;gBAAC,CAAC;YAC9C;YAEA,MAAM,IAAI,YAAY,CAAC;YACvB,OAAO,IAAI,CAAC;QACb,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,yCAAyC;YACvD,IAAI,iBAAiB,OAAO;gBAC3B,SAAS,MAAM,OAAO;YACvB,OAAO;gBACN,SAAS;YACV;QACD,SAAU;YACT,aAAa;QACd;IACD;IAEA,IAAI,CAAC,kBAAkB;QACtB,qBACC,wUAAC;YAAI,WAAU;;8BACd,wUAAC;oBAAI,WAAU;8BACd,cAAA,wUAAC,qTAAI;wBAAC,MAAK;kCACV,cAAA,wUAAC,2JAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC5B,wUAAC,gUAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;8BAKzC,wUAAC;oBAAI,WAAU;8BACd,cAAA,wUAAC;wBAAI,WAAU;;0CACd,wUAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,wUAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;IAOlC;IAEA,qBACC,wUAAC;QAAI,WAAU;;0BAEd,wUAAC;gBAAI,WAAU;;kCACd,wUAAC,qTAAI;wBAAC,MAAK;kCACV,cAAA,wUAAC,2JAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC5B,wUAAC,gUAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIxC,wUAAC;;0CACA,wUAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,wUAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAK/B,wUAAC,uJAAI;gBAAC,WAAU;;kCACf,wUAAC,6JAAU;;0CACV,wUAAC,4JAAS;0CAAC;;;;;;0CACX,wUAAC,kKAAe;0CAAC;;;;;;;;;;;;kCAIlB,wUAAC,8JAAW;kCACX,cAAA,wUAAC,uJAAI;4BAAE,GAAG,IAAI;sCACb,cAAA,wUAAC;gCAAK,UAAU,KAAK,YAAY,CAAC;gCAAW,WAAU;;kDAEtD,wUAAC;wCAAI,WAAU;;0DACd,wUAAC,4JAAS;gDACT,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ;wDAAC,EAAE,KAAK,EAAE;yEACjB,wUAAC,2JAAQ;;0EACR,wUAAC,4JAAS;0EAAC;;;;;;0EACX,wUAAC,8JAAW;0EACX,cAAA,wUAAC,yJAAK;oEACL,aAAY;oEACX,GAAG,KAAK;oEACT,UAAU;;;;;;;;;;;0EAGZ,wUAAC,8JAAW;;;;;;;;;;;;;;;;;0DAIf,wUAAC,4JAAS;gDACT,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ;wDAAC,EAAE,KAAK,EAAE;yEACjB,wUAAC,2JAAQ;;0EACR,wUAAC,4JAAS;0EAAC;;;;;;0EACX,wUAAC,8JAAW;0EACX,cAAA,wUAAC,yJAAK;oEACL,aAAY;oEACX,GAAG,KAAK;oEACT,UAAU;;;;;;;;;;;0EAGZ,wUAAC,8JAAW;;;;;;;;;;;;;;;;;;;;;;;kDAOhB,wUAAC,4JAAS;wCACT,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ;gDAAC,EAAE,KAAK,EAAE;iEACjB,wUAAC,2JAAQ;;kEACR,wUAAC,4JAAS;kEAAC;;;;;;kEACX,wUAAC,8JAAW;kEACX,cAAA,wUAAC,yJAAK;4DACL,MAAK;4DACL,aAAY;4DACX,GAAG,KAAK;4DACT,UAAU;;;;;;;;;;;kEAGZ,wUAAC,8JAAW;;;;;;;;;;;;;;;;;kDAKf,wUAAC,4JAAS;wCACT,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ;gDAAC,EAAE,KAAK,EAAE;iEACjB,wUAAC,2JAAQ;;kEACR,wUAAC,4JAAS;kEAAC;;;;;;kEACX,wUAAC,8JAAW;kEACX,cAAA,wUAAC,yJAAK;4DACL,aAAY;4DACX,GAAG,KAAK;4DACT,UAAU;;;;;;;;;;;kEAGZ,wUAAC,8JAAW;;;;;;;;;;;;;;;;;kDAKf,wUAAC,4JAAS;wCACT,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ;gDAAC,EAAE,KAAK,EAAE;iEACjB,wUAAC,2JAAQ;;kEACR,wUAAC,4JAAS;kEAAC;;;;;;kEACX,wUAAC,8JAAW;kEACX,cAAA,wUAAC,+JAAQ;4DACR,aAAY;4DACX,GAAG,KAAK;4DACT,UAAU;4DACV,MAAM;;;;;;;;;;;kEAGR,wUAAC,8JAAW;;;;;;;;;;;;;;;;;oCAQd,uBACA,wUAAC;wCAAI,WAAU;kDACb;;;;;;kDAKH,wUAAC;wCAAI,WAAU;;0DACd,wUAAC,qTAAI;gDAAC,MAAK;0DACV,cAAA,wUAAC,2JAAM;oDAAC,SAAQ;oDAAU,UAAU;8DAAW;;;;;;;;;;;0DAIhD,wUAAC,2JAAM;gDAAC,MAAK;gDAAS,UAAU;0DAC9B,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;GAhOwB;;QACG,8QAAU;QACrB,6RAAS;QACZ,mJAAM;QASL,iRAAO;;;KAZG", "debugId": null}}]}