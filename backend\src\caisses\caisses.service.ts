import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Caisse, CaisseDocument, CaisseStatus, CaisseType } from './schemas/caisse.schema';
import { CreateCaisseDto } from './dto/create-caisse.dto';
import { UpdateCaisseDto } from './dto/update-caisse.dto';
import { CreatePaymentDto } from '../payments/dto/create-payment.dto';
import { ExitOrder, ExitOrderDocument } from './schemas/exit-order.schema';
import { SessionsService } from '../sessions/sessions.service';
import { PaymentsService } from '../payments/payments.service';
import { Payment, PaymentDocument } from '../payments/schemas/payment.schema';
import { PaymentDirection, PaymentFunction } from '../payments/schemas/payment.schema';
import { RenflouerDto } from './dto/renflouer.dto';
import { ExtractionDto } from './dto/extraction.dto';
import { SessionMember, SessionMemberDocument } from '../sessions/schemas/session-member.schema';
import { Reunion, ReunionDocument } from '../reunions/schemas/reunion.schema';
import { Session, SessionDocument } from '../sessions/schemas/session.schema';
import { MemberContributionDto } from './dto/member-contribution.dto'; 
import { PaymentFiltersDto } from '../common/dto/payment-filters.dto';

@Injectable()
export class CaissesService {
  constructor(
    private paymentsService: PaymentsService,
    private readonly sessionService: SessionsService,
    @InjectModel(Caisse.name) private caisseModel: Model<CaisseDocument>,
    @InjectModel(ExitOrder.name) private exitOrderModel: Model<ExitOrderDocument>,
    @InjectModel(Payment.name) private paymentModel: Model<PaymentDocument>,
    @InjectModel(SessionMember.name) private sessionMemberModel: Model<SessionMemberDocument>,
    @InjectModel(Reunion.name) private reunionModel: Model<ReunionDocument>,
    @InjectModel(Session.name) private sessionModel: Model<SessionDocument>,
  ) {}

  async create(dto: CreateCaisseDto, createdBy: string): Promise<Caisse> {
    if (dto.type === CaisseType.REUNION) {
      if (!dto.sessionId) throw new BadRequestException('sessionId requis pour une caisse de type REUNION');
      if (!dto.caissePrincipaleId) throw new BadRequestException("caissePrincipaleId requis pour l'émargement");
      if (!dto.cashierId) throw new BadRequestException('cashierId requis pour une caisse de type REUNION');
    }

    const caisse = await this.caisseModel.create({
      nom: dto.nom,
      type: dto.type,
      soldeActuel: dto.soldeActuel ?? 0,
      sessionId: dto.sessionId ? new Types.ObjectId(dto.sessionId) : undefined,
      caissePrincipaleId: dto.caissePrincipaleId ? new Types.ObjectId(dto.caissePrincipaleId) : undefined,
      cashierId: dto.cashierId ? new Types.ObjectId(dto.cashierId) : undefined,
      createdBy: new Types.ObjectId(createdBy),
      status: CaisseStatus.CLOSED,
    });

    return caisse.toObject();
  }

  async findAll(): Promise<Caisse[]> {
    return this.caisseModel.find().sort({ createdAt: -1 }).lean();
  }

  async findOne(id: string): Promise<Caisse | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Caisse introuvable');
    return this.caisseModel.findById(id).lean();
  }

  async update(id: string, dto: UpdateCaisseDto): Promise<Caisse | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Caisse introuvable');
    const update: any = {};
    if (dto.nom !== undefined) update.nom = dto.nom;
    if (dto.type !== undefined) update.type = dto.type;
    if (dto.soldeActuel !== undefined) update.soldeActuel = dto.soldeActuel;
    if (dto.sessionId !== undefined) update.sessionId = dto.sessionId ? new Types.ObjectId(dto.sessionId) : undefined;
    if (dto.caissePrincipaleId !== undefined) update.caissePrincipaleId = dto.caissePrincipaleId ? new Types.ObjectId(dto.caissePrincipaleId) : undefined;

    const updated = await this.caisseModel.findByIdAndUpdate(id, { $set: update }, { new: true });
    return updated?.toObject() ?? null;
  }

  // cashier opens his assigned reunion caisse on meeting day
  async open(id: string, userId: string): Promise<Caisse> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Caisse introuvable');
    const caisse = await this.caisseModel.findById(id);
    if (!caisse) throw new NotFoundException('Caisse introuvable');
    if (caisse.type !== CaisseType.REUNION) throw new BadRequestException('Seules les caisses REUNION peuvent être ouvertes');
    if (!caisse.cashierId || caisse.cashierId.toString() !== userId) throw new ForbiddenException('Seul le caissier assigné peut ouvrir cette caisse');
    // mettre à jour la date/id de la prochaine réunion dans la session
    if (caisse.sessionId) await this.sessionService.updateNextMeet(caisse.sessionId.toString());
    // vérifier que la date du jour correspond à la prochaine réunion de la session
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const session = caisse.sessionId ? await this.sessionModel.findById(caisse.sessionId) : null;
    const isTodayNext = session?.dateProchaineReunion &&
      new Date((session.dateProchaineReunion as any)) >= todayStart &&
      new Date((session.dateProchaineReunion as any)) < new Date(todayStart.getTime() + 24*60*60*1000);
    if (!isTodayNext) throw new BadRequestException('La caisse ne peut être ouverte que le jour de la réunion');
    //on met à jour le statut
    caisse.status = CaisseStatus.OPEN;
    caisse.lastOpenedAt = new Date();
    await caisse.save();
    return caisse.toObject();
  }

  // controller or secretary_general closes; auto-emarge to principal and mark exit orders used
  async close(id: string, userId:string): Promise<Caisse> {
    const caisse = await this.caisseModel.findById(id);
    if (!caisse) throw new NotFoundException('Caisse introuvable');
    if (caisse.type !== CaisseType.REUNION) throw new BadRequestException('Seules les caisses REUNION peuvent être fermées');

    if (!caisse.caissePrincipaleId) throw new BadRequestException('Aucune caisse principale liée');
    const principal = await this.caisseModel.findById(caisse.caissePrincipaleId);
    if (!principal) throw new NotFoundException('Caisse principale liée introuvable');
    if (principal.type !== CaisseType.PRINCIPALE) {
      throw new BadRequestException("La caisse liée n'est pas de type PRINCIPALE");
    }

    const montant = caisse.soldeActuel || 0;
    if (montant > 0) {
   // emargement de la caisse vers la principale 
    await this.emargerCaisse(id,userId);
    }
    // close
    caisse.status = CaisseStatus.CLOSED;
    caisse.lastClosedAt = new Date();
    //mettre à jour la date de la prochaine réunion dans la session
    if(caisse.sessionId) await this.sessionService.updateNextMeet(caisse.sessionId.toString());

    await caisse.save();

    return caisse.toObject();
  }

  async remove(id: string): Promise<void> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Caisse introuvable');
    const caisse = await this.caisseModel.findById(id);
    if (!caisse) throw new NotFoundException('Caisse introuvable');
    if (caisse.type === CaisseType.REUNION && caisse.sessionId) {
      throw new BadRequestException('Impossible de supprimer une caisse de réunion liée à une session active');
    }
    await caisse.deleteOne();
  }

  async emargerCaisse(id: string, userId:string): Promise<Caisse> {
    const caisse = await this.caisseModel.findById(id);
    if (!caisse) throw new NotFoundException('Caisse introuvable');
    if (caisse.type !== CaisseType.REUNION) throw new BadRequestException('Seules les caisses de type REUNION peuvent être émargées');
    if (!caisse.caissePrincipaleId) throw new BadRequestException('Aucune caisse principale liée pour émargement');

    const principal = await this.caisseModel.findById(caisse.caissePrincipaleId);
    if (!principal) throw new NotFoundException('Caisse principale liée introuvable');
    if (principal.type !== CaisseType.PRINCIPALE) {
      throw new BadRequestException("La caisse liée n'est pas de type PRINCIPALE");
    }

    const montant = caisse.soldeActuel || 0;
    if (montant <= 0) throw new BadRequestException('Solde actuel nul, rien à émarger');

    // on enclenche le paiement
    const paymentData = new CreatePaymentDto()
    paymentData.amount = montant;
    paymentData.caisseId = id;
    paymentData.direction = PaymentDirection.OUT;
    paymentData.func = PaymentFunction.TRANSFER;
    paymentData.reason = 'emargement vers caisse principale';
    paymentData.sessionId = caisse.sessionId?.toString();
    paymentData.receivingCaisseId = principal._id.toString();
    await this.paymentsService.create(paymentData,userId);

    await principal.save();
    await caisse.save();

    return caisse.toObject();
  }

  async renflouerCaisse(id: string,dto:RenflouerDto, userId:string): Promise<Caisse> {
    const caisse = await this.caisseModel.findById(id);
    if (!caisse) throw new NotFoundException('Caisse introuvable');
    if (caisse.type !== CaisseType.REUNION) throw new BadRequestException('Seules les caisses de type REUNION peuvent être renflouer');
    if (!caisse.caissePrincipaleId) throw new BadRequestException('Aucune caisse principale liée pour émargement');

    const principal = await this.caisseModel.findById(dto.caissePrincipale);
    if (!principal) throw new NotFoundException('Caisse principale liée introuvable');
    if (principal.type !== CaisseType.PRINCIPALE) {
      throw new BadRequestException("La caisse liée n'est pas de type PRINCIPALE");
    }
    

    if (principal.soldeActuel <= 0) throw new BadRequestException('Solde actuel de la caisse principale nul, rien à renflouer');
    if (principal.soldeActuel <= dto.montant) throw new BadRequestException('Solde actuel inférieur au montant à renflouer');

    // on enclenche le paiement
    const paymentData = new CreatePaymentDto()
    paymentData.amount = dto.montant;
    paymentData.caisseId = dto.caissePrincipale;
    paymentData.receivingCaisseId = id;
    paymentData.direction = PaymentDirection.OUT;
    paymentData.func = PaymentFunction.TRANSFER;
    paymentData.reason = 'renflouement vers caisse de reunion';
    paymentData.sessionId = caisse.sessionId?.toString();
    await this.paymentsService.create(paymentData,userId);

    await principal.save();
    await caisse.save();

    return caisse.toObject();
  }

  async extractCaisse(dto:ExtractionDto, userId:string): Promise<Caisse> {
    const principal = await this.caisseModel.findById(dto.caissePrincipale);
    if (!principal) throw new NotFoundException('Caisse principale liée introuvable');
    if (principal.type !== CaisseType.PRINCIPALE) {
      throw new BadRequestException("La caisse liée n'est pas de type PRINCIPALE");
    }
// on verifie les montants si ils sont compatibles 
    if (principal.soldeActuel <= 0) throw new BadRequestException('Solde actuel de la caisse principale nul, rien à renflouer');
    if (principal.soldeActuel <= dto.montant) throw new BadRequestException('Solde actuel inférieur au montant à renflouer');

    // on enclenche le paiement
    const paymentData = new CreatePaymentDto()
    paymentData.amount = dto.montant;
    paymentData.caisseId = dto.caissePrincipale;
    paymentData.direction = PaymentDirection.OUT;
    paymentData.func = PaymentFunction.EXTERNAL;
    paymentData.reason = dto.reason || 'extraire de argent de la caisse principale';
    await this.paymentsService.create(paymentData,userId);

    await principal.save();

    return principal.toObject();
  }

  // ===== Cotisation Membre =====
  // Réalise une cotisation de membre sur la caisse de réunion. Si le montant dépasse la cotisation par réunion,
  // on crée des paiements successifs pour les prochaines réunions de la session.
  async memberContribute(caisseId: string, dto: MemberContributionDto, userId: string) {
    // validations de base
    const caisse = await this.caisseModel.findById(caisseId);
    if (!caisse) throw new NotFoundException('Caisse introuvable');
    if (caisse.type !== CaisseType.REUNION) throw new BadRequestException('Cotisation uniquement sur une caisse de type REUNION');
    if (caisse.status !== CaisseStatus.OPEN) throw new ForbiddenException('La caisse doit être ouverte');

    if (!Types.ObjectId.isValid(dto.memberId)) throw new NotFoundException('Membre invalide');
    if (!Types.ObjectId.isValid(dto.sessionId)) throw new NotFoundException('Session invalide');
    if (!Types.ObjectId.isValid(dto.reunionId)) throw new NotFoundException('Réunion invalide');

    // récupérer session + affiliation (SessionMember)
    const session = await this.sessionModel.findById(dto.sessionId);
    if (!session) throw new NotFoundException('Session introuvable');
    const affiliation = await this.sessionMemberModel.findOne({ sessionId: session._id, memberId: new Types.ObjectId(dto.memberId) });
    if (!affiliation) throw new NotFoundException('Membre non inscrit à la session');

    const reunion = await this.reunionModel.findById(dto.reunionId);
    if (!reunion || reunion.sessionId.toString() !== session._id.toString()) {
      throw new BadRequestException('Réunion non liée à la session');
    }

    // calcul du montant par réunion pour ce membre
    const perMeetingDue = (session.partFixe || 0) * (affiliation.parts || 0);
    if (perMeetingDue <= 0) throw new BadRequestException('Montant par réunion invalide');

    // récupérer toutes les réunions futures à partir de la réunion donnée (incluse)
    const futureReunions = await this.reunionModel.find({
      sessionId: session._id,
      dateReunion: { $gte: reunion.dateReunion },
    }).sort({ dateReunion: 1 });

    let remaining = dto.amount;
    const created: any[] = [];

    for (const r of futureReunions) {
      if (remaining <= 0) break;
      const payAmount = Math.min(remaining, perMeetingDue);

      const paymentData = new CreatePaymentDto();
      paymentData.amount = payAmount;
      paymentData.caisseId = caisseId;
      paymentData.direction = PaymentDirection.IN;
      paymentData.func = PaymentFunction.CONTRIBUTION;
      paymentData.sessionId = session._id.toString();
      paymentData.reunionId = r._id.toString();
      paymentData.memberId = dto.memberId;

      const payment = await this.paymentsService.create(paymentData, userId);
      created.push(payment);

      remaining -= payAmount;
    }

    // S'il reste un reliquat et plus de réunions futures, on le laisse comme avance (sur la même dernière réunion)
    if (remaining > 0 && futureReunions.length > 0) {
      const last = futureReunions[futureReunions.length - 1];
      const paymentData = new CreatePaymentDto();
      paymentData.amount = remaining; // avance
      paymentData.caisseId = caisseId;
      paymentData.direction = PaymentDirection.IN;
      paymentData.func = PaymentFunction.CONTRIBUTION;
      paymentData.sessionId = session._id.toString();
      paymentData.reunionId = last._id.toString();
      paymentData.memberId = dto.memberId;
      const payment = await this.paymentsService.create(paymentData, userId);
      created.push(payment);
    }

    return { count: created.length, payments: created };
  }

  // Prochaine cotisation (date + montant dû) pour un membre sur une session
  async nextContribution(memberId: string, sessionId: string) {
    if (!Types.ObjectId.isValid(memberId)) throw new NotFoundException('Membre invalide');
    if (!Types.ObjectId.isValid(sessionId)) throw new NotFoundException('Session invalide');
    const session = await this.sessionModel.findById(sessionId);
    if (!session) throw new NotFoundException('Session introuvable');
    const affiliation = await this.sessionMemberModel.findOne({ sessionId: session._id, memberId: new Types.ObjectId(memberId) });
    if (!affiliation) throw new NotFoundException('Membre non inscrit à la session');

    const now = new Date();
    const nextReunion = await this.reunionModel.findOne({ sessionId: session._id, dateReunion: { $gte: now } }).sort({ dateReunion: 1 });

    const perMeetingDue = (session.partFixe || 0) * (affiliation.parts || 0);
    const advance = Math.max(0, (affiliation.paidSoFar || 0) - (affiliation.expectedToDate || 0));
    const amountDue = Math.max(0, perMeetingDue - advance);

    return {
      memberId,
      sessionId,
      nextReunion: nextReunion ? { id: nextReunion._id.toString(), date: nextReunion.dateReunion } : null,
      perMeetingDue,
      amountDue,
    };
  }

  // Debrief financier d'une caisse (filtrable)
  async debriefCaisse(caisseId: string, filters: PaymentFiltersDto) {
    if (!Types.ObjectId.isValid(caisseId)) throw new NotFoundException('Caisse introuvable');
    const q: any = { caisseId: new Types.ObjectId(caisseId) };
    if (filters.memberId) q.memberId = new Types.ObjectId(filters.memberId);
    if (filters.sessionId) q.sessionId = new Types.ObjectId(filters.sessionId);
    if (filters.reunionId) q.reunionId = new Types.ObjectId(filters.reunionId);
    if (filters.direction) q.direction = filters.direction;
    if (filters.func) q.func = filters.func;
    if (filters.startDate || filters.endDate) {
      q.date = {} as any;
      if (filters.startDate) q.date.$gte = new Date(filters.startDate);
      if (filters.endDate) q.date.$lte = new Date(filters.endDate);
    }

    const payments = await this.paymentModel.find(q).lean();
    const totalIn = payments.filter(p => p.direction === PaymentDirection.IN).reduce((s,p)=>s+p.amount, 0);
    const totalOut = payments.filter(p => p.direction === PaymentDirection.OUT).reduce((s,p)=>s+p.amount, 0);
    const byFunc = payments.reduce((acc:any, p:any)=>{ acc[p.func]=(acc[p.func]||0)+p.amount; return acc; },{});

    return { caisseId, filters, totalIn, totalOut, net: totalIn-totalOut, byFunc, count: payments.length };
  }

  // Debrief "mydebrief" pour un caissier: vérifie la caisse assignée au user
  async myDebrief(caisseId: string, userId: string, filters: PaymentFiltersDto) {
    if (!Types.ObjectId.isValid(caisseId)) throw new NotFoundException('Caisse introuvable');
    const caisse = await this.caisseModel.findById(caisseId);
    if (!caisse) throw new NotFoundException('Caisse introuvable');
    if (!caisse.cashierId || caisse.cashierId.toString() !== userId) {
      throw new ForbiddenException("Cette caisse n'est pas assignée à cet utilisateur");
    }
    return this.debriefCaisse(caisseId, filters);
  }

  // ===== Exit Orders =====
  async createExitOrder(caisseId: string, dto: { amount: number; reason?: string }, userId: string) {
    //if (!Types.ObjectId.isValid(caisseId)) throw new NotFoundException('Caisse introuvable');
    const caisse = await this.caisseModel.findById(caisseId);
    if (!caisse) throw new NotFoundException('Caisse introuvable');
    if (caisse.type === CaisseType.PRINCIPALE && !dto.reason) {
      throw new BadRequestException('Reason obligatoire pour la caisse principale');
    }
    const eo = await this.exitOrderModel.create({
      caisseId: caisse._id,
      amount: dto.amount,
      reason: dto.reason,
      createdBy: new Types.ObjectId(userId),
      open: true,
    });
    return eo.toObject();
  }

  async listExitOrders(caisseId: string) {
    if (!Types.ObjectId.isValid(caisseId)) throw new NotFoundException('Caisse introuvable');
    return this.exitOrderModel.find({ caisseId: new Types.ObjectId(caisseId) }).sort({ createdAt: -1 }).lean();
  }  
  async listCashierExitOrders(caisseId: string,userId:string) {
    if (!Types.ObjectId.isValid(caisseId)) throw new NotFoundException('Caisse introuvable');
    if (!Types.ObjectId.isValid(userId)) throw new NotFoundException('caissier introuvable');
    const caisse = await this.caisseModel.findById(caisseId);
    if (!caisse) throw new NotFoundException('Caisse introuvable');
    if (!caisse.cashierId || caisse.cashierId.toString() !== userId) throw new ForbiddenException('Seul le caissier assigné peut ouvrir cette caisse');
    // ExitOrder currently has no cashierId field; return orders for this caisse only
    return this.exitOrderModel.find({ caisseId: new Types.ObjectId(caisseId) }).sort({ createdAt: -1 }).lean();
  }

  async closeExitOrder(caisseId: string, exitOrderId: string, userId:string) {
    if (!Types.ObjectId.isValid(caisseId)) throw new NotFoundException('Caisse introuvable');
    if (!Types.ObjectId.isValid(exitOrderId)) throw new NotFoundException('ExitOrder introuvable');
    const caisse = await this.caisseModel.findById(caisseId);
    if (!caisse) throw new NotFoundException('Caisse introuvable');
    const eo = await this.exitOrderModel.findOne({ _id: new Types.ObjectId(exitOrderId), caisseId: new Types.ObjectId(caisseId) });
    if (!eo) throw new NotFoundException('ExitOrder introuvable');
    // on enclenche le paiement , c'est fait par le caissier
    const paymentData = new CreatePaymentDto()
    paymentData.amount = eo.amount;
    paymentData.caisseId = eo.caisseId.toString();
    paymentData.direction = PaymentDirection.OUT;
    paymentData.func = PaymentFunction.EXTERNAL;
    paymentData.reason = eo.reason ? eo.reason : 'Paiement sortie de caisse';
    paymentData.exitOrderId = exitOrderId.toString();
    paymentData.sessionId = caisse.sessionId?.toString();
    await this.paymentsService.create(paymentData,userId);
    eo.open = false;
    await eo.save();
    return eo.toObject();
  }
  async removeExitOrder(exitOrderId: string) {
    if (!Types.ObjectId.isValid(exitOrderId)) throw new NotFoundException('ExitOrder introuvable');
    if(!( await this.exitOrderModel.findById(exitOrderId))?.open) throw new BadRequestException('Impossible de supprimer un ordre de sortie deja payé (fermé)');
    await this.exitOrderModel.deleteOne({ _id: new Types.ObjectId(exitOrderId)});

  }
}