(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,39731,e=>{"use strict";e.s(["$ZodAsyncError",()=>i,"$ZodEncodeError",()=>a,"$brand",()=>r,"$constructor",()=>n,"NEVER",()=>t,"config",()=>c,"globalConfig",()=>o]);let t=Object.freeze({status:"aborted"});function n(e,t,n){var r;function i(n,r){var i,a;for(let o in Object.defineProperty(n,"_zod",{value:null!=(a=n._zod)?a:{},enumerable:!1}),null!=(i=n._zod).traits||(i.traits=new Set),n._zod.traits.add(e),t(n,r),c.prototype)o in n||Object.defineProperty(n,o,{value:c.prototype[o].bind(n)});n._zod.constr=c,n._zod.def=r}let a=null!=(r=null==n?void 0:n.Parent)?r:Object;class o extends a{}function c(e){var t;let r=(null==n?void 0:n.Parent)?new o:this;for(let n of(i(r,e),null!=(t=r._zod).deferred||(t.deferred=[]),r._zod.deferred))n();return r}return Object.defineProperty(o,"name",{value:e}),Object.defineProperty(c,"init",{value:i}),Object.defineProperty(c,Symbol.hasInstance,{value:t=>{var r,i;return null!=n&&!!n.Parent&&t instanceof n.Parent||(null==t||null==(i=t._zod)||null==(r=i.traits)?void 0:r.has(e))}}),Object.defineProperty(c,"name",{value:e}),c}let r=Symbol("zod_brand");class i extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}class a extends Error{constructor(e){super("Encountered unidirectional transform during encode: ".concat(e)),this.name="ZodEncodeError"}}let o={};function c(e){return e&&Object.assign(o,e),o}},27874,e=>{"use strict";function t(e){return e}function n(e){return e}function r(e){}function i(e){throw Error()}function a(e){}function o(e){let t=Object.values(e).filter(e=>"number"==typeof e);return Object.entries(e).filter(e=>{let[n,r]=e;return -1===t.indexOf(+n)}).map(e=>{let[t,n]=e;return n})}function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"|";return e.map(e=>A(e)).join(t)}function u(e,t){return"bigint"==typeof t?t.toString():t}function l(e){return{get value(){{let t=e();return Object.defineProperty(this,"value",{value:t}),t}}}}function s(e){return null==e}function d(e){let t=+!!e.startsWith("^"),n=e.endsWith("$")?e.length-1:e.length;return e.slice(t,n)}function m(e,t){let n=(e.toString().split(".")[1]||"").length,r=t.toString(),i=(r.split(".")[1]||"").length;if(0===i&&/\d?e-\d?/.test(r)){let e=r.match(/\d?e-(\d?)/);(null==e?void 0:e[1])&&(i=Number.parseInt(e[1]))}let a=n>i?n:i;return Number.parseInt(e.toFixed(a).replace(".",""))%Number.parseInt(t.toFixed(a).replace(".",""))/10**a}e.s(["BIGINT_FORMAT_RANGES",()=>C,"Class",()=>ec,"NUMBER_FORMAT_RANGES",()=>L,"aborted",()=>K,"allowsEval",()=>S,"assert",()=>a,"assertEqual",()=>t,"assertIs",()=>r,"assertNever",()=>i,"assertNotEqual",()=>n,"assignProp",()=>g,"base64ToUint8Array",()=>et,"base64urlToUint8Array",()=>er,"cached",()=>l,"captureStackTrace",()=>z,"cleanEnum",()=>ee,"cleanRegex",()=>d,"clone",()=>N,"cloneDef",()=>y,"createTransparentProxy",()=>E,"defineLazy",()=>v,"esc",()=>I,"escapeRegex",()=>P,"extend",()=>F,"finalizeIssue",()=>q,"floatSafeRemainder",()=>m,"getElementAtPath",()=>_,"getEnumValues",()=>o,"getLengthableOrigin",()=>H,"getParsedType",()=>$,"getSizableOrigin",()=>Y,"hexToUint8Array",()=>ea,"isObject",()=>w,"isPlainObject",()=>Z,"issue",()=>Q,"joinValues",()=>c,"jsonStringifyReplacer",()=>u,"merge",()=>M,"mergeDefs",()=>h,"normalizeParams",()=>D,"nullish",()=>s,"numKeys",()=>j,"objectClone",()=>p,"omit",()=>V,"optionalKeys",()=>T,"partial",()=>W,"pick",()=>R,"prefixIssues",()=>G,"primitiveTypes",()=>O,"promiseAllObject",()=>b,"propertyKeyTypes",()=>U,"randomString",()=>k,"required",()=>B,"safeExtend",()=>J,"shallowClone",()=>x,"stringifyPrimitive",()=>A,"uint8ArrayToBase64",()=>en,"uint8ArrayToBase64url",()=>ei,"uint8ArrayToHex",()=>eo,"unwrapMessage",()=>X]);let f=Symbol("evaluating");function v(e,t,n){let r;Object.defineProperty(e,t,{get(){if(r!==f)return void 0===r&&(r=f,r=n()),r},set(n){Object.defineProperty(e,t,{value:n})},configurable:!0})}function p(e){return Object.create(Object.getPrototypeOf(e),Object.getOwnPropertyDescriptors(e))}function g(e,t,n){Object.defineProperty(e,t,{value:n,writable:!0,enumerable:!0,configurable:!0})}function h(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r={};for(let e of t)Object.assign(r,Object.getOwnPropertyDescriptors(e));return Object.defineProperties({},r)}function y(e){return h(e._zod.def)}function _(e,t){return t?t.reduce((e,t)=>null==e?void 0:e[t],e):e}function b(e){let t=Object.keys(e);return Promise.all(t.map(t=>e[t])).then(e=>{let n={};for(let r=0;r<t.length;r++)n[t[r]]=e[r];return n})}function k(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t="abcdefghijklmnopqrstuvwxyz",n="";for(let r=0;r<e;r++)n+=t[Math.floor(Math.random()*t.length)];return n}function I(e){return JSON.stringify(e)}let z="captureStackTrace"in Error?Error.captureStackTrace:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n]};function w(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}let S=l(()=>{var e,t;if("undefined"!=typeof navigator&&(null==(t=navigator)||null==(e=t.userAgent)?void 0:e.includes("Cloudflare")))return!1;try{return Function(""),!0}catch(e){return!1}});function Z(e){if(!1===w(e))return!1;let t=e.constructor;if(void 0===t)return!0;let n=t.prototype;return!1!==w(n)&&!1!==Object.prototype.hasOwnProperty.call(n,"isPrototypeOf")}function x(e){return Z(e)?{...e}:e}function j(e){let t=0;for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&t++;return t}let $=e=>{let t=typeof e;switch(t){case"undefined":return"undefined";case"string":return"string";case"number":return Number.isNaN(e)?"nan":"number";case"boolean":return"boolean";case"function":return"function";case"bigint":return"bigint";case"symbol":return"symbol";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return"promise";if("undefined"!=typeof Map&&e instanceof Map)return"map";if("undefined"!=typeof Set&&e instanceof Set)return"set";if("undefined"!=typeof Date&&e instanceof Date)return"date";if("undefined"!=typeof File&&e instanceof File)return"file";return"object";default:throw Error("Unknown data type: ".concat(t))}},U=new Set(["string","number","symbol"]),O=new Set(["string","number","bigint","boolean","symbol","undefined"]);function P(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function N(e,t,n){let r=new e._zod.constr(null!=t?t:e._zod.def);return(!t||(null==n?void 0:n.parent))&&(r._zod.parent=e),r}function D(e){if(!e)return{};if("string"==typeof e)return{error:()=>e};if((null==e?void 0:e.message)!==void 0){if((null==e?void 0:e.error)!==void 0)throw Error("Cannot specify both `message` and `error` params");e.error=e.message}return(delete e.message,"string"==typeof e.error)?{...e,error:()=>e.error}:e}function E(e){let t;return new Proxy({},{get:(n,r,i)=>(null!=t||(t=e()),Reflect.get(t,r,i)),set:(n,r,i,a)=>(null!=t||(t=e()),Reflect.set(t,r,i,a)),has:(n,r)=>(null!=t||(t=e()),Reflect.has(t,r)),deleteProperty:(n,r)=>(null!=t||(t=e()),Reflect.deleteProperty(t,r)),ownKeys:n=>(null!=t||(t=e()),Reflect.ownKeys(t)),getOwnPropertyDescriptor:(n,r)=>(null!=t||(t=e()),Reflect.getOwnPropertyDescriptor(t,r)),defineProperty:(n,r,i)=>(null!=t||(t=e()),Reflect.defineProperty(t,r,i))})}function A(e){return"bigint"==typeof e?e.toString()+"n":"string"==typeof e?'"'.concat(e,'"'):"".concat(e)}function T(e){return Object.keys(e).filter(t=>"optional"===e[t]._zod.optin&&"optional"===e[t]._zod.optout)}let L={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-0x80000000,0x7fffffff],uint32:[0,0xffffffff],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]},C={int64:[BigInt("-9223372036854775808"),BigInt("9223372036854775807")],uint64:[BigInt(0),BigInt("18446744073709551615")]};function R(e,t){let n=e._zod.def,r=h(e._zod.def,{get shape(){let e={};for(let r in t){if(!(r in n.shape))throw Error('Unrecognized key: "'.concat(r,'"'));t[r]&&(e[r]=n.shape[r])}return g(this,"shape",e),e},checks:[]});return N(e,r)}function V(e,t){let n=e._zod.def,r=h(e._zod.def,{get shape(){let r={...e._zod.def.shape};for(let e in t){if(!(e in n.shape))throw Error('Unrecognized key: "'.concat(e,'"'));t[e]&&delete r[e]}return g(this,"shape",r),r},checks:[]});return N(e,r)}function F(e,t){if(!Z(t))throw Error("Invalid input to extend: expected a plain object");let n=e._zod.def.checks;if(n&&n.length>0)throw Error("Object schemas containing refinements cannot be extended. Use `.safeExtend()` instead.");let r=h(e._zod.def,{get shape(){let n={...e._zod.def.shape,...t};return g(this,"shape",n),n},checks:[]});return N(e,r)}function J(e,t){if(!Z(t))throw Error("Invalid input to safeExtend: expected a plain object");let n={...e._zod.def,get shape(){let n={...e._zod.def.shape,...t};return g(this,"shape",n),n},checks:e._zod.def.checks};return N(e,n)}function M(e,t){let n=h(e._zod.def,{get shape(){let n={...e._zod.def.shape,...t._zod.def.shape};return g(this,"shape",n),n},get catchall(){return t._zod.def.catchall},checks:[]});return N(e,n)}function W(e,t,n){let r=h(t._zod.def,{get shape(){let r=t._zod.def.shape,i={...r};if(n)for(let t in n){if(!(t in r))throw Error('Unrecognized key: "'.concat(t,'"'));n[t]&&(i[t]=e?new e({type:"optional",innerType:r[t]}):r[t])}else for(let t in r)i[t]=e?new e({type:"optional",innerType:r[t]}):r[t];return g(this,"shape",i),i},checks:[]});return N(t,r)}function B(e,t,n){let r=h(t._zod.def,{get shape(){let r=t._zod.def.shape,i={...r};if(n)for(let t in n){if(!(t in i))throw Error('Unrecognized key: "'.concat(t,'"'));n[t]&&(i[t]=new e({type:"nonoptional",innerType:r[t]}))}else for(let t in r)i[t]=new e({type:"nonoptional",innerType:r[t]});return g(this,"shape",i),i},checks:[]});return N(t,r)}function K(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(!0===e.aborted)return!0;for(let r=t;r<e.issues.length;r++){var n;if((null==(n=e.issues[r])?void 0:n.continue)!==!0)return!0}return!1}function G(e,t){return t.map(t=>(null!=t.path||(t.path=[]),t.path.unshift(e),t))}function X(e){return"string"==typeof e?e:null==e?void 0:e.message}function q(e,t,n){var r,i,a,o,c,u,l,s,d,m,f;let v={...e,path:null!=(r=e.path)?r:[]};return e.message||(v.message=null!=(f=null!=(m=null!=(d=null!=(s=X(null==(o=e.inst)||null==(a=o._zod.def)||null==(i=a.error)?void 0:i.call(a,e)))?s:X(null==t||null==(c=t.error)?void 0:c.call(t,e)))?d:X(null==(u=n.customError)?void 0:u.call(n,e)))?m:X(null==(l=n.localeError)?void 0:l.call(n,e)))?f:"Invalid input"),delete v.inst,delete v.continue,(null==t?void 0:t.reportInput)||delete v.input,v}function Y(e){return e instanceof Set?"set":e instanceof Map?"map":e instanceof File?"file":"unknown"}function H(e){return Array.isArray(e)?"array":"string"==typeof e?"string":"unknown"}function Q(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,i,a]=t;return"string"==typeof r?{message:r,code:"custom",input:i,inst:a}:{...r}}function ee(e){return Object.entries(e).filter(e=>{let[t,n]=e;return Number.isNaN(Number.parseInt(t,10))}).map(e=>e[1])}function et(e){let t=atob(e),n=new Uint8Array(t.length);for(let e=0;e<t.length;e++)n[e]=t.charCodeAt(e);return n}function en(e){let t="";for(let n=0;n<e.length;n++)t+=String.fromCharCode(e[n]);return btoa(t)}function er(e){let t=e.replace(/-/g,"+").replace(/_/g,"/"),n="=".repeat((4-t.length%4)%4);return et(t+n)}function ei(e){return en(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function ea(e){let t=e.replace(/^0x/,"");if(t.length%2!=0)throw Error("Invalid hex string length");let n=new Uint8Array(t.length/2);for(let e=0;e<t.length;e+=2)n[e/2]=Number.parseInt(t.slice(e,e+2),16);return n}function eo(e){return Array.from(e).map(e=>e.toString(16).padStart(2,"0")).join("")}class ec{constructor(...e){}}},89601,78683,e=>{"use strict";e.s(["_decode",()=>k,"_decodeAsync",()=>S,"_encode",()=>_,"_encodeAsync",()=>z,"_parse",()=>d,"_parseAsync",()=>f,"_safeDecode",()=>$,"_safeDecodeAsync",()=>N,"_safeEncode",()=>x,"_safeEncodeAsync",()=>O,"_safeParse",()=>p,"_safeParseAsync",()=>h,"decode",()=>I,"decodeAsync",()=>Z,"encode",()=>b,"encodeAsync",()=>w,"parse",()=>m,"parseAsync",()=>v,"safeDecode",()=>U,"safeDecodeAsync",()=>D,"safeEncode",()=>j,"safeEncodeAsync",()=>P,"safeParse",()=>g,"safeParseAsync",()=>y],89601);var t=e.i(39731);e.s(["$ZodError",()=>i,"$ZodRealError",()=>a,"flattenError",()=>o,"formatError",()=>c,"prettifyError",()=>s,"toDotPath",()=>l,"treeifyError",()=>u],78683);var n=e.i(27874);let r=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),e.message=JSON.stringify(t,n.jsonStringifyReplacer,2),Object.defineProperty(e,"toString",{value:()=>e.message,enumerable:!1})},i=(0,t.$constructor)("$ZodError",r),a=(0,t.$constructor)("$ZodError",r,{Parent:Error});function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e.message,n={},r=[];for(let i of e.issues)i.path.length>0?(n[i.path[0]]=n[i.path[0]]||[],n[i.path[0]].push(t(i))):r.push(t(i));return{formErrors:r,fieldErrors:n}}function c(e,t){let n=t||function(e){return e.message},r={_errors:[]},i=e=>{for(let t of e.issues)if("invalid_union"===t.code&&t.errors.length)t.errors.map(e=>i({issues:e}));else if("invalid_key"===t.code)i({issues:t.issues});else if("invalid_element"===t.code)i({issues:t.issues});else if(0===t.path.length)r._errors.push(n(t));else{let e=r,i=0;for(;i<t.path.length;){let r=t.path[i];i===t.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(n(t))):e[r]=e[r]||{_errors:[]},e=e[r],i++}}};return i(e),r}function u(e,t){let n=t||function(e){return e.message},r={errors:[]},i=function(e){var t,a;let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];for(let c of e.issues)if("invalid_union"===c.code&&c.errors.length)c.errors.map(e=>i({issues:e},c.path));else if("invalid_key"===c.code)i({issues:c.issues},c.path);else if("invalid_element"===c.code)i({issues:c.issues},c.path);else{let e=[...o,...c.path];if(0===e.length){r.errors.push(n(c));continue}let i=r,u=0;for(;u<e.length;){let r=e[u],o=u===e.length-1;"string"==typeof r?(null!=i.properties||(i.properties={}),null!=(t=i.properties)[r]||(t[r]={errors:[]}),i=i.properties[r]):(null!=i.items||(i.items=[]),null!=(a=i.items)[r]||(a[r]={errors:[]}),i=i.items[r]),o&&i.errors.push(n(c)),u++}}};return i(e),r}function l(e){let t=[];for(let n of e.map(e=>"object"==typeof e?e.key:e))"number"==typeof n?t.push("[".concat(n,"]")):"symbol"==typeof n?t.push("[".concat(JSON.stringify(String(n)),"]")):/[^\w$]/.test(n)?t.push("[".concat(JSON.stringify(n),"]")):(t.length&&t.push("."),t.push(n));return t.join("")}function s(e){let t=[];for(let r of[...e.issues].sort((e,t)=>{var n,r;return(null!=(n=e.path)?n:[]).length-(null!=(r=t.path)?r:[]).length})){var n;t.push("✖ ".concat(r.message)),(null==(n=r.path)?void 0:n.length)&&t.push("  → at ".concat(l(r.path)))}return t.join("\n")}let d=e=>(r,i,a,o)=>{let c=a?Object.assign(a,{async:!1}):{async:!1},u=r._zod.run({value:i,issues:[]},c);if(u instanceof Promise)throw new t.$ZodAsyncError;if(u.issues.length){var l;let r=new(null!=(l=null==o?void 0:o.Err)?l:e)(u.issues.map(e=>n.finalizeIssue(e,c,t.config())));throw n.captureStackTrace(r,null==o?void 0:o.callee),r}return u.value},m=d(a),f=e=>async(r,i,a,o)=>{let c=a?Object.assign(a,{async:!0}):{async:!0},u=r._zod.run({value:i,issues:[]},c);if(u instanceof Promise&&(u=await u),u.issues.length){var l;let r=new(null!=(l=null==o?void 0:o.Err)?l:e)(u.issues.map(e=>n.finalizeIssue(e,c,t.config())));throw n.captureStackTrace(r,null==o?void 0:o.callee),r}return u.value},v=f(a),p=e=>(r,a,o)=>{let c=o?{...o,async:!1}:{async:!1},u=r._zod.run({value:a,issues:[]},c);if(u instanceof Promise)throw new t.$ZodAsyncError;return u.issues.length?{success:!1,error:new(null!=e?e:i)(u.issues.map(e=>n.finalizeIssue(e,c,t.config())))}:{success:!0,data:u.value}},g=p(a),h=e=>async(r,i,a)=>{let o=a?Object.assign(a,{async:!0}):{async:!0},c=r._zod.run({value:i,issues:[]},o);return c instanceof Promise&&(c=await c),c.issues.length?{success:!1,error:new e(c.issues.map(e=>n.finalizeIssue(e,o,t.config())))}:{success:!0,data:c.value}},y=h(a),_=e=>(t,n,r)=>{let i=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return d(e)(t,n,i)},b=_(a),k=e=>(t,n,r)=>d(e)(t,n,r),I=k(a),z=e=>async(t,n,r)=>{let i=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return f(e)(t,n,i)},w=z(a),S=e=>async(t,n,r)=>f(e)(t,n,r),Z=S(a),x=e=>(t,n,r)=>{let i=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return p(e)(t,n,i)},j=x(a),$=e=>(t,n,r)=>p(e)(t,n,r),U=$(a),O=e=>async(t,n,r)=>{let i=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return h(e)(t,n,i)},P=O(a),N=e=>async(t,n,r)=>h(e)(t,n,r),D=N(a)},47821,17486,46590,49131,64085,e=>{"use strict";e.s([],47821);var t=e.i(27874);e.s(["base64",()=>Z,"base64url",()=>x,"bigint",()=>T,"boolean",()=>R,"browserEmail",()=>b,"cidrv4",()=>w,"cidrv6",()=>S,"cuid",()=>n,"cuid2",()=>r,"date",()=>P,"datetime",()=>E,"domain",()=>$,"duration",()=>u,"e164",()=>U,"email",()=>p,"emoji",()=>k,"extendedDuration",()=>l,"guid",()=>s,"hex",()=>W,"hostname",()=>j,"html5Email",()=>g,"idnEmail",()=>_,"integer",()=>L,"ipv4",()=>I,"ipv6",()=>z,"ksuid",()=>o,"lowercase",()=>J,"md5_base64",()=>X,"md5_base64url",()=>q,"md5_hex",()=>G,"nanoid",()=>c,"null",()=>V,"number",()=>C,"rfc5322Email",()=>h,"sha1_base64",()=>H,"sha1_base64url",()=>Q,"sha1_hex",()=>Y,"sha256_base64",()=>et,"sha256_base64url",()=>en,"sha256_hex",()=>ee,"sha384_base64",()=>ei,"sha384_base64url",()=>ea,"sha384_hex",()=>er,"sha512_base64",()=>ec,"sha512_base64url",()=>eu,"sha512_hex",()=>eo,"string",()=>A,"time",()=>D,"ulid",()=>i,"undefined",()=>F,"unicodeEmail",()=>y,"uppercase",()=>M,"uuid",()=>d,"uuid4",()=>m,"uuid6",()=>f,"uuid7",()=>v,"xid",()=>a],17486);let n=/^[cC][^\s-]{8,}$/,r=/^[0-9a-z]+$/,i=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,a=/^[0-9a-vA-V]{20}$/,o=/^[A-Za-z0-9]{27}$/,c=/^[a-zA-Z0-9_-]{21}$/,u=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,l=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,s=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,d=e=>e?new RegExp("^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-".concat(e,"[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$")):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/,m=d(4),f=d(6),v=d(7),p=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,g=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,h=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,y=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,_=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,b=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;function k(){return RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")}let I=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,z=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,w=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,S=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Z=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,x=/^[A-Za-z0-9_-]*$/,j=/^(?=.{1,253}\.?$)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[-0-9a-zA-Z]{0,61}[0-9a-zA-Z])?)*\.?$/,$=/^([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/,U=/^\+(?:[0-9]){6,14}[0-9]$/,O="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",P=new RegExp("^".concat(O,"$"));function N(e){let t="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof e.precision?-1===e.precision?"".concat(t):0===e.precision?"".concat(t,":[0-5]\\d"):"".concat(t,":[0-5]\\d\\.\\d{").concat(e.precision,"}"):"".concat(t,"(?::[0-5]\\d(?:\\.\\d+)?)?")}function D(e){return new RegExp("^".concat(N(e),"$"))}function E(e){let t=N({precision:e.precision}),n=["Z"];e.local&&n.push(""),e.offset&&n.push("([+-](?:[01]\\d|2[0-3]):[0-5]\\d)");let r="".concat(t,"(?:").concat(n.join("|"),")");return new RegExp("^".concat(O,"T(?:").concat(r,")$"))}let A=e=>{var t,n;let r=e?"[\\s\\S]{".concat(null!=(t=null==e?void 0:e.minimum)?t:0,",").concat(null!=(n=null==e?void 0:e.maximum)?n:"","}"):"[\\s\\S]*";return new RegExp("^".concat(r,"$"))},T=/^\d+n?$/,L=/^\d+$/,C=/^-?\d+(?:\.\d+)?/i,R=/true|false/i,V=/null/i,F=/undefined/i,J=/^[^A-Z]*$/,M=/^[^a-z]*$/,W=/^[0-9a-fA-F]*$/;function B(e,t){return new RegExp("^[A-Za-z0-9+/]{".concat(e,"}").concat(t,"$"))}function K(e){return new RegExp("^[A-Za-z0-9-_]{".concat(e,"}$"))}let G=/^[0-9a-fA-F]{32}$/,X=B(22,"=="),q=K(22),Y=/^[0-9a-fA-F]{40}$/,H=B(27,"="),Q=K(27),ee=/^[0-9a-fA-F]{64}$/,et=B(43,"="),en=K(43),er=/^[0-9a-fA-F]{96}$/,ei=B(64,""),ea=K(64),eo=/^[0-9a-fA-F]{128}$/,ec=B(86,"=="),eu=K(86);function el(){return{localeError:(()=>{let e={string:{unit:"حرف",verb:"أن يحوي"},file:{unit:"بايت",verb:"أن يحوي"},array:{unit:"عنصر",verb:"أن يحوي"},set:{unit:"عنصر",verb:"أن يحوي"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"مدخل",email:"بريد إلكتروني",url:"رابط",emoji:"إيموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاريخ ووقت بمعيار ISO",date:"تاريخ بمعيار ISO",time:"وقت بمعيار ISO",duration:"مدة بمعيار ISO",ipv4:"عنوان IPv4",ipv6:"عنوان IPv6",cidrv4:"مدى عناوين بصيغة IPv4",cidrv6:"مدى عناوين بصيغة IPv6",base64:"نَص بترميز base64-encoded",base64url:"نَص بترميز base64url-encoded",json_string:"نَص على هيئة JSON",e164:"رقم هاتف بمعيار E.164",jwt:"JWT",template_literal:"مدخل"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"مدخلات غير مقبولة: يفترض إدخال ".concat(e.expected,"، ولكن تم إدخال ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"مدخلات غير مقبولة: يفترض إدخال ".concat(t.stringifyPrimitive(e.values[0]));return"اختيار غير مقبول: يتوقع انتقاء أحد هذه الخيارات: ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return" أكبر من اللازم: يفترض أن تكون ".concat(null!=(i=e.origin)?i:"القيمة"," ").concat(t," ").concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"عنصر");return"أكبر من اللازم: يفترض أن تكون ".concat(null!=(o=e.origin)?o:"القيمة"," ").concat(t," ").concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"أصغر من اللازم: يفترض لـ ".concat(e.origin," أن يكون ").concat(t," ").concat(e.minimum.toString()," ").concat(r.unit);return"أصغر من اللازم: يفترض لـ ".concat(e.origin," أن يكون ").concat(t," ").concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'نَص غير مقبول: يجب أن يبدأ بـ "'.concat(e.prefix,'"');if("ends_with"===e.format)return'نَص غير مقبول: يجب أن ينتهي بـ "'.concat(e.suffix,'"');if("includes"===e.format)return'نَص غير مقبول: يجب أن يتضمَّن "'.concat(e.includes,'"');if("regex"===e.format)return"نَص غير مقبول: يجب أن يطابق النمط ".concat(e.pattern);return"".concat(null!=(c=r[e.format])?c:e.format," غير مقبول");case"not_multiple_of":return"رقم غير مقبول: يجب أن يكون من مضاعفات ".concat(e.divisor);case"unrecognized_keys":return"معرف".concat(e.keys.length>1?"ات":""," غريب").concat(e.keys.length>1?"ة":"",": ").concat(t.joinValues(e.keys,"، "));case"invalid_key":return"معرف غير مقبول في ".concat(e.origin);case"invalid_union":default:return"مدخل غير مقبول";case"invalid_element":return"مدخل غير مقبول في ".concat(e.origin)}}})()}}function es(){return{localeError:(()=>{let e={string:{unit:"simvol",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"element",verb:"olmalıdır"},set:{unit:"element",verb:"olmalıdır"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Yanlış dəyər: gözlənilən ".concat(e.expected,", daxil olan ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Yanlış dəyər: gözlənilən ".concat(t.stringifyPrimitive(e.values[0]));return"Yanlış seçim: aşağıdakılardan biri olmalıdır: ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Çox böyük: gözlənilən ".concat(null!=(i=e.origin)?i:"dəyər"," ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"element");return"Çox böyük: gözlənilən ".concat(null!=(o=e.origin)?o:"dəyər"," ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Çox kiçik: gözlənilən ".concat(e.origin," ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Çox kiçik: gözlənilən ".concat(e.origin," ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Yanlış mətn: "'.concat(e.prefix,'" ilə başlamalıdır');if("ends_with"===e.format)return'Yanlış mətn: "'.concat(e.suffix,'" ilə bitməlidir');if("includes"===e.format)return'Yanlış mətn: "'.concat(e.includes,'" daxil olmalıdır');if("regex"===e.format)return"Yanlış mətn: ".concat(e.pattern," şablonuna uyğun olmalıdır");return"Yanlış ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Yanlış ədəd: ".concat(e.divisor," ilə bölünə bilən olmalıdır");case"unrecognized_keys":return"Tanınmayan açar".concat(e.keys.length>1?"lar":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"".concat(e.origin," daxilində yanlış açar");case"invalid_union":default:return"Yanlış dəyər";case"invalid_element":return"".concat(e.origin," daxilində yanlış dəyər")}}})()}}function ed(e,t,n,r){let i=Math.abs(e),a=i%10,o=i%100;return o>=11&&o<=19?r:1===a?t:a>=2&&a<=4?n:r}function em(){return{localeError:(()=>{let e={string:{unit:{one:"сімвал",few:"сімвалы",many:"сімвалаў"},verb:"мець"},array:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},set:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},file:{unit:{one:"байт",few:"байты",many:"байтаў"},verb:"мець"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"увод",email:"email адрас",url:"URL",emoji:"эмодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата і час",date:"ISO дата",time:"ISO час",duration:"ISO працягласць",ipv4:"IPv4 адрас",ipv6:"IPv6 адрас",cidrv4:"IPv4 дыяпазон",cidrv6:"IPv6 дыяпазон",base64:"радок у фармаце base64",base64url:"радок у фармаце base64url",json_string:"JSON радок",e164:"нумар E.164",jwt:"JWT",template_literal:"увод"};return e=>{var i,a,o;switch(e.code){case"invalid_type":return"Няправільны ўвод: чакаўся ".concat(e.expected,", атрымана ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"лік";case"object":if(Array.isArray(e))return"масіў";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Няправільны ўвод: чакалася ".concat(t.stringifyPrimitive(e.values[0]));return"Няправільны варыянт: чакаўся адзін з ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r){let n=ed(Number(e.maximum),r.unit.one,r.unit.few,r.unit.many);return"Занадта вялікі: чакалася, што ".concat(null!=(i=e.origin)?i:"значэнне"," павінна ").concat(r.verb," ").concat(t).concat(e.maximum.toString()," ").concat(n)}return"Занадта вялікі: чакалася, што ".concat(null!=(a=e.origin)?a:"значэнне"," павінна быць ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r){let n=ed(Number(e.minimum),r.unit.one,r.unit.few,r.unit.many);return"Занадта малы: чакалася, што ".concat(e.origin," павінна ").concat(r.verb," ").concat(t).concat(e.minimum.toString()," ").concat(n)}return"Занадта малы: чакалася, што ".concat(e.origin," павінна быць ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Няправільны радок: павінен пачынацца з "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Няправільны радок: павінен заканчвацца на "'.concat(e.suffix,'"');if("includes"===e.format)return'Няправільны радок: павінен змяшчаць "'.concat(e.includes,'"');if("regex"===e.format)return"Няправільны радок: павінен адпавядаць шаблону ".concat(e.pattern);return"Няправільны ".concat(null!=(o=r[e.format])?o:e.format);case"not_multiple_of":return"Няправільны лік: павінен быць кратным ".concat(e.divisor);case"unrecognized_keys":return"Нераспазнаны ".concat(e.keys.length>1?"ключы":"ключ",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Няправільны ключ у ".concat(e.origin);case"invalid_union":default:return"Няправільны ўвод";case"invalid_element":return"Няправільнае значэнне ў ".concat(e.origin)}}})()}}function ef(){return{localeError:(()=>{let e={string:{unit:"caràcters",verb:"contenir"},file:{unit:"bytes",verb:"contenir"},array:{unit:"elements",verb:"contenir"},set:{unit:"elements",verb:"contenir"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"entrada",email:"adreça electrònica",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i hora ISO",date:"data ISO",time:"hora ISO",duration:"durada ISO",ipv4:"adreça IPv4",ipv6:"adreça IPv6",cidrv4:"rang IPv4",cidrv6:"rang IPv6",base64:"cadena codificada en base64",base64url:"cadena codificada en base64url",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Tipus invàlid: s'esperava ".concat(e.expected,", s'ha rebut ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Valor invàlid: s'esperava ".concat(t.stringifyPrimitive(e.values[0]));return"Opció invàlida: s'esperava una de ".concat(t.joinValues(e.values," o "));case"too_big":{let t=e.inclusive?"com a màxim":"menys de",r=n(e.origin);if(r)return"Massa gran: s'esperava que ".concat(null!=(i=e.origin)?i:"el valor"," contingués ").concat(t," ").concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elements");return"Massa gran: s'esperava que ".concat(null!=(o=e.origin)?o:"el valor"," fos ").concat(t," ").concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?"com a mínim":"més de",r=n(e.origin);if(r)return"Massa petit: s'esperava que ".concat(e.origin," contingués ").concat(t," ").concat(e.minimum.toString()," ").concat(r.unit);return"Massa petit: s'esperava que ".concat(e.origin," fos ").concat(t," ").concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Format invàlid: ha de començar amb "'.concat(e.prefix,'"');if("ends_with"===e.format)return"Format invàlid: ha d'acabar amb \"".concat(e.suffix,'"');if("includes"===e.format)return"Format invàlid: ha d'incloure \"".concat(e.includes,'"');if("regex"===e.format)return"Format invàlid: ha de coincidir amb el patró ".concat(e.pattern);return"Format invàlid per a ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Número invàlid: ha de ser múltiple de ".concat(e.divisor);case"unrecognized_keys":return"Clau".concat(e.keys.length>1?"s":""," no reconeguda").concat(e.keys.length>1?"s":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Clau invàlida a ".concat(e.origin);case"invalid_union":default:return"Entrada invàlida";case"invalid_element":return"Element invàlid a ".concat(e.origin)}}})()}}function ev(){return{localeError:(()=>{let e={string:{unit:"znaků",verb:"mít"},file:{unit:"bajtů",verb:"mít"},array:{unit:"prvků",verb:"mít"},set:{unit:"prvků",verb:"mít"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"regulární výraz",email:"e-mailová adresa",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"datum a čas ve formátu ISO",date:"datum ve formátu ISO",time:"čas ve formátu ISO",duration:"doba trvání ISO",ipv4:"IPv4 adresa",ipv6:"IPv6 adresa",cidrv4:"rozsah IPv4",cidrv6:"rozsah IPv6",base64:"řetězec zakódovaný ve formátu base64",base64url:"řetězec zakódovaný ve formátu base64url",json_string:"řetězec ve formátu JSON",e164:"číslo E.164",jwt:"JWT",template_literal:"vstup"};return e=>{var i,a,o,c,u,l,s;switch(e.code){case"invalid_type":return"Neplatný vstup: očekáváno ".concat(e.expected,", obdrženo ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"číslo";case"string":return"řetězec";case"boolean":return"boolean";case"bigint":return"bigint";case"function":return"funkce";case"symbol":return"symbol";case"undefined":return"undefined";case"object":if(Array.isArray(e))return"pole";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Neplatný vstup: očekáváno ".concat(t.stringifyPrimitive(e.values[0]));return"Neplatná možnost: očekávána jedna z hodnot ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Hodnota je příliš velká: ".concat(null!=(i=e.origin)?i:"hodnota"," musí mít ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"prvků");return"Hodnota je příliš velká: ".concat(null!=(o=e.origin)?o:"hodnota"," musí být ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Hodnota je příliš malá: ".concat(null!=(c=e.origin)?c:"hodnota"," musí mít ").concat(t).concat(e.minimum.toString()," ").concat(null!=(u=r.unit)?u:"prvků");return"Hodnota je příliš malá: ".concat(null!=(l=e.origin)?l:"hodnota"," musí být ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Neplatný řetězec: musí začínat na "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Neplatný řetězec: musí končit na "'.concat(e.suffix,'"');if("includes"===e.format)return'Neplatný řetězec: musí obsahovat "'.concat(e.includes,'"');if("regex"===e.format)return"Neplatný řetězec: musí odpovídat vzoru ".concat(e.pattern);return"Neplatný formát ".concat(null!=(s=r[e.format])?s:e.format);case"not_multiple_of":return"Neplatné číslo: musí být násobkem ".concat(e.divisor);case"unrecognized_keys":return"Neznámé klíče: ".concat(t.joinValues(e.keys,", "));case"invalid_key":return"Neplatný klíč v ".concat(e.origin);case"invalid_union":default:return"Neplatný vstup";case"invalid_element":return"Neplatná hodnota v ".concat(e.origin)}}})()}}function ep(){return{localeError:(()=>{let e={string:{unit:"tegn",verb:"havde"},file:{unit:"bytes",verb:"havde"},array:{unit:"elementer",verb:"indeholdt"},set:{unit:"elementer",verb:"indeholdt"}},n={string:"streng",number:"tal",boolean:"boolean",array:"liste",object:"objekt",set:"sæt",file:"fil"};function r(t){var n;return null!=(n=e[t])?n:null}function i(e){var t;return null!=(t=n[e])?t:e}let a={regex:"input",email:"e-mailadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslæt",date:"ISO-dato",time:"ISO-klokkeslæt",duration:"ISO-varighed",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodet streng",base64url:"base64url-kodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return e=>{var n,o;switch(e.code){case"invalid_type":return"Ugyldigt input: forventede ".concat(i(e.expected),", fik ").concat(i((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"tal";case"object":if(Array.isArray(e))return"liste";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name;return"objekt"}return t})(e.input)));case"invalid_value":if(1===e.values.length)return"Ugyldig værdi: forventede ".concat(t.stringifyPrimitive(e.values[0]));return"Ugyldigt valg: forventede en af følgende ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",a=r(e.origin),o=i(e.origin);if(a)return"For stor: forventede ".concat(null!=o?o:"value"," ").concat(a.verb," ").concat(t," ").concat(e.maximum.toString()," ").concat(null!=(n=a.unit)?n:"elementer");return"For stor: forventede ".concat(null!=o?o:"value"," havde ").concat(t," ").concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",n=r(e.origin),a=i(e.origin);if(n)return"For lille: forventede ".concat(a," ").concat(n.verb," ").concat(t," ").concat(e.minimum.toString()," ").concat(n.unit);return"For lille: forventede ".concat(a," havde ").concat(t," ").concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Ugyldig streng: skal starte med "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Ugyldig streng: skal ende med "'.concat(e.suffix,'"');if("includes"===e.format)return'Ugyldig streng: skal indeholde "'.concat(e.includes,'"');if("regex"===e.format)return"Ugyldig streng: skal matche mønsteret ".concat(e.pattern);return"Ugyldig ".concat(null!=(o=a[e.format])?o:e.format);case"not_multiple_of":return"Ugyldigt tal: skal være deleligt med ".concat(e.divisor);case"unrecognized_keys":return"".concat(e.keys.length>1?"Ukendte nøgler":"Ukendt nøgle",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Ugyldig nøgle i ".concat(e.origin);case"invalid_union":return"Ugyldigt input: matcher ingen af de tilladte typer";case"invalid_element":return"Ugyldig værdi i ".concat(e.origin);default:return"Ugyldigt input"}}})()}}function eg(){return{localeError:(()=>{let e={string:{unit:"Zeichen",verb:"zu haben"},file:{unit:"Bytes",verb:"zu haben"},array:{unit:"Elemente",verb:"zu haben"},set:{unit:"Elemente",verb:"zu haben"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"Eingabe",email:"E-Mail-Adresse",url:"URL",emoji:"Emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-Datum und -Uhrzeit",date:"ISO-Datum",time:"ISO-Uhrzeit",duration:"ISO-Dauer",ipv4:"IPv4-Adresse",ipv6:"IPv6-Adresse",cidrv4:"IPv4-Bereich",cidrv6:"IPv6-Bereich",base64:"Base64-codierter String",base64url:"Base64-URL-codierter String",json_string:"JSON-String",e164:"E.164-Nummer",jwt:"JWT",template_literal:"Eingabe"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Ungültige Eingabe: erwartet ".concat(e.expected,", erhalten ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"Zahl";case"object":if(Array.isArray(e))return"Array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Ungültige Eingabe: erwartet ".concat(t.stringifyPrimitive(e.values[0]));return"Ungültige Option: erwartet eine von ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Zu groß: erwartet, dass ".concat(null!=(i=e.origin)?i:"Wert"," ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"Elemente"," hat");return"Zu groß: erwartet, dass ".concat(null!=(o=e.origin)?o:"Wert"," ").concat(t).concat(e.maximum.toString()," ist")}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Zu klein: erwartet, dass ".concat(e.origin," ").concat(t).concat(e.minimum.toString()," ").concat(r.unit," hat");return"Zu klein: erwartet, dass ".concat(e.origin," ").concat(t).concat(e.minimum.toString()," ist")}case"invalid_format":if("starts_with"===e.format)return'Ungültiger String: muss mit "'.concat(e.prefix,'" beginnen');if("ends_with"===e.format)return'Ungültiger String: muss mit "'.concat(e.suffix,'" enden');if("includes"===e.format)return'Ungültiger String: muss "'.concat(e.includes,'" enthalten');if("regex"===e.format)return"Ungültiger String: muss dem Muster ".concat(e.pattern," entsprechen");return"Ungültig: ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Ungültige Zahl: muss ein Vielfaches von ".concat(e.divisor," sein");case"unrecognized_keys":return"".concat(e.keys.length>1?"Unbekannte Schlüssel":"Unbekannter Schlüssel",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Ungültiger Schlüssel in ".concat(e.origin);case"invalid_union":default:return"Ungültige Eingabe";case"invalid_element":return"Ungültiger Wert in ".concat(e.origin)}}})()}}function eh(){return{localeError:(()=>{let e={string:{unit:"characters",verb:"to have"},file:{unit:"bytes",verb:"to have"},array:{unit:"items",verb:"to have"},set:{unit:"items",verb:"to have"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Invalid input: expected ".concat(e.expected,", received ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Invalid input: expected ".concat(t.stringifyPrimitive(e.values[0]));return"Invalid option: expected one of ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Too big: expected ".concat(null!=(i=e.origin)?i:"value"," to have ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elements");return"Too big: expected ".concat(null!=(o=e.origin)?o:"value"," to be ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Too small: expected ".concat(e.origin," to have ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Too small: expected ".concat(e.origin," to be ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Invalid string: must start with "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Invalid string: must end with "'.concat(e.suffix,'"');if("includes"===e.format)return'Invalid string: must include "'.concat(e.includes,'"');if("regex"===e.format)return"Invalid string: must match pattern ".concat(e.pattern);return"Invalid ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Invalid number: must be a multiple of ".concat(e.divisor);case"unrecognized_keys":return"Unrecognized key".concat(e.keys.length>1?"s":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Invalid key in ".concat(e.origin);case"invalid_union":default:return"Invalid input";case"invalid_element":return"Invalid value in ".concat(e.origin)}}})()}}function ey(){return{localeError:(()=>{let e={string:{unit:"karaktrojn",verb:"havi"},file:{unit:"bajtojn",verb:"havi"},array:{unit:"elementojn",verb:"havi"},set:{unit:"elementojn",verb:"havi"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"enigo",email:"retadreso",url:"URL",emoji:"emoĝio",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datotempo",date:"ISO-dato",time:"ISO-tempo",duration:"ISO-daŭro",ipv4:"IPv4-adreso",ipv6:"IPv6-adreso",cidrv4:"IPv4-rango",cidrv6:"IPv6-rango",base64:"64-ume kodita karaktraro",base64url:"URL-64-ume kodita karaktraro",json_string:"JSON-karaktraro",e164:"E.164-nombro",jwt:"JWT",template_literal:"enigo"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Nevalida enigo: atendiĝis ".concat(e.expected,", riceviĝis ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"nombro";case"object":if(Array.isArray(e))return"tabelo";if(null===e)return"senvalora";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Nevalida enigo: atendiĝis ".concat(t.stringifyPrimitive(e.values[0]));return"Nevalida opcio: atendiĝis unu el ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Tro granda: atendiĝis ke ".concat(null!=(i=e.origin)?i:"valoro"," havu ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elementojn");return"Tro granda: atendiĝis ke ".concat(null!=(o=e.origin)?o:"valoro"," havu ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Tro malgranda: atendiĝis ke ".concat(e.origin," havu ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Tro malgranda: atendiĝis ke ".concat(e.origin," estu ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Nevalida karaktraro: devas komenciĝi per "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Nevalida karaktraro: devas finiĝi per "'.concat(e.suffix,'"');if("includes"===e.format)return'Nevalida karaktraro: devas inkluzivi "'.concat(e.includes,'"');if("regex"===e.format)return"Nevalida karaktraro: devas kongrui kun la modelo ".concat(e.pattern);return"Nevalida ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Nevalida nombro: devas esti oblo de ".concat(e.divisor);case"unrecognized_keys":return"Nekonata".concat(e.keys.length>1?"j":""," ŝlosilo").concat(e.keys.length>1?"j":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Nevalida ŝlosilo en ".concat(e.origin);case"invalid_union":default:return"Nevalida enigo";case"invalid_element":return"Nevalida valoro en ".concat(e.origin)}}})()}}function e_(){return{localeError:(()=>{let e={string:{unit:"caracteres",verb:"tener"},file:{unit:"bytes",verb:"tener"},array:{unit:"elementos",verb:"tener"},set:{unit:"elementos",verb:"tener"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"entrada",email:"dirección de correo electrónico",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"fecha y hora ISO",date:"fecha ISO",time:"hora ISO",duration:"duración ISO",ipv4:"dirección IPv4",ipv6:"dirección IPv6",cidrv4:"rango IPv4",cidrv6:"rango IPv6",base64:"cadena codificada en base64",base64url:"URL codificada en base64",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Entrada inválida: se esperaba ".concat(e.expected,", recibido ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"número";case"object":if(Array.isArray(e))return"arreglo";if(null===e)return"nulo";if(Object.getPrototypeOf(e)!==Object.prototype)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Entrada inválida: se esperaba ".concat(t.stringifyPrimitive(e.values[0]));return"Opción inválida: se esperaba una de ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Demasiado grande: se esperaba que ".concat(null!=(i=e.origin)?i:"valor"," tuviera ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elementos");return"Demasiado grande: se esperaba que ".concat(null!=(o=e.origin)?o:"valor"," fuera ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Demasiado pequeño: se esperaba que ".concat(e.origin," tuviera ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Demasiado pequeño: se esperaba que ".concat(e.origin," fuera ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Cadena inválida: debe comenzar con "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Cadena inválida: debe terminar en "'.concat(e.suffix,'"');if("includes"===e.format)return'Cadena inválida: debe incluir "'.concat(e.includes,'"');if("regex"===e.format)return"Cadena inválida: debe coincidir con el patrón ".concat(e.pattern);return"Inválido ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Número inválido: debe ser múltiplo de ".concat(e.divisor);case"unrecognized_keys":return"Llave".concat(e.keys.length>1?"s":""," desconocida").concat(e.keys.length>1?"s":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Llave inválida en ".concat(e.origin);case"invalid_union":default:return"Entrada inválida";case"invalid_element":return"Valor inválido en ".concat(e.origin)}}})()}}function eb(){return{localeError:(()=>{let e={string:{unit:"کاراکتر",verb:"داشته باشد"},file:{unit:"بایت",verb:"داشته باشد"},array:{unit:"آیتم",verb:"داشته باشد"},set:{unit:"آیتم",verb:"داشته باشد"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"ورودی",email:"آدرس ایمیل",url:"URL",emoji:"ایموجی",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاریخ و زمان ایزو",date:"تاریخ ایزو",time:"زمان ایزو",duration:"مدت زمان ایزو",ipv4:"IPv4 آدرس",ipv6:"IPv6 آدرس",cidrv4:"IPv4 دامنه",cidrv6:"IPv6 دامنه",base64:"base64-encoded رشته",base64url:"base64url-encoded رشته",json_string:"JSON رشته",e164:"E.164 عدد",jwt:"JWT",template_literal:"ورودی"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"ورودی نامعتبر: می‌بایست ".concat(e.expected," می‌بود، ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"عدد";case"object":if(Array.isArray(e))return"آرایه";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input)," دریافت شد");case"invalid_value":if(1===e.values.length)return"ورودی نامعتبر: می‌بایست ".concat(t.stringifyPrimitive(e.values[0])," می‌بود");return"گزینه نامعتبر: می‌بایست یکی از ".concat(t.joinValues(e.values,"|")," می‌بود");case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"خیلی بزرگ: ".concat(null!=(i=e.origin)?i:"مقدار"," باید ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"عنصر"," باشد");return"خیلی بزرگ: ".concat(null!=(o=e.origin)?o:"مقدار"," باید ").concat(t).concat(e.maximum.toString()," باشد")}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"خیلی کوچک: ".concat(e.origin," باید ").concat(t).concat(e.minimum.toString()," ").concat(r.unit," باشد");return"خیلی کوچک: ".concat(e.origin," باید ").concat(t).concat(e.minimum.toString()," باشد")}case"invalid_format":if("starts_with"===e.format)return'رشته نامعتبر: باید با "'.concat(e.prefix,'" شروع شود');if("ends_with"===e.format)return'رشته نامعتبر: باید با "'.concat(e.suffix,'" تمام شود');if("includes"===e.format)return'رشته نامعتبر: باید شامل "'.concat(e.includes,'" باشد');if("regex"===e.format)return"رشته نامعتبر: باید با الگوی ".concat(e.pattern," مطابقت داشته باشد");return"".concat(null!=(c=r[e.format])?c:e.format," نامعتبر");case"not_multiple_of":return"عدد نامعتبر: باید مضرب ".concat(e.divisor," باشد");case"unrecognized_keys":return"کلید".concat(e.keys.length>1?"های":""," ناشناس: ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"کلید ناشناس در ".concat(e.origin);case"invalid_union":default:return"ورودی نامعتبر";case"invalid_element":return"مقدار نامعتبر در ".concat(e.origin)}}})()}}function ek(){return{localeError:(()=>{let e={string:{unit:"merkkiä",subject:"merkkijonon"},file:{unit:"tavua",subject:"tiedoston"},array:{unit:"alkiota",subject:"listan"},set:{unit:"alkiota",subject:"joukon"},number:{unit:"",subject:"luvun"},bigint:{unit:"",subject:"suuren kokonaisluvun"},int:{unit:"",subject:"kokonaisluvun"},date:{unit:"",subject:"päivämäärän"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"säännöllinen lauseke",email:"sähköpostiosoite",url:"URL-osoite",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-aikaleima",date:"ISO-päivämäärä",time:"ISO-aika",duration:"ISO-kesto",ipv4:"IPv4-osoite",ipv6:"IPv6-osoite",cidrv4:"IPv4-alue",cidrv6:"IPv6-alue",base64:"base64-koodattu merkkijono",base64url:"base64url-koodattu merkkijono",json_string:"JSON-merkkijono",e164:"E.164-luku",jwt:"JWT",template_literal:"templaattimerkkijono"};return e=>{switch(e.code){case"invalid_type":return"Virheellinen tyyppi: odotettiin ".concat(e.expected,", oli ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Virheellinen syöte: täytyy olla ".concat(t.stringifyPrimitive(e.values[0]));return"Virheellinen valinta: täytyy olla yksi seuraavista: ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Liian suuri: ".concat(r.subject," täytyy olla ").concat(t).concat(e.maximum.toString()," ").concat(r.unit).trim();return"Liian suuri: arvon täytyy olla ".concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Liian pieni: ".concat(r.subject," täytyy olla ").concat(t).concat(e.minimum.toString()," ").concat(r.unit).trim();return"Liian pieni: arvon täytyy olla ".concat(t).concat(e.minimum.toString())}case"invalid_format":var i;if("starts_with"===e.format)return'Virheellinen syöte: täytyy alkaa "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Virheellinen syöte: täytyy loppua "'.concat(e.suffix,'"');if("includes"===e.format)return'Virheellinen syöte: täytyy sisältää "'.concat(e.includes,'"');if("regex"===e.format)return"Virheellinen syöte: täytyy vastata säännöllistä lauseketta ".concat(e.pattern);return"Virheellinen ".concat(null!=(i=r[e.format])?i:e.format);case"not_multiple_of":return"Virheellinen luku: täytyy olla luvun ".concat(e.divisor," monikerta");case"unrecognized_keys":return"".concat(e.keys.length>1?"Tuntemattomat avaimet":"Tuntematon avain",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Virheellinen avain tietueessa";case"invalid_union":return"Virheellinen unioni";case"invalid_element":return"Virheellinen arvo joukossa";default:return"Virheellinen syöte"}}})()}}function eI(){return{localeError:(()=>{let e={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"entrée",email:"adresse e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date et heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Entrée invalide : ".concat(e.expected," attendu, ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"nombre";case"object":if(Array.isArray(e))return"tableau";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input)," reçu");case"invalid_value":if(1===e.values.length)return"Entrée invalide : ".concat(t.stringifyPrimitive(e.values[0])," attendu");return"Option invalide : une valeur parmi ".concat(t.joinValues(e.values,"|")," attendue");case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Trop grand : ".concat(null!=(i=e.origin)?i:"valeur"," doit ").concat(r.verb," ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"élément(s)");return"Trop grand : ".concat(null!=(o=e.origin)?o:"valeur"," doit être ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Trop petit : ".concat(e.origin," doit ").concat(r.verb," ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Trop petit : ".concat(e.origin," doit être ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Chaîne invalide : doit commencer par "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Chaîne invalide : doit se terminer par "'.concat(e.suffix,'"');if("includes"===e.format)return'Chaîne invalide : doit inclure "'.concat(e.includes,'"');if("regex"===e.format)return"Chaîne invalide : doit correspondre au modèle ".concat(e.pattern);return"".concat(null!=(c=r[e.format])?c:e.format," invalide");case"not_multiple_of":return"Nombre invalide : doit être un multiple de ".concat(e.divisor);case"unrecognized_keys":return"Clé".concat(e.keys.length>1?"s":""," non reconnue").concat(e.keys.length>1?"s":""," : ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Clé invalide dans ".concat(e.origin);case"invalid_union":default:return"Entrée invalide";case"invalid_element":return"Valeur invalide dans ".concat(e.origin)}}})()}}function ez(){return{localeError:(()=>{let e={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"entrée",email:"adresse courriel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date-heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return e=>{var i,a,o;switch(e.code){case"invalid_type":return"Entrée invalide : attendu ".concat(e.expected,", reçu ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Entrée invalide : attendu ".concat(t.stringifyPrimitive(e.values[0]));return"Option invalide : attendu l'une des valeurs suivantes ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"≤":"<",r=n(e.origin);if(r)return"Trop grand : attendu que ".concat(null!=(i=e.origin)?i:"la valeur"," ait ").concat(t).concat(e.maximum.toString()," ").concat(r.unit);return"Trop grand : attendu que ".concat(null!=(a=e.origin)?a:"la valeur"," soit ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?"≥":">",r=n(e.origin);if(r)return"Trop petit : attendu que ".concat(e.origin," ait ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Trop petit : attendu que ".concat(e.origin," soit ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Chaîne invalide : doit commencer par "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Chaîne invalide : doit se terminer par "'.concat(e.suffix,'"');if("includes"===e.format)return'Chaîne invalide : doit inclure "'.concat(e.includes,'"');if("regex"===e.format)return"Chaîne invalide : doit correspondre au motif ".concat(e.pattern);return"".concat(null!=(o=r[e.format])?o:e.format," invalide");case"not_multiple_of":return"Nombre invalide : doit être un multiple de ".concat(e.divisor);case"unrecognized_keys":return"Clé".concat(e.keys.length>1?"s":""," non reconnue").concat(e.keys.length>1?"s":""," : ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Clé invalide dans ".concat(e.origin);case"invalid_union":default:return"Entrée invalide";case"invalid_element":return"Valeur invalide dans ".concat(e.origin)}}})()}}function ew(){return{localeError:(()=>{let e={string:{unit:"אותיות",verb:"לכלול"},file:{unit:"בייטים",verb:"לכלול"},array:{unit:"פריטים",verb:"לכלול"},set:{unit:"פריטים",verb:"לכלול"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"קלט",email:"כתובת אימייל",url:"כתובת רשת",emoji:"אימוג'י",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"תאריך וזמן ISO",date:"תאריך ISO",time:"זמן ISO",duration:"משך זמן ISO",ipv4:"כתובת IPv4",ipv6:"כתובת IPv6",cidrv4:"טווח IPv4",cidrv6:"טווח IPv6",base64:"מחרוזת בבסיס 64",base64url:"מחרוזת בבסיס 64 לכתובות רשת",json_string:"מחרוזת JSON",e164:"מספר E.164",jwt:"JWT",template_literal:"קלט"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"קלט לא תקין: צריך ".concat(e.expected,", התקבל ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"קלט לא תקין: צריך ".concat(t.stringifyPrimitive(e.values[0]));return"קלט לא תקין: צריך אחת מהאפשרויות  ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"גדול מדי: ".concat(null!=(i=e.origin)?i:"value"," צריך להיות ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elements");return"גדול מדי: ".concat(null!=(o=e.origin)?o:"value"," צריך להיות ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"קטן מדי: ".concat(e.origin," צריך להיות ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"קטן מדי: ".concat(e.origin," צריך להיות ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'מחרוזת לא תקינה: חייבת להתחיל ב"'.concat(e.prefix,'"');if("ends_with"===e.format)return'מחרוזת לא תקינה: חייבת להסתיים ב "'.concat(e.suffix,'"');if("includes"===e.format)return'מחרוזת לא תקינה: חייבת לכלול "'.concat(e.includes,'"');if("regex"===e.format)return"מחרוזת לא תקינה: חייבת להתאים לתבנית ".concat(e.pattern);return"".concat(null!=(c=r[e.format])?c:e.format," לא תקין");case"not_multiple_of":return"מספר לא תקין: חייב להיות מכפלה של ".concat(e.divisor);case"unrecognized_keys":return"מפתח".concat(e.keys.length>1?"ות":""," לא מזוה").concat(e.keys.length>1?"ים":"ה",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"מפתח לא תקין ב".concat(e.origin);case"invalid_union":default:return"קלט לא תקין";case"invalid_element":return"ערך לא תקין ב".concat(e.origin)}}})()}}function eS(){return{localeError:(()=>{let e={string:{unit:"karakter",verb:"legyen"},file:{unit:"byte",verb:"legyen"},array:{unit:"elem",verb:"legyen"},set:{unit:"elem",verb:"legyen"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"bemenet",email:"email cím",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO időbélyeg",date:"ISO dátum",time:"ISO idő",duration:"ISO időintervallum",ipv4:"IPv4 cím",ipv6:"IPv6 cím",cidrv4:"IPv4 tartomány",cidrv6:"IPv6 tartomány",base64:"base64-kódolt string",base64url:"base64url-kódolt string",json_string:"JSON string",e164:"E.164 szám",jwt:"JWT",template_literal:"bemenet"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Érvénytelen bemenet: a várt érték ".concat(e.expected,", a kapott érték ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"szám";case"object":if(Array.isArray(e))return"tömb";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Érvénytelen bemenet: a várt érték ".concat(t.stringifyPrimitive(e.values[0]));return"Érvénytelen opció: valamelyik érték várt ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Túl nagy: ".concat(null!=(i=e.origin)?i:"érték"," mérete túl nagy ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elem");return"Túl nagy: a bemeneti érték ".concat(null!=(o=e.origin)?o:"érték"," túl nagy: ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Túl kicsi: a bemeneti érték ".concat(e.origin," mérete túl kicsi ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Túl kicsi: a bemeneti érték ".concat(e.origin," túl kicsi ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Érvénytelen string: "'.concat(e.prefix,'" értékkel kell kezdődnie');if("ends_with"===e.format)return'Érvénytelen string: "'.concat(e.suffix,'" értékkel kell végződnie');if("includes"===e.format)return'Érvénytelen string: "'.concat(e.includes,'" értéket kell tartalmaznia');if("regex"===e.format)return"Érvénytelen string: ".concat(e.pattern," mintának kell megfelelnie");return"Érvénytelen ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Érvénytelen szám: ".concat(e.divisor," többszörösének kell lennie");case"unrecognized_keys":return"Ismeretlen kulcs".concat(e.keys.length>1?"s":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Érvénytelen kulcs ".concat(e.origin);case"invalid_union":default:return"Érvénytelen bemenet";case"invalid_element":return"Érvénytelen érték: ".concat(e.origin)}}})()}}function eZ(){return{localeError:(()=>{let e={string:{unit:"karakter",verb:"memiliki"},file:{unit:"byte",verb:"memiliki"},array:{unit:"item",verb:"memiliki"},set:{unit:"item",verb:"memiliki"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"input",email:"alamat email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tanggal dan waktu format ISO",date:"tanggal format ISO",time:"jam format ISO",duration:"durasi format ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"rentang alamat IPv4",cidrv6:"rentang alamat IPv6",base64:"string dengan enkode base64",base64url:"string dengan enkode base64url",json_string:"string JSON",e164:"angka E.164",jwt:"JWT",template_literal:"input"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Input tidak valid: diharapkan ".concat(e.expected,", diterima ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Input tidak valid: diharapkan ".concat(t.stringifyPrimitive(e.values[0]));return"Pilihan tidak valid: diharapkan salah satu dari ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Terlalu besar: diharapkan ".concat(null!=(i=e.origin)?i:"value"," memiliki ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elemen");return"Terlalu besar: diharapkan ".concat(null!=(o=e.origin)?o:"value"," menjadi ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Terlalu kecil: diharapkan ".concat(e.origin," memiliki ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Terlalu kecil: diharapkan ".concat(e.origin," menjadi ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'String tidak valid: harus dimulai dengan "'.concat(e.prefix,'"');if("ends_with"===e.format)return'String tidak valid: harus berakhir dengan "'.concat(e.suffix,'"');if("includes"===e.format)return'String tidak valid: harus menyertakan "'.concat(e.includes,'"');if("regex"===e.format)return"String tidak valid: harus sesuai pola ".concat(e.pattern);return"".concat(null!=(c=r[e.format])?c:e.format," tidak valid");case"not_multiple_of":return"Angka tidak valid: harus kelipatan dari ".concat(e.divisor);case"unrecognized_keys":return"Kunci tidak dikenali ".concat(e.keys.length>1?"s":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Kunci tidak valid di ".concat(e.origin);case"invalid_union":default:return"Input tidak valid";case"invalid_element":return"Nilai tidak valid di ".concat(e.origin)}}})()}}function ex(){return{localeError:(()=>{let e={string:{unit:"stafi",verb:"að hafa"},file:{unit:"bæti",verb:"að hafa"},array:{unit:"hluti",verb:"að hafa"},set:{unit:"hluti",verb:"að hafa"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"gildi",email:"netfang",url:"vefslóð",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dagsetning og tími",date:"ISO dagsetning",time:"ISO tími",duration:"ISO tímalengd",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded strengur",base64url:"base64url-encoded strengur",json_string:"JSON strengur",e164:"E.164 tölugildi",jwt:"JWT",template_literal:"gildi"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Rangt gildi: Þú slóst inn ".concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"númer";case"object":if(Array.isArray(e))return"fylki";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input)," þar sem á að vera ").concat(e.expected);case"invalid_value":if(1===e.values.length)return"Rangt gildi: gert ráð fyrir ".concat(t.stringifyPrimitive(e.values[0]));return"Ógilt val: má vera eitt af eftirfarandi ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Of stórt: gert er ráð fyrir að ".concat(null!=(i=e.origin)?i:"gildi"," hafi ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"hluti");return"Of stórt: gert er ráð fyrir að ".concat(null!=(o=e.origin)?o:"gildi"," sé ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Of lítið: gert er ráð fyrir að ".concat(e.origin," hafi ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Of lítið: gert er ráð fyrir að ".concat(e.origin," sé ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Ógildur strengur: verður að byrja á "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Ógildur strengur: verður að enda á "'.concat(e.suffix,'"');if("includes"===e.format)return'Ógildur strengur: verður að innihalda "'.concat(e.includes,'"');if("regex"===e.format)return"Ógildur strengur: verður að fylgja mynstri ".concat(e.pattern);return"Rangt ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Röng tala: verður að vera margfeldi af ".concat(e.divisor);case"unrecognized_keys":return"Óþekkt ".concat(e.keys.length>1?"ir lyklar":"ur lykill",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Rangur lykill í ".concat(e.origin);case"invalid_union":default:return"Rangt gildi";case"invalid_element":return"Rangt gildi í ".concat(e.origin)}}})()}}function ej(){return{localeError:(()=>{let e={string:{unit:"caratteri",verb:"avere"},file:{unit:"byte",verb:"avere"},array:{unit:"elementi",verb:"avere"},set:{unit:"elementi",verb:"avere"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"input",email:"indirizzo email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e ora ISO",date:"data ISO",time:"ora ISO",duration:"durata ISO",ipv4:"indirizzo IPv4",ipv6:"indirizzo IPv6",cidrv4:"intervallo IPv4",cidrv6:"intervallo IPv6",base64:"stringa codificata in base64",base64url:"URL codificata in base64",json_string:"stringa JSON",e164:"numero E.164",jwt:"JWT",template_literal:"input"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Input non valido: atteso ".concat(e.expected,", ricevuto ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"numero";case"object":if(Array.isArray(e))return"vettore";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Input non valido: atteso ".concat(t.stringifyPrimitive(e.values[0]));return"Opzione non valida: atteso uno tra ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Troppo grande: ".concat(null!=(i=e.origin)?i:"valore"," deve avere ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elementi");return"Troppo grande: ".concat(null!=(o=e.origin)?o:"valore"," deve essere ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Troppo piccolo: ".concat(e.origin," deve avere ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Troppo piccolo: ".concat(e.origin," deve essere ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Stringa non valida: deve iniziare con "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Stringa non valida: deve terminare con "'.concat(e.suffix,'"');if("includes"===e.format)return'Stringa non valida: deve includere "'.concat(e.includes,'"');if("regex"===e.format)return"Stringa non valida: deve corrispondere al pattern ".concat(e.pattern);return"Invalid ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Numero non valido: deve essere un multiplo di ".concat(e.divisor);case"unrecognized_keys":return"Chiav".concat(e.keys.length>1?"i":"e"," non riconosciut").concat(e.keys.length>1?"e":"a",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Chiave non valida in ".concat(e.origin);case"invalid_union":default:return"Input non valido";case"invalid_element":return"Valore non valido in ".concat(e.origin)}}})()}}function e$(){return{localeError:(()=>{let e={string:{unit:"文字",verb:"である"},file:{unit:"バイト",verb:"である"},array:{unit:"要素",verb:"である"},set:{unit:"要素",verb:"である"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"入力値",email:"メールアドレス",url:"URL",emoji:"絵文字",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日時",date:"ISO日付",time:"ISO時刻",duration:"ISO期間",ipv4:"IPv4アドレス",ipv6:"IPv6アドレス",cidrv4:"IPv4範囲",cidrv6:"IPv6範囲",base64:"base64エンコード文字列",base64url:"base64urlエンコード文字列",json_string:"JSON文字列",e164:"E.164番号",jwt:"JWT",template_literal:"入力値"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"無効な入力: ".concat(e.expected,"が期待されましたが、").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"数値";case"object":if(Array.isArray(e))return"配列";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input),"が入力されました");case"invalid_value":if(1===e.values.length)return"無効な入力: ".concat(t.stringifyPrimitive(e.values[0]),"が期待されました");return"無効な選択: ".concat(t.joinValues(e.values,"、"),"のいずれかである必要があります");case"too_big":{let t=e.inclusive?"以下である":"より小さい",r=n(e.origin);if(r)return"大きすぎる値: ".concat(null!=(i=e.origin)?i:"値","は").concat(e.maximum.toString()).concat(null!=(a=r.unit)?a:"要素").concat(t,"必要があります");return"大きすぎる値: ".concat(null!=(o=e.origin)?o:"値","は").concat(e.maximum.toString()).concat(t,"必要があります")}case"too_small":{let t=e.inclusive?"以上である":"より大きい",r=n(e.origin);if(r)return"小さすぎる値: ".concat(e.origin,"は").concat(e.minimum.toString()).concat(r.unit).concat(t,"必要があります");return"小さすぎる値: ".concat(e.origin,"は").concat(e.minimum.toString()).concat(t,"必要があります")}case"invalid_format":if("starts_with"===e.format)return'無効な文字列: "'.concat(e.prefix,'"で始まる必要があります');if("ends_with"===e.format)return'無効な文字列: "'.concat(e.suffix,'"で終わる必要があります');if("includes"===e.format)return'無効な文字列: "'.concat(e.includes,'"を含む必要があります');if("regex"===e.format)return"無効な文字列: パターン".concat(e.pattern,"に一致する必要があります");return"無効な".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"無効な数値: ".concat(e.divisor,"の倍数である必要があります");case"unrecognized_keys":return"認識されていないキー".concat(e.keys.length>1?"群":"",": ").concat(t.joinValues(e.keys,"、"));case"invalid_key":return"".concat(e.origin,"内の無効なキー");case"invalid_union":default:return"無効な入力";case"invalid_element":return"".concat(e.origin,"内の無効な値")}}})()}}function eU(){return{localeError:(()=>{let e={string:{unit:"តួអក្សរ",verb:"គួរមាន"},file:{unit:"បៃ",verb:"គួរមាន"},array:{unit:"ធាតុ",verb:"គួរមាន"},set:{unit:"ធាតុ",verb:"គួរមាន"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"ទិន្នន័យបញ្ចូល",email:"អាសយដ្ឋានអ៊ីមែល",url:"URL",emoji:"សញ្ញាអារម្មណ៍",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"កាលបរិច្ឆេទ និងម៉ោង ISO",date:"កាលបរិច្ឆេទ ISO",time:"ម៉ោង ISO",duration:"រយៈពេល ISO",ipv4:"អាសយដ្ឋាន IPv4",ipv6:"អាសយដ្ឋាន IPv6",cidrv4:"ដែនអាសយដ្ឋាន IPv4",cidrv6:"ដែនអាសយដ្ឋាន IPv6",base64:"ខ្សែអក្សរអ៊ិកូដ base64",base64url:"ខ្សែអក្សរអ៊ិកូដ base64url",json_string:"ខ្សែអក្សរ JSON",e164:"លេខ E.164",jwt:"JWT",template_literal:"ទិន្នន័យបញ្ចូល"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ".concat(e.expected," ប៉ុន្តែទទួលបាន ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"មិនមែនជាលេខ (NaN)":"លេខ";case"object":if(Array.isArray(e))return"អារេ (Array)";if(null===e)return"គ្មានតម្លៃ (null)";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ".concat(t.stringifyPrimitive(e.values[0]));return"ជម្រើសមិនត្រឹមត្រូវ៖ ត្រូវជាមួយក្នុងចំណោម ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"ធំពេក៖ ត្រូវការ ".concat(null!=(i=e.origin)?i:"តម្លៃ"," ").concat(t," ").concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"ធាតុ");return"ធំពេក៖ ត្រូវការ ".concat(null!=(o=e.origin)?o:"តម្លៃ"," ").concat(t," ").concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"តូចពេក៖ ត្រូវការ ".concat(e.origin," ").concat(t," ").concat(e.minimum.toString()," ").concat(r.unit);return"តូចពេក៖ ត្រូវការ ".concat(e.origin," ").concat(t," ").concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវចាប់ផ្តើមដោយ "'.concat(e.prefix,'"');if("ends_with"===e.format)return'ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវបញ្ចប់ដោយ "'.concat(e.suffix,'"');if("includes"===e.format)return'ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវមាន "'.concat(e.includes,'"');if("regex"===e.format)return"ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវតែផ្គូផ្គងនឹងទម្រង់ដែលបានកំណត់ ".concat(e.pattern);return"មិនត្រឹមត្រូវ៖ ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"លេខមិនត្រឹមត្រូវ៖ ត្រូវតែជាពហុគុណនៃ ".concat(e.divisor);case"unrecognized_keys":return"រកឃើញសោមិនស្គាល់៖ ".concat(t.joinValues(e.keys,", "));case"invalid_key":return"សោមិនត្រឹមត្រូវនៅក្នុង ".concat(e.origin);case"invalid_union":default:return"ទិន្នន័យមិនត្រឹមត្រូវ";case"invalid_element":return"ទិន្នន័យមិនត្រឹមត្រូវនៅក្នុង ".concat(e.origin)}}})()}}function eO(){return{localeError:(()=>{let e={string:{unit:"문자",verb:"to have"},file:{unit:"바이트",verb:"to have"},array:{unit:"개",verb:"to have"},set:{unit:"개",verb:"to have"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"입력",email:"이메일 주소",url:"URL",emoji:"이모지",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 날짜시간",date:"ISO 날짜",time:"ISO 시간",duration:"ISO 기간",ipv4:"IPv4 주소",ipv6:"IPv6 주소",cidrv4:"IPv4 범위",cidrv6:"IPv6 범위",base64:"base64 인코딩 문자열",base64url:"base64url 인코딩 문자열",json_string:"JSON 문자열",e164:"E.164 번호",jwt:"JWT",template_literal:"입력"};return e=>{var i,a,o,c,u,l,s;switch(e.code){case"invalid_type":return"잘못된 입력: 예상 타입은 ".concat(e.expected,", 받은 타입은 ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input),"입니다");case"invalid_value":if(1===e.values.length)return"잘못된 입력: 값은 ".concat(t.stringifyPrimitive(e.values[0])," 이어야 합니다");return"잘못된 옵션: ".concat(t.joinValues(e.values,"또는 ")," 중 하나여야 합니다");case"too_big":{let t=e.inclusive?"이하":"미만",r="미만"===t?"이어야 합니다":"여야 합니다",c=n(e.origin),u=null!=(i=null==c?void 0:c.unit)?i:"요소";if(c)return"".concat(null!=(a=e.origin)?a:"값","이 너무 큽니다: ").concat(e.maximum.toString()).concat(u," ").concat(t).concat(r);return"".concat(null!=(o=e.origin)?o:"값","이 너무 큽니다: ").concat(e.maximum.toString()," ").concat(t).concat(r)}case"too_small":{let t=e.inclusive?"이상":"초과",r="이상"===t?"이어야 합니다":"여야 합니다",i=n(e.origin),a=null!=(c=null==i?void 0:i.unit)?c:"요소";if(i)return"".concat(null!=(u=e.origin)?u:"값","이 너무 작습니다: ").concat(e.minimum.toString()).concat(a," ").concat(t).concat(r);return"".concat(null!=(l=e.origin)?l:"값","이 너무 작습니다: ").concat(e.minimum.toString()," ").concat(t).concat(r)}case"invalid_format":if("starts_with"===e.format)return'잘못된 문자열: "'.concat(e.prefix,'"(으)로 시작해야 합니다');if("ends_with"===e.format)return'잘못된 문자열: "'.concat(e.suffix,'"(으)로 끝나야 합니다');if("includes"===e.format)return'잘못된 문자열: "'.concat(e.includes,'"을(를) 포함해야 합니다');if("regex"===e.format)return"잘못된 문자열: 정규식 ".concat(e.pattern," 패턴과 일치해야 합니다");return"잘못된 ".concat(null!=(s=r[e.format])?s:e.format);case"not_multiple_of":return"잘못된 숫자: ".concat(e.divisor,"의 배수여야 합니다");case"unrecognized_keys":return"인식할 수 없는 키: ".concat(t.joinValues(e.keys,", "));case"invalid_key":return"잘못된 키: ".concat(e.origin);case"invalid_union":default:return"잘못된 입력";case"invalid_element":return"잘못된 값: ".concat(e.origin)}}})()}}function eP(){return{localeError:(()=>{let e={string:{unit:"знаци",verb:"да имаат"},file:{unit:"бајти",verb:"да имаат"},array:{unit:"ставки",verb:"да имаат"},set:{unit:"ставки",verb:"да имаат"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"внес",email:"адреса на е-пошта",url:"URL",emoji:"емоџи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO датум и време",date:"ISO датум",time:"ISO време",duration:"ISO времетраење",ipv4:"IPv4 адреса",ipv6:"IPv6 адреса",cidrv4:"IPv4 опсег",cidrv6:"IPv6 опсег",base64:"base64-енкодирана низа",base64url:"base64url-енкодирана низа",json_string:"JSON низа",e164:"E.164 број",jwt:"JWT",template_literal:"внес"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Грешен внес: се очекува ".concat(e.expected,", примено ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"број";case"object":if(Array.isArray(e))return"низа";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Invalid input: expected ".concat(t.stringifyPrimitive(e.values[0]));return"Грешана опција: се очекува една ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Премногу голем: се очекува ".concat(null!=(i=e.origin)?i:"вредноста"," да има ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"елементи");return"Премногу голем: се очекува ".concat(null!=(o=e.origin)?o:"вредноста"," да биде ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Премногу мал: се очекува ".concat(e.origin," да има ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Премногу мал: се очекува ".concat(e.origin," да биде ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Неважечка низа: мора да започнува со "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Неважечка низа: мора да завршува со "'.concat(e.suffix,'"');if("includes"===e.format)return'Неважечка низа: мора да вклучува "'.concat(e.includes,'"');if("regex"===e.format)return"Неважечка низа: мора да одгоара на патернот ".concat(e.pattern);return"Invalid ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Грешен број: мора да биде делив со ".concat(e.divisor);case"unrecognized_keys":return"".concat(e.keys.length>1?"Непрепознаени клучеви":"Непрепознаен клуч",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Грешен клуч во ".concat(e.origin);case"invalid_union":default:return"Грешен внес";case"invalid_element":return"Грешна вредност во ".concat(e.origin)}}})()}}function eN(){return{localeError:(()=>{let e={string:{unit:"aksara",verb:"mempunyai"},file:{unit:"bait",verb:"mempunyai"},array:{unit:"elemen",verb:"mempunyai"},set:{unit:"elemen",verb:"mempunyai"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"input",email:"alamat e-mel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tarikh masa ISO",date:"tarikh ISO",time:"masa ISO",duration:"tempoh ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"julat IPv4",cidrv6:"julat IPv6",base64:"string dikodkan base64",base64url:"string dikodkan base64url",json_string:"string JSON",e164:"nombor E.164",jwt:"JWT",template_literal:"input"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Input tidak sah: dijangka ".concat(e.expected,", diterima ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"nombor";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Input tidak sah: dijangka ".concat(t.stringifyPrimitive(e.values[0]));return"Pilihan tidak sah: dijangka salah satu daripada ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Terlalu besar: dijangka ".concat(null!=(i=e.origin)?i:"nilai"," ").concat(r.verb," ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elemen");return"Terlalu besar: dijangka ".concat(null!=(o=e.origin)?o:"nilai"," adalah ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Terlalu kecil: dijangka ".concat(e.origin," ").concat(r.verb," ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Terlalu kecil: dijangka ".concat(e.origin," adalah ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'String tidak sah: mesti bermula dengan "'.concat(e.prefix,'"');if("ends_with"===e.format)return'String tidak sah: mesti berakhir dengan "'.concat(e.suffix,'"');if("includes"===e.format)return'String tidak sah: mesti mengandungi "'.concat(e.includes,'"');if("regex"===e.format)return"String tidak sah: mesti sepadan dengan corak ".concat(e.pattern);return"".concat(null!=(c=r[e.format])?c:e.format," tidak sah");case"not_multiple_of":return"Nombor tidak sah: perlu gandaan ".concat(e.divisor);case"unrecognized_keys":return"Kunci tidak dikenali: ".concat(t.joinValues(e.keys,", "));case"invalid_key":return"Kunci tidak sah dalam ".concat(e.origin);case"invalid_union":default:return"Input tidak sah";case"invalid_element":return"Nilai tidak sah dalam ".concat(e.origin)}}})()}}function eD(){return{localeError:(()=>{let e={string:{unit:"tekens"},file:{unit:"bytes"},array:{unit:"elementen"},set:{unit:"elementen"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"invoer",email:"emailadres",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum en tijd",date:"ISO datum",time:"ISO tijd",duration:"ISO duur",ipv4:"IPv4-adres",ipv6:"IPv6-adres",cidrv4:"IPv4-bereik",cidrv6:"IPv6-bereik",base64:"base64-gecodeerde tekst",base64url:"base64 URL-gecodeerde tekst",json_string:"JSON string",e164:"E.164-nummer",jwt:"JWT",template_literal:"invoer"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Ongeldige invoer: verwacht ".concat(e.expected,", ontving ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"getal";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Ongeldige invoer: verwacht ".concat(t.stringifyPrimitive(e.values[0]));return"Ongeldige optie: verwacht één van ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Te lang: verwacht dat ".concat(null!=(i=e.origin)?i:"waarde"," ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elementen"," bevat");return"Te lang: verwacht dat ".concat(null!=(o=e.origin)?o:"waarde"," ").concat(t).concat(e.maximum.toString()," is")}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Te kort: verwacht dat ".concat(e.origin," ").concat(t).concat(e.minimum.toString()," ").concat(r.unit," bevat");return"Te kort: verwacht dat ".concat(e.origin," ").concat(t).concat(e.minimum.toString()," is")}case"invalid_format":if("starts_with"===e.format)return'Ongeldige tekst: moet met "'.concat(e.prefix,'" beginnen');if("ends_with"===e.format)return'Ongeldige tekst: moet op "'.concat(e.suffix,'" eindigen');if("includes"===e.format)return'Ongeldige tekst: moet "'.concat(e.includes,'" bevatten');if("regex"===e.format)return"Ongeldige tekst: moet overeenkomen met patroon ".concat(e.pattern);return"Ongeldig: ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Ongeldig getal: moet een veelvoud van ".concat(e.divisor," zijn");case"unrecognized_keys":return"Onbekende key".concat(e.keys.length>1?"s":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Ongeldige key in ".concat(e.origin);case"invalid_union":default:return"Ongeldige invoer";case"invalid_element":return"Ongeldige waarde in ".concat(e.origin)}}})()}}function eE(){return{localeError:(()=>{let e={string:{unit:"tegn",verb:"å ha"},file:{unit:"bytes",verb:"å ha"},array:{unit:"elementer",verb:"å inneholde"},set:{unit:"elementer",verb:"å inneholde"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"input",email:"e-postadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslett",date:"ISO-dato",time:"ISO-klokkeslett",duration:"ISO-varighet",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spekter",cidrv6:"IPv6-spekter",base64:"base64-enkodet streng",base64url:"base64url-enkodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Ugyldig input: forventet ".concat(e.expected,", fikk ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"tall";case"object":if(Array.isArray(e))return"liste";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Ugyldig verdi: forventet ".concat(t.stringifyPrimitive(e.values[0]));return"Ugyldig valg: forventet en av ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"For stor(t): forventet ".concat(null!=(i=e.origin)?i:"value"," til å ha ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elementer");return"For stor(t): forventet ".concat(null!=(o=e.origin)?o:"value"," til å ha ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"For lite(n): forventet ".concat(e.origin," til å ha ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"For lite(n): forventet ".concat(e.origin," til å ha ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Ugyldig streng: må starte med "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Ugyldig streng: må ende med "'.concat(e.suffix,'"');if("includes"===e.format)return'Ugyldig streng: må inneholde "'.concat(e.includes,'"');if("regex"===e.format)return"Ugyldig streng: må matche mønsteret ".concat(e.pattern);return"Ugyldig ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Ugyldig tall: må være et multiplum av ".concat(e.divisor);case"unrecognized_keys":return"".concat(e.keys.length>1?"Ukjente nøkler":"Ukjent nøkkel",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Ugyldig nøkkel i ".concat(e.origin);case"invalid_union":default:return"Ugyldig input";case"invalid_element":return"Ugyldig verdi i ".concat(e.origin)}}})()}}function eA(){return{localeError:(()=>{let e={string:{unit:"harf",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"unsur",verb:"olmalıdır"},set:{unit:"unsur",verb:"olmalıdır"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"giren",email:"epostagâh",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO hengâmı",date:"ISO tarihi",time:"ISO zamanı",duration:"ISO müddeti",ipv4:"IPv4 nişânı",ipv6:"IPv6 nişânı",cidrv4:"IPv4 menzili",cidrv6:"IPv6 menzili",base64:"base64-şifreli metin",base64url:"base64url-şifreli metin",json_string:"JSON metin",e164:"E.164 sayısı",jwt:"JWT",template_literal:"giren"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Fâsit giren: umulan ".concat(e.expected,", alınan ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"numara";case"object":if(Array.isArray(e))return"saf";if(null===e)return"gayb";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Fâsit giren: umulan ".concat(t.stringifyPrimitive(e.values[0]));return"Fâsit tercih: mûteberler ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Fazla büyük: ".concat(null!=(i=e.origin)?i:"value",", ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elements"," sahip olmalıydı.");return"Fazla büyük: ".concat(null!=(o=e.origin)?o:"value",", ").concat(t).concat(e.maximum.toString()," olmalıydı.")}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Fazla küçük: ".concat(e.origin,", ").concat(t).concat(e.minimum.toString()," ").concat(r.unit," sahip olmalıydı.");return"Fazla küçük: ".concat(e.origin,", ").concat(t).concat(e.minimum.toString()," olmalıydı.")}case"invalid_format":if("starts_with"===e.format)return'Fâsit metin: "'.concat(e.prefix,'" ile başlamalı.');if("ends_with"===e.format)return'Fâsit metin: "'.concat(e.suffix,'" ile bitmeli.');if("includes"===e.format)return'Fâsit metin: "'.concat(e.includes,'" ihtivâ etmeli.');if("regex"===e.format)return"Fâsit metin: ".concat(e.pattern," nakşına uymalı.");return"Fâsit ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Fâsit sayı: ".concat(e.divisor," katı olmalıydı.");case"unrecognized_keys":return"Tanınmayan anahtar ".concat(e.keys.length>1?"s":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"".concat(e.origin," için tanınmayan anahtar var.");case"invalid_union":return"Giren tanınamadı.";case"invalid_element":return"".concat(e.origin," için tanınmayan kıymet var.");default:return"Kıymet tanınamadı."}}})()}}function eT(){return{localeError:(()=>{let e={string:{unit:"توکي",verb:"ولري"},file:{unit:"بایټس",verb:"ولري"},array:{unit:"توکي",verb:"ولري"},set:{unit:"توکي",verb:"ولري"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"ورودي",email:"بریښنالیک",url:"یو آر ال",emoji:"ایموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"نیټه او وخت",date:"نېټه",time:"وخت",duration:"موده",ipv4:"د IPv4 پته",ipv6:"د IPv6 پته",cidrv4:"د IPv4 ساحه",cidrv6:"د IPv6 ساحه",base64:"base64-encoded متن",base64url:"base64url-encoded متن",json_string:"JSON متن",e164:"د E.164 شمېره",jwt:"JWT",template_literal:"ورودي"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"ناسم ورودي: باید ".concat(e.expected," وای, مګر ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"عدد";case"object":if(Array.isArray(e))return"ارې";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input)," ترلاسه شو");case"invalid_value":if(1===e.values.length)return"ناسم ورودي: باید ".concat(t.stringifyPrimitive(e.values[0])," وای");return"ناسم انتخاب: باید یو له ".concat(t.joinValues(e.values,"|")," څخه وای");case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"ډیر لوی: ".concat(null!=(i=e.origin)?i:"ارزښت"," باید ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"عنصرونه"," ولري");return"ډیر لوی: ".concat(null!=(o=e.origin)?o:"ارزښت"," باید ").concat(t).concat(e.maximum.toString()," وي")}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"ډیر کوچنی: ".concat(e.origin," باید ").concat(t).concat(e.minimum.toString()," ").concat(r.unit," ولري");return"ډیر کوچنی: ".concat(e.origin," باید ").concat(t).concat(e.minimum.toString()," وي")}case"invalid_format":if("starts_with"===e.format)return'ناسم متن: باید د "'.concat(e.prefix,'" سره پیل شي');if("ends_with"===e.format)return'ناسم متن: باید د "'.concat(e.suffix,'" سره پای ته ورسيږي');if("includes"===e.format)return'ناسم متن: باید "'.concat(e.includes,'" ولري');if("regex"===e.format)return"ناسم متن: باید د ".concat(e.pattern," سره مطابقت ولري");return"".concat(null!=(c=r[e.format])?c:e.format," ناسم دی");case"not_multiple_of":return"ناسم عدد: باید د ".concat(e.divisor," مضرب وي");case"unrecognized_keys":return"ناسم ".concat(e.keys.length>1?"کلیډونه":"کلیډ",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"ناسم کلیډ په ".concat(e.origin," کې");case"invalid_union":default:return"ناسمه ورودي";case"invalid_element":return"ناسم عنصر په ".concat(e.origin," کې")}}})()}}function eL(){return{localeError:(()=>{let e={string:{unit:"znaków",verb:"mieć"},file:{unit:"bajtów",verb:"mieć"},array:{unit:"elementów",verb:"mieć"},set:{unit:"elementów",verb:"mieć"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"wyrażenie",email:"adres email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i godzina w formacie ISO",date:"data w formacie ISO",time:"godzina w formacie ISO",duration:"czas trwania ISO",ipv4:"adres IPv4",ipv6:"adres IPv6",cidrv4:"zakres IPv4",cidrv6:"zakres IPv6",base64:"ciąg znaków zakodowany w formacie base64",base64url:"ciąg znaków zakodowany w formacie base64url",json_string:"ciąg znaków w formacie JSON",e164:"liczba E.164",jwt:"JWT",template_literal:"wejście"};return e=>{var i,a,o,c,u,l,s;switch(e.code){case"invalid_type":return"Nieprawidłowe dane wejściowe: oczekiwano ".concat(e.expected,", otrzymano ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"liczba";case"object":if(Array.isArray(e))return"tablica";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Nieprawidłowe dane wejściowe: oczekiwano ".concat(t.stringifyPrimitive(e.values[0]));return"Nieprawidłowa opcja: oczekiwano jednej z wartości ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Za duża wartość: oczekiwano, że ".concat(null!=(i=e.origin)?i:"wartość"," będzie mieć ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elementów");return"Zbyt duż(y/a/e): oczekiwano, że ".concat(null!=(o=e.origin)?o:"wartość"," będzie wynosić ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Za mała wartość: oczekiwano, że ".concat(null!=(c=e.origin)?c:"wartość"," będzie mieć ").concat(t).concat(e.minimum.toString()," ").concat(null!=(u=r.unit)?u:"elementów");return"Zbyt mał(y/a/e): oczekiwano, że ".concat(null!=(l=e.origin)?l:"wartość"," będzie wynosić ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Nieprawidłowy ciąg znaków: musi zaczynać się od "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Nieprawidłowy ciąg znaków: musi kończyć się na "'.concat(e.suffix,'"');if("includes"===e.format)return'Nieprawidłowy ciąg znaków: musi zawierać "'.concat(e.includes,'"');if("regex"===e.format)return"Nieprawidłowy ciąg znaków: musi odpowiadać wzorcowi ".concat(e.pattern);return"Nieprawidłow(y/a/e) ".concat(null!=(s=r[e.format])?s:e.format);case"not_multiple_of":return"Nieprawidłowa liczba: musi być wielokrotnością ".concat(e.divisor);case"unrecognized_keys":return"Nierozpoznane klucze".concat(e.keys.length>1?"s":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Nieprawidłowy klucz w ".concat(e.origin);case"invalid_union":default:return"Nieprawidłowe dane wejściowe";case"invalid_element":return"Nieprawidłowa wartość w ".concat(e.origin)}}})()}}function eC(){return{localeError:(()=>{let e={string:{unit:"caracteres",verb:"ter"},file:{unit:"bytes",verb:"ter"},array:{unit:"itens",verb:"ter"},set:{unit:"itens",verb:"ter"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"padrão",email:"endereço de e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e hora ISO",date:"data ISO",time:"hora ISO",duration:"duração ISO",ipv4:"endereço IPv4",ipv6:"endereço IPv6",cidrv4:"faixa de IPv4",cidrv6:"faixa de IPv6",base64:"texto codificado em base64",base64url:"URL codificada em base64",json_string:"texto JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Tipo inválido: esperado ".concat(e.expected,", recebido ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"número";case"object":if(Array.isArray(e))return"array";if(null===e)return"nulo";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Entrada inválida: esperado ".concat(t.stringifyPrimitive(e.values[0]));return"Opção inválida: esperada uma das ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Muito grande: esperado que ".concat(null!=(i=e.origin)?i:"valor"," tivesse ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elementos");return"Muito grande: esperado que ".concat(null!=(o=e.origin)?o:"valor"," fosse ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Muito pequeno: esperado que ".concat(e.origin," tivesse ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Muito pequeno: esperado que ".concat(e.origin," fosse ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Texto inválido: deve começar com "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Texto inválido: deve terminar com "'.concat(e.suffix,'"');if("includes"===e.format)return'Texto inválido: deve incluir "'.concat(e.includes,'"');if("regex"===e.format)return"Texto inválido: deve corresponder ao padrão ".concat(e.pattern);return"".concat(null!=(c=r[e.format])?c:e.format," inválido");case"not_multiple_of":return"Número inválido: deve ser múltiplo de ".concat(e.divisor);case"unrecognized_keys":return"Chave".concat(e.keys.length>1?"s":""," desconhecida").concat(e.keys.length>1?"s":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Chave inválida em ".concat(e.origin);case"invalid_union":return"Entrada inválida";case"invalid_element":return"Valor inválido em ".concat(e.origin);default:return"Campo inválido"}}})()}}function eR(e,t,n,r){let i=Math.abs(e),a=i%10,o=i%100;return o>=11&&o<=19?r:1===a?t:a>=2&&a<=4?n:r}function eV(){return{localeError:(()=>{let e={string:{unit:{one:"символ",few:"символа",many:"символов"},verb:"иметь"},file:{unit:{one:"байт",few:"байта",many:"байт"},verb:"иметь"},array:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"},set:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"ввод",email:"email адрес",url:"URL",emoji:"эмодзи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата и время",date:"ISO дата",time:"ISO время",duration:"ISO длительность",ipv4:"IPv4 адрес",ipv6:"IPv6 адрес",cidrv4:"IPv4 диапазон",cidrv6:"IPv6 диапазон",base64:"строка в формате base64",base64url:"строка в формате base64url",json_string:"JSON строка",e164:"номер E.164",jwt:"JWT",template_literal:"ввод"};return e=>{var i,a,o;switch(e.code){case"invalid_type":return"Неверный ввод: ожидалось ".concat(e.expected,", получено ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"число";case"object":if(Array.isArray(e))return"массив";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Неверный ввод: ожидалось ".concat(t.stringifyPrimitive(e.values[0]));return"Неверный вариант: ожидалось одно из ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r){let n=eR(Number(e.maximum),r.unit.one,r.unit.few,r.unit.many);return"Слишком большое значение: ожидалось, что ".concat(null!=(i=e.origin)?i:"значение"," будет иметь ").concat(t).concat(e.maximum.toString()," ").concat(n)}return"Слишком большое значение: ожидалось, что ".concat(null!=(a=e.origin)?a:"значение"," будет ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r){let n=eR(Number(e.minimum),r.unit.one,r.unit.few,r.unit.many);return"Слишком маленькое значение: ожидалось, что ".concat(e.origin," будет иметь ").concat(t).concat(e.minimum.toString()," ").concat(n)}return"Слишком маленькое значение: ожидалось, что ".concat(e.origin," будет ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Неверная строка: должна начинаться с "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Неверная строка: должна заканчиваться на "'.concat(e.suffix,'"');if("includes"===e.format)return'Неверная строка: должна содержать "'.concat(e.includes,'"');if("regex"===e.format)return"Неверная строка: должна соответствовать шаблону ".concat(e.pattern);return"Неверный ".concat(null!=(o=r[e.format])?o:e.format);case"not_multiple_of":return"Неверное число: должно быть кратным ".concat(e.divisor);case"unrecognized_keys":return"Нераспознанн".concat(e.keys.length>1?"ые":"ый"," ключ").concat(e.keys.length>1?"и":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Неверный ключ в ".concat(e.origin);case"invalid_union":default:return"Неверные входные данные";case"invalid_element":return"Неверное значение в ".concat(e.origin)}}})()}}function eF(){return{localeError:(()=>{let e={string:{unit:"znakov",verb:"imeti"},file:{unit:"bajtov",verb:"imeti"},array:{unit:"elementov",verb:"imeti"},set:{unit:"elementov",verb:"imeti"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"vnos",email:"e-poštni naslov",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum in čas",date:"ISO datum",time:"ISO čas",duration:"ISO trajanje",ipv4:"IPv4 naslov",ipv6:"IPv6 naslov",cidrv4:"obseg IPv4",cidrv6:"obseg IPv6",base64:"base64 kodiran niz",base64url:"base64url kodiran niz",json_string:"JSON niz",e164:"E.164 številka",jwt:"JWT",template_literal:"vnos"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Neveljaven vnos: pričakovano ".concat(e.expected,", prejeto ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"število";case"object":if(Array.isArray(e))return"tabela";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Neveljaven vnos: pričakovano ".concat(t.stringifyPrimitive(e.values[0]));return"Neveljavna možnost: pričakovano eno izmed ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Preveliko: pričakovano, da bo ".concat(null!=(i=e.origin)?i:"vrednost"," imelo ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"elementov");return"Preveliko: pričakovano, da bo ".concat(null!=(o=e.origin)?o:"vrednost"," ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Premajhno: pričakovano, da bo ".concat(e.origin," imelo ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Premajhno: pričakovano, da bo ".concat(e.origin," ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Neveljaven niz: mora se začeti z "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Neveljaven niz: mora se končati z "'.concat(e.suffix,'"');if("includes"===e.format)return'Neveljaven niz: mora vsebovati "'.concat(e.includes,'"');if("regex"===e.format)return"Neveljaven niz: mora ustrezati vzorcu ".concat(e.pattern);return"Neveljaven ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Neveljavno število: mora biti večkratnik ".concat(e.divisor);case"unrecognized_keys":return"Neprepoznan".concat(e.keys.length>1?"i ključi":" ključ",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Neveljaven ključ v ".concat(e.origin);case"invalid_union":default:return"Neveljaven vnos";case"invalid_element":return"Neveljavna vrednost v ".concat(e.origin)}}})()}}function eJ(){return{localeError:(()=>{let e={string:{unit:"tecken",verb:"att ha"},file:{unit:"bytes",verb:"att ha"},array:{unit:"objekt",verb:"att innehålla"},set:{unit:"objekt",verb:"att innehålla"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"reguljärt uttryck",email:"e-postadress",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datum och tid",date:"ISO-datum",time:"ISO-tid",duration:"ISO-varaktighet",ipv4:"IPv4-intervall",ipv6:"IPv6-intervall",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodad sträng",base64url:"base64url-kodad sträng",json_string:"JSON-sträng",e164:"E.164-nummer",jwt:"JWT",template_literal:"mall-literal"};return e=>{var i,a,o,c,u,l,s,d;switch(e.code){case"invalid_type":return"Ogiltig inmatning: förväntat ".concat(e.expected,", fick ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"antal";case"object":if(Array.isArray(e))return"lista";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Ogiltig inmatning: förväntat ".concat(t.stringifyPrimitive(e.values[0]));return"Ogiltigt val: förväntade en av ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"För stor(t): förväntade ".concat(null!=(i=e.origin)?i:"värdet"," att ha ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"element");return"För stor(t): förväntat ".concat(null!=(o=e.origin)?o:"värdet"," att ha ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"För lite(t): förväntade ".concat(null!=(c=e.origin)?c:"värdet"," att ha ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"För lite(t): förväntade ".concat(null!=(u=e.origin)?u:"värdet"," att ha ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Ogiltig sträng: måste börja med "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Ogiltig sträng: måste sluta med "'.concat(e.suffix,'"');if("includes"===e.format)return'Ogiltig sträng: måste innehålla "'.concat(e.includes,'"');if("regex"===e.format)return'Ogiltig sträng: måste matcha mönstret "'.concat(e.pattern,'"');return"Ogiltig(t) ".concat(null!=(l=r[e.format])?l:e.format);case"not_multiple_of":return"Ogiltigt tal: måste vara en multipel av ".concat(e.divisor);case"unrecognized_keys":return"".concat(e.keys.length>1?"Okända nycklar":"Okänd nyckel",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Ogiltig nyckel i ".concat(null!=(s=e.origin)?s:"värdet");case"invalid_union":default:return"Ogiltig input";case"invalid_element":return"Ogiltigt värde i ".concat(null!=(d=e.origin)?d:"värdet")}}})()}}function eM(){return{localeError:(()=>{let e={string:{unit:"எழுத்துக்கள்",verb:"கொண்டிருக்க வேண்டும்"},file:{unit:"பைட்டுகள்",verb:"கொண்டிருக்க வேண்டும்"},array:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"},set:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"உள்ளீடு",email:"மின்னஞ்சல் முகவரி",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO தேதி நேரம்",date:"ISO தேதி",time:"ISO நேரம்",duration:"ISO கால அளவு",ipv4:"IPv4 முகவரி",ipv6:"IPv6 முகவரி",cidrv4:"IPv4 வரம்பு",cidrv6:"IPv6 வரம்பு",base64:"base64-encoded சரம்",base64url:"base64url-encoded சரம்",json_string:"JSON சரம்",e164:"E.164 எண்",jwt:"JWT",template_literal:"input"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ".concat(e.expected,", பெறப்பட்டது ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"எண் அல்லாதது":"எண்";case"object":if(Array.isArray(e))return"அணி";if(null===e)return"வெறுமை";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ".concat(t.stringifyPrimitive(e.values[0]));return"தவறான விருப்பம்: எதிர்பார்க்கப்பட்டது ".concat(t.joinValues(e.values,"|")," இல் ஒன்று");case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"மிக பெரியது: எதிர்பார்க்கப்பட்டது ".concat(null!=(i=e.origin)?i:"மதிப்பு"," ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"உறுப்புகள்"," ஆக இருக்க வேண்டும்");return"மிக பெரியது: எதிர்பார்க்கப்பட்டது ".concat(null!=(o=e.origin)?o:"மதிப்பு"," ").concat(t).concat(e.maximum.toString()," ஆக இருக்க வேண்டும்")}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ".concat(e.origin," ").concat(t).concat(e.minimum.toString()," ").concat(r.unit," ஆக இருக்க வேண்டும்");return"மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ".concat(e.origin," ").concat(t).concat(e.minimum.toString()," ஆக இருக்க வேண்டும்")}case"invalid_format":if("starts_with"===e.format)return'தவறான சரம்: "'.concat(e.prefix,'" இல் தொடங்க வேண்டும்');if("ends_with"===e.format)return'தவறான சரம்: "'.concat(e.suffix,'" இல் முடிவடைய வேண்டும்');if("includes"===e.format)return'தவறான சரம்: "'.concat(e.includes,'" ஐ உள்ளடக்க வேண்டும்');if("regex"===e.format)return"தவறான சரம்: ".concat(e.pattern," முறைபாட்டுடன் பொருந்த வேண்டும்");return"தவறான ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"தவறான எண்: ".concat(e.divisor," இன் பலமாக இருக்க வேண்டும்");case"unrecognized_keys":return"அடையாளம் தெரியாத விசை".concat(e.keys.length>1?"கள்":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"".concat(e.origin," இல் தவறான விசை");case"invalid_union":default:return"தவறான உள்ளீடு";case"invalid_element":return"".concat(e.origin," இல் தவறான மதிப்பு")}}})()}}function eW(){return{localeError:(()=>{let e={string:{unit:"ตัวอักษร",verb:"ควรมี"},file:{unit:"ไบต์",verb:"ควรมี"},array:{unit:"รายการ",verb:"ควรมี"},set:{unit:"รายการ",verb:"ควรมี"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"ข้อมูลที่ป้อน",email:"ที่อยู่อีเมล",url:"URL",emoji:"อิโมจิ",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"วันที่เวลาแบบ ISO",date:"วันที่แบบ ISO",time:"เวลาแบบ ISO",duration:"ช่วงเวลาแบบ ISO",ipv4:"ที่อยู่ IPv4",ipv6:"ที่อยู่ IPv6",cidrv4:"ช่วง IP แบบ IPv4",cidrv6:"ช่วง IP แบบ IPv6",base64:"ข้อความแบบ Base64",base64url:"ข้อความแบบ Base64 สำหรับ URL",json_string:"ข้อความแบบ JSON",e164:"เบอร์โทรศัพท์ระหว่างประเทศ (E.164)",jwt:"โทเคน JWT",template_literal:"ข้อมูลที่ป้อน"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"ประเภทข้อมูลไม่ถูกต้อง: ควรเป็น ".concat(e.expected," แต่ได้รับ ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"ไม่ใช่ตัวเลข (NaN)":"ตัวเลข";case"object":if(Array.isArray(e))return"อาร์เรย์ (Array)";if(null===e)return"ไม่มีค่า (null)";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"ค่าไม่ถูกต้อง: ควรเป็น ".concat(t.stringifyPrimitive(e.values[0]));return"ตัวเลือกไม่ถูกต้อง: ควรเป็นหนึ่งใน ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"ไม่เกิน":"น้อยกว่า",r=n(e.origin);if(r)return"เกินกำหนด: ".concat(null!=(i=e.origin)?i:"ค่า"," ควรมี").concat(t," ").concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"รายการ");return"เกินกำหนด: ".concat(null!=(o=e.origin)?o:"ค่า"," ควรมี").concat(t," ").concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?"อย่างน้อย":"มากกว่า",r=n(e.origin);if(r)return"น้อยกว่ากำหนด: ".concat(e.origin," ควรมี").concat(t," ").concat(e.minimum.toString()," ").concat(r.unit);return"น้อยกว่ากำหนด: ".concat(e.origin," ควรมี").concat(t," ").concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'รูปแบบไม่ถูกต้อง: ข้อความต้องขึ้นต้นด้วย "'.concat(e.prefix,'"');if("ends_with"===e.format)return'รูปแบบไม่ถูกต้อง: ข้อความต้องลงท้ายด้วย "'.concat(e.suffix,'"');if("includes"===e.format)return'รูปแบบไม่ถูกต้อง: ข้อความต้องมี "'.concat(e.includes,'" อยู่ในข้อความ');if("regex"===e.format)return"รูปแบบไม่ถูกต้อง: ต้องตรงกับรูปแบบที่กำหนด ".concat(e.pattern);return"รูปแบบไม่ถูกต้อง: ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"ตัวเลขไม่ถูกต้อง: ต้องเป็นจำนวนที่หารด้วย ".concat(e.divisor," ได้ลงตัว");case"unrecognized_keys":return"พบคีย์ที่ไม่รู้จัก: ".concat(t.joinValues(e.keys,", "));case"invalid_key":return"คีย์ไม่ถูกต้องใน ".concat(e.origin);case"invalid_union":return"ข้อมูลไม่ถูกต้อง: ไม่ตรงกับรูปแบบยูเนียนที่กำหนดไว้";case"invalid_element":return"ข้อมูลไม่ถูกต้องใน ".concat(e.origin);default:return"ข้อมูลไม่ถูกต้อง"}}})()}}function eB(){return{localeError:(()=>{let e={string:{unit:"karakter",verb:"olmalı"},file:{unit:"bayt",verb:"olmalı"},array:{unit:"öğe",verb:"olmalı"},set:{unit:"öğe",verb:"olmalı"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"girdi",email:"e-posta adresi",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO tarih ve saat",date:"ISO tarih",time:"ISO saat",duration:"ISO süre",ipv4:"IPv4 adresi",ipv6:"IPv6 adresi",cidrv4:"IPv4 aralığı",cidrv6:"IPv6 aralığı",base64:"base64 ile şifrelenmiş metin",base64url:"base64url ile şifrelenmiş metin",json_string:"JSON dizesi",e164:"E.164 sayısı",jwt:"JWT",template_literal:"Şablon dizesi"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Geçersiz değer: beklenen ".concat(e.expected,", alınan ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Geçersiz değer: beklenen ".concat(t.stringifyPrimitive(e.values[0]));return"Geçersiz seçenek: aşağıdakilerden biri olmalı: ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Çok büyük: beklenen ".concat(null!=(i=e.origin)?i:"değer"," ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"öğe");return"Çok büyük: beklenen ".concat(null!=(o=e.origin)?o:"değer"," ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Çok küçük: beklenen ".concat(e.origin," ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Çok küçük: beklenen ".concat(e.origin," ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Geçersiz metin: "'.concat(e.prefix,'" ile başlamalı');if("ends_with"===e.format)return'Geçersiz metin: "'.concat(e.suffix,'" ile bitmeli');if("includes"===e.format)return'Geçersiz metin: "'.concat(e.includes,'" içermeli');if("regex"===e.format)return"Geçersiz metin: ".concat(e.pattern," desenine uymalı");return"Geçersiz ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Geçersiz sayı: ".concat(e.divisor," ile tam bölünebilmeli");case"unrecognized_keys":return"Tanınmayan anahtar".concat(e.keys.length>1?"lar":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"".concat(e.origin," içinde geçersiz anahtar");case"invalid_union":default:return"Geçersiz değer";case"invalid_element":return"".concat(e.origin," içinde geçersiz değer")}}})()}}function eK(){return{localeError:(()=>{let e={string:{unit:"символів",verb:"матиме"},file:{unit:"байтів",verb:"матиме"},array:{unit:"елементів",verb:"матиме"},set:{unit:"елементів",verb:"матиме"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"вхідні дані",email:"адреса електронної пошти",url:"URL",emoji:"емодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"дата та час ISO",date:"дата ISO",time:"час ISO",duration:"тривалість ISO",ipv4:"адреса IPv4",ipv6:"адреса IPv6",cidrv4:"діапазон IPv4",cidrv6:"діапазон IPv6",base64:"рядок у кодуванні base64",base64url:"рядок у кодуванні base64url",json_string:"рядок JSON",e164:"номер E.164",jwt:"JWT",template_literal:"вхідні дані"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Неправильні вхідні дані: очікується ".concat(e.expected,", отримано ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"число";case"object":if(Array.isArray(e))return"масив";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Неправильні вхідні дані: очікується ".concat(t.stringifyPrimitive(e.values[0]));return"Неправильна опція: очікується одне з ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Занадто велике: очікується, що ".concat(null!=(i=e.origin)?i:"значення"," ").concat(r.verb," ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"елементів");return"Занадто велике: очікується, що ".concat(null!=(o=e.origin)?o:"значення"," буде ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Занадто мале: очікується, що ".concat(e.origin," ").concat(r.verb," ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Занадто мале: очікується, що ".concat(e.origin," буде ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Неправильний рядок: повинен починатися з "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Неправильний рядок: повинен закінчуватися на "'.concat(e.suffix,'"');if("includes"===e.format)return'Неправильний рядок: повинен містити "'.concat(e.includes,'"');if("regex"===e.format)return"Неправильний рядок: повинен відповідати шаблону ".concat(e.pattern);return"Неправильний ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"Неправильне число: повинно бути кратним ".concat(e.divisor);case"unrecognized_keys":return"Нерозпізнаний ключ".concat(e.keys.length>1?"і":"",": ").concat(t.joinValues(e.keys,", "));case"invalid_key":return"Неправильний ключ у ".concat(e.origin);case"invalid_union":default:return"Неправильні вхідні дані";case"invalid_element":return"Неправильне значення у ".concat(e.origin)}}})()}}function eG(){return{localeError:(()=>{let e={string:{unit:"حروف",verb:"ہونا"},file:{unit:"بائٹس",verb:"ہونا"},array:{unit:"آئٹمز",verb:"ہونا"},set:{unit:"آئٹمز",verb:"ہونا"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"ان پٹ",email:"ای میل ایڈریس",url:"یو آر ایل",emoji:"ایموجی",uuid:"یو یو آئی ڈی",uuidv4:"یو یو آئی ڈی وی 4",uuidv6:"یو یو آئی ڈی وی 6",nanoid:"نینو آئی ڈی",guid:"جی یو آئی ڈی",cuid:"سی یو آئی ڈی",cuid2:"سی یو آئی ڈی 2",ulid:"یو ایل آئی ڈی",xid:"ایکس آئی ڈی",ksuid:"کے ایس یو آئی ڈی",datetime:"آئی ایس او ڈیٹ ٹائم",date:"آئی ایس او تاریخ",time:"آئی ایس او وقت",duration:"آئی ایس او مدت",ipv4:"آئی پی وی 4 ایڈریس",ipv6:"آئی پی وی 6 ایڈریس",cidrv4:"آئی پی وی 4 رینج",cidrv6:"آئی پی وی 6 رینج",base64:"بیس 64 ان کوڈڈ سٹرنگ",base64url:"بیس 64 یو آر ایل ان کوڈڈ سٹرنگ",json_string:"جے ایس او این سٹرنگ",e164:"ای 164 نمبر",jwt:"جے ڈبلیو ٹی",template_literal:"ان پٹ"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"غلط ان پٹ: ".concat(e.expected," متوقع تھا، ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"نمبر";case"object":if(Array.isArray(e))return"آرے";if(null===e)return"نل";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input)," موصول ہوا");case"invalid_value":if(1===e.values.length)return"غلط ان پٹ: ".concat(t.stringifyPrimitive(e.values[0])," متوقع تھا");return"غلط آپشن: ".concat(t.joinValues(e.values,"|")," میں سے ایک متوقع تھا");case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"بہت بڑا: ".concat(null!=(i=e.origin)?i:"ویلیو"," کے ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"عناصر"," ہونے متوقع تھے");return"بہت بڑا: ".concat(null!=(o=e.origin)?o:"ویلیو"," کا ").concat(t).concat(e.maximum.toString()," ہونا متوقع تھا")}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"بہت چھوٹا: ".concat(e.origin," کے ").concat(t).concat(e.minimum.toString()," ").concat(r.unit," ہونے متوقع تھے");return"بہت چھوٹا: ".concat(e.origin," کا ").concat(t).concat(e.minimum.toString()," ہونا متوقع تھا")}case"invalid_format":if("starts_with"===e.format)return'غلط سٹرنگ: "'.concat(e.prefix,'" سے شروع ہونا چاہیے');if("ends_with"===e.format)return'غلط سٹرنگ: "'.concat(e.suffix,'" پر ختم ہونا چاہیے');if("includes"===e.format)return'غلط سٹرنگ: "'.concat(e.includes,'" شامل ہونا چاہیے');if("regex"===e.format)return"غلط سٹرنگ: پیٹرن ".concat(e.pattern," سے میچ ہونا چاہیے");return"غلط ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"غلط نمبر: ".concat(e.divisor," کا مضاعف ہونا چاہیے");case"unrecognized_keys":return"غیر تسلیم شدہ کی".concat(e.keys.length>1?"ز":"",": ").concat(t.joinValues(e.keys,"، "));case"invalid_key":return"".concat(e.origin," میں غلط کی");case"invalid_union":default:return"غلط ان پٹ";case"invalid_element":return"".concat(e.origin," میں غلط ویلیو")}}})()}}function eX(){return{localeError:(()=>{let e={string:{unit:"ký tự",verb:"có"},file:{unit:"byte",verb:"có"},array:{unit:"phần tử",verb:"có"},set:{unit:"phần tử",verb:"có"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"đầu vào",email:"địa chỉ email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ngày giờ ISO",date:"ngày ISO",time:"giờ ISO",duration:"khoảng thời gian ISO",ipv4:"địa chỉ IPv4",ipv6:"địa chỉ IPv6",cidrv4:"dải IPv4",cidrv6:"dải IPv6",base64:"chuỗi mã hóa base64",base64url:"chuỗi mã hóa base64url",json_string:"chuỗi JSON",e164:"số E.164",jwt:"JWT",template_literal:"đầu vào"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"Đầu vào không hợp lệ: mong đợi ".concat(e.expected,", nhận được ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"số";case"object":if(Array.isArray(e))return"mảng";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Đầu vào không hợp lệ: mong đợi ".concat(t.stringifyPrimitive(e.values[0]));return"Tùy chọn không hợp lệ: mong đợi một trong các giá trị ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Quá lớn: mong đợi ".concat(null!=(i=e.origin)?i:"giá trị"," ").concat(r.verb," ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"phần tử");return"Quá lớn: mong đợi ".concat(null!=(o=e.origin)?o:"giá trị"," ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Quá nhỏ: mong đợi ".concat(e.origin," ").concat(r.verb," ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"Quá nhỏ: mong đợi ".concat(e.origin," ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'Chuỗi không hợp lệ: phải bắt đầu bằng "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Chuỗi không hợp lệ: phải kết thúc bằng "'.concat(e.suffix,'"');if("includes"===e.format)return'Chuỗi không hợp lệ: phải bao gồm "'.concat(e.includes,'"');if("regex"===e.format)return"Chuỗi không hợp lệ: phải khớp với mẫu ".concat(e.pattern);return"".concat(null!=(c=r[e.format])?c:e.format," không hợp lệ");case"not_multiple_of":return"Số không hợp lệ: phải là bội số của ".concat(e.divisor);case"unrecognized_keys":return"Khóa không được nhận dạng: ".concat(t.joinValues(e.keys,", "));case"invalid_key":return"Khóa không hợp lệ trong ".concat(e.origin);case"invalid_union":default:return"Đầu vào không hợp lệ";case"invalid_element":return"Giá trị không hợp lệ trong ".concat(e.origin)}}})()}}function eq(){return{localeError:(()=>{let e={string:{unit:"字符",verb:"包含"},file:{unit:"字节",verb:"包含"},array:{unit:"项",verb:"包含"},set:{unit:"项",verb:"包含"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"输入",email:"电子邮件",url:"URL",emoji:"表情符号",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日期时间",date:"ISO日期",time:"ISO时间",duration:"ISO时长",ipv4:"IPv4地址",ipv6:"IPv6地址",cidrv4:"IPv4网段",cidrv6:"IPv6网段",base64:"base64编码字符串",base64url:"base64url编码字符串",json_string:"JSON字符串",e164:"E.164号码",jwt:"JWT",template_literal:"输入"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"无效输入：期望 ".concat(e.expected,"，实际接收 ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"非数字(NaN)":"数字";case"object":if(Array.isArray(e))return"数组";if(null===e)return"空值(null)";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"无效输入：期望 ".concat(t.stringifyPrimitive(e.values[0]));return"无效选项：期望以下之一 ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"数值过大：期望 ".concat(null!=(i=e.origin)?i:"值"," ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"个元素");return"数值过大：期望 ".concat(null!=(o=e.origin)?o:"值"," ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"数值过小：期望 ".concat(e.origin," ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"数值过小：期望 ".concat(e.origin," ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'无效字符串：必须以 "'.concat(e.prefix,'" 开头');if("ends_with"===e.format)return'无效字符串：必须以 "'.concat(e.suffix,'" 结尾');if("includes"===e.format)return'无效字符串：必须包含 "'.concat(e.includes,'"');if("regex"===e.format)return"无效字符串：必须满足正则表达式 ".concat(e.pattern);return"无效".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"无效数字：必须是 ".concat(e.divisor," 的倍数");case"unrecognized_keys":return"出现未知的键(key): ".concat(t.joinValues(e.keys,", "));case"invalid_key":return"".concat(e.origin," 中的键(key)无效");case"invalid_union":default:return"无效输入";case"invalid_element":return"".concat(e.origin," 中包含无效值(value)")}}})()}}function eY(){return{localeError:(()=>{let e={string:{unit:"字元",verb:"擁有"},file:{unit:"位元組",verb:"擁有"},array:{unit:"項目",verb:"擁有"},set:{unit:"項目",verb:"擁有"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"輸入",email:"郵件地址",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 日期時間",date:"ISO 日期",time:"ISO 時間",duration:"ISO 期間",ipv4:"IPv4 位址",ipv6:"IPv6 位址",cidrv4:"IPv4 範圍",cidrv6:"IPv6 範圍",base64:"base64 編碼字串",base64url:"base64url 編碼字串",json_string:"JSON 字串",e164:"E.164 數值",jwt:"JWT",template_literal:"輸入"};return e=>{var i,a,o,c;switch(e.code){case"invalid_type":return"無效的輸入值：預期為 ".concat(e.expected,"，但收到 ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"無效的輸入值：預期為 ".concat(t.stringifyPrimitive(e.values[0]));return"無效的選項：預期為以下其中之一 ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"數值過大：預期 ".concat(null!=(i=e.origin)?i:"值"," 應為 ").concat(t).concat(e.maximum.toString()," ").concat(null!=(a=r.unit)?a:"個元素");return"數值過大：預期 ".concat(null!=(o=e.origin)?o:"值"," 應為 ").concat(t).concat(e.maximum.toString())}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"數值過小：預期 ".concat(e.origin," 應為 ").concat(t).concat(e.minimum.toString()," ").concat(r.unit);return"數值過小：預期 ".concat(e.origin," 應為 ").concat(t).concat(e.minimum.toString())}case"invalid_format":if("starts_with"===e.format)return'無效的字串：必須以 "'.concat(e.prefix,'" 開頭');if("ends_with"===e.format)return'無效的字串：必須以 "'.concat(e.suffix,'" 結尾');if("includes"===e.format)return'無效的字串：必須包含 "'.concat(e.includes,'"');if("regex"===e.format)return"無效的字串：必須符合格式 ".concat(e.pattern);return"無效的 ".concat(null!=(c=r[e.format])?c:e.format);case"not_multiple_of":return"無效的數字：必須為 ".concat(e.divisor," 的倍數");case"unrecognized_keys":return"無法識別的鍵值".concat(e.keys.length>1?"們":"","：").concat(t.joinValues(e.keys,"、"));case"invalid_key":return"".concat(e.origin," 中有無效的鍵值");case"invalid_union":default:return"無效的輸入值";case"invalid_element":return"".concat(e.origin," 中有無效的值")}}})()}}function eH(){return{localeError:(()=>{let e={string:{unit:"àmi",verb:"ní"},file:{unit:"bytes",verb:"ní"},array:{unit:"nkan",verb:"ní"},set:{unit:"nkan",verb:"ní"}};function n(t){var n;return null!=(n=e[t])?n:null}let r={regex:"ẹ̀rọ ìbáwọlé",email:"àdírẹ́sì ìmẹ́lì",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"àkókò ISO",date:"ọjọ́ ISO",time:"àkókò ISO",duration:"àkókò tó pé ISO",ipv4:"àdírẹ́sì IPv4",ipv6:"àdírẹ́sì IPv6",cidrv4:"àgbègbè IPv4",cidrv6:"àgbègbè IPv6",base64:"ọ̀rọ̀ tí a kọ́ ní base64",base64url:"ọ̀rọ̀ base64url",json_string:"ọ̀rọ̀ JSON",e164:"nọ́mbà E.164",jwt:"JWT",template_literal:"ẹ̀rọ ìbáwọlé"};return e=>{var i,a;switch(e.code){case"invalid_type":return"Ìbáwọlé aṣìṣe: a ní láti fi ".concat(e.expected,", àmọ̀ a rí ").concat((e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"nọ́mbà";case"object":if(Array.isArray(e))return"akopọ";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(e.input));case"invalid_value":if(1===e.values.length)return"Ìbáwọlé aṣìṣe: a ní láti fi ".concat(t.stringifyPrimitive(e.values[0]));return"Àṣàyàn aṣìṣe: yan ọ̀kan lára ".concat(t.joinValues(e.values,"|"));case"too_big":{let t=e.inclusive?"<=":"<",r=n(e.origin);if(r)return"Tó pọ̀ jù: a ní láti jẹ́ pé ".concat(null!=(i=e.origin)?i:"iye"," ").concat(r.verb," ").concat(t).concat(e.maximum," ").concat(r.unit);return"Tó pọ̀ jù: a ní láti jẹ́ ".concat(t).concat(e.maximum)}case"too_small":{let t=e.inclusive?">=":">",r=n(e.origin);if(r)return"Kéré ju: a ní láti jẹ́ pé ".concat(e.origin," ").concat(r.verb," ").concat(t).concat(e.minimum," ").concat(r.unit);return"Kéré ju: a ní láti jẹ́ ".concat(t).concat(e.minimum)}case"invalid_format":if("starts_with"===e.format)return'Ọ̀rọ̀ aṣìṣe: gbọ́dọ̀ bẹ̀rẹ̀ pẹ̀lú "'.concat(e.prefix,'"');if("ends_with"===e.format)return'Ọ̀rọ̀ aṣìṣe: gbọ́dọ̀ parí pẹ̀lú "'.concat(e.suffix,'"');if("includes"===e.format)return'Ọ̀rọ̀ aṣìṣe: gbọ́dọ̀ ní "'.concat(e.includes,'"');if("regex"===e.format)return"Ọ̀rọ̀ aṣìṣe: gbọ́dọ̀ bá àpẹẹrẹ mu ".concat(e.pattern);return"Aṣìṣe: ".concat(null!=(a=r[e.format])?a:e.format);case"not_multiple_of":return"Nọ́mbà aṣìṣe: gbọ́dọ̀ jẹ́ èyà pípín ti ".concat(e.divisor);case"unrecognized_keys":return"Bọtìnì àìmọ̀: ".concat(t.joinValues(e.keys,", "));case"invalid_key":return"Bọtìnì aṣìṣe nínú ".concat(e.origin);case"invalid_union":default:return"Ìbáwọlé aṣìṣe";case"invalid_element":return"Iye aṣìṣe nínú ".concat(e.origin)}}})()}}e.i(17486),e.s(["ar",()=>el,"az",()=>es,"be",()=>em,"ca",()=>ef,"cs",()=>ev,"da",()=>ep,"de",()=>eg,"en",()=>eh,"eo",()=>ey,"es",()=>e_,"fa",()=>eb,"fi",()=>ek,"fr",()=>eI,"frCA",()=>ez,"he",()=>ew,"hu",()=>eS,"id",()=>eZ,"is",()=>ex,"it",()=>ej,"ja",()=>e$,"kh",()=>eU,"ko",()=>eO,"mk",()=>eP,"ms",()=>eN,"nl",()=>eD,"no",()=>eE,"ota",()=>eA,"pl",()=>eL,"ps",()=>eT,"pt",()=>eC,"ru",()=>eV,"sl",()=>eF,"sv",()=>eJ,"ta",()=>eM,"th",()=>eW,"tr",()=>eB,"ua",()=>eK,"ur",()=>eG,"vi",()=>eX,"yo",()=>eH,"zhCN",()=>eq,"zhTW",()=>eY],49131),e.s([],35234),e.i(35234),e.s(["default",()=>eh],46590),e.i(49131),e.s([],64085),e.i(64085)},45086,e=>{"use strict";e.s(["z",()=>ou],45086),e.s(["$brand",()=>on.$brand,"$input",()=>ts,"$output",()=>tl,"NEVER",()=>on.NEVER,"TimePrecision",()=>tL,"ZodAny",()=>i$,"ZodArray",()=>iC,"ZodBase64",()=>r1,"ZodBase64URL",()=>r9,"ZodBigInt",()=>iy,"ZodBigIntFormat",()=>ib,"ZodBoolean",()=>ig,"ZodCIDRv4",()=>rQ,"ZodCIDRv6",()=>r6,"ZodCUID",()=>rC,"ZodCUID2",()=>rV,"ZodCatch",()=>a_,"ZodCodec",()=>aS,"ZodCustom",()=>aT,"ZodCustomStringFormat",()=>it,"ZodDate",()=>iT,"ZodDefault",()=>ad,"ZodDiscriminatedUnion",()=>iG,"ZodE164",()=>r8,"ZodEmail",()=>rz,"ZodEmoji",()=>rE,"ZodEnum",()=>i8,"ZodError",()=>ri,"ZodFile",()=>an,"ZodFirstPartyTypeKind",()=>t,"ZodFunction",()=>aE,"ZodGUID",()=>rS,"ZodIPv4",()=>rX,"ZodIPv6",()=>rY,"ZodISODate",()=>aG,"ZodISODateTime",()=>aB,"ZodISODuration",()=>aH,"ZodISOTime",()=>aq,"ZodIntersection",()=>iq,"ZodIssueCode",()=>a7,"ZodJWT",()=>r5,"ZodKSUID",()=>rK,"ZodLazy",()=>aO,"ZodLiteral",()=>ae,"ZodMap",()=>i1,"ZodNaN",()=>ak,"ZodNanoID",()=>rT,"ZodNever",()=>iN,"ZodNonOptional",()=>ap,"ZodNull",()=>ix,"ZodNullable",()=>au,"ZodNumber",()=>ic,"ZodNumberFormat",()=>il,"ZodObject",()=>iF,"ZodOptional",()=>ao,"ZodPipe",()=>az,"ZodPrefault",()=>af,"ZodPromise",()=>aN,"ZodReadonly",()=>ax,"ZodRealError",()=>ra,"ZodRecord",()=>i4,"ZodSet",()=>i9,"ZodString",()=>rb,"ZodStringFormat",()=>rI,"ZodSuccess",()=>ah,"ZodSymbol",()=>iz,"ZodTemplateLiteral",()=>a$,"ZodTransform",()=>ai,"ZodTuple",()=>iH,"ZodType",()=>ry,"ZodULID",()=>rJ,"ZodURL",()=>rP,"ZodUUID",()=>rx,"ZodUndefined",()=>iS,"ZodUnion",()=>iB,"ZodUnknown",()=>iO,"ZodVoid",()=>iE,"ZodXID",()=>rW,"_ZodString",()=>r_,"_default",()=>am,"_function",()=>aA,"any",()=>iU,"array",()=>iR,"base64",()=>r2,"base64url",()=>r3,"bigint",()=>i_,"boolean",()=>ih,"catch",()=>ab,"check",()=>aL,"cidrv4",()=>r4,"cidrv6",()=>r0,"clone",()=>or.clone,"codec",()=>aZ,"coerce",()=>oc,"config",()=>on.config,"core",()=>a8,"cuid",()=>rR,"cuid2",()=>rF,"custom",()=>aC,"date",()=>iL,"decode",()=>rd,"decodeAsync",()=>rf,"discriminatedUnion",()=>iX,"e164",()=>r7,"email",()=>rw,"emoji",()=>rA,"encode",()=>rs,"encodeAsync",()=>rm,"endsWith",()=>nI,"enum",()=>i7,"file",()=>ar,"flattenError",()=>oi.flattenError,"float32",()=>id,"float64",()=>im,"formatError",()=>oi.formatError,"function",()=>aA,"getErrorMap",()=>oe,"globalRegistry",()=>tf,"gt",()=>ni,"gte",()=>na,"guid",()=>rZ,"hash",()=>io,"hex",()=>ia,"hostname",()=>ii,"httpUrl",()=>rD,"includes",()=>nb,"instanceof",()=>aF,"int",()=>is,"int32",()=>iv,"int64",()=>ik,"intersection",()=>iY,"ipv4",()=>rq,"ipv6",()=>rH,"iso",()=>oo,"json",()=>aM,"jwt",()=>ie,"keyof",()=>iV,"ksuid",()=>rG,"lazy",()=>aP,"length",()=>ng,"literal",()=>at,"locales",()=>oa,"looseObject",()=>iW,"lowercase",()=>ny,"lt",()=>nn,"lte",()=>nr,"map",()=>i2,"maxLength",()=>nv,"maxSize",()=>nd,"mime",()=>nw,"minLength",()=>np,"minSize",()=>nm,"multipleOf",()=>ns,"nan",()=>aI,"nanoid",()=>rL,"nativeEnum",()=>i5,"negative",()=>nc,"never",()=>iD,"nonnegative",()=>nl,"nonoptional",()=>ag,"nonpositive",()=>nu,"normalize",()=>nZ,"null",()=>ij,"nullable",()=>al,"nullish",()=>as,"number",()=>iu,"object",()=>iJ,"optional",()=>ac,"overwrite",()=>nS,"parse",()=>ro,"parseAsync",()=>rc,"partialRecord",()=>i0,"pipe",()=>aw,"positive",()=>no,"prefault",()=>av,"preprocess",()=>aW,"prettifyError",()=>oi.prettifyError,"promise",()=>aD,"property",()=>nz,"readonly",()=>aj,"record",()=>i6,"refine",()=>aR,"regex",()=>nh,"regexes",()=>rt,"registry",()=>tm,"safeDecode",()=>rp,"safeDecodeAsync",()=>rh,"safeEncode",()=>rv,"safeEncodeAsync",()=>rg,"safeParse",()=>ru,"safeParseAsync",()=>rl,"set",()=>i3,"setErrorMap",()=>a5,"size",()=>nf,"startsWith",()=>nk,"strictObject",()=>iM,"string",()=>rk,"stringFormat",()=>ir,"stringbool",()=>aJ,"success",()=>ay,"superRefine",()=>aV,"symbol",()=>iw,"templateLiteral",()=>aU,"toJSONSchema",()=>n8,"toLowerCase",()=>nj,"toUpperCase",()=>n$,"transform",()=>aa,"treeifyError",()=>oi.treeifyError,"trim",()=>nx,"tuple",()=>iQ,"uint32",()=>ip,"uint64",()=>iI,"ulid",()=>rM,"undefined",()=>iZ,"union",()=>iK,"unknown",()=>iP,"uppercase",()=>n_,"url",()=>rN,"util",()=>rn,"uuid",()=>rj,"uuidv4",()=>r$,"uuidv6",()=>rU,"uuidv7",()=>rO,"void",()=>iA,"xid",()=>rB],93129),e.s([],75579),e.s(["$ZodAny",()=>eh,"$ZodArray",()=>ez,"$ZodAsyncError",()=>n.$ZodAsyncError,"$ZodBase64",()=>en,"$ZodBase64URL",()=>ei,"$ZodBigInt",()=>em,"$ZodBigIntFormat",()=>ef,"$ZodBoolean",()=>ed,"$ZodCIDRv4",()=>Q,"$ZodCIDRv6",()=>ee,"$ZodCUID",()=>F,"$ZodCUID2",()=>J,"$ZodCatch",()=>e4,"$ZodCheck",()=>c,"$ZodCheckBigIntFormat",()=>f,"$ZodCheckEndsWith",()=>Z,"$ZodCheckGreaterThan",()=>s,"$ZodCheckIncludes",()=>w,"$ZodCheckLengthEquals",()=>_,"$ZodCheckLessThan",()=>l,"$ZodCheckLowerCase",()=>I,"$ZodCheckMaxLength",()=>h,"$ZodCheckMaxSize",()=>v,"$ZodCheckMimeType",()=>$,"$ZodCheckMinLength",()=>y,"$ZodCheckMinSize",()=>p,"$ZodCheckMultipleOf",()=>d,"$ZodCheckNumberFormat",()=>m,"$ZodCheckOverwrite",()=>U,"$ZodCheckProperty",()=>j,"$ZodCheckRegex",()=>k,"$ZodCheckSizeEquals",()=>g,"$ZodCheckStartsWith",()=>S,"$ZodCheckStringFormat",()=>b,"$ZodCheckUpperCase",()=>z,"$ZodCodec",()=>e2,"$ZodCustom",()=>tr,"$ZodCustomStringFormat",()=>eu,"$ZodDate",()=>ek,"$ZodDefault",()=>eG,"$ZodDiscriminatedUnion",()=>eO,"$ZodE164",()=>ea,"$ZodEmail",()=>L,"$ZodEmoji",()=>R,"$ZodEncodeError",()=>n.$ZodEncodeError,"$ZodEnum",()=>eV,"$ZodError",()=>i.$ZodError,"$ZodFile",()=>eJ,"$ZodFunction",()=>te,"$ZodGUID",()=>A,"$ZodIPv4",()=>Y,"$ZodIPv6",()=>H,"$ZodISODate",()=>G,"$ZodISODateTime",()=>K,"$ZodISODuration",()=>q,"$ZodISOTime",()=>X,"$ZodIntersection",()=>eP,"$ZodJWT",()=>ec,"$ZodKSUID",()=>B,"$ZodLazy",()=>tn,"$ZodLiteral",()=>eF,"$ZodMap",()=>eT,"$ZodNaN",()=>e6,"$ZodNanoID",()=>V,"$ZodNever",()=>e_,"$ZodNonOptional",()=>eY,"$ZodNull",()=>eg,"$ZodNullable",()=>eK,"$ZodNumber",()=>el,"$ZodNumberFormat",()=>es,"$ZodObject",()=>ex,"$ZodObjectJIT",()=>ej,"$ZodOptional",()=>eB,"$ZodPipe",()=>e0,"$ZodPrefault",()=>eq,"$ZodPromise",()=>tt,"$ZodReadonly",()=>e8,"$ZodRealError",()=>i.$ZodRealError,"$ZodRecord",()=>eA,"$ZodRegistry",()=>td,"$ZodSet",()=>eC,"$ZodString",()=>D,"$ZodStringFormat",()=>E,"$ZodSuccess",()=>eQ,"$ZodSymbol",()=>ev,"$ZodTemplateLiteral",()=>e5,"$ZodTransform",()=>eM,"$ZodTuple",()=>eD,"$ZodType",()=>N,"$ZodULID",()=>M,"$ZodURL",()=>C,"$ZodUUID",()=>T,"$ZodUndefined",()=>ep,"$ZodUnion",()=>eU,"$ZodUnknown",()=>ey,"$ZodVoid",()=>eb,"$ZodXID",()=>W,"$brand",()=>n.$brand,"$constructor",()=>n.$constructor,"$input",()=>ts,"$output",()=>tl,"Doc",()=>O,"JSONSchema",()=>n7,"JSONSchemaGenerator",()=>n3,"NEVER",()=>n.NEVER,"TimePrecision",()=>tL,"_any",()=>t9,"_array",()=>nU,"_base64",()=>tD,"_base64url",()=>tE,"_bigint",()=>tH,"_boolean",()=>tq,"_catch",()=>nG,"_check",()=>n1,"_cidrv4",()=>tP,"_cidrv6",()=>tN,"_coercedBigint",()=>tQ,"_coercedBoolean",()=>tY,"_coercedDate",()=>ne,"_coercedNumber",()=>tM,"_coercedString",()=>tp,"_cuid",()=>tS,"_cuid2",()=>tZ,"_custom",()=>n4,"_date",()=>t5,"_decode",()=>r._decode,"_decodeAsync",()=>r._decodeAsync,"_default",()=>nW,"_discriminatedUnion",()=>nP,"_e164",()=>tA,"_email",()=>tg,"_emoji",()=>tz,"_encode",()=>r._encode,"_encodeAsync",()=>r._encodeAsync,"_endsWith",()=>nI,"_enum",()=>nL,"_file",()=>nV,"_float32",()=>tB,"_float64",()=>tK,"_gt",()=>ni,"_gte",()=>na,"_guid",()=>th,"_includes",()=>nb,"_int",()=>tW,"_int32",()=>tG,"_int64",()=>t4,"_intersection",()=>nN,"_ipv4",()=>tU,"_ipv6",()=>tO,"_isoDate",()=>tR,"_isoDateTime",()=>tC,"_isoDuration",()=>tF,"_isoTime",()=>tV,"_jwt",()=>tT,"_ksuid",()=>t$,"_lazy",()=>nH,"_length",()=>ng,"_literal",()=>nR,"_lowercase",()=>ny,"_lt",()=>nn,"_lte",()=>nr,"_map",()=>nA,"_max",()=>nr,"_maxLength",()=>nv,"_maxSize",()=>nd,"_mime",()=>nw,"_min",()=>na,"_minLength",()=>np,"_minSize",()=>nm,"_multipleOf",()=>ns,"_nan",()=>nt,"_nanoid",()=>tw,"_nativeEnum",()=>nC,"_negative",()=>nc,"_never",()=>t8,"_nonnegative",()=>nl,"_nonoptional",()=>nB,"_nonpositive",()=>nu,"_normalize",()=>nZ,"_null",()=>t2,"_nullable",()=>nM,"_number",()=>tJ,"_optional",()=>nJ,"_overwrite",()=>nS,"_parse",()=>r._parse,"_parseAsync",()=>r._parseAsync,"_pipe",()=>nX,"_positive",()=>no,"_promise",()=>nQ,"_property",()=>nz,"_readonly",()=>nq,"_record",()=>nE,"_refine",()=>n6,"_regex",()=>nh,"_safeDecode",()=>r._safeDecode,"_safeDecodeAsync",()=>r._safeDecodeAsync,"_safeEncode",()=>r._safeEncode,"_safeEncodeAsync",()=>r._safeEncodeAsync,"_safeParse",()=>r._safeParse,"_safeParseAsync",()=>r._safeParseAsync,"_set",()=>nT,"_size",()=>nf,"_startsWith",()=>nk,"_string",()=>tv,"_stringFormat",()=>n9,"_stringbool",()=>n2,"_success",()=>nK,"_superRefine",()=>n0,"_symbol",()=>t0,"_templateLiteral",()=>nY,"_toLowerCase",()=>nj,"_toUpperCase",()=>n$,"_transform",()=>nF,"_trim",()=>nx,"_tuple",()=>nD,"_uint32",()=>tX,"_uint64",()=>t6,"_ulid",()=>tx,"_undefined",()=>t1,"_union",()=>nO,"_unknown",()=>t3,"_uppercase",()=>n_,"_url",()=>tI,"_uuid",()=>ty,"_uuidv4",()=>t_,"_uuidv6",()=>tb,"_uuidv7",()=>tk,"_void",()=>t7,"_xid",()=>tj,"clone",()=>ta.clone,"config",()=>n.config,"decode",()=>r.decode,"decodeAsync",()=>r.decodeAsync,"encode",()=>r.encode,"encodeAsync",()=>r.encodeAsync,"flattenError",()=>i.flattenError,"formatError",()=>i.formatError,"globalConfig",()=>n.globalConfig,"globalRegistry",()=>tf,"isValidBase64",()=>et,"isValidBase64URL",()=>er,"isValidJWT",()=>eo,"locales",()=>tu,"parse",()=>r.parse,"parseAsync",()=>r.parseAsync,"prettifyError",()=>i.prettifyError,"regexes",()=>tc,"registry",()=>tm,"safeDecode",()=>r.safeDecode,"safeDecodeAsync",()=>r.safeDecodeAsync,"safeEncode",()=>r.safeEncode,"safeEncodeAsync",()=>r.safeEncodeAsync,"safeParse",()=>r.safeParse,"safeParseAsync",()=>r.safeParseAsync,"toDotPath",()=>i.toDotPath,"toJSONSchema",()=>n8,"treeifyError",()=>i.treeifyError,"util",()=>to,"version",()=>P],38171),e.i(47821);var t,n=e.i(39731),r=e.i(89601),i=e.i(78683);e.s(["$ZodAny",()=>eh,"$ZodArray",()=>ez,"$ZodBase64",()=>en,"$ZodBase64URL",()=>ei,"$ZodBigInt",()=>em,"$ZodBigIntFormat",()=>ef,"$ZodBoolean",()=>ed,"$ZodCIDRv4",()=>Q,"$ZodCIDRv6",()=>ee,"$ZodCUID",()=>F,"$ZodCUID2",()=>J,"$ZodCatch",()=>e4,"$ZodCodec",()=>e2,"$ZodCustom",()=>tr,"$ZodCustomStringFormat",()=>eu,"$ZodDate",()=>ek,"$ZodDefault",()=>eG,"$ZodDiscriminatedUnion",()=>eO,"$ZodE164",()=>ea,"$ZodEmail",()=>L,"$ZodEmoji",()=>R,"$ZodEnum",()=>eV,"$ZodFile",()=>eJ,"$ZodFunction",()=>te,"$ZodGUID",()=>A,"$ZodIPv4",()=>Y,"$ZodIPv6",()=>H,"$ZodISODate",()=>G,"$ZodISODateTime",()=>K,"$ZodISODuration",()=>q,"$ZodISOTime",()=>X,"$ZodIntersection",()=>eP,"$ZodJWT",()=>ec,"$ZodKSUID",()=>B,"$ZodLazy",()=>tn,"$ZodLiteral",()=>eF,"$ZodMap",()=>eT,"$ZodNaN",()=>e6,"$ZodNanoID",()=>V,"$ZodNever",()=>e_,"$ZodNonOptional",()=>eY,"$ZodNull",()=>eg,"$ZodNullable",()=>eK,"$ZodNumber",()=>el,"$ZodNumberFormat",()=>es,"$ZodObject",()=>ex,"$ZodObjectJIT",()=>ej,"$ZodOptional",()=>eB,"$ZodPipe",()=>e0,"$ZodPrefault",()=>eq,"$ZodPromise",()=>tt,"$ZodReadonly",()=>e8,"$ZodRecord",()=>eA,"$ZodSet",()=>eC,"$ZodString",()=>D,"$ZodStringFormat",()=>E,"$ZodSuccess",()=>eQ,"$ZodSymbol",()=>ev,"$ZodTemplateLiteral",()=>e5,"$ZodTransform",()=>eM,"$ZodTuple",()=>eD,"$ZodType",()=>N,"$ZodULID",()=>M,"$ZodURL",()=>C,"$ZodUUID",()=>T,"$ZodUndefined",()=>ep,"$ZodUnion",()=>eU,"$ZodUnknown",()=>ey,"$ZodVoid",()=>eb,"$ZodXID",()=>W,"clone",()=>ta.clone,"isValidBase64",()=>et,"isValidBase64URL",()=>er,"isValidJWT",()=>eo],82480),e.s(["$ZodAny",()=>eh,"$ZodArray",()=>ez,"$ZodBase64",()=>en,"$ZodBase64URL",()=>ei,"$ZodBigInt",()=>em,"$ZodBigIntFormat",()=>ef,"$ZodBoolean",()=>ed,"$ZodCIDRv4",()=>Q,"$ZodCIDRv6",()=>ee,"$ZodCUID",()=>F,"$ZodCUID2",()=>J,"$ZodCatch",()=>e4,"$ZodCodec",()=>e2,"$ZodCustom",()=>tr,"$ZodCustomStringFormat",()=>eu,"$ZodDate",()=>ek,"$ZodDefault",()=>eG,"$ZodDiscriminatedUnion",()=>eO,"$ZodE164",()=>ea,"$ZodEmail",()=>L,"$ZodEmoji",()=>R,"$ZodEnum",()=>eV,"$ZodFile",()=>eJ,"$ZodFunction",()=>te,"$ZodGUID",()=>A,"$ZodIPv4",()=>Y,"$ZodIPv6",()=>H,"$ZodISODate",()=>G,"$ZodISODateTime",()=>K,"$ZodISODuration",()=>q,"$ZodISOTime",()=>X,"$ZodIntersection",()=>eP,"$ZodJWT",()=>ec,"$ZodKSUID",()=>B,"$ZodLazy",()=>tn,"$ZodLiteral",()=>eF,"$ZodMap",()=>eT,"$ZodNaN",()=>e6,"$ZodNanoID",()=>V,"$ZodNever",()=>e_,"$ZodNonOptional",()=>eY,"$ZodNull",()=>eg,"$ZodNullable",()=>eK,"$ZodNumber",()=>el,"$ZodNumberFormat",()=>es,"$ZodObject",()=>ex,"$ZodObjectJIT",()=>ej,"$ZodOptional",()=>eB,"$ZodPipe",()=>e0,"$ZodPrefault",()=>eq,"$ZodPromise",()=>tt,"$ZodReadonly",()=>e8,"$ZodRecord",()=>eA,"$ZodSet",()=>eC,"$ZodString",()=>D,"$ZodStringFormat",()=>E,"$ZodSuccess",()=>eQ,"$ZodSymbol",()=>ev,"$ZodTemplateLiteral",()=>e5,"$ZodTransform",()=>eM,"$ZodTuple",()=>eD,"$ZodType",()=>N,"$ZodULID",()=>M,"$ZodURL",()=>C,"$ZodUUID",()=>T,"$ZodUndefined",()=>ep,"$ZodUnion",()=>eU,"$ZodUnknown",()=>ey,"$ZodVoid",()=>eb,"$ZodXID",()=>W,"isValidBase64",()=>et,"isValidBase64URL",()=>er,"isValidJWT",()=>eo],52346),e.s(["$ZodCheck",()=>c,"$ZodCheckBigIntFormat",()=>f,"$ZodCheckEndsWith",()=>Z,"$ZodCheckGreaterThan",()=>s,"$ZodCheckIncludes",()=>w,"$ZodCheckLengthEquals",()=>_,"$ZodCheckLessThan",()=>l,"$ZodCheckLowerCase",()=>I,"$ZodCheckMaxLength",()=>h,"$ZodCheckMaxSize",()=>v,"$ZodCheckMimeType",()=>$,"$ZodCheckMinLength",()=>y,"$ZodCheckMinSize",()=>p,"$ZodCheckMultipleOf",()=>d,"$ZodCheckNumberFormat",()=>m,"$ZodCheckOverwrite",()=>U,"$ZodCheckProperty",()=>j,"$ZodCheckRegex",()=>k,"$ZodCheckSizeEquals",()=>g,"$ZodCheckStartsWith",()=>S,"$ZodCheckStringFormat",()=>b,"$ZodCheckUpperCase",()=>z],86557);var a=e.i(17486),o=e.i(27874);let c=n.$constructor("$ZodCheck",(e,t)=>{var n;null!=e._zod||(e._zod={}),e._zod.def=t,null!=(n=e._zod).onattach||(n.onattach=[])}),u={number:"number",bigint:"bigint",object:"date"},l=n.$constructor("$ZodCheckLessThan",(e,t)=>{c.init(e,t);let n=u[typeof t.value];e._zod.onattach.push(e=>{var n;let r=e._zod.bag,i=null!=(n=t.inclusive?r.maximum:r.exclusiveMaximum)?n:1/0;t.value<i&&(t.inclusive?r.maximum=t.value:r.exclusiveMaximum=t.value)}),e._zod.check=r=>{(t.inclusive?r.value<=t.value:r.value<t.value)||r.issues.push({origin:n,code:"too_big",maximum:t.value,input:r.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),s=n.$constructor("$ZodCheckGreaterThan",(e,t)=>{c.init(e,t);let n=u[typeof t.value];e._zod.onattach.push(e=>{var n;let r=e._zod.bag,i=null!=(n=t.inclusive?r.minimum:r.exclusiveMinimum)?n:-1/0;t.value>i&&(t.inclusive?r.minimum=t.value:r.exclusiveMinimum=t.value)}),e._zod.check=r=>{(t.inclusive?r.value>=t.value:r.value>t.value)||r.issues.push({origin:n,code:"too_small",minimum:t.value,input:r.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),d=n.$constructor("$ZodCheckMultipleOf",(e,t)=>{c.init(e,t),e._zod.onattach.push(e=>{var n;null!=(n=e._zod.bag).multipleOf||(n.multipleOf=t.value)}),e._zod.check=n=>{if(typeof n.value!=typeof t.value)throw Error("Cannot mix number and bigint in multiple_of check.");("bigint"==typeof n.value?n.value%t.value===BigInt(0):0===o.floatSafeRemainder(n.value,t.value))||n.issues.push({origin:typeof n.value,code:"not_multiple_of",divisor:t.value,input:n.value,inst:e,continue:!t.abort})}}),m=n.$constructor("$ZodCheckNumberFormat",(e,t)=>{var n;c.init(e,t),t.format=t.format||"float64";let r=null==(n=t.format)?void 0:n.includes("int"),i=r?"int":"number",[u,l]=o.NUMBER_FORMAT_RANGES[t.format];e._zod.onattach.push(e=>{let n=e._zod.bag;n.format=t.format,n.minimum=u,n.maximum=l,r&&(n.pattern=a.integer)}),e._zod.check=n=>{let a=n.value;if(r){if(!Number.isInteger(a))return void n.issues.push({expected:i,format:t.format,code:"invalid_type",continue:!1,input:a,inst:e});if(!Number.isSafeInteger(a))return void(a>0?n.issues.push({input:a,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:i,continue:!t.abort}):n.issues.push({input:a,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:i,continue:!t.abort}))}a<u&&n.issues.push({origin:"number",input:a,code:"too_small",minimum:u,inclusive:!0,inst:e,continue:!t.abort}),a>l&&n.issues.push({origin:"number",input:a,code:"too_big",maximum:l,inst:e})}}),f=n.$constructor("$ZodCheckBigIntFormat",(e,t)=>{c.init(e,t);let[n,r]=o.BIGINT_FORMAT_RANGES[t.format];e._zod.onattach.push(e=>{let i=e._zod.bag;i.format=t.format,i.minimum=n,i.maximum=r}),e._zod.check=i=>{let a=i.value;a<n&&i.issues.push({origin:"bigint",input:a,code:"too_small",minimum:n,inclusive:!0,inst:e,continue:!t.abort}),a>r&&i.issues.push({origin:"bigint",input:a,code:"too_big",maximum:r,inst:e})}}),v=n.$constructor("$ZodCheckMaxSize",(e,t)=>{var n;c.init(e,t),null!=(n=e._zod.def).when||(n.when=e=>{let t=e.value;return!o.nullish(t)&&void 0!==t.size}),e._zod.onattach.push(e=>{var n;let r=null!=(n=e._zod.bag.maximum)?n:1/0;t.maximum<r&&(e._zod.bag.maximum=t.maximum)}),e._zod.check=n=>{let r=n.value;r.size<=t.maximum||n.issues.push({origin:o.getSizableOrigin(r),code:"too_big",maximum:t.maximum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),p=n.$constructor("$ZodCheckMinSize",(e,t)=>{var n;c.init(e,t),null!=(n=e._zod.def).when||(n.when=e=>{let t=e.value;return!o.nullish(t)&&void 0!==t.size}),e._zod.onattach.push(e=>{var n;let r=null!=(n=e._zod.bag.minimum)?n:-1/0;t.minimum>r&&(e._zod.bag.minimum=t.minimum)}),e._zod.check=n=>{let r=n.value;r.size>=t.minimum||n.issues.push({origin:o.getSizableOrigin(r),code:"too_small",minimum:t.minimum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),g=n.$constructor("$ZodCheckSizeEquals",(e,t)=>{var n;c.init(e,t),null!=(n=e._zod.def).when||(n.when=e=>{let t=e.value;return!o.nullish(t)&&void 0!==t.size}),e._zod.onattach.push(e=>{let n=e._zod.bag;n.minimum=t.size,n.maximum=t.size,n.size=t.size}),e._zod.check=n=>{let r=n.value,i=r.size;if(i===t.size)return;let a=i>t.size;n.issues.push({origin:o.getSizableOrigin(r),...a?{code:"too_big",maximum:t.size}:{code:"too_small",minimum:t.size},inclusive:!0,exact:!0,input:n.value,inst:e,continue:!t.abort})}}),h=n.$constructor("$ZodCheckMaxLength",(e,t)=>{var n;c.init(e,t),null!=(n=e._zod.def).when||(n.when=e=>{let t=e.value;return!o.nullish(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{var n;let r=null!=(n=e._zod.bag.maximum)?n:1/0;t.maximum<r&&(e._zod.bag.maximum=t.maximum)}),e._zod.check=n=>{let r=n.value;if(r.length<=t.maximum)return;let i=o.getLengthableOrigin(r);n.issues.push({origin:i,code:"too_big",maximum:t.maximum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),y=n.$constructor("$ZodCheckMinLength",(e,t)=>{var n;c.init(e,t),null!=(n=e._zod.def).when||(n.when=e=>{let t=e.value;return!o.nullish(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{var n;let r=null!=(n=e._zod.bag.minimum)?n:-1/0;t.minimum>r&&(e._zod.bag.minimum=t.minimum)}),e._zod.check=n=>{let r=n.value;if(r.length>=t.minimum)return;let i=o.getLengthableOrigin(r);n.issues.push({origin:i,code:"too_small",minimum:t.minimum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),_=n.$constructor("$ZodCheckLengthEquals",(e,t)=>{var n;c.init(e,t),null!=(n=e._zod.def).when||(n.when=e=>{let t=e.value;return!o.nullish(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{let n=e._zod.bag;n.minimum=t.length,n.maximum=t.length,n.length=t.length}),e._zod.check=n=>{let r=n.value,i=r.length;if(i===t.length)return;let a=o.getLengthableOrigin(r),c=i>t.length;n.issues.push({origin:a,...c?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:n.value,inst:e,continue:!t.abort})}}),b=n.$constructor("$ZodCheckStringFormat",(e,t)=>{var n,r;c.init(e,t),e._zod.onattach.push(e=>{let n=e._zod.bag;(n.format=t.format,t.pattern)&&(null!=n.patterns||(n.patterns=new Set),n.patterns.add(t.pattern))}),t.pattern?null!=(n=e._zod).check||(n.check=n=>{t.pattern.lastIndex=0,t.pattern.test(n.value)||n.issues.push({origin:"string",code:"invalid_format",format:t.format,input:n.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:e,continue:!t.abort})}):null!=(r=e._zod).check||(r.check=()=>{})}),k=n.$constructor("$ZodCheckRegex",(e,t)=>{b.init(e,t),e._zod.check=n=>{t.pattern.lastIndex=0,t.pattern.test(n.value)||n.issues.push({origin:"string",code:"invalid_format",format:"regex",input:n.value,pattern:t.pattern.toString(),inst:e,continue:!t.abort})}}),I=n.$constructor("$ZodCheckLowerCase",(e,t)=>{null!=t.pattern||(t.pattern=a.lowercase),b.init(e,t)}),z=n.$constructor("$ZodCheckUpperCase",(e,t)=>{null!=t.pattern||(t.pattern=a.uppercase),b.init(e,t)}),w=n.$constructor("$ZodCheckIncludes",(e,t)=>{c.init(e,t);let n=o.escapeRegex(t.includes),r=new RegExp("number"==typeof t.position?"^.{".concat(t.position,"}").concat(n):n);t.pattern=r,e._zod.onattach.push(e=>{let t=e._zod.bag;null!=t.patterns||(t.patterns=new Set),t.patterns.add(r)}),e._zod.check=n=>{n.value.includes(t.includes,t.position)||n.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:n.value,inst:e,continue:!t.abort})}}),S=n.$constructor("$ZodCheckStartsWith",(e,t)=>{c.init(e,t);let n=new RegExp("^".concat(o.escapeRegex(t.prefix),".*"));null!=t.pattern||(t.pattern=n),e._zod.onattach.push(e=>{let t=e._zod.bag;null!=t.patterns||(t.patterns=new Set),t.patterns.add(n)}),e._zod.check=n=>{n.value.startsWith(t.prefix)||n.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:n.value,inst:e,continue:!t.abort})}}),Z=n.$constructor("$ZodCheckEndsWith",(e,t)=>{c.init(e,t);let n=new RegExp(".*".concat(o.escapeRegex(t.suffix),"$"));null!=t.pattern||(t.pattern=n),e._zod.onattach.push(e=>{let t=e._zod.bag;null!=t.patterns||(t.patterns=new Set),t.patterns.add(n)}),e._zod.check=n=>{n.value.endsWith(t.suffix)||n.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:n.value,inst:e,continue:!t.abort})}});function x(e,t,n){e.issues.length&&t.issues.push(...o.prefixIssues(n,e.issues))}let j=n.$constructor("$ZodCheckProperty",(e,t)=>{c.init(e,t),e._zod.check=e=>{let n=t.schema._zod.run({value:e.value[t.property],issues:[]},{});if(n instanceof Promise)return n.then(n=>x(n,e,t.property));x(n,e,t.property)}}),$=n.$constructor("$ZodCheckMimeType",(e,t)=>{c.init(e,t);let n=new Set(t.mime);e._zod.onattach.push(e=>{e._zod.bag.mime=t.mime}),e._zod.check=r=>{n.has(r.value.type)||r.issues.push({code:"invalid_value",values:t.mime,input:r.value.type,inst:e,continue:!t.abort})}}),U=n.$constructor("$ZodCheckOverwrite",(e,t)=>{c.init(e,t),e._zod.check=e=>{e.value=t.tx(e.value)}});e.s(["Doc",()=>O],26143);class O{indented(e){this.indent+=1,e(this),this.indent-=1}write(e){if("function"==typeof e){e(this,{execution:"sync"}),e(this,{execution:"async"});return}let t=e.split("\n").filter(e=>e),n=Math.min(...t.map(e=>e.length-e.trimStart().length));for(let e of t.map(e=>e.slice(n)).map(e=>" ".repeat(2*this.indent)+e))this.content.push(e)}compile(){var e;return Function(...null===this||void 0===this?void 0:this.args,[...(null!=(e=null===this||void 0===this?void 0:this.content)?e:[""]).map(e=>"  ".concat(e))].join("\n"))}constructor(e=[]){this.content=[],this.indent=0,this&&(this.args=e)}}e.s(["version",()=>P],98442);let P={major:4,minor:1,patch:5},N=n.$constructor("$ZodType",(e,t)=>{var i,a,c;null!=e||(e={}),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=P;let u=[...null!=(a=e._zod.def.checks)?a:[]];for(let t of(e._zod.traits.has("$ZodCheck")&&u.unshift(e),u))for(let n of t._zod.onattach)n(e);if(0===u.length)null!=(i=e._zod).deferred||(i.deferred=[]),null==(c=e._zod.deferred)||c.push(()=>{e._zod.run=e._zod.parse});else{let t=(e,t,r)=>{let i,a=o.aborted(e);for(let c of t){if(c._zod.def.when){if(!c._zod.def.when(e))continue}else if(a)continue;let t=e.issues.length,u=c._zod.check(e);if(u instanceof Promise&&(null==r?void 0:r.async)===!1)throw new n.$ZodAsyncError;if(i||u instanceof Promise)i=(null!=i?i:Promise.resolve()).then(async()=>{await u,e.issues.length!==t&&(a||(a=o.aborted(e,t)))});else{if(e.issues.length===t)continue;a||(a=o.aborted(e,t))}}return i?i.then(()=>e):e},r=(r,i,a)=>{if(o.aborted(r))return r.aborted=!0,r;let c=t(i,u,a);if(c instanceof Promise){if(!1===a.async)throw new n.$ZodAsyncError;return c.then(t=>e._zod.parse(t,a))}return e._zod.parse(c,a)};e._zod.run=(i,a)=>{if(a.skipChecks)return e._zod.parse(i,a);if("backward"===a.direction){let t=e._zod.parse({value:i.value,issues:[]},{...a,skipChecks:!0});return t instanceof Promise?t.then(e=>r(e,i,a)):r(t,i,a)}let o=e._zod.parse(i,a);if(o instanceof Promise){if(!1===a.async)throw new n.$ZodAsyncError;return o.then(e=>t(e,u,a))}return t(o,u,a)}}e["~standard"]={validate:t=>{try{var n;let i=(0,r.safeParse)(e,t);return i.success?{value:i.data}:{issues:null==(n=i.error)?void 0:n.issues}}catch(n){return(0,r.safeParseAsync)(e,t).then(e=>{var t;return e.success?{value:e.data}:{issues:null==(t=e.error)?void 0:t.issues}})}},vendor:"zod",version:1}}),D=n.$constructor("$ZodString",(e,t)=>{var n,r,i;N.init(e,t),e._zod.pattern=null!=(i=[...null!=(r=null==e||null==(n=e._zod.bag)?void 0:n.patterns)?r:[]].pop())?i:a.string(e._zod.bag),e._zod.parse=(n,r)=>{if(t.coerce)try{n.value=String(n.value)}catch(e){}return"string"==typeof n.value||n.issues.push({expected:"string",code:"invalid_type",input:n.value,inst:e}),n}}),E=n.$constructor("$ZodStringFormat",(e,t)=>{b.init(e,t),D.init(e,t)}),A=n.$constructor("$ZodGUID",(e,t)=>{null!=t.pattern||(t.pattern=a.guid),E.init(e,t)}),T=n.$constructor("$ZodUUID",(e,t)=>{if(t.version){let e={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(void 0===e)throw Error('Invalid UUID version: "'.concat(t.version,'"'));null!=t.pattern||(t.pattern=a.uuid(e))}else null!=t.pattern||(t.pattern=a.uuid());E.init(e,t)}),L=n.$constructor("$ZodEmail",(e,t)=>{null!=t.pattern||(t.pattern=a.email),E.init(e,t)}),C=n.$constructor("$ZodURL",(e,t)=>{E.init(e,t),e._zod.check=n=>{try{let r=n.value.trim(),i=new URL(r);t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(i.hostname)||n.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:a.hostname.source,input:n.value,inst:e,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(i.protocol.endsWith(":")?i.protocol.slice(0,-1):i.protocol)||n.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:n.value,inst:e,continue:!t.abort})),t.normalize?n.value=i.href:n.value=r;return}catch(r){n.issues.push({code:"invalid_format",format:"url",input:n.value,inst:e,continue:!t.abort})}}}),R=n.$constructor("$ZodEmoji",(e,t)=>{null!=t.pattern||(t.pattern=a.emoji()),E.init(e,t)}),V=n.$constructor("$ZodNanoID",(e,t)=>{null!=t.pattern||(t.pattern=a.nanoid),E.init(e,t)}),F=n.$constructor("$ZodCUID",(e,t)=>{null!=t.pattern||(t.pattern=a.cuid),E.init(e,t)}),J=n.$constructor("$ZodCUID2",(e,t)=>{null!=t.pattern||(t.pattern=a.cuid2),E.init(e,t)}),M=n.$constructor("$ZodULID",(e,t)=>{null!=t.pattern||(t.pattern=a.ulid),E.init(e,t)}),W=n.$constructor("$ZodXID",(e,t)=>{null!=t.pattern||(t.pattern=a.xid),E.init(e,t)}),B=n.$constructor("$ZodKSUID",(e,t)=>{null!=t.pattern||(t.pattern=a.ksuid),E.init(e,t)}),K=n.$constructor("$ZodISODateTime",(e,t)=>{null!=t.pattern||(t.pattern=a.datetime(t)),E.init(e,t)}),G=n.$constructor("$ZodISODate",(e,t)=>{null!=t.pattern||(t.pattern=a.date),E.init(e,t)}),X=n.$constructor("$ZodISOTime",(e,t)=>{null!=t.pattern||(t.pattern=a.time(t)),E.init(e,t)}),q=n.$constructor("$ZodISODuration",(e,t)=>{null!=t.pattern||(t.pattern=a.duration),E.init(e,t)}),Y=n.$constructor("$ZodIPv4",(e,t)=>{null!=t.pattern||(t.pattern=a.ipv4),E.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv4"})}),H=n.$constructor("$ZodIPv6",(e,t)=>{null!=t.pattern||(t.pattern=a.ipv6),E.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv6"}),e._zod.check=n=>{try{new URL("http://[".concat(n.value,"]"))}catch(r){n.issues.push({code:"invalid_format",format:"ipv6",input:n.value,inst:e,continue:!t.abort})}}}),Q=n.$constructor("$ZodCIDRv4",(e,t)=>{null!=t.pattern||(t.pattern=a.cidrv4),E.init(e,t)}),ee=n.$constructor("$ZodCIDRv6",(e,t)=>{null!=t.pattern||(t.pattern=a.cidrv6),E.init(e,t),e._zod.check=n=>{let[r,i]=n.value.split("/");try{if(!i)throw Error();let e=Number(i);if("".concat(e)!==i||e<0||e>128)throw Error();new URL("http://[".concat(r,"]"))}catch(r){n.issues.push({code:"invalid_format",format:"cidrv6",input:n.value,inst:e,continue:!t.abort})}}});function et(e){if(""===e)return!0;if(e.length%4!=0)return!1;try{return atob(e),!0}catch(e){return!1}}let en=n.$constructor("$ZodBase64",(e,t)=>{null!=t.pattern||(t.pattern=a.base64),E.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64"}),e._zod.check=n=>{et(n.value)||n.issues.push({code:"invalid_format",format:"base64",input:n.value,inst:e,continue:!t.abort})}});function er(e){if(!a.base64url.test(e))return!1;let t=e.replace(/[-_]/g,e=>"-"===e?"+":"/");return et(t.padEnd(4*Math.ceil(t.length/4),"="))}let ei=n.$constructor("$ZodBase64URL",(e,t)=>{null!=t.pattern||(t.pattern=a.base64url),E.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64url"}),e._zod.check=n=>{er(n.value)||n.issues.push({code:"invalid_format",format:"base64url",input:n.value,inst:e,continue:!t.abort})}}),ea=n.$constructor("$ZodE164",(e,t)=>{null!=t.pattern||(t.pattern=a.e164),E.init(e,t)});function eo(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let n=e.split(".");if(3!==n.length)return!1;let[r]=n;if(!r)return!1;let i=JSON.parse(atob(r));if("typ"in i&&(null==i?void 0:i.typ)!=="JWT"||!i.alg||t&&(!("alg"in i)||i.alg!==t))return!1;return!0}catch(e){return!1}}let ec=n.$constructor("$ZodJWT",(e,t)=>{E.init(e,t),e._zod.check=n=>{eo(n.value,t.alg)||n.issues.push({code:"invalid_format",format:"jwt",input:n.value,inst:e,continue:!t.abort})}}),eu=n.$constructor("$ZodCustomStringFormat",(e,t)=>{E.init(e,t),e._zod.check=n=>{t.fn(n.value)||n.issues.push({code:"invalid_format",format:t.format,input:n.value,inst:e,continue:!t.abort})}}),el=n.$constructor("$ZodNumber",(e,t)=>{var n;N.init(e,t),e._zod.pattern=null!=(n=e._zod.bag.pattern)?n:a.number,e._zod.parse=(n,r)=>{if(t.coerce)try{n.value=Number(n.value)}catch(e){}let i=n.value;if("number"==typeof i&&!Number.isNaN(i)&&Number.isFinite(i))return n;let a="number"==typeof i?Number.isNaN(i)?"NaN":Number.isFinite(i)?void 0:"Infinity":void 0;return n.issues.push({expected:"number",code:"invalid_type",input:i,inst:e,...a?{received:a}:{}}),n}}),es=n.$constructor("$ZodNumber",(e,t)=>{m.init(e,t),el.init(e,t)}),ed=n.$constructor("$ZodBoolean",(e,t)=>{N.init(e,t),e._zod.pattern=a.boolean,e._zod.parse=(n,r)=>{if(t.coerce)try{n.value=!!n.value}catch(e){}let i=n.value;return"boolean"==typeof i||n.issues.push({expected:"boolean",code:"invalid_type",input:i,inst:e}),n}}),em=n.$constructor("$ZodBigInt",(e,t)=>{N.init(e,t),e._zod.pattern=a.bigint,e._zod.parse=(n,r)=>{if(t.coerce)try{n.value=BigInt(n.value)}catch(e){}return"bigint"==typeof n.value||n.issues.push({expected:"bigint",code:"invalid_type",input:n.value,inst:e}),n}}),ef=n.$constructor("$ZodBigInt",(e,t)=>{f.init(e,t),em.init(e,t)}),ev=n.$constructor("$ZodSymbol",(e,t)=>{N.init(e,t),e._zod.parse=(t,n)=>{let r=t.value;return"symbol"==typeof r||t.issues.push({expected:"symbol",code:"invalid_type",input:r,inst:e}),t}}),ep=n.$constructor("$ZodUndefined",(e,t)=>{N.init(e,t),e._zod.pattern=a.undefined,e._zod.values=new Set([void 0]),e._zod.optin="optional",e._zod.optout="optional",e._zod.parse=(t,n)=>{let r=t.value;return void 0===r||t.issues.push({expected:"undefined",code:"invalid_type",input:r,inst:e}),t}}),eg=n.$constructor("$ZodNull",(e,t)=>{N.init(e,t),e._zod.pattern=a.null,e._zod.values=new Set([null]),e._zod.parse=(t,n)=>{let r=t.value;return null===r||t.issues.push({expected:"null",code:"invalid_type",input:r,inst:e}),t}}),eh=n.$constructor("$ZodAny",(e,t)=>{N.init(e,t),e._zod.parse=e=>e}),ey=n.$constructor("$ZodUnknown",(e,t)=>{N.init(e,t),e._zod.parse=e=>e}),e_=n.$constructor("$ZodNever",(e,t)=>{N.init(e,t),e._zod.parse=(t,n)=>(t.issues.push({expected:"never",code:"invalid_type",input:t.value,inst:e}),t)}),eb=n.$constructor("$ZodVoid",(e,t)=>{N.init(e,t),e._zod.parse=(t,n)=>{let r=t.value;return void 0===r||t.issues.push({expected:"void",code:"invalid_type",input:r,inst:e}),t}}),ek=n.$constructor("$ZodDate",(e,t)=>{N.init(e,t),e._zod.parse=(n,r)=>{if(t.coerce)try{n.value=new Date(n.value)}catch(e){}let i=n.value,a=i instanceof Date;return a&&!Number.isNaN(i.getTime())||n.issues.push({expected:"date",code:"invalid_type",input:i,...a?{received:"Invalid Date"}:{},inst:e}),n}});function eI(e,t,n){e.issues.length&&t.issues.push(...o.prefixIssues(n,e.issues)),t.value[n]=e.value}let ez=n.$constructor("$ZodArray",(e,t)=>{N.init(e,t),e._zod.parse=(n,r)=>{let i=n.value;if(!Array.isArray(i))return n.issues.push({expected:"array",code:"invalid_type",input:i,inst:e}),n;n.value=Array(i.length);let a=[];for(let e=0;e<i.length;e++){let o=i[e],c=t.element._zod.run({value:o,issues:[]},r);c instanceof Promise?a.push(c.then(t=>eI(t,n,e))):eI(c,n,e)}return a.length?Promise.all(a).then(()=>n):n}});function ew(e,t,n,r){e.issues.length&&t.issues.push(...o.prefixIssues(n,e.issues)),void 0===e.value?n in r&&(t.value[n]=void 0):t.value[n]=e.value}function eS(e){let t=Object.keys(e.shape);for(let n of t)if(!e.shape[n]._zod.traits.has("$ZodType"))throw Error('Invalid element at key "'.concat(n,'": expected a Zod schema'));let n=o.optionalKeys(e.shape);return{...e,keys:t,keySet:new Set(t),numKeys:t.length,optionalKeys:new Set(n)}}function eZ(e,t,n,r,i,a){let o=[],c=i.keySet,u=i.catchall._zod,l=u.def.type;for(let i of Object.keys(t)){if(c.has(i))continue;if("never"===l){o.push(i);continue}let a=u.run({value:t[i],issues:[]},r);a instanceof Promise?e.push(a.then(e=>ew(e,n,i,t))):ew(a,n,i,t)}return(o.length&&n.issues.push({code:"unrecognized_keys",keys:o,input:t,inst:a}),e.length)?Promise.all(e).then(()=>n):n}let ex=n.$constructor("$ZodObject",(e,t)=>{let n;N.init(e,t);let r=o.cached(()=>eS(t));o.defineLazy(e._zod,"propValues",()=>{let e=t.shape,n={};for(let t in e){let r=e[t]._zod;if(r.values){for(let e of(null!=n[t]||(n[t]=new Set),r.values))n[t].add(e)}}return n});let i=o.isObject,a=t.catchall;e._zod.parse=(t,o)=>{null!=n||(n=r.value);let c=t.value;if(!i(c))return t.issues.push({expected:"object",code:"invalid_type",input:c,inst:e}),t;t.value={};let u=[],l=n.shape;for(let e of n.keys){let n=l[e]._zod.run({value:c[e],issues:[]},o);n instanceof Promise?u.push(n.then(n=>ew(n,t,e,c))):ew(n,t,e,c)}return a?eZ(u,c,t,o,r.value,e):u.length?Promise.all(u).then(()=>t):t}}),ej=n.$constructor("$ZodObjectJIT",(e,t)=>{let r,i;ex.init(e,t);let a=e._zod.parse,c=o.cached(()=>eS(t)),u=o.isObject,l=!n.globalConfig.jitless,s=o.allowsEval,d=l&&s.value,m=t.catchall;e._zod.parse=(n,s)=>{null!=i||(i=c.value);let f=n.value;return u(f)?l&&d&&(null==s?void 0:s.async)===!1&&!0!==s.jitless?(r||(r=(e=>{let t=new O(["shape","payload","ctx"]),n=c.value,r=e=>{let t=o.esc(e);return"shape[".concat(t,"]._zod.run({ value: input[").concat(t,"], issues: [] }, ctx)")};t.write("const input = payload.value;");let i=Object.create(null),a=0;for(let e of n.keys)i[e]="key_".concat(a++);for(let e of(t.write("const newResult = {}"),n.keys)){let n=i[e],a=o.esc(e);t.write("const ".concat(n," = ").concat(r(e),";")),t.write("\n        if (".concat(n,".issues.length) {\n          payload.issues = payload.issues.concat(").concat(n,".issues.map(iss => ({\n            ...iss,\n            path: iss.path ? [").concat(a,", ...iss.path] : [").concat(a,"]\n          })));\n        }\n        \n        if (").concat(n,".value === undefined) {\n          if (").concat(a," in input) {\n            newResult[").concat(a,"] = undefined;\n          }\n        } else {\n          newResult[").concat(a,"] = ").concat(n,".value;\n        }\n      "))}t.write("payload.value = newResult;"),t.write("return payload;");let u=t.compile();return(t,n)=>u(e,t,n)})(t.shape)),n=r(n,s),m)?eZ([],f,n,s,i,e):n:a(n,s):(n.issues.push({expected:"object",code:"invalid_type",input:f,inst:e}),n)}});function e$(e,t,r,i){for(let n of e)if(0===n.issues.length)return t.value=n.value,t;let a=e.filter(e=>!o.aborted(e));return 1===a.length?(t.value=a[0].value,a[0]):(t.issues.push({code:"invalid_union",input:t.value,inst:r,errors:e.map(e=>e.issues.map(e=>o.finalizeIssue(e,i,n.config())))}),t)}let eU=n.$constructor("$ZodUnion",(e,t)=>{N.init(e,t),o.defineLazy(e._zod,"optin",()=>t.options.some(e=>"optional"===e._zod.optin)?"optional":void 0),o.defineLazy(e._zod,"optout",()=>t.options.some(e=>"optional"===e._zod.optout)?"optional":void 0),o.defineLazy(e._zod,"values",()=>{if(t.options.every(e=>e._zod.values))return new Set(t.options.flatMap(e=>Array.from(e._zod.values)))}),o.defineLazy(e._zod,"pattern",()=>{if(t.options.every(e=>e._zod.pattern)){let e=t.options.map(e=>e._zod.pattern);return new RegExp("^(".concat(e.map(e=>o.cleanRegex(e.source)).join("|"),")$"))}});let n=1===t.options.length,r=t.options[0]._zod.run;e._zod.parse=(i,a)=>{if(n)return r(i,a);let o=!1,c=[];for(let e of t.options){let t=e._zod.run({value:i.value,issues:[]},a);if(t instanceof Promise)c.push(t),o=!0;else{if(0===t.issues.length)return t;c.push(t)}}return o?Promise.all(c).then(t=>e$(t,i,e,a)):e$(c,i,e,a)}}),eO=n.$constructor("$ZodDiscriminatedUnion",(e,t)=>{eU.init(e,t);let n=e._zod.parse;o.defineLazy(e._zod,"propValues",()=>{let e={};for(let n of t.options){let r=n._zod.propValues;if(!r||0===Object.keys(r).length)throw Error('Invalid discriminated union option at index "'.concat(t.options.indexOf(n),'"'));for(let[t,n]of Object.entries(r))for(let r of(e[t]||(e[t]=new Set),n))e[t].add(r)}return e});let r=o.cached(()=>{let e=t.options,n=new Map;for(let i of e){var r;let e=null==(r=i._zod.propValues)?void 0:r[t.discriminator];if(!e||0===e.size)throw Error('Invalid discriminated union option at index "'.concat(t.options.indexOf(i),'"'));for(let t of e){if(n.has(t))throw Error('Duplicate discriminator value "'.concat(String(t),'"'));n.set(t,i)}}return n});e._zod.parse=(i,a)=>{let c=i.value;if(!o.isObject(c))return i.issues.push({code:"invalid_type",expected:"object",input:c,inst:e}),i;let u=r.value.get(null==c?void 0:c[t.discriminator]);return u?u._zod.run(i,a):t.unionFallback?n(i,a):(i.issues.push({code:"invalid_union",errors:[],note:"No matching discriminator",discriminator:t.discriminator,input:c,path:[t.discriminator],inst:e}),i)}}),eP=n.$constructor("$ZodIntersection",(e,t)=>{N.init(e,t),e._zod.parse=(e,n)=>{let r=e.value,i=t.left._zod.run({value:r,issues:[]},n),a=t.right._zod.run({value:r,issues:[]},n);return i instanceof Promise||a instanceof Promise?Promise.all([i,a]).then(t=>{let[n,r]=t;return eN(e,n,r)}):eN(e,i,a)}});function eN(e,t,n){if(t.issues.length&&e.issues.push(...t.issues),n.issues.length&&e.issues.push(...n.issues),o.aborted(e))return e;let r=function e(t,n){if(t===n||t instanceof Date&&n instanceof Date&&+t==+n)return{valid:!0,data:t};if(o.isPlainObject(t)&&o.isPlainObject(n)){let r=Object.keys(n),i=Object.keys(t).filter(e=>-1!==r.indexOf(e)),a={...t,...n};for(let r of i){let i=e(t[r],n[r]);if(!i.valid)return{valid:!1,mergeErrorPath:[r,...i.mergeErrorPath]};a[r]=i.data}return{valid:!0,data:a}}if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return{valid:!1,mergeErrorPath:[]};let r=[];for(let i=0;i<t.length;i++){let a=e(t[i],n[i]);if(!a.valid)return{valid:!1,mergeErrorPath:[i,...a.mergeErrorPath]};r.push(a.data)}return{valid:!0,data:r}}return{valid:!1,mergeErrorPath:[]}}(t.value,n.value);if(!r.valid)throw Error("Unmergable intersection. Error path: "+"".concat(JSON.stringify(r.mergeErrorPath)));return e.value=r.data,e}let eD=n.$constructor("$ZodTuple",(e,t)=>{N.init(e,t);let n=t.items,r=n.length-[...n].reverse().findIndex(e=>"optional"!==e._zod.optin);e._zod.parse=(i,a)=>{let o=i.value;if(!Array.isArray(o))return i.issues.push({input:o,inst:e,expected:"tuple",code:"invalid_type"}),i;i.value=[];let c=[];if(!t.rest){let t=o.length>n.length,a=o.length<r-1;if(t||a)return i.issues.push({...t?{code:"too_big",maximum:n.length}:{code:"too_small",minimum:n.length},input:o,inst:e,origin:"array"}),i}let u=-1;for(let e of n){if(++u>=o.length&&u>=r)continue;let t=e._zod.run({value:o[u],issues:[]},a);t instanceof Promise?c.push(t.then(e=>eE(e,i,u))):eE(t,i,u)}if(t.rest)for(let e of o.slice(n.length)){u++;let n=t.rest._zod.run({value:e,issues:[]},a);n instanceof Promise?c.push(n.then(e=>eE(e,i,u))):eE(n,i,u)}return c.length?Promise.all(c).then(()=>i):i}});function eE(e,t,n){e.issues.length&&t.issues.push(...o.prefixIssues(n,e.issues)),t.value[n]=e.value}let eA=n.$constructor("$ZodRecord",(e,t)=>{N.init(e,t),e._zod.parse=(r,i)=>{let a=r.value;if(!o.isPlainObject(a))return r.issues.push({expected:"record",code:"invalid_type",input:a,inst:e}),r;let c=[];if(t.keyType._zod.values){let n,u=t.keyType._zod.values;for(let e of(r.value={},u))if("string"==typeof e||"number"==typeof e||"symbol"==typeof e){let n=t.valueType._zod.run({value:a[e],issues:[]},i);n instanceof Promise?c.push(n.then(t=>{t.issues.length&&r.issues.push(...o.prefixIssues(e,t.issues)),r.value[e]=t.value})):(n.issues.length&&r.issues.push(...o.prefixIssues(e,n.issues)),r.value[e]=n.value)}for(let e in a)u.has(e)||(n=null!=n?n:[]).push(e);n&&n.length>0&&r.issues.push({code:"unrecognized_keys",input:a,inst:e,keys:n})}else for(let u of(r.value={},Reflect.ownKeys(a))){if("__proto__"===u)continue;let l=t.keyType._zod.run({value:u,issues:[]},i);if(l instanceof Promise)throw Error("Async schemas not supported in object keys currently");if(l.issues.length){r.issues.push({code:"invalid_key",origin:"record",issues:l.issues.map(e=>o.finalizeIssue(e,i,n.config())),input:u,path:[u],inst:e}),r.value[l.value]=l.value;continue}let s=t.valueType._zod.run({value:a[u],issues:[]},i);s instanceof Promise?c.push(s.then(e=>{e.issues.length&&r.issues.push(...o.prefixIssues(u,e.issues)),r.value[l.value]=e.value})):(s.issues.length&&r.issues.push(...o.prefixIssues(u,s.issues)),r.value[l.value]=s.value)}return c.length?Promise.all(c).then(()=>r):r}}),eT=n.$constructor("$ZodMap",(e,t)=>{N.init(e,t),e._zod.parse=(n,r)=>{let i=n.value;if(!(i instanceof Map))return n.issues.push({expected:"map",code:"invalid_type",input:i,inst:e}),n;let a=[];for(let[o,c]of(n.value=new Map,i)){let u=t.keyType._zod.run({value:o,issues:[]},r),l=t.valueType._zod.run({value:c,issues:[]},r);u instanceof Promise||l instanceof Promise?a.push(Promise.all([u,l]).then(t=>{let[a,c]=t;eL(a,c,n,o,i,e,r)})):eL(u,l,n,o,i,e,r)}return a.length?Promise.all(a).then(()=>n):n}});function eL(e,t,r,i,a,c,u){e.issues.length&&(o.propertyKeyTypes.has(typeof i)?r.issues.push(...o.prefixIssues(i,e.issues)):r.issues.push({code:"invalid_key",origin:"map",input:a,inst:c,issues:e.issues.map(e=>o.finalizeIssue(e,u,n.config()))})),t.issues.length&&(o.propertyKeyTypes.has(typeof i)?r.issues.push(...o.prefixIssues(i,t.issues)):r.issues.push({origin:"map",code:"invalid_element",input:a,inst:c,key:i,issues:t.issues.map(e=>o.finalizeIssue(e,u,n.config()))})),r.value.set(e.value,t.value)}let eC=n.$constructor("$ZodSet",(e,t)=>{N.init(e,t),e._zod.parse=(n,r)=>{let i=n.value;if(!(i instanceof Set))return n.issues.push({input:i,inst:e,expected:"set",code:"invalid_type"}),n;let a=[];for(let e of(n.value=new Set,i)){let i=t.valueType._zod.run({value:e,issues:[]},r);i instanceof Promise?a.push(i.then(e=>eR(e,n))):eR(i,n)}return a.length?Promise.all(a).then(()=>n):n}});function eR(e,t){e.issues.length&&t.issues.push(...e.issues),t.value.add(e.value)}let eV=n.$constructor("$ZodEnum",(e,t)=>{N.init(e,t);let n=o.getEnumValues(t.entries),r=new Set(n);e._zod.values=r,e._zod.pattern=new RegExp("^(".concat(n.filter(e=>o.propertyKeyTypes.has(typeof e)).map(e=>"string"==typeof e?o.escapeRegex(e):e.toString()).join("|"),")$")),e._zod.parse=(t,i)=>{let a=t.value;return r.has(a)||t.issues.push({code:"invalid_value",values:n,input:a,inst:e}),t}}),eF=n.$constructor("$ZodLiteral",(e,t)=>{if(N.init(e,t),0===t.values.length)throw Error("Cannot create literal schema with no valid values");e._zod.values=new Set(t.values),e._zod.pattern=new RegExp("^(".concat(t.values.map(e=>"string"==typeof e?o.escapeRegex(e):e?o.escapeRegex(e.toString()):String(e)).join("|"),")$")),e._zod.parse=(n,r)=>{let i=n.value;return e._zod.values.has(i)||n.issues.push({code:"invalid_value",values:t.values,input:i,inst:e}),n}}),eJ=n.$constructor("$ZodFile",(e,t)=>{N.init(e,t),e._zod.parse=(t,n)=>{let r=t.value;return r instanceof File||t.issues.push({expected:"file",code:"invalid_type",input:r,inst:e}),t}}),eM=n.$constructor("$ZodTransform",(e,t)=>{N.init(e,t),e._zod.parse=(r,i)=>{if("backward"===i.direction)throw new n.$ZodEncodeError(e.constructor.name);let a=t.transform(r.value,r);if(i.async)return(a instanceof Promise?a:Promise.resolve(a)).then(e=>(r.value=e,r));if(a instanceof Promise)throw new n.$ZodAsyncError;return r.value=a,r}});function eW(e,t){return e.issues.length&&void 0===t?{issues:[],value:void 0}:e}let eB=n.$constructor("$ZodOptional",(e,t)=>{N.init(e,t),e._zod.optin="optional",e._zod.optout="optional",o.defineLazy(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),o.defineLazy(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?new RegExp("^(".concat(o.cleanRegex(e.source),")?$")):void 0}),e._zod.parse=(e,n)=>{if("optional"===t.innerType._zod.optin){let r=t.innerType._zod.run(e,n);return r instanceof Promise?r.then(t=>eW(t,e.value)):eW(r,e.value)}return void 0===e.value?e:t.innerType._zod.run(e,n)}}),eK=n.$constructor("$ZodNullable",(e,t)=>{N.init(e,t),o.defineLazy(e._zod,"optin",()=>t.innerType._zod.optin),o.defineLazy(e._zod,"optout",()=>t.innerType._zod.optout),o.defineLazy(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?new RegExp("^(".concat(o.cleanRegex(e.source),"|null)$")):void 0}),o.defineLazy(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),e._zod.parse=(e,n)=>null===e.value?e:t.innerType._zod.run(e,n)}),eG=n.$constructor("$ZodDefault",(e,t)=>{N.init(e,t),e._zod.optin="optional",o.defineLazy(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>{if("backward"===n.direction)return t.innerType._zod.run(e,n);if(void 0===e.value)return e.value=t.defaultValue,e;let r=t.innerType._zod.run(e,n);return r instanceof Promise?r.then(e=>eX(e,t)):eX(r,t)}});function eX(e,t){return void 0===e.value&&(e.value=t.defaultValue),e}let eq=n.$constructor("$ZodPrefault",(e,t)=>{N.init(e,t),e._zod.optin="optional",o.defineLazy(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,n)=>("backward"===n.direction||void 0===e.value&&(e.value=t.defaultValue),t.innerType._zod.run(e,n))}),eY=n.$constructor("$ZodNonOptional",(e,t)=>{N.init(e,t),o.defineLazy(e._zod,"values",()=>{let e=t.innerType._zod.values;return e?new Set([...e].filter(e=>void 0!==e)):void 0}),e._zod.parse=(n,r)=>{let i=t.innerType._zod.run(n,r);return i instanceof Promise?i.then(t=>eH(t,e)):eH(i,e)}});function eH(e,t){return e.issues.length||void 0!==e.value||e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:t}),e}let eQ=n.$constructor("$ZodSuccess",(e,t)=>{N.init(e,t),e._zod.parse=(e,r)=>{if("backward"===r.direction)throw new n.$ZodEncodeError("ZodSuccess");let i=t.innerType._zod.run(e,r);return i instanceof Promise?i.then(t=>(e.value=0===t.issues.length,e)):(e.value=0===i.issues.length,e)}}),e4=n.$constructor("$ZodCatch",(e,t)=>{N.init(e,t),o.defineLazy(e._zod,"optin",()=>t.innerType._zod.optin),o.defineLazy(e._zod,"optout",()=>t.innerType._zod.optout),o.defineLazy(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,r)=>{if("backward"===r.direction)return t.innerType._zod.run(e,r);let i=t.innerType._zod.run(e,r);return i instanceof Promise?i.then(i=>(e.value=i.value,i.issues.length&&(e.value=t.catchValue({...e,error:{issues:i.issues.map(e=>o.finalizeIssue(e,r,n.config()))},input:e.value}),e.issues=[]),e)):(e.value=i.value,i.issues.length&&(e.value=t.catchValue({...e,error:{issues:i.issues.map(e=>o.finalizeIssue(e,r,n.config()))},input:e.value}),e.issues=[]),e)}}),e6=n.$constructor("$ZodNaN",(e,t)=>{N.init(e,t),e._zod.parse=(t,n)=>("number"==typeof t.value&&Number.isNaN(t.value)||t.issues.push({input:t.value,inst:e,expected:"nan",code:"invalid_type"}),t)}),e0=n.$constructor("$ZodPipe",(e,t)=>{N.init(e,t),o.defineLazy(e._zod,"values",()=>t.in._zod.values),o.defineLazy(e._zod,"optin",()=>t.in._zod.optin),o.defineLazy(e._zod,"optout",()=>t.out._zod.optout),o.defineLazy(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(e,n)=>{if("backward"===n.direction){let r=t.out._zod.run(e,n);return r instanceof Promise?r.then(e=>e1(e,t.in,n)):e1(r,t.in,n)}let r=t.in._zod.run(e,n);return r instanceof Promise?r.then(e=>e1(e,t.out,n)):e1(r,t.out,n)}});function e1(e,t,n){return e.issues.length?(e.aborted=!0,e):t._zod.run({value:e.value,issues:e.issues},n)}let e2=n.$constructor("$ZodCodec",(e,t)=>{N.init(e,t),o.defineLazy(e._zod,"values",()=>t.in._zod.values),o.defineLazy(e._zod,"optin",()=>t.in._zod.optin),o.defineLazy(e._zod,"optout",()=>t.out._zod.optout),o.defineLazy(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(e,n)=>{if("forward"===(n.direction||"forward")){let r=t.in._zod.run(e,n);return r instanceof Promise?r.then(e=>e9(e,t,n)):e9(r,t,n)}{let r=t.out._zod.run(e,n);return r instanceof Promise?r.then(e=>e9(e,t,n)):e9(r,t,n)}}});function e9(e,t,n){if(e.issues.length)return e.aborted=!0,e;if("forward"===(n.direction||"forward")){let r=t.transform(e.value,e);return r instanceof Promise?r.then(r=>e3(e,r,t.out,n)):e3(e,r,t.out,n)}{let r=t.reverseTransform(e.value,e);return r instanceof Promise?r.then(r=>e3(e,r,t.in,n)):e3(e,r,t.in,n)}}function e3(e,t,n,r){return e.issues.length?(e.aborted=!0,e):n._zod.run({value:t,issues:e.issues},r)}let e8=n.$constructor("$ZodReadonly",(e,t)=>{N.init(e,t),o.defineLazy(e._zod,"propValues",()=>t.innerType._zod.propValues),o.defineLazy(e._zod,"values",()=>t.innerType._zod.values),o.defineLazy(e._zod,"optin",()=>t.innerType._zod.optin),o.defineLazy(e._zod,"optout",()=>t.innerType._zod.optout),e._zod.parse=(e,n)=>{if("backward"===n.direction)return t.innerType._zod.run(e,n);let r=t.innerType._zod.run(e,n);return r instanceof Promise?r.then(e7):e7(r)}});function e7(e){return e.value=Object.freeze(e.value),e}let e5=n.$constructor("$ZodTemplateLiteral",(e,t)=>{N.init(e,t);let n=[];for(let e of t.parts)if("object"==typeof e&&null!==e){if(!e._zod.pattern)throw Error("Invalid template literal part, no pattern found: ".concat([...e._zod.traits].shift()));let t=e._zod.pattern instanceof RegExp?e._zod.pattern.source:e._zod.pattern;if(!t)throw Error("Invalid template literal part: ".concat(e._zod.traits));let r=+!!t.startsWith("^"),i=t.endsWith("$")?t.length-1:t.length;n.push(t.slice(r,i))}else if(null===e||o.primitiveTypes.has(typeof e))n.push(o.escapeRegex("".concat(e)));else throw Error("Invalid template literal part: ".concat(e));e._zod.pattern=new RegExp("^".concat(n.join(""),"$")),e._zod.parse=(n,r)=>{if("string"!=typeof n.value)return n.issues.push({input:n.value,inst:e,expected:"template_literal",code:"invalid_type"}),n;if(e._zod.pattern.lastIndex=0,!e._zod.pattern.test(n.value)){var i;n.issues.push({input:n.value,inst:e,code:"invalid_format",format:null!=(i=t.format)?i:"template_literal",pattern:e._zod.pattern.source})}return n}}),te=n.$constructor("$ZodFunction",(e,t)=>(N.init(e,t),e._def=t,e._zod.def=t,e.implement=t=>{if("function"!=typeof t)throw Error("implement() must be called with a function");return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];let o=Reflect.apply(t,this,e._def.input?(0,r.parse)(e._def.input,i):i);return e._def.output?(0,r.parse)(e._def.output,o):o}},e.implementAsync=t=>{if("function"!=typeof t)throw Error("implementAsync() must be called with a function");return async function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];let o=e._def.input?await (0,r.parseAsync)(e._def.input,i):i,c=await Reflect.apply(t,this,o);return e._def.output?await (0,r.parseAsync)(e._def.output,c):c}},e._zod.parse=(t,n)=>("function"!=typeof t.value?t.issues.push({code:"invalid_type",expected:"function",input:t.value,inst:e}):e._def.output&&"promise"===e._def.output._zod.def.type?t.value=e.implementAsync(t.value):t.value=e.implement(t.value),t),e.input=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let i=e.constructor;return new i(Array.isArray(n[0])?{type:"function",input:new eD({type:"tuple",items:n[0],rest:n[1]}),output:e._def.output}:{type:"function",input:n[0],output:e._def.output})},e.output=t=>new e.constructor({type:"function",input:e._def.input,output:t}),e)),tt=n.$constructor("$ZodPromise",(e,t)=>{N.init(e,t),e._zod.parse=(e,n)=>Promise.resolve(e.value).then(e=>t.innerType._zod.run({value:e,issues:[]},n))}),tn=n.$constructor("$ZodLazy",(e,t)=>{N.init(e,t),o.defineLazy(e._zod,"innerType",()=>t.getter()),o.defineLazy(e._zod,"pattern",()=>e._zod.innerType._zod.pattern),o.defineLazy(e._zod,"propValues",()=>e._zod.innerType._zod.propValues),o.defineLazy(e._zod,"optin",()=>{var t;return null!=(t=e._zod.innerType._zod.optin)?t:void 0}),o.defineLazy(e._zod,"optout",()=>{var t;return null!=(t=e._zod.innerType._zod.optout)?t:void 0}),e._zod.parse=(t,n)=>e._zod.innerType._zod.run(t,n)}),tr=n.$constructor("$ZodCustom",(e,t)=>{c.init(e,t),N.init(e,t),e._zod.parse=(e,t)=>e,e._zod.check=n=>{let r=n.value,i=t.fn(r);if(i instanceof Promise)return i.then(t=>ti(t,n,r,e));ti(i,n,r,e)}});function ti(e,t,n,r){if(!e){var i;let e={code:"custom",input:n,inst:r,path:[...null!=(i=r._zod.def.path)?i:[]],continue:!r._zod.def.abort};r._zod.def.params&&(e.params=r._zod.def.params),t.issues.push(o.issue(e))}}e.i(52346);var ta=o;e.i(82480),e.i(86557),e.i(98442);var to=e.i(27874),tc=a,tu=e.i(49131);e.s(["$ZodRegistry",()=>td,"$input",()=>ts,"$output",()=>tl,"globalRegistry",()=>tf,"registry",()=>tm],19930);let tl=Symbol("ZodOutput"),ts=Symbol("ZodInput");class td{add(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];let i=n[0];if(this._map.set(e,i),i&&"object"==typeof i&&"id"in i){if(this._idmap.has(i.id))throw Error("ID ".concat(i.id," already exists in the registry"));this._idmap.set(i.id,e)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(e){let t=this._map.get(e);return t&&"object"==typeof t&&"id"in t&&this._idmap.delete(t.id),this._map.delete(e),this}get(e){let t=e._zod.parent;if(t){var n;let r={...null!=(n=this.get(t))?n:{}};delete r.id;let i={...r,...this._map.get(e)};return Object.keys(i).length?i:void 0}return this._map.get(e)}has(e){return this._map.has(e)}constructor(){this._map=new Map,this._idmap=new Map}}function tm(){return new td}let tf=tm();function tv(e,t){return new e({type:"string",...o.normalizeParams(t)})}function tp(e,t){return new e({type:"string",coerce:!0,...o.normalizeParams(t)})}function tg(e,t){return new e({type:"string",format:"email",check:"string_format",abort:!1,...o.normalizeParams(t)})}function th(e,t){return new e({type:"string",format:"guid",check:"string_format",abort:!1,...o.normalizeParams(t)})}function ty(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,...o.normalizeParams(t)})}function t_(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...o.normalizeParams(t)})}function tb(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...o.normalizeParams(t)})}function tk(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...o.normalizeParams(t)})}function tI(e,t){return new e({type:"string",format:"url",check:"string_format",abort:!1,...o.normalizeParams(t)})}function tz(e,t){return new e({type:"string",format:"emoji",check:"string_format",abort:!1,...o.normalizeParams(t)})}function tw(e,t){return new e({type:"string",format:"nanoid",check:"string_format",abort:!1,...o.normalizeParams(t)})}function tS(e,t){return new e({type:"string",format:"cuid",check:"string_format",abort:!1,...o.normalizeParams(t)})}function tZ(e,t){return new e({type:"string",format:"cuid2",check:"string_format",abort:!1,...o.normalizeParams(t)})}function tx(e,t){return new e({type:"string",format:"ulid",check:"string_format",abort:!1,...o.normalizeParams(t)})}function tj(e,t){return new e({type:"string",format:"xid",check:"string_format",abort:!1,...o.normalizeParams(t)})}function t$(e,t){return new e({type:"string",format:"ksuid",check:"string_format",abort:!1,...o.normalizeParams(t)})}function tU(e,t){return new e({type:"string",format:"ipv4",check:"string_format",abort:!1,...o.normalizeParams(t)})}function tO(e,t){return new e({type:"string",format:"ipv6",check:"string_format",abort:!1,...o.normalizeParams(t)})}function tP(e,t){return new e({type:"string",format:"cidrv4",check:"string_format",abort:!1,...o.normalizeParams(t)})}function tN(e,t){return new e({type:"string",format:"cidrv6",check:"string_format",abort:!1,...o.normalizeParams(t)})}function tD(e,t){return new e({type:"string",format:"base64",check:"string_format",abort:!1,...o.normalizeParams(t)})}function tE(e,t){return new e({type:"string",format:"base64url",check:"string_format",abort:!1,...o.normalizeParams(t)})}function tA(e,t){return new e({type:"string",format:"e164",check:"string_format",abort:!1,...o.normalizeParams(t)})}function tT(e,t){return new e({type:"string",format:"jwt",check:"string_format",abort:!1,...o.normalizeParams(t)})}e.i(19930),e.i(26143),e.s(["TimePrecision",()=>tL,"_any",()=>t9,"_array",()=>nU,"_base64",()=>tD,"_base64url",()=>tE,"_bigint",()=>tH,"_boolean",()=>tq,"_catch",()=>nG,"_check",()=>n1,"_cidrv4",()=>tP,"_cidrv6",()=>tN,"_coercedBigint",()=>tQ,"_coercedBoolean",()=>tY,"_coercedDate",()=>ne,"_coercedNumber",()=>tM,"_coercedString",()=>tp,"_cuid",()=>tS,"_cuid2",()=>tZ,"_custom",()=>n4,"_date",()=>t5,"_default",()=>nW,"_discriminatedUnion",()=>nP,"_e164",()=>tA,"_email",()=>tg,"_emoji",()=>tz,"_endsWith",()=>nI,"_enum",()=>nL,"_file",()=>nV,"_float32",()=>tB,"_float64",()=>tK,"_gt",()=>ni,"_gte",()=>na,"_guid",()=>th,"_includes",()=>nb,"_int",()=>tW,"_int32",()=>tG,"_int64",()=>t4,"_intersection",()=>nN,"_ipv4",()=>tU,"_ipv6",()=>tO,"_isoDate",()=>tR,"_isoDateTime",()=>tC,"_isoDuration",()=>tF,"_isoTime",()=>tV,"_jwt",()=>tT,"_ksuid",()=>t$,"_lazy",()=>nH,"_length",()=>ng,"_literal",()=>nR,"_lowercase",()=>ny,"_lt",()=>nn,"_lte",()=>nr,"_map",()=>nA,"_max",()=>nr,"_maxLength",()=>nv,"_maxSize",()=>nd,"_mime",()=>nw,"_min",()=>na,"_minLength",()=>np,"_minSize",()=>nm,"_multipleOf",()=>ns,"_nan",()=>nt,"_nanoid",()=>tw,"_nativeEnum",()=>nC,"_negative",()=>nc,"_never",()=>t8,"_nonnegative",()=>nl,"_nonoptional",()=>nB,"_nonpositive",()=>nu,"_normalize",()=>nZ,"_null",()=>t2,"_nullable",()=>nM,"_number",()=>tJ,"_optional",()=>nJ,"_overwrite",()=>nS,"_pipe",()=>nX,"_positive",()=>no,"_promise",()=>nQ,"_property",()=>nz,"_readonly",()=>nq,"_record",()=>nE,"_refine",()=>n6,"_regex",()=>nh,"_set",()=>nT,"_size",()=>nf,"_startsWith",()=>nk,"_string",()=>tv,"_stringFormat",()=>n9,"_stringbool",()=>n2,"_success",()=>nK,"_superRefine",()=>n0,"_symbol",()=>t0,"_templateLiteral",()=>nY,"_toLowerCase",()=>nj,"_toUpperCase",()=>n$,"_transform",()=>nF,"_trim",()=>nx,"_tuple",()=>nD,"_uint32",()=>tX,"_uint64",()=>t6,"_ulid",()=>tx,"_undefined",()=>t1,"_union",()=>nO,"_unknown",()=>t3,"_uppercase",()=>n_,"_url",()=>tI,"_uuid",()=>ty,"_uuidv4",()=>t_,"_uuidv6",()=>tb,"_uuidv7",()=>tk,"_void",()=>t7,"_xid",()=>tj],71338);let tL={Any:null,Minute:-1,Second:0,Millisecond:3,Microsecond:6};function tC(e,t){return new e({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...o.normalizeParams(t)})}function tR(e,t){return new e({type:"string",format:"date",check:"string_format",...o.normalizeParams(t)})}function tV(e,t){return new e({type:"string",format:"time",check:"string_format",precision:null,...o.normalizeParams(t)})}function tF(e,t){return new e({type:"string",format:"duration",check:"string_format",...o.normalizeParams(t)})}function tJ(e,t){return new e({type:"number",checks:[],...o.normalizeParams(t)})}function tM(e,t){return new e({type:"number",coerce:!0,checks:[],...o.normalizeParams(t)})}function tW(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"safeint",...o.normalizeParams(t)})}function tB(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"float32",...o.normalizeParams(t)})}function tK(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"float64",...o.normalizeParams(t)})}function tG(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"int32",...o.normalizeParams(t)})}function tX(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"uint32",...o.normalizeParams(t)})}function tq(e,t){return new e({type:"boolean",...o.normalizeParams(t)})}function tY(e,t){return new e({type:"boolean",coerce:!0,...o.normalizeParams(t)})}function tH(e,t){return new e({type:"bigint",...o.normalizeParams(t)})}function tQ(e,t){return new e({type:"bigint",coerce:!0,...o.normalizeParams(t)})}function t4(e,t){return new e({type:"bigint",check:"bigint_format",abort:!1,format:"int64",...o.normalizeParams(t)})}function t6(e,t){return new e({type:"bigint",check:"bigint_format",abort:!1,format:"uint64",...o.normalizeParams(t)})}function t0(e,t){return new e({type:"symbol",...o.normalizeParams(t)})}function t1(e,t){return new e({type:"undefined",...o.normalizeParams(t)})}function t2(e,t){return new e({type:"null",...o.normalizeParams(t)})}function t9(e){return new e({type:"any"})}function t3(e){return new e({type:"unknown"})}function t8(e,t){return new e({type:"never",...o.normalizeParams(t)})}function t7(e,t){return new e({type:"void",...o.normalizeParams(t)})}function t5(e,t){return new e({type:"date",...o.normalizeParams(t)})}function ne(e,t){return new e({type:"date",coerce:!0,...o.normalizeParams(t)})}function nt(e,t){return new e({type:"nan",...o.normalizeParams(t)})}function nn(e,t){return new l({check:"less_than",...o.normalizeParams(t),value:e,inclusive:!1})}function nr(e,t){return new l({check:"less_than",...o.normalizeParams(t),value:e,inclusive:!0})}function ni(e,t){return new s({check:"greater_than",...o.normalizeParams(t),value:e,inclusive:!1})}function na(e,t){return new s({check:"greater_than",...o.normalizeParams(t),value:e,inclusive:!0})}function no(e){return ni(0,e)}function nc(e){return nn(0,e)}function nu(e){return nr(0,e)}function nl(e){return na(0,e)}function ns(e,t){return new d({check:"multiple_of",...o.normalizeParams(t),value:e})}function nd(e,t){return new v({check:"max_size",...o.normalizeParams(t),maximum:e})}function nm(e,t){return new p({check:"min_size",...o.normalizeParams(t),minimum:e})}function nf(e,t){return new g({check:"size_equals",...o.normalizeParams(t),size:e})}function nv(e,t){return new h({check:"max_length",...o.normalizeParams(t),maximum:e})}function np(e,t){return new y({check:"min_length",...o.normalizeParams(t),minimum:e})}function ng(e,t){return new _({check:"length_equals",...o.normalizeParams(t),length:e})}function nh(e,t){return new k({check:"string_format",format:"regex",...o.normalizeParams(t),pattern:e})}function ny(e){return new I({check:"string_format",format:"lowercase",...o.normalizeParams(e)})}function n_(e){return new z({check:"string_format",format:"uppercase",...o.normalizeParams(e)})}function nb(e,t){return new w({check:"string_format",format:"includes",...o.normalizeParams(t),includes:e})}function nk(e,t){return new S({check:"string_format",format:"starts_with",...o.normalizeParams(t),prefix:e})}function nI(e,t){return new Z({check:"string_format",format:"ends_with",...o.normalizeParams(t),suffix:e})}function nz(e,t,n){return new j({check:"property",property:e,schema:t,...o.normalizeParams(n)})}function nw(e,t){return new $({check:"mime_type",mime:e,...o.normalizeParams(t)})}function nS(e){return new U({check:"overwrite",tx:e})}function nZ(e){return nS(t=>t.normalize(e))}function nx(){return nS(e=>e.trim())}function nj(){return nS(e=>e.toLowerCase())}function n$(){return nS(e=>e.toUpperCase())}function nU(e,t,n){return new e({type:"array",element:t,...o.normalizeParams(n)})}function nO(e,t,n){return new e({type:"union",options:t,...o.normalizeParams(n)})}function nP(e,t,n,r){return new e({type:"union",options:n,discriminator:t,...o.normalizeParams(r)})}function nN(e,t,n){return new e({type:"intersection",left:t,right:n})}function nD(e,t,n,r){let i=n instanceof N,a=i?r:n;return new e({type:"tuple",items:t,rest:i?n:null,...o.normalizeParams(a)})}function nE(e,t,n,r){return new e({type:"record",keyType:t,valueType:n,...o.normalizeParams(r)})}function nA(e,t,n,r){return new e({type:"map",keyType:t,valueType:n,...o.normalizeParams(r)})}function nT(e,t,n){return new e({type:"set",valueType:t,...o.normalizeParams(n)})}function nL(e,t,n){return new e({type:"enum",entries:Array.isArray(t)?Object.fromEntries(t.map(e=>[e,e])):t,...o.normalizeParams(n)})}function nC(e,t,n){return new e({type:"enum",entries:t,...o.normalizeParams(n)})}function nR(e,t,n){return new e({type:"literal",values:Array.isArray(t)?t:[t],...o.normalizeParams(n)})}function nV(e,t){return new e({type:"file",...o.normalizeParams(t)})}function nF(e,t){return new e({type:"transform",transform:t})}function nJ(e,t){return new e({type:"optional",innerType:t})}function nM(e,t){return new e({type:"nullable",innerType:t})}function nW(e,t,n){return new e({type:"default",innerType:t,get defaultValue(){return"function"==typeof n?n():o.shallowClone(n)}})}function nB(e,t,n){return new e({type:"nonoptional",innerType:t,...o.normalizeParams(n)})}function nK(e,t){return new e({type:"success",innerType:t})}function nG(e,t,n){return new e({type:"catch",innerType:t,catchValue:"function"==typeof n?n:()=>n})}function nX(e,t,n){return new e({type:"pipe",in:t,out:n})}function nq(e,t){return new e({type:"readonly",innerType:t})}function nY(e,t,n){return new e({type:"template_literal",parts:t,...o.normalizeParams(n)})}function nH(e,t){return new e({type:"lazy",getter:t})}function nQ(e,t){return new e({type:"promise",innerType:t})}function n4(e,t,n){let r=o.normalizeParams(n);return null!=r.abort||(r.abort=!0),new e({type:"custom",check:"custom",fn:t,...r})}function n6(e,t,n){return new e({type:"custom",check:"custom",fn:t,...o.normalizeParams(n)})}function n0(e){let t=n1(n=>(n.addIssue=e=>{if("string"==typeof e)n.issues.push(o.issue(e,n.value,t._zod.def));else e.fatal&&(e.continue=!1),null!=e.code||(e.code="custom"),null!=e.input||(e.input=n.value),null!=e.inst||(e.inst=t),null!=e.continue||(e.continue=!t._zod.def.abort),n.issues.push(o.issue(e))},e(n.value,n)));return t}function n1(e,t){let n=new c({check:"custom",...o.normalizeParams(t)});return n._zod.check=e,n}function n2(e,t){var n,r,i,a,c;let u=o.normalizeParams(t),l=null!=(n=u.truthy)?n:["true","1","yes","on","y","enabled"],s=null!=(r=u.falsy)?r:["false","0","no","off","n","disabled"];"sensitive"!==u.case&&(l=l.map(e=>"string"==typeof e?e.toLowerCase():e),s=s.map(e=>"string"==typeof e?e.toLowerCase():e));let d=new Set(l),m=new Set(s),f=null!=(i=e.Codec)?i:e2,v=null!=(a=e.Boolean)?a:ed,p=new f({type:"pipe",in:new(null!=(c=e.String)?c:D)({type:"string",error:u.error}),out:new v({type:"boolean",error:u.error}),transform:(e,t)=>{let n=e;return"sensitive"!==u.case&&(n=n.toLowerCase()),!!d.has(n)||!m.has(n)&&(t.issues.push({code:"invalid_value",expected:"stringbool",values:[...d,...m],input:t.value,inst:p,continue:!1}),{})},reverseTransform:(e,t)=>!0===e?l[0]||"true":s[0]||"false",error:u.error});return p}function n9(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=o.normalizeParams(r),a={...o.normalizeParams(r),check:"string_format",type:"string",format:t,fn:"function"==typeof n?n:e=>n.test(e),...i};return n instanceof RegExp&&(a.pattern=n),new e(a)}e.i(71338),e.s(["JSONSchemaGenerator",()=>n3,"toJSONSchema",()=>n8],71071);class n3{process(e){var t,n,r,i,a;let c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{path:[],schemaPath:[]},u=e._zod.def,l=this.seen.get(e);if(l)return l.count++,c.schemaPath.includes(e)&&(l.cycle=c.path),l.schema;let s={schema:{},count:1,cycle:void 0,path:c.path};this.seen.set(e,s);let d=null==(t=(n=e._zod).toJSONSchema)?void 0:t.call(n);if(d)s.schema=d;else{let t={...c,schemaPath:[...c.schemaPath,e],path:c.path},n=e._zod.parent;if(n)s.ref=n,this.process(n,t),this.seen.get(n).isParent=!0;else{let n=s.schema;switch(u.type){case"string":{n.type="string";let{minimum:t,maximum:r,format:a,patterns:o,contentEncoding:c}=e._zod.bag;if("number"==typeof t&&(n.minLength=t),"number"==typeof r&&(n.maxLength=r),a&&(n.format=null!=(i=({guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""})[a])?i:a,""===n.format&&delete n.format),c&&(n.contentEncoding=c),o&&o.size>0){let e=[...o];1===e.length?n.pattern=e[0].source:e.length>1&&(s.schema.allOf=[...e.map(e=>({..."draft-7"===this.target||"draft-4"===this.target||"openapi-3.0"===this.target?{type:"string"}:{},pattern:e.source}))])}break}case"number":{let{minimum:t,maximum:r,format:i,multipleOf:a,exclusiveMaximum:o,exclusiveMinimum:c}=e._zod.bag;"string"==typeof i&&i.includes("int")?n.type="integer":n.type="number","number"==typeof c&&("draft-4"===this.target||"openapi-3.0"===this.target?(n.minimum=c,n.exclusiveMinimum=!0):n.exclusiveMinimum=c),"number"==typeof t&&(n.minimum=t,"number"==typeof c&&"draft-4"!==this.target&&(c>=t?delete n.minimum:delete n.exclusiveMinimum)),"number"==typeof o&&("draft-4"===this.target||"openapi-3.0"===this.target?(n.maximum=o,n.exclusiveMaximum=!0):n.exclusiveMaximum=o),"number"==typeof r&&(n.maximum=r,"number"==typeof o&&"draft-4"!==this.target&&(o<=r?delete n.maximum:delete n.exclusiveMaximum)),"number"==typeof a&&(n.multipleOf=a);break}case"boolean":case"success":n.type="boolean";break;case"bigint":if("throw"===this.unrepresentable)throw Error("BigInt cannot be represented in JSON Schema");break;case"symbol":if("throw"===this.unrepresentable)throw Error("Symbols cannot be represented in JSON Schema");break;case"null":"openapi-3.0"===this.target?(n.type="string",n.nullable=!0,n.enum=[null]):n.type="null";break;case"any":case"unknown":break;case"undefined":if("throw"===this.unrepresentable)throw Error("Undefined cannot be represented in JSON Schema");break;case"void":if("throw"===this.unrepresentable)throw Error("Void cannot be represented in JSON Schema");break;case"never":n.not={};break;case"date":if("throw"===this.unrepresentable)throw Error("Date cannot be represented in JSON Schema");break;case"array":{let{minimum:r,maximum:i}=e._zod.bag;"number"==typeof r&&(n.minItems=r),"number"==typeof i&&(n.maxItems=i),n.type="array",n.items=this.process(u.element,{...t,path:[...t.path,"items"]});break}case"object":{n.type="object",n.properties={};let e=u.shape;for(let r in e)n.properties[r]=this.process(e[r],{...t,path:[...t.path,"properties",r]});let r=new Set([...new Set(Object.keys(e))].filter(e=>{let t=u.shape[e]._zod;return"input"===this.io?void 0===t.optin:void 0===t.optout}));r.size>0&&(n.required=Array.from(r)),(null==(a=u.catchall)?void 0:a._zod.def.type)==="never"?n.additionalProperties=!1:u.catchall?u.catchall&&(n.additionalProperties=this.process(u.catchall,{...t,path:[...t.path,"additionalProperties"]})):"output"===this.io&&(n.additionalProperties=!1);break}case"union":n.anyOf=u.options.map((e,n)=>this.process(e,{...t,path:[...t.path,"anyOf",n]}));break;case"intersection":{let e=this.process(u.left,{...t,path:[...t.path,"allOf",0]}),r=this.process(u.right,{...t,path:[...t.path,"allOf",1]}),i=e=>"allOf"in e&&1===Object.keys(e).length;n.allOf=[...i(e)?e.allOf:[e],...i(r)?r.allOf:[r]];break}case"tuple":{n.type="array";let r="draft-2020-12"===this.target?"prefixItems":"items",i="draft-2020-12"===this.target||"openapi-3.0"===this.target?"items":"additionalItems",a=u.items.map((e,n)=>this.process(e,{...t,path:[...t.path,r,n]})),o=u.rest?this.process(u.rest,{...t,path:[...t.path,i,..."openapi-3.0"===this.target?[u.items.length]:[]]}):null;"draft-2020-12"===this.target?(n.prefixItems=a,o&&(n.items=o)):"openapi-3.0"===this.target?(n.items={anyOf:a},o&&n.items.anyOf.push(o),n.minItems=a.length,o||(n.maxItems=a.length)):(n.items=a,o&&(n.additionalItems=o));let{minimum:c,maximum:l}=e._zod.bag;"number"==typeof c&&(n.minItems=c),"number"==typeof l&&(n.maxItems=l);break}case"record":n.type="object",("draft-7"===this.target||"draft-2020-12"===this.target)&&(n.propertyNames=this.process(u.keyType,{...t,path:[...t.path,"propertyNames"]})),n.additionalProperties=this.process(u.valueType,{...t,path:[...t.path,"additionalProperties"]});break;case"map":if("throw"===this.unrepresentable)throw Error("Map cannot be represented in JSON Schema");break;case"set":if("throw"===this.unrepresentable)throw Error("Set cannot be represented in JSON Schema");break;case"enum":{let e=(0,o.getEnumValues)(u.entries);e.every(e=>"number"==typeof e)&&(n.type="number"),e.every(e=>"string"==typeof e)&&(n.type="string"),n.enum=e;break}case"literal":{let e=[];for(let t of u.values)if(void 0===t){if("throw"===this.unrepresentable)throw Error("Literal `undefined` cannot be represented in JSON Schema")}else if("bigint"==typeof t)if("throw"===this.unrepresentable)throw Error("BigInt literals cannot be represented in JSON Schema");else e.push(Number(t));else e.push(t);if(0===e.length);else if(1===e.length){let t=e[0];n.type=null===t?"null":typeof t,"draft-4"===this.target||"openapi-3.0"===this.target?n.enum=[t]:n.const=t}else e.every(e=>"number"==typeof e)&&(n.type="number"),e.every(e=>"string"==typeof e)&&(n.type="string"),e.every(e=>"boolean"==typeof e)&&(n.type="string"),e.every(e=>null===e)&&(n.type="null"),n.enum=e;break}case"file":{let t={type:"string",format:"binary",contentEncoding:"binary"},{minimum:r,maximum:i,mime:a}=e._zod.bag;void 0!==r&&(t.minLength=r),void 0!==i&&(t.maxLength=i),a?1===a.length?(t.contentMediaType=a[0],Object.assign(n,t)):n.anyOf=a.map(e=>({...t,contentMediaType:e})):Object.assign(n,t);break}case"transform":if("throw"===this.unrepresentable)throw Error("Transforms cannot be represented in JSON Schema");break;case"nullable":{let e=this.process(u.innerType,t);"openapi-3.0"===this.target?(s.ref=u.innerType,n.nullable=!0):n.anyOf=[e,{type:"null"}];break}case"nonoptional":case"promise":case"optional":this.process(u.innerType,t),s.ref=u.innerType;break;case"default":this.process(u.innerType,t),s.ref=u.innerType,n.default=JSON.parse(JSON.stringify(u.defaultValue));break;case"prefault":this.process(u.innerType,t),s.ref=u.innerType,"input"===this.io&&(n._prefault=JSON.parse(JSON.stringify(u.defaultValue)));break;case"catch":{let e;this.process(u.innerType,t),s.ref=u.innerType;try{e=u.catchValue(void 0)}catch(e){throw Error("Dynamic catch values are not supported in JSON Schema")}n.default=e;break}case"nan":if("throw"===this.unrepresentable)throw Error("NaN cannot be represented in JSON Schema");break;case"template_literal":{let t=e._zod.pattern;if(!t)throw Error("Pattern not found in template literal");n.type="string",n.pattern=t.source;break}case"pipe":{let e="input"===this.io?"transform"===u.in._zod.def.type?u.out:u.in:u.out;this.process(e,t),s.ref=e;break}case"readonly":this.process(u.innerType,t),s.ref=u.innerType,n.readOnly=!0;break;case"lazy":{let n=e._zod.innerType;this.process(n,t),s.ref=n;break}case"custom":if("throw"===this.unrepresentable)throw Error("Custom types cannot be represented in JSON Schema");break;case"function":if("throw"===this.unrepresentable)throw Error("Function types cannot be represented in JSON Schema")}}}let m=this.metadataRegistry.get(e);return m&&Object.assign(s.schema,m),"input"===this.io&&function e(t,n){let r=null!=n?n:{seen:new Set};if(r.seen.has(t))return!1;r.seen.add(t);let i=t._zod.def;switch(i.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":case"custom":case"success":case"catch":case"function":return!1;case"array":return e(i.element,r);case"object":for(let t in i.shape)if(e(i.shape[t],r))return!0;return!1;case"union":for(let t of i.options)if(e(t,r))return!0;return!1;case"intersection":return e(i.left,r)||e(i.right,r);case"tuple":for(let t of i.items)if(e(t,r))return!0;if(i.rest&&e(i.rest,r))return!0;return!1;case"record":case"map":return e(i.keyType,r)||e(i.valueType,r);case"set":return e(i.valueType,r);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":case"default":case"prefault":return e(i.innerType,r);case"lazy":return e(i.getter(),r);case"transform":return!0;case"pipe":return e(i.in,r)||e(i.out,r)}throw Error("Unknown schema type: ".concat(i.type))}(e)&&(delete s.schema.examples,delete s.schema.default),"input"===this.io&&s.schema._prefault&&(null!=(r=s.schema).default||(r.default=s.schema._prefault)),delete s.schema._prefault,this.seen.get(e).schema}emit(e,t){var n,r,i,a,o,c,u,l,s,d;let m={cycles:null!=(i=null==t?void 0:t.cycles)?i:"ref",reused:null!=(a=null==t?void 0:t.reused)?a:"inline",external:null!=(o=null==t?void 0:t.external)?o:void 0},f=this.seen.get(e);if(!f)throw Error("Unprocessed schema. This is a bug in Zod.");let v=e=>{var t,n,r,i,a;let o="draft-2020-12"===this.target?"$defs":"definitions";if(m.external){let a=null==(t=m.external.registry.get(e[0]))?void 0:t.id,c=null!=(n=m.external.uri)?n:e=>e;if(a)return{ref:c(a)};let u=null!=(i=null!=(r=e[1].defId)?r:e[1].schema.id)?i:"schema".concat(this.counter++);return e[1].defId=u,{defId:u,ref:"".concat(c("__shared"),"#/").concat(o,"/").concat(u)}}if(e[1]===f)return{ref:"#"};let c="".concat("#","/").concat(o,"/"),u=null!=(a=e[1].schema.id)?a:"__schema".concat(this.counter++);return{defId:u,ref:c+u}},p=e=>{if(e[1].schema.$ref)return;let t=e[1],{ref:n,defId:r}=v(e);t.def={...t.schema},r&&(t.defId=r);let i=t.schema;for(let e in i)delete i[e];i.$ref=n};if("throw"===m.cycles)for(let e of this.seen.entries()){let t=e[1];if(t.cycle)throw Error("Cycle detected: "+"#/".concat(null==(c=t.cycle)?void 0:c.join("/"),"/<root>")+'\n\nSet the `cycles` parameter to `"ref"` to resolve cyclical schemas with defs.')}for(let t of this.seen.entries()){let n=t[1];if(e===t[0]){p(t);continue}if(m.external){let n=null==(l=m.external.registry.get(t[0]))?void 0:l.id;if(e!==t[0]&&n){p(t);continue}}if((null==(u=this.metadataRegistry.get(t[0]))?void 0:u.id)||n.cycle||n.count>1&&"ref"===m.reused){p(t);continue}}let g=(e,t)=>{var n,r,i;let a=this.seen.get(e),o=null!=(n=a.def)?n:a.schema,c={...o};if(null===a.ref)return;let u=a.ref;if(a.ref=null,u){g(u,t);let e=this.seen.get(u).schema;e.$ref&&("draft-7"===t.target||"draft-4"===t.target||"openapi-3.0"===t.target)?(o.allOf=null!=(r=o.allOf)?r:[],o.allOf.push(e)):(Object.assign(o,e),Object.assign(o,c))}a.isParent||this.override({zodSchema:e,jsonSchema:o,path:null!=(i=a.path)?i:[]})};for(let e of[...this.seen.entries()].reverse())g(e[0],{target:this.target});let h={};if("draft-2020-12"===this.target?h.$schema="https://json-schema.org/draft/2020-12/schema":"draft-7"===this.target?h.$schema="http://json-schema.org/draft-07/schema#":"draft-4"===this.target?h.$schema="http://json-schema.org/draft-04/schema#":"openapi-3.0"===this.target||console.warn("Invalid target: ".concat(this.target)),null==(n=m.external)?void 0:n.uri){let t=null==(s=m.external.registry.get(e))?void 0:s.id;if(!t)throw Error("Schema is missing an `id` property");h.$id=m.external.uri(t)}Object.assign(h,f.def);let y=null!=(d=null==(r=m.external)?void 0:r.defs)?d:{};for(let e of this.seen.entries()){let t=e[1];t.def&&t.defId&&(y[t.defId]=t.def)}m.external||Object.keys(y).length>0&&("draft-2020-12"===this.target?h.$defs=y:h.definitions=y);try{return JSON.parse(JSON.stringify(h))}catch(e){throw Error("Error converting schema to JSON.")}}constructor(e){var t,n,r,i,a;this.counter=0,this.metadataRegistry=null!=(t=null==e?void 0:e.metadata)?t:tf,this.target=null!=(n=null==e?void 0:e.target)?n:"draft-2020-12",this.unrepresentable=null!=(r=null==e?void 0:e.unrepresentable)?r:"throw",this.override=null!=(i=null==e?void 0:e.override)?i:()=>{},this.io=null!=(a=null==e?void 0:e.io)?a:"output",this.seen=new Map}}function n8(e,t){if(e instanceof td){let n=new n3(t),r={};for(let t of e._idmap.entries()){let[e,r]=t;n.process(r)}let i={},a={registry:e,uri:null==t?void 0:t.uri,defs:r};for(let r of e._idmap.entries()){let[e,o]=r;i[e]=n.emit(o,{...t,external:a})}return Object.keys(r).length>0&&(i.__shared={["draft-2020-12"===n.target?"$defs":"definitions"]:r}),{schemas:i}}let n=new n3(t);return n.process(e),n.emit(e,t)}e.i(71071);var n7=e.i(64085),n5=e.i(38171),re=e.i(46590);e.s(["ZodISODate",()=>aG,"ZodISODateTime",()=>aB,"ZodISODuration",()=>aH,"ZodISOTime",()=>aq,"date",()=>aX,"datetime",()=>aK,"duration",()=>aQ,"time",()=>aY],45417),e.s(["ZodAny",()=>i$,"ZodArray",()=>iC,"ZodBase64",()=>r1,"ZodBase64URL",()=>r9,"ZodBigInt",()=>iy,"ZodBigIntFormat",()=>ib,"ZodBoolean",()=>ig,"ZodCIDRv4",()=>rQ,"ZodCIDRv6",()=>r6,"ZodCUID",()=>rC,"ZodCUID2",()=>rV,"ZodCatch",()=>a_,"ZodCodec",()=>aS,"ZodCustom",()=>aT,"ZodCustomStringFormat",()=>it,"ZodDate",()=>iT,"ZodDefault",()=>ad,"ZodDiscriminatedUnion",()=>iG,"ZodE164",()=>r8,"ZodEmail",()=>rz,"ZodEmoji",()=>rE,"ZodEnum",()=>i8,"ZodFile",()=>an,"ZodFunction",()=>aE,"ZodGUID",()=>rS,"ZodIPv4",()=>rX,"ZodIPv6",()=>rY,"ZodIntersection",()=>iq,"ZodJWT",()=>r5,"ZodKSUID",()=>rK,"ZodLazy",()=>aO,"ZodLiteral",()=>ae,"ZodMap",()=>i1,"ZodNaN",()=>ak,"ZodNanoID",()=>rT,"ZodNever",()=>iN,"ZodNonOptional",()=>ap,"ZodNull",()=>ix,"ZodNullable",()=>au,"ZodNumber",()=>ic,"ZodNumberFormat",()=>il,"ZodObject",()=>iF,"ZodOptional",()=>ao,"ZodPipe",()=>az,"ZodPrefault",()=>af,"ZodPromise",()=>aN,"ZodReadonly",()=>ax,"ZodRecord",()=>i4,"ZodSet",()=>i9,"ZodString",()=>rb,"ZodStringFormat",()=>rI,"ZodSuccess",()=>ah,"ZodSymbol",()=>iz,"ZodTemplateLiteral",()=>a$,"ZodTransform",()=>ai,"ZodTuple",()=>iH,"ZodType",()=>ry,"ZodULID",()=>rJ,"ZodURL",()=>rP,"ZodUUID",()=>rx,"ZodUndefined",()=>iS,"ZodUnion",()=>iB,"ZodUnknown",()=>iO,"ZodVoid",()=>iE,"ZodXID",()=>rW,"_ZodString",()=>r_,"_default",()=>am,"_function",()=>aA,"any",()=>iU,"array",()=>iR,"base64",()=>r2,"base64url",()=>r3,"bigint",()=>i_,"boolean",()=>ih,"catch",()=>ab,"check",()=>aL,"cidrv4",()=>r4,"cidrv6",()=>r0,"codec",()=>aZ,"cuid",()=>rR,"cuid2",()=>rF,"custom",()=>aC,"date",()=>iL,"discriminatedUnion",()=>iX,"e164",()=>r7,"email",()=>rw,"emoji",()=>rA,"enum",()=>i7,"file",()=>ar,"float32",()=>id,"float64",()=>im,"function",()=>aA,"guid",()=>rZ,"hash",()=>io,"hex",()=>ia,"hostname",()=>ii,"httpUrl",()=>rD,"instanceof",()=>aF,"int",()=>is,"int32",()=>iv,"int64",()=>ik,"intersection",()=>iY,"ipv4",()=>rq,"ipv6",()=>rH,"json",()=>aM,"jwt",()=>ie,"keyof",()=>iV,"ksuid",()=>rG,"lazy",()=>aP,"literal",()=>at,"looseObject",()=>iW,"map",()=>i2,"nan",()=>aI,"nanoid",()=>rL,"nativeEnum",()=>i5,"never",()=>iD,"nonoptional",()=>ag,"null",()=>ij,"nullable",()=>al,"nullish",()=>as,"number",()=>iu,"object",()=>iJ,"optional",()=>ac,"partialRecord",()=>i0,"pipe",()=>aw,"prefault",()=>av,"preprocess",()=>aW,"promise",()=>aD,"readonly",()=>aj,"record",()=>i6,"refine",()=>aR,"set",()=>i3,"strictObject",()=>iM,"string",()=>rk,"stringFormat",()=>ir,"stringbool",()=>aJ,"success",()=>ay,"superRefine",()=>aV,"symbol",()=>iw,"templateLiteral",()=>aU,"transform",()=>aa,"tuple",()=>iQ,"uint32",()=>ip,"uint64",()=>iI,"ulid",()=>rM,"undefined",()=>iZ,"union",()=>iK,"unknown",()=>iP,"url",()=>rN,"uuid",()=>rj,"uuidv4",()=>r$,"uuidv6",()=>rU,"uuidv7",()=>rO,"void",()=>iA,"xid",()=>rB],50651);var rt=a,rn=o;e.s(["decode",()=>rd,"decodeAsync",()=>rf,"encode",()=>rs,"encodeAsync",()=>rm,"parse",()=>ro,"parseAsync",()=>rc,"safeDecode",()=>rp,"safeDecodeAsync",()=>rh,"safeEncode",()=>rv,"safeEncodeAsync",()=>rg,"safeParse",()=>ru,"safeParseAsync",()=>rl],73119),e.s(["ZodError",()=>ri,"ZodRealError",()=>ra],12055);let rr=(e,t)=>{i.$ZodError.init(e,t),e.name="ZodError",Object.defineProperties(e,{format:{value:t=>i.formatError(e,t)},flatten:{value:t=>i.flattenError(e,t)},addIssue:{value:t=>{e.issues.push(t),e.message=JSON.stringify(e.issues,o.jsonStringifyReplacer,2)}},addIssues:{value:t=>{e.issues.push(...t),e.message=JSON.stringify(e.issues,o.jsonStringifyReplacer,2)}},isEmpty:{get:()=>0===e.issues.length}})},ri=n.$constructor("ZodError",rr),ra=n.$constructor("ZodError",rr,{Parent:Error}),ro=r._parse(ra),rc=r._parseAsync(ra),ru=r._safeParse(ra),rl=r._safeParseAsync(ra),rs=r._encode(ra),rd=r._decode(ra),rm=r._encodeAsync(ra),rf=r._decodeAsync(ra),rv=r._safeEncode(ra),rp=r._safeDecode(ra),rg=r._safeEncodeAsync(ra),rh=r._safeDecodeAsync(ra),ry=n.$constructor("ZodType",(e,t)=>(N.init(e,t),e.def=t,e.type=t.type,Object.defineProperty(e,"_def",{value:t}),e.check=function(){for(var n,r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];return e.clone({...t,checks:[...null!=(n=t.checks)?n:[],...i.map(e=>"function"==typeof e?{_zod:{check:e,def:{check:"custom"},onattach:[]}}:e)]})},e.clone=(t,n)=>o.clone(e,t,n),e.brand=()=>e,e.register=(t,n)=>(t.add(e,n),e),e.parse=(t,n)=>ro(e,t,n,{callee:e.parse}),e.safeParse=(t,n)=>ru(e,t,n),e.parseAsync=async(t,n)=>rc(e,t,n,{callee:e.parseAsync}),e.safeParseAsync=async(t,n)=>rl(e,t,n),e.spa=e.safeParseAsync,e.encode=(t,n)=>rs(e,t,n),e.decode=(t,n)=>rd(e,t,n),e.encodeAsync=async(t,n)=>rm(e,t,n),e.decodeAsync=async(t,n)=>rf(e,t,n),e.safeEncode=(t,n)=>rv(e,t,n),e.safeDecode=(t,n)=>rp(e,t,n),e.safeEncodeAsync=async(t,n)=>rg(e,t,n),e.safeDecodeAsync=async(t,n)=>rh(e,t,n),e.refine=(t,n)=>e.check(aR(t,n)),e.superRefine=t=>e.check(n0(t)),e.overwrite=t=>e.check(nS(t)),e.optional=()=>ac(e),e.nullable=()=>al(e),e.nullish=()=>ac(al(e)),e.nonoptional=t=>ag(e,t),e.array=()=>iR(e),e.or=t=>iK([e,t]),e.and=t=>iY(e,t),e.transform=t=>aw(e,aa(t)),e.default=t=>am(e,t),e.prefault=t=>av(e,t),e.catch=t=>ab(e,t),e.pipe=t=>aw(e,t),e.readonly=()=>aj(e),e.describe=t=>{let n=e.clone();return tf.add(n,{description:t}),n},Object.defineProperty(e,"description",{get(){var t;return null==(t=tf.get(e))?void 0:t.description},configurable:!0}),e.meta=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];if(0===n.length)return tf.get(e);let i=e.clone();return tf.add(i,n[0]),i},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),r_=n.$constructor("_ZodString",(e,t)=>{var n,r,i;D.init(e,t),ry.init(e,t);let a=e._zod.bag;e.format=null!=(n=a.format)?n:null,e.minLength=null!=(r=a.minimum)?r:null,e.maxLength=null!=(i=a.maximum)?i:null,e.regex=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(nh(...n))},e.includes=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(nb(...n))},e.startsWith=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(nk(...n))},e.endsWith=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(nI(...n))},e.min=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(np(...n))},e.max=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(nv(...n))},e.length=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(ng(...n))},e.nonempty=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(np(1,...n))},e.lowercase=t=>e.check(ny(t)),e.uppercase=t=>e.check(n_(t)),e.trim=()=>e.check(nx()),e.normalize=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(nZ(...n))},e.toLowerCase=()=>e.check(nj()),e.toUpperCase=()=>e.check(n$())}),rb=n.$constructor("ZodString",(e,t)=>{D.init(e,t),r_.init(e,t),e.email=t=>e.check(tg(rz,t)),e.url=t=>e.check(tI(rP,t)),e.jwt=t=>e.check(tT(r5,t)),e.emoji=t=>e.check(tz(rE,t)),e.guid=t=>e.check(th(rS,t)),e.uuid=t=>e.check(ty(rx,t)),e.uuidv4=t=>e.check(t_(rx,t)),e.uuidv6=t=>e.check(tb(rx,t)),e.uuidv7=t=>e.check(tk(rx,t)),e.nanoid=t=>e.check(tw(rT,t)),e.guid=t=>e.check(th(rS,t)),e.cuid=t=>e.check(tS(rC,t)),e.cuid2=t=>e.check(tZ(rV,t)),e.ulid=t=>e.check(tx(rJ,t)),e.base64=t=>e.check(tD(r1,t)),e.base64url=t=>e.check(tE(r9,t)),e.xid=t=>e.check(tj(rW,t)),e.ksuid=t=>e.check(t$(rK,t)),e.ipv4=t=>e.check(tU(rX,t)),e.ipv6=t=>e.check(tO(rY,t)),e.cidrv4=t=>e.check(tP(rQ,t)),e.cidrv6=t=>e.check(tN(r6,t)),e.e164=t=>e.check(tA(r8,t)),e.datetime=t=>e.check(aK(t)),e.date=t=>e.check(aX(t)),e.time=t=>e.check(aY(t)),e.duration=t=>e.check(aQ(t))});function rk(e){return tv(rb,e)}let rI=n.$constructor("ZodStringFormat",(e,t)=>{E.init(e,t),r_.init(e,t)}),rz=n.$constructor("ZodEmail",(e,t)=>{L.init(e,t),rI.init(e,t)});function rw(e){return tg(rz,e)}let rS=n.$constructor("ZodGUID",(e,t)=>{A.init(e,t),rI.init(e,t)});function rZ(e){return th(rS,e)}let rx=n.$constructor("ZodUUID",(e,t)=>{T.init(e,t),rI.init(e,t)});function rj(e){return ty(rx,e)}function r$(e){return t_(rx,e)}function rU(e){return tb(rx,e)}function rO(e){return tk(rx,e)}let rP=n.$constructor("ZodURL",(e,t)=>{C.init(e,t),rI.init(e,t)});function rN(e){return tI(rP,e)}function rD(e){return tI(rP,{protocol:/^https?$/,hostname:rt.domain,...rn.normalizeParams(e)})}let rE=n.$constructor("ZodEmoji",(e,t)=>{R.init(e,t),rI.init(e,t)});function rA(e){return tz(rE,e)}let rT=n.$constructor("ZodNanoID",(e,t)=>{V.init(e,t),rI.init(e,t)});function rL(e){return tw(rT,e)}let rC=n.$constructor("ZodCUID",(e,t)=>{F.init(e,t),rI.init(e,t)});function rR(e){return tS(rC,e)}let rV=n.$constructor("ZodCUID2",(e,t)=>{J.init(e,t),rI.init(e,t)});function rF(e){return tZ(rV,e)}let rJ=n.$constructor("ZodULID",(e,t)=>{M.init(e,t),rI.init(e,t)});function rM(e){return tx(rJ,e)}let rW=n.$constructor("ZodXID",(e,t)=>{W.init(e,t),rI.init(e,t)});function rB(e){return tj(rW,e)}let rK=n.$constructor("ZodKSUID",(e,t)=>{B.init(e,t),rI.init(e,t)});function rG(e){return t$(rK,e)}let rX=n.$constructor("ZodIPv4",(e,t)=>{Y.init(e,t),rI.init(e,t)});function rq(e){return tU(rX,e)}let rY=n.$constructor("ZodIPv6",(e,t)=>{H.init(e,t),rI.init(e,t)});function rH(e){return tO(rY,e)}let rQ=n.$constructor("ZodCIDRv4",(e,t)=>{Q.init(e,t),rI.init(e,t)});function r4(e){return tP(rQ,e)}let r6=n.$constructor("ZodCIDRv6",(e,t)=>{ee.init(e,t),rI.init(e,t)});function r0(e){return tN(r6,e)}let r1=n.$constructor("ZodBase64",(e,t)=>{en.init(e,t),rI.init(e,t)});function r2(e){return tD(r1,e)}let r9=n.$constructor("ZodBase64URL",(e,t)=>{ei.init(e,t),rI.init(e,t)});function r3(e){return tE(r9,e)}let r8=n.$constructor("ZodE164",(e,t)=>{ea.init(e,t),rI.init(e,t)});function r7(e){return tA(r8,e)}let r5=n.$constructor("ZodJWT",(e,t)=>{ec.init(e,t),rI.init(e,t)});function ie(e){return tT(r5,e)}let it=n.$constructor("ZodCustomStringFormat",(e,t)=>{eu.init(e,t),rI.init(e,t)});function ir(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n9(it,e,t,n)}function ii(e){return n9(it,"hostname",rt.hostname,e)}function ia(e){return n9(it,"hex",rt.hex,e)}function io(e,t){var n;let r=null!=(n=null==t?void 0:t.enc)?n:"hex",i="".concat(e,"_").concat(r),a=rt[i];if(!a)throw Error("Unrecognized hash format: ".concat(i));return n9(it,i,a,t)}let ic=n.$constructor("ZodNumber",(e,t)=>{var n,r,i,a,o,c,u,l,s;el.init(e,t),ry.init(e,t),e.gt=(t,n)=>e.check(ni(t,n)),e.gte=(t,n)=>e.check(na(t,n)),e.min=(t,n)=>e.check(na(t,n)),e.lt=(t,n)=>e.check(nn(t,n)),e.lte=(t,n)=>e.check(nr(t,n)),e.max=(t,n)=>e.check(nr(t,n)),e.int=t=>e.check(is(t)),e.safe=t=>e.check(is(t)),e.positive=t=>e.check(ni(0,t)),e.nonnegative=t=>e.check(na(0,t)),e.negative=t=>e.check(nn(0,t)),e.nonpositive=t=>e.check(nr(0,t)),e.multipleOf=(t,n)=>e.check(ns(t,n)),e.step=(t,n)=>e.check(ns(t,n)),e.finite=()=>e;let d=e._zod.bag;e.minValue=null!=(i=Math.max(null!=(n=d.minimum)?n:-1/0,null!=(r=d.exclusiveMinimum)?r:-1/0))?i:null,e.maxValue=null!=(c=Math.min(null!=(a=d.maximum)?a:1/0,null!=(o=d.exclusiveMaximum)?o:1/0))?c:null,e.isInt=(null!=(u=d.format)?u:"").includes("int")||Number.isSafeInteger(null!=(l=d.multipleOf)?l:.5),e.isFinite=!0,e.format=null!=(s=d.format)?s:null});function iu(e){return tJ(ic,e)}let il=n.$constructor("ZodNumberFormat",(e,t)=>{es.init(e,t),ic.init(e,t)});function is(e){return tW(il,e)}function id(e){return tB(il,e)}function im(e){return tK(il,e)}function iv(e){return tG(il,e)}function ip(e){return tX(il,e)}let ig=n.$constructor("ZodBoolean",(e,t)=>{ed.init(e,t),ry.init(e,t)});function ih(e){return tq(ig,e)}let iy=n.$constructor("ZodBigInt",(e,t)=>{var n,r,i;em.init(e,t),ry.init(e,t),e.gte=(t,n)=>e.check(na(t,n)),e.min=(t,n)=>e.check(na(t,n)),e.gt=(t,n)=>e.check(ni(t,n)),e.gte=(t,n)=>e.check(na(t,n)),e.min=(t,n)=>e.check(na(t,n)),e.lt=(t,n)=>e.check(nn(t,n)),e.lte=(t,n)=>e.check(nr(t,n)),e.max=(t,n)=>e.check(nr(t,n)),e.positive=t=>e.check(ni(BigInt(0),t)),e.negative=t=>e.check(nn(BigInt(0),t)),e.nonpositive=t=>e.check(nr(BigInt(0),t)),e.nonnegative=t=>e.check(na(BigInt(0),t)),e.multipleOf=(t,n)=>e.check(ns(t,n));let a=e._zod.bag;e.minValue=null!=(n=a.minimum)?n:null,e.maxValue=null!=(r=a.maximum)?r:null,e.format=null!=(i=a.format)?i:null});function i_(e){return tH(iy,e)}let ib=n.$constructor("ZodBigIntFormat",(e,t)=>{ef.init(e,t),iy.init(e,t)});function ik(e){return t4(ib,e)}function iI(e){return t6(ib,e)}let iz=n.$constructor("ZodSymbol",(e,t)=>{ev.init(e,t),ry.init(e,t)});function iw(e){return t0(iz,e)}let iS=n.$constructor("ZodUndefined",(e,t)=>{ep.init(e,t),ry.init(e,t)});function iZ(e){return t1(iS,e)}let ix=n.$constructor("ZodNull",(e,t)=>{eg.init(e,t),ry.init(e,t)});function ij(e){return t2(ix,e)}let i$=n.$constructor("ZodAny",(e,t)=>{eh.init(e,t),ry.init(e,t)});function iU(){return t9(i$)}let iO=n.$constructor("ZodUnknown",(e,t)=>{ey.init(e,t),ry.init(e,t)});function iP(){return t3(iO)}let iN=n.$constructor("ZodNever",(e,t)=>{e_.init(e,t),ry.init(e,t)});function iD(e){return t8(iN,e)}let iE=n.$constructor("ZodVoid",(e,t)=>{eb.init(e,t),ry.init(e,t)});function iA(e){return t7(iE,e)}let iT=n.$constructor("ZodDate",(e,t)=>{ek.init(e,t),ry.init(e,t),e.min=(t,n)=>e.check(na(t,n)),e.max=(t,n)=>e.check(nr(t,n));let n=e._zod.bag;e.minDate=n.minimum?new Date(n.minimum):null,e.maxDate=n.maximum?new Date(n.maximum):null});function iL(e){return t5(iT,e)}let iC=n.$constructor("ZodArray",(e,t)=>{ez.init(e,t),ry.init(e,t),e.element=t.element,e.min=(t,n)=>e.check(np(t,n)),e.nonempty=t=>e.check(np(1,t)),e.max=(t,n)=>e.check(nv(t,n)),e.length=(t,n)=>e.check(ng(t,n)),e.unwrap=()=>e.element});function iR(e,t){return nU(iC,e,t)}function iV(e){return i7(Object.keys(e._zod.def.shape))}let iF=n.$constructor("ZodObject",(e,t)=>{ej.init(e,t),ry.init(e,t),rn.defineLazy(e,"shape",()=>t.shape),e.keyof=()=>i7(Object.keys(e._zod.def.shape)),e.catchall=t=>e.clone({...e._zod.def,catchall:t}),e.passthrough=()=>e.clone({...e._zod.def,catchall:iP()}),e.loose=()=>e.clone({...e._zod.def,catchall:iP()}),e.strict=()=>e.clone({...e._zod.def,catchall:iD()}),e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=t=>rn.extend(e,t),e.safeExtend=t=>rn.safeExtend(e,t),e.merge=t=>rn.merge(e,t),e.pick=t=>rn.pick(e,t),e.omit=t=>rn.omit(e,t),e.partial=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return rn.partial(ao,e,n[0])},e.required=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return rn.required(ap,e,n[0])}});function iJ(e,t){return new iF({type:"object",get shape(){return rn.assignProp(this,"shape",e?rn.objectClone(e):{}),this.shape},...rn.normalizeParams(t)})}function iM(e,t){return new iF({type:"object",get shape(){return rn.assignProp(this,"shape",rn.objectClone(e)),this.shape},catchall:iD(),...rn.normalizeParams(t)})}function iW(e,t){return new iF({type:"object",get shape(){return rn.assignProp(this,"shape",rn.objectClone(e)),this.shape},catchall:iP(),...rn.normalizeParams(t)})}let iB=n.$constructor("ZodUnion",(e,t)=>{eU.init(e,t),ry.init(e,t),e.options=t.options});function iK(e,t){return new iB({type:"union",options:e,...rn.normalizeParams(t)})}let iG=n.$constructor("ZodDiscriminatedUnion",(e,t)=>{iB.init(e,t),eO.init(e,t)});function iX(e,t,n){return new iG({type:"union",options:t,discriminator:e,...rn.normalizeParams(n)})}let iq=n.$constructor("ZodIntersection",(e,t)=>{eP.init(e,t),ry.init(e,t)});function iY(e,t){return new iq({type:"intersection",left:e,right:t})}let iH=n.$constructor("ZodTuple",(e,t)=>{eD.init(e,t),ry.init(e,t),e.rest=t=>e.clone({...e._zod.def,rest:t})});function iQ(e,t,n){let r=t instanceof N,i=r?n:t;return new iH({type:"tuple",items:e,rest:r?t:null,...rn.normalizeParams(i)})}let i4=n.$constructor("ZodRecord",(e,t)=>{eA.init(e,t),ry.init(e,t),e.keyType=t.keyType,e.valueType=t.valueType});function i6(e,t,n){return new i4({type:"record",keyType:e,valueType:t,...rn.normalizeParams(n)})}function i0(e,t,n){let r=o.clone(e);return r._zod.values=void 0,new i4({type:"record",keyType:r,valueType:t,...rn.normalizeParams(n)})}let i1=n.$constructor("ZodMap",(e,t)=>{eT.init(e,t),ry.init(e,t),e.keyType=t.keyType,e.valueType=t.valueType});function i2(e,t,n){return new i1({type:"map",keyType:e,valueType:t,...rn.normalizeParams(n)})}let i9=n.$constructor("ZodSet",(e,t)=>{eC.init(e,t),ry.init(e,t),e.min=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(nm(...n))},e.nonempty=t=>e.check(nm(1,t)),e.max=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(nd(...n))},e.size=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.check(nf(...n))}});function i3(e,t){return new i9({type:"set",valueType:e,...rn.normalizeParams(t)})}let i8=n.$constructor("ZodEnum",(e,t)=>{eV.init(e,t),ry.init(e,t),e.enum=t.entries,e.options=Object.values(t.entries);let n=new Set(Object.keys(t.entries));e.extract=(e,r)=>{let i={};for(let r of e)if(n.has(r))i[r]=t.entries[r];else throw Error("Key ".concat(r," not found in enum"));return new i8({...t,checks:[],...rn.normalizeParams(r),entries:i})},e.exclude=(e,r)=>{let i={...t.entries};for(let t of e)if(n.has(t))delete i[t];else throw Error("Key ".concat(t," not found in enum"));return new i8({...t,checks:[],...rn.normalizeParams(r),entries:i})}});function i7(e,t){return new i8({type:"enum",entries:Array.isArray(e)?Object.fromEntries(e.map(e=>[e,e])):e,...rn.normalizeParams(t)})}function i5(e,t){return new i8({type:"enum",entries:e,...rn.normalizeParams(t)})}let ae=n.$constructor("ZodLiteral",(e,t)=>{eF.init(e,t),ry.init(e,t),e.values=new Set(t.values),Object.defineProperty(e,"value",{get(){if(t.values.length>1)throw Error("This schema contains multiple valid literal values. Use `.values` instead.");return t.values[0]}})});function at(e,t){return new ae({type:"literal",values:Array.isArray(e)?e:[e],...rn.normalizeParams(t)})}let an=n.$constructor("ZodFile",(e,t)=>{eJ.init(e,t),ry.init(e,t),e.min=(t,n)=>e.check(nm(t,n)),e.max=(t,n)=>e.check(nd(t,n)),e.mime=(t,n)=>e.check(nw(Array.isArray(t)?t:[t],n))});function ar(e){return nV(an,e)}let ai=n.$constructor("ZodTransform",(e,t)=>{eM.init(e,t),ry.init(e,t),e._zod.parse=(r,i)=>{if("backward"===i.direction)throw new n.$ZodEncodeError(e.constructor.name);r.addIssue=n=>{if("string"==typeof n)r.issues.push(rn.issue(n,r.value,t));else n.fatal&&(n.continue=!1),null!=n.code||(n.code="custom"),null!=n.input||(n.input=r.value),null!=n.inst||(n.inst=e),r.issues.push(rn.issue(n))};let a=t.transform(r.value,r);return a instanceof Promise?a.then(e=>(r.value=e,r)):(r.value=a,r)}});function aa(e){return new ai({type:"transform",transform:e})}let ao=n.$constructor("ZodOptional",(e,t)=>{eB.init(e,t),ry.init(e,t),e.unwrap=()=>e._zod.def.innerType});function ac(e){return new ao({type:"optional",innerType:e})}let au=n.$constructor("ZodNullable",(e,t)=>{eK.init(e,t),ry.init(e,t),e.unwrap=()=>e._zod.def.innerType});function al(e){return new au({type:"nullable",innerType:e})}function as(e){return ac(al(e))}let ad=n.$constructor("ZodDefault",(e,t)=>{eG.init(e,t),ry.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap});function am(e,t){return new ad({type:"default",innerType:e,get defaultValue(){return"function"==typeof t?t():rn.shallowClone(t)}})}let af=n.$constructor("ZodPrefault",(e,t)=>{eq.init(e,t),ry.init(e,t),e.unwrap=()=>e._zod.def.innerType});function av(e,t){return new af({type:"prefault",innerType:e,get defaultValue(){return"function"==typeof t?t():rn.shallowClone(t)}})}let ap=n.$constructor("ZodNonOptional",(e,t)=>{eY.init(e,t),ry.init(e,t),e.unwrap=()=>e._zod.def.innerType});function ag(e,t){return new ap({type:"nonoptional",innerType:e,...rn.normalizeParams(t)})}let ah=n.$constructor("ZodSuccess",(e,t)=>{eQ.init(e,t),ry.init(e,t),e.unwrap=()=>e._zod.def.innerType});function ay(e){return new ah({type:"success",innerType:e})}let a_=n.$constructor("ZodCatch",(e,t)=>{e4.init(e,t),ry.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap});function ab(e,t){return new a_({type:"catch",innerType:e,catchValue:"function"==typeof t?t:()=>t})}let ak=n.$constructor("ZodNaN",(e,t)=>{e6.init(e,t),ry.init(e,t)});function aI(e){return nt(ak,e)}let az=n.$constructor("ZodPipe",(e,t)=>{e0.init(e,t),ry.init(e,t),e.in=t.in,e.out=t.out});function aw(e,t){return new az({type:"pipe",in:e,out:t})}let aS=n.$constructor("ZodCodec",(e,t)=>{az.init(e,t),e2.init(e,t)});function aZ(e,t,n){return new aS({type:"pipe",in:e,out:t,transform:n.decode,reverseTransform:n.encode})}let ax=n.$constructor("ZodReadonly",(e,t)=>{e8.init(e,t),ry.init(e,t),e.unwrap=()=>e._zod.def.innerType});function aj(e){return new ax({type:"readonly",innerType:e})}let a$=n.$constructor("ZodTemplateLiteral",(e,t)=>{e5.init(e,t),ry.init(e,t)});function aU(e,t){return new a$({type:"template_literal",parts:e,...rn.normalizeParams(t)})}let aO=n.$constructor("ZodLazy",(e,t)=>{tn.init(e,t),ry.init(e,t),e.unwrap=()=>e._zod.def.getter()});function aP(e){return new aO({type:"lazy",getter:e})}let aN=n.$constructor("ZodPromise",(e,t)=>{tt.init(e,t),ry.init(e,t),e.unwrap=()=>e._zod.def.innerType});function aD(e){return new aN({type:"promise",innerType:e})}let aE=n.$constructor("ZodFunction",(e,t)=>{te.init(e,t),ry.init(e,t)});function aA(e){var t,n;return new aE({type:"function",input:Array.isArray(null==e?void 0:e.input)?iQ(null==e?void 0:e.input):null!=(t=null==e?void 0:e.input)?t:iR(iP()),output:null!=(n=null==e?void 0:e.output)?n:iP()})}let aT=n.$constructor("ZodCustom",(e,t)=>{tr.init(e,t),ry.init(e,t)});function aL(e){let t=new c({check:"custom"});return t._zod.check=e,t}function aC(e,t){return n4(aT,null!=e?e:()=>!0,t)}function aR(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n6(aT,e,t)}function aV(e){return n0(e)}function aF(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{error:"Input not instance of ".concat(e.name)},n=new aT({type:"custom",check:"custom",fn:t=>t instanceof e,abort:!0,...rn.normalizeParams(t)});return n._zod.bag.Class=e,n}let aJ=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return n2({Codec:aS,Boolean:ig,String:rb},...t)};function aM(e){let t=aP(()=>iK([rk(e),iu(),ih(),ij(),iR(t),i6(rk(),t)]));return t}function aW(e,t){return aw(aa(e),t)}let aB=n.$constructor("ZodISODateTime",(e,t)=>{K.init(e,t),rI.init(e,t)});function aK(e){return tC(aB,e)}let aG=n.$constructor("ZodISODate",(e,t)=>{G.init(e,t),rI.init(e,t)});function aX(e){return tR(aG,e)}let aq=n.$constructor("ZodISOTime",(e,t)=>{X.init(e,t),rI.init(e,t)});function aY(e){return tV(aq,e)}let aH=n.$constructor("ZodISODuration",(e,t)=>{q.init(e,t),rI.init(e,t)});function aQ(e){return tF(aH,e)}var a4=e.i(45417);function a6(e){return tp(rb,e)}function a0(e){return tM(ic,e)}function a1(e){return tY(ig,e)}function a2(e){return tQ(iy,e)}function a9(e){return ne(iT,e)}e.s(["bigint",()=>a2,"boolean",()=>a1,"date",()=>a9,"number",()=>a0,"string",()=>a6],12847);var a3=e.i(12847);(0,n.config)((0,re.default)()),e.i(75579);var a8=n5;e.i(50651),e.s(["endsWith",()=>nI,"gt",()=>ni,"gte",()=>na,"includes",()=>nb,"length",()=>ng,"lowercase",()=>ny,"lt",()=>nn,"lte",()=>nr,"maxLength",()=>nv,"maxSize",()=>nd,"mime",()=>nw,"minLength",()=>np,"minSize",()=>nm,"multipleOf",()=>ns,"negative",()=>nc,"nonnegative",()=>nl,"nonpositive",()=>nu,"normalize",()=>nZ,"overwrite",()=>nS,"positive",()=>no,"property",()=>nz,"regex",()=>nh,"size",()=>nf,"startsWith",()=>nk,"toLowerCase",()=>nj,"toUpperCase",()=>n$,"trim",()=>nx,"uppercase",()=>n_],52344),e.s([],95771),e.i(95771),e.i(52344),e.i(12055),e.i(73119),e.s(["$brand",()=>ot.$brand,"ZodFirstPartyTypeKind",()=>t,"ZodIssueCode",()=>a7,"config",()=>ot.config,"getErrorMap",()=>oe,"setErrorMap",()=>a5],21669),e.s(["ZodFirstPartyTypeKind",()=>t,"ZodIssueCode",()=>a7,"getErrorMap",()=>oe,"setErrorMap",()=>a5],73397);let a7={invalid_type:"invalid_type",too_big:"too_big",too_small:"too_small",invalid_format:"invalid_format",not_multiple_of:"not_multiple_of",unrecognized_keys:"unrecognized_keys",invalid_union:"invalid_union",invalid_key:"invalid_key",invalid_element:"invalid_element",invalid_value:"invalid_value",custom:"custom"};function a5(e){n.config({customError:e})}function oe(){return n.config().customError}t||(t={}),e.i(73397);var ot=n;e.i(21669);var on=n,or=o,rt=a,oi=i,rn=o,oa=tu,oo=a4,oc=a3,ou=e.i(93129)},75680,e=>{"use strict";e.s(["Card",()=>r,"CardContent",()=>c,"CardDescription",()=>o,"CardHeader",()=>i,"CardTitle",()=>a]);var t=e.i(4051),n=e.i(41428);function r(e){let{className:r,...i}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...i})}function i(e){let{className:r,...i}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...i})}function a(e){let{className:r,...i}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",r),...i})}function o(e){let{className:r,...i}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",r),...i})}function c(e){let{className:r,...i}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",r),...i})}}]);