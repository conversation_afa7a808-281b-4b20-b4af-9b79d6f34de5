(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,57223,e=>{"use strict";e.s(["DollarSign",()=>t],57223);let t=(0,e.i(44571).default)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},44640,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ReflectAdapter",{enumerable:!0,get:function(){return s}});class s{static get(e,t,r){let s=Reflect.get(e,t,r);return"function"==typeof s?s.bind(e):s}static set(e,t,r,s){return Reflect.set(e,t,r,s)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},8356,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorageInstance",{enumerable:!0,get:function(){return s}});let s=(0,e.r(97918).createAsyncLocalStorage)()},62355,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorage",{enumerable:!0,get:function(){return s.afterTaskAsyncStorageInstance}});let s=e.r(8356)},17939,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return o},throwWithStaticGenerationBailoutError:function(){return a},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return i}});let s=e.r(85115),n=e.r(62355);function a(e,t){throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(e," couldn't be rendered statically because it used ").concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function i(e,t){throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(e,' with `dynamic = "error"` couldn\'t be rendered statically because it used ').concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function o(e,t){let r=Object.defineProperty(Error("Route ".concat(e.route,' used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(r,t),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=r),r}function c(){let e=n.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},93168,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{describeHasCheckingStringProperty:function(){return a},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return i}});let s=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return s.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function a(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let i=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},63470,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var o=a?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(s,i,o):s[i]=e[i]}return s.default=e,r&&r.set(e,s),s}(e.r(38477));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}let a={current:null},i="function"==typeof s.cache?s.cache:e=>e,o=console.warn;function c(e){return function(){for(var t=arguments.length,r=Array(t),s=0;s<t;s++)r[s]=arguments[s];o(e(...r))}}i(e=>{try{o(a.current)}finally{a.current=null}})},4467,e=>{"use strict";e.s(["useApi",()=>s]);var t=e.i(1269),r=e.i(1831);function s(){let{data:e}=(0,t.useSession)(),s=async function(t){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(null==e?void 0:e.accessToken))throw Error("Non authentifié");return r.apiService.authenticatedRequest(t,e.accessToken,s)};return{login:r.apiService.login.bind(r.apiService),register:r.apiService.register.bind(r.apiService),authenticatedRequest:s,getUsers:()=>s("/users"),getUser:e=>s("/users/".concat(e)),createUser:e=>s("/users",{method:"POST",body:JSON.stringify(e)}),updateUser:(e,t)=>s("/users/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteUser:e=>s("/users/".concat(e),{method:"DELETE"}),getSessions:()=>s("/sessions"),getSession:e=>s("/sessions/".concat(e)),createSession:e=>s("/sessions",{method:"POST",body:JSON.stringify(e)}),updateSession:(e,t)=>s("/sessions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteSession:e=>s("/sessions/".concat(e),{method:"DELETE"}),getCaisses:()=>s("/caisses"),getCaisse:e=>s("/caisses/".concat(e)),createCaisse:e=>s("/caisses",{method:"POST",body:JSON.stringify(e)}),updateCaisse:(e,t)=>s("/caisses/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteCaisse:e=>s("/caisses/".concat(e),{method:"DELETE"}),emargerCaisse:e=>s("/caisses/".concat(e,"/emarger"),{method:"POST"}),getReunions:()=>s("/reunions"),getReunion:e=>s("/reunions/".concat(e)),updateReunion:(e,t)=>s("/reunions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),getMembers:()=>s("/members"),getMember:e=>s("/members/".concat(e)),createMember:e=>s("/members",{method:"POST",body:JSON.stringify(e)}),updateMember:(e,t)=>s("/members/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteMember:e=>s("/members/".concat(e),{method:"DELETE"}),getMemberDebrief:(e,t)=>{let r=new URLSearchParams;(null==t?void 0:t.dateFrom)&&r.append("dateFrom",t.dateFrom),(null==t?void 0:t.dateTo)&&r.append("dateTo",t.dateTo),(null==t?void 0:t.sessionId)&&r.append("sessionId",t.sessionId);let n=r.toString()?"?".concat(r.toString()):"";return s("/members/".concat(e,"/debrief").concat(n))},createPayment:e=>s("/payments",{method:"POST",body:JSON.stringify(e)}),getSessionMembers:e=>s("/sessions/".concat(e,"/members")),addSessionMember:e=>s("/session-members",{method:"POST",body:JSON.stringify(e)}),removeSessionMember:(e,t)=>s("/sessions/".concat(e,"/members/").concat(t),{method:"DELETE"})}}},12058,e=>{"use strict";e.s(["CaisseType",()=>t,"PaymentDirection",()=>s,"PaymentFunction",()=>n,"UserRole",()=>r]);var t=function(e){return e.PRINCIPALE="PRINCIPALE",e.REUNION="REUNION",e}({}),r=function(e){return e.SECRETARY_GENERAL="secretary_general",e.CONTROLLER="controller",e.CASHIER="cashier",e}({}),s=function(e){return e.IN="IN",e.OUT="OUT",e}({}),n=function(e){return e.CONTRIBUTION="cotisation",e.TRANSFER="transfert",e.EXTERNAL="exterieur",e}({})},81787,e=>{"use strict";e.s(["Plus",()=>t],81787);let t=(0,e.i(44571).default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},24250,38508,e=>{"use strict";e.s(["TrendingUp",()=>r],24250);var t=e.i(44571);let r=(0,t.default)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);e.s(["TrendingDown",()=>s],38508);let s=(0,t.default)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},10756,e=>{"use strict";e.s(["default",()=>h]);var t=e.i(4051),r=e.i(38477),s=e.i(1269),n=e.i(81787),a=e.i(57223),i=e.i(24250),o=e.i(38508),c=e.i(5085),l=e.i(85205),d=e.i(75680),u=e.i(4467),m=e.i(12058);function h(){let{data:e}=(0,s.useSession)();(0,u.useApi)();let[h,f]=(0,r.useState)(null),[p,x]=(0,r.useState)(!0),g=(null==e?void 0:e.user)&&(e.user.role===m.UserRole.SECRETARY_GENERAL||e.user.role===m.UserRole.CONTROLLER||e.user.role===m.UserRole.CASHIER),y=(null==e?void 0:e.user)&&(e.user.role===m.UserRole.SECRETARY_GENERAL||e.user.role===m.UserRole.CONTROLLER||e.user.role===m.UserRole.CASHIER);(0,r.useEffect)(()=>{(null==e?void 0:e.accessToken)&&b()},[e]);let b=async()=>{try{x(!0),f({totalIn:0,totalOut:0,netAmount:0,contributionsTotal:0,transfersTotal:0,externalTotal:0})}catch(e){console.error("Erreur lors du chargement des statistiques:",e)}finally{x(!1)}},j=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"XAF"}).format(e);return g?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Gestion des Paiements"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Enregistrez et gérez les paiements de la tontine"})]}),y&&(0,t.jsx)(c.default,{href:"/dashboard/payments/new",children:(0,t.jsxs)(l.Button,{children:[(0,t.jsx)(n.Plus,{className:"h-4 w-4 mr-2"}),"Nouveau paiement"]})})]}),h&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsxs)(d.CardTitle,{className:"text-sm font-medium text-gray-600 flex items-center",children:[(0,t.jsx)(i.TrendingUp,{className:"h-4 w-4 mr-2 text-green-600"}),"Total Entrées"]})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:j(h.totalIn)})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsxs)(d.CardTitle,{className:"text-sm font-medium text-gray-600 flex items-center",children:[(0,t.jsx)(o.TrendingDown,{className:"h-4 w-4 mr-2 text-red-600"}),"Total Sorties"]})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:j(h.totalOut)})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsxs)(d.CardTitle,{className:"text-sm font-medium text-gray-600 flex items-center",children:[(0,t.jsx)(a.DollarSign,{className:"h-4 w-4 mr-2"}),"Solde Net"]})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold ".concat(h.netAmount>=0?"text-green-600":"text-red-600"),children:j(h.netAmount)})})]})]}),h&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Cotisations"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:j(h.contributionsTotal)})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Transferts"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:j(h.transfersTotal)})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Paiements Externes"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:j(h.externalTotal)})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsx)(d.Card,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,t.jsx)(c.default,{href:"/dashboard/payments/new?type=contribution",children:(0,t.jsxs)(d.CardHeader,{children:[(0,t.jsxs)(d.CardTitle,{className:"text-lg flex items-center",children:[(0,t.jsx)(i.TrendingUp,{className:"h-5 w-5 mr-2 text-green-600"}),"Enregistrer une cotisation"]}),(0,t.jsx)(d.CardDescription,{children:"Enregistrer le paiement d'une cotisation par un membre"})]})})}),(0,t.jsx)(d.Card,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,t.jsx)(c.default,{href:"/dashboard/payments/new?type=transfer",children:(0,t.jsxs)(d.CardHeader,{children:[(0,t.jsxs)(d.CardTitle,{className:"text-lg flex items-center",children:[(0,t.jsx)(a.DollarSign,{className:"h-5 w-5 mr-2 text-purple-600"}),"Effectuer un transfert"]}),(0,t.jsx)(d.CardDescription,{children:"Transférer des fonds entre caisses"})]})})}),(0,t.jsx)(d.Card,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,t.jsx)(c.default,{href:"/dashboard/payments/new?type=external",children:(0,t.jsxs)(d.CardHeader,{children:[(0,t.jsxs)(d.CardTitle,{className:"text-lg flex items-center",children:[(0,t.jsx)(o.TrendingDown,{className:"h-5 w-5 mr-2 text-orange-600"}),"Paiement externe"]}),(0,t.jsx)(d.CardDescription,{children:"Enregistrer un paiement externe (frais, achats, etc.)"})]})})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{children:(0,t.jsx)(d.CardTitle,{children:"Information"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,t.jsxs)("p",{children:["• ",(0,t.jsx)("strong",{children:"Cotisations :"})," Paiements des membres pour leurs parts dans la session"]}),(0,t.jsxs)("p",{children:["• ",(0,t.jsx)("strong",{children:"Transferts :"})," Mouvements de fonds entre caisses (principale ↔ réunion)"]}),(0,t.jsxs)("p",{children:["• ",(0,t.jsx)("strong",{children:"Paiements externes :"})," Sorties pour frais, achats ou autres dépenses"]})]})})]})]}):(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Accès refusé"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Vous n'avez pas les permissions pour accéder à cette page."})]})})}}]);