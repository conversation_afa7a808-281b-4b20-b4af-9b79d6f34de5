(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,44640,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ReflectAdapter",{enumerable:!0,get:function(){return s}});class s{static get(e,t,r){let s=Reflect.get(e,t,r);return"function"==typeof s?s.bind(e):s}static set(e,t,r,s){return Reflect.set(e,t,r,s)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},8356,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorageInstance",{enumerable:!0,get:function(){return s}});let s=(0,e.r(97918).createAsyncLocalStorage)()},62355,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorage",{enumerable:!0,get:function(){return s.afterTaskAsyncStorageInstance}});let s=e.r(8356)},17939,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{isRequestAPICallableInsideAfter:function(){return o},throwForSearchParamsAccessInUseCache:function(){return c},throwWithStaticGenerationBailoutError:function(){return a},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return i}});let s=e.r(85115),n=e.r(62355);function a(e,t){throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(e," couldn't be rendered statically because it used ").concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function i(e,t){throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(e,' with `dynamic = "error"` couldn\'t be rendered statically because it used ').concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function c(e,t){let r=Object.defineProperty(Error("Route ".concat(e.route,' used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(r,t),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=r),r}function o(){let e=n.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},93168,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{describeHasCheckingStringProperty:function(){return a},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return i}});let s=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return s.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function a(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let i=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},63470,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return o}});let s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var c=a?Object.getOwnPropertyDescriptor(e,i):null;c&&(c.get||c.set)?Object.defineProperty(s,i,c):s[i]=e[i]}return s.default=e,r&&r.set(e,s),s}(e.r(38477));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}let a={current:null},i="function"==typeof s.cache?s.cache:e=>e,c=console.warn;function o(e){return function(){for(var t=arguments.length,r=Array(t),s=0;s<t;s++)r[s]=arguments[s];c(e(...r))}}i(e=>{try{c(a.current)}finally{a.current=null}})},4467,e=>{"use strict";e.s(["useApi",()=>s]);var t=e.i(1269),r=e.i(1831);function s(){let{data:e}=(0,t.useSession)(),s=async function(t){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(null==e?void 0:e.accessToken))throw Error("Non authentifié");return r.apiService.authenticatedRequest(t,e.accessToken,s)};return{login:r.apiService.login.bind(r.apiService),register:r.apiService.register.bind(r.apiService),authenticatedRequest:s,getUsers:()=>s("/users"),getUser:e=>s("/users/".concat(e)),createUser:e=>s("/users",{method:"POST",body:JSON.stringify(e)}),updateUser:(e,t)=>s("/users/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteUser:e=>s("/users/".concat(e),{method:"DELETE"}),getSessions:()=>s("/sessions"),getSession:e=>s("/sessions/".concat(e)),createSession:e=>s("/sessions",{method:"POST",body:JSON.stringify(e)}),updateSession:(e,t)=>s("/sessions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteSession:e=>s("/sessions/".concat(e),{method:"DELETE"}),getCaisses:()=>s("/caisses"),getCaisse:e=>s("/caisses/".concat(e)),createCaisse:e=>s("/caisses",{method:"POST",body:JSON.stringify(e)}),updateCaisse:(e,t)=>s("/caisses/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteCaisse:e=>s("/caisses/".concat(e),{method:"DELETE"}),emargerCaisse:e=>s("/caisses/".concat(e,"/emarger"),{method:"POST"}),getReunions:()=>s("/reunions"),getReunion:e=>s("/reunions/".concat(e)),updateReunion:(e,t)=>s("/reunions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),getMembers:()=>s("/members"),getMember:e=>s("/members/".concat(e)),createMember:e=>s("/members",{method:"POST",body:JSON.stringify(e)}),updateMember:(e,t)=>s("/members/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteMember:e=>s("/members/".concat(e),{method:"DELETE"}),getMemberDebrief:(e,t)=>{let r=new URLSearchParams;(null==t?void 0:t.dateFrom)&&r.append("dateFrom",t.dateFrom),(null==t?void 0:t.dateTo)&&r.append("dateTo",t.dateTo),(null==t?void 0:t.sessionId)&&r.append("sessionId",t.sessionId);let n=r.toString()?"?".concat(r.toString()):"";return s("/members/".concat(e,"/debrief").concat(n))},createPayment:e=>s("/payments",{method:"POST",body:JSON.stringify(e)}),getSessionMembers:e=>s("/sessions/".concat(e,"/members")),addSessionMember:e=>s("/session-members",{method:"POST",body:JSON.stringify(e)}),removeSessionMember:(e,t)=>s("/sessions/".concat(e,"/members/").concat(t),{method:"DELETE"})}}},12058,e=>{"use strict";e.s(["CaisseType",()=>t,"PaymentDirection",()=>s,"PaymentFunction",()=>n,"UserRole",()=>r]);var t=function(e){return e.PRINCIPALE="PRINCIPALE",e.REUNION="REUNION",e}({}),r=function(e){return e.SECRETARY_GENERAL="secretary_general",e.CONTROLLER="controller",e.CASHIER="cashier",e}({}),s=function(e){return e.IN="IN",e.OUT="OUT",e}({}),n=function(e){return e.CONTRIBUTION="cotisation",e.TRANSFER="transfert",e.EXTERNAL="exterieur",e}({})},14545,e=>{"use strict";e.s(["ArrowLeft",()=>t],14545);let t=(0,e.i(44571).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},41850,e=>{"use strict";e.s(["Table",()=>s,"TableBody",()=>a,"TableCell",()=>o,"TableHead",()=>c,"TableHeader",()=>n,"TableRow",()=>i]);var t=e.i(4051),r=e.i(41428);function s(e){let{className:s,...n}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",s),...n})})}function n(e){let{className:s,...n}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",s),...n})}function a(e){let{className:s,...n}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",s),...n})}function i(e){let{className:s,...n}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...n})}function c(e){let{className:s,...n}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...n})}function o(e){let{className:s,...n}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...n})}},2470,e=>{"use strict";e.s(["Badge",()=>i]);var t=e.i(4051),r=e.i(81221),s=e.i(62244),n=e.i(41428);let a=(0,s.cva)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:s,variant:i,asChild:c=!1,...o}=e,l=c?r.Slot:"span";return(0,t.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(a({variant:i}),s),...o})}},24250,38508,e=>{"use strict";e.s(["TrendingUp",()=>r],24250);var t=e.i(44571);let r=(0,t.default)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);e.s(["TrendingDown",()=>s],38508);let s=(0,t.default)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},53917,80712,23738,e=>{"use strict";e.s(["Mail",()=>r],53917);var t=e.i(44571);let r=(0,t.default)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);e.s(["Phone",()=>s],80712);let s=(0,t.default)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);e.s(["MapPin",()=>n],23738);let n=(0,t.default)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},62131,e=>{"use strict";e.s(["default",()=>p]);var t=e.i(4051),r=e.i(38477),s=e.i(57691),n=e.i(1269),a=e.i(14545),i=e.i(53917),c=e.i(80712),o=e.i(23738),l=e.i(24250),d=e.i(38508),u=e.i(5085),m=e.i(85205),h=e.i(75680),f=e.i(41850),x=e.i(2470),b=e.i(4467),g=e.i(12058);function p(){var e,p,y,j;let v=(0,s.useParams)();(0,s.useRouter)();let{data:N,status:T}=(0,n.useSession)(),S=(0,b.useApi)(),[C,E]=(0,r.useState)(null),[O,P]=(0,r.useState)(null),[R,w]=(0,r.useState)(!0),[A,_]=(0,r.useState)(null),M=v.id,k=(null==N?void 0:N.user)&&(N.user.role===g.UserRole.SECRETARY_GENERAL||N.user.role===g.UserRole.CONTROLLER);(0,r.useEffect)(()=>{(null==N?void 0:N.accessToken)&&M&&I()},[T,M]);let I=async()=>{try{w(!0),_(null);let[e,t]=await Promise.all([S.getMember(M),S.getMemberDebrief(M)]);E(e),P(t)}catch(e){console.error("Erreur lors du chargement:",e),_("Erreur lors du chargement des données")}finally{w(!1)}},L=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"XAF"}).format(e);return k?R?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsx)(u.default,{href:"/dashboard/members",children:(0,t.jsxs)(m.Button,{variant:"ghost",size:"sm",children:[(0,t.jsx)(a.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,t.jsx)("div",{className:"flex justify-center py-8",children:(0,t.jsx)("div",{className:"text-gray-500",children:"Chargement..."})})]}):A||!C?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsx)(u.default,{href:"/dashboard/members",children:(0,t.jsxs)(m.Button,{variant:"ghost",size:"sm",children:[(0,t.jsx)(a.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Erreur"}),(0,t.jsx)("p",{className:"text-gray-600",children:A||"Membre introuvable"})]})})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(u.default,{href:"/dashboard/members",children:(0,t.jsxs)(m.Button,{variant:"ghost",size:"sm",children:[(0,t.jsx)(a.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:[C.firstName," ",C.lastName]}),(0,t.jsx)("p",{className:"text-gray-600",children:"Détails du membre"})]})]}),(0,t.jsx)(u.default,{href:"/dashboard/members/".concat(C._id,"/edit"),children:(0,t.jsx)(m.Button,{children:"Modifier"})})]}),(0,t.jsxs)(h.Card,{children:[(0,t.jsx)(h.CardHeader,{children:(0,t.jsx)(h.CardTitle,{children:"Informations personnelles"})}),(0,t.jsx)(h.CardContent,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Nom complet"}),(0,t.jsxs)("p",{className:"text-gray-600",children:[C.firstName," ",C.lastName]})]}),C.email&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Email"}),(0,t.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,t.jsx)(i.Mail,{className:"h-4 w-4 mr-2"}),C.email]})]}),C.phone&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Téléphone"}),(0,t.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,t.jsx)(c.Phone,{className:"h-4 w-4 mr-2"}),C.phone]})]}),C.address&&(0,t.jsxs)("div",{className:"md:col-span-3",children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Adresse"}),(0,t.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,t.jsx)(o.MapPin,{className:"h-4 w-4 mr-2"}),C.address]})]})]})})]}),O&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)(h.Card,{children:[(0,t.jsx)(h.CardHeader,{className:"pb-2",children:(0,t.jsx)(h.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Total Entrées"})}),(0,t.jsx)(h.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:L(O.totalIn)})})]}),(0,t.jsxs)(h.Card,{children:[(0,t.jsx)(h.CardHeader,{className:"pb-2",children:(0,t.jsx)(h.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Total Sorties"})}),(0,t.jsx)(h.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:L(O.totalOut)})})]}),(0,t.jsxs)(h.Card,{children:[(0,t.jsx)(h.CardHeader,{className:"pb-2",children:(0,t.jsx)(h.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Solde Net"})}),(0,t.jsx)(h.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold ".concat(O.netAmount>=0?"text-green-600":"text-red-600"),children:L(null!=(y=O.netAmount)?y:0)})})]}),(0,t.jsxs)(h.Card,{children:[(0,t.jsx)(h.CardHeader,{className:"pb-2",children:(0,t.jsx)(h.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Cotisations"})}),(0,t.jsx)(h.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:L(null!=(j=O.contributionsTotal)?j:0)})})]})]}),(0,t.jsxs)(h.Card,{children:[(0,t.jsxs)(h.CardHeader,{children:[(0,t.jsx)(h.CardTitle,{children:"Historique des paiements"}),(0,t.jsxs)(h.CardDescription,{children:[null==(e=O.payments)?void 0:e.length," paiement(s) enregistré(s)"]})]}),(0,t.jsx)(h.CardContent,{children:(null==(p=O.payments)?void 0:p.length)>0?(0,t.jsxs)(f.Table,{children:[(0,t.jsx)(f.TableHeader,{children:(0,t.jsxs)(f.TableRow,{children:[(0,t.jsx)(f.TableHead,{children:"Date"}),(0,t.jsx)(f.TableHead,{children:"Type"}),(0,t.jsx)(f.TableHead,{children:"Fonction"}),(0,t.jsx)(f.TableHead,{children:"Montant"})]})}),(0,t.jsx)(f.TableBody,{children:O.payments.map(e=>(0,t.jsxs)(f.TableRow,{children:[(0,t.jsx)(f.TableCell,{children:new Date(e.date).toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric"})}),(0,t.jsx)(f.TableCell,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[e.direction===g.PaymentDirection.IN?(0,t.jsx)(l.TrendingUp,{className:"h-4 w-4 text-green-600"}):(0,t.jsx)(d.TrendingDown,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"ml-2",children:e.direction===g.PaymentDirection.IN?"Entrée":"Sortie"})]})}),(0,t.jsx)(f.TableCell,{children:(e=>{let r={[g.PaymentFunction.CONTRIBUTION]:"default",[g.PaymentFunction.TRANSFER]:"secondary",[g.PaymentFunction.EXTERNAL]:"outline"},s={[g.PaymentFunction.CONTRIBUTION]:"Cotisation",[g.PaymentFunction.TRANSFER]:"Transfert",[g.PaymentFunction.EXTERNAL]:"Extérieur"};return(0,t.jsx)(x.Badge,{variant:r[e],children:s[e]})})(e.func)}),(0,t.jsx)(f.TableCell,{children:(0,t.jsx)("span",{className:e.direction===g.PaymentDirection.IN?"text-green-600":"text-red-600",children:L(e.amount)})})]},e._id))})]}):(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Aucun paiement enregistré"})})]})]})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsx)(u.default,{href:"/dashboard/members",children:(0,t.jsxs)(m.Button,{variant:"ghost",size:"sm",children:[(0,t.jsx)(a.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Accès refusé"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Vous n'avez pas les permissions pour accéder à cette page."})]})})]})}}]);