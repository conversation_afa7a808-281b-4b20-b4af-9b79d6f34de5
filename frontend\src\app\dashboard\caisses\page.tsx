"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import {
	Plus,
	Search,
	Filter,
	MoreHorizontal,
	Edit,
	Trash2,
	Wallet,
	DollarSign,
	ArrowUpRight,
	Building,
	Users,
} from "lucide-react";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useApi } from "@/hooks/use-api";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>se<PERSON>tats, CaisseType } from "@/types";

export default function CaissesPage() {
	const { data: session } = useSession();
	const api = useApi();

	const [caisses, setCaisses] = useState<Caisse[]>([]);
	const [stats, setStats] = useState<CaisseStats | null>(null);
	const [loading, setLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [typeFilter, setTypeFilter] = useState<string>("all");

	// Vérifier les permissions
	const canManageCaisses =
		session?.user &&
		((session.user as any).role === "secretary_general" ||
			(session.user as any).role === "controller" ||
			(session.user as any).role === "cashier");
	const canCreateCaisses =
		session?.user && (session.user as any).role === "secretary_general";
	const canEmarger =
		session?.user && (session.user as any).role === "cashier";

	const loadData = async () => {
		try {
			setLoading(true);
			const caissesData = await api.getCaisses();
			setCaisses(caissesData);

			// Calculer les statistiques
			const principales = caissesData.filter(
				(c) => c.type === CaisseType.PRINCIPALE
			);
			const reunions = caissesData.filter(
				(c) => c.type === CaisseType.REUNION
			);
			const soldeTotal = caissesData.reduce(
				(sum, c) => sum + c.soldeActuel,
				0
			);
			const soldePrincipales = principales.reduce(
				(sum, c) => sum + c.soldeActuel,
				0
			);
			const soldeReunions = reunions.reduce(
				(sum, c) => sum + c.soldeActuel,
				0
			);

			setStats({
				total: caissesData.length,
				principales: principales.length,
				reunions: reunions.length,
				soldeTotal,
				soldePrincipales,
				soldeReunions,
			});
		} catch (error) {
			console.error("Erreur lors du chargement des caisses:", error);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (session?.accessToken) {
			loadData();
		}
	}, [session]);

	const handleDelete = async (caisseId: string) => {
		if (!confirm("Êtes-vous sûr de vouloir supprimer cette caisse ?")) {
			return;
		}

		try {
			await api.deleteCaisse(caisseId);
			loadData();
		} catch (error) {
			console.error("Erreur lors de la suppression:", error);
		}
	};

	const handleEmarger = async (caisseId: string) => {
		if (
			!confirm(
				"Êtes-vous sûr de vouloir émarger cette caisse ? Le solde sera transféré vers la caisse principale."
			)
		) {
			return;
		}

		try {
			await api.emargerCaisse(caisseId);
			loadData();
		} catch (error) {
			console.error("Erreur lors de l'émargement:", error);
		}
	};

	// Filtrer les caisses
	const filteredCaisses = caisses.filter((caisse) => {
		const matchesSearch =
			caisse.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
			caisse.soldeActuel.toString().includes(searchTerm);

		const matchesType =
			typeFilter === "all" || caisse.type === typeFilter;

		return matchesSearch && matchesType;
	});

	const getTypeVariant = (type: CaisseType) => {
		switch (type) {
			case CaisseType.PRINCIPALE:
				return "default" as const;
			case CaisseType.REUNION:
				return "secondary" as const;
			default:
				return "outline" as const;
		}
	};

	const getTypeLabel = (type: CaisseType) => {
		switch (type) {
			case CaisseType.PRINCIPALE:
				return "Principale";
			case CaisseType.REUNION:
				return "Réunion";
			default:
				return type;
		}
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
					<p className="mt-2 text-sm text-gray-600">
						Chargement des caisses...
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-3xl font-bold tracking-tight">Caisses</h1>
					<p className="text-muted-foreground">
						Gérez les caisses principales et de réunion
					</p>
				</div>
				{canCreateCaisses && (
					<Button asChild>
						<Link href="/dashboard/caisses/new">
							<Plus className="mr-2 h-4 w-4" />
							Nouvelle caisse
						</Link>
					</Button>
				)}
			</div>

			{/* Statistiques */}
			{stats && (
				<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Total Caisses
							</CardTitle>
							<Wallet className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{stats.total}</div>
							<p className="text-xs text-muted-foreground">
								{stats.principales} principales, {stats.reunions} réunions
							</p>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Solde Total
							</CardTitle>
							<DollarSign className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{stats.soldeTotal.toLocaleString()} FCFA
							</div>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Caisses Principales
							</CardTitle>
							<Building className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{stats.soldePrincipales.toLocaleString()} FCFA
							</div>
							<p className="text-xs text-muted-foreground">
								{stats.principales} caisses
							</p>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Caisses Réunions
							</CardTitle>
							<Users className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{stats.soldeReunions.toLocaleString()} FCFA
							</div>
							<p className="text-xs text-muted-foreground">
								{stats.reunions} caisses
							</p>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Filtres */}
			<Card>
				<CardHeader>
					<CardTitle>Filtres</CardTitle>
					<CardDescription>
						Recherchez et filtrez les caisses
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="flex gap-4">
						<div className="flex-1">
							<div className="relative">
								<Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
								<Input
									placeholder="Rechercher par nom ou solde..."
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className="pl-8"
								/>
							</div>
						</div>
						<Select value={typeFilter} onValueChange={setTypeFilter}>
							<SelectTrigger className="w-[180px]">
								<SelectValue placeholder="Filtrer par type" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">Tous les types</SelectItem>
								<SelectItem value={CaisseType.PRINCIPALE}>
									Principales
								</SelectItem>
								<SelectItem value={CaisseType.REUNION}>Réunions</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</CardContent>
			</Card>

			{/* Tableau des caisses */}
			<Card>
				<CardHeader>
					<CardTitle>Caisses ({filteredCaisses.length})</CardTitle>
				</CardHeader>
				<CardContent>
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Nom</TableHead>
								<TableHead>Type</TableHead>
								<TableHead>Solde Actuel</TableHead>
								<TableHead>Créée le</TableHead>
								{canManageCaisses && <TableHead>Actions</TableHead>}
							</TableRow>
						</TableHeader>
						<TableBody>
							{filteredCaisses.map((caisse) => (
								<TableRow key={caisse._id}>
									<TableCell className="font-medium">
										{caisse.nom}
									</TableCell>
									<TableCell>
										<Badge variant={getTypeVariant(caisse.type)}>
											{getTypeLabel(caisse.type)}
										</Badge>
									</TableCell>
									<TableCell>
										<span
											className={
												caisse.soldeActuel > 0
													? "text-green-600 font-medium"
													: "text-gray-500"
											}
										>
											{caisse.soldeActuel.toLocaleString()} FCFA
										</span>
									</TableCell>
									<TableCell>
										{new Date(caisse.createdAt).toLocaleDateString()}
									</TableCell>
									{canManageCaisses && (
										<TableCell>
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button variant="ghost" className="h-8 w-8 p-0">
														<MoreHorizontal className="h-4 w-4" />
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align="end">
													<DropdownMenuItem asChild>
														<Link
															href={`/dashboard/caisses/${caisse._id}/edit`}
														>
															<Edit className="mr-2 h-4 w-4" />
															Modifier
														</Link>
													</DropdownMenuItem>
													{canEmarger &&
														caisse.type === CaisseType.REUNION &&
														caisse.soldeActuel > 0 && (
															<DropdownMenuItem
																onClick={() => handleEmarger(caisse._id)}
															>
																<ArrowUpRight className="mr-2 h-4 w-4" />
																Émarger
															</DropdownMenuItem>
														)}
													{canCreateCaisses && (
														<DropdownMenuItem
															onClick={() => handleDelete(caisse._id)}
															className="text-red-600"
														>
															<Trash2 className="mr-2 h-4 w-4" />
															Supprimer
														</DropdownMenuItem>
													)}
												</DropdownMenuContent>
											</DropdownMenu>
										</TableCell>
									)}
								</TableRow>
							))}
						</TableBody>
					</Table>
					{filteredCaisses.length === 0 && (
						<div className="text-center py-8">
							<p className="text-muted-foreground">Aucune caisse trouvée</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
