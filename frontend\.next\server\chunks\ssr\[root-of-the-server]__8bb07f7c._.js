module.exports=[24725,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},62303,a=>{"use strict";a.s(["DollarSign",()=>b],62303);let b=(0,a.i(621).default)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},12594,a=>{"use strict";a.s(["useApi",()=>d]);var b=a.i(81223),c=a.i(34075);function d(){let{data:a}=(0,b.useSession)(),d=async(b,d={})=>{if(!a?.accessToken)throw Error("Non authentifié");return c.apiService.authenticatedRequest(b,a.accessToken,d)};return{login:c.apiService.login.bind(c.apiService),register:c.apiService.register.bind(c.apiService),authenticatedRequest:d,getUsers:()=>d("/users"),getUser:a=>d(`/users/${a}`),createUser:a=>d("/users",{method:"POST",body:JSON.stringify(a)}),updateUser:(a,b)=>d(`/users/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteUser:a=>d(`/users/${a}`,{method:"DELETE"}),getSessions:()=>d("/sessions"),getSession:a=>d(`/sessions/${a}`),createSession:a=>d("/sessions",{method:"POST",body:JSON.stringify(a)}),updateSession:(a,b)=>d(`/sessions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteSession:a=>d(`/sessions/${a}`,{method:"DELETE"}),getCaisses:()=>d("/caisses"),getCaisse:a=>d(`/caisses/${a}`),createCaisse:a=>d("/caisses",{method:"POST",body:JSON.stringify(a)}),updateCaisse:(a,b)=>d(`/caisses/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteCaisse:a=>d(`/caisses/${a}`,{method:"DELETE"}),emargerCaisse:a=>d(`/caisses/${a}/emarger`,{method:"POST"}),getReunions:()=>d("/reunions"),getReunion:a=>d(`/reunions/${a}`),updateReunion:(a,b)=>d(`/reunions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),getMembers:()=>d("/members"),getMember:a=>d(`/members/${a}`),createMember:a=>d("/members",{method:"POST",body:JSON.stringify(a)}),updateMember:(a,b)=>d(`/members/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteMember:a=>d(`/members/${a}`,{method:"DELETE"}),getMemberDebrief:(a,b)=>{let c=new URLSearchParams;b?.dateFrom&&c.append("dateFrom",b.dateFrom),b?.dateTo&&c.append("dateTo",b.dateTo),b?.sessionId&&c.append("sessionId",b.sessionId);let e=c.toString()?`?${c.toString()}`:"";return d(`/members/${a}/debrief${e}`)},createPayment:a=>d("/payments",{method:"POST",body:JSON.stringify(a)}),getSessionMembers:a=>d(`/sessions/${a}/members`),addSessionMember:a=>d("/session-members",{method:"POST",body:JSON.stringify(a)}),removeSessionMember:(a,b)=>d(`/sessions/${a}/members/${b}`,{method:"DELETE"})}}},78655,a=>{"use strict";a.s(["CaisseType",()=>b,"PaymentDirection",()=>d,"PaymentFunction",()=>e,"UserRole",()=>c]);var b=function(a){return a.PRINCIPALE="PRINCIPALE",a.REUNION="REUNION",a}({}),c=function(a){return a.SECRETARY_GENERAL="secretary_general",a.CONTROLLER="controller",a.CASHIER="cashier",a}({}),d=function(a){return a.IN="IN",a.OUT="OUT",a}({}),e=function(a){return a.CONTRIBUTION="cotisation",a.TRANSFER="transfert",a.EXTERNAL="exterieur",a}({})},91486,a=>{"use strict";a.s(["Plus",()=>b],91486);let b=(0,a.i(621).default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},53391,63482,a=>{"use strict";a.s(["TrendingUp",()=>c],53391);var b=a.i(621);let c=(0,b.default)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);a.s(["TrendingDown",()=>d],63482);let d=(0,b.default)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},50999,a=>{"use strict";a.s(["default",()=>n]);var b=a.i(68116),c=a.i(128),d=a.i(81223),e=a.i(91486),f=a.i(62303),g=a.i(53391),h=a.i(63482),i=a.i(33055),j=a.i(2979),k=a.i(75780),l=a.i(12594),m=a.i(78655);function n(){let{data:a}=(0,d.useSession)();(0,l.useApi)();let[n,o]=(0,c.useState)(null),[p,q]=(0,c.useState)(!0),r=a?.user&&(a.user.role===m.UserRole.SECRETARY_GENERAL||a.user.role===m.UserRole.CONTROLLER||a.user.role===m.UserRole.CASHIER),s=a?.user&&(a.user.role===m.UserRole.SECRETARY_GENERAL||a.user.role===m.UserRole.CONTROLLER||a.user.role===m.UserRole.CASHIER);(0,c.useEffect)(()=>{a?.accessToken&&t()},[a]);let t=async()=>{try{q(!0),o({totalIn:0,totalOut:0,netAmount:0,contributionsTotal:0,transfersTotal:0,externalTotal:0})}catch(a){console.error("Erreur lors du chargement des statistiques:",a)}finally{q(!1)}},u=a=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"XAF"}).format(a);return r?(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Gestion des Paiements"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Enregistrez et gérez les paiements de la tontine"})]}),s&&(0,b.jsx)(i.default,{href:"/dashboard/payments/new",children:(0,b.jsxs)(j.Button,{children:[(0,b.jsx)(e.Plus,{className:"h-4 w-4 mr-2"}),"Nouveau paiement"]})})]}),n&&(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{className:"pb-2",children:(0,b.jsxs)(k.CardTitle,{className:"text-sm font-medium text-gray-600 flex items-center",children:[(0,b.jsx)(g.TrendingUp,{className:"h-4 w-4 mr-2 text-green-600"}),"Total Entrées"]})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-green-600",children:u(n.totalIn)})})]}),(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{className:"pb-2",children:(0,b.jsxs)(k.CardTitle,{className:"text-sm font-medium text-gray-600 flex items-center",children:[(0,b.jsx)(h.TrendingDown,{className:"h-4 w-4 mr-2 text-red-600"}),"Total Sorties"]})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-red-600",children:u(n.totalOut)})})]}),(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{className:"pb-2",children:(0,b.jsxs)(k.CardTitle,{className:"text-sm font-medium text-gray-600 flex items-center",children:[(0,b.jsx)(f.DollarSign,{className:"h-4 w-4 mr-2"}),"Solde Net"]})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsx)("div",{className:`text-2xl font-bold ${n.netAmount>=0?"text-green-600":"text-red-600"}`,children:u(n.netAmount)})})]})]}),n&&(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{className:"pb-2",children:(0,b.jsx)(k.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Cotisations"})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:u(n.contributionsTotal)})})]}),(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{className:"pb-2",children:(0,b.jsx)(k.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Transferts"})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:u(n.transfersTotal)})})]}),(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{className:"pb-2",children:(0,b.jsx)(k.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Paiements Externes"})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:u(n.externalTotal)})})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,b.jsx)(k.Card,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,b.jsx)(i.default,{href:"/dashboard/payments/new?type=contribution",children:(0,b.jsxs)(k.CardHeader,{children:[(0,b.jsxs)(k.CardTitle,{className:"text-lg flex items-center",children:[(0,b.jsx)(g.TrendingUp,{className:"h-5 w-5 mr-2 text-green-600"}),"Enregistrer une cotisation"]}),(0,b.jsx)(k.CardDescription,{children:"Enregistrer le paiement d'une cotisation par un membre"})]})})}),(0,b.jsx)(k.Card,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,b.jsx)(i.default,{href:"/dashboard/payments/new?type=transfer",children:(0,b.jsxs)(k.CardHeader,{children:[(0,b.jsxs)(k.CardTitle,{className:"text-lg flex items-center",children:[(0,b.jsx)(f.DollarSign,{className:"h-5 w-5 mr-2 text-purple-600"}),"Effectuer un transfert"]}),(0,b.jsx)(k.CardDescription,{children:"Transférer des fonds entre caisses"})]})})}),(0,b.jsx)(k.Card,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,b.jsx)(i.default,{href:"/dashboard/payments/new?type=external",children:(0,b.jsxs)(k.CardHeader,{children:[(0,b.jsxs)(k.CardTitle,{className:"text-lg flex items-center",children:[(0,b.jsx)(h.TrendingDown,{className:"h-5 w-5 mr-2 text-orange-600"}),"Paiement externe"]}),(0,b.jsx)(k.CardDescription,{children:"Enregistrer un paiement externe (frais, achats, etc.)"})]})})})]}),(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{children:(0,b.jsx)(k.CardTitle,{children:"Information"})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,b.jsxs)("p",{children:["• ",(0,b.jsx)("strong",{children:"Cotisations :"})," Paiements des membres pour leurs parts dans la session"]}),(0,b.jsxs)("p",{children:["• ",(0,b.jsx)("strong",{children:"Transferts :"})," Mouvements de fonds entre caisses (principale ↔ réunion)"]}),(0,b.jsxs)("p",{children:["• ",(0,b.jsx)("strong",{children:"Paiements externes :"})," Sorties pour frais, achats ou autres dépenses"]})]})})]})]}):(0,b.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Accès refusé"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Vous n'avez pas les permissions pour accéder à cette page."})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__8bb07f7c._.js.map