import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

// Order emitted by SECRETARY_GENERAL for cash out (for reunion caisse, cashier must select one)
export type ExitOrderDocument = HydratedDocument<ExitOrder>;

@Schema({ timestamps: true })
export class ExitOrder {
  @Prop({ type: Types.ObjectId, ref: 'Caisse', required: true })
  caisseId!: Types.ObjectId;

  @Prop({ type: Number, required: true, min: 0 })
  amount!: number;

  // optional reason, mandatory for principal caisse in business rule (validated in service)
  @Prop({ type: String, required: false })
  reason?: string;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  createdBy!: Types.ObjectId;

  // open/used flag
  @Prop({ type: Boolean, required: true, default: true })
  open!: boolean;
}

export const ExitOrderSchema = SchemaFactory.createForClass(ExitOrder);