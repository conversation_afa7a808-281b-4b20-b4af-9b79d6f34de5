import 'reflect-metadata';
import { Test } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { PaymentsController } from '../src/payments/payments.controller';
import { PaymentsService } from '../src/payments/payments.service';
import { getModelToken } from '@nestjs/mongoose';
import { Caisse } from '../src/caisses/schemas/caisse.schema';
import { AppTestingModule } from './app-testing.module';

const paymentsServiceMock = { create: jest.fn().mockResolvedValue({ _id: 'p1' }) };

// Use valid ObjectId-like strings for Mongo ObjectId conversion
const CAISSE_ID = '64b7a1f2a5f0c9b1d2e3f456';

const caisseModelMock = {
  findById: jest.fn().mockResolvedValue({ _id: CAISSE_ID, cashierId: 'test-user-id', type: 'REUNION', status: 'OPEN' }),
};

describe('PaymentsController', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      controllers: [PaymentsController],
      providers: [
        { provide: PaymentsService, useValue: paymentsServiceMock },
        { provide: getModelToken(Caisse.name), useValue: caisseModelMock },
      ],
    })
      .overrideGuard(require('../src/auth/jwt-auth.guard').JwtAuthGuard)
      .useClass(require('./utils/mock-guards').MockJwtAuthGuard)
      .overrideGuard(require('../src/common/guards/role.guard').RolesGuard)
      .useClass(require('./utils/mock-guards').MockRolesGuard)
      .compile();

    app = moduleRef.createNestApplication();
    await app.init();
  });

  afterAll(async () => { await app.close(); });

  it('POST /payments success for cashier on own caisse', async () => {
    await request(app.getHttpServer())
      .post('/payments')
      .send({ caisseId: CAISSE_ID, amount: 1000 })
      .set('x-user-role', 'cashier')
      .set('x-user-id', 'test-user-id')
      .expect(201);
  });

  it('POST /payments forbidden for cashier on other caisse', async () => {
    const OTHER_CAISSE_ID = '64b7a1f2a5f0c9b1d2e3f457';
    caisseModelMock.findById.mockResolvedValueOnce({ _id: OTHER_CAISSE_ID, cashierId: 'other', type: 'REUNION', status: 'OPEN' });
    await request(app.getHttpServer())
      .post('/payments')
      .send({ caisseId: OTHER_CAISSE_ID, amount: 1000 })
      .set('x-user-role', 'cashier')
      .set('x-user-id', 'test-user-id')
      .expect(403);
  });

  it('POST /payments success for secretary', async () => {
    await request(app.getHttpServer())
      .post('/payments')
      .send({ caisseId: CAISSE_ID, amount: 1000 })
      .set('x-user-role', 'secretary_general')
      .expect(201);
  });
});