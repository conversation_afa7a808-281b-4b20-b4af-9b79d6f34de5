# Analyse des Rôles et Permissions - Backend vs Frontend

## Rôles Utilisateurs

### Backend (Valeurs enum)
```typescript
export enum UserRole {
  CASHIER = 'cashier',
  CONTROLLER = 'controller', 
  SECRETARY_GENERAL = 'secretary_general',
}
```

### Frontend (Aligné)
```typescript
export enum UserRole {
  CASHIER = 'cashier',
  CONTROLLER = 'controller',
  SECRETARY_GENERAL = 'secretary_general',
}
```

## Autorisations Backend par Module

### 1. **Members** (`/members`)
- **Créer** (`POST /members`) : `SECRETARY_GENERAL` uniquement
- **Lister** (`GET /members`) : `SECRETARY_GENERAL`, `CONTROLLER`
- **Détail** (`GET /members/:id`) : `SECRETARY_GENERAL`, `CONTROLLER`
- **Modifier** (`PATCH /members/:id`) : `SECRETARY_GENERAL` uniquement
- **Supprimer** (`DELETE /members/:id`) : `SECRETARY_GENERAL` uniquement
- **Debrief** (`GET /members/:id/debrief`) : `SECRETARY_GENERAL`, `CONTROLLER`

### 2. **Sessions** (`/sessions`)
- **Créer** (`POST /sessions`) : `SECRETARY_GENERAL` uniquement
- **Lister** (`GET /sessions`) : `SECRETARY_GENERAL`, `CONTROLLER`, `CASHIER`
- **Détail** (`GET /sessions/:id`) : `SECRETARY_GENERAL`, `CONTROLLER`, `CASHIER`
- **Modifier** (`PATCH /sessions/:id`) : `SECRETARY_GENERAL` uniquement
- **Supprimer** (`DELETE /sessions/:id`) : `SECRETARY_GENERAL` uniquement
- **Ajouter membre** (`POST /sessions/:id/members`) : `SECRETARY_GENERAL` uniquement
- **Debrief paiements** (`GET /sessions/:id/payments-debrief`) : `SECRETARY_GENERAL`, `CONTROLLER`

### 3. **Caisses** (`/caisses`)
- **Créer** (`POST /caisses`) : `SECRETARY_GENERAL` uniquement
- **Lister** (`GET /caisses`) : `SECRETARY_GENERAL`, `CONTROLLER`, `CASHIER`
- **Détail** (`GET /caisses/:id`) : `SECRETARY_GENERAL`, `CONTROLLER`, `CASHIER`
- **Modifier** (`PATCH /caisses/:id`) : `SECRETARY_GENERAL` uniquement
- **Supprimer** (`DELETE /caisses/:id`) : `SECRETARY_GENERAL` uniquement
- **Ouvrir caisse** (`POST /caisses/:id/open`) : `CASHIER` uniquement (sa caisse assignée)
- **Fermer caisse** (`POST /caisses/:id/close`) : `SECRETARY_GENERAL`, `CONTROLLER`
- **Ordres de sortie** (`GET /caisses/:id/exit-orders`) : `SECRETARY_GENERAL`, `CONTROLLER`
- **Mes ordres** (`GET /caisses/:id/my-exit-orders`) : `CASHIER` uniquement
- **Cotisation** (`POST /caisses/:id/cotiser`) : `CASHIER` uniquement
- **Prochaine cotisation** (`GET /caisses/:id/next-contribution/:sessionId/:memberId`) : `SECRETARY_GENERAL`, `CONTROLLER`, `CASHIER`

### 4. **Payments** (`/payments`)
- **Créer** (`POST /payments`) : `CASHIER`, `SECRETARY_GENERAL`, `CONTROLLER`
  - **Règle spéciale** : `CASHIER` ne peut créer que sur sa caisse assignée

### 5. **Reunions** (`/reunions`)
- **Lister** (`GET /reunions`) : `SECRETARY_GENERAL`, `CONTROLLER`, `CASHIER`
- **Détail** (`GET /reunions/:id`) : `SECRETARY_GENERAL`, `CONTROLLER`, `CASHIER`
- **Paiements attendus** (`GET /reunions/:id/expected-payments`) : `SECRETARY_GENERAL`, `CONTROLLER`, `CASHIER`
- **Debrief** (`GET /reunions/:id/debrief`) : `SECRETARY_GENERAL`, `CONTROLLER`
- **État** (`GET /reunions/:id/state`) : `SECRETARY_GENERAL`, `CONTROLLER`, `CASHIER`
- **Modifier** (`PATCH /reunions/:id`) : `SECRETARY_GENERAL` uniquement

## Corrections Frontend Appliquées

### ✅ **Pages Corrigées**

#### 1. **Dashboard Principal** (`/dashboard`)
**Avant** :
```typescript
canViewSessions = ["admin", "tresorier", "membre"].includes(role)
canViewCaisses = ["admin", "tresorier"].includes(role)
canCreateSessions = role === "admin"
canCreateCaisses = ["admin", "tresorier"].includes(role)
```

**Après** :
```typescript
canViewSessions = ["secretary_general", "controller", "cashier"].includes(role)
canViewCaisses = ["secretary_general", "controller", "cashier"].includes(role)
canCreateSessions = role === "secretary_general"
canCreateCaisses = role === "secretary_general"
```

#### 2. **Sessions** (`/dashboard/sessions`)
**Avant** :
```typescript
canManageSessions = ["admin", "tresorier"].includes(role)
canCreateSessions = role === "admin"
```

**Après** :
```typescript
canManageSessions = ["secretary_general", "controller", "cashier"].includes(role)
canCreateSessions = role === "secretary_general"
```

#### 3. **Caisses** (`/dashboard/caisses`)
**Avant** :
```typescript
canManageCaisses = ["admin", "tresorier"].includes(role)
canCreateCaisses = ["admin", "tresorier"].includes(role)
canEmarger = role === "tresorier"
```

**Après** :
```typescript
canManageCaisses = ["secretary_general", "controller", "cashier"].includes(role)
canCreateCaisses = role === "secretary_general"
canEmarger = role === "cashier"
```

#### 4. **Création/Édition Caisses**
**Avant** :
```typescript
canCreateCaisses = ["admin", "tresorier"].includes(role)
canEditCaisses = ["admin", "tresorier"].includes(role)
```

**Après** :
```typescript
canCreateCaisses = role === "secretary_general"
canEditCaisses = role === "secretary_general"
```

### ✅ **Pages Déjà Correctes**

#### 1. **Members** (`/dashboard/members`)
```typescript
canManageMembers = [UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER].includes(role)
canEditMembers = role === UserRole.SECRETARY_GENERAL
```

#### 2. **Payments** (`/dashboard/payments`)
```typescript
canManagePayments = [UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER, UserRole.CASHIER].includes(role)
canCreatePayments = [UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER, UserRole.CASHIER].includes(role)
```

#### 3. **Sessions Detail** (`/dashboard/sessions/[id]`)
```typescript
canManageSession = [UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER].includes(role)
```

#### 4. **Users** (`/dashboard/users`)
```typescript
canManageUsers = role === UserRole.SECRETARY_GENERAL
```

## Hiérarchie des Permissions

### **SECRETARY_GENERAL** (Secrétaire Général)
- **Accès complet** : Toutes les opérations CRUD
- **Gestion exclusive** : Création/modification/suppression de Members, Sessions, Caisses
- **Supervision** : Fermeture des caisses, gestion des ordres de sortie
- **Administration** : Gestion des utilisateurs

### **CONTROLLER** (Contrôleur)
- **Lecture étendue** : Consultation de tous les modules
- **Paiements** : Création de paiements (tous types)
- **Supervision** : Fermeture des caisses, consultation des ordres de sortie
- **Rapports** : Accès aux debriefs financiers

### **CASHIER** (Caissier)
- **Lecture limitée** : Consultation des sessions, caisses, réunions
- **Paiements** : Création uniquement sur sa caisse assignée
- **Caisse** : Ouverture/fermeture de sa caisse de réunion
- **Cotisations** : Enregistrement des cotisations des membres

## Cohérence Assurée ✅

1. **Valeurs enum** : Backend et frontend utilisent les mêmes valeurs
2. **Permissions** : Frontend respecte exactement les autorisations backend
3. **Hiérarchie** : Logique métier cohérente entre les deux couches
4. **Sécurité** : Vérifications côté client alignées sur les guards backend
