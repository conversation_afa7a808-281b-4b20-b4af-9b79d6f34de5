import { Body, Controller, Delete, Get, Param, Patch, Post, Req, UseGuards, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../common/guards/role.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../common/enums/user-role.enum';
import { CaissesService } from './caisses.service';
import { CreateCaisseDto } from './dto/create-caisse.dto';
import { UpdateCaisseDto } from './dto/update-caisse.dto';
import { CreateExitOrderDto } from './dto/create-exit-order.dto';
import { CloseExitOrderDto } from './dto/close-exit-order.dto';
import { ExitOrder } from './schemas/exit-order.schema';
import { RenflouerDto } from './dto/renflouer.dto';
import { ExtractionDto } from './dto/extraction.dto';
import { MemberContributionDto } from './dto/member-contribution.dto';

@ApiTags('caisses')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('caisses')
export class CaissesController {
  constructor(private readonly caissesService: CaissesService) {}

  @Post()
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Créer une caisse', description: 'Crée une caisse PRINCIPALE ou REUNION. Pour une caisse REUNION, fournir obligatoirement sessionId, caissePrincipaleId et cashierId.' })
  @ApiResponse({ status: 201, description: 'Caisse créée' })
  create(@Body() dto: CreateCaisseDto, @Req() req: any) {
    return this.caissesService.create(dto, req.user._id);
  }

  @Get()
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER, UserRole.CASHIER)
  @ApiOperation({ summary: 'Lister les caisses', description: 'Retourne la liste des caisses triées par date de création (desc).' })
  findAll() {
    return this.caissesService.findAll();
  }

  @Get(':id')
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER, UserRole.CASHIER)
  @ApiOperation({ summary: 'Détail d\'une caisse' })
  @ApiParam({ name: 'id', description: 'ID de la caisse' })
  findOne(@Param('id') id: string) {
    return this.caissesService.findOne(id);
  }

  @Patch(':id')
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Mettre à jour une caisse', description: 'Modifie les métadonnées de la caisse (nom, type, solde, liens...).' })
  update(@Param('id') id: string, @Body() dto: UpdateCaisseDto) {
    return this.caissesService.update(id, dto);
  }

  @Delete(':id')
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Supprimer une caisse', description: 'Impossible de supprimer une caisse REUNION liée à une session active.' })
  remove(@Param('id') id: string) {
    return this.caissesService.remove(id);
  }

  // cashier opens his own reunion caisse on meeting day
  @Post(':id/open')
  @Roles(UserRole.CASHIER)
  @ApiOperation({ summary: 'Ouvrir une caisse de réunion', description: 'Uniquement par le caissier assigné et uniquement le jour de la prochaine réunion de la session.' })
  @ApiParam({ name: 'id', description: 'ID de la caisse REUNION' })
  open(@Param('id') id: string, @Req() req: any) {
    return this.caissesService.open(id, req.user._id);
  }

  // controller/secretary close and auto-emarge to principal
  @Post(':id/close')
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER)
  @ApiOperation({ summary: 'Fermer une caisse de réunion', description: 'Fermeture par contrôleur/SG. Si un solde existe, un émargement automatique (transfert) vers la caisse principale liée est créé avant la fermeture.' })
  @ApiParam({ name: 'id', description: 'ID de la caisse REUNION' })
  close(@Param('id') id: string, @Req() req: any) {
    return this.caissesService.close(id, req.user._id);
  }

  // Exit Orders management (SECRETARY_GENERAL)
  @Post(':id/exit-orders')
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Émettre un ordre de sortie', description: 'Émis par le SG. Sur caisse REUNION: reason optionnel. Sur caisse PRINCIPALE: reason obligatoire. Un ordre de sortie est un droit à payer plus tard par le caissier.' })
  @ApiParam({ name: 'id', description: 'ID de la caisse' })
  createExitOrder(@Param('id') caisseId: string, @Body() dto: CreateExitOrderDto, @Req() req: any) {
    return this.caissesService.createExitOrder(caisseId, dto, req.user._id);
  }

  @Get(':id/exit-orders')
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER)
  @ApiOperation({ summary: 'Lister les ordres de sortie d\'une caisse' })
  listExitOrders(@Param('id') caisseId: string) {
    return this.caissesService.listExitOrders(caisseId);
  }

  @Get(':id/my-exit-orders')
  @Roles(UserRole.CASHIER)
  @ApiOperation({ summary: 'Lister mes ordres de sortie (caissier)', description: 'Retourne les ordres de sortie de la caisse dont je suis le caissier assigné.' })
  listCashierExitOrders(@Param('id') caisseId: string, @Req() req: any) {
    return this.caissesService.listCashierExitOrders(caisseId, req.user._id);
  }
  @Delete('exit-orders/:exitOrderId')
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Supprimer un ordre de sortie (annulation)', description: 'Annule un ordre de sortie encore ouvert (non payé). Ne ferme pas un ordre déjà payé.' })
  @ApiParam({ name: 'exitOrderId', description: 'ID de l\'ordre de sortie' })
  deleteExitOrders(@Param('exitOrderId') exitOrderId: string) {
    return this.caissesService.removeExitOrder(exitOrderId);
  }

  @Post(':id/exit-orders/close')
  @Roles(UserRole.CASHIER)
  @ApiOperation({ summary: 'Clore un ordre de sortie (effectuer le paiement)', description: 'Le caissier effectue le paiement OUT EXTERNAL rattaché à l\'ordre de sortie. Clore ici veut dire payer, pas annuler.' })
  closeExitOrder(@Param('id') caisseId: string, @Body() dto: CloseExitOrderDto, @Req() req: any) {
    return this.caissesService.closeExitOrder(caisseId, dto.exitOrderId, req.user._id);
  }

  @Post(':id/emarger')
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Émarger manuellement une caisse de réunion', description: 'Crée un transfert OUT de la réunion vers la principale liée. Utilisé si besoin hors fermeture automatique.' })
  emarger(@Param('id') id: string, @Req() req: any) {
    return this.caissesService.emargerCaisse(id,req.user._id);
  }

  @Post(':id/renflouer')
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Renflouer une caisse de réunion depuis la principale', description: 'Crée un transfert OUT de la PRINCIPALE (dto.caissePrincipale) vers la caisse REUNION identifiée par :id.' })
  renflouer(@Param('id') id: string, @Body() dto:RenflouerDto, @Req() req: any) {
    return this.caissesService.renflouerCaisse(id,dto,req.user._id);
  }

  @Post('extraction')
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Extraction externe depuis la principale', description: 'Effectue un paiement OUT EXTERNAL depuis la caisse principale indiquée. Un motif (reason) est recommandé pour la traçabilité.' })
  extraction(@Body() dto:ExtractionDto, @Req() req: any) {
    return this.caissesService.extractCaisse(dto,req.user._id);
  }

  // Contribution d'un membre (cotisation), enregistrée par le caissier sur la caisse de réunion
  @Post(':id/cotiser')
  @Roles(UserRole.CASHIER)
  @ApiOperation({ summary: 'Cotisation d\'un membre', description: 'Enregistre une cotisation IN sur la caisse REUNION ouverte. Si le montant dépasse la cotisation par réunion, il est ventilé automatiquement sur les réunions futures; le reliquat devient une avance.' })
  contribute(@Param('id') caisseId: string, @Body() dto: MemberContributionDto, @Req() req: any) {
    return this.caissesService.memberContribute(caisseId, dto, req.user._id);
  }

  // Prochaine cotisation d'un membre (date + montant)
  @Get(':id/next-contribution/:sessionId/:memberId')
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER, UserRole.CASHIER)
  @ApiOperation({ summary: 'Prochaine cotisation d\'un membre', description: 'Renvoie la prochaine réunion (id/date), le montant de la cotisation par réunion, et le montant dû tenant compte des avances.' })
  nextContribution(@Param('id') caisseId: string, @Param('sessionId') sessionId: string, @Param('memberId') memberId: string) {
    return this.caissesService.nextContribution(memberId, sessionId);
  }

  // Debrief d'une caisse avec filtres de paiement
  @Get(':id/debrief')
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER)
  @ApiOperation({ summary: 'Debrief financier d\'une caisse', description: 'Retourne totaux IN/OUT, net, agrégations par fonction et nombre d\'opérations. Filtrable par membre, session, réunion, période, direction, fonction.' })
  debriefCaisse(@Param('id') caisseId: string, @Query() filters: any) {
    return this.caissesService.debriefCaisse(caisseId, filters);
  }

  // MyDebrief pour un caissier (vérifie la caisse assignée au user)
  @Get(':id/mydebrief')
  @Roles(UserRole.CASHIER)
  @ApiOperation({ summary: 'Mon debrief (caissier)', description: 'Identique au debrief mais uniquement si la caisse est assignée au caissier connecté.' })
  myDebrief(@Param('id') caisseId: string, @Query() filters: any, @Req() req: any) {
    return this.caissesService.myDebrief(caisseId, req.user._id, filters);
  }
}