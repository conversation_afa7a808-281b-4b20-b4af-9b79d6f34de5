{"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-context@1.1_a458b34bf99ec9ddcc4dc5937e16e7cc/node_modules/@radix-ui/react-context/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+primitive@1.1.3/node_modules/@radix-ui/primitive/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-dismissable_62ddbcfd147cbfa2458e5e368e4002ea/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-use-callbac_a01884fe90fe5a972b949512d5480bf5/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-use-escape-_3be2485f0f853aa7d18aed039d2fa05d/node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-focus-guard_7fddbd90fb84fc98f3dd15f4f9f638f7/node_modules/@radix-ui/react-focus-guards/src/focus-guards.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-focus-scope_80509ef8bf77adc6c05b71a111d35384/node_modules/@radix-ui/react-focus-scope/src/focus-scope.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-use-layout-_a793615e2072124f7d07f32b66088c76/node_modules/@radix-ui/react-use-layout-effect/src/use-layout-effect.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@19.1.12_react@19.1.0/node_modules/@radix-ui/react-id/src/id.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/@floating-ui+utils@0.2.10/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@floating-ui+utils@0.2.10/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-popper@1.2._cc253319faffd3d7ce17be59fc646eee/node_modules/@radix-ui/react-popper/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-arrow@1.1.7_387823ed6f581660b94c656af558ed76/node_modules/@radix-ui/react-arrow/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@floating-ui+core@1.7.3/node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-use-size@1._3083e675b94b2c2159c2a5ad58199274/node_modules/@radix-ui/react-use-size/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@floating-ui+react-dom@2.1._1deb9f017982eca36e7d9387cb29beb0/node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@floating-ui+dom@1.7.4/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-portal@1.1._b9ca9c5637b1986f5bec5fa6ccc4ccfb/node_modules/@radix-ui/react-portal/src/portal.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-use-control_86b98e710431e3b830ecbc7609fd2a29/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-use-effect-_d497940a1782654d8de56ac4869fd5de/node_modules/@radix-ui/react-use-effect-event/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/aria-hidden@1.2.6/node_modules/aria-hidden/dist/es2015/index.js", "turbopack:///[project]/frontend/node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.1.12_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/Combination.js", "turbopack:///[project]/frontend/node_modules/.pnpm/react-remove-scroll-bar@2.3_425067ad6c7fd086de4e363fd3126591/node_modules/react-remove-scroll-bar/dist/es2015/constants.js", "turbopack:///[project]/frontend/node_modules/.pnpm/use-callback-ref@1.3.3_@types+react@19.1.12_react@19.1.0/node_modules/use-callback-ref/dist/es2015/assignRef.js", "turbopack:///[project]/frontend/node_modules/.pnpm/use-callback-ref@1.3.3_@types+react@19.1.12_react@19.1.0/node_modules/use-callback-ref/dist/es2015/useRef.js", "turbopack:///[project]/frontend/node_modules/.pnpm/use-callback-ref@1.3.3_@types+react@19.1.12_react@19.1.0/node_modules/use-callback-ref/dist/es2015/useMergeRef.js", "turbopack:///[project]/frontend/node_modules/.pnpm/use-sidecar@1.1.3_@types+react@19.1.12_react@19.1.0/node_modules/use-sidecar/dist/es2015/medium.js", "turbopack:///[project]/frontend/node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.1.12_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/medium.js", "turbopack:///[project]/frontend/node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.1.12_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/UI.js", "turbopack:///[project]/frontend/node_modules/.pnpm/use-sidecar@1.1.3_@types+react@19.1.12_react@19.1.0/node_modules/use-sidecar/dist/es2015/exports.js", "turbopack:///[project]/frontend/node_modules/.pnpm/get-nonce@1.0.1/node_modules/get-nonce/dist/es2015/index.js", "turbopack:///[project]/frontend/node_modules/.pnpm/react-style-singleton@2.2.3_bfd35ae52fed470502d2812b81122336/node_modules/react-style-singleton/dist/es2015/singleton.js", "turbopack:///[project]/frontend/node_modules/.pnpm/react-style-singleton@2.2.3_bfd35ae52fed470502d2812b81122336/node_modules/react-style-singleton/dist/es2015/hook.js", "turbopack:///[project]/frontend/node_modules/.pnpm/react-style-singleton@2.2.3_bfd35ae52fed470502d2812b81122336/node_modules/react-style-singleton/dist/es2015/component.js", "turbopack:///[project]/frontend/node_modules/.pnpm/react-remove-scroll-bar@2.3_425067ad6c7fd086de4e363fd3126591/node_modules/react-remove-scroll-bar/dist/es2015/utils.js", "turbopack:///[project]/frontend/node_modules/.pnpm/react-remove-scroll-bar@2.3_425067ad6c7fd086de4e363fd3126591/node_modules/react-remove-scroll-bar/dist/es2015/component.js", "turbopack:///[project]/frontend/node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.1.12_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "turbopack:///[project]/frontend/node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.1.12_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "turbopack:///[project]/frontend/node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.1.12_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "turbopack:///[project]/frontend/node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@19.1.12_react@19.1.0/node_modules/react-remove-scroll/dist/es2015/sidecar.js"], "sourcesContent": ["// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nvar canUseDOM = !!(typeof window !== \"undefined\" && window.document && window.document.createElement);\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nfunction getOwnerWindow(element) {\n  if (!canUseDOM) {\n    throw new Error(\"Cannot access window outside of the DOM\");\n  }\n  return element?.ownerDocument?.defaultView ?? window;\n}\nfunction getOwnerDocument(element) {\n  if (!canUseDOM) {\n    throw new Error(\"Cannot access document outside of the DOM\");\n  }\n  return element?.ownerDocument ?? document;\n}\nfunction getActiveElement(node, activeDescendant = false) {\n  const { activeElement } = getOwnerDocument(node);\n  if (!activeElement?.nodeName) {\n    return null;\n  }\n  if (isFrame(activeElement) && activeElement.contentDocument) {\n    return getActiveElement(activeElement.contentDocument.body, activeDescendant);\n  }\n  if (activeDescendant) {\n    const id = activeElement.getAttribute(\"aria-activedescendant\");\n    if (id) {\n      const element = getOwnerDocument(activeElement).getElementById(id);\n      if (element) {\n        return element;\n      }\n    }\n  }\n  return activeElement;\n}\nfunction isFrame(element) {\n  return element.tagName === \"IFRAME\";\n}\nexport {\n  canUseDOM,\n  composeEventHandlers,\n  getActiveElement,\n  getOwnerDocument,\n  getOwnerWindow,\n  isFrame\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/dismissable-layer.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { Primitive, dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useEscapeKeydown } from \"@radix-ui/react-use-escape-keydown\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = React.createContext({\n  layers: /* @__PURE__ */ new Set(),\n  layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n  branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node2) => setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = \"none\";\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n          pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n          ...props.style\n        },\n        onFocusCapture: composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )\n      }\n    );\n  }\n);\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = React.forwardRef((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n  return /* @__PURE__ */ jsx(Primitive.div, { ...props, ref: composedRefs });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside);\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {\n  });\n  React.useEffect(() => {\n    const handlePointerDown = (event) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        let handleAndDispatchPointerDownOutsideEvent2 = function() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        };\n        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n        const eventDetail = { originalEvent: event };\n        if (event.pointerType === \"touch\") {\n          ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n          ownerDocument.addEventListener(\"click\", handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent2();\n        }\n      } else {\n        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n      ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => isPointerInsideReactTreeRef.current = true\n  };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside);\n  const isFocusInsideReactTreeRef = React.useRef(false);\n  React.useEffect(() => {\n    const handleFocus = (event) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false\n        });\n      }\n    };\n    ownerDocument.addEventListener(\"focusin\", handleFocus);\n    return () => ownerDocument.removeEventListener(\"focusin\", handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n  return {\n    onFocusCapture: () => isFocusInsideReactTreeRef.current = true,\n    onBlurCapture: () => isFocusInsideReactTreeRef.current = false\n  };\n}\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler, { once: true });\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\nexport {\n  Branch,\n  DismissableLayer,\n  DismissableLayerBranch,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-callback-ref/src/use-callback-ref.tsx\nimport * as React from \"react\";\nfunction useCallbackRef(callback) {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return React.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\nexport {\n  useCallbackRef\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\nimport * as React from \"react\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n  React.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\nexport {\n  useEscapeKeydown\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from 'react';\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0;\n\ninterface FocusGuardsProps {\n  children?: React.ReactNode;\n}\n\nfunction FocusGuards(props: FocusGuardsProps) {\n  useFocusGuards();\n  return props.children;\n}\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction useFocusGuards() {\n  /* eslint-disable no-restricted-globals */\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', edgeGuards[1] ?? createFocusGuard());\n    count++;\n\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll('[data-radix-focus-guard]').forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n  /* eslint-enable no-restricted-globals */\n}\n\nfunction createFocusGuard() {\n  // eslint-disable-next-line no-restricted-globals\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.outline = 'none';\n  element.style.opacity = '0';\n  element.style.position = 'fixed';\n  element.style.pointerEvents = 'none';\n  return element;\n}\n\nexport {\n  FocusGuards,\n  //\n  FocusGuards as Root,\n  //\n  useFocusGuards,\n};\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n        // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return;\n\n        // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations: MutationRecord[]) {\n        const focusedElement = document.activeElement as HTMLElement | null;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n", "import * as React from 'react';\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst useLayoutEffect = globalThis?.document ? React.useLayoutEffect : () => {};\n\nexport { useLayoutEffect };\n", "import * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n// We spaces with `.trim().toString()` to prevent bundlers from trying to `import { useId } from 'react';`\nconst useReactId = (React as any)[' useId '.trim().toString()] || (() => undefined);\nlet count = 0;\n\nfunction useId(deterministicId?: string): string {\n  const [id, setId] = React.useState<string | undefined>(useReactId());\n  // React versions older than 18 will have client-side ids only.\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : '');\n}\n\nexport { useId };\n", "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nconst invalidOverflowDisplayValues = /*#__PURE__*/new Set(['inline', 'contents']);\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !invalidOverflowDisplayValues.has(display);\n}\nconst tableElements = /*#__PURE__*/new Set(['table', 'td', 'th']);\nfunction isTableElement(element) {\n  return tableElements.has(getNodeName(element));\n}\nconst topLayerSelectors = [':popover-open', ':modal'];\nfunction isTopLayer(element) {\n  return topLayerSelectors.some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (_e) {\n      return false;\n    }\n  });\n}\nconst transformProperties = ['transform', 'translate', 'scale', 'rotate', 'perspective'];\nconst willChangeValues = ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'];\nconst containValues = ['paint', 'layout', 'strict', 'content'];\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return transformProperties.some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || willChangeValues.some(value => (css.willChange || '').includes(value)) || containValues.some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nconst lastTraversableNodeNames = /*#__PURE__*/new Set(['html', 'body', '#document']);\nfunction isLastTraversableNode(node) {\n  return lastTraversableNodeNames.has(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n", "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nconst yAxisSides = /*#__PURE__*/new Set(['top', 'bottom']);\nfunction getSideAxis(placement) {\n  return yAxisSides.has(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nconst lrPlacement = ['left', 'right'];\nconst rlPlacement = ['right', 'left'];\nconst tbPlacement = ['top', 'bottom'];\nconst btPlacement = ['bottom', 'top'];\nfunction getSideList(side, isStart, rtl) {\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rlPlacement : lrPlacement;\n      return isStart ? lrPlacement : rlPlacement;\n    case 'left':\n    case 'right':\n      return isStart ? tbPlacement : btPlacement;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "\"use client\";\n\n// src/popper.tsx\nimport * as React from \"react\";\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size\n} from \"@floating-ui/react-dom\";\nimport * as ArrowPrimitive from \"@radix-ui/react-arrow\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { useSize } from \"@radix-ui/react-use-size\";\nimport { jsx } from \"react/jsx-runtime\";\nvar SIDE_OPTIONS = [\"top\", \"right\", \"bottom\", \"left\"];\nvar ALIGN_OPTIONS = [\"start\", \"center\", \"end\"];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState(null);\n  return /* @__PURE__ */ jsx(PopperProvider, { scope: __scopePopper, anchor, onAnchorChange: setAnchor, children });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const anchorRef = React.useRef(null);\n    React.useEffect(() => {\n      const previousAnchor = anchorRef.current;\n      anchorRef.current = virtualRef?.current || ref.current;\n      if (previousAnchor !== anchorRef.current) {\n        context.onAnchorChange(anchorRef.current);\n      }\n    });\n    return virtualRef ? null : /* @__PURE__ */ jsx(Primitive.div, { ...anchorProps, ref: composedRefs });\n  }\n);\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = \"bottom\",\n      sideOffset = 0,\n      align = \"center\",\n      alignOffset = 0,\n      arrowPadding = 0,\n      avoidCollisions = true,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = \"partial\",\n      hideWhenDetached = false,\n      updatePositionStrategy = \"optimized\",\n      onPlaced,\n      ...contentProps\n    } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [arrow, setArrow] = React.useState(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: \"fixed\",\n      placement: desiredPlacement,\n      whileElementsMounted: (...args) => {\n        const cleanup = autoUpdate(...args, {\n          animationFrame: updatePositionStrategy === \"always\"\n        });\n        return cleanup;\n      },\n      elements: {\n        reference: context.anchor\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions && shift({\n          mainAxis: true,\n          crossAxis: false,\n          limiter: sticky === \"partial\" ? limitShift() : void 0,\n          ...detectOverflowOptions\n        }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n            contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n            contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n            contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n          }\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: \"referenceHidden\", ...detectOverflowOptions })\n      ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = React.useState();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n    return /* @__PURE__ */ jsx(\n      \"div\",\n      {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n          // keep off the page when measuring\n          minWidth: \"max-content\",\n          zIndex: contentZIndex,\n          [\"--radix-popper-transform-origin\"]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y\n          ].join(\" \"),\n          // hide the content if using the hide middleware and should be hidden\n          // set visibility to hidden and disable pointer events so the UI behaves\n          // as if the PopperContent isn't there at all\n          ...middlewareData.hide?.referenceHidden && {\n            visibility: \"hidden\",\n            pointerEvents: \"none\"\n          }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ jsx(\n          PopperContentProvider,\n          {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ jsx(\n              Primitive.div,\n              {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                  ...contentProps.style,\n                  // if the PopperContent hasn't been placed yet (not all measurements done)\n                  // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                  animation: !isPositioned ? \"none\" : void 0\n                }\n              }\n            )\n          }\n        )\n      }\n    );\n  }\n);\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n  top: \"bottom\",\n  right: \"left\",\n  bottom: \"top\",\n  left: \"right\"\n};\nvar PopperArrow = React.forwardRef(function PopperArrow2(props, forwardedRef) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ jsx(\n      \"span\",\n      {\n        ref: contentContext.onArrowChange,\n        style: {\n          position: \"absolute\",\n          left: contentContext.arrowX,\n          top: contentContext.arrowY,\n          [baseSide]: 0,\n          transformOrigin: {\n            top: \"\",\n            right: \"0 0\",\n            bottom: \"center 0\",\n            left: \"100% 0\"\n          }[contentContext.placedSide],\n          transform: {\n            top: \"translateY(100%)\",\n            right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n            bottom: `rotate(180deg)`,\n            left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n          }[contentContext.placedSide],\n          visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ jsx(\n          ArrowPrimitive.Root,\n          {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n              ...arrowProps.style,\n              // ensures the element can be measured correctly (mostly for if SVG)\n              display: \"block\"\n            }\n          }\n        )\n      }\n    )\n  );\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n  return value !== null;\n}\nvar transformOrigin = (options) => ({\n  name: \"transformOrigin\",\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: \"0%\", center: \"50%\", end: \"100%\" }[placedAlign];\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n    let x = \"\";\n    let y = \"\";\n    if (placedSide === \"bottom\") {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === \"top\") {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === \"right\") {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === \"left\") {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  }\n});\nfunction getSideAndAlignFromPlacement(placement) {\n  const [side, align = \"center\"] = placement.split(\"-\");\n  return [side, align];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\nexport {\n  ALIGN_OPTIONS,\n  Anchor,\n  Arrow,\n  Content,\n  Popper,\n  PopperAnchor,\n  PopperArrow,\n  PopperContent,\n  Root2 as Root,\n  SIDE_OPTIONS,\n  createPopperScope\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/arrow.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Arrow\";\nvar Arrow = React.forwardRef((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return /* @__PURE__ */ jsx(\n    Primitive.svg,\n    {\n      ...arrowProps,\n      ref: forwardedRef,\n      width,\n      height,\n      viewBox: \"0 0 30 10\",\n      preserveAspectRatio: \"none\",\n      children: props.asChild ? children : /* @__PURE__ */ jsx(\"polygon\", { points: \"0,0 30,0 15,10\" })\n    }\n  );\n});\nArrow.displayName = NAME;\nvar Root = Arrow;\nexport {\n  Arrow,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          if (!ignoreCrossAxisOverflow ||\n          // We leave the current main axis only if every placement on that axis\n          // overflows the main axis.\n          overflowsData.every(d => getSideAxis(d.placement) === initialSideAxis ? d.overflows[0] > 0 : true)) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nconst originSides = /*#__PURE__*/new Set(['left', 'top']);\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = originSides.has(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = originSides.has(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "// packages/react/use-size/src/use-size.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nfunction useSize(element) {\n  const [size, setSize] = React.useState(void 0);\n  useLayoutEffect(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\nexport {\n  useSize\n};\n//# sourceMappingURL=index.mjs.map\n", "import { computePosition, arrow as arrow$2, autoPlacement as autoPlacement$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1 } from '@floating-ui/dom';\nexport { autoUpdate, computePosition, detectOverflow, getOverflowAncestors, platform } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\nvar isClient = typeof document !== 'undefined';\n\nvar noop = function noop() {};\nvar index = isClient ? useLayoutEffect : noop;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length;\n  let i;\n  let keys;\n  if (a && b && typeof a === 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length !== b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node !== referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, []);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, []);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const hasWhileElementsMounted = whileElementsMounted != null;\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const openRef = useLatestRef(open);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        // The floating element's position may be recomputed while it's closed\n        // but still mounted (such as when transitioning out). To ensure\n        // `isPositioned` will be `false` initially on the next open, avoid\n        // setting it to `true` when `open === false` (must be specified).\n        isPositioned: openRef.current !== false\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef, openRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      }\n      update();\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef, hasWhileElementsMounted]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$2({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      }\n      if (element) {\n        return arrow$2({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = (options, deps) => ({\n  ...offset$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = (options, deps) => ({\n  ...shift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = (options, deps) => ({\n  ...limitShift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = (options, deps) => ({\n  ...flip$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = (options, deps) => ({\n  ...size$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = (options, deps) => ({\n  ...autoPlacement$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = (options, deps) => ({\n  ...hide$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = (options, deps) => ({\n  ...inline$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = (options, deps) => ({\n  ...arrow$1(options),\n  options: [options, deps]\n});\n\nexport { arrow, autoPlacement, flip, hide, inline, limitShift, offset, shift, size, useFloating };\n", "import { rectToClientRect, arrow as arrow$1, autoPlacement as autoPlacement$1, detectOverflow as detectOverflow$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle as getComputedStyle$1, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle$1(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle$1(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll) {\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - getWindowScrollBarX(documentElement, htmlRect);\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle$1(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Safety check: ensure the scrollbar space is reasonable in case this\n// calculation is affected by unusual styles.\n// Most scrollbars leave 15-18px of space.\nconst SCROLLBAR_MAX = 25;\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  const windowScrollbarX = getWindowScrollBarX(html);\n  // <html> `overflow: hidden` + `scrollbar-gutter: stable` reduces the\n  // visual width of the <html> but this is not considered in the size\n  // of `html.clientWidth`.\n  if (windowScrollbarX <= 0) {\n    const doc = html.ownerDocument;\n    const body = doc.body;\n    const bodyStyles = getComputedStyle(body);\n    const bodyMarginInline = doc.compatMode === 'CSS1Compat' ? parseFloat(bodyStyles.marginLeft) + parseFloat(bodyStyles.marginRight) || 0 : 0;\n    const clippingStableScrollbarWidth = Math.abs(html.clientWidth - body.clientWidth - bodyMarginInline);\n    if (clippingStableScrollbarWidth <= SCROLLBAR_MAX) {\n      width -= clippingStableScrollbarWidth;\n    }\n  } else if (windowScrollbarX <= SCROLLBAR_MAX) {\n    // If the <body> scrollbar is on the left, the width needs to be extended\n    // by the scrollbar amount so there isn't extra space on the right.\n    width += windowScrollbarX;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nconst absoluteOrFixed = /*#__PURE__*/new Set(['absolute', 'fixed']);\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle$1(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle$1(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle$1(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && absoluteOrFixed.has(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle$1(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle$1(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle$1(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n", "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n/* -------------------------------------------------------------------------------------------------\n * Portal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'Portal';\n\ntype PortalElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PortalProps extends PrimitiveDivProps {\n  /**\n   * An optional container where the portaled content should be appended.\n   */\n  container?: Element | DocumentFragment | null;\n}\n\nconst Portal = React.forwardRef<PortalElement, PortalProps>((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || (mounted && globalThis?.document?.body);\n  return container\n    ? ReactDOM.createPortal(<Primitive.div {...portalProps} ref={forwardedRef} />, container)\n    : null;\n});\n\nPortal.displayName = PORTAL_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Portal;\n\nexport {\n  Portal,\n  //\n  Root,\n};\nexport type { PortalProps };\n", "// src/use-controllable-state.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useInsertionEffect = React[\" useInsertionEffect \".trim().toString()] || useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = React.useRef(prop !== void 0);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\nimport * as React2 from \"react\";\nimport { useEffectEvent } from \"@radix-ui/react-use-effect-event\";\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = useEffectEvent(onChangeProp);\n  if (true) {\n    const isControlledRef = React2.useRef(controlledState !== void 0);\n    React2.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = React2.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React2.useRef(uncontrolledState);\n  React2.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = React2.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  React2.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\nexport {\n  useControllableState,\n  useControllableStateReducer\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/use-effect-event.tsx\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport * as React from \"react\";\nvar useReactEffectEvent = React[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = React[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n  if (typeof useReactEffectEvent === \"function\") {\n    return useReactEffectEvent(callback);\n  }\n  const ref = React.useRef(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  if (typeof useReactInsertionEffect === \"function\") {\n    useReactInsertionEffect(() => {\n      ref.current = callback;\n    });\n  } else {\n    useLayoutEffect(() => {\n      ref.current = callback;\n    });\n  }\n  return React.useMemo(() => (...args) => ref.current?.(...args), []);\n}\nexport {\n  useEffectEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "var getDefaultParent = function (originalTarget) {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    var sampleTarget = Array.isArray(originalTarget) ? originalTarget[0] : originalTarget;\n    return sampleTarget.ownerDocument.body;\n};\nvar counterMap = new WeakMap();\nvar uncontrolledNodes = new WeakMap();\nvar markerMap = {};\nvar lockCount = 0;\nvar unwrapHost = function (node) {\n    return node && (node.host || unwrapHost(node.parentNode));\n};\nvar correctTargets = function (parent, targets) {\n    return targets\n        .map(function (target) {\n        if (parent.contains(target)) {\n            return target;\n        }\n        var correctedTarget = unwrapHost(target);\n        if (correctedTarget && parent.contains(correctedTarget)) {\n            return correctedTarget;\n        }\n        console.error('aria-hidden', target, 'in not contained inside', parent, '. Doing nothing');\n        return null;\n    })\n        .filter(function (x) { return Boolean(x); });\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @param {String} [controlAttribute] - html Attribute to control\n * @return {Undo} undo command\n */\nvar applyAttributeToOthers = function (originalTarget, parentNode, markerName, controlAttribute) {\n    var targets = correctTargets(parentNode, Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    if (!markerMap[markerName]) {\n        markerMap[markerName] = new WeakMap();\n    }\n    var markerCounter = markerMap[markerName];\n    var hiddenNodes = [];\n    var elementsToKeep = new Set();\n    var elementsToStop = new Set(targets);\n    var keep = function (el) {\n        if (!el || elementsToKeep.has(el)) {\n            return;\n        }\n        elementsToKeep.add(el);\n        keep(el.parentNode);\n    };\n    targets.forEach(keep);\n    var deep = function (parent) {\n        if (!parent || elementsToStop.has(parent)) {\n            return;\n        }\n        Array.prototype.forEach.call(parent.children, function (node) {\n            if (elementsToKeep.has(node)) {\n                deep(node);\n            }\n            else {\n                try {\n                    var attr = node.getAttribute(controlAttribute);\n                    var alreadyHidden = attr !== null && attr !== 'false';\n                    var counterValue = (counterMap.get(node) || 0) + 1;\n                    var markerValue = (markerCounter.get(node) || 0) + 1;\n                    counterMap.set(node, counterValue);\n                    markerCounter.set(node, markerValue);\n                    hiddenNodes.push(node);\n                    if (counterValue === 1 && alreadyHidden) {\n                        uncontrolledNodes.set(node, true);\n                    }\n                    if (markerValue === 1) {\n                        node.setAttribute(markerName, 'true');\n                    }\n                    if (!alreadyHidden) {\n                        node.setAttribute(controlAttribute, 'true');\n                    }\n                }\n                catch (e) {\n                    console.error('aria-hidden: cannot operate on ', node, e);\n                }\n            }\n        });\n    };\n    deep(parentNode);\n    elementsToKeep.clear();\n    lockCount++;\n    return function () {\n        hiddenNodes.forEach(function (node) {\n            var counterValue = counterMap.get(node) - 1;\n            var markerValue = markerCounter.get(node) - 1;\n            counterMap.set(node, counterValue);\n            markerCounter.set(node, markerValue);\n            if (!counterValue) {\n                if (!uncontrolledNodes.has(node)) {\n                    node.removeAttribute(controlAttribute);\n                }\n                uncontrolledNodes.delete(node);\n            }\n            if (!markerValue) {\n                node.removeAttribute(markerName);\n            }\n        });\n        lockCount--;\n        if (!lockCount) {\n            // clear\n            counterMap = new WeakMap();\n            counterMap = new WeakMap();\n            uncontrolledNodes = new WeakMap();\n            markerMap = {};\n        }\n    };\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var hideOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-aria-hidden'; }\n    var targets = Array.from(Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    // we should not hide aria-live elements - https://github.com/theKashey/aria-hidden/issues/10\n    // and script elements, as they have no impact on accessibility.\n    targets.push.apply(targets, Array.from(activeParentNode.querySelectorAll('[aria-live], script')));\n    return applyAttributeToOthers(targets, activeParentNode, markerName, 'aria-hidden');\n};\n/**\n * Marks everything except given node(or nodes) as inert\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var inertOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-inert-ed'; }\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    return applyAttributeToOthers(originalTarget, activeParentNode, markerName, 'inert');\n};\n/**\n * @returns if current browser supports inert\n */\nexport var supportsInert = function () {\n    return typeof HTMLElement !== 'undefined' && HTMLElement.prototype.hasOwnProperty('inert');\n};\n/**\n * Automatic function to \"suppress\" DOM elements - _hide_ or _inert_ in the best possible way\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var suppressOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-suppressed'; }\n    return (supportsInert() ? inertOthers : hideOthers)(originalTarget, parentNode, markerName);\n};\n", "/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n", "export var zeroRightClassName = 'right-scroll-bar-position';\nexport var fullWidthClassName = 'width-before-scroll-bar';\nexport var noScrollbarsClassName = 'with-scroll-bars-hidden';\n/**\n * Name of a CSS variable containing the amount of \"hidden\" scrollbar\n * ! might be undefined ! use will fallback!\n */\nexport var removedBarSizeVariable = '--removed-body-scroll-bar-size';\n", "/**\n * Assigns a value for a given ref, no matter of the ref format\n * @param {RefObject} ref - a callback function or ref object\n * @param value - a new value\n *\n * @see https://github.com/theKashey/use-callback-ref#assignref\n * @example\n * const refObject = useRef();\n * const refFn = (ref) => {....}\n *\n * assignRef(refObject, \"refValue\");\n * assignRef(refFn, \"refValue\");\n */\nexport function assignRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    }\n    else if (ref) {\n        ref.current = value;\n    }\n    return ref;\n}\n", "import { useState } from 'react';\n/**\n * creates a MutableRef with ref change callback\n * @param initialValue - initial ref value\n * @param {Function} callback - a callback to run when value changes\n *\n * @example\n * const ref = useCallbackRef(0, (newValue, oldValue) => console.log(oldValue, '->', newValue);\n * ref.current = 1;\n * // prints 0 -> 1\n *\n * @see https://reactjs.org/docs/hooks-reference.html#useref\n * @see https://github.com/theKashey/use-callback-ref#usecallbackref---to-replace-reactuseref\n * @returns {MutableRefObject}\n */\nexport function useCallbackRef(initialValue, callback) {\n    var ref = useState(function () { return ({\n        // value\n        value: initialValue,\n        // last callback\n        callback: callback,\n        // \"memoized\" public interface\n        facade: {\n            get current() {\n                return ref.value;\n            },\n            set current(value) {\n                var last = ref.value;\n                if (last !== value) {\n                    ref.value = value;\n                    ref.callback(value, last);\n                }\n            },\n        },\n    }); })[0];\n    // update callback\n    ref.callback = callback;\n    return ref.facade;\n}\n", "import * as React from 'react';\nimport { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nvar currentValues = new WeakMap();\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n    var callbackRef = useCallbackRef(defaultValue || null, function (newValue) {\n        return refs.forEach(function (ref) { return assignRef(ref, newValue); });\n    });\n    // handle refs changes - added or removed\n    useIsomorphicLayoutEffect(function () {\n        var oldValue = currentValues.get(callbackRef);\n        if (oldValue) {\n            var prevRefs_1 = new Set(oldValue);\n            var nextRefs_1 = new Set(refs);\n            var current_1 = callbackRef.current;\n            prevRefs_1.forEach(function (ref) {\n                if (!nextRefs_1.has(ref)) {\n                    assignRef(ref, null);\n                }\n            });\n            nextRefs_1.forEach(function (ref) {\n                if (!prevRefs_1.has(ref)) {\n                    assignRef(ref, current_1);\n                }\n            });\n        }\n        currentValues.set(callbackRef, refs);\n    }, [refs]);\n    return callbackRef;\n}\n", "import { __assign } from \"tslib\";\nfunction ItoI(a) {\n    return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    var buffer = [];\n    var assigned = false;\n    var medium = {\n        read: function () {\n            if (assigned) {\n                throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n            }\n            if (buffer.length) {\n                return buffer[buffer.length - 1];\n            }\n            return defaults;\n        },\n        useMedium: function (data) {\n            var item = middleware(data, assigned);\n            buffer.push(item);\n            return function () {\n                buffer = buffer.filter(function (x) { return x !== item; });\n            };\n        },\n        assignSyncMedium: function (cb) {\n            assigned = true;\n            while (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n            }\n            buffer = {\n                push: function (x) { return cb(x); },\n                filter: function () { return buffer; },\n            };\n        },\n        assignMedium: function (cb) {\n            assigned = true;\n            var pendingQueue = [];\n            if (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n                pendingQueue = buffer;\n            }\n            var executeQueue = function () {\n                var cbs = pendingQueue;\n                pendingQueue = [];\n                cbs.forEach(cb);\n            };\n            var cycle = function () { return Promise.resolve().then(executeQueue); };\n            cycle();\n            buffer = {\n                push: function (x) {\n                    pendingQueue.push(x);\n                    cycle();\n                },\n                filter: function (filter) {\n                    pendingQueue = pendingQueue.filter(filter);\n                    return buffer;\n                },\n            };\n        },\n    };\n    return medium;\n}\nexport function createMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    return innerCreateMedium(defaults, middleware);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function createSidecarMedium(options) {\n    if (options === void 0) { options = {}; }\n    var medium = innerCreateMedium(null);\n    medium.options = __assign({ async: true, ssr: false }, options);\n    return medium;\n}\n", "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noRelative = props.noRelative, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, gapMode = props.gapMode, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noRelative\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\", \"gapMode\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noRelative: noRelative, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref, gapMode: gapMode })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n", "import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nvar SideCar = function (_a) {\n    var sideCar = _a.sideCar, rest = __rest(_a, [\"sideCar\"]);\n    if (!sideCar) {\n        throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n    }\n    var Target = sideCar.read();\n    if (!Target) {\n        throw new Error('Sidecar medium not found');\n    }\n    return React.createElement(Target, __assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nexport function exportSidecar(medium, exported) {\n    medium.useMedium(exported);\n    return SideCar;\n}\n", "var currentNonce;\nexport var setNonce = function (nonce) {\n    currentNonce = nonce;\n};\nexport var getNonce = function () {\n    if (currentNonce) {\n        return currentNonce;\n    }\n    if (typeof __webpack_nonce__ !== 'undefined') {\n        return __webpack_nonce__;\n    }\n    return undefined;\n};\n", "import { getNonce } from 'get-nonce';\nfunction makeStyleTag() {\n    if (!document)\n        return null;\n    var tag = document.createElement('style');\n    tag.type = 'text/css';\n    var nonce = getNonce();\n    if (nonce) {\n        tag.setAttribute('nonce', nonce);\n    }\n    return tag;\n}\nfunction injectStyles(tag, css) {\n    // @ts-ignore\n    if (tag.styleSheet) {\n        // @ts-ignore\n        tag.styleSheet.cssText = css;\n    }\n    else {\n        tag.appendChild(document.createTextNode(css));\n    }\n}\nfunction insertStyleTag(tag) {\n    var head = document.head || document.getElementsByTagName('head')[0];\n    head.appendChild(tag);\n}\nexport var stylesheetSingleton = function () {\n    var counter = 0;\n    var stylesheet = null;\n    return {\n        add: function (style) {\n            if (counter == 0) {\n                if ((stylesheet = makeStyleTag())) {\n                    injectStyles(stylesheet, style);\n                    insertStyleTag(stylesheet);\n                }\n            }\n            counter++;\n        },\n        remove: function () {\n            counter--;\n            if (!counter && stylesheet) {\n                stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n                stylesheet = null;\n            }\n        },\n    };\n};\n", "import * as React from 'react';\nimport { stylesheetSingleton } from './singleton';\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nexport var styleHookSingleton = function () {\n    var sheet = stylesheetSingleton();\n    return function (styles, isDynamic) {\n        React.useEffect(function () {\n            sheet.add(styles);\n            return function () {\n                sheet.remove();\n            };\n        }, [styles && isDynamic]);\n    };\n};\n", "import { styleHook<PERSON>ingleton } from './hook';\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nexport var styleSingleton = function () {\n    var useStyle = styleHookSingleton();\n    var Sheet = function (_a) {\n        var styles = _a.styles, dynamic = _a.dynamic;\n        useStyle(styles, dynamic);\n        return null;\n    };\n    return Sheet;\n};\n", "export var zeroGap = {\n    left: 0,\n    top: 0,\n    right: 0,\n    gap: 0,\n};\nvar parse = function (x) { return parseInt(x || '', 10) || 0; };\nvar getOffset = function (gapMode) {\n    var cs = window.getComputedStyle(document.body);\n    var left = cs[gapMode === 'padding' ? 'paddingLeft' : 'marginLeft'];\n    var top = cs[gapMode === 'padding' ? 'paddingTop' : 'marginTop'];\n    var right = cs[gapMode === 'padding' ? 'paddingRight' : 'marginRight'];\n    return [parse(left), parse(top), parse(right)];\n};\nexport var getGapWidth = function (gapMode) {\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    if (typeof window === 'undefined') {\n        return zeroGap;\n    }\n    var offsets = getOffset(gapMode);\n    var documentWidth = document.documentElement.clientWidth;\n    var windowWidth = window.innerWidth;\n    return {\n        left: offsets[0],\n        top: offsets[1],\n        right: offsets[2],\n        gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0]),\n    };\n};\n", "import * as React from 'react';\nimport { styleSingleton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\nexport var lockAttribute = 'data-scroll-locked';\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n    var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body[\").concat(lockAttribute, \"] {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([\n        allowRelative && \"position: relative \".concat(important, \";\"),\n        gapMode === 'margin' &&\n            \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"),\n        gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\"),\n    ]\n        .filter(Boolean)\n        .join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body[\").concat(lockAttribute, \"] {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\nvar getCurrentUseCounter = function () {\n    var counter = parseInt(document.body.getAttribute(lockAttribute) || '0', 10);\n    return isFinite(counter) ? counter : 0;\n};\nexport var useLockAttribute = function () {\n    React.useEffect(function () {\n        document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());\n        return function () {\n            var newCounter = getCurrentUseCounter() - 1;\n            if (newCounter <= 0) {\n                document.body.removeAttribute(lockAttribute);\n            }\n            else {\n                document.body.setAttribute(lockAttribute, newCounter.toString());\n            }\n        };\n    }, []);\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (_a) {\n    var noRelative = _a.noRelative, noImportant = _a.noImportant, _b = _a.gapMode, gapMode = _b === void 0 ? 'margin' : _b;\n    useLockAttribute();\n    /*\n     gap will be measured on every component mount\n     however it will be used only by the \"first\" invocation\n     due to singleton nature of <Style\n     */\n    var gap = React.useMemo(function () { return getGapWidth(gapMode); }, [gapMode]);\n    return React.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '') });\n};\n", "var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n", "var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    if (!(node instanceof Element)) {\n        return false;\n    }\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var ownerDocument = node.ownerDocument;\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), scrollHeight = _a[1], clientHeight = _a[2];\n            if (scrollHeight > clientHeight) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== ownerDocument.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        if (!target) {\n            break;\n        }\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        var parent_1 = target.parentNode;\n        // we will \"bubble\" from ShadowDom in case we are, or just to the parent in normal case\n        // this is the same logic used in focus-lock\n        target = (parent_1 && parent_1.nodeType === Node.DOCUMENT_FRAGMENT_NODE ? parent_1.host : parent_1);\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    // handle epsilon around 0 (non standard zoom levels)\n    if (isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScroll) < 1) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScrollTop) < 1) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n", "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(styleSingleton)[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if (('touches' in event && event.touches.length === 2) || (event.type === 'wheel' && event.ctrlKey)) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should, shadowParent: getOutermostShadowParent(target) };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { noRelative: props.noRelative, gapMode: props.gapMode }) : null));\n}\nfunction getOutermostShadowParent(node) {\n    var shadowParent = null;\n    while (node !== null) {\n        if (node instanceof ShadowRoot) {\n            shadowParent = node.host;\n            node = node.host;\n        }\n        node = node.parentNode;\n    }\n    return shadowParent;\n}\n", "import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n"], "names": ["handleFocusIn", "handleFocusOut", "handleMutations", "container", "useLayoutEffect"], "mappings": "6CCEA,SAAS,EAAqB,CAAoB,CAAE,CAAe,CAAE,0BAAE,GAA2B,CAAI,CAAE,CAAG,CAAC,CAAC,EAC3G,OAAO,SAAS,AAAY,CAAK,EAE/B,GADA,IAAuB,IACU,IAA7B,GAAsC,CAAC,EAAM,gBAAgB,CAC/D,CADiE,MAC1D,IAAkB,EAE7B,CACF,yGDRA,IAAA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OACA,SAAS,EAAe,CAAiB,CAAE,CAAc,EACvD,IAAM,EAAU,EAAA,aAAmB,CAAC,GAC9B,EAAW,AAAC,IAChB,GAAM,UAAE,CAAQ,CAAE,GAAG,EAAS,CAAG,EAC3B,EAAQ,EAAA,OAAa,CAAC,IAAM,EAAS,OAAO,MAAM,CAAC,IACzD,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAQ,EAAf,MAAuB,CAAE,OAAE,EAAO,UAAS,EACjE,SACA,EAAS,WAAW,CAAG,EAAoB,WAOpC,CAAC,EANR,SAAS,AAAY,CAAY,EAC/B,IAAM,EAAU,EAAA,UAAgB,CAAC,GACjC,GAAI,EAAS,OAAO,EACpB,GAAuB,KAAK,IAAxB,EAA2B,OAAO,CACtC,OAAU,AAAJ,MAAU,CAAC,EAAE,EAAE,EAAa,yBAAyB,EAAE,EAAkB,EAAE,CAAC,CACpF,EAC8B,AAChC,CACA,SAAS,EAAmB,CAAS,CAAE,EAAyB,EAAE,EAChE,IAAI,EAAkB,EAAE,CAqBlB,EAAc,KAClB,IAAM,EAAgB,EAAgB,GAAG,CAAC,AAAC,GAClC,EAAA,aAAmB,CAAC,IAE7B,OAAO,SAAS,AAAS,CAAK,EAC5B,IAAM,EAAW,GAAO,CAAC,EAAU,EAAI,EACvC,OAAO,EAAA,OAAa,CAClB,IAAM,CAAC,CAAE,CAAC,CAAC,OAAO,EAAE,EAAA,CAAW,CAAC,CAAE,CAAE,GAAG,CAAK,CAAE,CAAC,EAAU,CAAE,CAAS,EAAE,CAAC,CACvE,CAAC,EAAO,EAAS,CAErB,CACF,EAEA,OADA,EAAY,SAAS,CAAG,EACjB,CAjCP,SAAS,AAAe,CAAiB,CAAE,CAAc,EACvD,IAAM,EAAc,EAAA,aAAmB,CAAC,GAClC,EAAQ,EAAgB,MAAM,CACpC,EAAkB,IAAI,EAAiB,EAAe,CACtD,IAAM,EAAW,AAAC,IAChB,GAAM,CAAE,OAAK,UAAE,CAAQ,CAAE,GAAG,EAAS,CAAG,EAClC,EAAU,GAAO,CAAC,EAAU,EAAE,CAAC,EAAM,EAAI,EACzC,EAAQ,EAAA,OAAa,CAAC,IAAM,EAAS,OAAO,MAAM,CAAC,IACzD,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAAC,EAAQ,EAAf,MAAuB,CAAE,OAAE,WAAO,CAAS,EACjE,SACA,EAAS,WAAW,CAAG,EAAoB,WAQpC,CAAC,EAPR,SAAS,AAAY,CAAY,CAAE,CAAK,EACtC,IAAM,EAAU,GAAO,CAAC,EAAU,EAAE,CAAC,EAAM,EAAI,EACzC,EAAU,EAAA,UAAgB,CAAC,GACjC,GAAI,EAAS,OAAO,EACpB,GAAuB,KAAK,IAAxB,EAA2B,OAAO,CACtC,OAAM,AAAI,MAAM,CAAC,EAAE,EAAE,EAAa,yBAAyB,EAAE,EAAkB,EAAE,CAAC,CACpF,EAC8B,AAChC,EAcwB,AAE1B,SAAS,AAAqB,GAAG,CAAM,EACrC,IAAM,EAAY,CAAM,CAAC,EAAE,CAC3B,GAAsB,IAAlB,EAAO,MAAM,CAAQ,OAAO,EAChC,IAAM,EAAc,KAClB,IAAM,EAAa,EAAO,GAAG,CAAC,AAAC,IAAkB,CAC/C,SAAU,CADoC,GAE9C,UAAW,EAAa,SAAS,AACnC,CAAC,GACD,OAAO,SAA2B,AAAlB,CAAgC,EAC9C,IAAM,EAAa,EAAW,MAAM,CAAC,CAAC,EAAa,UAAE,CAAQ,WAAE,CAAS,CAAE,IAExE,IAAM,EADa,AACE,EADO,EACG,CAAC,CAAC,OAAO,EAAE,EAAA,CAAW,CAAC,CACtD,MAAO,CAAE,GAAG,CAAW,CAAE,GAAG,CAAY,AAAC,CAC3C,EAAG,CAAC,GACJ,OAAO,EAAA,OAAa,CAAC,IAAO,AAAD,EAAG,CAAC,CAAC,OAAO,EAAE,EAAU,SAAS,CAAA,CAAE,CAAC,CAAE,CAAW,CAAC,EAAG,CAAC,EAAW,CAC9F,CACF,EAEA,OADA,EAAY,SAAS,CAAG,EAAU,SAAS,CACpC,CACT,EArB+C,KAAgB,GAAwB,AACvF,qEEpDA,IAWI,EAXJ,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MCJA,SAAS,EAAe,CAAQ,EAC9B,IAAM,EAAc,EAAA,MAAY,CAAC,GAIjC,OAHA,EAAA,SAAe,CAAC,KACd,EAAY,OAAO,CAAG,CACxB,GACO,EAAA,OAAa,CAAC,IAAM,CAAC,GAAG,IAAS,EAAY,OAAO,MAAM,GAAO,EAAE,CAC5E,oCDCA,IAAA,EAAA,EAAA,CAAA,CAAA,OAEI,EAAiB,0BAIjB,EAA0B,EAAA,aAAmB,CAAC,CAChD,OAAwB,CAAhB,GAAoB,IAC5B,MADqB,iCACmC,CAAhB,GAAoB,IAC5D,MADqD,GAC3B,CAAhB,GAAoB,GAChC,GACI,EAAmB,EAFE,AAEF,UAAgB,CACrC,CAAC,EAAO,KACN,GAAM,CACJ,+BAA8B,CAAK,iBACnC,CAAe,CACf,sBAAoB,gBACpB,CAAc,mBACd,CAAiB,WACjB,CAAS,CACT,GAAG,EACJ,CAAG,EACE,EAAU,EAAA,UAAgB,CAAC,GAC3B,CAAC,EAAM,EAAQ,CAAG,EAAA,QAAc,CAAC,MACjC,EAAgB,GAAM,eAAiB,YAAY,SACnD,EAAG,EAAM,CAAG,EAAA,QAAc,CAAC,CAAC,GAC5B,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,AAAC,GAAU,EAAQ,IAChE,EAAS,MAAM,IAAI,CAAC,EAAQ,MAAM,EAClC,CAAC,EAA6C,CAAG,IAAI,EAAQ,sCAAsC,CAAC,CAAC,KAAK,CAAC,CAAC,GAC5G,EAAoD,EAAO,OAAO,CAAC,GACnE,EAAQ,EAAO,EAAO,OAAO,CAAC,GAAQ,CAAC,EACvC,EAA8B,EAAQ,sCAAsC,CAAC,IAAI,CAAG,EACpF,EAAyB,GAAS,EAClC,EAAqB,AA4F/B,SAAS,AAAsB,CAAoB,CAAE,EAAgB,YAAY,QAAQ,EACvF,IAAM,EAA2B,EAAe,GAC1C,EAA8B,EAAA,MAAY,EAAC,GAC3C,EAAiB,EAAA,MAAY,CAAC,KACpC,GAmCA,OAlCA,EAAA,SAAe,CAAC,KACd,IAAM,EAAoB,AAAC,IACzB,GAAI,EAAM,MAAM,EAAI,CAAC,EAA4B,OAAO,CAAE,CACxD,IAAI,EAA4C,WAC9C,EAnIiB,2BAoIf,WACA,EACA,EACA,CAAE,UAAU,CAAK,EAErB,EAEM,EAAc,CAAE,cAAe,CAAM,EACjB,SAAS,CAA/B,EAAM,WAAW,EACnB,EAAc,mBAAmB,CAAC,QAAS,EAAe,OAAO,EACjE,EAAe,OAAO,CAAG,EACzB,EAAc,gBAAgB,CAAC,QAAS,EAAe,OAAO,CAAE,CAAE,MAAM,CAAK,IAE7E,GAEJ,MACE,CADK,CACS,mBAAmB,CAAC,QAAS,EAAe,OAAO,EAEnE,EAA4B,OAAO,EAAG,CACxC,EACM,EAAU,OAAO,UAAU,CAAC,KAChC,EAAc,gBAAgB,CAAC,cAAe,EAChD,EAAG,GACH,MAAO,KACL,OAAO,YAAY,CAAC,GACpB,EAAc,mBAAmB,CAAC,cAAe,GACjD,EAAc,mBAAmB,CAAC,QAAS,EAAe,OAAO,CACnE,CACF,EAAG,CAAC,EAAe,EAAyB,EACrC,CAEL,qBAAsB,IAAM,EAA4B,OAAO,EAAG,CACpE,CACF,EAvIqD,AAAC,IAChD,IAAM,EAAS,EAAM,MAAM,CACrB,EAAwB,IAAI,EAAQ,QAAQ,CAAC,CAAC,IAAI,CAAC,AAAC,GAAW,EAAO,QAAQ,CAAC,IAChF,IAA0B,IAC/B,IAAuB,GACvB,IAAoB,GAChB,AAAC,EAAM,GAH2C,aAG3B,EAAE,MAC/B,EAAG,GACG,EAgIV,AAhIyB,SAgIhB,AAAgB,CAAc,CAAE,EAAgB,YAAY,QAAQ,EAC3E,IAAM,EAAqB,EAAe,GACpC,EAA4B,EAAA,MAAY,EAAC,GAa/C,OAZA,EAAA,SAAe,CAAC,KACd,IAAM,EAAc,AAAC,IACf,EAAM,MAAM,EAAI,CAAC,EAA0B,OAAO,EAAE,AAEtD,EA5KY,2BA4KiB,KAAe,EADxB,CAAE,cAAe,CAAM,EACqB,AAAa,CAC3E,SAAU,EACZ,EAEJ,EAEA,OADA,EAAc,gBAAgB,CAAC,UAAW,GACnC,IAAM,EAAc,mBAAmB,CAAC,UAAW,EAC5D,EAAG,CAAC,EAAe,EAAmB,EAC/B,CACL,eAAgB,IAAM,EAA0B,OAAO,EAAG,EAC1D,cAAe,IAAM,EAA0B,OAAO,CAAG,EAC3D,CACF,EAnJyC,AAAC,IACpC,IAAM,EAAS,EAAM,MAAM,EACH,IAAI,EAAQ,QAAQ,CAAC,CAAC,IAAI,CAAC,AAAC,GAAW,EAAO,QAAQ,CAAC,MAE/E,IAAiB,GACjB,IAAoB,GACf,AAAD,EAAO,gBAAgB,EAAE,MAC/B,EAAG,GAwCH,OE9FJ,AF8FW,AAvCP,SEvDK,AAAiB,CAAmB,CAAE,EF8FvB,AE9FuC,YAAY,QAAQ,EACjF,IAAM,EAAkB,EAAe,GACvC,EAAA,SAAe,CAAC,KACd,IAAM,EAAgB,AAAC,IACH,UAAU,CAAxB,EAAM,GAAG,EACX,EAAgB,EAEpB,EAEA,OADA,EAAc,gBAAgB,CAAC,UAAW,EAAe,CAAE,SAAS,CAAK,GAClE,IAAM,EAAc,mBAAmB,CAAC,UAAW,EAAe,CAAE,SAAS,CAAK,EAC3F,EAAG,CAAC,EAAiB,EAAc,CACrC,EF4CsB,AAAD,IACQ,IAAU,EAAQ,MAAM,CAAC,IAAI,CAAG,IAEvD,IAAkB,GACd,CAAC,EAAM,gBAAgB,EAAI,IAC7B,EAAM,KADkC,SACpB,GACpB,KAEJ,EAAG,GACH,EAAA,SAAe,CAAC,KACd,GAAK,CAAD,CAUJ,IAVW,GACP,IAC0D,GAAG,CAA3D,EAAQ,mBADmB,mBACmB,CAAC,IAAI,GACrD,EAA4B,EAAc,IAAI,CAAC,KAAK,CAAC,aAAa,CAClE,EAAc,IAAI,CAAC,KAAK,CAAC,aAAa,CAAG,QAE3C,EAAQ,sCAAsC,CAAC,GAAG,CAAC,IAErD,EAAQ,MAAM,CAAC,GAAG,CAAC,GACnB,IACO,KACD,GAAuF,GAAG,CAA3D,EAAQ,sCAAsC,CAAC,IAAI,GACpF,EAAc,IAAI,CAAC,KAAK,CAAC,aAAa,CAAG,CAAA,CAE7C,CACF,EAAG,CAAC,EAAM,EAAe,EAA6B,EAAQ,EAC9D,EAAA,SAAe,CAAC,IACP,KACA,IACL,EADW,AACH,MAAM,CAAC,MAAM,CAAC,GACtB,EAAQ,sCAAsC,CAAC,MAAM,CAAC,GACtD,IACF,EACC,CAAC,EAAM,EAAQ,EAClB,EAAA,SAAe,CAAC,KACd,IAAM,EAAe,IAAM,EAAM,CAAC,GAElC,OADA,SAAS,gBAAgB,CAAC,EAAgB,GACnC,IAAM,SAAS,mBAAmB,CAAC,EAAgB,EAC5D,EAAG,EAAE,EACkB,CAAA,EAAA,EAAA,GAAA,AAAG,EACxB,EAAA,SAAS,CAAC,GAAG,CACb,CACE,GAAG,CAAU,CACb,IAAK,EACL,MAAO,CACL,cAAe,EAA8B,EAAyB,OAAS,OAAS,KAAK,EAC7F,GAAG,EAAM,KAAK,AAChB,EACA,eAAgB,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,cAAc,CAAE,EAAa,cAAc,EACtF,cAAe,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,aAAa,CAAE,EAAa,aAAa,EACnF,qBAAsB,CAAA,EAAA,EAAA,oBAAA,AAAoB,EACxC,EAAM,oBAAoB,CAC1B,EAAmB,oBAAoB,CAE3C,EAEJ,GAoFF,SAAS,IACP,IAAM,EAAQ,IAAI,YAAY,GAC9B,SAAS,aAAa,CAAC,EACzB,CACA,SAAS,EAA6B,CAAI,CAAE,CAAO,CAAE,CAAM,CAAE,UAAE,CAAQ,CAAE,EACvE,IAAM,EAAS,EAAO,aAAa,CAAC,MAAM,CACpC,EAAQ,IAAI,YAAY,EAAM,CAAE,SAAS,EAAO,YAAY,SAAM,CAAO,GAC3E,GAAS,EAAO,gBAAgB,CAAC,EAAM,EAAS,CAAE,KAAM,EAAK,GAC7D,EACF,CAAA,EAAA,EAAA,GADY,wBACZ,AAA2B,EAAC,EAAQ,GAEpC,EAAO,aAAa,CAAC,EAEzB,CA/FA,EAAiB,WAAW,CA1GC,EA0GE,iBAiB/B,AAf6B,EAAA,UAAgB,CAAC,CAAC,EAAO,KACpD,IAAM,EAAU,EAAA,UAAgB,CAAC,GAC3B,EAAM,EAAA,MAAY,CAAC,MACnB,EAAe,CAAA,EAAA,EAAA,eAAe,AAAf,EAAgB,EAAc,GAUnD,OAAO,AATP,EAAA,SAAe,CAAC,CASI,IARlB,IAAM,EAAO,EAAI,OAAO,CACxB,GAAI,EAEF,IAFQ,GACR,EAAQ,QAAQ,CAAC,GAAG,CAAC,GACd,KACL,EAAQ,QAAQ,CAAC,MAAM,CAAC,EAC1B,CAEJ,EAAG,CAAC,EAAQ,QAAQ,CAAC,EACE,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,SAAS,CAAC,GAAG,CAAE,CAAE,GAAG,CAAK,CAAE,IAAK,CAAa,EAC1E,GACuB,WAAW,CAhBhB,EAgBmB,6EGrIrC,IAAA,EAAuB,EAAA,CAAA,CAAA,EAAX,GAGR,EAAQ,EAeZ,IAlBuB,KAkBd,IAED,EAAA,SAAA,CAAU,CAFQ,IAGtB,CADoB,GACd,EAAa,SAAS,gBAAA,CAAiB,0BAA0B,EAKvE,OAJA,SAAS,IAAA,CAAK,qBAAA,CAAsB,aAAc,CAAA,CAAW,CAAC,CAAA,EAAK,KACnE,SAAS,GAD2E,CAAC,AAC5E,CAAK,qBAAA,CAAsB,YAAa,CAAA,CAAW,CAAC,CAAA,EAAK,KAClE,IAEO,KACD,AAAU,CADH,EACM,AAJgE,CAAC,GAKhF,SAAS,gBAAA,CAAiB,0BAA0B,EAAE,OAAA,CAAQ,AAAC,GAAS,EAAK,MAAA,CAAO,CAAC,EAEvF,GACF,CACF,EAAG,CAAC,CAAC,CAEP,CAEA,SAAS,IAEP,IAAM,EAAU,SAAS,AAFC,aAED,CAAc,MAAM,EAO7C,OANA,EAAQ,YAAA,CAAa,yBAA0B,EAAE,EACjD,EAAQ,QAAA,CAAW,EACnB,EAAQ,KAAA,CAAM,OAAA,CAAU,OACxB,EAAQ,KAAA,CAAM,OAAA,CAAU,IACxB,EAAQ,KAAA,CAAM,QAAA,CAAW,QACzB,EAAQ,KAAA,CAAM,aAAA,CAAgB,OACvB,CACT,oDC9CA,IAAA,EAAuB,EAAA,CAAA,CAAA,EAAX,GACZ,EAAgC,EAAA,CAAvB,AAAuB,CAAA,EADT,IAEvB,EAA0B,EAAA,CAAjB,AAAiB,CAAA,OAC1B,EAA+B,CAFC,CAED,CAAtB,AAAsB,CAAA,GADL,GAyMtB,EAAA,EAAA,CAAA,CAAA,OAtME,EAFyB,AAEJ,8BACrB,EAAuB,gCACvB,EAAgB,CAAE,SAAS,EAAO,YAAY,CAAK,EAwCnD,EAAmB,EAAA,UAAA,CAA+C,CAAC,EAAO,KAC9E,GAAM,CACJ,QAF6F,AAEtF,CAAA,SACP,GAAU,CAAA,CACV,iBAAkB,CAAA,CAClB,mBAAoB,CAAA,CACpB,GAAG,EACL,CAAI,EACE,CAAC,EAAW,EAAY,CAAU,EAAA,OAAV,CAAU,CAA6B,IAAI,EACnE,EAAA,CAAA,EAAmB,EAAA,cAAA,EAAe,GAClC,EAAA,CAAA,EAAqB,EAAA,UADiC,IACjC,EAAe,GACpC,EAA8B,EAAA,MAAA,CAA2B,IAAI,EAC7D,EAAA,AAF0D,CAE1D,EAAe,EAAA,eAAA,EAAgB,EAAc,AAAC,GAAS,EAAa,IAAI,AAExE,CAFyE,CAEtD,EAAA,MAAA,CAAO,CAC9B,QAAQ,EACR,QACE,AADM,IACN,CAAK,MAAA,EAAS,CAChB,EACA,SAAS,AACP,IAAA,CAAK,MAAA,EAAS,CAChB,CACF,CAAC,EAAE,OAAA,CAGG,EAAA,SAAA,CAAU,KACd,CADoB,EAChB,EAAS,CACX,IAASA,EAAT,SAAuB,CAAA,EAAmB,AACxC,GAAI,EAAW,MAAA,EAAU,CAAC,EAAW,OACrC,CADqC,GAC/B,EAAS,EAAM,MAAA,CACjB,EAAU,QAAA,CAAS,GACrB,EAAsB,CADK,GAAG,GACR,CAAU,EAEhC,EAAM,EAAsB,OAAA,CAAS,CAAE,QAAQ,CAAK,CAAC,CAEzD,EAESC,EAAT,SAAwB,CAAA,EAAmB,AACzC,GAAI,EAAW,MAAA,EAAU,CAAC,EAAW,OACrC,CADqC,GAC/B,EAAgB,EAAM,aAAA,AAYN,KAAM,CAAA,EAAxB,IAIA,AAAC,EAAU,QAAA,CAAS,IACtB,EAAM,EAAsB,OAAA,CAAS,CAAE,QAAQ,CAAK,CAAC,CADlB,CAGvC,EAH0C,AAgB1C,CARSC,QAQA,gBAAA,CAAiB,UAAWF,GACrC,SAAS,EADyC,cACzC,CAAiB,WAAYC,GACtC,IAAM,EAAmB,IAAI,EADuB,eATpD,AAU8CC,SAVrB,CAAA,EAA6B,AAEpD,GADuB,AACnB,CAQuD,QAT3B,aAAA,GACT,SAAS,IAAA,CAAM,AACtC,CADsC,GACtC,IAAW,KAAY,EACjB,EAAS,MADmB,MACnB,CAAa,MAAA,CAAS,EAAG,CAAA,EAAM,EAEhD,GAOA,IATyD,GAOrD,GAAW,EAAiB,KAAjB,EAAiB,CAAQ,EAAW,CAAE,WAAW,EAAM,SAAS,CAAK,CAAC,EAE9E,KACL,CADW,QACF,mBAAA,CAAoB,UAAWF,GACxC,SAAS,EAD4C,iBAC5C,CAAoB,WAAYC,GACzC,EAAiB,UAAA,AADsC,CAC3B,CAC9B,CACF,CACF,EAAG,CAAC,EAAS,EAAW,EAAW,MAAM,CAAC,EAEpC,EAAA,SAAA,CAAU,KACd,CADoB,EAChB,EAAW,CACb,EAAiB,GAAA,CAAI,GACrB,IAAM,EAA2B,CADF,QACW,aAAA,CAG1C,GAAI,CAAC,AAFuB,EAAU,QAAA,CAAS,GAErB,CACxB,IAAM,EAAa,IAAI,UAH8C,EAGlC,EAAoB,GACvD,EAAU,QAD0D,QAC1D,CAAiB,EAAoB,GAC/C,EAAU,WADqD,EACrD,CAAc,GACnB,EAAW,KADkB,WAClB,EAAkB,CAChC,AA2EV,SAAS,AAAW,CAAA,CA3EC,AA2E0B,QAAE,GAAS,CAAA,CAAM,CAAI,CAAC,CAAA,EAAG,AACtE,IAAM,EAA2B,SAAS,aAAA,CAC1C,IAAA,IAAW,KAAa,EAEtB,GADA,EAAM,EAAW,EADiB,MACf,CAAO,CAAC,EACvB,SAAS,aAAA,GAAkB,EAA0B,MAE7D,EAsHS,AAvMwB,EAAsB,GAuMxC,MAAA,AAvMiD,CAAC,AAuM3C,AAAC,GAAS,AAxH6B,AAwHZ,GAAG,KAAf,OAAA,EAvM+B,CAAE,QAAQ,CAAK,CAAC,EACtE,SAAS,aAAA,GAAkB,GAC7B,EAAM,GAGZ,CAEA,KALqB,CAKd,KACL,CADW,CACD,IAPiD,eAOjD,CAAoB,EAAoB,GAKlD,WAAW,EALuD,GAMhE,CADe,GACT,EAAe,IAAI,YAAY,EAAsB,GAC3D,EAAU,QAD8D,QAC9D,CAAiB,EAAsB,GACjD,EAAU,aADyD,AACzD,CAAc,GACpB,AAAC,EAAa,OADkB,SAClB,EAAkB,AAClC,EAAM,GAA4B,SAAS,IAAA,CAAM,CAAE,QAAQ,CAAK,CAAC,EAGnE,EAAU,mBAAA,CAAoB,EAAsB,GAEpD,EAAiB,MAAA,CAAO,EAC1B,EAAG,CAAC,CAHoE,AAI1E,CACF,CACF,EAJ0C,AAIvC,CAAC,EAAW,EAAkB,EAAoB,EAAW,EAGhE,IAAM,EAHyD,AAGnC,EAAA,WAAA,CAC1B,AAAC,IACC,GAAI,CAAC,EADyB,CACjB,CAAC,GACV,EAAW,GADQ,GACR,CAAQ,AADA,CACA,MAEvB,IAAM,EAAyB,QAAd,EAAM,GAAA,EAAiB,CAAC,EAAM,MAAA,EAAU,CAAC,EAAM,OAAA,EAAW,CAAC,EAAM,OAAA,CAC5E,EAAiB,SAAS,aAAA,CAEhC,GAAI,GAAY,EAAgB,CAC9B,IAAME,EAAY,EAAM,aAAA,CAClB,CAAC,EAAO,EAAI,CAAI,AA8C9B,CA9C0B,QA8CjB,AAAiB,CAAA,EAAwB,AAChD,IAAM,EAAa,EAAsB,GAGzC,MAHkD,AAG3C,CAFO,EAAY,EAAY,GACzB,EAAY,EAAW,EADW,KACX,CAAQ,EAAG,GAC5B,AACrB,EAnD+CA,GACL,CAgDgB,EAhDP,EAMpC,AAAD,EAAO,AAPmC,QAOnC,EAAY,IAAmB,EAG/B,EAAM,EAH+B,MAG/B,EAAY,IAAmB,IAC9C,EAAM,CAD+C,aAC/C,CAAe,EACjB,GAAM,EAAA,AAAM,EAAM,CAAE,QAAQ,CAAK,CAAC,IAJtC,EAAM,cAAA,CAAe,EACjB,GAAM,EAAM,AAAN,EAAa,CAAE,QAAQ,CAAK,CAAC,GAJrC,IAAmBA,GAAW,EAAM,MAAN,QAAM,CAAe,CAU3D,CACF,EACA,CAAC,EAAM,EAAS,EAAW,MAAM,CAAA,EAGnC,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,EAAD,OAAC,CAAU,GAAA,CAAV,CAAc,SAAU,CAAA,EAAK,GAAG,CAAA,CAAY,IAAK,EAAc,UAAW,CAAA,CAAe,CAE9F,CAAC,EAwCD,SAAS,EAAsB,CAAA,EAAwB,AACrD,IAAM,EAAuB,CAAC,CAAA,CACxB,EAAS,SAAS,gBAAA,CAAiB,EAAW,WAAW,YAAA,CAAc,CAC3E,WAAY,AAAC,IACX,IAAM,CADmB,CACc,UAAjB,EAAK,OAAA,EAAqC,WAAd,EAAK,IAAA,QACvD,AAAI,EAAK,QAAA,EAAY,EAAK,MAAA,EAAU,EAAsB,WAAW,CAAlB,CAAA,SAAkB,CAI9D,EAAK,QAAA,EAAY,EAAI,WAAW,aAAA,CAAgB,WAAW,WACpE,AADoE,CAEtE,CAAC,EACD,KAAO,EAAO,QAAA,CAAS,GAAG,EAAM,IAAA,CAAK,EAAO,WAA0B,EAGtE,OAAO,CACT,CAMA,SAAS,EAAY,CAAA,CAAyB,CAAA,EAAwB,AACpE,IAAA,IAAW,KAAW,EAEpB,GAAI,CAAC,AAIT,GANkC,MAMzB,AAAS,CAAA,CAAmB,MAAE,CAAA,CAAK,EAA2B,AACrE,GAA0C,SAAU,EAAhD,iBAAiB,GAAM,CAAF,SAAE,CAAyB,OAAO,EAC3D,KAAO,AAEL,GAAI,CAAS,KAAA,OAAa,IAAS,CAAA,EAAM,CAF9B,AAE8B,CACzC,GAAuC,GADS,IACD,EAA3C,iBAAiB,GAAM,CAAF,MAAE,CAAoB,OAAO,EACtD,EAAO,EAAK,aAAA,AACd,CACA,OAAO,CACT,EAbkB,EAAS,CAAE,KAAM,CAAU,CAAC,EAAG,OAAO,CAExD,CAiBA,SAAS,EAAM,CAAA,CAAkC,QAAE,GAAS,CAAA,CAAM,CAAI,CAAC,CAAA,EAAG,AAExE,GAAI,GAAW,EAAQ,KAAA,CAAO,OAC5B,IAAM,EAA2B,SAAS,aAAA,CAE1C,EAAQ,KAAA,CAAM,CAAE,eAAe,CAAK,CAAC,EAEjC,IAAY,GAVX,CADkB,EAWuC,KAXvC,EAAmE,AAWrB,MAV7C,SAUoB,SAVA,WAAY,GAUkB,GACxE,EAAQ,MAAA,CAAO,CACnB,CACF,CA5FA,EAAW,WAAA,CAhMc,EAgMA,WAmGzB,IAAM,EAAmB,AAEzB,SAAS,EAEP,IAAI,EAAyB,CAAC,CAAA,CAE9B,GAN8C,GAMvC,CACL,IAAI,CAAA,EAA2B,AAE7B,AAP4B,IAOtB,EAAmB,CAAA,CAAM,CAAC,CAAA,AAC5B,KAAe,GACjB,GAAkB,MAAM,EAI1B,CADA,EAAQ,CAJ6B,CAIjB,EAAO,EAAU,EAC/B,OAAA,CAAQ,EAChB,EAEA,MAH0B,CAGnB,CAAA,EAA2B,AAChC,EAAQ,EAAY,EAAO,GAC3B,CAAA,CAAM,CAAC,CAAA,EAAG,CAD2B,MACpB,CACnB,CACF,CACF,IAEA,SAAS,EAAe,CAAA,CAAY,CAAA,EAAS,AAC3C,IAAM,EAAe,CAAC,GAAG,EAAK,CACxB,EADwB,AAChB,EAAa,OAAA,CAAQ,GAInC,CAJuC,MACnC,AAAU,CAAA,GAAI,IAChB,EAAa,MAAA,CAAO,EAAO,CAAC,EAEvB,CACT,yDClVA,IAAA,EAAuB,EAAA,CAAA,CAAA,EAAX,GASNC,EAAkB,MATD,MASa,SAAiB,EAAA,eAAA,CAAkB,KAAO,CAAD,8CCT7E,IAAA,EAAuB,EAAA,CAAA,CAAA,EAAX,GACZ,EAAgC,EAAA,CAAvB,AAAuB,CAAA,EADT,KAIjB,EAAc,CAAA,CAAc,UAAU,CAHZ,GAGY,CAAK,EAAE,QAAA,CAAS,CAAC,CAAA,GAAM,CAAN,GAAY,MAAA,CAAA,CACrE,EAAQ,EAEZ,SAAS,EAAM,CAAA,EAAkC,AAC/C,GAAM,CAAC,EAAI,EAAK,CAAU,EAAV,AAAU,QAAA,CAA6B,KAKvD,MALkE,AAElE,CAFmE,AAEnE,EAAA,EAAA,eAAA,EAAgB,KACV,AAAC,CADe,EACE,EAAM,AAAC,GAAY,GAAW,KAA9B,EAAqC,KAC7D,EADoE,AACjE,CADkE,AACjE,EAAgB,EACb,GAAoB,GAAK,CAAA,IADb,EACa,EAAS,EAAE,CAAA,CAAK,AAAzC,EAAyC,CAAA,AAClD,gIGXA,IAAA,EAAA,EAAA,CAAA,CAAA,KDEA,IAAM,EAAQ,CAAC,MAAO,QAAS,SAAU,OAAO,CAG1C,EAAM,KAAK,GAAG,CACd,EAAM,KAAK,GAAG,CACd,EAAQ,KAAK,KAAK,CAClB,EAAQ,KAAK,KAAK,CAClB,EAAe,IAAK,AAAC,CACzB,EAAG,EACH,EAAG,CACL,CAAC,EACK,EAAkB,CACtB,KAAM,QACN,MAAO,OACP,OAAQ,MACR,IAAK,QACP,EACM,EAAuB,CAC3B,MAAO,MACP,IAAK,OACP,EAIA,SAAS,EAAS,CAAK,CAAE,CAAK,EAC5B,MAAwB,YAAjB,OAAO,EAAuB,EAAM,GAAS,CACtD,CACA,SAAS,EAAQ,CAAS,EACxB,OAAO,EAAU,KAAK,CAAC,IAAI,CAAC,EAAE,AAChC,CACA,SAAS,EAAa,CAAS,EAC7B,OAAO,EAAU,KAAK,CAAC,IAAI,CAAC,EAAE,AAChC,CACA,SAAS,EAAgB,CAAI,EAC3B,MAAgB,MAAT,EAAe,IAAM,GAC9B,CACA,SAAS,EAAc,CAAI,EACzB,MAAgB,MAAT,EAAe,SAAW,OACnC,CACA,IAAM,EAA0B,IAAI,IAAI,CAAC,EAAtB,IAA6B,OAAlB,EAA2B,EACzD,SAAS,EAAY,CAAS,EAC5B,OAAO,EAAW,GAAG,CAAC,EAAQ,IAAc,IAAM,GACpD,CAqBA,SAAS,EAA8B,CAAS,EAC9C,OAAO,EAAU,OAAO,CAAC,aAAc,GAAa,CAAoB,CAAC,EAAU,CACrF,CACA,IAAM,EAAc,CAAC,OAAQ,QAAQ,CAC/B,EAAc,CAAC,QAAS,OAAO,CAC/B,EAAc,CAAC,MAAO,SAAS,CAC/B,EAAc,CAAC,SAAU,MAAM,CAyBrC,SAAS,EAAqB,CAAS,EACrC,OAAO,EAAU,OAAO,CAAC,yBAA0B,GAAQ,CAAe,CAAC,EAAK,CAClF,CAUA,SAAS,EAAiB,CAAO,EAC/B,MAA0B,UAAnB,CAA8B,MAAvB,EATP,CACL,IAAK,EACL,MAAO,EACP,OAAQ,EACR,KAAM,EACN,GAAG,AAIoD,CAAA,AAHzD,EAGoE,CAClE,GALU,CAKL,EACL,MAAO,EACP,OAAQ,EACR,KAAM,CACR,CACF,CACA,SAAS,EAAiB,CAAI,EAC5B,GAAM,GACJ,CAAC,GACD,CAAC,CACD,OAAK,QACL,CAAM,CACP,CAAG,EACJ,MAAO,OACL,SACA,EACA,IAAK,EACL,KAAM,EACN,MAAO,EAAI,EACX,OAAQ,EAAI,IACZ,EACA,GACF,CACF,CGrIA,SAAS,EAA2B,CAAI,CAAE,CAAS,CAAE,CAAG,EACtD,IAYI,EAZA,WACF,CAAS,UACT,CAAQ,CACT,CAAG,EACE,EAAW,EAAY,GACvB,EHwCC,IGxCgC,IACjC,EAAc,EAAc,EADZ,CAEhB,EAAO,EAAQ,GACf,EAA0B,AAAb,QACb,EAAU,EAAU,CAAC,CAAG,EAAU,KAAK,CAAG,EAAI,EAAS,KAAK,CAAG,EAC/D,EAAU,EAAU,CAAC,CAAG,EAAU,MAAM,CAAG,EAAI,EAAS,MAAM,CAAG,EACjE,EAAc,CAAS,CAAC,EAAY,CAAG,EAAI,CAAQ,CAAC,EAAY,CAAG,EAEzE,OAAQ,GACN,IAAK,MACH,EAAS,CACP,EAAG,EACH,EAAG,EAAU,CAAC,CAAG,EAAS,MAAM,AAClC,EACA,KACF,KAAK,SACH,EAAS,CACP,EAAG,EACH,EAAG,EAAU,CAAC,CAAG,EAAU,MAAM,AACnC,EACA,KACF,KAAK,QACH,EAAS,CACP,EAAG,EAAU,CAAC,CAAG,EAAU,KAAK,CAChC,EAAG,CACL,EACA,KACF,KAAK,OACH,EAAS,CACP,EAAG,EAAU,CAAC,CAAG,EAAS,KAAK,CAC/B,EAAG,CACL,EACA,KACF,SACE,EAAS,CACP,EAAG,EAAU,CAAC,CACd,EAAG,EAAU,CAAC,AAChB,CACJ,CACA,OAAQ,EAAa,IACnB,IAAK,QACH,CAAM,CAAC,EAAc,EAAI,GAAe,GAAO,EAAa,CAAC,EAAI,CAAC,EAA3B,AACvC,KACF,KAAK,MACH,CAAM,CAAC,EAAc,EAAI,GAAe,GAAO,EAAa,CAAC,GAAI,CAAC,AAEtE,CAF2C,AAG3C,OAAO,CACT,CASA,IAAM,EAAkB,MAAO,EAAW,EAAU,KAClD,GAAM,WACJ,EAAY,QAAQ,UACpB,EAAW,UAAU,YACrB,EAAa,EAAE,UACf,CAAQ,CACT,CAAG,EACE,EAAkB,EAAW,MAAM,CAAC,SACpC,EAAM,MAAM,CAAmB,MAAlB,EAAS,KAAK,CAAW,KAAK,EAAI,EAAS,KAAK,CAAC,EAAA,CAAS,CACzE,EAAQ,MAAM,EAAS,eAAe,CAAC,WACzC,EACA,WACA,UACF,GACI,GACF,CAAC,GACD,CAAC,CACF,CAAG,EAA2B,EAAO,EAAW,GAC7C,EAAoB,EACpB,EAAiB,CAAC,EAClB,EAAa,EACjB,IAAK,IAAI,EAAI,EAAG,EAAI,EAAgB,MAAM,CAAE,IAAK,CAC/C,GAAM,MACJ,CAAI,IACJ,CAAE,CACH,CAAG,CAAe,CAAC,EAAE,CAChB,CACJ,EAAG,CAAK,CACR,EAAG,CAAK,MACR,CAAI,OACJ,CAAK,CACN,CAAG,MAAM,EAAG,GACX,IACA,EACA,iBAAkB,EAClB,UAAW,WACX,iBACA,QACA,WACA,EACA,SAAU,WACR,WACA,CACF,CACF,GACA,EAAa,MAAT,EAAgB,EAAQ,EAC5B,EAAa,MAAT,EAAgB,EAAQ,EAC5B,EAAiB,CACf,GAAG,CAAc,CACjB,CAAC,EAAK,CAAE,CACN,GAAG,CAAc,CAAC,EAAK,CACvB,GAAG,CAAI,AACT,CACF,EACI,GAAS,GAAc,IAAI,CAC7B,IACqB,UAAjB,AAA2B,OAApB,IACL,EAAM,SAAS,EAAE,CACnB,EAAoB,EAAM,SAAA,AAAS,EAEjC,EAAM,KAAK,EAAE,AACf,IAAwB,IAAhB,EAAM,KAAK,CAAY,MAAM,EAAS,eAAe,CAAC,WAC5D,WACA,EACA,UACF,GAAK,EAAM,KAAA,AAAK,EAEjB,GACC,CAAC,GACD,CAAC,CACF,CAAG,EAA2B,EAAO,EAAmB,IAAI,AAE/D,EAAI,CAAC,EAET,CACA,MAAO,GACL,IACA,EACA,UAAW,WACX,EACA,gBACF,CACF,EAUA,eAAe,EAAe,CAAK,CAAE,CAAO,EAC1C,IAAI,CACY,MAAK,GAAG,CAApB,IACF,EAAU,EAAC,EAEb,GAAM,GACJ,CAAC,CACD,GAAC,UACD,CAAQ,OACR,CAAK,UACL,CAAQ,UACR,CAAQ,CACT,CAAG,EACE,UACJ,EAAW,mBAAmB,cAC9B,EAAe,UAAU,gBACzB,EAAiB,UAAU,aAC3B,GAAc,CAAK,SACnB,EAAU,CAAC,CACZ,CAAG,EAAS,EAAS,GAChB,EAAgB,EAAiB,GAEjC,EAAU,CAAQ,CAAC,EADa,YACC,CADpB,EAAgC,YAAc,WACb,EAAe,CAC7D,EAAqB,EAAiB,MAAM,EAAS,eAAe,CAAC,CACzE,QAAS,AAAC,AAAuG,OAAtG,EAAwB,MAAM,CAAuB,MAAtB,EAAS,SAAS,CAAW,KAAK,EAAI,EAAS,SAAS,CAAC,EAAA,CAAQ,CAAC,EAAY,EAAgC,EAAU,EAAQ,cAAc,EAAK,EAA7C,IAAI,AAA+C,CAAgC,MAA/B,EAAS,kBAAkB,CAAW,KAAK,EAAI,EAAS,kBAAkB,CAAC,EAAS,SAAQ,CAAC,UACjS,eACA,WACA,CACF,IACM,EAAO,AAAmB,eAAa,GAC3C,IACA,EACA,MAAO,EAAM,QAAQ,CAAC,KAAK,CAC3B,OAAQ,EAAM,QAAQ,CAAC,MAAM,AAC/B,EAAI,EAAM,SAAS,CACb,EAAe,MAAM,CAA6B,MAA5B,EAAS,eAAe,CAAW,KAAK,EAAI,EAAS,eAAe,CAAC,EAAS,SAAQ,CAAC,CAC7G,EAAe,MAAM,CAAuB,MAAtB,EAAS,SAAS,CAAW,KAAK,EAAI,EAAS,SAAS,CAAC,EAAA,CAAa,EAAM,MAAM,CAAC,AAAqB,QAAZ,QAAQ,CAAW,KAAK,EAAI,EAAS,QAAQ,CAAC,EAAA,CAAa,EAAM,CACvL,EAAG,EACH,EAAG,CACL,EAIM,EAJF,AAIsB,EAAiB,EAAS,qDAAqD,CAAG,MAAM,EAAS,qDAAqD,CAAC,CAC/K,gBACA,eACA,WACA,CACF,GAAK,GACL,MAAO,CACL,IAAK,CAAC,EAAmB,GAAG,CAAG,EAAkB,GAAG,CAAG,EAAc,GAAA,AAAG,EAAI,EAAY,CAAC,CACzF,OAAQ,AAAC,GAAkB,MAAM,CAAG,EAAmB,MAAM,CAAG,EAAc,MAAA,AAAM,EAAI,EAAY,CAAC,CACrG,KAAM,CAAC,EAAmB,IAAI,CAAG,EAAkB,IAAI,CAAG,EAAc,IAAI,AAAJ,EAAQ,EAAY,CAAC,CAC7F,MAAO,CAAC,EAAkB,KAAK,CAAG,EAAmB,KAAK,CAAG,EAAc,KAAA,AAAK,EAAI,EAAY,CAAC,AACnG,CACF,CA+TA,SAAS,EAAe,CAAQ,CAAE,CAAI,EACpC,MAAO,CACL,IAAK,EAAS,GAAG,CAAG,EAAK,MAAM,CAC/B,MAAO,EAAS,KAAK,CAAG,EAAK,KAAK,CAClC,OAAQ,EAAS,MAAM,CAAG,EAAK,MAAM,CACrC,KAAM,EAAS,IAAI,CAAG,EAAK,KAAK,AAClC,CACF,CACA,SAAS,EAAsB,CAAQ,EACrC,OAAO,EAAM,IAAI,CAAC,GAAQ,CAAQ,CAAC,EAAK,EAAI,EAC9C,CA8LA,IAAM,EAA2B,IAAI,IAAI,CAAC,GAAtB,IAA8B,MAAM,CAAzB,CAK/B,eAAe,EAAqB,CAAK,CAAE,CAAO,EAChD,GAAM,WACJ,CAAS,UACT,CAAQ,UACR,CAAQ,CACT,CAAG,EACE,EAAM,MAAM,CAAmB,MAAlB,EAAS,KAAK,CAAW,KAAK,EAAI,EAAS,KAAK,CAAC,EAAS,SAAQ,CAAC,CAChF,EAAO,EAAQ,GACf,EAAY,EAAa,GACzB,EAAwC,MAA3B,EAAY,GACzB,EAAgB,EAAY,GAAG,CAAC,GAAQ,CAAC,EAAI,EAC7C,EAAiB,GAAO,EAAa,CAAC,EAAI,EAC1C,EAAW,EAAS,EAAS,GAG/B,UACF,CAAQ,WACR,CAAS,eACT,CAAa,CACd,CAAuB,UAApB,OAAO,EAAwB,CACjC,SAAU,EACV,UAAW,EACX,cAAe,IACjB,EAAI,CACF,SAAU,EAAS,QAAQ,EAAI,EAC/B,UAAW,EAAS,SAAS,EAAI,EACjC,cAAe,EAAS,aAAa,AACvC,EAIA,OAHI,GAAsC,UAAU,AAAnC,OAAO,IACtB,EAA0B,QAAd,EAAsC,CAAC,EAAjB,EAAqB,CAAA,EAElD,EAAa,CAClB,EAAG,EAAY,EACf,EAAG,EAAW,CAChB,EAAI,CACF,EAAG,EAAW,EACd,EAAG,EAAY,CACjB,CACF,CJpwBA,SAAS,EAAY,CAAI,SACvB,AAAI,oBAqBN,EArBa,GACF,CAAC,EAAK,CADG,OACK,EAAI,EAAA,CAAE,CAAE,WAAW,GAKnC,WACT,CACA,SAAS,EAAU,CAAI,EACrB,IAAI,EACJ,MAAO,CAAS,MAAR,GAA8D,AAA9C,OAAC,EAAsB,EAAK,aAAa,AAAb,EAAyB,KAAK,EAAI,EAAoB,WAAA,AAAW,GAAK,MAC5H,CACA,SAAS,EAAmB,CAAI,EAC9B,IAAI,EACJ,OAAO,AAAmF,OAAlF,EAAO,CAEjB,AAFkB,SAET,AAAO,CAAK,EAEjB,OAAO,GAJc,GAAQ,EAAK,aAAa,CAAG,EAAK,QAAQ,AAAR,GAAa,OAAO,QAAA,AAAQ,EAAY,KAAK,EAAI,EAAK,eACjH,AADgI,CAQhI,SAAS,EAAU,CAAK,EAEpB,QAGJ,CAaA,IAAM,EAA4C,IAAI,IAAI,CAAC,SAAU,WAAhC,AAA2C,EAChF,SAAS,AADuC,EACrB,CAAO,EAChC,GAAM,UACJ,CAAQ,WACR,CAAS,WACT,CAAS,SACT,CAAO,CACR,CAAG,EAAiB,GACrB,MAAO,kCAAkC,IAAI,CAAC,EAAW,EAAY,IAAc,CAAC,EAA6B,GAAG,CAAC,EACvH,CACA,IAAM,EAA6B,IAAI,IAAI,CAAC,KAAtB,GAA+B,KAAM,GAA1B,EAA+B,EAI1D,EAAoB,CAAC,gBAAiB,SAAS,CACrD,SAAS,EAAW,CAAO,EACzB,OAAO,EAAkB,IAAI,CAAC,IAC5B,GAAI,CACF,OAAO,EAAQ,OAAO,CAAC,EACzB,CAAE,MAAO,EAAI,CACX,OAAO,CACT,CACF,EACF,CACA,IAAM,EAAsB,CAAC,YAAa,YAAa,QAAS,SAAU,cAAc,CAClF,EAAmB,CAAC,YAAa,YAAa,QAAS,SAAU,cAAe,SAAS,CACzF,EAAgB,CAAC,QAAS,SAAU,SAAU,UAAU,CAC9D,SAAS,EAAkB,CAAY,EACrC,IAAM,EAAS,IACT,EAAiE,EAIvE,EAJY,KAIL,EAAoB,GAJL,CAIS,CAAC,GAAS,GAAG,CAAC,EAAM,EAAkB,GAJ/B,MAIgB,AAAwB,CAArB,CAAC,EAAM,KAAyB,CAAD,CAAK,CAJtC,YAImD,EAAyB,WAAtB,AAAiC,EAA7B,GAAkC,UAArB,EAA0B,CAAC,KAAW,EAAI,GAAL,WAAmB,EAA0B,SAAvB,AAAgC,EAA5B,GAAiC,WAAnB,EAAwB,CAAC,KAAW,EAAI,GAAL,GAAW,EAAkB,SAAf,AAAwB,EAApB,GAAyB,GAAnB,EAAwB,EAAiB,IAAI,CAAC,GAAS,CAAC,EAAI,UAAU,EAAI,EAAA,CAAE,CAAE,QAAQ,CAAC,KAAW,EAAc,IAAI,CAAC,GAAS,CAAC,EAAI,OAAO,EAAI,EAAA,CAAE,CAAE,QAAQ,CAAC,GACna,CAaA,SAAS,UACP,AAAmB,aAAf,OAAO,MAAuB,CAAC,IAAI,QAAQ,EAAE,AAC1C,IAAI,GAD6C,KACrC,CAAC,0BAA2B,OACjD,CACA,IAAM,EAAwC,IAAI,IAAI,CAAC,OAAQ,OAAQ,EAAtC,UAAkD,CAAvC,CAC5C,SAAS,EAAsB,CAAI,EACjC,OAAO,EAAyB,GAAG,CAAC,EAAY,GAClD,CACA,SAAS,EAAiB,CAAO,EAC/B,OAAO,EAAU,GAAS,gBAAgB,CAAC,EAC7C,CACA,SAAS,EAAc,CAAO,QAOrB,CACL,WAAY,EAAQ,OAAO,CAC3B,UAAW,EAAQ,OAAO,AAC5B,CACF,CACA,SAAS,EAAc,CAAI,EACzB,GAAI,AAAsB,QAAQ,GAAlB,GACd,OAAO,EAET,IAAM,EAEN,EAAK,KADL,OACiB,EAEjB,EADA,AACK,UAAU,EAlFN,EAmFT,EAGA,EAAmB,GACnB,GANwB,IAMjB,AAAqC,CAC9C,CAWA,OAhByB,EAgBhB,EAZa,AAYQ,CAAI,CAAE,CAAI,CAAE,CAAe,EACvD,CArB4D,EAQ9B,CAa1B,CACS,KAdwB,CAcnB,GAAG,AAdoB,CAcrC,IACF,EAAO,EAAA,AAAE,EAEa,KAAK,GAAG,CAA5B,IACF,GAAkB,CAAA,EAEpB,IAAM,EAlBR,AAkB6B,SAlBpB,EAA2B,CAAI,EACtC,IAAM,EAAa,EAAc,UACjC,AAAI,EAAsB,GACjB,EAAK,QADyB,KACZ,CAAG,EAAK,aAAa,CAAC,IAAI,CAAG,EAAK,IAAI,CAK1D,EAA2B,EACpC,EASwD,GAChD,EAAS,IAAwB,CAA+C,OAA9C,EAAuB,EAAK,OAA9B,MAA8B,AAAa,EAAY,KAAK,EAAI,EAAqB,IAAA,AAAI,EACzH,EAAM,EAAU,GACtB,GAAI,EAAQ,CACV,IAAM,EAAe,EAAgB,GACrC,OAAO,EAAK,MAAM,CAAC,EAAK,EAAI,cAAc,EAAI,EAAE,CAAE,EAAkB,GAAsB,EAAqB,EAAE,CAAE,GAAgB,EAAkB,EAAqB,GAAgB,EAAE,CAC9L,CACA,OAAO,EAAK,MAAM,CAAC,EAAoB,EAAqB,EAAoB,EAAE,CAAE,GACtF,CACA,SAAS,EAAgB,CAAG,EAC1B,OAAO,EAAI,MAAM,EAAI,OAAO,cAAc,CAAC,EAAI,MAAM,EAAI,EAAI,YAAY,CAAG,IAC9E,COzJA,SAAS,EAAiB,CAAO,EAC/B,IAAM,EAAM,EAAmB,GAG3B,EAAQ,WAAW,EAAI,KAAK,GAAK,EACjC,EAAS,WAAW,EAAI,MAAM,GAAK,EACjC,KACA,EAAc,EAAY,EAAQ,CADtB,UACiC,CAAG,EAChD,CAF0B,CAEX,EAAY,EAAQ,YAAY,CAAG,EAClD,EAAiB,EAAM,KAAW,GAAe,EAAM,KAAY,EAKzE,OAJI,IACF,EAAQ,EACR,EAAS,GAEJ,CACL,EALkB,aAMlB,EACA,EAAG,CACL,CACF,CAEA,SAAS,GAAc,CAAO,EAC5B,OAAO,CAAC,CAAqB,EAAQ,OAAnB,OAAiC,AACrD,CAEA,EAHwD,OAG/C,GAAS,CAAO,EACvB,IAAM,EAAa,GAAc,GAC7B,CAAC,CACH,OAAO,EAAa,EAuBxB,CAEA,CA1BqB,GA0Bf,GAAyB,EAAa,GAC5C,EA3BkC,EA0BhB,KACT,GAAiB,CAAO,EADJ,AAE3B,IAAM,EAAM,EAAU,UACtB,AAAI,AAAC,KAAe,EAAI,OAAL,OAAmB,CAG/B,CAHiC,AAItC,EAAG,EAAI,cAAc,CAAC,UAAU,CAChC,EAAG,EAAI,cAAc,CAAC,SAAS,AACjC,EALS,EAMX,CAWA,SAAS,GAAsB,CAAO,CAAE,CAAY,CAAE,CAAe,CAAE,CAAY,OAC7E,AAAiB,MAAK,GAAG,KAC3B,GAAe,CAAA,EAEO,KAAK,GAAG,CAA5B,IACF,GAAkB,CAAA,EAEpB,IAAM,EAAa,EAAQ,qBAAqB,GAC1C,EAAa,GAAc,GAC7B,EAAQ,EAAa,GACrB,IACE,GAKF,GAAQ,GAAS,CANH,CAMG,GALD,AAQpB,IAAM,EAAgB,CA5BlB,AAAY,KAAK,GAAG,EADe,EA6BkB,KA7BX,AAE5C,EAF8C,CAEpC,CAAA,OAEiB,GAAW,AAyBkC,IAzBT,EAyBpB,EAzB8B,AAJP,GAO7D,AAH8E,GAAV,AAyBe,GAAiB,GAAc,EAAa,GAClI,EAAI,CAAC,EAAW,IAAI,CAAG,EAAc,CAAC,EAAI,EAAM,CAAC,CACjD,EAAI,CAAC,EAAW,GAAG,CAAG,GAAe,AAAD,EAAK,EAAM,CAAC,CAChD,EAAQ,EAAW,KAAK,CAAG,EAAM,CAAC,CAClC,EAAS,EAAW,MAAM,CAAG,EAAM,CAAC,CACxC,GAAI,EAAY,CACd,IAAM,EAAM,EAAU,GAChB,EAAgF,EAClF,EAAa,EACb,EAAgB,EAAgB,AAFlB,GAGlB,KAAO,GAAiB,GAAgB,EAHN,EAGoB,GAAY,CAChE,IAAM,AAJoC,EAItB,GAAS,GACvB,EAAa,EAAc,IALyB,UAAU,OAKd,GAChD,EAAM,EAAmB,GACzB,EAAO,EAAW,IAAI,CAAG,CAAC,EAAc,UAAU,CAAG,WAAW,EAAI,WAAW,CAAC,EAAI,EAAY,CAAC,CACjG,EAAM,EAAW,GAAG,CAAG,CAAC,EAAc,SAAS,CAAG,WAAW,EAAI,WAAU,CAAC,CAAI,EAAY,CAAC,CACnG,GAAK,EAAY,CAAC,CAClB,GAAK,EAAY,CAAC,CAClB,GAAS,EAAY,CAAC,CACtB,GAAU,EAAY,CAAC,CACvB,GAAK,EACL,GAAK,EAEL,EAAgB,EADhB,EAAa,EAAU,GAEzB,CACF,CACA,KAHoC,EAG7B,EAAiB,OACtB,SACA,IACA,IACA,CACF,EACF,CAIA,SAAS,GAAoB,CAAO,CAAE,CAAI,EACxC,IAAM,EAAa,EAAc,GAAS,UAAU,QAC/C,AAAL,EAGO,EAAK,AAHR,EAAO,EAGK,CAAG,EAFV,GAAsB,EAAmB,IAAU,IAAI,CAAG,CAGrE,CAEA,SAAS,GAAc,CAAe,CAAE,CAAM,EAC5C,IAAM,EAAW,EAAgB,qBAAqB,GAGtD,MAAO,CACL,EAHQ,EAAS,IAAI,CAAG,EAAO,UAAU,CAAG,GAAoB,EAAiB,GAIjF,EAHQ,EAAS,GAAG,CAAG,EAAO,SAAS,AAIzC,CACF,CAoIA,SAAS,GAAkC,CAAO,CAAE,CAAgB,CAAE,CAAQ,EAC5E,IAAI,EACJ,GAAyB,YAAY,CAAjC,EACF,EAhEJ,AAgEW,SAhEc,AAAhB,CAAuB,CAAE,CAAQ,EACxC,IAAM,EAAM,EAAU,GAChB,EAAO,EAAmB,GAC1B,EAAiB,EAAI,cAAc,CACrC,EAAQ,EAAK,WAAW,CACxB,EAAS,EAAK,YAAY,CAC1B,EAAI,EACJ,EAAI,EACR,GAAI,EAAgB,CAClB,EAAQ,EAAe,KAAK,CAC5B,EAAS,EAAe,MAAM,CAC9B,IAAM,EAAsB,KACxB,CAAC,GAAuB,GAAoC,UAAb,CAAa,GAAS,CACvE,EAAI,EAAe,UAAU,CAC7B,EAAI,EAAe,SAAS,CAEhC,CACA,IAAM,EAAmB,GAAoB,GAI7C,GAAI,GAAoB,EAAG,CACzB,IAAM,EAAM,EAAK,aAAa,CACxB,EAAO,EAAI,IAAI,CACf,EAAa,iBAAiB,GAC9B,EAAsC,eAAnB,EAAI,UAAU,EAAoB,WAAW,EAAW,UAAU,EAAI,WAAW,EAAW,WAAW,GAAK,EAC/H,EADmI,AACpG,KAAK,GAAG,CAAC,EAAK,WAAW,CAAG,EAAK,WAAW,CAAG,GAChF,GA5Bc,KA6BhB,GAAS,CAAA,CAEb,MAAW,CAAJ,OAGL,GAAS,CAAA,CAN2B,CAQtC,MAL+B,AAKxB,OACL,CATmD,CAUnD,MAP4C,KAQ5C,IACA,CACF,CACF,EAuB2B,EAAS,QAC3B,GAAyB,YAAY,CAAjC,EACT,EAAO,AAzFX,SAAyB,AAAhB,CAAuB,EAC9B,IAAM,EAAO,EAAmB,GAC1B,EAAS,EAAc,GACvB,EAAO,EAAQ,aAAa,CAAC,IAAI,CACjC,EAAQ,EAAI,EAAK,WAAW,CAAE,EAAK,WAAW,CAAE,EAAK,WAAW,CAAE,EAAK,WAAW,EAClF,EAAS,EAAI,EAAK,YAAY,CAAE,EAAK,YAAY,CAAE,EAAK,YAAY,CAAE,EAAK,YAAY,EACzF,EAAI,CAAC,EAAO,UAAU,CAAG,GAAoB,GAC3C,EAAI,CAAC,EAAO,SAAS,CAI3B,MAH2C,OAAO,CAA9C,EAAmB,GAAM,SAAS,GACpC,GAAK,EAAI,EAAK,WAAW,CAAE,EAAK,WAAW,EAAI,CAAA,EAE1C,OACL,SACA,IACA,IACA,CACF,CACF,EAwE2B,EAAmB,QACrC,EAEA,EAFI,AAGT,IAAM,EAAgB,GAAiB,CAHpB,EAInB,EAAO,CACL,EAAG,EAAiB,CAAC,CAAG,EAAc,CAAC,CACvC,EAAG,EAAiB,AANgB,CAMf,CAAG,EAAc,CAAC,CACvC,MAAO,EAAiB,KAAK,CAC7B,OAAQ,EAAiB,MAAM,AACjC,EACF,CACA,OAAO,EAAiB,EAC1B,CA4HA,SAAS,GAAmB,CAAO,EACjC,MAAgD,WAAzC,EAAmB,GAAS,QACrC,AAD6C,CAG7C,SAAS,GAAoB,CAAO,CAAE,CAAQ,EACxC,CAAC,CACH,OAAO,IAeX,CAIA,CApBqB,QAoBZ,GAAgB,CApBQ,AAoBD,CAAE,CAAQ,MP1YlB,EO2YtB,IAAM,CP3YuB,CO2YjB,EArBsC,AAqB5B,GACtB,GAAI,EAAW,CAtB4C,EAuBzD,MAvBiE,CAuB1D,AADgB,EAGrB,CAAC,AAAwB,CAzB2C,CA0BtE,IAAI,EAAkB,EA1ByD,AA0B3C,GACpC,CAFiB,IAEV,GAAmB,CAAC,EAAsB,IAAkB,CP5a5D,EOgbL,EAAkB,EAAc,EAClC,CACA,OAAO,CACT,CASF,CAEA,IAAM,GAAkB,eAAgB,CAAI,EAC1C,IAAM,EAAoB,IAAI,CAAC,eAAe,EAAI,GAC5C,EAAkB,IAAI,CAAC,aAAa,CACpC,EAAqB,MAAM,EAAgB,EAAK,QAAQ,EAC9D,MAAO,CACL,UAjGJ,AAiGe,SAjGN,AAA8B,CAAO,CAAE,CAAY,CAAE,CAAQ,QACpE,IAAM,KAAwC,MACxC,EAAkB,EAAmB,GACrC,EAAuB,MAFG,IAEhB,EACV,EAAO,GAAsB,GAAS,EAAM,EAAS,GACvD,EAAS,CACX,WAAY,EACZ,UAAW,CACb,EACM,EAAU,EAAa,GAO7B,GAAI,GAA2B,CAAC,GAA2B,CAAC,EAI1D,IAHkC,GADiC,MAC/D,EAAY,IAA4B,EAAkB,EAAA,GAC5D,AAD8E,GACrE,EAAc,EAAA,EAErB,EAAyB,CAC3B,IAAM,EAAa,GAAsB,GAAc,EAAM,EAAS,GACtE,EAAQ,CAAC,CAAG,EAAW,CAAC,CAAG,EAAa,UAAU,CAClD,EAAQ,CAAC,CAAG,EAAW,CAAC,CAAG,EAAa,SAAS,AACnD,MAAW,CAAJ,YAVyB,EAc9B,EAJ0B,CAIf,CAAC,GAA2B,IAdzC,EAAQ,CAAC,CAAG,GAAoB,EAAA,EAiBlC,EAH4D,EAGtD,GAAa,GAAoB,GAA4B,EAAmD,EAAa,GAAtD,GAAc,EAAiB,CAAtE,EAGtC,MAAO,CACL,EAHQ,AADwD,EACnD,IAAI,CAAG,EAAO,UAAU,CAAG,EAAQ,CAAC,CAAG,EAAW,CAAC,CAIhE,EAHQ,EAAK,GAAG,CAAG,EAAO,SAAS,CAAG,EAAQ,CAAC,CAAG,EAAW,CAAC,CAI9D,MAAO,EAAK,KAAK,CACjB,OAAQ,EAAK,MAAM,AACrB,CACF,EAyD6C,EAAK,SAAS,CAAE,MAAM,EAAkB,EAAK,QAAQ,EAAG,EAAK,QAAQ,EAC9G,SAAU,CACR,EAAG,EACH,EAAG,EACH,MAAO,EAAmB,KAAK,CAC/B,OAAQ,EAAmB,MAAM,AACnC,CACF,CACF,EAMM,GAAW,CACf,sDAtVF,SAAS,AAAsD,CAAI,UACjE,GAAI,UACF,CAAQ,MACR,CAAI,CACJ,cAAY,UACZ,CAAQ,CACT,CAAG,EACE,EAAU,AAAa,YACvB,EAAkB,EAAmB,GACrC,IAAW,GAAW,EAAW,EAAS,QAAQ,EACxD,EAD4D,CACxD,IAAiB,GAAmB,GAAY,EAClD,OAD2D,AACpD,EAET,IAAI,EAAS,CACX,WAAY,EACZ,UAAW,CACb,EACI,EAAQ,EAAa,GACnB,EAAU,EAAa,GACvB,GP3Ie,EO2IyB,EPzIrC,CAFiB,GO4ItB,IAA2B,CAAC,GAA2B,CAAC,CAAA,GAAS,EADrC,AAEI,SAA9B,EAAY,IAA4B,EAAkB,EAAA,GAAkB,CAC9E,EAAS,EAAc,EAAA,IAEP,KAOpB,IAAM,GAAa,GAPgB,AAOI,GAA4B,EAAmD,EAAa,GAAtD,GAAc,EAAiB,CAAtE,EACtC,MAAO,CACL,EAFgE,IAEzD,EAAK,KAAK,CAAG,EAAM,CAAC,CAC3B,OAAQ,EAAK,MAAM,CAAG,EAAM,CAAC,CAC7B,EAAG,EAAK,CAAC,CAAG,EAAM,CAAC,CAAG,EAAO,UAAU,CAAG,EAAM,CAAC,CAAG,EAAQ,CAAC,CAAG,EAAW,CAAC,CAC5E,EAAG,EAAK,CAAC,CAAG,EAAM,CAAC,CAAG,EAAO,SAAS,CAAG,EAAM,CAAC,CAAG,EAAQ,CAAC,CAAG,EAAW,CAAC,AAC7E,CACF,EAiTE,mBAAA,EACA,gBAvJF,SAAS,AAAgB,CAAI,EAC3B,GAAI,SACF,CAAO,UACP,CAAQ,cACR,CAAY,UACZ,CAAQ,CACT,CAAG,EAEE,EAAoB,IADoB,sBAAb,EAAmC,EAAW,GAAW,EAAE,CAxC9F,AAwCiG,SAxCxF,AAA4B,CAAO,CAAE,CAAK,QACjD,IAAM,EAAe,EAAM,GAAG,CAAC,GAC/B,GAAI,EACF,OAAO,EAET,GAHkB,CAGd,EAAS,EAAqB,EAAS,EAAE,EAAE,GAAO,MAAM,CAAC,KAAM,cAAU,OAEvE,AAF8E,EAEpB,UAFgC,AAEzE,EAAmB,GAAS,GAFqD,KAE7C,CACvD,EAAc,EAAiB,EAAc,GAAW,EAoB5D,SAjBiB,EAgBjB,EAAM,GAAG,CAAC,EAAS,GACZ,CACT,EAlBmC,AA6B0F,CA7BzF,CA6BkG,IAAI,CAAC,EAAE,EAAI,EAAE,CAAC,MAAM,CAAC,EA7BjG,CA8BA,EAAa,CAC/D,EAAwB,CAAiB,CAAC,EAAE,CAC5C,EAhCgE,AAgCjD,EAAkB,MAAM,CAAC,CAAC,EAAS,KACtD,IAAM,EAAO,GAAkC,EAAS,EAAkB,GAK1E,OAJA,EAAQ,GAAG,CAAG,EAAI,EAAK,GAAG,CAAE,EAAQ,GAAG,EACvC,EAAQ,KAAK,CAAG,EAAI,EAAK,KAAK,CAAE,EAAQ,KAAK,EAC7C,EAAQ,MAAM,CAAG,EAAI,EAAK,MAAM,CAAE,EAAQ,MAAM,EAChD,EAAQ,IAAI,CAAG,EAAI,EAAK,IAAI,CAAE,EAAQ,IAAI,EACnC,CACT,EAAG,GAAkC,EAAS,EAAuB,IACrE,MAAO,CACL,MAAO,EAAa,KAAK,CAAG,EAAa,IAAI,CAC7C,OAAQ,EAAa,MAAM,CAAG,EAAa,GAAG,CAC9C,EAAG,EAAa,IAAI,CACpB,EAAG,EAAa,GAAG,AACrB,CACF,kBAgIE,mBACA,GACA,eAnTF,SAAS,AAAe,CAAO,EAC7B,OAAO,MAAM,IAAI,CAAC,EAAQ,cAAc,GAC1C,EAkTE,cAjIF,SAAS,AAAc,CAAO,EAC5B,GAAM,OACJ,CAAK,QACL,CAAM,CACP,CAAG,EAAiB,GACrB,MAAO,OACL,EACA,QACF,CACF,WAyHE,GACA,UAAA,EACA,MAdF,SAAS,AAAM,CAAO,EACpB,MAAiD,QAA1C,EAAmB,GAAS,SAAS,AAC9C,CAaA,EAEA,SAAS,GAAc,CAAC,CAAE,CAAC,EACzB,OAAO,EAAE,CAAC,GAAK,EAAE,CAAC,EAAI,EAAE,CAAC,GAAK,EAAE,CAAC,EAAI,EAAE,KAAK,GAAK,EAAE,KAAK,EAAI,EAAE,MAAM,GAAK,EAAE,MAAM,AACnF,CAuOA,IAAM,GHvgBQ,IAAY,CACxB,CGsgBY,IHvgBW,AACjB,gBACN,EACA,MAAM,GAAG,CAAK,EACZ,GAAM,GACJ,CAAC,GACD,CAAC,WACD,CAAS,OACT,CAAK,CACL,UAAQ,UACR,CAAQ,gBACR,CAAc,CACf,CAAG,EAEE,SACJ,CAAO,SACP,EAAU,CAAC,CACZ,CAAG,EAAS,EAAS,IAAU,CAAC,EACjC,GAAe,MAAX,AAAiB,EACnB,MAAO,CAAC,EAEV,IAAM,EAAgB,EAAiB,GACjC,EAAS,GACb,IACA,CACF,EACM,IHrMe,EGqMS,CAAjB,GACP,EAAS,EAAc,EHtMI,CGuM3B,EAAkB,MAAM,EAAS,aAAa,CAAC,GAC/C,EAAU,AAAS,QAGnB,EAAa,EAAU,eAAiB,cACxC,EAAU,EAAM,SAAS,CAAC,EAAO,CAAG,EAAM,SAAS,CAAC,EAAK,CAAG,CAAM,CAAC,EAAK,CAAG,EAAM,QAAQ,CAAC,EAAO,CACjG,EAAY,CAAM,CAAC,EAAK,CAAG,EAAM,SAAS,CAAC,EAAK,CAChD,EAAoB,MAAM,CAAC,AAA4B,QAAnB,eAAe,CAAW,KAAK,EAAI,EAAS,eAAe,CAAC,EAAA,CAAQ,CAC1G,EAAa,EAAoB,CAAiB,CAAC,EAAW,CAAG,CAGjE,CAAC,GAAgB,MAAM,CAAuB,IAA/B,EAAS,EAAS,SAAS,CAAW,KAAK,EAAI,EAAS,SAAS,CAAC,EAAA,CAAkB,GAAI,AACzG,EAAa,EAAS,QAAQ,CAAC,EAAW,EAAI,EAAM,QAAQ,CAAC,EAAA,AAAO,EAMtE,IAAM,EAAyB,EAAa,EAAI,CAAe,CAAC,EAAO,CAAG,EAAI,EACxE,EAAa,EAAI,CAAa,CAjBpB,AAiBqB,EAjBX,MAAQ,OAiBW,CAAE,GACzC,EAAa,EAAI,CAAa,CAjBpB,AAiBqB,EAjBX,SAAW,QAiBQ,CAAE,GAKzC,EAAM,EAAa,CAAe,CAAC,EAAO,CAAG,EAC7C,EAAS,EAAa,EAAI,CAAe,CAAC,EAAO,CAAG,GAZhC,CAYoC,CAZ1B,EAAI,EAAY,GAa9C,QAAsB,CAAb,CAAqB,IAM9B,CANe,CAMG,CAAC,EAAe,KAAK,EAA+B,MAA3B,EAAa,IAAsB,IAAW,GAAU,EAAM,SAAS,CAAC,EAAO,CAAG,GAAK,CAAD,GAAkB,EAAa,CAAA,CAAU,CAA/B,AAAmC,CAAe,CAAC,EAAO,CAAG,EAAI,EAC5M,EAAkB,EAAkB,IAAiB,EAV7C,EAU8D,CAAzB,CAAkC,EAAM,CAAvB,CACpE,MAAO,CACL,CAAC,EAAK,CAAE,CAAM,CAAC,EAAK,CAAG,EACvB,KAAM,CACJ,CAAC,EAAK,CAAE,EACR,aAAc,EAAS,EAAS,EAChC,GAAI,GAAmB,iBACrB,CACF,CACF,AADG,EAEH,MAAO,CACT,CACF,EACF,CAAC,CEnSD,IAAA,GAAA,EAAA,CAAA,CAAA,OAKI,GAH+B,AAGvB,aAHG,OAAO,SAGC,EAAA,eAAe,CAD3B,EAC8B,OADrB,EAAQ,EAK5B,SAAS,GAAU,CAAC,CAAE,CAAC,MAUjB,EACA,EACA,EAXJ,GAAI,IAAM,EACR,CADW,MACJ,EAET,GAAI,OAAO,GAAM,OAAO,EACtB,CADyB,MAClB,EAET,GAAiB,YAAb,OAAO,GAAoB,EAAE,QAAQ,KAAO,EAAE,QAAQ,GACxD,CAD4D,MACrD,EAKT,GAAI,GAAK,GAAkB,AAAb,iBAAO,EAAgB,CACnC,GAAI,MAAM,OAAO,CAAC,GAAI,CAEpB,GAAI,CADJ,EAAS,EAAE,MAAA,AAAM,IACF,EAAE,MAAM,CAAE,OAAO,EAChC,IAAK,EAAI,EAAgB,GAAR,AAAY,KAC3B,GAAI,CAAC,GAAU,CAAC,CAAC,EAAE,CAAE,CAAC,CAAC,EAAE,EACvB,CAD0B,MACnB,EAGX,OAAO,CACT,CAGA,GAAI,CADJ,EAAS,CADT,EAAO,OAAO,IAAI,CAAC,EAAA,EACL,MAAA,AAAM,IACL,OAAO,IAAI,CAAC,GAAG,MAAM,CAClC,CADoC,MAC7B,EAET,IAAK,EAAI,EAAgB,GAAR,AAAY,KAC3B,GAAI,CAAC,CAAA,EAAC,CAAA,CAAE,cAAc,CAAC,IAAI,CAAC,EAAG,CAAI,CAAC,EAAE,EACpC,CADuC,MAChC,EAGX,IAAK,EAAI,EAAQ,AAAQ,QAAI,CAC3B,IAAM,EAAM,CAAI,CAAC,EAAE,CACnB,IAAY,WAAR,IAAoB,EAAE,QAAA,AAAQ,EAAE,CAGhC,CAAC,GAAU,CAAC,CAAC,EAAI,CAAE,CAAC,CAAC,EAAI,EAC3B,CAD8B,MACvB,CAEX,CACA,MAAO,EACT,CACA,OAAO,GAAM,GAAK,GAAM,CAC1B,CAUA,SAAS,GAAW,CAAO,CAAE,CAAK,EAChC,IAAM,EAPG,EAQT,EADY,KACL,EADY,GACP,KAAK,CAAC,EAAQ,GAAO,CACnC,CAEA,SAAS,GAAa,CAAK,EACzB,IAAM,EAAM,EAAA,MAAY,CAAC,GAIzB,OAHA,GAAM,KACJ,EAAI,OAAO,CAAG,CAChB,GACO,CACT,CH9EA,IAAA,GAAA,EAAA,CAAA,CAAA,OACA,GAAA,EAAA,CAAA,CAAA,OAEI,GAAQ,EAAA,UAAgB,CAAC,CAAC,EAAO,KACnC,GAAM,UAAE,CAAQ,OAAE,EAAQ,EAAE,QAAE,EAAS,CAAC,CAAE,GAAG,EAAY,CAAG,EAC5D,MAAuB,CAAhB,AAAgB,EAAA,GAAA,GAAA,AAAG,EACxB,GADkB,AAClB,SAAS,CAAC,GAAG,CACb,CACE,GAAG,CAAU,CACb,IAAK,QACL,SACA,EACA,QAAS,YACT,oBAAqB,OACrB,SAAU,EAAM,OAAO,CAAG,EAA2B,CAAA,EAAA,GAAA,GAAA,AAAG,AAAnB,EAAoB,UAAW,CAAE,AAApB,OAA4B,gBAAiB,EACjG,EAEJ,GACA,GAAM,WAAW,CAhBN,EAgBS,MDJpB,IAAA,GAAA,EAAA,CAAA,CAAA,MACA,GAAA,EAAA,CAAA,CAAA,OAEA,GAAA,EAAA,CAAA,CAAA,MACA,GAAA,EAAA,CAAA,CAAA,OAKI,GAAc,SACd,CAAC,GAAqB,GAAkB,CAAG,CAAA,EAAA,GAAA,kBAAkB,AAAlB,EAAmB,IAC9D,CAAC,GAAgB,GAAiB,CAAG,GAAoB,IACzD,GAAS,AAAC,IACZ,GAAM,CAAE,eAAa,UAAE,CAAQ,CAAE,CAAG,EAC9B,CAAC,EAAQ,EAAU,CAAG,EAAA,QAAc,CAAC,MAC3C,MAAuB,CAAhB,AAAgB,EAAA,GAAA,GAAG,AAAH,EAAI,GAAgB,AAAvB,CAAyB,MAAO,SAAe,EAAQ,eAAgB,WAAW,CAAS,EACjH,EACA,GAAO,WAAW,CAAG,GACrB,IAAI,GAAc,eACd,GAAe,EAAA,UAAgB,CACjC,CAAC,EAAO,KACN,GAAM,eAAE,CAAa,YAAE,CAAU,CAAE,GAAG,EAAa,CAAG,EAChD,EAAU,GAAiB,GAAa,GACxC,EAAM,EAAA,MAAY,CAAC,MACnB,EAAe,CAAA,EAAA,GAAA,eAAA,AAAe,EAAC,EAAc,GAC7C,EAAY,EAAA,MAAY,CAAC,MAQ/B,OAPA,EAAA,SAAe,CAAC,KACd,IAAM,EAAiB,EAAU,OAAO,CACxC,EAAU,OAAO,CAAG,GAAY,SAAW,EAAI,OAAO,CAClD,IAAmB,EAAU,OAAO,EAAE,AACxC,EAAQ,cAAc,CAAC,EAAU,OAAO,CAE5C,GACO,EAAa,KAAuB,CAAA,CAAhB,CAAgB,GAAA,GAAA,AAAG,EAAC,GAAA,CAAP,QAAgB,CAAC,GAAG,CAAE,CAAE,GAAG,CAAW,CAAE,IAAK,CAAa,EACpG,GAEF,GAAa,WAAW,CAAG,GAC3B,IAAI,GAAe,gBACf,CAAC,GAAuB,GAAkB,CAAG,GAAoB,IACjE,GAAgB,EAAA,UAAgB,CAClC,CAAC,EAAO,KACN,GAAM,eACJ,CAAa,MACb,EAAO,QAAQ,YACf,EAAa,CAAC,OACd,EAAQ,QAAQ,aAChB,EAAc,CAAC,cACf,EAAe,CAAC,iBAChB,GAAkB,CAAI,mBACtB,EAAoB,EAAE,CACtB,iBAAkB,EAAuB,CAAC,QAC1C,EAAS,SAAS,CAClB,oBAAmB,CAAK,wBACxB,EAAyB,WAAW,UACpC,CAAQ,CACR,GAAG,EACJ,CAAG,EACE,EAAU,GAAiB,GAAc,GACzC,CAAC,EAAS,EAAW,CAAG,EAAA,QAAc,CAAC,MACvC,EAAe,CAAA,EAAA,GAAA,eAAA,AAAe,EAAC,EAAc,AAAC,GAAS,EAAW,IAClE,CAAC,EAAO,EAAS,CAAG,EAAA,QAAc,CAAC,MACnC,EG1EV,AH0EsB,SG1EL,AAAR,CAAe,EACtB,GAAM,CAAC,EAAM,EAAQ,CAAG,EAAA,QAAc,CAAC,KAAK,GA+B5C,MA9BA,CAAA,EAAA,GAAA,eAAA,AAAe,EAAC,KACd,GAAI,EAAS,CACX,EAAQ,CAAE,MAAO,EAAQ,WAAW,CAAE,OAAQ,EAAQ,YAAY,AAAC,GACnE,IAAM,EAAiB,IAAI,eAAe,AAAC,QAQrC,EACA,EARJ,GAAI,CAAC,MAAM,OAAO,CAAC,IAGf,CAAC,EAAQ,GAHgB,GAGV,CAFjB,CAEmB,MAGrB,IAAM,EAAQ,CAAO,CAAC,EAAE,CAGxB,GAAI,kBAAmB,EAAO,CAC5B,IAAM,EAAkB,EAAM,GAAD,UAAiB,CACxC,EAAa,MAAM,OAAO,CAAC,GAAmB,CAAe,CAAC,EAAE,CAAG,EACzE,EAAQ,EAAW,QAAD,EAAc,CAChC,EAAS,EAAW,QAAD,CAAa,AAClC,MACE,CADK,CACG,EAAQ,WAAW,CAC3B,EAAS,EAAQ,YAAY,CAE/B,EAAQ,OAAE,SAAO,CAAO,EAC1B,GAEA,OADA,EAAe,OAAO,CAAC,EAAS,CAAE,IAAK,YAAa,GAC7C,IAAM,EAAe,SAAS,CAAC,EACxC,CACE,EAAQ,IADH,CACQ,EAEjB,EAAG,CAAC,EAAQ,EACL,CACT,EHyC8B,GACpB,EAAa,GAAW,OAAS,EACjC,EAAc,GAAW,QAAU,EAEnC,EAAmD,UAAhC,OAAO,EAAoC,EAAuB,CAAE,IAAK,EAAG,MAAO,EAAG,OAAQ,EAAG,KAAM,EAAG,GAAG,CAAoB,AAAC,EACrJ,EAAW,MAAM,OAAO,CAAC,GAAqB,EAAoB,CAAC,EAAkB,CACrF,EAAwB,EAAS,MAAM,CAAG,EAC1C,EAAwB,CAC5B,QAAS,EACT,SAAU,EAAS,MAAM,CAAC,IAE1B,YAAa,CACf,EACM,MAAE,CAAI,gBAAE,EAAc,WAAE,EAAS,cAAE,EAAY,gBAAE,EAAc,CAAE,CIJ3E,AJI8E,SIJrE,AAAY,CAAO,EACtB,AAAY,KAAK,GAAG,KACtB,EAAU,EAAC,EAEb,GAAM,WACJ,EAAY,QAAQ,CACpB,WAAW,UAAU,YACrB,EAAa,EAAE,UACf,CAAQ,CACR,SAAU,CACR,UAAW,CAAiB,CAC5B,SAAU,CAAgB,CAC3B,CAAG,CAAC,CAAC,WACN,GAAY,CAAI,sBAChB,CAAoB,MACpB,CAAI,CACL,CAAG,EACE,CAAC,EAAM,EAAQ,CAAG,EAAA,QAAc,CAAC,CACrC,EAAG,EACH,EAAG,WACH,YACA,EACA,eAAgB,CAAC,EACjB,aAAc,EAChB,GACM,CAAC,EAAkB,EAAoB,CAAG,EAAA,QAAc,CAAC,EAC3D,CAAC,GAAU,EAAkB,IAC/B,EAAoB,GAEtB,GAAM,CAAC,AAHuC,EAG3B,EAAc,CAAG,EAAA,QAAc,CAAC,MAC7C,CAAC,EAAW,EAAa,CAAG,EAAA,QAAc,CAAC,MAC3C,EAAe,EAAA,WAAiB,CAAC,IACjC,IAAS,EAAa,OAAO,EAAE,CACjC,EAAa,OAAO,CAAG,EACvB,EAAc,GAElB,EAAG,EAAE,EACC,EAAc,EAAA,WAAiB,CAAC,IAChC,IAAS,EAAY,OAAO,EAAE,CAChC,EAAY,OAAO,CAAG,EACtB,EAAa,GAEjB,EAAG,EAAE,EACC,EAAc,GAAqB,EACnC,EAAa,GAAoB,EACjC,EAAe,EAAA,MAAY,CAAC,MAC5B,EAAc,EAAA,MAAY,CAAC,MAC3B,EAAU,EAAA,MAAY,CAAC,GACvB,EAAkD,MAAxB,EAC1B,EAA0B,GAAa,GACvC,EAAc,GAAa,GAC3B,EAAU,GAAa,GACvB,EAAS,EAAA,WAAiB,CAAC,KAC/B,GAAI,CAAC,EAAa,OAAO,EAAI,CAAC,EAAY,OAAO,CAC/C,CADiD,MAGnD,IAAM,EAAS,WACb,WACA,EACA,WAAY,CACd,EACI,EAAY,OAAO,EAAE,CACvB,EAAO,QAAQ,CAAG,EAAY,OAAA,AAAO,EAEvC,CC+lBoB,CAAC,EAAW,EAAU,KAI5C,IAAM,EAAQ,IAAI,IACZ,EAAgB,UACpB,GACA,GAAG,CACL,AADY,EAEN,EAAoB,CACxB,GAAG,EAAc,QAAQ,CACzB,GAAI,CACN,EACA,OAAO,EAAkB,EAAW,EAAU,CAC5C,GAAG,CAAa,CAChB,SAAU,CACZ,EACF,GDhnBoB,EAAa,OAAO,CAAE,EAAY,OAAO,CAAE,GAAQ,IAAI,CAAC,IACtE,IAAM,EAAW,CACf,GAAG,CAAI,CAKP,aAAc,AAAoB,OAAZ,OAAO,AAC/B,EACI,EAAa,OAAO,EAAI,CAAC,GAAU,EAAQ,OAAO,CAAE,KACtD,EAAQ,IADyD,GAClD,CAAG,EAClB,GAAA,SAAkB,CAAC,KACjB,EAAQ,EACV,GAEJ,EACF,EAAG,CAAC,EAAkB,EAAW,EAAU,EAAa,EAAQ,EAChE,GAAM,KACS,KAAT,GAAkB,EAAQ,OAAO,CAAC,YAAY,EAAE,CAClD,EAAQ,OAAO,CAAC,YAAY,EAAG,EAC/B,EAAQ,IAAS,CACf,EADc,CACX,CAAI,CACP,cAAc,EAChB,CAAC,EAEL,EAAG,CAAC,EAAK,EACT,IAAM,EAAe,EAAA,MAAY,EAAC,GAClC,GAAM,KACJ,EAAa,OAAO,EAAG,EAChB,KACL,EAAa,OAAO,EAAG,CACzB,GACC,EAAE,EACL,GAAM,KAGJ,GAFI,IAAa,EAAa,OAAO,CAAG,CAAA,EACpC,IAAY,EAAY,OAAO,CAAG,CAAA,EAClC,GAAe,EAAY,CAC7B,GAAI,EAAwB,OAAO,CACjC,CADmC,MAC5B,EAAwB,OAAO,CAAC,EAAa,EAAY,GAElE,GACF,CACF,EAAG,CAAC,EAAa,EAAY,EAAQ,EAAyB,EAAwB,EACtF,IAAM,EAAO,EAAA,OAAa,CAAC,IAAM,CAAC,CAChC,UAAW,EACX,SAAU,eACV,cACA,EACF,CAAC,CAAG,CAAC,EAAc,EAAY,EACzB,EAAW,EAAA,OAAa,CAAC,IAAM,CAAC,CACpC,UAAW,EACX,SAAU,EACZ,CAAC,CAAG,CAAC,EAAa,EAAW,EACvB,EAAiB,EAAA,OAAa,CAAC,KACnC,IAAM,EAAgB,CACpB,SAAU,EACV,KAAM,EACN,IAAK,CACP,EACA,GAAI,CAAC,EAAS,QAAQ,CACpB,CADsB,MACf,EAET,IAAM,EAAI,GAAW,EAAS,QAAQ,CAAE,EAAK,CAAC,EACxC,EAAI,GAAW,EAAS,QAAQ,CAAE,EAAK,CAAC,EAC9C,GAAI,EACF,MAAO,CACL,EAFW,CAER,CAAa,CAChB,UAAW,aAAe,EAAI,OAAS,EAAI,MAC3C,IAAI,AAAO,EAAS,QAAQ,KAAK,CAEjC,CAAC,AACH,EAEF,GAL4C,GAKrC,CACL,SAAU,EACV,KAAM,EACN,IAAK,CACP,CACF,EAAG,CAAC,EAAU,EAAW,EAAS,QAAQ,CAAE,EAAK,CAAC,CAAE,EAAK,CAAC,CAAC,EAC3D,OAAO,EAAA,OAAa,CAAC,IAAM,CAAC,CAC1B,GAAG,CAAI,QACP,OACA,WACA,iBACA,EACF,CAAC,CAAG,CAAC,EAAM,EAAQ,EAAM,EAAU,EAAe,CACpD,EJlJ0F,CAEpF,SAAU,QACV,UAbuB,CAaZ,EAboB,AAAU,IAAX,SAAsB,IAAM,EAAQ,EAAA,CAAE,CAcpE,qBAAsB,CAAC,GAAG,IACR,AK+fxB,SAAS,CAAW,CAAS,CAAE,CAAQ,CAAE,CAAM,CAAE,CAAO,MA0ClD,CAzCY,MAAK,GAAG,CAApB,IACF,EAAU,EAAC,EAEb,GAAM,gBACJ,GAAiB,CAAI,gBACrB,GAAiB,CAAI,eACrB,EAA0C,YAA1B,OAAO,cAA6B,CACpD,cAAc,AAAgC,mBAAzB,oBAAmC,gBACxD,GAAiB,CAAK,CACvB,CAAG,EACE,EAAc,GAAc,GAC5B,EAAY,GAAkB,EAAiB,IAAK,EAAc,EAAqB,GAAe,EAAE,IAAM,EAAqB,GAAU,CAAG,EAAE,CACxJ,EAAU,OAAO,CAAC,IAChB,GAAkB,EAAS,gBAAgB,CAAC,SAAU,EAAQ,CAC5D,SAAS,CACX,GACA,GAAkB,EAAS,gBAAgB,CAAC,SAAU,EACxD,GACA,IAAM,EAAY,GAAe,EAAc,AAlHjD,SAAS,AAAY,CAAO,CAAE,CAAM,EAClC,IACI,EADA,EAAK,KAEH,EAAO,EAAmB,GAChC,SAAS,IACP,IAAI,EACJ,aAAa,GACC,AAAd,OAAC,EAAM,CAAA,CAAE,EAAa,EAAI,UAAU,GACpC,EAAK,IACP,CA2EA,OADA,AAzEA,SAAS,EAAQ,CAAI,CAAE,CAAS,EACjB,KAAK,GAAG,CAAjB,IACF,GAAO,CAAA,EAES,KAAK,GAAG,CAAtB,IACF,GAAY,EAEd,IACA,IAAM,EAA2B,EAAQ,qBAAqB,GACxD,MACJ,CAAI,KACJ,CAAG,OACH,CAAK,QACL,CAAM,CACP,CAAG,EAIJ,GAHI,AAAC,GACH,GADS,CAGP,CAAC,GAAS,CAAC,EACb,MADqB,CAGvB,IAAM,EAAW,EAAM,GACjB,EAAa,EAAM,EAAK,WAAW,EAAI,CAAD,CAAQ,CAAA,CAAK,EAInD,EAAU,CACd,WAFiB,CAAC,EAAW,MAAQ,CAAC,EAAa,MAAQ,CAFzC,AAE0C,EAFpC,EAAK,YAAY,EAAI,CAAD,CAAO,CAAA,CAAM,EAEiB,MAAQ,CADlE,AACmE,EAD7D,GACyE,KAG/F,UAAW,EAAI,EAAG,EAAI,EAAG,KAAe,CAC1C,EACI,GAAgB,EACpB,SAAS,EAAc,CAAO,EAC5B,IAAM,EAAQ,CAAO,CAAC,EAAE,CAAC,iBAAiB,CAC1C,GAAI,IAAU,EAAW,CACvB,GAAI,CAAC,EACH,OAAO,IAEJ,EAHe,AAUlB,EAAQ,GAAO,AAPL,GAGV,EAAY,WAAW,KACrB,GAAQ,EAAO,KACjB,EAAG,IAIP,CACc,IAAV,CAAe,EAAC,GAAc,EAA0B,EAAQ,qBAAqB,KAQvF,AAR4F,IAU9F,GAAgB,CAClB,CAIA,GAAI,CACF,EAAK,IAAI,qBAAqB,EAAe,CAC3C,GAAG,CAAO,CAEV,KAAM,EAAK,aACb,AAD0B,EAE5B,CAAE,MAAO,EAAI,CACX,EAAK,IAAI,qBAAqB,EAAe,EAC/C,CACA,EAAG,OAAO,CAAC,EACb,GACQ,GACD,CACT,EA6B6D,EAAa,GAAU,KAC9E,EAAiB,CAAC,EAClB,EAAiB,KACjB,IACF,EAAiB,IAAI,KADJ,UACmB,IAClC,GAAI,CAAC,EAAW,CAAG,EACf,GAAc,EAAW,MAAM,GAAK,GAAe,IAGrD,EAAe,SAAS,CAH6C,AAG5C,GACzB,qBAAqB,GACrB,EAAiB,sBAAsB,KACrC,IAAI,CACJ,AAAsC,QAArC,EAAkB,CAAA,CAAc,EAAa,EAAgB,OAAO,CAAC,EACxE,IAEF,GACF,GACI,GAAe,CAAC,GAClB,EAAe,OAAO,CAAC,GADW,AAGpC,EAAe,OAAO,CAAC,IAGzB,IAAI,EAAc,EAAiB,GAAsB,GAAa,YAClE,GACF,AAEF,SAAS,IAHW,AAIlB,IAAM,EAAc,GAAsB,GACtC,GAAe,CAAC,GAAc,EAAa,IAC7C,IAEF,EAAc,EACd,EAJ6D,AAInD,sBAAsB,EAClC,IACA,IACO,KACL,IAAI,EACJ,EAAU,OAAO,CAAC,IAChB,GAAkB,EAAS,mBAAmB,CAAC,SAAU,GACzD,GAAkB,EAAS,mBAAmB,CAAC,SAAU,EAC3D,GACA,AAAa,SAAQ,IACrB,AAAuC,OAAtC,EAAmB,CAAA,CAAc,EAAa,EAAiB,UAAU,GAC1E,EAAiB,KACb,GACF,aADkB,QACG,EAEzB,EACF,KLpkBsC,EAAM,CAClC,eAA2C,WAA3B,CAClB,GAGF,SAAU,CACR,UAAW,EAAQ,MAAM,AAC3B,EACA,WAAY,CACV,CImLO,CAAC,EAAS,KAAU,CACjC,CADgC,EAC7B,AFofU,SAAU,CAAO,EAI9B,OAHI,AAAY,KAAK,GAAG,KACtB,GAAU,EAEL,CACL,KAAM,iBACN,EACA,MAAM,GAAG,CAAK,EACZ,IAAI,EAAuB,EAC3B,GAAM,CACJ,GAAC,GACD,CAAC,WACD,CAAS,gBACT,CAAc,CACf,CAAG,EACE,EAAa,MAAM,EAAqB,EAAO,UAIrD,AAAI,IAAe,CAAmD,OAAlD,EAAF,AAA0B,EAAe,MAAA,AAAM,EAAY,KAAK,EAAI,EAAsB,SAAS,AAAT,GAAc,AAAkD,OAAjD,EAAwB,EAAe,KAAA,AAAK,GAAa,EAAsB,eAAe,CAChN,CADkN,AACjN,EAEH,CACL,EAAG,EAAI,EAAW,CAAC,CACnB,EAAG,EAAI,EAAW,CAAC,CACnB,KAAM,CACJ,GAAG,CAAU,WACb,CACF,CACF,CACF,CACF,CACF,EEphBc,EAAQ,CACpB,QAAS,CAAC,EAAS,EAAK,CAC1B,CAAC,EJtLc,CAAE,SAAU,EAAa,EAAa,cAAe,CAAY,GACxE,GAAmB,CI4Lb,CAAC,EAAS,KAAU,CAChC,CAD+B,EFkhBnB,AEjhBT,SFihBmB,CAAO,EAI7B,OAHgB,KAAK,GAAG,CAApB,IACF,EAAU,EAAC,EAEN,CACL,KAAM,gBACN,EACA,MAAM,GAAG,CAAK,EACZ,GAAM,GACJ,CAAC,GACD,CAAC,WACD,CAAS,CACV,CAAG,EACE,CACJ,SAAU,GAAgB,CAAI,CAC9B,UAAW,GAAiB,CAAK,SACjC,EAAU,CACR,GAAI,IACF,GAAI,CACF,GAAC,GACD,CAAC,CACF,CAAG,EACJ,MAAO,GACL,IACA,CACF,CACF,CACF,CAAC,CACD,GAAG,EACJ,CAAG,EAAS,EAAS,GAChB,EAAS,GACb,IACA,CACF,EACM,EAAW,MAAM,EAAe,EAAO,GACvC,EAAY,EAAY,EAAQ,IAChC,EAAW,EAAgB,GAC7B,EAAgB,CAAM,CAAC,EAAS,CAChC,EAAiB,CAAM,CAAC,EAAU,CACtC,GAAI,EAAe,CACjB,IAAM,EAAuB,MAAb,EAAmB,MAAQ,OACrC,EAAuB,MAAb,EAAmB,SAAW,QACxC,EAAM,EAAgB,CAAQ,CAAC,EAAQ,CACvC,EAAM,EAAgB,CAAQ,CAAC,EAAQ,CAC7C,IAAsB,IAAK,EAAe,GAC5C,CACA,EAFkB,CAEd,EAAgB,CAClB,IAAM,EAAwB,MAAd,EAAoB,MAAQ,OACtC,EAAwB,MAAd,EAAoB,SAAW,QACzC,EAAM,EAAiB,CAAQ,CAAC,EAAQ,CACxC,EAAM,EAAiB,CAAQ,CAAC,EAAQ,CAC9C,EH/0BC,EG+0BsB,EH/0BlB,AAAO,EG+0BgB,EH/0BZ,AG+0B4B,GAC9C,CACA,GHj1ByB,AG+0BN,CAEb,EAAgB,EAAQ,EAAE,CAAC,CAC/B,GAAG,CAAK,CACR,CAAC,EAAS,CAAE,EACZ,CAAC,EAAU,CAAE,CACf,GACA,MAAO,CACL,GAAG,CAAa,CAChB,KAAM,CACJ,EAAG,EAAc,CAAC,CAAG,EACrB,EAAG,EAAc,CAAC,CAAG,EACrB,QAAS,CACP,CAAC,EAAS,CAAE,EACZ,CAAC,EAAU,CAAE,CACf,CACF,CACF,CACF,CACF,CACF,EExlBa,EAAQ,CACnB,QAAS,CAAC,EAAS,EAAK,CAC1B,CAAC,EJ/LgC,CACvB,UAAU,EACV,WAAW,EACX,QAAoB,YAAX,EAAuB,CIiMvB,CAAC,EAAS,IAAU,EACrC,CADoC,EACjC,AFolBc,SAAU,CAAO,EAIlC,OAHgB,KAAK,GAAG,CAApB,IACF,EAAU,CAAC,GAEN,SACL,EACA,GAAG,CAAK,EACN,GAAM,GACJ,CAAC,GACD,CAAC,WACD,CAAS,CACT,OAAK,gBACL,CAAc,CACf,CAAG,EACE,QACJ,EAAS,CAAC,CACV,SAAU,GAAgB,CAAI,CAC9B,UAAW,EAAiB,EAAI,CACjC,CAAG,EAAS,EAAS,GAChB,EAAS,GACb,IACA,CACF,EACM,EAAY,EAAY,GACxB,EAAW,EAAgB,GAC7B,EAAgB,CAAM,CAAC,EAAS,CAChC,EAAiB,CAAM,CAAC,EAAU,CAChC,EAAY,EAAS,EAAQ,GAC7B,EAAsC,UAArB,OAAO,EAAyB,CACrD,SAAU,EACV,UAAW,CACb,EAAI,CACF,SAAU,EACV,UAAW,EACX,GAAG,CAAS,AACd,EACA,GAAI,EAAe,CACjB,IAAM,EAAmB,MAAb,EAAmB,SAAW,QACpC,EAAW,EAAM,SAAS,CAAC,EAAS,CAAG,EAAM,QAAQ,CAAC,EAAI,CAAG,EAAe,QAAQ,CACpF,EAAW,EAAM,SAAS,CAAC,EAAS,CAAG,EAAM,SAAS,CAAC,EAAI,CAAG,EAAe,QAAQ,AACvF,GAAgB,EAClB,EAAgB,EACP,EAAgB,EAFG,EAG5B,EAAgB,CAAA,CAEpB,CACA,CAJuC,EAInC,EAAgB,CAClB,IAAI,EAAuB,EAC3B,IAAM,EAAM,AAAa,QAAM,QAAU,SACnC,EAAe,EAAY,GAAG,CAAC,EAAQ,IACvC,EAAW,EAAM,SAAS,CAAC,EAAU,CAAG,EAAM,QAAQ,CAAC,EAAI,CAAI,EAAD,GAAiB,AAAmD,OAAlD,EAAwB,CAA1B,CAAyC,MAAA,AAAM,EAAY,KAAK,EAAI,CAAqB,CAAC,EAAA,AAAU,GAAK,CAAI,CAAC,EAAK,EAAD,AAAgB,EAAI,EAAe,SAAA,AAAS,EAC5O,EAAW,EAAM,SAAS,CAAC,EAAU,CAAG,EAAM,SAAS,CAAC,EAAI,EAAI,CAAD,CAAgB,EAAI,CAAC,AAAoD,OAAnD,EAAyB,EAAe,MAAA,AAAM,EAAY,KAAK,EAAI,CAAsB,CAAC,EAAA,AAAU,IAAK,CAAC,CAAK,GAAD,AAAgB,EAAe,SAAS,EAAG,CAAC,CACjP,EAAiB,EACnB,EAAiB,EACR,EAAiB,EAFG,EAG7B,EAAiB,CAAA,CAErB,CACA,CAJwC,KAIjC,CACL,CAAC,EAAS,CAAE,EACZ,CAAC,EAAU,CAAE,CACf,CACF,CACF,CACF,EEppBkB,EAAQ,CACxB,QAAS,CAAC,EAAS,EAAK,CAC1B,CAAC,IJpMwD,KAAK,EACpD,GAAG,CAAqB,AAC1B,GACA,GIyMK,AJzMc,EIyMb,EAAS,KAAU,CAC/B,CAD8B,EAC3B,AF6FQ,SAAU,CAAO,EAI5B,OAHI,AAAY,KAAK,GAAG,KACtB,EAAU,EAAC,EAEN,CACL,KAAM,eACN,EACA,MAAM,GAAG,CAAK,MACR,EAAuB,EAqDrB,EAAuB,EA+Bf,EAnFd,GAAM,CACJ,WAAS,CACT,gBAAc,OACd,CAAK,kBACL,CAAgB,UAChB,CAAQ,UACR,CAAQ,CACT,CAAG,EACE,CACJ,SAAU,GAAgB,CAAI,CAC9B,UAAW,GAAiB,CAAI,CAChC,mBAAoB,CAA2B,kBAC/C,EAAmB,SAAS,CAC5B,4BAA4B,MAAM,eAClC,GAAgB,CAAI,CACpB,GAAG,EACJ,CAAG,EAAS,EAAS,GAMtB,GAAsD,AAAlD,OAAC,EAAwB,EAAe,KAAA,AAAK,GAAa,EAAsB,eAAe,CACjG,CADmG,KAC5F,CAAC,EAEV,IAAM,EAAO,EAAQ,GACf,EAAkB,EAAY,GAC9B,EAAkB,EAAQ,KAAsB,EAChD,EAAM,MAAM,CAAmB,MAAlB,EAAS,KAAK,CAAW,KAAK,EAAI,EAAS,KAAK,CAAC,EAAS,SAAQ,CAAC,CAChF,EAAqB,IAAgC,GAAmB,CAAC,EAAgB,CAAC,EAAqB,GAAkB,CH5X7I,AG4XgJ,SH5XjH,AAAtB,CAA+B,EACtC,EG2X8D,EH3XxD,EAAoB,EAAqB,GAC/C,MAAO,CAAC,EAA8B,GAAY,EAAmB,EAA8B,GAAmB,AACxH,EGyXsK,EAAA,CAAiB,CAC3K,EAA6D,AAA9B,UACjC,EAAC,GAA+B,GAClC,EAAmB,IAAI,IHvW/B,AGuWmC,SHvW1B,AAA0B,CAAS,CAAE,CAAa,CAAE,CAAS,CAAE,CAAG,CGsWH,CHrWtE,IAAM,EAAY,EAAa,GAC3B,EAAO,AAfb,SAAS,AAAY,CAAI,CAAE,CAAO,CAAE,CAAG,EACrC,OAAQ,GACN,IAAK,MACL,IAAK,SACH,GAAI,EAAK,OAAO,EAAU,EAAc,EACxC,OAAO,EAAU,EAAc,CACjC,KAAK,OACL,IAAK,QACH,OAAO,EAAU,EAAc,CACjC,SACE,MAAO,EAAE,AACb,CACF,EAGyB,EAAQ,GAA0B,UAAd,EAAuB,GAOlE,OANI,IACF,EAAO,EAAK,GADC,AACE,CAAC,GAAQ,EAAO,IAAM,GACjC,IACF,EAAO,EAAK,MAAM,CADD,AACE,EAAK,GAAG,CAAC,GAAA,GAGzB,CACT,EG6V6D,EAAkB,EAAe,EAA2B,IAEnH,IAAM,EAAa,CAAC,KAAqB,EAAmB,CACtD,EAAW,MAAM,EAAe,EAAO,GACvC,EAAY,EAAE,CAChB,EAAgB,CAAC,AAAgD,MAA/C,GAAuB,EAAe,IAAA,AAAI,EAAY,KAAK,EAAI,EAAqB,SAAA,AAAS,GAAK,EAAE,CAI1H,GAHI,GACF,EAAU,IAAI,CAAC,CAAQ,CAAC,EAAK,CADZ,CAGf,EAAgB,CAClB,IAAM,EAAQ,AHtZtB,SAAS,AAAkB,CAAS,CAAE,CAAK,CAAE,CAAG,EAClC,KAAK,GAAG,CAAhB,IACF,GAAM,CAAA,EAER,IAAM,EAAY,EAAa,GACzB,MAAiC,IACjC,EAAS,EAAc,EADP,CAElB,EAAsC,MAAlB,EAAwB,KAAe,EAAM,MAAQ,CAAf,MAAe,CAAO,CAAI,QAAU,OAAuB,UAAd,EAAwB,SAAW,MAI9I,OAHI,EAAM,SAAS,CAAC,EAAO,CAAG,EAAM,QAAQ,CAAC,EAAO,EAAE,CACpD,EAAoB,EAAqB,EAAA,EAEpC,CAAC,EAAmB,EAAqB,GAAmB,AACrE,EG0YwC,EAAW,EAAO,GAClD,EAAU,IAAI,CAAC,CAAQ,CAAC,CAAK,CAAC,EAAE,CAAC,CAAE,CAAQ,CAAC,CAAK,CAAC,EAAE,CAAC,CACvD,CAOA,GANA,EAAgB,IAAI,EAAe,WACjC,YACA,CACF,EAAE,CAGE,CAAC,EAAU,KAAK,CAAC,GAAQ,GAAQ,GAAI,CAEvC,IAAM,EAAY,AAAC,EAAkD,AAAjD,OAAC,EAAwB,EAAe,IAAA,AAAI,EAAY,KAAK,EAAI,EAAsB,KAAK,AAAL,GAAU,CAAC,EAAI,EACpH,EAAgB,CAAU,CAAC,EAAU,CAC3C,GAAI,GAEE,CAAC,AAD8C,WADlC,GACe,GAAiC,IAAoB,EAAY,IAIjG,AAFA,EAEc,KAAK,CAAC,GAAK,EAAY,AAJ6E,EAI3E,SAAS,IAAM,GAAkB,EAAE,SAAS,CAAC,EAAE,CAAG,EAAI,EAAA,AAE3F,CAFkG,KAE3F,CACL,KAAM,CACJ,MAAO,CANyD,CAOhE,UAAW,CACb,EACA,MAAO,CACL,UAAW,CACb,CACF,EAMJ,IAAI,EAAgJ,AAA/H,OAAC,EAAwB,EAAc,MAAM,CAAC,GAAK,EAAE,SAAS,CAAC,EAAE,EAAI,GAAG,IAAI,CAAC,CAAC,EAAG,IAAM,EAAE,SAAS,CAAC,EAAE,CAAG,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,AAAF,EAAc,KAAK,EAAI,EAAsB,SAAS,CAGnM,GAAI,CAAC,EACH,OAAQ,GACN,IAFiB,AAEZ,UACH,CAEE,IAAM,EASmJ,AATvI,OAAC,EAAyB,EAAc,MAAM,CAAC,IAC/D,GAAI,EAA8B,CAChC,IAAM,EAAkB,EAAY,EAAE,SAAS,EAC/C,OAAO,IAAoB,GAGP,MAApB,CACF,CACA,OAAO,CAJL,AAKJ,GAAG,GAAG,CAAC,GAAK,CAAC,EAAE,SAAS,CAAE,EAAE,SAAS,CAAC,MAAM,CAAC,GAAY,EAAW,GAAG,KALZ,CAKkB,CAAC,CAAC,EAAK,IAAa,EAAM,EAAU,GAAG,EAAE,IAAI,CAAC,CAAC,EAAG,IAAM,CAAC,CAAC,EAAE,CAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,AAAF,EAAc,KAAK,EAAI,CAAsB,CAAC,EAAE,CAC9L,IACF,EAAiB,CAAA,EAEnB,EAHe,GAIjB,CACF,IAAK,mBACH,EAAiB,CAErB,CAEF,GAAI,IAAc,EAChB,MAAO,CACL,MAAO,CAFuB,AAG5B,UAAW,CACb,CACF,CAEJ,CACA,MAAO,CAAC,CACV,CACF,CACF,EEzNY,EAAQ,CAClB,QAAS,CAAC,EAAS,EAAK,CAC1B,CAAC,EJ5M+B,CAAE,GAAG,CAAqB,AAAC,GACnD,CImNK,CAAC,EAAS,KAAU,CAC/B,CAD8B,EFuoBnB,AEtoBR,SFsoBkB,CAAO,EAI5B,OAHgB,KAAK,GAAG,CAApB,GACF,GAAU,EAAC,EAEN,CACL,KAAM,eACN,EACA,MAAM,GAAG,CAAK,MACR,EAAuB,MAmBvB,EACA,EAnBE,WACJ,CAAS,OACT,CAAK,UACL,CAAQ,UACR,CAAQ,CACT,CAAG,EACE,OACJ,EAAQ,KAAO,CAAC,CAChB,GAAG,EACJ,CAAG,EAAS,EAAS,GAChB,EAAW,MAAM,EAAe,EAAO,GACvC,EAAO,EAAQ,GACf,EAAY,EAAa,GACzB,EAAqC,MAA3B,EAAY,GACtB,OACJ,CAAK,QACL,CAAM,CACP,CAAG,EAAM,QAAQ,CAGL,QAAT,GAA2B,UAAU,CAAnB,GACpB,EAAa,EACb,EAAY,KAAgB,MAAM,CAAmB,EAA3B,IAAS,EAAS,KAAK,CAAW,KAAK,EAAI,EAAS,KAAK,CAAC,EAAS,QAAQ,CAAC,EAAK,QAAU,KAAA,CAAK,CAAI,OAAS,UAEvI,EAAY,EACZ,EAA2B,QAAd,EAAsB,MAAQ,UAE7C,IAAM,EAAwB,EAAS,EAAS,GAAG,CAAG,EAAS,MAAM,CAC/D,EAAuB,EAAQ,EAAS,IAAI,CAAG,EAAS,KAAK,CAC7D,EAA0B,EAAI,EAAS,CAAQ,CAAC,EAAW,CAAE,GAC7D,EAAyB,EAAI,EAAQ,CAAQ,CAAC,EAAU,CAAE,GAC1D,EAAU,CAAC,EAAM,cAAc,CAAC,KAAK,CACvC,EAAkB,EAClB,EAAiB,EAOrB,GANI,AAAwD,OAAvD,EAAwB,EAAM,cAAc,CAAC,KAAA,AAAK,GAAa,EAAsB,OAAO,CAAC,CAAC,EAAE,CACnG,EAAiB,CAAA,EAE0C,AAAzD,OAAC,EAAyB,EAAM,cAAc,CAAC,KAAA,AAAK,GAAa,EAAuB,OAAO,CAAC,CAAC,EAAE,CACrG,EAAkB,CAAA,EAEhB,GAAW,CAAC,EAAW,CACzB,IAAM,EAAO,EAAI,EAAS,IAAI,CAAE,GAC1B,EAAO,EAAI,EAAS,KAAK,CAAE,GAC3B,EAAO,EAAI,EAAS,GAAG,CAAE,GACzB,EAAO,EAAI,EAAS,MAAM,CAAE,GAC9B,EACF,EAAiB,EAAQ,GADd,AACmB,AAAS,CAAV,MAAe,AAAS,MAAI,EAAO,EAAO,EAAI,EAAS,IAAI,CAAE,EAAS,MAAK,CAAC,CAEzG,EAAkB,EAAS,GAAc,CAAV,GAAC,GAAuB,IAAT,EAAa,EAAO,EAAO,EAAI,EAAS,GAAG,CAAE,EAAS,OAAM,CAAC,AAE/G,CACA,MAAM,EAAM,CACV,GAAG,CAAK,gBACR,kBACA,CACF,GACA,IAAM,EAAiB,MAAM,EAAS,aAAa,CAAC,EAAS,QAAQ,SACjE,AAAJ,IAAc,EAAe,KAAK,EAAI,IAAW,EAAe,MAAM,CAC7D,CAD+D,AAEpE,MAAO,CACL,OAAO,CACT,CACF,EAEK,CAAC,CACV,CACF,CACF,EEltBY,EAAQ,CAClB,QAAS,CAAC,EAAS,EAAK,CAC1B,CAAC,EJtNY,CACH,GAAG,CAAqB,CACxB,MAAO,CAAC,UAAE,CAAQ,OAAE,CAAK,gBAAE,CAAc,iBAAE,CAAe,CAAE,IAC1D,GAAM,CAAE,MAAO,CAAW,CAAE,OAAQ,CAAY,CAAE,CAAG,EAAM,SAAS,CAC9D,EAAe,EAAS,QAAQ,CAAC,KAAK,CAC5C,EAAa,WAAW,CAAC,iCAAkC,CAAA,EAAG,EAAe,EAAE,CAAC,EAChF,EAAa,WAAW,CAAC,kCAAmC,CAAA,EAAG,EAAgB,EAAE,CAAC,EAClF,EAAa,WAAW,CAAC,8BAA+B,CAAA,EAAG,EAAY,EAAE,CAAC,EAC1E,EAAa,WAAW,CAAC,+BAAgC,CAAA,EAAG,EAAa,EAAE,CAAC,CAC9E,CACF,GACA,GAAS,CIkPH,CAAC,EAAS,IAAU,EAChC,CAD+B,EAC5B,CA1HW,GAIP,EACL,KAAM,gBACN,EACA,GAAG,CAAK,EACN,GAAM,SACJ,CAAO,SACP,CAAO,CACR,CAAsB,YAAnB,OAAO,EAAyB,EAAQ,GAAS,SACrD,AAAI,GAVC,CAAA,EAAC,CAAA,CAAE,GAUO,WAVO,CAAC,IAAI,CAAC,AAUP,EAVc,QAUJ,GAC7B,AAAuB,MAAnB,AAAyB,EAAjB,OAAO,CACV,GAAQ,CACb,QAAS,EAAQ,OAAO,SACxB,CACF,GAAG,EAAE,CAAC,GAED,CAAC,EAEV,AAAI,EACK,GAAQ,IADJ,KAET,UACA,CACF,GAAG,EAAE,CAAC,GAED,CAAC,CACV,EACF,CACF,EA4Fa,EAAQ,CACnB,QAAS,CAAC,EAAS,EAAK,CAC1B,CAAC,EJrPgC,CAAE,QAAS,EAAO,QAAS,CAAa,GACjE,GAAgB,CAAE,yBAAY,CAAY,GAC1C,GAAoB,CI2Nf,CAAC,EAAS,KAAU,CAC/B,CAD8B,EAC3B,AF2MQ,SAAU,CAAO,EAI5B,OAHgB,KAAK,GAAG,CAApB,IACF,EAAU,EAAC,EAEN,CACL,KAAM,eACN,EACA,MAAM,GAAG,CAAK,EACZ,GAAM,OACJ,CAAK,CACN,CAAG,EACE,UACJ,EAAW,iBAAiB,CAC5B,GAAG,EACJ,CAAG,EAAS,EAAS,GACtB,OAAQ,GACN,IAAK,kBACH,CAKE,IAAM,EAAU,EAJC,MAAM,EAAe,EAAO,CAC3C,EAG6B,CAH1B,CAAqB,CACxB,eAAgB,WAClB,GACyC,EAAM,SAAS,EACxD,MAAO,CACL,KAAM,CACJ,uBAAwB,EACxB,gBAAiB,EAAsB,EACzC,CACF,CACF,CACF,IAAK,UACH,CAKE,IAAM,EAAU,EAJC,MAAM,EAAe,EAAO,CAC3C,EAG6B,CAH1B,CAAqB,CACxB,aAAa,CACf,GACyC,EAAM,QAAQ,EACvD,MAAO,CACL,KAAM,CACJ,eAAgB,EAChB,QAAS,EAAsB,EACjC,CACF,CACF,CACF,QAEI,MAAO,CAAC,CAEd,CACF,CACF,CACF,EE9PY,EAAQ,CAClB,QAAS,CAAC,EAAS,EAAK,CAC1B,CAAC,EJ9NgC,CAAE,SAAU,kBAAmB,GAAG,CAAsB,AAAD,GACjF,AACH,GACM,CAAC,GAAY,GAAY,CAAG,GAA6B,IACzD,GAAe,CAAA,EAAA,GAAA,cAAA,AAAc,EAAC,GACpC,CAAA,EAAA,GAAA,eAAA,AAAe,EAAC,KACV,IACF,MAEJ,EAAG,CAAC,CAHgB,EAGF,GAAa,EAC/B,IAAM,GAAS,GAAe,KAAK,EAAE,EAC/B,GAAS,GAAe,KAAK,EAAE,EAC/B,GAAoB,GAAe,KAAK,EAAE,eAAiB,EAC3D,CAAC,GAAe,GAAiB,CAAG,EAAA,QAAc,GAIxD,MAHA,CAAA,AAGO,EAHP,GAAA,QAGoB,OAHpB,AAAe,EAAC,KACV,GAAS,GAAiB,OAAO,gBAAgB,CAAC,GAAS,MAAM,CACvE,EAAG,CAAC,EAAQ,EACW,CAAA,EAAA,GAAA,GAAA,AAAG,EACxB,MACA,CACE,IAAK,EAAK,WAAW,CACrB,oCAAqC,GACrC,MAAO,CACL,GAAG,EAAc,CACjB,UAAW,GAAe,GAAe,SAAS,CAAG,sBAErD,SAAU,cACV,OAAQ,GACP,kCAAoC,AAAF,CACjC,GAAe,eAAe,EAAE,EAChC,GAAe,eAAe,EAAE,EACjC,CAAC,IAAI,CAAC,KAIP,GAAG,GAAe,IAAI,EAAE,iBAAmB,CACzC,WAAY,SACZ,cAAe,MACjB,CAAC,AACH,EACA,IAAK,EAAM,GAAG,CACd,SAA0B,CAAhB,AAAgB,EAAA,GAAA,GAAA,AAAG,EAC3B,GACA,AAFqB,CAGnB,MAAO,EACP,cACA,cAAe,SACf,GACA,UACA,gBAAiB,GACjB,SAA0B,CAAhB,AAAgB,EAAA,GAAA,GAAA,AAAG,EAC3B,GADqB,AACrB,SAAS,CAAC,GAAG,CACb,CACE,YAAa,GACb,aAAc,GACd,GAAG,CAAY,CACf,IAAK,EACL,MAAO,CACL,GAAG,EAAa,KAAK,CAGrB,UAAW,AAAC,GAAwB,KAAK,EAAd,MAC7B,CACF,EAEJ,EAEJ,EAEJ,GAEF,GAAc,WAAW,CAAG,GAC5B,IAAI,GAAa,cACb,GAAgB,CAClB,IAAK,SACL,MAAO,OACP,OAAQ,MACR,KAAM,OACR,EACI,GAAc,EAAA,UAAgB,CAAC,SAAS,AAAa,CAAK,CAAE,CAAY,EAC1E,GAAM,eAAE,CAAa,CAAE,GAAG,EAAY,CAAG,EACnC,EAAiB,GAAkB,GAAY,GAC/C,EAAW,EAAa,CAAC,EAAe,UAAU,CAAC,CACzD,MAIkB,CAAA,AAHhB,EAGgB,GAAA,GAAA,AAAG,EACjB,OACA,CACE,IAAK,EAAe,aAAa,CACjC,MAAO,CACL,SAAU,WACV,KAAM,EAAe,MAAM,CAC3B,AAVyE,IAUpE,EAAe,MAAM,CAC1B,CAAC,EAAS,CAAE,EACZ,gBAAiB,CACf,IAAK,GACL,MAAO,MACP,OAAQ,WACR,KAAM,QACR,CAAC,CAAC,EAAe,UAAU,CAAC,CAC5B,UAAW,CACT,IAAK,mBACL,MAAO,iDACP,OAAQ,CAAC,cAAc,CAAC,CACxB,KAAM,gDACR,CAAC,CAAC,EAAe,UAAU,CAAC,CAC5B,WAAY,EAAe,eAAe,CAAG,SAAW,KAAK,CAC/D,EACA,SAA0B,CAAhB,AAAgB,EAAA,GAAA,GAAA,AAAG,EAC3B,ACvNC,GDsNoB,AAErB,CACE,GAAG,CAAU,CACb,IAAK,EACL,MAAO,CACL,GAAG,EAAW,KAAK,CAEnB,QAAS,OACX,CACF,EAEJ,EAGN,GAEA,SAAS,GAAU,CAAK,EACtB,OAAiB,OAAV,CACT,CAHA,GAAY,WAAW,CAAG,GAI1B,IAAI,GAAkB,AAAC,GAAa,EAClC,KAAM,AAD2B,0BAEjC,EACA,GAAG,CAAI,EACL,GAAM,WAAE,CAAS,OAAE,CAAK,gBAAE,CAAc,CAAE,CAAG,EACvC,EAAoB,EAAe,KAAK,EAAE,eAAiB,EAE3D,EADgB,AACH,EAAgB,EAAI,EAAQ,UAAU,CACnD,EAAc,EAAgB,EAAI,EAAQ,WAAW,CACrD,CAAC,EAAY,EAAY,CAAG,GAA6B,GACzD,EAAe,CAAE,MAAO,KAAM,OAAQ,MAAO,IAAK,MAAO,CAAC,CAAC,EAAY,CACvE,EAAe,CAAC,EAAe,KAAK,EAAE,IAAK,CAAC,CAAI,EAAa,EAC7D,EAAe,CAAC,EAAe,KAAK,EAAE,IAAK,CAAC,CAAI,EAAc,EAChE,EAAI,GACJ,EAAI,GAcR,MAbmB,UAAU,CAAzB,GACF,EAAI,EAAgB,EAAe,CAAA,EAAG,EAAa,EAAE,CAAC,CACtD,EAAI,CAAA,EAAG,CAAC,EAAY,EAAE,CAAC,EACd,AAAe,OAAO,IAC/B,EAAI,EAAgB,EAAe,CAAA,EAAG,EAAa,EAAE,CAAC,CACtD,EAAI,CAAA,EAAG,EAAM,QAAQ,CAAC,MAAM,CAAG,EAAY,EAAE,CAAC,EACtB,SAAS,CAAxB,GACT,EAAI,CAAA,EAAG,CAAC,EAAY,EAAE,CAAC,CACvB,EAAI,EAAgB,EAAe,CAAA,EAAG,EAAa,EAAE,CAAC,EAC9B,QAAQ,CAAvB,IACT,EAAI,CAAA,EAAG,EAAM,QAAQ,CAAC,KAAK,CAAG,EAAY,EAAE,CAAC,CAC7C,EAAI,EAAgB,EAAe,CAAA,EAAG,EAAa,EAAE,CAAC,EAEjD,CAAE,KAAM,GAAE,IAAG,CAAE,CAAE,CAC1B,EACF,CAAC,CACD,SAAS,GAA6B,CAAS,EAC7C,GAAM,CAAC,EAAM,EAAQ,QAAQ,CAAC,CAAG,EAAU,KAAK,CAAC,KACjD,MAAO,CAAC,EAAM,EAAM,AACtB,CACA,IAAI,GAAQ,GACR,GAAS,GACT,GAAU,GACV,GAAQ,iDMrSZ,IAAA,EAAuB,EAAA,CAAA,CAAA,EAAX,GACZ,EAAqB,CAAd,CAAc,CAAA,CAAA,EADE,KAEvB,EAA0B,EAAA,AADL,CACK,AAAjB,CAAiB,OAC1B,EAAgC,EAAA,CAAvB,AAAuB,CAAA,GADN,IAwBE,EAAA,EAAA,CAAA,CAAA,OANtB,EAjB0B,AAiBX,EAAA,UAAA,CAAuC,CAAC,EAAO,KAClE,GAAM,CAAE,QAD2E,EAChE,CAAA,CAAe,GAAG,EAAY,CAAI,EAC/C,CAAC,EAAS,EAAU,CAAU,CADa,CACb,KAAV,GAAU,EAAS,GAC7C,CAAA,CADkD,CAClD,EAAA,eAAA,EAAgB,IAAM,GAAW,GAAO,CAAH,AAAI,CAAC,EAC1C,IAAM,EAAY,GAAkB,GAAW,YAAY,UAAU,KACrE,OAAO,EACH,EAAA,OAAA,CAAS,YAAA,CAAa,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAD,QAAC,CAAU,GAAA,CAAV,CAAe,GAAG,CAAA,CAAa,IAAK,CAAA,CAAc,EAAI,GAC7E,IACN,CAAC,CAF2F,CAI5F,EAAO,WAAA,CArBa,EAqBC,yEC7BrB,IAAA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,MCC0B,EAAK,CAAC,mBAAmB,IAAI,GAAG,QAAQ,GAAG,CACvC,CAAK,CAAC,uBAAuB,IAAI,GAAG,QAAQ,GAAG,CDD7E,IAAI,EAAqB,CAAK,CAAC,uBAAuB,IAAI,GAAG,QAAQ,GAAG,EAAI,EAAA,eAAe,CAC3F,SAAS,EAAqB,MAC5B,CAAI,aACJ,CAAW,UACX,EAAW,KACX,CAAC,QACD,CAAM,CACP,EACC,GAAM,CAAC,EAAkB,EAAqB,EAAY,CAAG,AAmC/D,SAAS,AAAqB,aAC5B,CAAW,CACX,UAAQ,CACT,EACC,GAAM,CAAC,EAAO,EAAS,CAAG,EAAM,QAAQ,CAAC,GACnC,EAAe,EAAM,MAAM,CAAC,GAC5B,EAAc,EAAM,MAAM,CAAC,GAUjC,OATA,EAAmB,KACjB,EAAY,OAAO,CAAG,CACxB,EAAG,CAAC,EAAS,EACb,EAAM,SAAS,CAAC,KACV,EAAa,OAAO,GAAK,IAC3B,EAAY,CADsB,MACf,GAAG,GACtB,EAAa,OAAO,CAAG,EAE3B,EAAG,CAAC,EAAO,EAAa,EACjB,CAAC,EAAO,EAAU,EAC3B,AADuC,EAnD6C,aAChF,WACA,CACF,GACM,EAAwB,KAAK,IAAd,EACf,EAAQ,EAAe,EAAO,CAC1B,EACR,IAAM,EAAkB,EAAM,MAAM,CAAU,KAAK,IAAd,GACrC,EAAM,SAAS,CAAC,KACd,IAAM,EAAgB,EAAgB,OAAO,CAC7C,GAAI,IAAkB,EAAc,CAElC,IAAM,EAAK,EAAe,aAAe,eACzC,QAAQ,IAAI,CACV,CAAA,EAAG,EAAO,kBAAkB,EAHjB,AAGmB,EAHH,aAAe,eAGP,IAAI,EAAE,EAAG,0KAA0K,CAAC,CAE3N,CACA,EAAgB,OAAO,CAAG,CAC5B,EAAG,CAAC,EAAc,EAAO,CAC3B,CAcA,MAAO,CAAC,EAbS,EAAM,WAAW,CAChC,AAAC,IACC,GAAI,EAAc,CAChB,IAAM,EAAS,AA+BG,YAAjB,OA/ByB,AA+BlB,EA/B+B,EAAU,GAAQ,EACrD,IAAW,GACb,EAAY,CADO,MACA,GAAG,EAE1B,MACE,CADK,CACe,EAExB,EACA,CAAC,EAAc,EAAM,EAAqB,EAAY,EAEhC,AAC1B,CA0BiB,OAAO,sEEhExB,IAAI,EAAa,IAAI,QACjB,EAAoB,IAAI,QACxB,EAAY,CAAC,EACb,EAAY,EACZ,EAAa,SAAU,CAAI,EAC3B,OAAO,IAAS,EAAK,EAAN,EAAU,EAAI,EAAW,EAAK,WAAU,CAAC,AAC5D,EAwBI,EAAyB,SAAU,CAAc,CAAE,CAAU,CAAE,CAAU,CAAE,CAAgB,EAC3F,IAAI,EAvBG,AAuBkC,OAAM,CAAjC,MAAwC,CAAC,GAAkB,EAAiB,CAAC,EAAe,EAtBrG,GAAG,CAAC,SAAU,CAAM,EACrB,GAAI,EAAO,QAAQ,CAAC,GAChB,MADyB,CAClB,EAEX,IAAI,EAAkB,EAAW,UACjC,AAAI,GAAmB,EAAO,QAAQ,CAAC,GAC5B,GAEX,QAAQ,IAHiD,CAG5C,CAAC,cAAe,EAAQ,0BAcZ,CAduC,CAAQ,mBACjE,KACX,GACK,MAAM,CAAC,SAAU,CAAC,EAAI,OAAO,CAAQ,CAAI,EAY1C,CAAC,CAAS,CAAC,EAAW,EAAE,CACxB,CAAS,CAAC,EAAW,CAAG,IAAI,OAAA,EAEhC,IAAI,EAAgB,CAAS,CAAC,EAAW,CACrC,EAAc,EAAE,CAChB,EAAiB,IAAI,IACrB,EAAiB,IAAI,IAAI,GACzB,EAAO,SAAU,CAAE,EACf,CAAC,GAAM,EAAe,GAAG,CAAC,KAAK,AAGnC,EAAe,GAAG,CAAC,GACnB,EAAK,EAAG,UAAU,EACtB,EACA,EAAQ,OAAO,CAAC,GAChB,IAAI,EAAO,SAAU,CAAM,EACnB,CAAC,GAAU,EAAe,GAAG,CAAC,IAGlC,KAH2C,CAGrC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,EAAO,QAAQ,CAAE,SAAU,CAAI,EACxD,GAAI,EAAe,GAAG,CAAC,GACnB,EAAK,EADqB,MAI1B,GAAI,CACA,IAAI,EAAO,EAAK,YAAY,CAAC,GACzB,EAAgB,AAAS,UAAiB,UAAT,EACjC,EAAe,CAAC,EAAW,GAAG,CAAC,KAAS,CAAC,CAAI,EAC7C,EAAc,CAAC,EAAc,GAAG,CAAC,KAAS,CAAC,CAAI,EACnD,EAAW,GAAG,CAAC,EAAM,GACrB,EAAc,GAAG,CAAC,EAAM,GACxB,EAAY,IAAI,CAAC,GACI,IAAjB,GAAsB,GACtB,EAAkB,GAAG,CAAC,GAAM,GADS,AAGrB,GAAG,CAAnB,GACA,EAAK,YAAY,CAAC,EAAY,QAE9B,AAAC,GACD,EAAK,UADW,EACC,CAAC,EAAkB,OAE5C,CACA,MAAO,EAAG,CACN,QAAQ,KAAK,CAAC,kCAAmC,EAAM,EAC3D,CAER,EACJ,EAIA,OAHA,EAAK,GACL,EAAe,KAAK,GACpB,IACO,WACH,EAAY,OAAO,CAAC,SAAU,CAAI,EAC9B,IAAI,EAAe,EAAW,GAAG,CAAC,GAAQ,EACtC,EAAc,EAAc,GAAG,CAAC,GAAQ,EAC5C,EAAW,GAAG,CAAC,EAAM,GACrB,EAAc,GAAG,CAAC,EAAM,GACnB,IACG,AAAC,EAAkB,GAAG,CAAC,IACvB,AAFW,EAEN,CADyB,cACV,CAAC,GAEzB,EAAkB,MAAM,CAAC,IAEzB,AAAC,GACD,EAAK,QADS,OACM,CAAC,EAE7B,KAEK,IAED,EAAa,IAAI,CAFL,OAGZ,EAAa,IAAI,QACjB,EAAoB,IAAI,QACxB,EAAY,CAAC,EAErB,CACJ,EAQW,EAAa,SAAU,CAAc,CAAE,CAAU,CAAE,CAAU,EACjD,KAAK,GAAG,CAAvB,IAAyB,EAAa,kBAAA,EAC1C,IAAI,EAAU,MAAM,IAAI,CAAC,MAAM,OAAO,CAAC,GAAkB,EAAiB,CAAC,EAAe,EACtF,EAAmB,IA7HvB,AAAwB,UA6Ha,GA7HA,AAAjC,OAAO,SACA,KAGJ,CADY,MAAM,OAAO,CAAC,GAAkB,AA0HG,CA1HW,CAAC,EAAE,EAAG,CAAA,CACnD,aAAa,CAAC,IAFlC,AAEsC,SA0HtC,AAAK,GAKL,CALI,CAKI,IAAI,CAAC,KAAK,CAAC,EAAS,AALL,MAKW,IAAI,CAAC,EAAiB,gBAAgB,CAAC,yBAClE,EAAuB,EAAS,EAAkB,EAAY,gBAL1D,WAAc,OAAO,IAAM,CAM1C,4DCtHA,IAAI,EUhBA,EV+BO,EAAW,UAfF,CAuBlB,MAAO,CAPP,CAhB2B,CAgBhB,AAhBiB,EAAE,CAAC,IAgBb,MAAM,EAAI,SAAS,AAAS,CAAC,EAC3C,IAAK,IAAI,EAAG,EAAI,EAAG,EAAI,UAAU,MAAM,CAAE,EAAI,EAAG,IAAK,AAEjD,IAAK,IAAI,KADT,AACc,EADV,AACa,SADJ,CAAC,EAAE,CACK,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAG,KAAI,CAAC,CAAC,EAAE,CAAG,CAAC,CAAC,EAAA,AAAE,EAEhF,OAAO,EACX,EACgB,KAAK,CAAC,IAAI,CAAE,UAC9B,EAEO,SAAS,EAAO,CAAC,CAAE,CAAC,EACzB,IAAI,EAAI,CAAC,EACT,IAAK,IAAI,KAAK,EAAG,AAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAG,IAAqB,EAAf,EAAE,OAAO,CAAC,KACzE,CAAC,CAAC,EAAE,CAAG,CAAC,CAAC,EAAA,AAAE,EACf,GAAI,AAAK,SAAgD,YAAxC,OAAO,OAAO,qBAAqB,CAChD,IAAK,IAAI,EAAI,EAAG,EAAI,OAAO,qBAAqB,CAAC,GAAI,EAAI,EAAE,MAAM,CAAE,IAAK,AAC9C,EAAlB,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAS,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAG,CAAC,CAAC,EAAE,IACzE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAG,CAAC,CAAC,CAAC,CAAC,GAAE,AAAC,EAE7B,OAAO,CACT,CAoG6B,OAAO,MAAM,CA2GjB,EA3GqB,KA2Gd,IA3GuB,CAAC,CA2GlB,CA3GoB,AA2G1D,CA3G2D,CA2GjB,CA3GmB,CAAC,AA2G1D,AClQJ,EDuJgE,CA+Kd,CA/KgB,GA2Gf,CAAC,EAAE,CAAC,IAoEhC,OAAO,iBAAiC,gBCtU/D,EAAA,ADsUiF,CCtUjF,CAAA,MCDW,CFuUgF,CEvU3D,IFuUgE,EAAE,UAAU,EAAE,OAAO,GEtU1G,EAAqB,0BCYzB,SAAS,EAAU,CAAG,CAAE,CAAK,EAOhC,MANI,AAAe,YAAY,OAApB,EACP,EAAI,GAEC,GACL,EADU,CACN,OAAO,CAAG,CAAA,EAEX,CACX,CElBA,IAAI,EAAoF,EAAA,SAAe,CACnG,EAAgB,IAAI,QCHxB,ADEgC,SCFvB,EAAK,CAAC,EACX,OAAO,CACX,CCFO,IAAI,EDuEJ,ACvEgB,SFEyC,ACqEhD,AAAoB,CAAO,EACvB,KAAK,GAAG,CAApB,IAAsB,EAAU,EAAC,EACrC,IAtEiC,MAsE7B,GArEe,CADwB,IACnB,CAqEX,EArEc,CAAvB,IAAyB,EAAa,CAAA,EACtC,EAAS,EAAE,CACX,GAAW,EACF,CACT,KAAM,WACF,GAAI,EACA,MAAU,AAAJ,EADI,IACM,2GAEpB,AAAI,EAAO,MAAM,CACN,CADQ,AACF,CAAC,EAAO,MAAM,CAAG,EAAE,CA4Db,IAzD3B,EACA,UAAW,SAAU,CAAI,EACrB,IAAI,EAAO,EAAW,EAAM,GAE5B,OADA,EAAO,IAAI,CAAC,GACL,WACH,EAAS,EAAO,MAAM,CAAC,SAAU,CAAC,EAAI,OAAO,IAAM,CAAM,EAC7D,CACJ,EACA,iBAAkB,SAAU,CAAE,EAE1B,IADA,GAAW,EACJ,EAAO,MAAM,EAAE,CAClB,IAAI,EAAM,EACV,EAAS,EAAE,CACX,EAAI,OAAO,CAAC,EAChB,CACA,EAAS,CACL,KAAM,SAAU,CAAC,EAAI,OAAO,EAAG,EAAI,EACnC,OAAQ,WAAc,OAAO,CAAQ,CACzC,CACJ,EACA,aAAc,SAAU,CAAE,EACtB,GAAW,EACX,IAAI,EAAe,EAAE,CACrB,GAAI,EAAO,MAAM,CAAE,CACf,IAAI,EAAM,EACV,EAAS,EAAE,CACX,EAAI,OAAO,CAAC,GACZ,EAAe,CACnB,CACA,IAAI,EAAe,WACf,IAAI,EAAM,EACV,EAAe,EAAE,CACjB,EAAI,OAAO,CAAC,EAChB,EACI,EAAQ,WAAc,OAAO,QAAQ,OAAO,GAAG,IAAI,CAAC,EAAe,EACvE,IACA,EAAS,CACL,KAAM,SAAU,CAAC,EACb,EAAa,IAAI,CAAC,GAClB,GACJ,EACA,OAAQ,SAAU,CAAM,EAEpB,OADA,EAAe,EAAa,MAAM,CAAC,GAC5B,CACX,CACJ,CACJ,CACJ,GAYA,OADA,EAAO,OAAO,CAAG,EAAS,CAAE,OAAO,EAAM,KAAK,CAAM,EAAG,GAChD,CACX,IExEI,EAAU,WAEd,EAII,EAAe,EAAA,UAAgB,CAAC,SAAU,CAAK,CAAE,CAAS,EAC1D,MJGyC,MIHrC,EAAM,AJGuC,EIHvC,MAAY,CAAC,MACnB,EAAK,EAAA,QAAc,CAAC,CACpB,gBAAiB,EACjB,eAAgB,EAChB,mBAAoB,CACxB,GAAI,EAAY,CAAE,CAAC,EAAE,CAAE,EAAe,CAAE,CAAC,EAAE,CACvC,EAAe,EAAM,YAAY,CAAE,EAAW,EAAM,QAAQ,CAAE,EAAY,EAAM,SAAS,CAAE,EAAkB,EAAM,eAAe,CAAE,EAAU,EAAM,OAAO,CAAE,EAAS,EAAM,MAAM,CAAE,EAAU,EAAM,OAAO,CAAE,EAAa,EAAM,UAAU,CAAE,EAAc,EAAM,WAAW,CAAE,EAAQ,EAAM,KAAK,CAAE,EAAiB,EAAM,cAAc,CAAE,EAAK,EAAM,EAAE,CAA0C,CAAxC,CAAkD,EAAM,OAAO,CAAE,EAAO,EAAO,EAAO,CAAC,eAAgB,WAAY,YAAa,kBAAmB,UAAW,SAAU,UAAW,aAAc,cAAe,QAAS,iBAAkB,KAAM,UAAU,EAEnlB,GHDqB,EGCO,CAAC,CHDJ,CGCS,CHDP,CGCiB,GHAO,EGApC,MHDwB,CACsB,CAAQ,EACrE,OAAO,EAAK,OAAO,CAAC,SAAU,CAAG,EAAI,OAAO,EAAU,EAAK,EAAW,EAC1E,EDcA,CApBI,EAAM,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,WAAc,MAAQ,CAErC,MCE6C,CDFtC,IAEP,SAAU,EAEV,OAAQ,CACJ,IAAI,SAAU,CACV,OAAO,EAAI,KAAK,AACpB,EACA,IAAI,QAAQ,MAAO,CACf,IAAI,EAAO,EAAI,KAAK,CAChB,IAAS,OAAO,CAChB,EAAI,KAAK,CAAG,MACZ,EAAI,QAAQ,CAAC,MAAO,GAE5B,CACJ,CACJ,CAAI,EAAE,CAAC,EAAE,EAEL,QAAQ,CAAG,EChBX,EDiBG,EAAI,MAAM,CCbjB,EAA0B,CAJR,UAKd,IAAI,CALyB,CAKd,EAAc,GAAG,CAAC,GACjC,GAAI,EAAU,CACV,IAAI,EAAa,IAAI,IAAI,GACrB,EAAa,IAAI,IAAI,GACrB,EAAY,EAAY,OAAO,CACnC,EAAW,OAAO,CAAC,SAAU,CAAG,EACxB,AAAC,EAAW,GAAG,CAAC,IAChB,EADsB,AACZ,EAAK,KAEvB,GACA,EAAW,OAAO,CAAC,SAAU,CAAG,EACxB,AAAC,EAAW,GAAG,CAAC,IAChB,EADsB,AACZ,EAAK,EAEvB,EACJ,CACA,EAAc,GAAG,CAAC,EAAa,EACnC,EAAG,CAAC,EAAK,EACF,GGtBH,EAAiB,EAAS,EAAS,CAAC,EAAG,GAAO,GAClD,OAAQ,EAAA,aAAmB,CAAC,EAAA,QAAc,CAAE,KACxC,GAAY,EAAA,aAAmB,CAAC,AAJtB,EAI+B,CAAE,QAAS,EAAW,gBAAiB,EAAiB,OAAQ,EAAQ,WAAY,EAAY,YAAa,EAAa,MAAO,EAAO,aAAc,EAAc,eAAgB,CAAC,CAAC,EAAgB,QAAS,EAAK,QAAS,CAAQ,GAC9Q,EAAgB,EAAA,YAAkB,CAAC,EAAA,QAAc,CAAC,IAAI,CAAC,GAAW,EAAS,EAAS,CAAC,EAAG,GAAiB,CAAE,IAAK,CAAa,IAAQ,EAAA,aAAmB,CAAC,AANkN,KAAK,IAAZ,EAAgB,MAAQ,EAMxN,EAAS,CAAC,EAAG,EAAgB,CAAE,UAAW,EAAW,IAAK,CAAa,GAAI,GACvP,GACA,EAAa,YAAY,CAAG,CACxB,QAAS,GACT,gBAAiB,GACjB,OAAO,CACX,EACA,EAAa,UAAU,CAAG,CACtB,UAAW,EACX,UAAW,CACf,EChCA,IAAI,EAAU,SAAU,CAAE,EACtB,IAAI,EAAU,EAAG,OAAO,CAAE,EAAO,EAAO,EAAI,CAAC,UAAU,EACvD,GAAI,CAAC,EACD,MAAU,AAAJ,CADI,KACM,sEAEpB,IAAI,EAAS,EAAQ,IAAI,GACzB,GAAI,CAAC,EACD,MADS,AACH,AAAI,MAAM,4BAEpB,OAAO,EAAA,aAAmB,CAAC,EAAQ,EAAS,CAAC,EAAG,GACpD,EACA,EAAQ,eAAe,EAAG,EEanB,IAAI,EAAsB,WAC7B,IAAI,EAAU,EACV,EAAa,KACjB,MAAO,CACH,IAAK,SAAU,CAAK,EAChB,GAAe,GAAX,AAAc,IACT,EAAa,AA/BlC,SAAS,EACL,GAAI,CAAC,SACD,OAAO,KACX,IAAI,EAAM,SAAS,aAAa,CAAC,SACjC,EAAI,IAAI,CAAG,WACX,IAAI,EDDJ,AAAI,IAG6B,ECFrB,QDDM,GAGd,AAA0C,OAAnC,kBACA,0BCCX,OAHI,GACA,EAAI,EADG,UACS,CAAC,QAAS,GAEvB,CACX,GAqBkC,EAAiB,QAlB3C,EAFc,EAqBW,CArBR,EAAE,AAEf,GAFkB,OAER,CAEd,CAFgB,CAEZ,UAAU,CAAC,OAAO,GAAG,AAGzB,EAAI,WAAW,CAAC,SAAS,cAAc,CAcF,AAdG,IAGxB,EAYW,CAZR,CAEvB,CADW,SAAS,IAAI,EAAI,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAA,AAAE,EAC/D,WAAW,CAAC,EAWL,CAEJ,GACJ,EACA,OAAQ,aAEC,IAAW,IACZ,EAAW,MADa,IACH,EAAI,EAAW,UAAU,CAAC,WAAW,CAAC,GAC3D,EAAa,KAErB,CACJ,CACJ,ECpCW,EAAqB,WAC5B,IAAI,EAAQ,IACZ,OAAO,SAAU,CAAM,CAAE,CAAS,EAC9B,EAAA,SAAe,CAAC,WAEZ,OADA,EAAM,GAAG,CAAC,GACH,WACH,EAAM,MAAM,EAChB,CACJ,EAAG,CAAC,GAAU,EAAU,CAC5B,CACJ,ECdW,EAAiB,WACxB,IAAI,EAAW,IAMf,OALY,AAKL,SALe,CAAE,EAGpB,OADA,EADa,EAAG,KACP,CADa,CAAY,CAAV,CAAa,IACpB,GAD2B,EAErC,IACX,CAEJ,ECfW,EAAU,CACjB,KAAM,EACN,IAAK,EACL,MAAO,EACP,IAAK,CACT,ECDI,EAAQ,IACD,EAAgB,qBAIvB,EAAY,SAAU,CAAE,CAAE,CAAa,CAAE,CAAO,CAAE,CAAS,EAC3D,IAAI,EAAO,EAAG,IAAI,CAAE,EAAM,EAAG,GAAG,CAAE,EAAQ,EAAG,KAAK,CAAE,EAAM,EAAG,GAAG,CAEhE,OADgB,KAAK,GAAG,CAApB,IAAsB,EAAU,QAAA,EAC7B,QAAQ,MAAM,CAAC,AbVS,0BaUc,4BAA4B,MAAM,CAAC,EAAW,yBAAyB,MAAM,CAAC,EAAK,OAAO,MAAM,CAAC,EAAW,mBAAmB,MAAM,CAAC,EAAe,8BAA8B,MAAM,CAAC,EAAW,8CAA8C,MAAM,CAAC,CACnS,GAAiB,sBAAsB,MAAM,CAAC,EAAW,KACzD,AAAY,cACR,uBAAuB,MAAM,CAAC,EAAM,0BAA0B,MAAM,CAAC,EAAK,4BAA4B,MAAM,CAAC,EAAO,kEAAkE,MAAM,CAAC,EAAK,OAAO,MAAM,CAAC,EAAW,WACnN,YAAZ,GAAyB,kBAAkB,MAAM,CAAC,EAAK,OAAO,MAAM,CAAC,EAAW,KACnF,CACI,MAAM,CAAC,SACP,IAAI,CAAC,IAAK,kBAAkB,MAAM,CAAC,EAAoB,mBAAmB,MAAM,CAAC,EAAK,OAAO,MAAM,CAAC,EAAW,mBAAmB,MAAM,CAAC,EAAoB,0BAA0B,MAAM,CAAC,EAAK,OAAO,MAAM,CAAC,EAAW,mBAAmB,MAAM,CAAC,EAAoB,MAAM,MAAM,CAAC,EAAoB,qBAAqB,MAAM,CAAC,EAAW,mBAAmB,MAAM,CAAC,EAAoB,MAAM,MAAM,CAAC,EAAoB,4BAA4B,MAAM,CAAC,EAAW,uBAAuB,MAAM,CAAC,EAAe,aAAa,MAAM,CAAC,AbZrf,iCaY6gB,MAAM,MAAM,CAAC,EAAK,aACnkB,EACI,EAAuB,WACvB,IAAI,EAAU,SAAS,SAAS,IAAI,CAAC,YAAY,CAAC,IAAkB,IAAK,IACzE,OAAO,SAAS,GAAW,EAAU,CACzC,EACW,EAAmB,WAC1B,EAAA,SAAe,CAAC,WAEZ,OADA,SAAS,IAAI,CAAC,YAAY,CAAC,EAAe,CAAC,KAAyB,CAAC,CAAE,QAAQ,IACxE,WACH,IAAI,EAAa,IAAyB,EACtC,GAAc,EACd,CADiB,QACR,IAAI,CAAC,eAAe,CAAC,GAG9B,SAAS,IAAI,CAAC,YAAY,CAAC,EAAe,EAAW,QAAQ,GAErE,CACJ,EAAG,EAAE,CACT,EAIW,EAAkB,SAAU,CAAE,EACrC,IAAI,EAAa,EAAG,UAAU,CAAE,EAAc,EAAG,WAAW,CAAE,EAAK,EAAG,OAAO,CAAE,EAAiB,KAAK,IAAZ,EAAgB,SAAW,EACpH,IAMA,IAAI,EAAM,EAAA,OAAa,CAAC,WAAc,ODnClC,ACmCyC,ADnC7B,KAAK,GAAG,CCmCiC,EDjC9C,CCiCwD,EAAG,CAAC,EAAQ,EAC/E,OAAO,EAAA,aAAmB,CAAC,EAAO,CAAE,OAAQ,EAAU,EAAK,CAAC,EAAY,EAAS,AAAC,EAA6B,GAAf,aAAmB,EACvH,EEhDI,EAAuB,SAAU,CAAI,CAAE,CAAQ,EAC/C,GAAI,CAAC,CAAC,aAAgB,OAAA,CAAO,CACzB,EAD4B,KACrB,EAEX,IAAI,EAAS,OAAO,gBAAgB,CAAC,GACrC,MAEqB,CADrB,UACA,CAAM,AACF,CADG,EAAS,GAEV,EAAO,EAHQ,OAGC,GAAK,EAAO,QADA,CACS,EAXnB,AAAjB,EAWwC,CAAC,UAAqB,EAXzD,OAAO,EAWgF,YAArB,CAAM,CAAC,EAAS,AAAK,CAAS,AAChH,EAGW,EAA0B,SAAU,CAAI,CAAE,CAAI,EACrD,IAAI,EAAgB,EAAK,aAAa,CAClC,EAAU,EACd,EAAG,CAMC,GAJI,AAAsB,CAItB,mBAJO,YAA8B,aAAmB,YAAY,AACpE,GAAU,EAAQ,IAAA,AAAI,EAEP,EAAuB,EAAM,GAC9B,CACd,IAAI,EAAK,EAAmB,EAAM,GAClC,GAD2D,AACvD,CADyD,CAAC,EAAlB,AAAoB,CAAiB,CAAf,AAAiB,CAAC,EAAE,CAElF,KADe,EACR,CAEf,CACA,EAAU,EAAQ,MAJmB,IAKzC,AADgC,OACvB,GAAW,IAAY,EAAc,IAAI,CAAE,AACpD,OAAO,CACX,EAiBI,EAAyB,SAAU,CAAI,CAAE,CAAI,EAC7C,MAAgB,MAAT,AAAe,EAtC6B,EAsCL,EAtCgC,MAsCxB,OArCH,EAqC2B,EAtCN,AACM,YAsClF,EACI,EAAqB,CAvCmD,QAuCzC,CAAI,CAAE,CAAI,EACzC,MAAgB,MAAT,AAAe,EAlBf,CADS,EAAG,SAAS,CAAiB,CAAf,CAAkB,YAAY,CAAiB,AAmBnC,CAnBoB,CAAkB,MAmB9B,MAnB0C,CAK3F,CAIM,CADU,EAAG,UAAU,CAAgB,CAAd,CAAiB,WAAW,CAAgB,AAWN,CAXR,CAAiB,WAAW,CAKzF,AAOL,EASW,EAAe,SAAU,CAAI,CAAE,CAAS,CAAE,CAAK,CAAE,CAAW,CAAE,CAAY,EACjF,MAAI,GATiC,EASU,OATD,AASQ,MAAhC,UAAgD,CAAC,GAAW,SAAS,CAH3E,AAAT,MAGkC,GAHJ,QAAd,EAAsB,CAAC,EAAI,GAI9C,EAAQ,EAAkB,EAE1B,EAAS,EAAM,MAAM,CACrB,EAAe,EAAU,QAAQ,CAAC,GAClC,GAAqB,EACrB,EAAkB,EAAQ,EAC1B,EAAkB,EAClB,EAAqB,EACzB,EAAG,CACC,GAAI,CAAC,EACD,MADS,AAGb,IAAI,EAAK,EAAmB,EAAM,GAAS,EAAW,CAAE,CAAC,EAAE,CACvD,CADyD,CAAW,AACpD,CADsD,CAAC,EAAE,CAAa,CAAE,AAAb,CAAc,EAAE,CACrD,CAAX,CAA6B,GACxD,GAAY,CAAA,GACR,AADuB,EACA,EAAM,KAC7B,GAAmB,CADmB,CAEtC,GAAsB,GAG9B,IAAI,EAAW,EAAO,UAAU,CAGhC,EAAU,GAAY,EAAS,QAAQ,GAAK,KAAK,sBAAsB,CAAG,EAAS,IAAI,CAAG,CAC9F,OAEC,CADD,AACE,GAAgB,IAAW,SAAS,GADnB,CACuB,EAErC,IAAiB,EAAU,QAAQ,CAAC,CAApB,GAA+B,IAAc,CAAA,CAAM,CAUxE,AAV4E,OAExE,IACE,GAA4C,EAA5B,KAAK,GAAG,CAAC,CAA3B,GAAqD,CAAC,GAAgB,EAAQ,CAAA,CAAgB,CAC9F,EADiG,CAC5E,EAEhB,CAAC,IACJ,GAA+C,EAA/B,KAAK,GAAG,CAAC,CAA3B,GAAwD,CAAC,GAAgB,CAAC,EAAQ,CAAA,CAAmB,GACrG,AADwG,GACnF,CAAA,EAElB,CACX,ECrGW,EAAa,SAAU,CAAK,EACnC,MAAO,mBAAoB,EAAQ,CAAC,EAAM,cAAc,CAAC,EAAE,CAAC,OAAO,CAAE,EAAM,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC,CAAG,CAAC,EAAG,EAAE,AAClH,EACW,EAAa,SAAU,CAAK,EAAI,MAAO,CAAC,EAAM,MAAM,CAAE,EAAM,MAAM,CAAC,AAAE,EAC5E,EAAa,SAAU,CAAG,EAC1B,OAAO,GAAO,YAAa,EAAM,EAAI,OAAO,CAAG,CACnD,EAGI,EAAY,EACZ,EAAY,EAAE,QTFoB,ESG/B,MTHuC,GSG9B,AAAoB,CAAK,EACrC,IAAI,EAAqB,EAAA,MAAY,CAAC,EAAE,ACf7B,EDgBP,EAAgB,EAAA,MAAY,CAAC,CAAC,EAAG,EAAE,EACnC,EAAa,EAAA,MAAY,GACzB,EAAK,EAAA,QAAc,CAAC,IAAY,CAAC,EAAE,CACnC,EAAQ,EAAA,QAAc,CAAC,EAAe,CAAC,EAAE,CACzC,EAAY,EAAA,MAAY,CAAC,GAC7B,EAAA,SAAe,CAAC,WACZ,EAAU,OAAO,CAAG,CACxB,EAAG,CAAC,EAAM,EACV,EAAA,SAAe,CAAC,WACZ,GAAI,EAAM,KAAK,CAAE,CACb,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,MAAM,CAAC,IAC1D,IAAI,EAAU,ClBuLnB,SAAS,AAAc,CAAE,CAAE,CAAI,CAAE,CAAI,EAC1C,GAAI,GAA6B,GAArB,UAAU,MAAM,CAAQ,IAAK,IAA4B,EAAxB,EAAI,EAAG,EAAI,EAAK,MAAM,CAAM,EAAI,EAAG,IAAK,CAC7E,GAAQ,GAAF,CAAC,CAAM,IAAI,AACb,AAAC,GADe,CACX,EAAK,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAM,EAAG,EAAA,EAClD,CAAE,CAAC,EAAE,CAAG,CAAI,CAAC,EAAE,EAGvB,OAAO,EAAG,MAAM,CAAC,GAAM,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GACpD,GkB/LwC,CAAC,EAAM,OAAO,CAAC,OAAO,CAAC,CAAE,CAAC,EAAM,MAAM,EAAI,EAAA,AAAE,EAAE,GAAG,CAAC,IAAa,GAAM,MAAM,CAAC,SAExG,OADA,EAAQ,OAAO,CAAC,SAAU,CAAE,EAAI,OAAO,EAAG,SAAS,CAAC,GAAG,CAAC,uBAAuB,MAAM,CAAC,GAAM,GACrF,WACH,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,uBAAuB,MAAM,CAAC,IAC7D,EAAQ,OAAO,CAAC,SAAU,CAAE,EAAI,OAAO,EAAG,SAAS,CAAC,MAAM,CAAC,uBAAuB,MAAM,CAAC,GAAM,EACnG,CACJ,CAEJ,EAAG,CAAC,EAAM,KAAK,CAAE,EAAM,OAAO,CAAC,OAAO,CAAE,EAAM,MAAM,CAAC,EACrD,IAAI,EAAoB,EAAA,WAAiB,CAAC,SAAU,CAAK,CAAE,CAAM,EAC7D,GAAK,YAAa,GAAkC,IAAzB,EAAM,OAAO,CAAC,MAAM,EAA2B,UAAf,EAAM,IAAI,EAAgB,EAAM,OAAO,CAC9F,CADiG,KAC1F,CAAC,EAAU,OAAO,CAAC,cAAc,CAE5C,IAII,EAJA,EAAQ,EAAW,GACnB,EAAa,EAAc,OAAO,CAClC,EAAS,WAAY,EAAQ,EAAM,MAAM,CAAG,CAAU,CAAC,EAAE,CAAG,CAAK,CAAC,EAAE,CACpE,EAAS,WAAY,EAAQ,EAAM,MAAM,CAAG,CAAU,CAAC,EAAE,CAAG,CAAK,CAAC,EAAE,CAEpE,EAAS,EAAM,MAAM,CACrB,EAAgB,KAAK,GAAG,CAAC,GAAU,KAAK,GAAG,CAAC,GAAU,IAAM,IAEhE,GAAI,YAAa,GAA2B,MAAlB,GAAyC,SAAS,CAAzB,EAAO,IAAI,CAC1D,MAAO,GAEX,IAAI,EAA+B,EAAwB,EAAe,GAC1E,GAAI,CAAC,EACD,OAAO,EAUX,GARI,EACA,EAAc,GAGd,EAAgC,MAAlB,CAPiB,CAOO,IAAM,IAC5C,EAA+B,EAAwB,CALzB,CAKwC,IAGtE,CAAC,EACD,OAAO,EAKX,GAHI,CAAC,EAAW,OAAO,EAAI,IAHQ,eAGY,IAAU,GAAU,CAAA,CAAX,AAAiB,GAAG,AACxE,EAAW,OAAO,CAAG,CAAA,EAErB,CAAC,EACD,OAAO,EAEX,EAHkB,EAGd,EAAgB,EAAW,OAAO,EAAI,EAC1C,OAAO,EAAa,EAAe,EAAQ,EAAyB,MAAlB,EAAwB,EAAS,GAAQ,EAC/F,EAAG,EAAE,EACD,EAAgB,EAAA,WAAiB,CAAC,SAAU,CAAM,EAElD,GAAI,AAAC,EAAU,MAAM,EAAI,CAAS,CAAC,EAAU,MAAM,CAAG,EAAE,GAAK,GAI7D,IAJoE,AAIhE,EAAQ,YAAY,CAAQ,KAAoB,EALxC,GAMR,CADuC,CACzB,EAAmB,EAD0B,KACnB,CAAC,MAAM,CAAC,SAAU,CAAC,QAAI,OAAO,EAAE,IAAI,GAAK,EAAM,IAAI,GAAK,CAAD,CAAG,MAAM,GAAK,EAAM,MAAM,EAAI,EAAM,MAAM,GAAK,EAAE,YAAA,AAAY,IAxE3I,CAAC,AAwE+I,CAAa,CAxE1J,CAAC,AAwE2J,KAAK,CAxErJ,CAAC,CAAC,EAAE,GAAK,CAAC,CAAC,EAAE,EAAI,CAAC,CAAC,EAAE,GAwEkI,AAxE7H,CAAC,CAAC,EAAE,CAwEiI,EAAE,CAAC,EAAE,CAExM,GAAI,GAAe,EAAY,MAAM,CAAE,CAC/B,EAAM,UAAU,EAAE,AAClB,EAAM,cAAc,GAExB,MACJ,CAEA,GAAI,CAAC,EAAa,CACd,IAAI,EAAa,CAAC,EAAU,OAAO,CAAC,MAAM,EAAI,EAAA,AAAE,EAC3C,GAAG,CAAC,GACJ,MAAM,CAAC,SACP,MAAM,CAAC,SAAU,CAAI,EAAI,OAAO,EAAK,QAAQ,CAAC,EAAM,MAAM,CAAG,EAE9D,EADa,EAAW,MAAM,CAAG,CACrB,CADyB,IAAyB,CAAU,CAAC,EAAE,EAAI,CAAC,EAAU,KAAnC,EAA0C,CAAC,WAAA,AAAW,GAEzG,EAAM,UAAU,EAAE,AAClB,EAAM,cAAc,EAGhC,EACJ,EAAG,EAAE,EACD,EAAe,EAAA,WAAiB,CAAC,SAAU,CAAI,CAAE,CAAK,CAAE,CAAM,CAAE,CAAM,EACtE,IAAI,EAAQ,CAAE,KAAM,EAAM,MAAO,EAAO,OAAQ,EAAQ,OAAQ,EAAQ,aAAc,AAsC9F,SAAS,AAAyB,CAAI,EAElC,IADA,IAAI,EAAe,KACZ,AAAS,KAAM,KACd,aAAgB,YAAY,CAC5B,EAAe,EAAK,IAAI,CACxB,EAAO,EAAK,IAAI,EAEpB,EAAO,EAAK,UAAU,CAE1B,OAAO,CACX,EAhDuH,EAAQ,EACvH,EAAmB,OAAO,CAAC,IAAI,CAAC,GAChC,WAAW,WACP,EAAmB,OAAO,CAAG,EAAmB,OAAO,CAAC,MAAM,CAAC,SAAU,CAAC,EAAI,OAAO,IAAM,CAAO,EACtG,EAAG,EACP,EAAG,EAAE,EACD,EAAmB,EAAA,WAAiB,CAAC,SAAU,CAAK,EACpD,EAAc,OAAO,CAAG,EAAW,GACnC,EAAW,OAAO,MAAG,CACzB,EAAG,EAAE,EACD,EAAc,EAAA,WAAiB,CAAC,SAAU,CAAK,EAC/C,EAAa,EAAM,IAAI,CAAE,EAAW,GAAQ,EAAM,MAAM,CAAE,EAAkB,EAAO,EAAM,OAAO,CAAC,OAAO,EAC5G,EAAG,EAAE,EACD,EAAkB,EAAA,WAAiB,CAAC,SAAU,CAAK,EACnD,EAAa,EAAM,IAAI,CAAE,EAAW,GAAQ,EAAM,MAAM,CAAE,EAAkB,EAAO,EAAM,OAAO,CAAC,OAAO,EAC5G,EAAG,EAAE,EACL,EAAA,SAAe,CAAC,WAUZ,OATA,EAAU,IAAI,CAAC,GACf,EAAM,YAAY,CAAC,CACf,gBAAiB,EACjB,eAAgB,EAChB,mBAAoB,CACxB,GACA,SAAS,gBAAgB,CAAC,QAAS,MACnC,SADkD,AACzC,gBAAgB,CAAC,YAAa,MACvC,SADsD,AAC7C,gBAAgB,CAAC,aAAc,MACjC,WACH,CAFsD,CAE1C,EAAU,MAAM,CAAC,SAAU,CAAI,EAAI,OAAO,IAAS,CAAO,GACtE,SAAS,mBAAmB,CAAC,QAAS,MACtC,SADqD,AAC5C,mBAAmB,CAAC,YAAa,MAC1C,SADyD,AAChD,mBAAmB,CAAC,aAAc,GF1IhC,EE2If,CACJ,EAAG,EAAE,EACL,IAAI,EAHiE,AAG/C,EAAM,eAAe,CAAE,EAAQ,EAAM,KAAK,CAChE,OAAQ,EAAA,aAAmB,CAAC,EAAA,QAAc,CAAE,KACxC,EAAQ,EAAA,aAAmB,CAAC,EAAO,CAAE,OAjIF,CAiIU,2BAjIkB,MAAM,CAAC,EAAI,qDAAqD,MAAM,CAAC,AAiI3E,EAjI+E,4BAiI3E,GAAK,KACpE,EAAkB,EAAA,aAAmB,CAAC,EAAiB,CAAE,WAAY,EAAM,UAAU,CAAE,QAAS,EAAM,OAAO,AAAC,GAAK,KAC3H,EC9I6B,AVYzB,EAAO,SAAS,AUZoB,CVYnB,GACV,GRZX,IAAI,EAAoB,EAAA,UAAgB,CAAC,SAAU,CAAK,CAAE,CAAG,EAAI,OAAQ,EAAA,aAAmB,CAAC,EAAc,EAAS,CAAC,EAAG,EAAO,CAAE,IAAK,EAAK,QAAS,CAAQ,GAAM,GAClK,EAAkB,UAAU,CAAG,EAAa,UAAU,OACvC", "ignoreList": [0, 1, 2, 3, 4, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40]}