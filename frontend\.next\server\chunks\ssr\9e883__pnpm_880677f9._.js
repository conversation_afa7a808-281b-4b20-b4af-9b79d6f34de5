module.exports=[57565,54130,a=>{"use strict";function b(a,c,{checkForDefaultPrevented:d=!0}={}){return function(b){if(a?.(b),!1===d||!b.defaultPrevented)return c?.(b)}}a.s(["composeEventHandlers",()=>b],57565),a.s(["createContext",()=>e,"createContextScope",()=>f],54130);var c=a.i(128),d=a.i(68116);function e(a,b){let e=c.createContext(b),f=a=>{let{children:b,...f}=a,g=c.useMemo(()=>f,Object.values(f));return(0,d.jsx)(e.Provider,{value:g,children:b})};return f.displayName=a+"Provider",[f,function(d){let f=c.useContext(e);if(f)return f;if(void 0!==b)return b;throw Error(`\`${d}\` must be used within \`${a}\``)}]}function f(a,b=[]){let e=[],g=()=>{let b=e.map(a=>c.createContext(a));return function(d){let e=d?.[a]||b;return c.useMemo(()=>({[`__scope${a}`]:{...d,[a]:e}}),[d,e])}};return g.scopeName=a,[function(b,f){let g=c.createContext(f),h=e.length;e=[...e,f];let i=b=>{let{scope:e,children:f,...i}=b,j=e?.[a]?.[h]||g,k=c.useMemo(()=>i,Object.values(i));return(0,d.jsx)(j.Provider,{value:k,children:f})};return i.displayName=b+"Provider",[i,function(d,e){let i=e?.[a]?.[h]||g,j=c.useContext(i);if(j)return j;if(void 0!==f)return f;throw Error(`\`${d}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let d=()=>{let d=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let e=d.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return c.useMemo(()=>({[`__scope${b.scopeName}`]:e}),[e])}};return d.scopeName=b.scopeName,d}(g,...b)]}},44177,1510,a=>{"use strict";a.s(["DismissableLayer",()=>k],44177);var b,c=a.i(128),d=a.i(57565),e=a.i(48206),f=a.i(9403);function g(a){let b=c.useRef(a);return c.useEffect(()=>{b.current=a}),c.useMemo(()=>(...a)=>b.current?.(...a),[])}a.s(["useCallbackRef",()=>g],1510);var h=a.i(68116),i="dismissableLayer.update",j=c.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),k=c.forwardRef((a,k)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:o,onPointerDownOutside:p,onFocusOutside:q,onInteractOutside:r,onDismiss:s,...t}=a,u=c.useContext(j),[v,w]=c.useState(null),x=v?.ownerDocument??globalThis?.document,[,y]=c.useState({}),z=(0,f.useComposedRefs)(k,a=>w(a)),A=Array.from(u.layers),[B]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),C=A.indexOf(B),D=v?A.indexOf(v):-1,E=u.layersWithOutsidePointerEventsDisabled.size>0,F=D>=C,G=function(a,b=globalThis?.document){let d=g(a),e=c.useRef(!1),f=c.useRef(()=>{});return c.useEffect(()=>{let a=a=>{if(a.target&&!e.current){let c=function(){m("dismissableLayer.pointerDownOutside",d,e,{discrete:!0})},e={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",f.current),f.current=c,b.addEventListener("click",f.current,{once:!0})):c()}else b.removeEventListener("click",f.current);e.current=!1},c=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(c),b.removeEventListener("pointerdown",a),b.removeEventListener("click",f.current)}},[b,d]),{onPointerDownCapture:()=>e.current=!0}}(a=>{let b=a.target,c=[...u.branches].some(a=>a.contains(b));F&&!c&&(p?.(a),r?.(a),a.defaultPrevented||s?.())},x),H=function(a,b=globalThis?.document){let d=g(a),e=c.useRef(!1);return c.useEffect(()=>{let a=a=>{a.target&&!e.current&&m("dismissableLayer.focusOutside",d,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,d]),{onFocusCapture:()=>e.current=!0,onBlurCapture:()=>e.current=!1}}(a=>{let b=a.target;![...u.branches].some(a=>a.contains(b))&&(q?.(a),r?.(a),a.defaultPrevented||s?.())},x);return!function(a,b=globalThis?.document){let d=g(a);c.useEffect(()=>{let a=a=>{"Escape"===a.key&&d(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[d,b])}(a=>{D===u.layers.size-1&&(o?.(a),!a.defaultPrevented&&s&&(a.preventDefault(),s()))},x),c.useEffect(()=>{if(v)return n&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(b=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(v)),u.layers.add(v),l(),()=>{n&&1===u.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=b)}},[v,x,n,u]),c.useEffect(()=>()=>{v&&(u.layers.delete(v),u.layersWithOutsidePointerEventsDisabled.delete(v),l())},[v,u]),c.useEffect(()=>{let a=()=>y({});return document.addEventListener(i,a),()=>document.removeEventListener(i,a)},[]),(0,h.jsx)(e.Primitive.div,{...t,ref:z,style:{pointerEvents:E?F?"auto":"none":void 0,...a.style},onFocusCapture:(0,d.composeEventHandlers)(a.onFocusCapture,H.onFocusCapture),onBlurCapture:(0,d.composeEventHandlers)(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:(0,d.composeEventHandlers)(a.onPointerDownCapture,G.onPointerDownCapture)})});function l(){let a=new CustomEvent(i);document.dispatchEvent(a)}function m(a,b,c,{discrete:d}){let f=c.originalEvent.target,g=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&f.addEventListener(a,b,{once:!0}),d?(0,e.dispatchDiscreteCustomEvent)(f,g):f.dispatchEvent(g)}k.displayName="DismissableLayer",c.forwardRef((a,b)=>{let d=c.useContext(j),g=c.useRef(null),i=(0,f.useComposedRefs)(b,g);return c.useEffect(()=>{let a=g.current;if(a)return d.branches.add(a),()=>{d.branches.delete(a)}},[d.branches]),(0,h.jsx)(e.Primitive.div,{...a,ref:i})}).displayName="DismissableLayerBranch"},95686,a=>{"use strict";a.s(["useFocusGuards",()=>d]);var b=a.i(128),c=0;function d(){b.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??e()),document.body.insertAdjacentElement("beforeend",a[1]??e()),c++,()=>{1===c&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),c--}},[])}function e(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}},45105,a=>{"use strict";a.s(["FocusScope",()=>j]);var b=a.i(128),c=a.i(9403),d=a.i(48206),e=a.i(1510),f=a.i(68116),g="focusScope.autoFocusOnMount",h="focusScope.autoFocusOnUnmount",i={bubbles:!1,cancelable:!0},j=b.forwardRef((a,j)=>{let{loop:o=!1,trapped:p=!1,onMountAutoFocus:q,onUnmountAutoFocus:r,...s}=a,[t,u]=b.useState(null),v=(0,e.useCallbackRef)(q),w=(0,e.useCallbackRef)(r),x=b.useRef(null),y=(0,c.useComposedRefs)(j,a=>u(a)),z=b.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;b.useEffect(()=>{if(p){let a=function(a){if(z.paused||!t)return;let b=a.target;t.contains(b)?x.current=b:m(x.current,{select:!0})},b=function(a){if(z.paused||!t)return;let b=a.relatedTarget;null!==b&&(t.contains(b)||m(x.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&m(t)});return t&&c.observe(t,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[p,t,z.paused]),b.useEffect(()=>{if(t){n.add(z);let a=document.activeElement;if(!t.contains(a)){let b=new CustomEvent(g,i);t.addEventListener(g,v),t.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(m(d,{select:b}),document.activeElement!==c)return}(k(t).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&m(t))}return()=>{t.removeEventListener(g,v),setTimeout(()=>{let b=new CustomEvent(h,i);t.addEventListener(h,w),t.dispatchEvent(b),b.defaultPrevented||m(a??document.body,{select:!0}),t.removeEventListener(h,w),n.remove(z)},0)}}},[t,v,w,z]);let A=b.useCallback(a=>{if(!o&&!p||z.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,c=document.activeElement;if(b&&c){let b=a.currentTarget,[d,e]=function(a){let b=k(a);return[l(b,a),l(b.reverse(),a)]}(b);d&&e?a.shiftKey||c!==e?a.shiftKey&&c===d&&(a.preventDefault(),o&&m(e,{select:!0})):(a.preventDefault(),o&&m(d,{select:!0})):c===b&&a.preventDefault()}},[o,p,z.paused]);return(0,f.jsx)(d.Primitive.div,{tabIndex:-1,...s,ref:y,onKeyDown:A})});function k(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function l(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function m(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}j.displayName="FocusScope";var n=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=o(a,b)).unshift(b)},remove(b){a=o(a,b),a[0]?.resume()}}}();function o(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}},58714,a=>{"use strict";a.s(["useLayoutEffect",()=>c]);var b=a.i(128),c=globalThis?.document?b.useLayoutEffect:()=>{}},90368,a=>{"use strict";a.s(["useId",()=>f]);var b=a.i(128),c=a.i(58714),d=b[" useId ".trim().toString()]||(()=>void 0),e=0;function f(a){let[f,g]=b.useState(d());return(0,c.useLayoutEffect)(()=>{a||g(a=>a??String(e++))},[a]),a||(f?`radix-${f}`:"")}},60091,a=>{"use strict";a.s(["Anchor",()=>aU,"Arrow",()=>aW,"Content",()=>aV,"Root",()=>aT,"createPopperScope",()=>aD],60091);var b=a.i(128);let c=["top","right","bottom","left"],d=Math.min,e=Math.max,f=Math.round,g=Math.floor,h=a=>({x:a,y:a}),i={left:"right",right:"left",bottom:"top",top:"bottom"},j={start:"end",end:"start"};function k(a,b){return"function"==typeof a?a(b):a}function l(a){return a.split("-")[0]}function m(a){return a.split("-")[1]}function n(a){return"x"===a?"y":"x"}function o(a){return"y"===a?"height":"width"}let p=new Set(["top","bottom"]);function q(a){return p.has(l(a))?"y":"x"}function r(a){return a.replace(/start|end/g,a=>j[a])}let s=["left","right"],t=["right","left"],u=["top","bottom"],v=["bottom","top"];function w(a){return a.replace(/left|right|bottom|top/g,a=>i[a])}function x(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function y(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function z(a,b,c){let d,{reference:e,floating:f}=a,g=q(b),h=n(q(b)),i=o(h),j=l(b),k="y"===g,p=e.x+e.width/2-f.width/2,r=e.y+e.height/2-f.height/2,s=e[i]/2-f[i]/2;switch(j){case"top":d={x:p,y:e.y-f.height};break;case"bottom":d={x:p,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:r};break;case"left":d={x:e.x-f.width,y:r};break;default:d={x:e.x,y:e.y}}switch(m(b)){case"start":d[h]-=s*(c&&k?-1:1);break;case"end":d[h]+=s*(c&&k?-1:1)}return d}let A=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=z(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=z(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function B(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:l="viewport",elementContext:m="floating",altBoundary:n=!1,padding:o=0}=k(b,a),p=x(o),q=h[n?"floating"===m?"reference":"floating":m],r=y(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(q)))||c?q:q.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:l,strategy:i})),s="floating"===m?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,t=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),u=await (null==f.isElement?void 0:f.isElement(t))&&await (null==f.getScale?void 0:f.getScale(t))||{x:1,y:1},v=y(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:s,offsetParent:t,strategy:i}):s);return{top:(r.top-v.top+p.top)/u.y,bottom:(v.bottom-r.bottom+p.bottom)/u.y,left:(r.left-v.left+p.left)/u.x,right:(v.right-r.right+p.right)/u.x}}function C(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function D(a){return c.some(b=>a[b]>=0)}let E=new Set(["left","top"]);async function F(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=l(c),h=m(c),i="y"===q(c),j=E.has(g)?-1:1,n=f&&i?-1:1,o=k(b,a),{mainAxis:p,crossAxis:r,alignmentAxis:s}="number"==typeof o?{mainAxis:o,crossAxis:0,alignmentAxis:null}:{mainAxis:o.mainAxis||0,crossAxis:o.crossAxis||0,alignmentAxis:o.alignmentAxis};return h&&"number"==typeof s&&(r="end"===h?-1*s:s),i?{x:r*n,y:p*j}:{x:p*j,y:r*n}}function G(a){return function(a){return!1}(a)?(a.nodeName||"").toLowerCase():"#document"}function H(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function I(a){var b;return null==(b=(function(a){return!1}(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function J(a){return!1}let K=new Set(["inline","contents"]);function L(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=W(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!K.has(e)}let M=new Set(["table","td","th"]),N=[":popover-open",":modal"];function O(a){return N.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let P=["transform","translate","scale","rotate","perspective"],Q=["transform","translate","scale","rotate","perspective","filter"],R=["paint","layout","strict","content"];function S(a){let b=T(),c=a;return P.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||Q.some(a=>(c.willChange||"").includes(a))||R.some(a=>(c.contain||"").includes(a))}function T(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let U=new Set(["html","body","#document"]);function V(a){return U.has(G(a))}function W(a){return H(a).getComputedStyle(a)}function X(a){return{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function Y(a){if("html"===G(a))return a;let b=a.assignedSlot||a.parentNode||!1||I(a);return b}function Z(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=Y(b);return V(c)?b.ownerDocument?b.ownerDocument.body:b.body:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=H(e);if(f){let a=$(g);return b.concat(g,g.visualViewport||[],L(e)?e:[],a&&c?Z(a):[])}return b.concat(e,Z(e,[],c))}function $(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function _(a){let b=W(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=!1,g=e?a.offsetWidth:c,h=e?a.offsetHeight:d,i=f(c)!==g||f(d)!==h;return i&&(c=g,d=h),{width:c,height:d,$:i}}function aa(a){return 0,a.contextElement}function ab(a){let b=aa(a);1;return h(1)}let ac=h(0);function ad(a){let b=H(a);return T()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:ac}function ae(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=aa(a),i=h(1);b&&(d||(i=ab(a)));let j=(void 0===(e=c)&&(e=!1),d&&(!e||d===H(g))&&e)?ad(g):h(0),k=(f.left+j.x)/i.x,l=(f.top+j.y)/i.y,m=f.width/i.x,n=f.height/i.y;if(g){let a=H(g),b=d,c=a,e=$(c);for(;e&&d&&b!==c;){let a=ab(e),b=e.getBoundingClientRect(),d=W(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;k*=a.x,l*=a.y,m*=a.x,n*=a.y,k+=f,l+=g,e=$(c=H(e))}}return y({width:m,height:n,x:k,y:l})}function af(a,b){let c=X(a).scrollLeft;return b?b.left+c:ae(I(a)).left+c}function ag(a,b){let c=a.getBoundingClientRect();return{x:c.left+b.scrollLeft-af(a,c),y:c.top+b.scrollTop}}function ah(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=H(a),d=I(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=T();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}let j=af(d);if(j<=0){let a=d.ownerDocument,b=a.body,c=getComputedStyle(b),e="CSS1Compat"===a.compatMode&&parseFloat(c.marginLeft)+parseFloat(c.marginRight)||0,g=Math.abs(d.clientWidth-b.clientWidth-e);g<=25&&(f-=g)}else j<=25&&(f+=j);return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=I(a),c=X(a),d=a.ownerDocument.body,f=e(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),g=e(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),h=-c.scrollLeft+af(a),i=-c.scrollTop;return"rtl"===W(d).direction&&(h+=e(b.clientWidth,d.clientWidth)-f),{width:f,height:g,x:h,y:i}}(I(a));else{1;{let c=ad(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}}return y(d)}function ai(a){return"static"===W(a).position}function aj(a,b){1;return null}function ak(a,b){var c;let d=H(a);if(O(a))return d;1;{let b=Y(a);for(;b&&!V(b);){0;b=Y(b)}return d}}let al=async function(a){let b=this.getOffsetParent||ak,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){var d;let e=(d=0,!1),f=I(b),g="fixed"===c,i=ae(a,!0,g,b),j={scrollLeft:0,scrollTop:0},k=h(0);if(e||!e&&!g)if(("body"!==G(b)||L(f))&&(j=X(b)),e){let a=ae(b,!0,g,b);k.x=a.x+b.clientLeft,k.y=a.y+b.clientTop}else f&&(k.x=af(f));g&&!e&&f&&(k.x=af(f));let l=!f||e||g?h(0):ag(f,j);return{x:i.left+j.scrollLeft-k.x-l.x,y:i.top+j.scrollTop-k.y-l.y,width:i.width,height:i.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},am={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){var b,c;let{elements:d,rect:e,offsetParent:f,strategy:g}=a,i="fixed"===g,j=I(f),k=!!d&&O(d.floating);if(f===j||k&&i)return e;let l={scrollLeft:0,scrollTop:0},m=h(1),n=h(0),o=(b=0,!1);(o||!o&&!i)&&(("body"!==G(f)||L(j))&&(l=X(f)),c=0,0);let p=!j||o||i?h(0):ag(j,l);return{width:e.width*m.x,height:e.height*m.y,x:e.x*m.x-l.scrollLeft*m.x+n.x+p.x,y:e.y*m.y-l.scrollTop*m.y+n.y+p.y}},getDocumentElement:I,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:f,strategy:g}=a,h=[..."clippingAncestors"===c?O(b)?[]:function(a,b){var c;let d=b.get(a);if(d)return d;let e=Z(a,[],!1).filter(a=>{var b;return b=0,!1}),f="fixed"===W(a).position,g=f?Y(a):a;return c=0,b.set(a,e),e}(b,this._c):[].concat(c),f],i=h[0],j=h.reduce((a,c)=>{let f=ah(b,c,g);return a.top=e(f.top,a.top),a.right=d(f.right,a.right),a.bottom=d(f.bottom,a.bottom),a.left=e(f.left,a.left),a},ah(b,i,g));return{width:j.right-j.left,height:j.bottom-j.top,x:j.left,y:j.top}},getOffsetParent:ak,getElementRects:al,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=_(a);return{width:b,height:c}},getScale:ab,isElement:J,isRTL:function(a){return"rtl"===W(a).direction}};function an(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let ao=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:f,placement:g,rects:h,platform:i,elements:j,middlewareData:l}=b,{element:p,padding:r=0}=k(a,b)||{};if(null==p)return{};let s=x(r),t={x:c,y:f},u=n(q(g)),v=o(u),w=await i.getDimensions(p),y="y"===u,z=y?"clientHeight":"clientWidth",A=h.reference[v]+h.reference[u]-t[u]-h.floating[v],B=t[u]-h.reference[u],C=await (null==i.getOffsetParent?void 0:i.getOffsetParent(p)),D=C?C[z]:0;D&&await (null==i.isElement?void 0:i.isElement(C))||(D=j.floating[z]||h.floating[v]);let E=D/2-w[v]/2-1,F=d(s[y?"top":"left"],E),G=d(s[y?"bottom":"right"],E),H=D-w[v]-G,I=D/2-w[v]/2+(A/2-B/2),J=e(F,d(I,H)),K=!l.arrow&&null!=m(g)&&I!==J&&h.reference[v]/2-(I<F?F:G)-w[v]/2<0,L=K?I<F?I-F:I-H:0;return{[u]:t[u]+L,data:{[u]:J,centerOffset:I-J-L,...K&&{alignmentOffset:L}},reset:K}}});var ap=a.i(60443),aq="undefined"!=typeof document?b.useLayoutEffect:function(){};function ar(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!ar(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!ar(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function as(a,b){let c=1;return Math.round(b*c)/c}function at(a){let c=b.useRef(a);return aq(()=>{c.current=a}),c}var au=a.i(48206),av=a.i(68116),aw=b.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,av.jsx)(au.Primitive.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,av.jsx)("polygon",{points:"0,0 30,0 15,10"})})});aw.displayName="Arrow";var ax=a.i(9403),ay=a.i(54130),az=a.i(1510),aA=a.i(58714),aB="Popper",[aC,aD]=(0,ay.createContextScope)(aB),[aE,aF]=aC(aB),aG=a=>{let{__scopePopper:c,children:d}=a,[e,f]=b.useState(null);return(0,av.jsx)(aE,{scope:c,anchor:e,onAnchorChange:f,children:d})};aG.displayName=aB;var aH="PopperAnchor",aI=b.forwardRef((a,c)=>{let{__scopePopper:d,virtualRef:e,...f}=a,g=aF(aH,d),h=b.useRef(null),i=(0,ax.useComposedRefs)(c,h),j=b.useRef(null);return b.useEffect(()=>{let a=j.current;j.current=e?.current||h.current,a!==j.current&&g.onAnchorChange(j.current)}),e?null:(0,av.jsx)(au.Primitive.div,{...f,ref:i})});aI.displayName=aH;var aJ="PopperContent",[aK,aL]=aC(aJ),aM=b.forwardRef((a,c)=>{let{__scopePopper:f,side:h="bottom",sideOffset:i=0,align:j="center",alignOffset:p=0,arrowPadding:x=0,avoidCollisions:y=!0,collisionBoundary:z=[],collisionPadding:G=0,sticky:H="partial",hideWhenDetached:J=!1,updatePositionStrategy:K="optimized",onPlaced:L,...M}=a,N=aF(aJ,f),[O,P]=b.useState(null),Q=(0,ax.useComposedRefs)(c,a=>P(a)),[R,S]=b.useState(null),T=function(a){let[c,d]=b.useState(void 0);return(0,aA.useLayoutEffect)(()=>{if(a){d({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let c,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;c=b.inlineSize,e=b.blockSize}else c=a.offsetWidth,e=a.offsetHeight;d({width:c,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}d(void 0)},[a]),c}(R),U=T?.width??0,V=T?.height??0,W="number"==typeof G?G:{top:0,right:0,bottom:0,left:0,...G},X=Array.isArray(z)?z:[z],Y=X.length>0,$={padding:W,boundary:X.filter(aQ),altBoundary:Y},{refs:_,floatingStyles:ab,placement:ac,isPositioned:ad,middlewareData:af}=function(a){void 0===a&&(a={});let{placement:c="bottom",strategy:d="absolute",middleware:e=[],platform:f,elements:{reference:g,floating:h}={},transform:i=!0,whileElementsMounted:j,open:k}=a,[l,m]=b.useState({x:0,y:0,strategy:d,placement:c,middlewareData:{},isPositioned:!1}),[n,o]=b.useState(e);ar(n,e)||o(e);let[p,q]=b.useState(null),[r,s]=b.useState(null),t=b.useCallback(a=>{a!==x.current&&(x.current=a,q(a))},[]),u=b.useCallback(a=>{a!==y.current&&(y.current=a,s(a))},[]),v=g||p,w=h||r,x=b.useRef(null),y=b.useRef(null),z=b.useRef(l),B=null!=j,C=at(j),D=at(f),E=at(k),F=b.useCallback(()=>{if(!x.current||!y.current)return;let a={placement:c,strategy:d,middleware:n};D.current&&(a.platform=D.current),((a,b,c)=>{let d=new Map,e={platform:am,...c},f={...e.platform,_c:d};return A(a,b,{...e,platform:f})})(x.current,y.current,a).then(a=>{let b={...a,isPositioned:!1!==E.current};G.current&&!ar(z.current,b)&&(z.current=b,ap.flushSync(()=>{m(b)}))})},[n,c,d,D,E]);aq(()=>{!1===k&&z.current.isPositioned&&(z.current.isPositioned=!1,m(a=>({...a,isPositioned:!1})))},[k]);let G=b.useRef(!1);aq(()=>(G.current=!0,()=>{G.current=!1}),[]),aq(()=>{if(v&&(x.current=v),w&&(y.current=w),v&&w){if(C.current)return C.current(v,w,F);F()}},[v,w,F,C,B]);let H=b.useMemo(()=>({reference:x,floating:y,setReference:t,setFloating:u}),[t,u]),I=b.useMemo(()=>({reference:v,floating:w}),[v,w]),J=b.useMemo(()=>{let a={position:d,left:0,top:0};if(!I.floating)return a;let b=as(I.floating,l.x),c=as(I.floating,l.y);if(i)return{...a,transform:"translate("+b+"px, "+c+"px)",...(I.floating,false)};return{position:d,left:b,top:c}},[d,i,I.floating,l.x,l.y]);return b.useMemo(()=>({...l,update:F,refs:H,elements:I,floatingStyles:J}),[l,F,H,I,J])}({strategy:"fixed",placement:h+("center"!==j?"-"+j:""),whileElementsMounted:(...a)=>(function(a,b,c,f){let h;void 0===f&&(f={});let{ancestorScroll:i=!0,ancestorResize:j=!0,elementResize:k="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:m=!1}=f,n=aa(a),o=i||j?[...n?Z(n):[],...Z(b)]:[];o.forEach(a=>{i&&a.addEventListener("scroll",c,{passive:!0}),j&&a.addEventListener("resize",c)});let p=n&&l?function(a,b){let c,f=null,h=I(a);function i(){var a;clearTimeout(c),null==(a=f)||a.disconnect(),f=null}return!function j(k,l){void 0===k&&(k=!1),void 0===l&&(l=1),i();let m=a.getBoundingClientRect(),{left:n,top:o,width:p,height:q}=m;if(k||b(),!p||!q)return;let r=g(o),s=g(h.clientWidth-(n+p)),t={rootMargin:-r+"px "+-s+"px "+-g(h.clientHeight-(o+q))+"px "+-g(n)+"px",threshold:e(0,d(1,l))||1},u=!0;function v(b){let d=b[0].intersectionRatio;if(d!==l){if(!u)return j();d?j(!1,d):c=setTimeout(()=>{j(!1,1e-7)},1e3)}1!==d||an(m,a.getBoundingClientRect())||j(),u=!1}try{f=new IntersectionObserver(v,{...t,root:h.ownerDocument})}catch(a){f=new IntersectionObserver(v,t)}f.observe(a)}(!0),i}(n,c):null,q=-1,r=null;k&&(r=new ResizeObserver(a=>{let[d]=a;d&&d.target===n&&r&&(r.unobserve(b),cancelAnimationFrame(q),q=requestAnimationFrame(()=>{var a;null==(a=r)||a.observe(b)})),c()}),n&&!m&&r.observe(n),r.observe(b));let s=m?ae(a):null;return m&&function b(){let d=ae(a);s&&!an(s,d)&&c(),s=d,h=requestAnimationFrame(b)}(),c(),()=>{var a;o.forEach(a=>{i&&a.removeEventListener("scroll",c),j&&a.removeEventListener("resize",c)}),null==p||p(),null==(a=r)||a.disconnect(),r=null,m&&cancelAnimationFrame(h)}})(...a,{animationFrame:"always"===K}),elements:{reference:N.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await F(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,b]}))({mainAxis:i+V,alignmentAxis:p}),y&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:f,placement:g}=b,{mainAxis:h=!0,crossAxis:i=!1,limiter:j={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...m}=k(a,b),o={x:c,y:f},p=await B(b,m),r=q(l(g)),s=n(r),t=o[s],u=o[r];if(h){let a="y"===s?"top":"left",b="y"===s?"bottom":"right",c=t+p[a],f=t-p[b];t=e(c,d(t,f))}if(i){let a="y"===r?"top":"left",b="y"===r?"bottom":"right",c=u+p[a],f=u-p[b];u=e(c,d(u,f))}let v=j.fn({...b,[s]:t,[r]:u});return{...v,data:{x:v.x-c,y:v.y-f,enabled:{[s]:h,[r]:i}}}}}}(a),options:[a,b]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===H?((a,b)=>({...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=k(a,b),m={x:c,y:d},o=q(e),p=n(o),r=m[p],s=m[o],t=k(h,b),u="number"==typeof t?{mainAxis:t,crossAxis:0}:{mainAxis:0,crossAxis:0,...t};if(i){let a="y"===p?"height":"width",b=f.reference[p]-f.floating[a]+u.mainAxis,c=f.reference[p]+f.reference[a]-u.mainAxis;r<b?r=b:r>c&&(r=c)}if(j){var v,w;let a="y"===p?"width":"height",b=E.has(l(e)),c=f.reference[o]-f.floating[a]+(b&&(null==(v=g.offset)?void 0:v[o])||0)+(b?0:u.crossAxis),d=f.reference[o]+f.reference[a]+(b?0:(null==(w=g.offset)?void 0:w[o])||0)-(b?u.crossAxis:0);s<c?s=c:s>d&&(s=d)}return{[p]:r,[o]:s}}}}(a),options:[a,b]}))():void 0,...$}),y&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:p,platform:x,elements:y}=b,{mainAxis:z=!0,crossAxis:A=!0,fallbackPlacements:C,fallbackStrategy:D="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:F=!0,...G}=k(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let H=l(h),I=q(p),J=l(p)===p,K=await (null==x.isRTL?void 0:x.isRTL(y.floating)),L=C||(J||!F?[w(p)]:function(a){let b=w(a);return[r(a),b,r(b)]}(p)),M="none"!==E;!C&&M&&L.push(...function(a,b,c,d){let e=m(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?t:s;return b?s:t;case"left":case"right":return b?u:v;default:return[]}}(l(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(r)))),f}(p,F,E,K));let N=[p,...L],O=await B(b,G),P=[],Q=(null==(d=i.flip)?void 0:d.overflows)||[];if(z&&P.push(O[H]),A){let a=function(a,b,c){void 0===c&&(c=!1);let d=m(a),e=n(q(a)),f=o(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=w(g)),[g,w(g)]}(h,j,K);P.push(O[a[0]],O[a[1]])}if(Q=[...Q,{placement:h,overflows:P}],!P.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=N[a];if(b&&("alignment"!==A||I===q(b)||Q.every(a=>q(a.placement)!==I||a.overflows[0]>0)))return{data:{index:a,overflows:Q},reset:{placement:b}};let c=null==(f=Q.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(D){case"bestFit":{let a=null==(g=Q.filter(a=>{if(M){let b=q(a.placement);return b===I||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=p}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,b]}))({...$}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,f;let g,h,{placement:i,rects:j,platform:n,elements:o}=b,{apply:p=()=>{},...r}=k(a,b),s=await B(b,r),t=l(i),u=m(i),v="y"===q(i),{width:w,height:x}=j.floating;"top"===t||"bottom"===t?(g=t,h=u===(await (null==n.isRTL?void 0:n.isRTL(o.floating))?"start":"end")?"left":"right"):(h=t,g="end"===u?"top":"bottom");let y=x-s.top-s.bottom,z=w-s.left-s.right,A=d(x-s[g],y),C=d(w-s[h],z),D=!b.middlewareData.shift,E=A,F=C;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(F=z),null!=(f=b.middlewareData.shift)&&f.enabled.y&&(E=y),D&&!u){let a=e(s.left,0),b=e(s.right,0),c=e(s.top,0),d=e(s.bottom,0);v?F=w-2*(0!==a||0!==b?a+b:e(s.left,s.right)):E=x-2*(0!==c||0!==d?c+d:e(s.top,s.bottom))}await p({...b,availableWidth:F,availableHeight:E});let G=await n.getDimensions(o.floating);return w!==G.width||x!==G.height?{reset:{rects:!0}}:{}}}}(a),options:[a,b]}))({...$,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),R&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?ao({element:c.current,padding:d}).fn(b):{}:c?ao({element:c,padding:d}).fn(b):{}}}))(a),options:[a,b]}))({element:R,padding:x}),aR({arrowWidth:U,arrowHeight:V}),J&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=k(a,b);switch(d){case"referenceHidden":{let a=C(await B(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:D(a)}}}case"escaped":{let a=C(await B(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:D(a)}}}default:return{}}}}}(a),options:[a,b]}))({strategy:"referenceHidden",...$})]}),[ag,ah]=aS(ac),ai=(0,az.useCallbackRef)(L);(0,aA.useLayoutEffect)(()=>{ad&&ai?.()},[ad,ai]);let aj=af.arrow?.x,ak=af.arrow?.y,al=af.arrow?.centerOffset!==0,[aw,ay]=b.useState();return(0,aA.useLayoutEffect)(()=>{O&&ay(window.getComputedStyle(O).zIndex)},[O]),(0,av.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...ab,transform:ad?ab.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:aw,"--radix-popper-transform-origin":[af.transformOrigin?.x,af.transformOrigin?.y].join(" "),...af.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,av.jsx)(aK,{scope:f,placedSide:ag,onArrowChange:S,arrowX:aj,arrowY:ak,shouldHideArrow:al,children:(0,av.jsx)(au.Primitive.div,{"data-side":ag,"data-align":ah,...M,ref:Q,style:{...M.style,animation:ad?void 0:"none"}})})})});aM.displayName=aJ;var aN="PopperArrow",aO={top:"bottom",right:"left",bottom:"top",left:"right"},aP=b.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=aL(aN,c),f=aO[e.placedSide];return(0,av.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,av.jsx)(aw,{...d,ref:b,style:{...d.style,display:"block"}})})});function aQ(a){return null!==a}aP.displayName=aN;var aR=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=aS(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function aS(a){let[b,c="center"]=a.split("-");return[b,c]}var aT=aG,aU=aI,aV=aM,aW=aP},24321,a=>{"use strict";a.s(["Portal",()=>g]);var b=a.i(128),c=a.i(60443),d=a.i(48206),e=a.i(58714),f=a.i(68116),g=b.forwardRef((a,g)=>{let{container:h,...i}=a,[j,k]=b.useState(!1);(0,e.useLayoutEffect)(()=>k(!0),[]);let l=h||j&&globalThis?.document?.body;return l?c.default.createPortal((0,f.jsx)(d.Primitive.div,{...i,ref:g}),l):null});g.displayName="Portal"},65662,a=>{"use strict";a.s(["useControllableState",()=>e],65662);var b=a.i(128),c=a.i(58714);b[" useEffectEvent ".trim().toString()],b[" useInsertionEffect ".trim().toString()];var d=b[" useInsertionEffect ".trim().toString()]||c.useLayoutEffect;function e({prop:a,defaultProp:c,onChange:e=()=>{},caller:f}){let[g,h,i]=function({defaultProp:a,onChange:c}){let[e,f]=b.useState(a),g=b.useRef(e),h=b.useRef(c);return d(()=>{h.current=c},[c]),b.useEffect(()=>{g.current!==e&&(h.current?.(e),g.current=e)},[e,g]),[e,f,h]}({defaultProp:c,onChange:e}),j=void 0!==a,k=j?a:g;{let c=b.useRef(void 0!==a);b.useEffect(()=>{let a=c.current;if(a!==j){let b=j?"controlled":"uncontrolled";console.warn(`${f} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}c.current=j},[j,f])}return[k,b.useCallback(b=>{if(j){let c="function"==typeof b?b(a):b;c!==a&&i.current?.(c)}else h(b)},[j,a,h,i])]}Symbol("RADIX:SYNC_STATE")},22999,a=>{"use strict";a.s(["hideOthers",()=>h]);var b=new WeakMap,c=new WeakMap,d={},e=0,f=function(a){return a&&(a.host||f(a.parentNode))},g=function(a,g,h,i){var j=(Array.isArray(a)?a:[a]).map(function(a){if(g.contains(a))return a;var b=f(a);return b&&g.contains(b)?b:(console.error("aria-hidden",a,"in not contained inside",g,". Doing nothing"),null)}).filter(function(a){return!!a});d[h]||(d[h]=new WeakMap);var k=d[h],l=[],m=new Set,n=new Set(j),o=function(a){!a||m.has(a)||(m.add(a),o(a.parentNode))};j.forEach(o);var p=function(a){!a||n.has(a)||Array.prototype.forEach.call(a.children,function(a){if(m.has(a))p(a);else try{var d=a.getAttribute(i),e=null!==d&&"false"!==d,f=(b.get(a)||0)+1,g=(k.get(a)||0)+1;b.set(a,f),k.set(a,g),l.push(a),1===f&&e&&c.set(a,!0),1===g&&a.setAttribute(h,"true"),e||a.setAttribute(i,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return p(g),m.clear(),e++,function(){l.forEach(function(a){var d=b.get(a)-1,e=k.get(a)-1;b.set(a,d),k.set(a,e),d||(c.has(a)||a.removeAttribute(i),c.delete(a)),e||a.removeAttribute(h)}),--e||(b=new WeakMap,b=new WeakMap,c=new WeakMap,d={})}},h=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),g(d,e,c,"aria-hidden")):function(){return null}}},39491,a=>{"use strict";a.s(["RemoveScroll",()=>M],39491);var b,c,d=function(){return(d=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function e(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var f=("function"==typeof SuppressedError&&SuppressedError,a.i(128)),g="right-scroll-bar-position",h="width-before-scroll-bar";function i(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var j=f.useEffect,k=new WeakMap;function l(a){return a}var m=function(a){void 0===a&&(a={});var b,c,e,f=(void 0===b&&(b=l),c=[],e=!1,{read:function(){if(e)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var d=b(a,e);return c.push(d),function(){c=c.filter(function(a){return a!==d})}},assignSyncMedium:function(a){for(e=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){e=!0;var b=[];if(c.length){var d=c;c=[],d.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return f.options=d({async:!0,ssr:!1},a),f}(),n=function(){},o=f.forwardRef(function(a,b){var c,g,h,l,o=f.useRef(null),p=f.useState({onScrollCapture:n,onWheelCapture:n,onTouchMoveCapture:n}),q=p[0],r=p[1],s=a.forwardProps,t=a.children,u=a.className,v=a.removeScrollBar,w=a.enabled,x=a.shards,y=a.sideCar,z=a.noRelative,A=a.noIsolation,B=a.inert,C=a.allowPinchZoom,D=a.as,E=a.gapMode,F=e(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),G=(c=[o,b],g=function(a){return c.forEach(function(b){return i(b,a)})},(h=(0,f.useState)(function(){return{value:null,callback:g,facade:{get current(){return h.value},set current(value){var a=h.value;a!==value&&(h.value=value,h.callback(value,a))}}}})[0]).callback=g,l=h.facade,j(function(){var a=k.get(l);if(a){var b=new Set(a),d=new Set(c),e=l.current;b.forEach(function(a){d.has(a)||i(a,null)}),d.forEach(function(a){b.has(a)||i(a,e)})}k.set(l,c)},[c]),l),H=d(d({},F),q);return f.createElement(f.Fragment,null,w&&f.createElement(y,{sideCar:m,removeScrollBar:v,shards:x,noRelative:z,noIsolation:A,inert:B,setCallbacks:r,allowPinchZoom:!!C,lockRef:o,gapMode:E}),s?f.cloneElement(f.Children.only(t),d(d({},H),{ref:G})):f.createElement(void 0===D?"div":D,d({},H,{className:u,ref:G}),t))});o.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},o.classNames={fullWidth:h,zeroRight:g};var p=function(a){var b=a.sideCar,c=e(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var g=b.read();if(!g)throw Error("Sidecar medium not found");return f.createElement(g,d({},c))};p.isSideCarExport=!0;var q=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=c||("undefined"!=typeof __webpack_nonce__?__webpack_nonce__:void 0);return b&&a.setAttribute("nonce",b),a}())){var e,f;(e=b).styleSheet?e.styleSheet.cssText=d:e.appendChild(document.createTextNode(d)),f=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(f)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},r=function(){var a=q();return function(b,c){f.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},s=function(){var a=r();return function(b){return a(b.styles,b.dynamic),null}},t={left:0,top:0,right:0,gap:0},u=s(),v="data-scroll-locked",w=function(a,b,c,d){var e=a.left,f=a.top,i=a.right,j=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(j,"px ").concat(d,";\n  }\n  body[").concat(v,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(j,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(j,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(g," {\n    right: ").concat(j,"px ").concat(d,";\n  }\n  \n  .").concat(h," {\n    margin-right: ").concat(j,"px ").concat(d,";\n  }\n  \n  .").concat(g," .").concat(g," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(h," .").concat(h," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(v,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(j,"px;\n  }\n")},x=function(){var a=parseInt(document.body.getAttribute(v)||"0",10);return isFinite(a)?a:0},y=function(){f.useEffect(function(){return document.body.setAttribute(v,(x()+1).toString()),function(){var a=x()-1;a<=0?document.body.removeAttribute(v):document.body.setAttribute(v,a.toString())}},[])},z=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;y();var g=f.useMemo(function(){return void 0===e,t},[e]);return f.createElement(u,{styles:w(g,!b,e,c?"":"!important")})},A=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},B=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),C(a,d)){var e=D(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body)return!1},C=function(a,b){return"v"===a?A(b,"overflowY"):A(b,"overflowX")},D=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},E=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=D(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&C(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i))return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},F=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},G=function(a){return[a.deltaX,a.deltaY]},H=function(a){return a&&"current"in a?a.current:a},I=0,J=[];let K=(b=function(a){var b=f.useRef([]),c=f.useRef([0,0]),d=f.useRef(),e=f.useState(I++)[0],g=f.useState(s)[0],h=f.useRef(a);f.useEffect(function(){h.current=a},[a]),f.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(H),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=f.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!h.current.allowPinchZoom;var e,f=F(a),g=c.current,i="deltaX"in a?a.deltaX:g[0]-f[0],j="deltaY"in a?a.deltaY:g[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=B(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=B(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return E(n,b,a,"h"===n?i:j,!0)},[]),j=f.useCallback(function(a){if(J.length&&J[J.length-1]===g){var c="deltaY"in a?G(a):F(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(h.current.shards||[]).map(H).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!h.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=f.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=f.useCallback(function(a){c.current=F(a),d.current=void 0},[]),m=f.useCallback(function(b){k(b.type,G(b),b.target,i(b,a.lockRef.current))},[]),n=f.useCallback(function(b){k(b.type,F(b),b.target,i(b,a.lockRef.current))},[]);f.useEffect(function(){return J.push(g),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,!1),document.addEventListener("touchmove",j,!1),document.addEventListener("touchstart",l,!1),function(){J=J.filter(function(a){return a!==g}),document.removeEventListener("wheel",j,!1),document.removeEventListener("touchmove",j,!1),document.removeEventListener("touchstart",l,!1)}},[]);var o=a.removeScrollBar,p=a.inert;return f.createElement(f.Fragment,null,p?f.createElement(g,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?f.createElement(z,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},m.useMedium(b),p);var L=f.forwardRef(function(a,b){return f.createElement(o,d({},a,{ref:b,sideCar:K}))});L.classNames=o.classNames;let M=L}];

//# sourceMappingURL=9e883__pnpm_880677f9._.js.map