import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ReunionsController } from './reunions.controller';
import { ReunionsService } from './reunions.service';
import { Reunion, ReunionSchema } from './schemas/reunion.schema';
import { Session, SessionSchema } from '../sessions/schemas/session.schema';
import { SessionMember, SessionMemberSchema } from '../sessions/schemas/session-member.schema';
import { Member, MemberSchema } from '../members/schemas/member.schema';
import { Payment, PaymentSchema } from '../payments/schemas/payment.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Reunion.name, schema: ReunionSchema },
      { name: Session.name, schema: SessionSchema },
      { name: SessionMember.name, schema: SessionMemberSchema },
      { name: Member.name, schema: MemberSchema },
      { name: Payment.name, schema: PaymentSchema },
    ]),
  ],
  controllers: [ReunionsController],
  providers: [ReunionsService],
  exports: [ReunionsService],
})
export class ReunionsModule {}