import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Session, SessionDocument } from './schemas/session.schema';
import { SessionMember, SessionMemberDocument } from './schemas/session-member.schema';
import { Reunion, ReunionDocument } from '../reunions/schemas/reunion.schema';

// Service planificateur pour mettre à jour expectedToDate et overdueAmount
@Injectable()
export class SessionsScheduler {
  private readonly logger = new Logger(SessionsScheduler.name);
  constructor(
    @InjectModel(Session.name) private sessionModel: Model<SessionDocument>,
    @InjectModel(SessionMember.name) private sessionMemberModel: Model<SessionMemberDocument>,
    @InjectModel(Reunion.name) private reunionModel: Model<ReunionDocument>,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async updateExpectedAndOverdue() {
    // Pour chaque session, compter le nombre de reunions passées et mettre à jour expectedToDate et overdueAmount
    const now = new Date();
    const sessions = await this.sessionModel.find().lean();
    for (const s of sessions) {
      const reunionsPassed = await this.reunionModel.countDocuments({ sessionId: s._id, dateReunion: { $lte: now } });
      const members = await this.sessionMemberModel.find({ sessionId: s._id });
      for (const m of members) {
        const perMeetingDue = s.partFixe * m.parts; // montant par réunion pour ce membre
        const expected = perMeetingDue * reunionsPassed;
        m.expectedToDate = expected;
        m.overdueAmount = Math.max(0, expected - (m.paidSoFar || 0));
        await m.save();
      }
    }
    this.logger.log('Session members expected/overdue updated');
  }
}