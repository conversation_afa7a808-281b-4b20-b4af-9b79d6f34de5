module.exports=[75780,a=>{"use strict";a.s(["Card",()=>d,"CardContent",()=>h,"CardDescription",()=>g,"CardHeader",()=>e,"CardTitle",()=>f]);var b=a.i(68116),c=a.i(22171);function d({className:a,...d}){return(0,b.jsx)("div",{"data-slot":"card",className:(0,c.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...d})}function e({className:a,...d}){return(0,b.jsx)("div",{"data-slot":"card-header",className:(0,c.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...d})}function f({className:a,...d}){return(0,b.jsx)("div",{"data-slot":"card-title",className:(0,c.cn)("leading-none font-semibold",a),...d})}function g({className:a,...d}){return(0,b.jsx)("div",{"data-slot":"card-description",className:(0,c.cn)("text-muted-foreground text-sm",a),...d})}function h({className:a,...d}){return(0,b.jsx)("div",{"data-slot":"card-content",className:(0,c.cn)("px-6",a),...d})}},91530,88344,a=>{"use strict";a.s(["$ZodAsyncError",()=>e,"$ZodEncodeError",()=>f,"$brand",()=>d,"$constructor",()=>c,"NEVER",()=>b,"config",()=>h,"globalConfig",()=>g],91530);let b=Object.freeze({status:"aborted"});function c(a,b,c){function d(c,d){var e;for(let f in Object.defineProperty(c,"_zod",{value:c._zod??{},enumerable:!1}),(e=c._zod).traits??(e.traits=new Set),c._zod.traits.add(a),b(c,d),g.prototype)f in c||Object.defineProperty(c,f,{value:g.prototype[f].bind(c)});c._zod.constr=g,c._zod.def=d}let e=c?.Parent??Object;class f extends e{}function g(a){var b;let e=c?.Parent?new f:this;for(let c of(d(e,a),(b=e._zod).deferred??(b.deferred=[]),e._zod.deferred))c();return e}return Object.defineProperty(f,"name",{value:a}),Object.defineProperty(g,"init",{value:d}),Object.defineProperty(g,Symbol.hasInstance,{value:b=>!!c?.Parent&&b instanceof c.Parent||b?._zod?.traits?.has(a)}),Object.defineProperty(g,"name",{value:a}),g}let d=Symbol("zod_brand");class e extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}class f extends Error{constructor(a){super(`Encountered unidirectional transform during encode: ${a}`),this.name="ZodEncodeError"}}let g={};function h(a){return a&&Object.assign(g,a),g}function i(a){return a}function j(a){return a}function k(a){}function l(a){throw Error()}function m(a){}function n(a){let b=Object.values(a).filter(a=>"number"==typeof a);return Object.entries(a).filter(([a,c])=>-1===b.indexOf(+a)).map(([a,b])=>b)}function o(a,b="|"){return a.map(a=>R(a)).join(b)}function p(a,b){return"bigint"==typeof b?b.toString():b}function q(a){return{get value(){{let b=a();return Object.defineProperty(this,"value",{value:b}),b}}}}function r(a){return null==a}function s(a){let b=+!!a.startsWith("^"),c=a.endsWith("$")?a.length-1:a.length;return a.slice(b,c)}function t(a,b){let c=(a.toString().split(".")[1]||"").length,d=b.toString(),e=(d.split(".")[1]||"").length;if(0===e&&/\d?e-\d?/.test(d)){let a=d.match(/\d?e-(\d?)/);a?.[1]&&(e=Number.parseInt(a[1]))}let f=c>e?c:e;return Number.parseInt(a.toFixed(f).replace(".",""))%Number.parseInt(b.toFixed(f).replace(".",""))/10**f}a.s(["BIGINT_FORMAT_RANGES",()=>U,"Class",()=>ao,"NUMBER_FORMAT_RANGES",()=>T,"aborted",()=>aa,"allowsEval",()=>G,"assert",()=>m,"assertEqual",()=>i,"assertIs",()=>k,"assertNever",()=>l,"assertNotEqual",()=>j,"assignProp",()=>x,"base64ToUint8Array",()=>ai,"base64urlToUint8Array",()=>ak,"cached",()=>q,"captureStackTrace",()=>E,"cleanEnum",()=>ah,"cleanRegex",()=>s,"clone",()=>O,"cloneDef",()=>z,"createTransparentProxy",()=>Q,"defineLazy",()=>v,"esc",()=>D,"escapeRegex",()=>N,"extend",()=>X,"finalizeIssue",()=>ad,"floatSafeRemainder",()=>t,"getElementAtPath",()=>A,"getEnumValues",()=>n,"getLengthableOrigin",()=>af,"getParsedType",()=>K,"getSizableOrigin",()=>ae,"hexToUint8Array",()=>am,"isObject",()=>F,"isPlainObject",()=>H,"issue",()=>ag,"joinValues",()=>o,"jsonStringifyReplacer",()=>p,"merge",()=>Z,"mergeDefs",()=>y,"normalizeParams",()=>P,"nullish",()=>r,"numKeys",()=>J,"objectClone",()=>w,"omit",()=>W,"optionalKeys",()=>S,"partial",()=>$,"pick",()=>V,"prefixIssues",()=>ab,"primitiveTypes",()=>M,"promiseAllObject",()=>B,"propertyKeyTypes",()=>L,"randomString",()=>C,"required",()=>_,"safeExtend",()=>Y,"shallowClone",()=>I,"stringifyPrimitive",()=>R,"uint8ArrayToBase64",()=>aj,"uint8ArrayToBase64url",()=>al,"uint8ArrayToHex",()=>an,"unwrapMessage",()=>ac],88344);let u=Symbol("evaluating");function v(a,b,c){let d;Object.defineProperty(a,b,{get(){if(d!==u)return void 0===d&&(d=u,d=c()),d},set(c){Object.defineProperty(a,b,{value:c})},configurable:!0})}function w(a){return Object.create(Object.getPrototypeOf(a),Object.getOwnPropertyDescriptors(a))}function x(a,b,c){Object.defineProperty(a,b,{value:c,writable:!0,enumerable:!0,configurable:!0})}function y(...a){let b={};for(let c of a)Object.assign(b,Object.getOwnPropertyDescriptors(c));return Object.defineProperties({},b)}function z(a){return y(a._zod.def)}function A(a,b){return b?b.reduce((a,b)=>a?.[b],a):a}function B(a){let b=Object.keys(a);return Promise.all(b.map(b=>a[b])).then(a=>{let c={};for(let d=0;d<b.length;d++)c[b[d]]=a[d];return c})}function C(a=10){let b="abcdefghijklmnopqrstuvwxyz",c="";for(let d=0;d<a;d++)c+=b[Math.floor(Math.random()*b.length)];return c}function D(a){return JSON.stringify(a)}let E="captureStackTrace"in Error?Error.captureStackTrace:(...a)=>{};function F(a){return"object"==typeof a&&null!==a&&!Array.isArray(a)}let G=q(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(a){return!1}});function H(a){if(!1===F(a))return!1;let b=a.constructor;if(void 0===b)return!0;let c=b.prototype;return!1!==F(c)&&!1!==Object.prototype.hasOwnProperty.call(c,"isPrototypeOf")}function I(a){return H(a)?{...a}:a}function J(a){let b=0;for(let c in a)Object.prototype.hasOwnProperty.call(a,c)&&b++;return b}let K=a=>{let b=typeof a;switch(b){case"undefined":return"undefined";case"string":return"string";case"number":return Number.isNaN(a)?"nan":"number";case"boolean":return"boolean";case"function":return"function";case"bigint":return"bigint";case"symbol":return"symbol";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(a.then&&"function"==typeof a.then&&a.catch&&"function"==typeof a.catch)return"promise";if("undefined"!=typeof Map&&a instanceof Map)return"map";if("undefined"!=typeof Set&&a instanceof Set)return"set";if("undefined"!=typeof Date&&a instanceof Date)return"date";if("undefined"!=typeof File&&a instanceof File)return"file";return"object";default:throw Error(`Unknown data type: ${b}`)}},L=new Set(["string","number","symbol"]),M=new Set(["string","number","bigint","boolean","symbol","undefined"]);function N(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function O(a,b,c){let d=new a._zod.constr(b??a._zod.def);return(!b||c?.parent)&&(d._zod.parent=a),d}function P(a){if(!a)return{};if("string"==typeof a)return{error:()=>a};if(a?.message!==void 0){if(a?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");a.error=a.message}return(delete a.message,"string"==typeof a.error)?{...a,error:()=>a.error}:a}function Q(a){let b;return new Proxy({},{get:(c,d,e)=>(b??(b=a()),Reflect.get(b,d,e)),set:(c,d,e,f)=>(b??(b=a()),Reflect.set(b,d,e,f)),has:(c,d)=>(b??(b=a()),Reflect.has(b,d)),deleteProperty:(c,d)=>(b??(b=a()),Reflect.deleteProperty(b,d)),ownKeys:c=>(b??(b=a()),Reflect.ownKeys(b)),getOwnPropertyDescriptor:(c,d)=>(b??(b=a()),Reflect.getOwnPropertyDescriptor(b,d)),defineProperty:(c,d,e)=>(b??(b=a()),Reflect.defineProperty(b,d,e))})}function R(a){return"bigint"==typeof a?a.toString()+"n":"string"==typeof a?`"${a}"`:`${a}`}function S(a){return Object.keys(a).filter(b=>"optional"===a[b]._zod.optin&&"optional"===a[b]._zod.optout)}let T={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-0x80000000,0x7fffffff],uint32:[0,0xffffffff],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]},U={int64:[BigInt("-9223372036854775808"),BigInt("9223372036854775807")],uint64:[BigInt(0),BigInt("18446744073709551615")]};function V(a,b){let c=a._zod.def,d=y(a._zod.def,{get shape(){let a={};for(let d in b){if(!(d in c.shape))throw Error(`Unrecognized key: "${d}"`);b[d]&&(a[d]=c.shape[d])}return x(this,"shape",a),a},checks:[]});return O(a,d)}function W(a,b){let c=a._zod.def,d=y(a._zod.def,{get shape(){let d={...a._zod.def.shape};for(let a in b){if(!(a in c.shape))throw Error(`Unrecognized key: "${a}"`);b[a]&&delete d[a]}return x(this,"shape",d),d},checks:[]});return O(a,d)}function X(a,b){if(!H(b))throw Error("Invalid input to extend: expected a plain object");let c=a._zod.def.checks;if(c&&c.length>0)throw Error("Object schemas containing refinements cannot be extended. Use `.safeExtend()` instead.");let d=y(a._zod.def,{get shape(){let c={...a._zod.def.shape,...b};return x(this,"shape",c),c},checks:[]});return O(a,d)}function Y(a,b){if(!H(b))throw Error("Invalid input to safeExtend: expected a plain object");let c={...a._zod.def,get shape(){let c={...a._zod.def.shape,...b};return x(this,"shape",c),c},checks:a._zod.def.checks};return O(a,c)}function Z(a,b){let c=y(a._zod.def,{get shape(){let c={...a._zod.def.shape,...b._zod.def.shape};return x(this,"shape",c),c},get catchall(){return b._zod.def.catchall},checks:[]});return O(a,c)}function $(a,b,c){let d=y(b._zod.def,{get shape(){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in d))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=a?new a({type:"optional",innerType:d[b]}):d[b])}else for(let b in d)e[b]=a?new a({type:"optional",innerType:d[b]}):d[b];return x(this,"shape",e),e},checks:[]});return O(b,d)}function _(a,b,c){let d=y(b._zod.def,{get shape(){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in e))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=new a({type:"nonoptional",innerType:d[b]}))}else for(let b in d)e[b]=new a({type:"nonoptional",innerType:d[b]});return x(this,"shape",e),e},checks:[]});return O(b,d)}function aa(a,b=0){if(!0===a.aborted)return!0;for(let c=b;c<a.issues.length;c++)if(a.issues[c]?.continue!==!0)return!0;return!1}function ab(a,b){return b.map(b=>(b.path??(b.path=[]),b.path.unshift(a),b))}function ac(a){return"string"==typeof a?a:a?.message}function ad(a,b,c){let d={...a,path:a.path??[]};return a.message||(d.message=ac(a.inst?._zod.def?.error?.(a))??ac(b?.error?.(a))??ac(c.customError?.(a))??ac(c.localeError?.(a))??"Invalid input"),delete d.inst,delete d.continue,b?.reportInput||delete d.input,d}function ae(a){return a instanceof Set?"set":a instanceof Map?"map":a instanceof File?"file":"unknown"}function af(a){return Array.isArray(a)?"array":"string"==typeof a?"string":"unknown"}function ag(...a){let[b,c,d]=a;return"string"==typeof b?{message:b,code:"custom",input:c,inst:d}:{...b}}function ah(a){return Object.entries(a).filter(([a,b])=>Number.isNaN(Number.parseInt(a,10))).map(a=>a[1])}function ai(a){let b=atob(a),c=new Uint8Array(b.length);for(let a=0;a<b.length;a++)c[a]=b.charCodeAt(a);return c}function aj(a){let b="";for(let c=0;c<a.length;c++)b+=String.fromCharCode(a[c]);return btoa(b)}function ak(a){let b=a.replace(/-/g,"+").replace(/_/g,"/"),c="=".repeat((4-b.length%4)%4);return ai(b+c)}function al(a){return aj(a).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function am(a){let b=a.replace(/^0x/,"");if(b.length%2!=0)throw Error("Invalid hex string length");let c=new Uint8Array(b.length/2);for(let a=0;a<b.length;a+=2)c[a/2]=Number.parseInt(b.slice(a,a+2),16);return c}function an(a){return Array.from(a).map(a=>a.toString(16).padStart(2,"0")).join("")}class ao{constructor(...a){}}},93711,79410,69585,a=>{"use strict";a.s([],93711);var b=a.i(91530);a.s(["default",()=>d],79410);var c=a.i(88344);function d(){return{localeError:(()=>{let a={string:{unit:"characters",verb:"to have"},file:{unit:"bytes",verb:"to have"},array:{unit:"items",verb:"to have"},set:{unit:"items",verb:"to have"}},b={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return d=>{switch(d.code){case"invalid_type":return`Invalid input: expected ${d.expected}, received ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(d.input)}`;case"invalid_value":if(1===d.values.length)return`Invalid input: expected ${c.stringifyPrimitive(d.values[0])}`;return`Invalid option: expected one of ${c.joinValues(d.values,"|")}`;case"too_big":{let b=d.inclusive?"<=":"<",c=a[d.origin]??null;if(c)return`Too big: expected ${d.origin??"value"} to have ${b}${d.maximum.toString()} ${c.unit??"elements"}`;return`Too big: expected ${d.origin??"value"} to be ${b}${d.maximum.toString()}`}case"too_small":{let b=d.inclusive?">=":">",c=a[d.origin]??null;if(c)return`Too small: expected ${d.origin} to have ${b}${d.minimum.toString()} ${c.unit}`;return`Too small: expected ${d.origin} to be ${b}${d.minimum.toString()}`}case"invalid_format":if("starts_with"===d.format)return`Invalid string: must start with "${d.prefix}"`;if("ends_with"===d.format)return`Invalid string: must end with "${d.suffix}"`;if("includes"===d.format)return`Invalid string: must include "${d.includes}"`;if("regex"===d.format)return`Invalid string: must match pattern ${d.pattern}`;return`Invalid ${b[d.format]??d.format}`;case"not_multiple_of":return`Invalid number: must be a multiple of ${d.divisor}`;case"unrecognized_keys":return`Unrecognized key${d.keys.length>1?"s":""}: ${c.joinValues(d.keys,", ")}`;case"invalid_key":return`Invalid key in ${d.origin}`;case"invalid_union":default:return"Invalid input";case"invalid_element":return`Invalid value in ${d.origin}`}}})()}}(0,b.config)(d()),a.s([],69585)},62360,76697,a=>{"use strict";a.s(["_decode",()=>v,"_decodeAsync",()=>z,"_encode",()=>t,"_encodeAsync",()=>x,"_parse",()=>l,"_parseAsync",()=>n,"_safeDecode",()=>D,"_safeDecodeAsync",()=>H,"_safeEncode",()=>B,"_safeEncodeAsync",()=>F,"_safeParse",()=>p,"_safeParseAsync",()=>r,"decode",()=>w,"decodeAsync",()=>A,"encode",()=>u,"encodeAsync",()=>y,"parse",()=>m,"parseAsync",()=>o,"safeDecode",()=>E,"safeDecodeAsync",()=>I,"safeEncode",()=>C,"safeEncodeAsync",()=>G,"safeParse",()=>q,"safeParseAsync",()=>s],62360);var b=a.i(91530);a.s(["$ZodError",()=>e,"$ZodRealError",()=>f,"flattenError",()=>g,"formatError",()=>h,"prettifyError",()=>k,"toDotPath",()=>j,"treeifyError",()=>i],76697);var c=a.i(88344);let d=(a,b)=>{a.name="$ZodError",Object.defineProperty(a,"_zod",{value:a._zod,enumerable:!1}),Object.defineProperty(a,"issues",{value:b,enumerable:!1}),a.message=JSON.stringify(b,c.jsonStringifyReplacer,2),Object.defineProperty(a,"toString",{value:()=>a.message,enumerable:!1})},e=(0,b.$constructor)("$ZodError",d),f=(0,b.$constructor)("$ZodError",d,{Parent:Error});function g(a,b=a=>a.message){let c={},d=[];for(let e of a.issues)e.path.length>0?(c[e.path[0]]=c[e.path[0]]||[],c[e.path[0]].push(b(e))):d.push(b(e));return{formErrors:d,fieldErrors:c}}function h(a,b){let c=b||function(a){return a.message},d={_errors:[]},e=a=>{for(let b of a.issues)if("invalid_union"===b.code&&b.errors.length)b.errors.map(a=>e({issues:a}));else if("invalid_key"===b.code)e({issues:b.issues});else if("invalid_element"===b.code)e({issues:b.issues});else if(0===b.path.length)d._errors.push(c(b));else{let a=d,e=0;for(;e<b.path.length;){let d=b.path[e];e===b.path.length-1?(a[d]=a[d]||{_errors:[]},a[d]._errors.push(c(b))):a[d]=a[d]||{_errors:[]},a=a[d],e++}}};return e(a),d}function i(a,b){let c=b||function(a){return a.message},d={errors:[]},e=(a,b=[])=>{var f,g;for(let h of a.issues)if("invalid_union"===h.code&&h.errors.length)h.errors.map(a=>e({issues:a},h.path));else if("invalid_key"===h.code)e({issues:h.issues},h.path);else if("invalid_element"===h.code)e({issues:h.issues},h.path);else{let a=[...b,...h.path];if(0===a.length){d.errors.push(c(h));continue}let e=d,i=0;for(;i<a.length;){let b=a[i],d=i===a.length-1;"string"==typeof b?(e.properties??(e.properties={}),(f=e.properties)[b]??(f[b]={errors:[]}),e=e.properties[b]):(e.items??(e.items=[]),(g=e.items)[b]??(g[b]={errors:[]}),e=e.items[b]),d&&e.errors.push(c(h)),i++}}};return e(a),d}function j(a){let b=[];for(let c of a.map(a=>"object"==typeof a?a.key:a))"number"==typeof c?b.push(`[${c}]`):"symbol"==typeof c?b.push(`[${JSON.stringify(String(c))}]`):/[^\w$]/.test(c)?b.push(`[${JSON.stringify(c)}]`):(b.length&&b.push("."),b.push(c));return b.join("")}function k(a){let b=[];for(let c of[...a.issues].sort((a,b)=>(a.path??[]).length-(b.path??[]).length))b.push(`✖ ${c.message}`),c.path?.length&&b.push(`  → at ${j(c.path)}`);return b.join("\n")}let l=a=>(d,e,f,g)=>{let h=f?Object.assign(f,{async:!1}):{async:!1},i=d._zod.run({value:e,issues:[]},h);if(i instanceof Promise)throw new b.$ZodAsyncError;if(i.issues.length){let d=new(g?.Err??a)(i.issues.map(a=>c.finalizeIssue(a,h,b.config())));throw c.captureStackTrace(d,g?.callee),d}return i.value},m=l(f),n=a=>async(d,e,f,g)=>{let h=f?Object.assign(f,{async:!0}):{async:!0},i=d._zod.run({value:e,issues:[]},h);if(i instanceof Promise&&(i=await i),i.issues.length){let d=new(g?.Err??a)(i.issues.map(a=>c.finalizeIssue(a,h,b.config())));throw c.captureStackTrace(d,g?.callee),d}return i.value},o=n(f),p=a=>(d,f,g)=>{let h=g?{...g,async:!1}:{async:!1},i=d._zod.run({value:f,issues:[]},h);if(i instanceof Promise)throw new b.$ZodAsyncError;return i.issues.length?{success:!1,error:new(a??e)(i.issues.map(a=>c.finalizeIssue(a,h,b.config())))}:{success:!0,data:i.value}},q=p(f),r=a=>async(d,e,f)=>{let g=f?Object.assign(f,{async:!0}):{async:!0},h=d._zod.run({value:e,issues:[]},g);return h instanceof Promise&&(h=await h),h.issues.length?{success:!1,error:new a(h.issues.map(a=>c.finalizeIssue(a,g,b.config())))}:{success:!0,data:h.value}},s=r(f),t=a=>(b,c,d)=>{let e=d?Object.assign(d,{direction:"backward"}):{direction:"backward"};return l(a)(b,c,e)},u=t(f),v=a=>(b,c,d)=>l(a)(b,c,d),w=v(f),x=a=>async(b,c,d)=>{let e=d?Object.assign(d,{direction:"backward"}):{direction:"backward"};return n(a)(b,c,e)},y=x(f),z=a=>async(b,c,d)=>n(a)(b,c,d),A=z(f),B=a=>(b,c,d)=>{let e=d?Object.assign(d,{direction:"backward"}):{direction:"backward"};return p(a)(b,c,e)},C=B(f),D=a=>(b,c,d)=>p(a)(b,c,d),E=D(f),F=a=>async(b,c,d)=>{let e=d?Object.assign(d,{direction:"backward"}):{direction:"backward"};return r(a)(b,c,e)},G=F(f),H=a=>async(b,c,d)=>r(a)(b,c,d),I=H(f)},66446,a=>{"use strict";a.s(["z",()=>hR],66446),a.s(["$brand",()=>hG.$brand,"$input",()=>cY,"$output",()=>cX,"NEVER",()=>hG.NEVER,"TimePrecision",()=>dp,"ZodAny",()=>gl,"ZodArray",()=>gv,"ZodBase64",()=>fP,"ZodBase64URL",()=>fR,"ZodBigInt",()=>ga,"ZodBigIntFormat",()=>gc,"ZodBoolean",()=>f8,"ZodCIDRv4",()=>fL,"ZodCIDRv6",()=>fN,"ZodCUID",()=>fx,"ZodCUID2",()=>fz,"ZodCatch",()=>hb,"ZodCodec",()=>hh,"ZodCustom",()=>ht,"ZodCustomStringFormat",()=>fX,"ZodDate",()=>gt,"ZodDefault",()=>g3,"ZodDiscriminatedUnion",()=>gE,"ZodE164",()=>fT,"ZodEmail",()=>fh,"ZodEmoji",()=>ft,"ZodEnum",()=>gR,"ZodError",()=>e$,"ZodFile",()=>gW,"ZodFirstPartyTypeKind",()=>b,"ZodFunction",()=>hr,"ZodGUID",()=>fj,"ZodIPv4",()=>fH,"ZodIPv6",()=>fJ,"ZodISODate",()=>eT,"ZodISODateTime",()=>eR,"ZodISODuration",()=>eX,"ZodISOTime",()=>eV,"ZodIntersection",()=>gG,"ZodIssueCode",()=>hC,"ZodJWT",()=>fV,"ZodKSUID",()=>fF,"ZodLazy",()=>hn,"ZodLiteral",()=>gU,"ZodMap",()=>gN,"ZodNaN",()=>hd,"ZodNanoID",()=>fv,"ZodNever",()=>gp,"ZodNonOptional",()=>g7,"ZodNull",()=>gj,"ZodNullable",()=>g0,"ZodNumber",()=>f0,"ZodNumberFormat",()=>f2,"ZodObject",()=>gy,"ZodOptional",()=>g$,"ZodPipe",()=>hf,"ZodPrefault",()=>g5,"ZodPromise",()=>hp,"ZodReadonly",()=>hj,"ZodRealError",()=>e_,"ZodRecord",()=>gK,"ZodSet",()=>gP,"ZodString",()=>fe,"ZodStringFormat",()=>fg,"ZodSuccess",()=>g9,"ZodSymbol",()=>gf,"ZodTemplateLiteral",()=>hl,"ZodTransform",()=>gY,"ZodTuple",()=>gI,"ZodType",()=>fc,"ZodULID",()=>fB,"ZodURL",()=>fq,"ZodUUID",()=>fl,"ZodUndefined",()=>gh,"ZodUnion",()=>gC,"ZodUnknown",()=>gn,"ZodVoid",()=>gr,"ZodXID",()=>fD,"_ZodString",()=>fd,"_default",()=>g4,"_function",()=>hs,"any",()=>gm,"array",()=>gw,"base64",()=>fQ,"base64url",()=>fS,"bigint",()=>gb,"boolean",()=>f9,"catch",()=>hc,"check",()=>hu,"cidrv4",()=>fM,"cidrv6",()=>fO,"clone",()=>hH.clone,"codec",()=>hi,"coerce",()=>hQ,"config",()=>hG.config,"core",()=>eO,"cuid",()=>fy,"cuid2",()=>fA,"custom",()=>hv,"date",()=>gu,"decode",()=>e5,"decodeAsync",()=>e7,"discriminatedUnion",()=>gF,"e164",()=>fU,"email",()=>fi,"emoji",()=>fu,"encode",()=>e4,"encodeAsync",()=>e6,"endsWith",()=>d9,"enum",()=>gS,"file",()=>gX,"flattenError",()=>hI.flattenError,"float32",()=>f4,"float64",()=>f5,"formatError",()=>hI.formatError,"function",()=>hs,"getErrorMap",()=>hE,"globalRegistry",()=>c_,"gt",()=>dT,"gte",()=>dU,"guid",()=>fk,"hash",()=>f_,"hex",()=>f$,"hostname",()=>fZ,"httpUrl",()=>fs,"includes",()=>d7,"instanceof",()=>hy,"int",()=>f3,"int32",()=>f6,"int64",()=>gd,"intersection",()=>gH,"ipv4",()=>fI,"ipv6",()=>fK,"iso",()=>hK,"json",()=>hA,"jwt",()=>fW,"keyof",()=>gx,"ksuid",()=>fG,"lazy",()=>ho,"length",()=>d3,"literal",()=>gV,"locales",()=>hJ,"looseObject",()=>gB,"lowercase",()=>d5,"lt",()=>dR,"lte",()=>dS,"map",()=>gO,"maxLength",()=>d1,"maxSize",()=>d$,"mime",()=>eb,"minLength",()=>d2,"minSize",()=>d_,"multipleOf",()=>dZ,"nan",()=>he,"nanoid",()=>fw,"nativeEnum",()=>gT,"negative",()=>dW,"never",()=>gq,"nonnegative",()=>dY,"nonoptional",()=>g8,"nonpositive",()=>dX,"normalize",()=>ed,"null",()=>gk,"nullable",()=>g1,"nullish",()=>g2,"number",()=>f1,"object",()=>gz,"optional",()=>g_,"overwrite",()=>ec,"parse",()=>e0,"parseAsync",()=>e1,"partialRecord",()=>gM,"pipe",()=>hg,"positive",()=>dV,"prefault",()=>g6,"preprocess",()=>hB,"prettifyError",()=>hI.prettifyError,"promise",()=>hq,"property",()=>ea,"readonly",()=>hk,"record",()=>gL,"refine",()=>hw,"regex",()=>d4,"regexes",()=>eP,"registry",()=>c$,"safeDecode",()=>e9,"safeDecodeAsync",()=>fb,"safeEncode",()=>e8,"safeEncodeAsync",()=>fa,"safeParse",()=>e2,"safeParseAsync",()=>e3,"set",()=>gQ,"setErrorMap",()=>hD,"size",()=>d0,"startsWith",()=>d8,"strictObject",()=>gA,"string",()=>ff,"stringFormat",()=>fY,"stringbool",()=>hz,"success",()=>ha,"superRefine",()=>hx,"symbol",()=>gg,"templateLiteral",()=>hm,"toJSONSchema",()=>eM,"toLowerCase",()=>ef,"toUpperCase",()=>eg,"transform",()=>gZ,"treeifyError",()=>hI.treeifyError,"trim",()=>ee,"tuple",()=>gJ,"uint32",()=>f7,"uint64",()=>ge,"ulid",()=>fC,"undefined",()=>gi,"union",()=>gD,"unknown",()=>go,"uppercase",()=>d6,"url",()=>fr,"util",()=>eQ,"uuid",()=>fm,"uuidv4",()=>fn,"uuidv6",()=>fo,"uuidv7",()=>fp,"void",()=>gs,"xid",()=>fE],47361),a.i(93711),a.s(["$ZodAny",()=>bn,"$ZodArray",()=>bt,"$ZodAsyncError",()=>c.$ZodAsyncError,"$ZodBase64",()=>a8,"$ZodBase64URL",()=>ba,"$ZodBigInt",()=>bi,"$ZodBigIntFormat",()=>bj,"$ZodBoolean",()=>bh,"$ZodCIDRv4",()=>a5,"$ZodCIDRv6",()=>a6,"$ZodCUID",()=>aW,"$ZodCUID2",()=>aX,"$ZodCatch",()=>bY,"$ZodCheck",()=>an,"$ZodCheckBigIntFormat",()=>at,"$ZodCheckEndsWith",()=>aG,"$ZodCheckGreaterThan",()=>aq,"$ZodCheckIncludes",()=>aE,"$ZodCheckLengthEquals",()=>az,"$ZodCheckLessThan",()=>ap,"$ZodCheckLowerCase",()=>aC,"$ZodCheckMaxLength",()=>ax,"$ZodCheckMaxSize",()=>au,"$ZodCheckMimeType",()=>aJ,"$ZodCheckMinLength",()=>ay,"$ZodCheckMinSize",()=>av,"$ZodCheckMultipleOf",()=>ar,"$ZodCheckNumberFormat",()=>as,"$ZodCheckOverwrite",()=>aK,"$ZodCheckProperty",()=>aI,"$ZodCheckRegex",()=>aB,"$ZodCheckSizeEquals",()=>aw,"$ZodCheckStartsWith",()=>aF,"$ZodCheckStringFormat",()=>aA,"$ZodCheckUpperCase",()=>aD,"$ZodCodec",()=>b0,"$ZodCustom",()=>b9,"$ZodCustomStringFormat",()=>be,"$ZodDate",()=>br,"$ZodDefault",()=>bS,"$ZodDiscriminatedUnion",()=>bB,"$ZodE164",()=>bb,"$ZodEmail",()=>aS,"$ZodEmoji",()=>aU,"$ZodEncodeError",()=>c.$ZodEncodeError,"$ZodEnum",()=>bL,"$ZodError",()=>e.$ZodError,"$ZodFile",()=>bN,"$ZodFunction",()=>b6,"$ZodGUID",()=>aQ,"$ZodIPv4",()=>a3,"$ZodIPv6",()=>a4,"$ZodISODate",()=>a0,"$ZodISODateTime",()=>a_,"$ZodISODuration",()=>a2,"$ZodISOTime",()=>a1,"$ZodIntersection",()=>bC,"$ZodJWT",()=>bd,"$ZodKSUID",()=>a$,"$ZodLazy",()=>b8,"$ZodLiteral",()=>bM,"$ZodMap",()=>bH,"$ZodNaN",()=>bZ,"$ZodNanoID",()=>aV,"$ZodNever",()=>bp,"$ZodNonOptional",()=>bV,"$ZodNull",()=>bm,"$ZodNullable",()=>bR,"$ZodNumber",()=>bf,"$ZodNumberFormat",()=>bg,"$ZodObject",()=>bx,"$ZodObjectJIT",()=>by,"$ZodOptional",()=>bQ,"$ZodPipe",()=>b$,"$ZodPrefault",()=>bU,"$ZodPromise",()=>b7,"$ZodReadonly",()=>b3,"$ZodRealError",()=>e.$ZodRealError,"$ZodRecord",()=>bG,"$ZodRegistry",()=>cZ,"$ZodSet",()=>bJ,"$ZodString",()=>aO,"$ZodStringFormat",()=>aP,"$ZodSuccess",()=>bX,"$ZodSymbol",()=>bk,"$ZodTemplateLiteral",()=>b5,"$ZodTransform",()=>bO,"$ZodTuple",()=>bE,"$ZodType",()=>aN,"$ZodULID",()=>aY,"$ZodURL",()=>aT,"$ZodUUID",()=>aR,"$ZodUndefined",()=>bl,"$ZodUnion",()=>bA,"$ZodUnknown",()=>bo,"$ZodVoid",()=>bq,"$ZodXID",()=>aZ,"$brand",()=>c.$brand,"$constructor",()=>c.$constructor,"$input",()=>cY,"$output",()=>cX,"Doc",()=>aL,"JSONSchema",()=>eN,"JSONSchemaGenerator",()=>eL,"NEVER",()=>c.NEVER,"TimePrecision",()=>dp,"_any",()=>dK,"_array",()=>eh,"_base64",()=>dk,"_base64url",()=>dl,"_bigint",()=>dD,"_boolean",()=>dB,"_catch",()=>ez,"_check",()=>eI,"_cidrv4",()=>di,"_cidrv6",()=>dj,"_coercedBigint",()=>dE,"_coercedBoolean",()=>dC,"_coercedDate",()=>dP,"_coercedNumber",()=>dv,"_coercedString",()=>c1,"_cuid",()=>db,"_cuid2",()=>dc,"_custom",()=>eF,"_date",()=>dO,"_decode",()=>d._decode,"_decodeAsync",()=>d._decodeAsync,"_default",()=>ew,"_discriminatedUnion",()=>ej,"_e164",()=>dm,"_email",()=>c2,"_emoji",()=>c9,"_encode",()=>d._encode,"_encodeAsync",()=>d._encodeAsync,"_endsWith",()=>d9,"_enum",()=>ep,"_file",()=>es,"_float32",()=>dx,"_float64",()=>dy,"_gt",()=>dT,"_gte",()=>dU,"_guid",()=>c3,"_includes",()=>d7,"_int",()=>dw,"_int32",()=>dz,"_int64",()=>dF,"_intersection",()=>ek,"_ipv4",()=>dg,"_ipv6",()=>dh,"_isoDate",()=>dr,"_isoDateTime",()=>dq,"_isoDuration",()=>dt,"_isoTime",()=>ds,"_jwt",()=>dn,"_ksuid",()=>df,"_lazy",()=>eD,"_length",()=>d3,"_literal",()=>er,"_lowercase",()=>d5,"_lt",()=>dR,"_lte",()=>dS,"_map",()=>en,"_max",()=>dS,"_maxLength",()=>d1,"_maxSize",()=>d$,"_mime",()=>eb,"_min",()=>dU,"_minLength",()=>d2,"_minSize",()=>d_,"_multipleOf",()=>dZ,"_nan",()=>dQ,"_nanoid",()=>da,"_nativeEnum",()=>eq,"_negative",()=>dW,"_never",()=>dM,"_nonnegative",()=>dY,"_nonoptional",()=>ex,"_nonpositive",()=>dX,"_normalize",()=>ed,"_null",()=>dJ,"_nullable",()=>ev,"_number",()=>du,"_optional",()=>eu,"_overwrite",()=>ec,"_parse",()=>d._parse,"_parseAsync",()=>d._parseAsync,"_pipe",()=>eA,"_positive",()=>dV,"_promise",()=>eE,"_property",()=>ea,"_readonly",()=>eB,"_record",()=>em,"_refine",()=>eG,"_regex",()=>d4,"_safeDecode",()=>d._safeDecode,"_safeDecodeAsync",()=>d._safeDecodeAsync,"_safeEncode",()=>d._safeEncode,"_safeEncodeAsync",()=>d._safeEncodeAsync,"_safeParse",()=>d._safeParse,"_safeParseAsync",()=>d._safeParseAsync,"_set",()=>eo,"_size",()=>d0,"_startsWith",()=>d8,"_string",()=>c0,"_stringFormat",()=>eK,"_stringbool",()=>eJ,"_success",()=>ey,"_superRefine",()=>eH,"_symbol",()=>dH,"_templateLiteral",()=>eC,"_toLowerCase",()=>ef,"_toUpperCase",()=>eg,"_transform",()=>et,"_trim",()=>ee,"_tuple",()=>el,"_uint32",()=>dA,"_uint64",()=>dG,"_ulid",()=>dd,"_undefined",()=>dI,"_union",()=>ei,"_unknown",()=>dL,"_uppercase",()=>d6,"_url",()=>c8,"_uuid",()=>c4,"_uuidv4",()=>c5,"_uuidv6",()=>c6,"_uuidv7",()=>c7,"_void",()=>dN,"_xid",()=>de,"clone",()=>cb.clone,"config",()=>c.config,"decode",()=>d.decode,"decodeAsync",()=>d.decodeAsync,"encode",()=>d.encode,"encodeAsync",()=>d.encodeAsync,"flattenError",()=>e.flattenError,"formatError",()=>e.formatError,"globalConfig",()=>c.globalConfig,"globalRegistry",()=>c_,"isValidBase64",()=>a7,"isValidBase64URL",()=>a9,"isValidJWT",()=>bc,"locales",()=>cW,"parse",()=>d.parse,"parseAsync",()=>d.parseAsync,"prettifyError",()=>e.prettifyError,"regexes",()=>cd,"registry",()=>c$,"safeDecode",()=>d.safeDecode,"safeDecodeAsync",()=>d.safeDecodeAsync,"safeEncode",()=>d.safeEncode,"safeEncodeAsync",()=>d.safeEncodeAsync,"safeParse",()=>d.safeParse,"safeParseAsync",()=>d.safeParseAsync,"toDotPath",()=>e.toDotPath,"toJSONSchema",()=>eM,"treeifyError",()=>e.treeifyError,"util",()=>cc,"version",()=>aM],99490),a.i(69585);var b,c=a.i(91530),d=a.i(62360),e=a.i(76697);a.s(["$ZodAny",()=>bn,"$ZodArray",()=>bt,"$ZodBase64",()=>a8,"$ZodBase64URL",()=>ba,"$ZodBigInt",()=>bi,"$ZodBigIntFormat",()=>bj,"$ZodBoolean",()=>bh,"$ZodCIDRv4",()=>a5,"$ZodCIDRv6",()=>a6,"$ZodCUID",()=>aW,"$ZodCUID2",()=>aX,"$ZodCatch",()=>bY,"$ZodCodec",()=>b0,"$ZodCustom",()=>b9,"$ZodCustomStringFormat",()=>be,"$ZodDate",()=>br,"$ZodDefault",()=>bS,"$ZodDiscriminatedUnion",()=>bB,"$ZodE164",()=>bb,"$ZodEmail",()=>aS,"$ZodEmoji",()=>aU,"$ZodEnum",()=>bL,"$ZodFile",()=>bN,"$ZodFunction",()=>b6,"$ZodGUID",()=>aQ,"$ZodIPv4",()=>a3,"$ZodIPv6",()=>a4,"$ZodISODate",()=>a0,"$ZodISODateTime",()=>a_,"$ZodISODuration",()=>a2,"$ZodISOTime",()=>a1,"$ZodIntersection",()=>bC,"$ZodJWT",()=>bd,"$ZodKSUID",()=>a$,"$ZodLazy",()=>b8,"$ZodLiteral",()=>bM,"$ZodMap",()=>bH,"$ZodNaN",()=>bZ,"$ZodNanoID",()=>aV,"$ZodNever",()=>bp,"$ZodNonOptional",()=>bV,"$ZodNull",()=>bm,"$ZodNullable",()=>bR,"$ZodNumber",()=>bf,"$ZodNumberFormat",()=>bg,"$ZodObject",()=>bx,"$ZodObjectJIT",()=>by,"$ZodOptional",()=>bQ,"$ZodPipe",()=>b$,"$ZodPrefault",()=>bU,"$ZodPromise",()=>b7,"$ZodReadonly",()=>b3,"$ZodRecord",()=>bG,"$ZodSet",()=>bJ,"$ZodString",()=>aO,"$ZodStringFormat",()=>aP,"$ZodSuccess",()=>bX,"$ZodSymbol",()=>bk,"$ZodTemplateLiteral",()=>b5,"$ZodTransform",()=>bO,"$ZodTuple",()=>bE,"$ZodType",()=>aN,"$ZodULID",()=>aY,"$ZodURL",()=>aT,"$ZodUUID",()=>aR,"$ZodUndefined",()=>bl,"$ZodUnion",()=>bA,"$ZodUnknown",()=>bo,"$ZodVoid",()=>bq,"$ZodXID",()=>aZ,"clone",()=>cb.clone,"isValidBase64",()=>a7,"isValidBase64URL",()=>a9,"isValidJWT",()=>bc],27035),a.s(["$ZodAny",()=>bn,"$ZodArray",()=>bt,"$ZodBase64",()=>a8,"$ZodBase64URL",()=>ba,"$ZodBigInt",()=>bi,"$ZodBigIntFormat",()=>bj,"$ZodBoolean",()=>bh,"$ZodCIDRv4",()=>a5,"$ZodCIDRv6",()=>a6,"$ZodCUID",()=>aW,"$ZodCUID2",()=>aX,"$ZodCatch",()=>bY,"$ZodCodec",()=>b0,"$ZodCustom",()=>b9,"$ZodCustomStringFormat",()=>be,"$ZodDate",()=>br,"$ZodDefault",()=>bS,"$ZodDiscriminatedUnion",()=>bB,"$ZodE164",()=>bb,"$ZodEmail",()=>aS,"$ZodEmoji",()=>aU,"$ZodEnum",()=>bL,"$ZodFile",()=>bN,"$ZodFunction",()=>b6,"$ZodGUID",()=>aQ,"$ZodIPv4",()=>a3,"$ZodIPv6",()=>a4,"$ZodISODate",()=>a0,"$ZodISODateTime",()=>a_,"$ZodISODuration",()=>a2,"$ZodISOTime",()=>a1,"$ZodIntersection",()=>bC,"$ZodJWT",()=>bd,"$ZodKSUID",()=>a$,"$ZodLazy",()=>b8,"$ZodLiteral",()=>bM,"$ZodMap",()=>bH,"$ZodNaN",()=>bZ,"$ZodNanoID",()=>aV,"$ZodNever",()=>bp,"$ZodNonOptional",()=>bV,"$ZodNull",()=>bm,"$ZodNullable",()=>bR,"$ZodNumber",()=>bf,"$ZodNumberFormat",()=>bg,"$ZodObject",()=>bx,"$ZodObjectJIT",()=>by,"$ZodOptional",()=>bQ,"$ZodPipe",()=>b$,"$ZodPrefault",()=>bU,"$ZodPromise",()=>b7,"$ZodReadonly",()=>b3,"$ZodRecord",()=>bG,"$ZodSet",()=>bJ,"$ZodString",()=>aO,"$ZodStringFormat",()=>aP,"$ZodSuccess",()=>bX,"$ZodSymbol",()=>bk,"$ZodTemplateLiteral",()=>b5,"$ZodTransform",()=>bO,"$ZodTuple",()=>bE,"$ZodType",()=>aN,"$ZodULID",()=>aY,"$ZodURL",()=>aT,"$ZodUUID",()=>aR,"$ZodUndefined",()=>bl,"$ZodUnion",()=>bA,"$ZodUnknown",()=>bo,"$ZodVoid",()=>bq,"$ZodXID",()=>aZ,"isValidBase64",()=>a7,"isValidBase64URL",()=>a9,"isValidJWT",()=>bc],39262),a.s(["$ZodCheck",()=>an,"$ZodCheckBigIntFormat",()=>at,"$ZodCheckEndsWith",()=>aG,"$ZodCheckGreaterThan",()=>aq,"$ZodCheckIncludes",()=>aE,"$ZodCheckLengthEquals",()=>az,"$ZodCheckLessThan",()=>ap,"$ZodCheckLowerCase",()=>aC,"$ZodCheckMaxLength",()=>ax,"$ZodCheckMaxSize",()=>au,"$ZodCheckMimeType",()=>aJ,"$ZodCheckMinLength",()=>ay,"$ZodCheckMinSize",()=>av,"$ZodCheckMultipleOf",()=>ar,"$ZodCheckNumberFormat",()=>as,"$ZodCheckOverwrite",()=>aK,"$ZodCheckProperty",()=>aI,"$ZodCheckRegex",()=>aB,"$ZodCheckSizeEquals",()=>aw,"$ZodCheckStartsWith",()=>aF,"$ZodCheckStringFormat",()=>aA,"$ZodCheckUpperCase",()=>aD],83330),a.s(["base64",()=>D,"base64url",()=>E,"bigint",()=>O,"boolean",()=>R,"browserEmail",()=>x,"cidrv4",()=>B,"cidrv6",()=>C,"cuid",()=>f,"cuid2",()=>g,"date",()=>J,"datetime",()=>M,"domain",()=>G,"duration",()=>l,"e164",()=>H,"email",()=>s,"emoji",()=>y,"extendedDuration",()=>m,"guid",()=>n,"hex",()=>W,"hostname",()=>F,"html5Email",()=>t,"idnEmail",()=>w,"integer",()=>P,"ipv4",()=>z,"ipv6",()=>A,"ksuid",()=>j,"lowercase",()=>U,"md5_base64",()=>$,"md5_base64url",()=>_,"md5_hex",()=>Z,"nanoid",()=>k,"null",()=>S,"number",()=>Q,"rfc5322Email",()=>u,"sha1_base64",()=>ab,"sha1_base64url",()=>ac,"sha1_hex",()=>aa,"sha256_base64",()=>ae,"sha256_base64url",()=>af,"sha256_hex",()=>ad,"sha384_base64",()=>ah,"sha384_base64url",()=>ai,"sha384_hex",()=>ag,"sha512_base64",()=>ak,"sha512_base64url",()=>al,"sha512_hex",()=>aj,"string",()=>N,"time",()=>L,"ulid",()=>h,"undefined",()=>T,"unicodeEmail",()=>v,"uppercase",()=>V,"uuid",()=>o,"uuid4",()=>p,"uuid6",()=>q,"uuid7",()=>r,"xid",()=>i],14195);let f=/^[cC][^\s-]{8,}$/,g=/^[0-9a-z]+$/,h=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,i=/^[0-9a-vA-V]{20}$/,j=/^[A-Za-z0-9]{27}$/,k=/^[a-zA-Z0-9_-]{21}$/,l=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,m=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,n=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,o=a=>a?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${a}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/,p=o(4),q=o(6),r=o(7),s=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,t=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,u=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,v=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,w=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,x=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;function y(){return RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")}let z=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,A=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,B=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,C=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,D=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,E=/^[A-Za-z0-9_-]*$/,F=/^(?=.{1,253}\.?$)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[-0-9a-zA-Z]{0,61}[0-9a-zA-Z])?)*\.?$/,G=/^([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/,H=/^\+(?:[0-9]){6,14}[0-9]$/,I="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",J=RegExp(`^${I}$`);function K(a){let b="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof a.precision?-1===a.precision?`${b}`:0===a.precision?`${b}:[0-5]\\d`:`${b}:[0-5]\\d\\.\\d{${a.precision}}`:`${b}(?::[0-5]\\d(?:\\.\\d+)?)?`}function L(a){return RegExp(`^${K(a)}$`)}function M(a){let b=K({precision:a.precision}),c=["Z"];a.local&&c.push(""),a.offset&&c.push("([+-](?:[01]\\d|2[0-3]):[0-5]\\d)");let d=`${b}(?:${c.join("|")})`;return RegExp(`^${I}T(?:${d})$`)}let N=a=>{let b=a?`[\\s\\S]{${a?.minimum??0},${a?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${b}$`)},O=/^\d+n?$/,P=/^\d+$/,Q=/^-?\d+(?:\.\d+)?/i,R=/true|false/i,S=/null/i,T=/undefined/i,U=/^[^A-Z]*$/,V=/^[^a-z]*$/,W=/^[0-9a-fA-F]*$/;function X(a,b){return RegExp(`^[A-Za-z0-9+/]{${a}}${b}$`)}function Y(a){return RegExp(`^[A-Za-z0-9-_]{${a}}$`)}let Z=/^[0-9a-fA-F]{32}$/,$=X(22,"=="),_=Y(22),aa=/^[0-9a-fA-F]{40}$/,ab=X(27,"="),ac=Y(27),ad=/^[0-9a-fA-F]{64}$/,ae=X(43,"="),af=Y(43),ag=/^[0-9a-fA-F]{96}$/,ah=X(64,""),ai=Y(64),aj=/^[0-9a-fA-F]{128}$/,ak=X(86,"=="),al=Y(86);var am=a.i(88344);let an=c.$constructor("$ZodCheck",(a,b)=>{var c;a._zod??(a._zod={}),a._zod.def=b,(c=a._zod).onattach??(c.onattach=[])}),ao={number:"number",bigint:"bigint",object:"date"},ap=c.$constructor("$ZodCheckLessThan",(a,b)=>{an.init(a,b);let c=ao[typeof b.value];a._zod.onattach.push(a=>{let c=a._zod.bag,d=(b.inclusive?c.maximum:c.exclusiveMaximum)??1/0;b.value<d&&(b.inclusive?c.maximum=b.value:c.exclusiveMaximum=b.value)}),a._zod.check=d=>{(b.inclusive?d.value<=b.value:d.value<b.value)||d.issues.push({origin:c,code:"too_big",maximum:b.value,input:d.value,inclusive:b.inclusive,inst:a,continue:!b.abort})}}),aq=c.$constructor("$ZodCheckGreaterThan",(a,b)=>{an.init(a,b);let c=ao[typeof b.value];a._zod.onattach.push(a=>{let c=a._zod.bag,d=(b.inclusive?c.minimum:c.exclusiveMinimum)??-1/0;b.value>d&&(b.inclusive?c.minimum=b.value:c.exclusiveMinimum=b.value)}),a._zod.check=d=>{(b.inclusive?d.value>=b.value:d.value>b.value)||d.issues.push({origin:c,code:"too_small",minimum:b.value,input:d.value,inclusive:b.inclusive,inst:a,continue:!b.abort})}}),ar=c.$constructor("$ZodCheckMultipleOf",(a,b)=>{an.init(a,b),a._zod.onattach.push(a=>{var c;(c=a._zod.bag).multipleOf??(c.multipleOf=b.value)}),a._zod.check=c=>{if(typeof c.value!=typeof b.value)throw Error("Cannot mix number and bigint in multiple_of check.");("bigint"==typeof c.value?c.value%b.value===BigInt(0):0===am.floatSafeRemainder(c.value,b.value))||c.issues.push({origin:typeof c.value,code:"not_multiple_of",divisor:b.value,input:c.value,inst:a,continue:!b.abort})}}),as=c.$constructor("$ZodCheckNumberFormat",(a,b)=>{an.init(a,b),b.format=b.format||"float64";let c=b.format?.includes("int"),d=c?"int":"number",[e,f]=am.NUMBER_FORMAT_RANGES[b.format];a._zod.onattach.push(a=>{let d=a._zod.bag;d.format=b.format,d.minimum=e,d.maximum=f,c&&(d.pattern=P)}),a._zod.check=g=>{let h=g.value;if(c){if(!Number.isInteger(h))return void g.issues.push({expected:d,format:b.format,code:"invalid_type",continue:!1,input:h,inst:a});if(!Number.isSafeInteger(h))return void(h>0?g.issues.push({input:h,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:a,origin:d,continue:!b.abort}):g.issues.push({input:h,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:a,origin:d,continue:!b.abort}))}h<e&&g.issues.push({origin:"number",input:h,code:"too_small",minimum:e,inclusive:!0,inst:a,continue:!b.abort}),h>f&&g.issues.push({origin:"number",input:h,code:"too_big",maximum:f,inst:a})}}),at=c.$constructor("$ZodCheckBigIntFormat",(a,b)=>{an.init(a,b);let[c,d]=am.BIGINT_FORMAT_RANGES[b.format];a._zod.onattach.push(a=>{let e=a._zod.bag;e.format=b.format,e.minimum=c,e.maximum=d}),a._zod.check=e=>{let f=e.value;f<c&&e.issues.push({origin:"bigint",input:f,code:"too_small",minimum:c,inclusive:!0,inst:a,continue:!b.abort}),f>d&&e.issues.push({origin:"bigint",input:f,code:"too_big",maximum:d,inst:a})}}),au=c.$constructor("$ZodCheckMaxSize",(a,b)=>{var c;an.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!am.nullish(b)&&void 0!==b.size}),a._zod.onattach.push(a=>{let c=a._zod.bag.maximum??1/0;b.maximum<c&&(a._zod.bag.maximum=b.maximum)}),a._zod.check=c=>{let d=c.value;d.size<=b.maximum||c.issues.push({origin:am.getSizableOrigin(d),code:"too_big",maximum:b.maximum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),av=c.$constructor("$ZodCheckMinSize",(a,b)=>{var c;an.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!am.nullish(b)&&void 0!==b.size}),a._zod.onattach.push(a=>{let c=a._zod.bag.minimum??-1/0;b.minimum>c&&(a._zod.bag.minimum=b.minimum)}),a._zod.check=c=>{let d=c.value;d.size>=b.minimum||c.issues.push({origin:am.getSizableOrigin(d),code:"too_small",minimum:b.minimum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),aw=c.$constructor("$ZodCheckSizeEquals",(a,b)=>{var c;an.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!am.nullish(b)&&void 0!==b.size}),a._zod.onattach.push(a=>{let c=a._zod.bag;c.minimum=b.size,c.maximum=b.size,c.size=b.size}),a._zod.check=c=>{let d=c.value,e=d.size;if(e===b.size)return;let f=e>b.size;c.issues.push({origin:am.getSizableOrigin(d),...f?{code:"too_big",maximum:b.size}:{code:"too_small",minimum:b.size},inclusive:!0,exact:!0,input:c.value,inst:a,continue:!b.abort})}}),ax=c.$constructor("$ZodCheckMaxLength",(a,b)=>{var c;an.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!am.nullish(b)&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.maximum??1/0;b.maximum<c&&(a._zod.bag.maximum=b.maximum)}),a._zod.check=c=>{let d=c.value;if(d.length<=b.maximum)return;let e=am.getLengthableOrigin(d);c.issues.push({origin:e,code:"too_big",maximum:b.maximum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),ay=c.$constructor("$ZodCheckMinLength",(a,b)=>{var c;an.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!am.nullish(b)&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.minimum??-1/0;b.minimum>c&&(a._zod.bag.minimum=b.minimum)}),a._zod.check=c=>{let d=c.value;if(d.length>=b.minimum)return;let e=am.getLengthableOrigin(d);c.issues.push({origin:e,code:"too_small",minimum:b.minimum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),az=c.$constructor("$ZodCheckLengthEquals",(a,b)=>{var c;an.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!am.nullish(b)&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag;c.minimum=b.length,c.maximum=b.length,c.length=b.length}),a._zod.check=c=>{let d=c.value,e=d.length;if(e===b.length)return;let f=am.getLengthableOrigin(d),g=e>b.length;c.issues.push({origin:f,...g?{code:"too_big",maximum:b.length}:{code:"too_small",minimum:b.length},inclusive:!0,exact:!0,input:c.value,inst:a,continue:!b.abort})}}),aA=c.$constructor("$ZodCheckStringFormat",(a,b)=>{var c,d;an.init(a,b),a._zod.onattach.push(a=>{let c=a._zod.bag;c.format=b.format,b.pattern&&(c.patterns??(c.patterns=new Set),c.patterns.add(b.pattern))}),b.pattern?(c=a._zod).check??(c.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:b.format,input:c.value,...b.pattern?{pattern:b.pattern.toString()}:{},inst:a,continue:!b.abort})}):(d=a._zod).check??(d.check=()=>{})}),aB=c.$constructor("$ZodCheckRegex",(a,b)=>{aA.init(a,b),a._zod.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:"regex",input:c.value,pattern:b.pattern.toString(),inst:a,continue:!b.abort})}}),aC=c.$constructor("$ZodCheckLowerCase",(a,b)=>{b.pattern??(b.pattern=U),aA.init(a,b)}),aD=c.$constructor("$ZodCheckUpperCase",(a,b)=>{b.pattern??(b.pattern=V),aA.init(a,b)}),aE=c.$constructor("$ZodCheckIncludes",(a,b)=>{an.init(a,b);let c=am.escapeRegex(b.includes),d=new RegExp("number"==typeof b.position?`^.{${b.position}}${c}`:c);b.pattern=d,a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(d)}),a._zod.check=c=>{c.value.includes(b.includes,b.position)||c.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:b.includes,input:c.value,inst:a,continue:!b.abort})}}),aF=c.$constructor("$ZodCheckStartsWith",(a,b)=>{an.init(a,b);let c=RegExp(`^${am.escapeRegex(b.prefix)}.*`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.startsWith(b.prefix)||c.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:b.prefix,input:c.value,inst:a,continue:!b.abort})}}),aG=c.$constructor("$ZodCheckEndsWith",(a,b)=>{an.init(a,b);let c=RegExp(`.*${am.escapeRegex(b.suffix)}$`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.endsWith(b.suffix)||c.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:b.suffix,input:c.value,inst:a,continue:!b.abort})}});function aH(a,b,c){a.issues.length&&b.issues.push(...am.prefixIssues(c,a.issues))}let aI=c.$constructor("$ZodCheckProperty",(a,b)=>{an.init(a,b),a._zod.check=a=>{let c=b.schema._zod.run({value:a.value[b.property],issues:[]},{});if(c instanceof Promise)return c.then(c=>aH(c,a,b.property));aH(c,a,b.property)}}),aJ=c.$constructor("$ZodCheckMimeType",(a,b)=>{an.init(a,b);let c=new Set(b.mime);a._zod.onattach.push(a=>{a._zod.bag.mime=b.mime}),a._zod.check=d=>{c.has(d.value.type)||d.issues.push({code:"invalid_value",values:b.mime,input:d.value.type,inst:a,continue:!b.abort})}}),aK=c.$constructor("$ZodCheckOverwrite",(a,b)=>{an.init(a,b),a._zod.check=a=>{a.value=b.tx(a.value)}});a.s(["Doc",()=>aL],37144);class aL{constructor(a=[]){this.content=[],this.indent=0,this&&(this.args=a)}indented(a){this.indent+=1,a(this),this.indent-=1}write(a){if("function"==typeof a){a(this,{execution:"sync"}),a(this,{execution:"async"});return}let b=a.split("\n").filter(a=>a),c=Math.min(...b.map(a=>a.length-a.trimStart().length));for(let a of b.map(a=>a.slice(c)).map(a=>" ".repeat(2*this.indent)+a))this.content.push(a)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(a=>`  ${a}`)].join("\n"))}}a.s(["version",()=>aM],7052);let aM={major:4,minor:1,patch:5},aN=c.$constructor("$ZodType",(a,b)=>{var e;a??(a={}),a._zod.def=b,a._zod.bag=a._zod.bag||{},a._zod.version=aM;let f=[...a._zod.def.checks??[]];for(let b of(a._zod.traits.has("$ZodCheck")&&f.unshift(a),f))for(let c of b._zod.onattach)c(a);if(0===f.length)(e=a._zod).deferred??(e.deferred=[]),a._zod.deferred?.push(()=>{a._zod.run=a._zod.parse});else{let b=(a,b,d)=>{let e,f=am.aborted(a);for(let g of b){if(g._zod.def.when){if(!g._zod.def.when(a))continue}else if(f)continue;let b=a.issues.length,h=g._zod.check(a);if(h instanceof Promise&&d?.async===!1)throw new c.$ZodAsyncError;if(e||h instanceof Promise)e=(e??Promise.resolve()).then(async()=>{await h,a.issues.length!==b&&(f||(f=am.aborted(a,b)))});else{if(a.issues.length===b)continue;f||(f=am.aborted(a,b))}}return e?e.then(()=>a):a},d=(d,e,g)=>{if(am.aborted(d))return d.aborted=!0,d;let h=b(e,f,g);if(h instanceof Promise){if(!1===g.async)throw new c.$ZodAsyncError;return h.then(b=>a._zod.parse(b,g))}return a._zod.parse(h,g)};a._zod.run=(e,g)=>{if(g.skipChecks)return a._zod.parse(e,g);if("backward"===g.direction){let b=a._zod.parse({value:e.value,issues:[]},{...g,skipChecks:!0});return b instanceof Promise?b.then(a=>d(a,e,g)):d(b,e,g)}let h=a._zod.parse(e,g);if(h instanceof Promise){if(!1===g.async)throw new c.$ZodAsyncError;return h.then(a=>b(a,f,g))}return b(h,f,g)}}a["~standard"]={validate:b=>{try{let c=(0,d.safeParse)(a,b);return c.success?{value:c.data}:{issues:c.error?.issues}}catch(c){return(0,d.safeParseAsync)(a,b).then(a=>a.success?{value:a.data}:{issues:a.error?.issues})}},vendor:"zod",version:1}}),aO=c.$constructor("$ZodString",(a,b)=>{aN.init(a,b),a._zod.pattern=[...a?._zod.bag?.patterns??[]].pop()??N(a._zod.bag),a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=String(c.value)}catch(a){}return"string"==typeof c.value||c.issues.push({expected:"string",code:"invalid_type",input:c.value,inst:a}),c}}),aP=c.$constructor("$ZodStringFormat",(a,b)=>{aA.init(a,b),aO.init(a,b)}),aQ=c.$constructor("$ZodGUID",(a,b)=>{b.pattern??(b.pattern=n),aP.init(a,b)}),aR=c.$constructor("$ZodUUID",(a,b)=>{if(b.version){let a={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[b.version];if(void 0===a)throw Error(`Invalid UUID version: "${b.version}"`);b.pattern??(b.pattern=o(a))}else b.pattern??(b.pattern=o());aP.init(a,b)}),aS=c.$constructor("$ZodEmail",(a,b)=>{b.pattern??(b.pattern=s),aP.init(a,b)}),aT=c.$constructor("$ZodURL",(a,b)=>{aP.init(a,b),a._zod.check=c=>{try{let d=c.value.trim(),e=new URL(d);b.hostname&&(b.hostname.lastIndex=0,b.hostname.test(e.hostname)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:F.source,input:c.value,inst:a,continue:!b.abort})),b.protocol&&(b.protocol.lastIndex=0,b.protocol.test(e.protocol.endsWith(":")?e.protocol.slice(0,-1):e.protocol)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:b.protocol.source,input:c.value,inst:a,continue:!b.abort})),b.normalize?c.value=e.href:c.value=d;return}catch(d){c.issues.push({code:"invalid_format",format:"url",input:c.value,inst:a,continue:!b.abort})}}}),aU=c.$constructor("$ZodEmoji",(a,b)=>{b.pattern??(b.pattern=y()),aP.init(a,b)}),aV=c.$constructor("$ZodNanoID",(a,b)=>{b.pattern??(b.pattern=k),aP.init(a,b)}),aW=c.$constructor("$ZodCUID",(a,b)=>{b.pattern??(b.pattern=f),aP.init(a,b)}),aX=c.$constructor("$ZodCUID2",(a,b)=>{b.pattern??(b.pattern=g),aP.init(a,b)}),aY=c.$constructor("$ZodULID",(a,b)=>{b.pattern??(b.pattern=h),aP.init(a,b)}),aZ=c.$constructor("$ZodXID",(a,b)=>{b.pattern??(b.pattern=i),aP.init(a,b)}),a$=c.$constructor("$ZodKSUID",(a,b)=>{b.pattern??(b.pattern=j),aP.init(a,b)}),a_=c.$constructor("$ZodISODateTime",(a,b)=>{b.pattern??(b.pattern=M(b)),aP.init(a,b)}),a0=c.$constructor("$ZodISODate",(a,b)=>{b.pattern??(b.pattern=J),aP.init(a,b)}),a1=c.$constructor("$ZodISOTime",(a,b)=>{b.pattern??(b.pattern=L(b)),aP.init(a,b)}),a2=c.$constructor("$ZodISODuration",(a,b)=>{b.pattern??(b.pattern=l),aP.init(a,b)}),a3=c.$constructor("$ZodIPv4",(a,b)=>{b.pattern??(b.pattern=z),aP.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv4"})}),a4=c.$constructor("$ZodIPv6",(a,b)=>{b.pattern??(b.pattern=A),aP.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv6"}),a._zod.check=c=>{try{new URL(`http://[${c.value}]`)}catch{c.issues.push({code:"invalid_format",format:"ipv6",input:c.value,inst:a,continue:!b.abort})}}}),a5=c.$constructor("$ZodCIDRv4",(a,b)=>{b.pattern??(b.pattern=B),aP.init(a,b)}),a6=c.$constructor("$ZodCIDRv6",(a,b)=>{b.pattern??(b.pattern=C),aP.init(a,b),a._zod.check=c=>{let[d,e]=c.value.split("/");try{if(!e)throw Error();let a=Number(e);if(`${a}`!==e||a<0||a>128)throw Error();new URL(`http://[${d}]`)}catch{c.issues.push({code:"invalid_format",format:"cidrv6",input:c.value,inst:a,continue:!b.abort})}}});function a7(a){if(""===a)return!0;if(a.length%4!=0)return!1;try{return atob(a),!0}catch{return!1}}let a8=c.$constructor("$ZodBase64",(a,b)=>{b.pattern??(b.pattern=D),aP.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64"}),a._zod.check=c=>{a7(c.value)||c.issues.push({code:"invalid_format",format:"base64",input:c.value,inst:a,continue:!b.abort})}});function a9(a){if(!E.test(a))return!1;let b=a.replace(/[-_]/g,a=>"-"===a?"+":"/");return a7(b.padEnd(4*Math.ceil(b.length/4),"="))}let ba=c.$constructor("$ZodBase64URL",(a,b)=>{b.pattern??(b.pattern=E),aP.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64url"}),a._zod.check=c=>{a9(c.value)||c.issues.push({code:"invalid_format",format:"base64url",input:c.value,inst:a,continue:!b.abort})}}),bb=c.$constructor("$ZodE164",(a,b)=>{b.pattern??(b.pattern=H),aP.init(a,b)});function bc(a,b=null){try{let c=a.split(".");if(3!==c.length)return!1;let[d]=c;if(!d)return!1;let e=JSON.parse(atob(d));if("typ"in e&&e?.typ!=="JWT"||!e.alg||b&&(!("alg"in e)||e.alg!==b))return!1;return!0}catch{return!1}}let bd=c.$constructor("$ZodJWT",(a,b)=>{aP.init(a,b),a._zod.check=c=>{bc(c.value,b.alg)||c.issues.push({code:"invalid_format",format:"jwt",input:c.value,inst:a,continue:!b.abort})}}),be=c.$constructor("$ZodCustomStringFormat",(a,b)=>{aP.init(a,b),a._zod.check=c=>{b.fn(c.value)||c.issues.push({code:"invalid_format",format:b.format,input:c.value,inst:a,continue:!b.abort})}}),bf=c.$constructor("$ZodNumber",(a,b)=>{aN.init(a,b),a._zod.pattern=a._zod.bag.pattern??Q,a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=Number(c.value)}catch(a){}let e=c.value;if("number"==typeof e&&!Number.isNaN(e)&&Number.isFinite(e))return c;let f="number"==typeof e?Number.isNaN(e)?"NaN":Number.isFinite(e)?void 0:"Infinity":void 0;return c.issues.push({expected:"number",code:"invalid_type",input:e,inst:a,...f?{received:f}:{}}),c}}),bg=c.$constructor("$ZodNumber",(a,b)=>{as.init(a,b),bf.init(a,b)}),bh=c.$constructor("$ZodBoolean",(a,b)=>{aN.init(a,b),a._zod.pattern=R,a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=!!c.value}catch(a){}let e=c.value;return"boolean"==typeof e||c.issues.push({expected:"boolean",code:"invalid_type",input:e,inst:a}),c}}),bi=c.$constructor("$ZodBigInt",(a,b)=>{aN.init(a,b),a._zod.pattern=O,a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=BigInt(c.value)}catch(a){}return"bigint"==typeof c.value||c.issues.push({expected:"bigint",code:"invalid_type",input:c.value,inst:a}),c}}),bj=c.$constructor("$ZodBigInt",(a,b)=>{at.init(a,b),bi.init(a,b)}),bk=c.$constructor("$ZodSymbol",(a,b)=>{aN.init(a,b),a._zod.parse=(b,c)=>{let d=b.value;return"symbol"==typeof d||b.issues.push({expected:"symbol",code:"invalid_type",input:d,inst:a}),b}}),bl=c.$constructor("$ZodUndefined",(a,b)=>{aN.init(a,b),a._zod.pattern=T,a._zod.values=new Set([void 0]),a._zod.optin="optional",a._zod.optout="optional",a._zod.parse=(b,c)=>{let d=b.value;return void 0===d||b.issues.push({expected:"undefined",code:"invalid_type",input:d,inst:a}),b}}),bm=c.$constructor("$ZodNull",(a,b)=>{aN.init(a,b),a._zod.pattern=S,a._zod.values=new Set([null]),a._zod.parse=(b,c)=>{let d=b.value;return null===d||b.issues.push({expected:"null",code:"invalid_type",input:d,inst:a}),b}}),bn=c.$constructor("$ZodAny",(a,b)=>{aN.init(a,b),a._zod.parse=a=>a}),bo=c.$constructor("$ZodUnknown",(a,b)=>{aN.init(a,b),a._zod.parse=a=>a}),bp=c.$constructor("$ZodNever",(a,b)=>{aN.init(a,b),a._zod.parse=(b,c)=>(b.issues.push({expected:"never",code:"invalid_type",input:b.value,inst:a}),b)}),bq=c.$constructor("$ZodVoid",(a,b)=>{aN.init(a,b),a._zod.parse=(b,c)=>{let d=b.value;return void 0===d||b.issues.push({expected:"void",code:"invalid_type",input:d,inst:a}),b}}),br=c.$constructor("$ZodDate",(a,b)=>{aN.init(a,b),a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=new Date(c.value)}catch(a){}let e=c.value,f=e instanceof Date;return f&&!Number.isNaN(e.getTime())||c.issues.push({expected:"date",code:"invalid_type",input:e,...f?{received:"Invalid Date"}:{},inst:a}),c}});function bs(a,b,c){a.issues.length&&b.issues.push(...am.prefixIssues(c,a.issues)),b.value[c]=a.value}let bt=c.$constructor("$ZodArray",(a,b)=>{aN.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!Array.isArray(e))return c.issues.push({expected:"array",code:"invalid_type",input:e,inst:a}),c;c.value=Array(e.length);let f=[];for(let a=0;a<e.length;a++){let g=e[a],h=b.element._zod.run({value:g,issues:[]},d);h instanceof Promise?f.push(h.then(b=>bs(b,c,a))):bs(h,c,a)}return f.length?Promise.all(f).then(()=>c):c}});function bu(a,b,c,d){a.issues.length&&b.issues.push(...am.prefixIssues(c,a.issues)),void 0===a.value?c in d&&(b.value[c]=void 0):b.value[c]=a.value}function bv(a){let b=Object.keys(a.shape);for(let c of b)if(!a.shape[c]._zod.traits.has("$ZodType"))throw Error(`Invalid element at key "${c}": expected a Zod schema`);let c=am.optionalKeys(a.shape);return{...a,keys:b,keySet:new Set(b),numKeys:b.length,optionalKeys:new Set(c)}}function bw(a,b,c,d,e,f){let g=[],h=e.keySet,i=e.catchall._zod,j=i.def.type;for(let e of Object.keys(b)){if(h.has(e))continue;if("never"===j){g.push(e);continue}let f=i.run({value:b[e],issues:[]},d);f instanceof Promise?a.push(f.then(a=>bu(a,c,e,b))):bu(f,c,e,b)}return(g.length&&c.issues.push({code:"unrecognized_keys",keys:g,input:b,inst:f}),a.length)?Promise.all(a).then(()=>c):c}let bx=c.$constructor("$ZodObject",(a,b)=>{let c;aN.init(a,b);let d=am.cached(()=>bv(b));am.defineLazy(a._zod,"propValues",()=>{let a=b.shape,c={};for(let b in a){let d=a[b]._zod;if(d.values)for(let a of(c[b]??(c[b]=new Set),d.values))c[b].add(a)}return c});let e=am.isObject,f=b.catchall;a._zod.parse=(b,g)=>{c??(c=d.value);let h=b.value;if(!e(h))return b.issues.push({expected:"object",code:"invalid_type",input:h,inst:a}),b;b.value={};let i=[],j=c.shape;for(let a of c.keys){let c=j[a]._zod.run({value:h[a],issues:[]},g);c instanceof Promise?i.push(c.then(c=>bu(c,b,a,h))):bu(c,b,a,h)}return f?bw(i,h,b,g,d.value,a):i.length?Promise.all(i).then(()=>b):b}}),by=c.$constructor("$ZodObjectJIT",(a,b)=>{let d,e;bx.init(a,b);let f=a._zod.parse,g=am.cached(()=>bv(b)),h=am.isObject,i=!c.globalConfig.jitless,j=am.allowsEval,k=i&&j.value,l=b.catchall;a._zod.parse=(c,j)=>{e??(e=g.value);let m=c.value;return h(m)?i&&k&&j?.async===!1&&!0!==j.jitless?(d||(d=(a=>{let b=new aL(["shape","payload","ctx"]),c=g.value,d=a=>{let b=am.esc(a);return`shape[${b}]._zod.run({ value: input[${b}], issues: [] }, ctx)`};b.write("const input = payload.value;");let e=Object.create(null),f=0;for(let a of c.keys)e[a]=`key_${f++}`;for(let a of(b.write("const newResult = {}"),c.keys)){let c=e[a],f=am.esc(a);b.write(`const ${c} = ${d(a)};`),b.write(`
        if (${c}.issues.length) {
          payload.issues = payload.issues.concat(${c}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${f}, ...iss.path] : [${f}]
          })));
        }
        
        if (${c}.value === undefined) {
          if (${f} in input) {
            newResult[${f}] = undefined;
          }
        } else {
          newResult[${f}] = ${c}.value;
        }
      `)}b.write("payload.value = newResult;"),b.write("return payload;");let h=b.compile();return(b,c)=>h(a,b,c)})(b.shape)),c=d(c,j),l)?bw([],m,c,j,e,a):c:f(c,j):(c.issues.push({expected:"object",code:"invalid_type",input:m,inst:a}),c)}});function bz(a,b,d,e){for(let c of a)if(0===c.issues.length)return b.value=c.value,b;let f=a.filter(a=>!am.aborted(a));return 1===f.length?(b.value=f[0].value,f[0]):(b.issues.push({code:"invalid_union",input:b.value,inst:d,errors:a.map(a=>a.issues.map(a=>am.finalizeIssue(a,e,c.config())))}),b)}let bA=c.$constructor("$ZodUnion",(a,b)=>{aN.init(a,b),am.defineLazy(a._zod,"optin",()=>b.options.some(a=>"optional"===a._zod.optin)?"optional":void 0),am.defineLazy(a._zod,"optout",()=>b.options.some(a=>"optional"===a._zod.optout)?"optional":void 0),am.defineLazy(a._zod,"values",()=>{if(b.options.every(a=>a._zod.values))return new Set(b.options.flatMap(a=>Array.from(a._zod.values)))}),am.defineLazy(a._zod,"pattern",()=>{if(b.options.every(a=>a._zod.pattern)){let a=b.options.map(a=>a._zod.pattern);return RegExp(`^(${a.map(a=>am.cleanRegex(a.source)).join("|")})$`)}});let c=1===b.options.length,d=b.options[0]._zod.run;a._zod.parse=(e,f)=>{if(c)return d(e,f);let g=!1,h=[];for(let a of b.options){let b=a._zod.run({value:e.value,issues:[]},f);if(b instanceof Promise)h.push(b),g=!0;else{if(0===b.issues.length)return b;h.push(b)}}return g?Promise.all(h).then(b=>bz(b,e,a,f)):bz(h,e,a,f)}}),bB=c.$constructor("$ZodDiscriminatedUnion",(a,b)=>{bA.init(a,b);let c=a._zod.parse;am.defineLazy(a._zod,"propValues",()=>{let a={};for(let c of b.options){let d=c._zod.propValues;if(!d||0===Object.keys(d).length)throw Error(`Invalid discriminated union option at index "${b.options.indexOf(c)}"`);for(let[b,c]of Object.entries(d))for(let d of(a[b]||(a[b]=new Set),c))a[b].add(d)}return a});let d=am.cached(()=>{let a=b.options,c=new Map;for(let d of a){let a=d._zod.propValues?.[b.discriminator];if(!a||0===a.size)throw Error(`Invalid discriminated union option at index "${b.options.indexOf(d)}"`);for(let b of a){if(c.has(b))throw Error(`Duplicate discriminator value "${String(b)}"`);c.set(b,d)}}return c});a._zod.parse=(e,f)=>{let g=e.value;if(!am.isObject(g))return e.issues.push({code:"invalid_type",expected:"object",input:g,inst:a}),e;let h=d.value.get(g?.[b.discriminator]);return h?h._zod.run(e,f):b.unionFallback?c(e,f):(e.issues.push({code:"invalid_union",errors:[],note:"No matching discriminator",discriminator:b.discriminator,input:g,path:[b.discriminator],inst:a}),e)}}),bC=c.$constructor("$ZodIntersection",(a,b)=>{aN.init(a,b),a._zod.parse=(a,c)=>{let d=a.value,e=b.left._zod.run({value:d,issues:[]},c),f=b.right._zod.run({value:d,issues:[]},c);return e instanceof Promise||f instanceof Promise?Promise.all([e,f]).then(([b,c])=>bD(a,b,c)):bD(a,e,f)}});function bD(a,b,c){if(b.issues.length&&a.issues.push(...b.issues),c.issues.length&&a.issues.push(...c.issues),am.aborted(a))return a;let d=function a(b,c){if(b===c||b instanceof Date&&c instanceof Date&&+b==+c)return{valid:!0,data:b};if(am.isPlainObject(b)&&am.isPlainObject(c)){let d=Object.keys(c),e=Object.keys(b).filter(a=>-1!==d.indexOf(a)),f={...b,...c};for(let d of e){let e=a(b[d],c[d]);if(!e.valid)return{valid:!1,mergeErrorPath:[d,...e.mergeErrorPath]};f[d]=e.data}return{valid:!0,data:f}}if(Array.isArray(b)&&Array.isArray(c)){if(b.length!==c.length)return{valid:!1,mergeErrorPath:[]};let d=[];for(let e=0;e<b.length;e++){let f=a(b[e],c[e]);if(!f.valid)return{valid:!1,mergeErrorPath:[e,...f.mergeErrorPath]};d.push(f.data)}return{valid:!0,data:d}}return{valid:!1,mergeErrorPath:[]}}(b.value,c.value);if(!d.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(d.mergeErrorPath)}`);return a.value=d.data,a}let bE=c.$constructor("$ZodTuple",(a,b)=>{aN.init(a,b);let c=b.items,d=c.length-[...c].reverse().findIndex(a=>"optional"!==a._zod.optin);a._zod.parse=(e,f)=>{let g=e.value;if(!Array.isArray(g))return e.issues.push({input:g,inst:a,expected:"tuple",code:"invalid_type"}),e;e.value=[];let h=[];if(!b.rest){let b=g.length>c.length,f=g.length<d-1;if(b||f)return e.issues.push({...b?{code:"too_big",maximum:c.length}:{code:"too_small",minimum:c.length},input:g,inst:a,origin:"array"}),e}let i=-1;for(let a of c){if(++i>=g.length&&i>=d)continue;let b=a._zod.run({value:g[i],issues:[]},f);b instanceof Promise?h.push(b.then(a=>bF(a,e,i))):bF(b,e,i)}if(b.rest)for(let a of g.slice(c.length)){i++;let c=b.rest._zod.run({value:a,issues:[]},f);c instanceof Promise?h.push(c.then(a=>bF(a,e,i))):bF(c,e,i)}return h.length?Promise.all(h).then(()=>e):e}});function bF(a,b,c){a.issues.length&&b.issues.push(...am.prefixIssues(c,a.issues)),b.value[c]=a.value}let bG=c.$constructor("$ZodRecord",(a,b)=>{aN.init(a,b),a._zod.parse=(d,e)=>{let f=d.value;if(!am.isPlainObject(f))return d.issues.push({expected:"record",code:"invalid_type",input:f,inst:a}),d;let g=[];if(b.keyType._zod.values){let c,h=b.keyType._zod.values;for(let a of(d.value={},h))if("string"==typeof a||"number"==typeof a||"symbol"==typeof a){let c=b.valueType._zod.run({value:f[a],issues:[]},e);c instanceof Promise?g.push(c.then(b=>{b.issues.length&&d.issues.push(...am.prefixIssues(a,b.issues)),d.value[a]=b.value})):(c.issues.length&&d.issues.push(...am.prefixIssues(a,c.issues)),d.value[a]=c.value)}for(let a in f)h.has(a)||(c=c??[]).push(a);c&&c.length>0&&d.issues.push({code:"unrecognized_keys",input:f,inst:a,keys:c})}else for(let h of(d.value={},Reflect.ownKeys(f))){if("__proto__"===h)continue;let i=b.keyType._zod.run({value:h,issues:[]},e);if(i instanceof Promise)throw Error("Async schemas not supported in object keys currently");if(i.issues.length){d.issues.push({code:"invalid_key",origin:"record",issues:i.issues.map(a=>am.finalizeIssue(a,e,c.config())),input:h,path:[h],inst:a}),d.value[i.value]=i.value;continue}let j=b.valueType._zod.run({value:f[h],issues:[]},e);j instanceof Promise?g.push(j.then(a=>{a.issues.length&&d.issues.push(...am.prefixIssues(h,a.issues)),d.value[i.value]=a.value})):(j.issues.length&&d.issues.push(...am.prefixIssues(h,j.issues)),d.value[i.value]=j.value)}return g.length?Promise.all(g).then(()=>d):d}}),bH=c.$constructor("$ZodMap",(a,b)=>{aN.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!(e instanceof Map))return c.issues.push({expected:"map",code:"invalid_type",input:e,inst:a}),c;let f=[];for(let[g,h]of(c.value=new Map,e)){let i=b.keyType._zod.run({value:g,issues:[]},d),j=b.valueType._zod.run({value:h,issues:[]},d);i instanceof Promise||j instanceof Promise?f.push(Promise.all([i,j]).then(([b,f])=>{bI(b,f,c,g,e,a,d)})):bI(i,j,c,g,e,a,d)}return f.length?Promise.all(f).then(()=>c):c}});function bI(a,b,d,e,f,g,h){a.issues.length&&(am.propertyKeyTypes.has(typeof e)?d.issues.push(...am.prefixIssues(e,a.issues)):d.issues.push({code:"invalid_key",origin:"map",input:f,inst:g,issues:a.issues.map(a=>am.finalizeIssue(a,h,c.config()))})),b.issues.length&&(am.propertyKeyTypes.has(typeof e)?d.issues.push(...am.prefixIssues(e,b.issues)):d.issues.push({origin:"map",code:"invalid_element",input:f,inst:g,key:e,issues:b.issues.map(a=>am.finalizeIssue(a,h,c.config()))})),d.value.set(a.value,b.value)}let bJ=c.$constructor("$ZodSet",(a,b)=>{aN.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!(e instanceof Set))return c.issues.push({input:e,inst:a,expected:"set",code:"invalid_type"}),c;let f=[];for(let a of(c.value=new Set,e)){let e=b.valueType._zod.run({value:a,issues:[]},d);e instanceof Promise?f.push(e.then(a=>bK(a,c))):bK(e,c)}return f.length?Promise.all(f).then(()=>c):c}});function bK(a,b){a.issues.length&&b.issues.push(...a.issues),b.value.add(a.value)}let bL=c.$constructor("$ZodEnum",(a,b)=>{aN.init(a,b);let c=am.getEnumValues(b.entries),d=new Set(c);a._zod.values=d,a._zod.pattern=RegExp(`^(${c.filter(a=>am.propertyKeyTypes.has(typeof a)).map(a=>"string"==typeof a?am.escapeRegex(a):a.toString()).join("|")})$`),a._zod.parse=(b,e)=>{let f=b.value;return d.has(f)||b.issues.push({code:"invalid_value",values:c,input:f,inst:a}),b}}),bM=c.$constructor("$ZodLiteral",(a,b)=>{if(aN.init(a,b),0===b.values.length)throw Error("Cannot create literal schema with no valid values");a._zod.values=new Set(b.values),a._zod.pattern=RegExp(`^(${b.values.map(a=>"string"==typeof a?am.escapeRegex(a):a?am.escapeRegex(a.toString()):String(a)).join("|")})$`),a._zod.parse=(c,d)=>{let e=c.value;return a._zod.values.has(e)||c.issues.push({code:"invalid_value",values:b.values,input:e,inst:a}),c}}),bN=c.$constructor("$ZodFile",(a,b)=>{aN.init(a,b),a._zod.parse=(b,c)=>{let d=b.value;return d instanceof File||b.issues.push({expected:"file",code:"invalid_type",input:d,inst:a}),b}}),bO=c.$constructor("$ZodTransform",(a,b)=>{aN.init(a,b),a._zod.parse=(d,e)=>{if("backward"===e.direction)throw new c.$ZodEncodeError(a.constructor.name);let f=b.transform(d.value,d);if(e.async)return(f instanceof Promise?f:Promise.resolve(f)).then(a=>(d.value=a,d));if(f instanceof Promise)throw new c.$ZodAsyncError;return d.value=f,d}});function bP(a,b){return a.issues.length&&void 0===b?{issues:[],value:void 0}:a}let bQ=c.$constructor("$ZodOptional",(a,b)=>{aN.init(a,b),a._zod.optin="optional",a._zod.optout="optional",am.defineLazy(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,void 0]):void 0),am.defineLazy(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${am.cleanRegex(a.source)})?$`):void 0}),a._zod.parse=(a,c)=>{if("optional"===b.innerType._zod.optin){let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(b=>bP(b,a.value)):bP(d,a.value)}return void 0===a.value?a:b.innerType._zod.run(a,c)}}),bR=c.$constructor("$ZodNullable",(a,b)=>{aN.init(a,b),am.defineLazy(a._zod,"optin",()=>b.innerType._zod.optin),am.defineLazy(a._zod,"optout",()=>b.innerType._zod.optout),am.defineLazy(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${am.cleanRegex(a.source)}|null)$`):void 0}),am.defineLazy(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,null]):void 0),a._zod.parse=(a,c)=>null===a.value?a:b.innerType._zod.run(a,c)}),bS=c.$constructor("$ZodDefault",(a,b)=>{aN.init(a,b),a._zod.optin="optional",am.defineLazy(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{if("backward"===c.direction)return b.innerType._zod.run(a,c);if(void 0===a.value)return a.value=b.defaultValue,a;let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(a=>bT(a,b)):bT(d,b)}});function bT(a,b){return void 0===a.value&&(a.value=b.defaultValue),a}let bU=c.$constructor("$ZodPrefault",(a,b)=>{aN.init(a,b),a._zod.optin="optional",am.defineLazy(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>("backward"===c.direction||void 0===a.value&&(a.value=b.defaultValue),b.innerType._zod.run(a,c))}),bV=c.$constructor("$ZodNonOptional",(a,b)=>{aN.init(a,b),am.defineLazy(a._zod,"values",()=>{let a=b.innerType._zod.values;return a?new Set([...a].filter(a=>void 0!==a)):void 0}),a._zod.parse=(c,d)=>{let e=b.innerType._zod.run(c,d);return e instanceof Promise?e.then(b=>bW(b,a)):bW(e,a)}});function bW(a,b){return a.issues.length||void 0!==a.value||a.issues.push({code:"invalid_type",expected:"nonoptional",input:a.value,inst:b}),a}let bX=c.$constructor("$ZodSuccess",(a,b)=>{aN.init(a,b),a._zod.parse=(a,d)=>{if("backward"===d.direction)throw new c.$ZodEncodeError("ZodSuccess");let e=b.innerType._zod.run(a,d);return e instanceof Promise?e.then(b=>(a.value=0===b.issues.length,a)):(a.value=0===e.issues.length,a)}}),bY=c.$constructor("$ZodCatch",(a,b)=>{aN.init(a,b),am.defineLazy(a._zod,"optin",()=>b.innerType._zod.optin),am.defineLazy(a._zod,"optout",()=>b.innerType._zod.optout),am.defineLazy(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,d)=>{if("backward"===d.direction)return b.innerType._zod.run(a,d);let e=b.innerType._zod.run(a,d);return e instanceof Promise?e.then(e=>(a.value=e.value,e.issues.length&&(a.value=b.catchValue({...a,error:{issues:e.issues.map(a=>am.finalizeIssue(a,d,c.config()))},input:a.value}),a.issues=[]),a)):(a.value=e.value,e.issues.length&&(a.value=b.catchValue({...a,error:{issues:e.issues.map(a=>am.finalizeIssue(a,d,c.config()))},input:a.value}),a.issues=[]),a)}}),bZ=c.$constructor("$ZodNaN",(a,b)=>{aN.init(a,b),a._zod.parse=(b,c)=>("number"==typeof b.value&&Number.isNaN(b.value)||b.issues.push({input:b.value,inst:a,expected:"nan",code:"invalid_type"}),b)}),b$=c.$constructor("$ZodPipe",(a,b)=>{aN.init(a,b),am.defineLazy(a._zod,"values",()=>b.in._zod.values),am.defineLazy(a._zod,"optin",()=>b.in._zod.optin),am.defineLazy(a._zod,"optout",()=>b.out._zod.optout),am.defineLazy(a._zod,"propValues",()=>b.in._zod.propValues),a._zod.parse=(a,c)=>{if("backward"===c.direction){let d=b.out._zod.run(a,c);return d instanceof Promise?d.then(a=>b_(a,b.in,c)):b_(d,b.in,c)}let d=b.in._zod.run(a,c);return d instanceof Promise?d.then(a=>b_(a,b.out,c)):b_(d,b.out,c)}});function b_(a,b,c){return a.issues.length?(a.aborted=!0,a):b._zod.run({value:a.value,issues:a.issues},c)}let b0=c.$constructor("$ZodCodec",(a,b)=>{aN.init(a,b),am.defineLazy(a._zod,"values",()=>b.in._zod.values),am.defineLazy(a._zod,"optin",()=>b.in._zod.optin),am.defineLazy(a._zod,"optout",()=>b.out._zod.optout),am.defineLazy(a._zod,"propValues",()=>b.in._zod.propValues),a._zod.parse=(a,c)=>{if("forward"===(c.direction||"forward")){let d=b.in._zod.run(a,c);return d instanceof Promise?d.then(a=>b1(a,b,c)):b1(d,b,c)}{let d=b.out._zod.run(a,c);return d instanceof Promise?d.then(a=>b1(a,b,c)):b1(d,b,c)}}});function b1(a,b,c){if(a.issues.length)return a.aborted=!0,a;if("forward"===(c.direction||"forward")){let d=b.transform(a.value,a);return d instanceof Promise?d.then(d=>b2(a,d,b.out,c)):b2(a,d,b.out,c)}{let d=b.reverseTransform(a.value,a);return d instanceof Promise?d.then(d=>b2(a,d,b.in,c)):b2(a,d,b.in,c)}}function b2(a,b,c,d){return a.issues.length?(a.aborted=!0,a):c._zod.run({value:b,issues:a.issues},d)}let b3=c.$constructor("$ZodReadonly",(a,b)=>{aN.init(a,b),am.defineLazy(a._zod,"propValues",()=>b.innerType._zod.propValues),am.defineLazy(a._zod,"values",()=>b.innerType._zod.values),am.defineLazy(a._zod,"optin",()=>b.innerType._zod.optin),am.defineLazy(a._zod,"optout",()=>b.innerType._zod.optout),a._zod.parse=(a,c)=>{if("backward"===c.direction)return b.innerType._zod.run(a,c);let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(b4):b4(d)}});function b4(a){return a.value=Object.freeze(a.value),a}let b5=c.$constructor("$ZodTemplateLiteral",(a,b)=>{aN.init(a,b);let c=[];for(let a of b.parts)if("object"==typeof a&&null!==a){if(!a._zod.pattern)throw Error(`Invalid template literal part, no pattern found: ${[...a._zod.traits].shift()}`);let b=a._zod.pattern instanceof RegExp?a._zod.pattern.source:a._zod.pattern;if(!b)throw Error(`Invalid template literal part: ${a._zod.traits}`);let d=+!!b.startsWith("^"),e=b.endsWith("$")?b.length-1:b.length;c.push(b.slice(d,e))}else if(null===a||am.primitiveTypes.has(typeof a))c.push(am.escapeRegex(`${a}`));else throw Error(`Invalid template literal part: ${a}`);a._zod.pattern=RegExp(`^${c.join("")}$`),a._zod.parse=(c,d)=>("string"!=typeof c.value?c.issues.push({input:c.value,inst:a,expected:"template_literal",code:"invalid_type"}):(a._zod.pattern.lastIndex=0,a._zod.pattern.test(c.value)||c.issues.push({input:c.value,inst:a,code:"invalid_format",format:b.format??"template_literal",pattern:a._zod.pattern.source})),c)}),b6=c.$constructor("$ZodFunction",(a,b)=>(aN.init(a,b),a._def=b,a._zod.def=b,a.implement=b=>{if("function"!=typeof b)throw Error("implement() must be called with a function");return function(...c){let e=Reflect.apply(b,this,a._def.input?(0,d.parse)(a._def.input,c):c);return a._def.output?(0,d.parse)(a._def.output,e):e}},a.implementAsync=b=>{if("function"!=typeof b)throw Error("implementAsync() must be called with a function");return async function(...c){let e=a._def.input?await (0,d.parseAsync)(a._def.input,c):c,f=await Reflect.apply(b,this,e);return a._def.output?await (0,d.parseAsync)(a._def.output,f):f}},a._zod.parse=(b,c)=>("function"!=typeof b.value?b.issues.push({code:"invalid_type",expected:"function",input:b.value,inst:a}):a._def.output&&"promise"===a._def.output._zod.def.type?b.value=a.implementAsync(b.value):b.value=a.implement(b.value),b),a.input=(...b)=>{let c=a.constructor;return new c(Array.isArray(b[0])?{type:"function",input:new bE({type:"tuple",items:b[0],rest:b[1]}),output:a._def.output}:{type:"function",input:b[0],output:a._def.output})},a.output=b=>new a.constructor({type:"function",input:a._def.input,output:b}),a)),b7=c.$constructor("$ZodPromise",(a,b)=>{aN.init(a,b),a._zod.parse=(a,c)=>Promise.resolve(a.value).then(a=>b.innerType._zod.run({value:a,issues:[]},c))}),b8=c.$constructor("$ZodLazy",(a,b)=>{aN.init(a,b),am.defineLazy(a._zod,"innerType",()=>b.getter()),am.defineLazy(a._zod,"pattern",()=>a._zod.innerType._zod.pattern),am.defineLazy(a._zod,"propValues",()=>a._zod.innerType._zod.propValues),am.defineLazy(a._zod,"optin",()=>a._zod.innerType._zod.optin??void 0),am.defineLazy(a._zod,"optout",()=>a._zod.innerType._zod.optout??void 0),a._zod.parse=(b,c)=>a._zod.innerType._zod.run(b,c)}),b9=c.$constructor("$ZodCustom",(a,b)=>{an.init(a,b),aN.init(a,b),a._zod.parse=(a,b)=>a,a._zod.check=c=>{let d=c.value,e=b.fn(d);if(e instanceof Promise)return e.then(b=>ca(b,c,d,a));ca(e,c,d,a)}});function ca(a,b,c,d){if(!a){let a={code:"custom",input:c,inst:d,path:[...d._zod.def.path??[]],continue:!d._zod.def.abort};d._zod.def.params&&(a.params=d._zod.def.params),b.issues.push(am.issue(a))}}a.i(39262);var cb=am;a.i(27035),a.i(83330),a.i(7052);var cc=a.i(88344),cd=a.i(14195);function ce(){return{localeError:(()=>{let a={string:{unit:"حرف",verb:"أن يحوي"},file:{unit:"بايت",verb:"أن يحوي"},array:{unit:"عنصر",verb:"أن يحوي"},set:{unit:"عنصر",verb:"أن يحوي"}},b={regex:"مدخل",email:"بريد إلكتروني",url:"رابط",emoji:"إيموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاريخ ووقت بمعيار ISO",date:"تاريخ بمعيار ISO",time:"وقت بمعيار ISO",duration:"مدة بمعيار ISO",ipv4:"عنوان IPv4",ipv6:"عنوان IPv6",cidrv4:"مدى عناوين بصيغة IPv4",cidrv6:"مدى عناوين بصيغة IPv6",base64:"نَص بترميز base64-encoded",base64url:"نَص بترميز base64url-encoded",json_string:"نَص على هيئة JSON",e164:"رقم هاتف بمعيار E.164",jwt:"JWT",template_literal:"مدخل"};return c=>{switch(c.code){case"invalid_type":return`مدخلات غير مقبولة: يفترض إدخال ${c.expected}، ولكن تم إدخال ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`مدخلات غير مقبولة: يفترض إدخال ${am.stringifyPrimitive(c.values[0])}`;return`اختيار غير مقبول: يتوقع انتقاء أحد هذه الخيارات: ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return` أكبر من اللازم: يفترض أن تكون ${c.origin??"القيمة"} ${b} ${c.maximum.toString()} ${d.unit??"عنصر"}`;return`أكبر من اللازم: يفترض أن تكون ${c.origin??"القيمة"} ${b} ${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`أصغر من اللازم: يفترض لـ ${c.origin} أن يكون ${b} ${c.minimum.toString()} ${d.unit}`;return`أصغر من اللازم: يفترض لـ ${c.origin} أن يكون ${b} ${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`نَص غير مقبول: يجب أن يبدأ بـ "${c.prefix}"`;if("ends_with"===c.format)return`نَص غير مقبول: يجب أن ينتهي بـ "${c.suffix}"`;if("includes"===c.format)return`نَص غير مقبول: يجب أن يتضمَّن "${c.includes}"`;if("regex"===c.format)return`نَص غير مقبول: يجب أن يطابق النمط ${c.pattern}`;return`${b[c.format]??c.format} غير مقبول`;case"not_multiple_of":return`رقم غير مقبول: يجب أن يكون من مضاعفات ${c.divisor}`;case"unrecognized_keys":return`معرف${c.keys.length>1?"ات":""} غريب${c.keys.length>1?"ة":""}: ${am.joinValues(c.keys,"، ")}`;case"invalid_key":return`معرف غير مقبول في ${c.origin}`;case"invalid_union":default:return"مدخل غير مقبول";case"invalid_element":return`مدخل غير مقبول في ${c.origin}`}}})()}}function cf(){return{localeError:(()=>{let a={string:{unit:"simvol",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"element",verb:"olmalıdır"},set:{unit:"element",verb:"olmalıdır"}},b={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Yanlış dəyər: g\xf6zlənilən ${c.expected}, daxil olan ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Yanlış dəyər: g\xf6zlənilən ${am.stringifyPrimitive(c.values[0])}`;return`Yanlış se\xe7im: aşağıdakılardan biri olmalıdır: ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`\xc7ox b\xf6y\xfck: g\xf6zlənilən ${c.origin??"dəyər"} ${b}${c.maximum.toString()} ${d.unit??"element"}`;return`\xc7ox b\xf6y\xfck: g\xf6zlənilən ${c.origin??"dəyər"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`\xc7ox ki\xe7ik: g\xf6zlənilən ${c.origin} ${b}${c.minimum.toString()} ${d.unit}`;return`\xc7ox ki\xe7ik: g\xf6zlənilən ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Yanlış mətn: "${c.prefix}" ilə başlamalıdır`;if("ends_with"===c.format)return`Yanlış mətn: "${c.suffix}" ilə bitməlidir`;if("includes"===c.format)return`Yanlış mətn: "${c.includes}" daxil olmalıdır`;if("regex"===c.format)return`Yanlış mətn: ${c.pattern} şablonuna uyğun olmalıdır`;return`Yanlış ${b[c.format]??c.format}`;case"not_multiple_of":return`Yanlış ədəd: ${c.divisor} ilə b\xf6l\xfcnə bilən olmalıdır`;case"unrecognized_keys":return`Tanınmayan a\xe7ar${c.keys.length>1?"lar":""}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`${c.origin} daxilində yanlış a\xe7ar`;case"invalid_union":return"Yanlış dəyər";case"invalid_element":return`${c.origin} daxilində yanlış dəyər`;default:return`Yanlış dəyər`}}})()}}function cg(a,b,c,d){let e=Math.abs(a),f=e%10,g=e%100;return g>=11&&g<=19?d:1===f?b:f>=2&&f<=4?c:d}function ch(){return{localeError:(()=>{let a={string:{unit:{one:"сімвал",few:"сімвалы",many:"сімвалаў"},verb:"мець"},array:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},set:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},file:{unit:{one:"байт",few:"байты",many:"байтаў"},verb:"мець"}},b={regex:"увод",email:"email адрас",url:"URL",emoji:"эмодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата і час",date:"ISO дата",time:"ISO час",duration:"ISO працягласць",ipv4:"IPv4 адрас",ipv6:"IPv6 адрас",cidrv4:"IPv4 дыяпазон",cidrv6:"IPv6 дыяпазон",base64:"радок у фармаце base64",base64url:"радок у фармаце base64url",json_string:"JSON радок",e164:"нумар E.164",jwt:"JWT",template_literal:"увод"};return c=>{switch(c.code){case"invalid_type":return`Няправільны ўвод: чакаўся ${c.expected}, атрымана ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"лік";case"object":if(Array.isArray(a))return"масіў";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Няправільны ўвод: чакалася ${am.stringifyPrimitive(c.values[0])}`;return`Няправільны варыянт: чакаўся адзін з ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d){let a=cg(Number(c.maximum),d.unit.one,d.unit.few,d.unit.many);return`Занадта вялікі: чакалася, што ${c.origin??"значэнне"} павінна ${d.verb} ${b}${c.maximum.toString()} ${a}`}return`Занадта вялікі: чакалася, што ${c.origin??"значэнне"} павінна быць ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d){let a=cg(Number(c.minimum),d.unit.one,d.unit.few,d.unit.many);return`Занадта малы: чакалася, што ${c.origin} павінна ${d.verb} ${b}${c.minimum.toString()} ${a}`}return`Занадта малы: чакалася, што ${c.origin} павінна быць ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Няправільны радок: павінен пачынацца з "${c.prefix}"`;if("ends_with"===c.format)return`Няправільны радок: павінен заканчвацца на "${c.suffix}"`;if("includes"===c.format)return`Няправільны радок: павінен змяшчаць "${c.includes}"`;if("regex"===c.format)return`Няправільны радок: павінен адпавядаць шаблону ${c.pattern}`;return`Няправільны ${b[c.format]??c.format}`;case"not_multiple_of":return`Няправільны лік: павінен быць кратным ${c.divisor}`;case"unrecognized_keys":return`Нераспазнаны ${c.keys.length>1?"ключы":"ключ"}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Няправільны ключ у ${c.origin}`;case"invalid_union":return"Няправільны ўвод";case"invalid_element":return`Няправільнае значэнне ў ${c.origin}`;default:return`Няправільны ўвод`}}})()}}function ci(){return{localeError:(()=>{let a={string:{unit:"caràcters",verb:"contenir"},file:{unit:"bytes",verb:"contenir"},array:{unit:"elements",verb:"contenir"},set:{unit:"elements",verb:"contenir"}},b={regex:"entrada",email:"adreça electrònica",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i hora ISO",date:"data ISO",time:"hora ISO",duration:"durada ISO",ipv4:"adreça IPv4",ipv6:"adreça IPv6",cidrv4:"rang IPv4",cidrv6:"rang IPv6",base64:"cadena codificada en base64",base64url:"cadena codificada en base64url",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return c=>{switch(c.code){case"invalid_type":return`Tipus inv\xe0lid: s'esperava ${c.expected}, s'ha rebut ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Valor inv\xe0lid: s'esperava ${am.stringifyPrimitive(c.values[0])}`;return`Opci\xf3 inv\xe0lida: s'esperava una de ${am.joinValues(c.values," o ")}`;case"too_big":{let b=c.inclusive?"com a màxim":"menys de",d=a[c.origin]??null;if(d)return`Massa gran: s'esperava que ${c.origin??"el valor"} contingu\xe9s ${b} ${c.maximum.toString()} ${d.unit??"elements"}`;return`Massa gran: s'esperava que ${c.origin??"el valor"} fos ${b} ${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?"com a mínim":"més de",d=a[c.origin]??null;if(d)return`Massa petit: s'esperava que ${c.origin} contingu\xe9s ${b} ${c.minimum.toString()} ${d.unit}`;return`Massa petit: s'esperava que ${c.origin} fos ${b} ${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Format inv\xe0lid: ha de comen\xe7ar amb "${c.prefix}"`;if("ends_with"===c.format)return`Format inv\xe0lid: ha d'acabar amb "${c.suffix}"`;if("includes"===c.format)return`Format inv\xe0lid: ha d'incloure "${c.includes}"`;if("regex"===c.format)return`Format inv\xe0lid: ha de coincidir amb el patr\xf3 ${c.pattern}`;return`Format inv\xe0lid per a ${b[c.format]??c.format}`;case"not_multiple_of":return`N\xfamero inv\xe0lid: ha de ser m\xfaltiple de ${c.divisor}`;case"unrecognized_keys":return`Clau${c.keys.length>1?"s":""} no reconeguda${c.keys.length>1?"s":""}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Clau inv\xe0lida a ${c.origin}`;case"invalid_union":return"Entrada invàlida";case"invalid_element":return`Element inv\xe0lid a ${c.origin}`;default:return`Entrada inv\xe0lida`}}})()}}function cj(){return{localeError:(()=>{let a={string:{unit:"znaků",verb:"mít"},file:{unit:"bajtů",verb:"mít"},array:{unit:"prvků",verb:"mít"},set:{unit:"prvků",verb:"mít"}},b={regex:"regulární výraz",email:"e-mailová adresa",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"datum a čas ve formátu ISO",date:"datum ve formátu ISO",time:"čas ve formátu ISO",duration:"doba trvání ISO",ipv4:"IPv4 adresa",ipv6:"IPv6 adresa",cidrv4:"rozsah IPv4",cidrv6:"rozsah IPv6",base64:"řetězec zakódovaný ve formátu base64",base64url:"řetězec zakódovaný ve formátu base64url",json_string:"řetězec ve formátu JSON",e164:"číslo E.164",jwt:"JWT",template_literal:"vstup"};return c=>{switch(c.code){case"invalid_type":return`Neplatn\xfd vstup: oček\xe1v\xe1no ${c.expected}, obdrženo ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"číslo";case"string":return"řetězec";case"boolean":return"boolean";case"bigint":return"bigint";case"function":return"funkce";case"symbol":return"symbol";case"undefined":return"undefined";case"object":if(Array.isArray(a))return"pole";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Neplatn\xfd vstup: oček\xe1v\xe1no ${am.stringifyPrimitive(c.values[0])}`;return`Neplatn\xe1 možnost: oček\xe1v\xe1na jedna z hodnot ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Hodnota je př\xedliš velk\xe1: ${c.origin??"hodnota"} mus\xed m\xedt ${b}${c.maximum.toString()} ${d.unit??"prvků"}`;return`Hodnota je př\xedliš velk\xe1: ${c.origin??"hodnota"} mus\xed b\xfdt ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Hodnota je př\xedliš mal\xe1: ${c.origin??"hodnota"} mus\xed m\xedt ${b}${c.minimum.toString()} ${d.unit??"prvků"}`;return`Hodnota je př\xedliš mal\xe1: ${c.origin??"hodnota"} mus\xed b\xfdt ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Neplatn\xfd řetězec: mus\xed zač\xednat na "${c.prefix}"`;if("ends_with"===c.format)return`Neplatn\xfd řetězec: mus\xed končit na "${c.suffix}"`;if("includes"===c.format)return`Neplatn\xfd řetězec: mus\xed obsahovat "${c.includes}"`;if("regex"===c.format)return`Neplatn\xfd řetězec: mus\xed odpov\xeddat vzoru ${c.pattern}`;return`Neplatn\xfd form\xe1t ${b[c.format]??c.format}`;case"not_multiple_of":return`Neplatn\xe9 č\xedslo: mus\xed b\xfdt n\xe1sobkem ${c.divisor}`;case"unrecognized_keys":return`Nezn\xe1m\xe9 kl\xedče: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Neplatn\xfd kl\xedč v ${c.origin}`;case"invalid_union":return"Neplatný vstup";case"invalid_element":return`Neplatn\xe1 hodnota v ${c.origin}`;default:return`Neplatn\xfd vstup`}}})()}}function ck(){return{localeError:(()=>{let a={string:{unit:"tegn",verb:"havde"},file:{unit:"bytes",verb:"havde"},array:{unit:"elementer",verb:"indeholdt"},set:{unit:"elementer",verb:"indeholdt"}},b={string:"streng",number:"tal",boolean:"boolean",array:"liste",object:"objekt",set:"sæt",file:"fil"},c={regex:"input",email:"e-mailadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslæt",date:"ISO-dato",time:"ISO-klokkeslæt",duration:"ISO-varighed",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodet streng",base64url:"base64url-kodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return d=>{var e,f,g,h;switch(d.code){case"invalid_type":return`Ugyldigt input: forventede ${b[e=d.expected]??e}, fik ${b[f=(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"tal";case"object":if(Array.isArray(a))return"liste";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name;return"objekt"}return b})(d.input)]??f}`;case"invalid_value":if(1===d.values.length)return`Ugyldig v\xe6rdi: forventede ${am.stringifyPrimitive(d.values[0])}`;return`Ugyldigt valg: forventede en af f\xf8lgende ${am.joinValues(d.values,"|")}`;case"too_big":{let c=d.inclusive?"<=":"<",e=a[d.origin]??null,f=b[g=d.origin]??g;if(e)return`For stor: forventede ${f??"value"} ${e.verb} ${c} ${d.maximum.toString()} ${e.unit??"elementer"}`;return`For stor: forventede ${f??"value"} havde ${c} ${d.maximum.toString()}`}case"too_small":{let c=d.inclusive?">=":">",e=a[d.origin]??null,f=b[h=d.origin]??h;if(e)return`For lille: forventede ${f} ${e.verb} ${c} ${d.minimum.toString()} ${e.unit}`;return`For lille: forventede ${f} havde ${c} ${d.minimum.toString()}`}case"invalid_format":if("starts_with"===d.format)return`Ugyldig streng: skal starte med "${d.prefix}"`;if("ends_with"===d.format)return`Ugyldig streng: skal ende med "${d.suffix}"`;if("includes"===d.format)return`Ugyldig streng: skal indeholde "${d.includes}"`;if("regex"===d.format)return`Ugyldig streng: skal matche m\xf8nsteret ${d.pattern}`;return`Ugyldig ${c[d.format]??d.format}`;case"not_multiple_of":return`Ugyldigt tal: skal v\xe6re deleligt med ${d.divisor}`;case"unrecognized_keys":return`${d.keys.length>1?"Ukendte nøgler":"Ukendt nøgle"}: ${am.joinValues(d.keys,", ")}`;case"invalid_key":return`Ugyldig n\xf8gle i ${d.origin}`;case"invalid_union":return"Ugyldigt input: matcher ingen af de tilladte typer";case"invalid_element":return`Ugyldig v\xe6rdi i ${d.origin}`;default:return"Ugyldigt input"}}})()}}function cl(){return{localeError:(()=>{let a={string:{unit:"Zeichen",verb:"zu haben"},file:{unit:"Bytes",verb:"zu haben"},array:{unit:"Elemente",verb:"zu haben"},set:{unit:"Elemente",verb:"zu haben"}},b={regex:"Eingabe",email:"E-Mail-Adresse",url:"URL",emoji:"Emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-Datum und -Uhrzeit",date:"ISO-Datum",time:"ISO-Uhrzeit",duration:"ISO-Dauer",ipv4:"IPv4-Adresse",ipv6:"IPv6-Adresse",cidrv4:"IPv4-Bereich",cidrv6:"IPv6-Bereich",base64:"Base64-codierter String",base64url:"Base64-URL-codierter String",json_string:"JSON-String",e164:"E.164-Nummer",jwt:"JWT",template_literal:"Eingabe"};return c=>{switch(c.code){case"invalid_type":return`Ung\xfcltige Eingabe: erwartet ${c.expected}, erhalten ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"Zahl";case"object":if(Array.isArray(a))return"Array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ung\xfcltige Eingabe: erwartet ${am.stringifyPrimitive(c.values[0])}`;return`Ung\xfcltige Option: erwartet eine von ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Zu gro\xdf: erwartet, dass ${c.origin??"Wert"} ${b}${c.maximum.toString()} ${d.unit??"Elemente"} hat`;return`Zu gro\xdf: erwartet, dass ${c.origin??"Wert"} ${b}${c.maximum.toString()} ist`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Zu klein: erwartet, dass ${c.origin} ${b}${c.minimum.toString()} ${d.unit} hat`;return`Zu klein: erwartet, dass ${c.origin} ${b}${c.minimum.toString()} ist`}case"invalid_format":if("starts_with"===c.format)return`Ung\xfcltiger String: muss mit "${c.prefix}" beginnen`;if("ends_with"===c.format)return`Ung\xfcltiger String: muss mit "${c.suffix}" enden`;if("includes"===c.format)return`Ung\xfcltiger String: muss "${c.includes}" enthalten`;if("regex"===c.format)return`Ung\xfcltiger String: muss dem Muster ${c.pattern} entsprechen`;return`Ung\xfcltig: ${b[c.format]??c.format}`;case"not_multiple_of":return`Ung\xfcltige Zahl: muss ein Vielfaches von ${c.divisor} sein`;case"unrecognized_keys":return`${c.keys.length>1?"Unbekannte Schlüssel":"Unbekannter Schlüssel"}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Ung\xfcltiger Schl\xfcssel in ${c.origin}`;case"invalid_union":return"Ungültige Eingabe";case"invalid_element":return`Ung\xfcltiger Wert in ${c.origin}`;default:return`Ung\xfcltige Eingabe`}}})()}}a.s(["ar",()=>ce,"az",()=>cf,"be",()=>ch,"ca",()=>ci,"cs",()=>cj,"da",()=>ck,"de",()=>cl,"en",()=>cm.default,"eo",()=>cn,"es",()=>co,"fa",()=>cp,"fi",()=>cq,"fr",()=>cr,"frCA",()=>cs,"he",()=>ct,"hu",()=>cu,"id",()=>cv,"is",()=>cw,"it",()=>cx,"ja",()=>cy,"kh",()=>cz,"ko",()=>cA,"mk",()=>cB,"ms",()=>cC,"nl",()=>cD,"no",()=>cE,"ota",()=>cF,"pl",()=>cH,"ps",()=>cG,"pt",()=>cI,"ru",()=>cK,"sl",()=>cL,"sv",()=>cM,"ta",()=>cN,"th",()=>cO,"tr",()=>cP,"ua",()=>cQ,"ur",()=>cR,"vi",()=>cS,"yo",()=>cV,"zhCN",()=>cT,"zhTW",()=>cU],43153),a.s([],52382),a.i(52382);var cm=a.i(79410);function cn(){return{localeError:(()=>{let a={string:{unit:"karaktrojn",verb:"havi"},file:{unit:"bajtojn",verb:"havi"},array:{unit:"elementojn",verb:"havi"},set:{unit:"elementojn",verb:"havi"}},b={regex:"enigo",email:"retadreso",url:"URL",emoji:"emoĝio",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datotempo",date:"ISO-dato",time:"ISO-tempo",duration:"ISO-daŭro",ipv4:"IPv4-adreso",ipv6:"IPv6-adreso",cidrv4:"IPv4-rango",cidrv6:"IPv6-rango",base64:"64-ume kodita karaktraro",base64url:"URL-64-ume kodita karaktraro",json_string:"JSON-karaktraro",e164:"E.164-nombro",jwt:"JWT",template_literal:"enigo"};return c=>{switch(c.code){case"invalid_type":return`Nevalida enigo: atendiĝis ${c.expected}, riceviĝis ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"nombro";case"object":if(Array.isArray(a))return"tabelo";if(null===a)return"senvalora";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Nevalida enigo: atendiĝis ${am.stringifyPrimitive(c.values[0])}`;return`Nevalida opcio: atendiĝis unu el ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Tro granda: atendiĝis ke ${c.origin??"valoro"} havu ${b}${c.maximum.toString()} ${d.unit??"elementojn"}`;return`Tro granda: atendiĝis ke ${c.origin??"valoro"} havu ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Tro malgranda: atendiĝis ke ${c.origin} havu ${b}${c.minimum.toString()} ${d.unit}`;return`Tro malgranda: atendiĝis ke ${c.origin} estu ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Nevalida karaktraro: devas komenciĝi per "${c.prefix}"`;if("ends_with"===c.format)return`Nevalida karaktraro: devas finiĝi per "${c.suffix}"`;if("includes"===c.format)return`Nevalida karaktraro: devas inkluzivi "${c.includes}"`;if("regex"===c.format)return`Nevalida karaktraro: devas kongrui kun la modelo ${c.pattern}`;return`Nevalida ${b[c.format]??c.format}`;case"not_multiple_of":return`Nevalida nombro: devas esti oblo de ${c.divisor}`;case"unrecognized_keys":return`Nekonata${c.keys.length>1?"j":""} ŝlosilo${c.keys.length>1?"j":""}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Nevalida ŝlosilo en ${c.origin}`;case"invalid_union":default:return"Nevalida enigo";case"invalid_element":return`Nevalida valoro en ${c.origin}`}}})()}}function co(){return{localeError:(()=>{let a={string:{unit:"caracteres",verb:"tener"},file:{unit:"bytes",verb:"tener"},array:{unit:"elementos",verb:"tener"},set:{unit:"elementos",verb:"tener"}},b={regex:"entrada",email:"dirección de correo electrónico",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"fecha y hora ISO",date:"fecha ISO",time:"hora ISO",duration:"duración ISO",ipv4:"dirección IPv4",ipv6:"dirección IPv6",cidrv4:"rango IPv4",cidrv6:"rango IPv6",base64:"cadena codificada en base64",base64url:"URL codificada en base64",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return c=>{switch(c.code){case"invalid_type":return`Entrada inv\xe1lida: se esperaba ${c.expected}, recibido ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"número";case"object":if(Array.isArray(a))return"arreglo";if(null===a)return"nulo";if(Object.getPrototypeOf(a)!==Object.prototype)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Entrada inv\xe1lida: se esperaba ${am.stringifyPrimitive(c.values[0])}`;return`Opci\xf3n inv\xe1lida: se esperaba una de ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Demasiado grande: se esperaba que ${c.origin??"valor"} tuviera ${b}${c.maximum.toString()} ${d.unit??"elementos"}`;return`Demasiado grande: se esperaba que ${c.origin??"valor"} fuera ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Demasiado peque\xf1o: se esperaba que ${c.origin} tuviera ${b}${c.minimum.toString()} ${d.unit}`;return`Demasiado peque\xf1o: se esperaba que ${c.origin} fuera ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Cadena inv\xe1lida: debe comenzar con "${c.prefix}"`;if("ends_with"===c.format)return`Cadena inv\xe1lida: debe terminar en "${c.suffix}"`;if("includes"===c.format)return`Cadena inv\xe1lida: debe incluir "${c.includes}"`;if("regex"===c.format)return`Cadena inv\xe1lida: debe coincidir con el patr\xf3n ${c.pattern}`;return`Inv\xe1lido ${b[c.format]??c.format}`;case"not_multiple_of":return`N\xfamero inv\xe1lido: debe ser m\xfaltiplo de ${c.divisor}`;case"unrecognized_keys":return`Llave${c.keys.length>1?"s":""} desconocida${c.keys.length>1?"s":""}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Llave inv\xe1lida en ${c.origin}`;case"invalid_union":return"Entrada inválida";case"invalid_element":return`Valor inv\xe1lido en ${c.origin}`;default:return`Entrada inv\xe1lida`}}})()}}function cp(){return{localeError:(()=>{let a={string:{unit:"کاراکتر",verb:"داشته باشد"},file:{unit:"بایت",verb:"داشته باشد"},array:{unit:"آیتم",verb:"داشته باشد"},set:{unit:"آیتم",verb:"داشته باشد"}},b={regex:"ورودی",email:"آدرس ایمیل",url:"URL",emoji:"ایموجی",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاریخ و زمان ایزو",date:"تاریخ ایزو",time:"زمان ایزو",duration:"مدت زمان ایزو",ipv4:"IPv4 آدرس",ipv6:"IPv6 آدرس",cidrv4:"IPv4 دامنه",cidrv6:"IPv6 دامنه",base64:"base64-encoded رشته",base64url:"base64url-encoded رشته",json_string:"JSON رشته",e164:"E.164 عدد",jwt:"JWT",template_literal:"ورودی"};return c=>{switch(c.code){case"invalid_type":return`ورودی نامعتبر: می‌بایست ${c.expected} می‌بود، ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"عدد";case"object":if(Array.isArray(a))return"آرایه";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} دریافت شد`;case"invalid_value":if(1===c.values.length)return`ورودی نامعتبر: می‌بایست ${am.stringifyPrimitive(c.values[0])} می‌بود`;return`گزینه نامعتبر: می‌بایست یکی از ${am.joinValues(c.values,"|")} می‌بود`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`خیلی بزرگ: ${c.origin??"مقدار"} باید ${b}${c.maximum.toString()} ${d.unit??"عنصر"} باشد`;return`خیلی بزرگ: ${c.origin??"مقدار"} باید ${b}${c.maximum.toString()} باشد`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`خیلی کوچک: ${c.origin} باید ${b}${c.minimum.toString()} ${d.unit} باشد`;return`خیلی کوچک: ${c.origin} باید ${b}${c.minimum.toString()} باشد`}case"invalid_format":if("starts_with"===c.format)return`رشته نامعتبر: باید با "${c.prefix}" شروع شود`;if("ends_with"===c.format)return`رشته نامعتبر: باید با "${c.suffix}" تمام شود`;if("includes"===c.format)return`رشته نامعتبر: باید شامل "${c.includes}" باشد`;if("regex"===c.format)return`رشته نامعتبر: باید با الگوی ${c.pattern} مطابقت داشته باشد`;return`${b[c.format]??c.format} نامعتبر`;case"not_multiple_of":return`عدد نامعتبر: باید مضرب ${c.divisor} باشد`;case"unrecognized_keys":return`کلید${c.keys.length>1?"های":""} ناشناس: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`کلید ناشناس در ${c.origin}`;case"invalid_union":default:return`ورودی نامعتبر`;case"invalid_element":return`مقدار نامعتبر در ${c.origin}`}}})()}}function cq(){return{localeError:(()=>{let a={string:{unit:"merkkiä",subject:"merkkijonon"},file:{unit:"tavua",subject:"tiedoston"},array:{unit:"alkiota",subject:"listan"},set:{unit:"alkiota",subject:"joukon"},number:{unit:"",subject:"luvun"},bigint:{unit:"",subject:"suuren kokonaisluvun"},int:{unit:"",subject:"kokonaisluvun"},date:{unit:"",subject:"päivämäärän"}},b={regex:"säännöllinen lauseke",email:"sähköpostiosoite",url:"URL-osoite",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-aikaleima",date:"ISO-päivämäärä",time:"ISO-aika",duration:"ISO-kesto",ipv4:"IPv4-osoite",ipv6:"IPv6-osoite",cidrv4:"IPv4-alue",cidrv6:"IPv6-alue",base64:"base64-koodattu merkkijono",base64url:"base64url-koodattu merkkijono",json_string:"JSON-merkkijono",e164:"E.164-luku",jwt:"JWT",template_literal:"templaattimerkkijono"};return c=>{switch(c.code){case"invalid_type":return`Virheellinen tyyppi: odotettiin ${c.expected}, oli ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Virheellinen sy\xf6te: t\xe4ytyy olla ${am.stringifyPrimitive(c.values[0])}`;return`Virheellinen valinta: t\xe4ytyy olla yksi seuraavista: ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Liian suuri: ${d.subject} t\xe4ytyy olla ${b}${c.maximum.toString()} ${d.unit}`.trim();return`Liian suuri: arvon t\xe4ytyy olla ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Liian pieni: ${d.subject} t\xe4ytyy olla ${b}${c.minimum.toString()} ${d.unit}`.trim();return`Liian pieni: arvon t\xe4ytyy olla ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Virheellinen sy\xf6te: t\xe4ytyy alkaa "${c.prefix}"`;if("ends_with"===c.format)return`Virheellinen sy\xf6te: t\xe4ytyy loppua "${c.suffix}"`;if("includes"===c.format)return`Virheellinen sy\xf6te: t\xe4ytyy sis\xe4lt\xe4\xe4 "${c.includes}"`;if("regex"===c.format)return`Virheellinen sy\xf6te: t\xe4ytyy vastata s\xe4\xe4nn\xf6llist\xe4 lauseketta ${c.pattern}`;return`Virheellinen ${b[c.format]??c.format}`;case"not_multiple_of":return`Virheellinen luku: t\xe4ytyy olla luvun ${c.divisor} monikerta`;case"unrecognized_keys":return`${c.keys.length>1?"Tuntemattomat avaimet":"Tuntematon avain"}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return"Virheellinen avain tietueessa";case"invalid_union":return"Virheellinen unioni";case"invalid_element":return"Virheellinen arvo joukossa";default:return`Virheellinen sy\xf6te`}}})()}}function cr(){return{localeError:(()=>{let a={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}},b={regex:"entrée",email:"adresse e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date et heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return c=>{switch(c.code){case"invalid_type":return`Entr\xe9e invalide : ${c.expected} attendu, ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"nombre";case"object":if(Array.isArray(a))return"tableau";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} re\xe7u`;case"invalid_value":if(1===c.values.length)return`Entr\xe9e invalide : ${am.stringifyPrimitive(c.values[0])} attendu`;return`Option invalide : une valeur parmi ${am.joinValues(c.values,"|")} attendue`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Trop grand : ${c.origin??"valeur"} doit ${d.verb} ${b}${c.maximum.toString()} ${d.unit??"élément(s)"}`;return`Trop grand : ${c.origin??"valeur"} doit \xeatre ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Trop petit : ${c.origin} doit ${d.verb} ${b}${c.minimum.toString()} ${d.unit}`;return`Trop petit : ${c.origin} doit \xeatre ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Cha\xeene invalide : doit commencer par "${c.prefix}"`;if("ends_with"===c.format)return`Cha\xeene invalide : doit se terminer par "${c.suffix}"`;if("includes"===c.format)return`Cha\xeene invalide : doit inclure "${c.includes}"`;if("regex"===c.format)return`Cha\xeene invalide : doit correspondre au mod\xe8le ${c.pattern}`;return`${b[c.format]??c.format} invalide`;case"not_multiple_of":return`Nombre invalide : doit \xeatre un multiple de ${c.divisor}`;case"unrecognized_keys":return`Cl\xe9${c.keys.length>1?"s":""} non reconnue${c.keys.length>1?"s":""} : ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Cl\xe9 invalide dans ${c.origin}`;case"invalid_union":return"Entrée invalide";case"invalid_element":return`Valeur invalide dans ${c.origin}`;default:return`Entr\xe9e invalide`}}})()}}function cs(){return{localeError:(()=>{let a={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}},b={regex:"entrée",email:"adresse courriel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date-heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return c=>{switch(c.code){case"invalid_type":return`Entr\xe9e invalide : attendu ${c.expected}, re\xe7u ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Entr\xe9e invalide : attendu ${am.stringifyPrimitive(c.values[0])}`;return`Option invalide : attendu l'une des valeurs suivantes ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"≤":"<",d=a[c.origin]??null;if(d)return`Trop grand : attendu que ${c.origin??"la valeur"} ait ${b}${c.maximum.toString()} ${d.unit}`;return`Trop grand : attendu que ${c.origin??"la valeur"} soit ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?"≥":">",d=a[c.origin]??null;if(d)return`Trop petit : attendu que ${c.origin} ait ${b}${c.minimum.toString()} ${d.unit}`;return`Trop petit : attendu que ${c.origin} soit ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Cha\xeene invalide : doit commencer par "${c.prefix}"`;if("ends_with"===c.format)return`Cha\xeene invalide : doit se terminer par "${c.suffix}"`;if("includes"===c.format)return`Cha\xeene invalide : doit inclure "${c.includes}"`;if("regex"===c.format)return`Cha\xeene invalide : doit correspondre au motif ${c.pattern}`;return`${b[c.format]??c.format} invalide`;case"not_multiple_of":return`Nombre invalide : doit \xeatre un multiple de ${c.divisor}`;case"unrecognized_keys":return`Cl\xe9${c.keys.length>1?"s":""} non reconnue${c.keys.length>1?"s":""} : ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Cl\xe9 invalide dans ${c.origin}`;case"invalid_union":return"Entrée invalide";case"invalid_element":return`Valeur invalide dans ${c.origin}`;default:return`Entr\xe9e invalide`}}})()}}function ct(){return{localeError:(()=>{let a={string:{unit:"אותיות",verb:"לכלול"},file:{unit:"בייטים",verb:"לכלול"},array:{unit:"פריטים",verb:"לכלול"},set:{unit:"פריטים",verb:"לכלול"}},b={regex:"קלט",email:"כתובת אימייל",url:"כתובת רשת",emoji:"אימוג'י",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"תאריך וזמן ISO",date:"תאריך ISO",time:"זמן ISO",duration:"משך זמן ISO",ipv4:"כתובת IPv4",ipv6:"כתובת IPv6",cidrv4:"טווח IPv4",cidrv6:"טווח IPv6",base64:"מחרוזת בבסיס 64",base64url:"מחרוזת בבסיס 64 לכתובות רשת",json_string:"מחרוזת JSON",e164:"מספר E.164",jwt:"JWT",template_literal:"קלט"};return c=>{switch(c.code){case"invalid_type":return`קלט לא תקין: צריך ${c.expected}, התקבל ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`קלט לא תקין: צריך ${am.stringifyPrimitive(c.values[0])}`;return`קלט לא תקין: צריך אחת מהאפשרויות  ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`גדול מדי: ${c.origin??"value"} צריך להיות ${b}${c.maximum.toString()} ${d.unit??"elements"}`;return`גדול מדי: ${c.origin??"value"} צריך להיות ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`קטן מדי: ${c.origin} צריך להיות ${b}${c.minimum.toString()} ${d.unit}`;return`קטן מדי: ${c.origin} צריך להיות ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`מחרוזת לא תקינה: חייבת להתחיל ב"${c.prefix}"`;if("ends_with"===c.format)return`מחרוזת לא תקינה: חייבת להסתיים ב "${c.suffix}"`;if("includes"===c.format)return`מחרוזת לא תקינה: חייבת לכלול "${c.includes}"`;if("regex"===c.format)return`מחרוזת לא תקינה: חייבת להתאים לתבנית ${c.pattern}`;return`${b[c.format]??c.format} לא תקין`;case"not_multiple_of":return`מספר לא תקין: חייב להיות מכפלה של ${c.divisor}`;case"unrecognized_keys":return`מפתח${c.keys.length>1?"ות":""} לא מזוה${c.keys.length>1?"ים":"ה"}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`מפתח לא תקין ב${c.origin}`;case"invalid_union":return"קלט לא תקין";case"invalid_element":return`ערך לא תקין ב${c.origin}`;default:return`קלט לא תקין`}}})()}}function cu(){return{localeError:(()=>{let a={string:{unit:"karakter",verb:"legyen"},file:{unit:"byte",verb:"legyen"},array:{unit:"elem",verb:"legyen"},set:{unit:"elem",verb:"legyen"}},b={regex:"bemenet",email:"email cím",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO időbélyeg",date:"ISO dátum",time:"ISO idő",duration:"ISO időintervallum",ipv4:"IPv4 cím",ipv6:"IPv6 cím",cidrv4:"IPv4 tartomány",cidrv6:"IPv6 tartomány",base64:"base64-kódolt string",base64url:"base64url-kódolt string",json_string:"JSON string",e164:"E.164 szám",jwt:"JWT",template_literal:"bemenet"};return c=>{switch(c.code){case"invalid_type":return`\xc9rv\xe9nytelen bemenet: a v\xe1rt \xe9rt\xe9k ${c.expected}, a kapott \xe9rt\xe9k ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"szám";case"object":if(Array.isArray(a))return"tömb";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`\xc9rv\xe9nytelen bemenet: a v\xe1rt \xe9rt\xe9k ${am.stringifyPrimitive(c.values[0])}`;return`\xc9rv\xe9nytelen opci\xf3: valamelyik \xe9rt\xe9k v\xe1rt ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`T\xfal nagy: ${c.origin??"érték"} m\xe9rete t\xfal nagy ${b}${c.maximum.toString()} ${d.unit??"elem"}`;return`T\xfal nagy: a bemeneti \xe9rt\xe9k ${c.origin??"érték"} t\xfal nagy: ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`T\xfal kicsi: a bemeneti \xe9rt\xe9k ${c.origin} m\xe9rete t\xfal kicsi ${b}${c.minimum.toString()} ${d.unit}`;return`T\xfal kicsi: a bemeneti \xe9rt\xe9k ${c.origin} t\xfal kicsi ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`\xc9rv\xe9nytelen string: "${c.prefix}" \xe9rt\xe9kkel kell kezdődnie`;if("ends_with"===c.format)return`\xc9rv\xe9nytelen string: "${c.suffix}" \xe9rt\xe9kkel kell v\xe9gződnie`;if("includes"===c.format)return`\xc9rv\xe9nytelen string: "${c.includes}" \xe9rt\xe9ket kell tartalmaznia`;if("regex"===c.format)return`\xc9rv\xe9nytelen string: ${c.pattern} mint\xe1nak kell megfelelnie`;return`\xc9rv\xe9nytelen ${b[c.format]??c.format}`;case"not_multiple_of":return`\xc9rv\xe9nytelen sz\xe1m: ${c.divisor} t\xf6bbsz\xf6r\xf6s\xe9nek kell lennie`;case"unrecognized_keys":return`Ismeretlen kulcs${c.keys.length>1?"s":""}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`\xc9rv\xe9nytelen kulcs ${c.origin}`;case"invalid_union":return"Érvénytelen bemenet";case"invalid_element":return`\xc9rv\xe9nytelen \xe9rt\xe9k: ${c.origin}`;default:return`\xc9rv\xe9nytelen bemenet`}}})()}}function cv(){return{localeError:(()=>{let a={string:{unit:"karakter",verb:"memiliki"},file:{unit:"byte",verb:"memiliki"},array:{unit:"item",verb:"memiliki"},set:{unit:"item",verb:"memiliki"}},b={regex:"input",email:"alamat email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tanggal dan waktu format ISO",date:"tanggal format ISO",time:"jam format ISO",duration:"durasi format ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"rentang alamat IPv4",cidrv6:"rentang alamat IPv6",base64:"string dengan enkode base64",base64url:"string dengan enkode base64url",json_string:"string JSON",e164:"angka E.164",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Input tidak valid: diharapkan ${c.expected}, diterima ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Input tidak valid: diharapkan ${am.stringifyPrimitive(c.values[0])}`;return`Pilihan tidak valid: diharapkan salah satu dari ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Terlalu besar: diharapkan ${c.origin??"value"} memiliki ${b}${c.maximum.toString()} ${d.unit??"elemen"}`;return`Terlalu besar: diharapkan ${c.origin??"value"} menjadi ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Terlalu kecil: diharapkan ${c.origin} memiliki ${b}${c.minimum.toString()} ${d.unit}`;return`Terlalu kecil: diharapkan ${c.origin} menjadi ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`String tidak valid: harus dimulai dengan "${c.prefix}"`;if("ends_with"===c.format)return`String tidak valid: harus berakhir dengan "${c.suffix}"`;if("includes"===c.format)return`String tidak valid: harus menyertakan "${c.includes}"`;if("regex"===c.format)return`String tidak valid: harus sesuai pola ${c.pattern}`;return`${b[c.format]??c.format} tidak valid`;case"not_multiple_of":return`Angka tidak valid: harus kelipatan dari ${c.divisor}`;case"unrecognized_keys":return`Kunci tidak dikenali ${c.keys.length>1?"s":""}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Kunci tidak valid di ${c.origin}`;case"invalid_union":default:return"Input tidak valid";case"invalid_element":return`Nilai tidak valid di ${c.origin}`}}})()}}function cw(){return{localeError:(()=>{let a={string:{unit:"stafi",verb:"að hafa"},file:{unit:"bæti",verb:"að hafa"},array:{unit:"hluti",verb:"að hafa"},set:{unit:"hluti",verb:"að hafa"}},b={regex:"gildi",email:"netfang",url:"vefslóð",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dagsetning og tími",date:"ISO dagsetning",time:"ISO tími",duration:"ISO tímalengd",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded strengur",base64url:"base64url-encoded strengur",json_string:"JSON strengur",e164:"E.164 tölugildi",jwt:"JWT",template_literal:"gildi"};return c=>{switch(c.code){case"invalid_type":return`Rangt gildi: \xde\xfa sl\xf3st inn ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"númer";case"object":if(Array.isArray(a))return"fylki";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} \xfear sem \xe1 a\xf0 vera ${c.expected}`;case"invalid_value":if(1===c.values.length)return`Rangt gildi: gert r\xe1\xf0 fyrir ${am.stringifyPrimitive(c.values[0])}`;return`\xd3gilt val: m\xe1 vera eitt af eftirfarandi ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Of st\xf3rt: gert er r\xe1\xf0 fyrir a\xf0 ${c.origin??"gildi"} hafi ${b}${c.maximum.toString()} ${d.unit??"hluti"}`;return`Of st\xf3rt: gert er r\xe1\xf0 fyrir a\xf0 ${c.origin??"gildi"} s\xe9 ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Of l\xedti\xf0: gert er r\xe1\xf0 fyrir a\xf0 ${c.origin} hafi ${b}${c.minimum.toString()} ${d.unit}`;return`Of l\xedti\xf0: gert er r\xe1\xf0 fyrir a\xf0 ${c.origin} s\xe9 ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 byrja \xe1 "${c.prefix}"`;if("ends_with"===c.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 enda \xe1 "${c.suffix}"`;if("includes"===c.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 innihalda "${c.includes}"`;if("regex"===c.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 fylgja mynstri ${c.pattern}`;return`Rangt ${b[c.format]??c.format}`;case"not_multiple_of":return`R\xf6ng tala: ver\xf0ur a\xf0 vera margfeldi af ${c.divisor}`;case"unrecognized_keys":return`\xd3\xfeekkt ${c.keys.length>1?"ir lyklar":"ur lykill"}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Rangur lykill \xed ${c.origin}`;case"invalid_union":default:return"Rangt gildi";case"invalid_element":return`Rangt gildi \xed ${c.origin}`}}})()}}function cx(){return{localeError:(()=>{let a={string:{unit:"caratteri",verb:"avere"},file:{unit:"byte",verb:"avere"},array:{unit:"elementi",verb:"avere"},set:{unit:"elementi",verb:"avere"}},b={regex:"input",email:"indirizzo email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e ora ISO",date:"data ISO",time:"ora ISO",duration:"durata ISO",ipv4:"indirizzo IPv4",ipv6:"indirizzo IPv6",cidrv4:"intervallo IPv4",cidrv6:"intervallo IPv6",base64:"stringa codificata in base64",base64url:"URL codificata in base64",json_string:"stringa JSON",e164:"numero E.164",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Input non valido: atteso ${c.expected}, ricevuto ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"numero";case"object":if(Array.isArray(a))return"vettore";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Input non valido: atteso ${am.stringifyPrimitive(c.values[0])}`;return`Opzione non valida: atteso uno tra ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Troppo grande: ${c.origin??"valore"} deve avere ${b}${c.maximum.toString()} ${d.unit??"elementi"}`;return`Troppo grande: ${c.origin??"valore"} deve essere ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Troppo piccolo: ${c.origin} deve avere ${b}${c.minimum.toString()} ${d.unit}`;return`Troppo piccolo: ${c.origin} deve essere ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Stringa non valida: deve iniziare con "${c.prefix}"`;if("ends_with"===c.format)return`Stringa non valida: deve terminare con "${c.suffix}"`;if("includes"===c.format)return`Stringa non valida: deve includere "${c.includes}"`;if("regex"===c.format)return`Stringa non valida: deve corrispondere al pattern ${c.pattern}`;return`Invalid ${b[c.format]??c.format}`;case"not_multiple_of":return`Numero non valido: deve essere un multiplo di ${c.divisor}`;case"unrecognized_keys":return`Chiav${c.keys.length>1?"i":"e"} non riconosciut${c.keys.length>1?"e":"a"}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Chiave non valida in ${c.origin}`;case"invalid_union":default:return"Input non valido";case"invalid_element":return`Valore non valido in ${c.origin}`}}})()}}function cy(){return{localeError:(()=>{let a={string:{unit:"文字",verb:"である"},file:{unit:"バイト",verb:"である"},array:{unit:"要素",verb:"である"},set:{unit:"要素",verb:"である"}},b={regex:"入力値",email:"メールアドレス",url:"URL",emoji:"絵文字",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日時",date:"ISO日付",time:"ISO時刻",duration:"ISO期間",ipv4:"IPv4アドレス",ipv6:"IPv6アドレス",cidrv4:"IPv4範囲",cidrv6:"IPv6範囲",base64:"base64エンコード文字列",base64url:"base64urlエンコード文字列",json_string:"JSON文字列",e164:"E.164番号",jwt:"JWT",template_literal:"入力値"};return c=>{switch(c.code){case"invalid_type":return`無効な入力: ${c.expected}が期待されましたが、${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"数値";case"object":if(Array.isArray(a))return"配列";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}が入力されました`;case"invalid_value":if(1===c.values.length)return`無効な入力: ${am.stringifyPrimitive(c.values[0])}が期待されました`;return`無効な選択: ${am.joinValues(c.values,"、")}のいずれかである必要があります`;case"too_big":{let b=c.inclusive?"以下である":"より小さい",d=a[c.origin]??null;if(d)return`大きすぎる値: ${c.origin??"値"}は${c.maximum.toString()}${d.unit??"要素"}${b}必要があります`;return`大きすぎる値: ${c.origin??"値"}は${c.maximum.toString()}${b}必要があります`}case"too_small":{let b=c.inclusive?"以上である":"より大きい",d=a[c.origin]??null;if(d)return`小さすぎる値: ${c.origin}は${c.minimum.toString()}${d.unit}${b}必要があります`;return`小さすぎる値: ${c.origin}は${c.minimum.toString()}${b}必要があります`}case"invalid_format":if("starts_with"===c.format)return`無効な文字列: "${c.prefix}"で始まる必要があります`;if("ends_with"===c.format)return`無効な文字列: "${c.suffix}"で終わる必要があります`;if("includes"===c.format)return`無効な文字列: "${c.includes}"を含む必要があります`;if("regex"===c.format)return`無効な文字列: パターン${c.pattern}に一致する必要があります`;return`無効な${b[c.format]??c.format}`;case"not_multiple_of":return`無効な数値: ${c.divisor}の倍数である必要があります`;case"unrecognized_keys":return`認識されていないキー${c.keys.length>1?"群":""}: ${am.joinValues(c.keys,"、")}`;case"invalid_key":return`${c.origin}内の無効なキー`;case"invalid_union":return"無効な入力";case"invalid_element":return`${c.origin}内の無効な値`;default:return`無効な入力`}}})()}}function cz(){return{localeError:(()=>{let a={string:{unit:"តួអក្សរ",verb:"គួរមាន"},file:{unit:"បៃ",verb:"គួរមាន"},array:{unit:"ធាតុ",verb:"គួរមាន"},set:{unit:"ធាតុ",verb:"គួរមាន"}},b={regex:"ទិន្នន័យបញ្ចូល",email:"អាសយដ្ឋានអ៊ីមែល",url:"URL",emoji:"សញ្ញាអារម្មណ៍",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"កាលបរិច្ឆេទ និងម៉ោង ISO",date:"កាលបរិច្ឆេទ ISO",time:"ម៉ោង ISO",duration:"រយៈពេល ISO",ipv4:"អាសយដ្ឋាន IPv4",ipv6:"អាសយដ្ឋាន IPv6",cidrv4:"ដែនអាសយដ្ឋាន IPv4",cidrv6:"ដែនអាសយដ្ឋាន IPv6",base64:"ខ្សែអក្សរអ៊ិកូដ base64",base64url:"ខ្សែអក្សរអ៊ិកូដ base64url",json_string:"ខ្សែអក្សរ JSON",e164:"លេខ E.164",jwt:"JWT",template_literal:"ទិន្នន័យបញ្ចូល"};return c=>{switch(c.code){case"invalid_type":return`ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ${c.expected} ប៉ុន្តែទទួលបាន ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"មិនមែនជាលេខ (NaN)":"លេខ";case"object":if(Array.isArray(a))return"អារេ (Array)";if(null===a)return"គ្មានតម្លៃ (null)";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ${am.stringifyPrimitive(c.values[0])}`;return`ជម្រើសមិនត្រឹមត្រូវ៖ ត្រូវជាមួយក្នុងចំណោម ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`ធំពេក៖ ត្រូវការ ${c.origin??"តម្លៃ"} ${b} ${c.maximum.toString()} ${d.unit??"ធាតុ"}`;return`ធំពេក៖ ត្រូវការ ${c.origin??"តម្លៃ"} ${b} ${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`តូចពេក៖ ត្រូវការ ${c.origin} ${b} ${c.minimum.toString()} ${d.unit}`;return`តូចពេក៖ ត្រូវការ ${c.origin} ${b} ${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវចាប់ផ្តើមដោយ "${c.prefix}"`;if("ends_with"===c.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវបញ្ចប់ដោយ "${c.suffix}"`;if("includes"===c.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវមាន "${c.includes}"`;if("regex"===c.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវតែផ្គូផ្គងនឹងទម្រង់ដែលបានកំណត់ ${c.pattern}`;return`មិនត្រឹមត្រូវ៖ ${b[c.format]??c.format}`;case"not_multiple_of":return`លេខមិនត្រឹមត្រូវ៖ ត្រូវតែជាពហុគុណនៃ ${c.divisor}`;case"unrecognized_keys":return`រកឃើញសោមិនស្គាល់៖ ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`សោមិនត្រឹមត្រូវនៅក្នុង ${c.origin}`;case"invalid_union":default:return`ទិន្នន័យមិនត្រឹមត្រូវ`;case"invalid_element":return`ទិន្នន័យមិនត្រឹមត្រូវនៅក្នុង ${c.origin}`}}})()}}function cA(){return{localeError:(()=>{let a={string:{unit:"문자",verb:"to have"},file:{unit:"바이트",verb:"to have"},array:{unit:"개",verb:"to have"},set:{unit:"개",verb:"to have"}},b={regex:"입력",email:"이메일 주소",url:"URL",emoji:"이모지",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 날짜시간",date:"ISO 날짜",time:"ISO 시간",duration:"ISO 기간",ipv4:"IPv4 주소",ipv6:"IPv6 주소",cidrv4:"IPv4 범위",cidrv6:"IPv6 범위",base64:"base64 인코딩 문자열",base64url:"base64url 인코딩 문자열",json_string:"JSON 문자열",e164:"E.164 번호",jwt:"JWT",template_literal:"입력"};return c=>{switch(c.code){case"invalid_type":return`잘못된 입력: 예상 타입은 ${c.expected}, 받은 타입은 ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}입니다`;case"invalid_value":if(1===c.values.length)return`잘못된 입력: 값은 ${am.stringifyPrimitive(c.values[0])} 이어야 합니다`;return`잘못된 옵션: ${am.joinValues(c.values,"또는 ")} 중 하나여야 합니다`;case"too_big":{let b=c.inclusive?"이하":"미만",d="미만"===b?"이어야 합니다":"여야 합니다",e=a[c.origin]??null,f=e?.unit??"요소";if(e)return`${c.origin??"값"}이 너무 큽니다: ${c.maximum.toString()}${f} ${b}${d}`;return`${c.origin??"값"}이 너무 큽니다: ${c.maximum.toString()} ${b}${d}`}case"too_small":{let b=c.inclusive?"이상":"초과",d="이상"===b?"이어야 합니다":"여야 합니다",e=a[c.origin]??null,f=e?.unit??"요소";if(e)return`${c.origin??"값"}이 너무 작습니다: ${c.minimum.toString()}${f} ${b}${d}`;return`${c.origin??"값"}이 너무 작습니다: ${c.minimum.toString()} ${b}${d}`}case"invalid_format":if("starts_with"===c.format)return`잘못된 문자열: "${c.prefix}"(으)로 시작해야 합니다`;if("ends_with"===c.format)return`잘못된 문자열: "${c.suffix}"(으)로 끝나야 합니다`;if("includes"===c.format)return`잘못된 문자열: "${c.includes}"을(를) 포함해야 합니다`;if("regex"===c.format)return`잘못된 문자열: 정규식 ${c.pattern} 패턴과 일치해야 합니다`;return`잘못된 ${b[c.format]??c.format}`;case"not_multiple_of":return`잘못된 숫자: ${c.divisor}의 배수여야 합니다`;case"unrecognized_keys":return`인식할 수 없는 키: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`잘못된 키: ${c.origin}`;case"invalid_union":default:return`잘못된 입력`;case"invalid_element":return`잘못된 값: ${c.origin}`}}})()}}function cB(){return{localeError:(()=>{let a={string:{unit:"знаци",verb:"да имаат"},file:{unit:"бајти",verb:"да имаат"},array:{unit:"ставки",verb:"да имаат"},set:{unit:"ставки",verb:"да имаат"}},b={regex:"внес",email:"адреса на е-пошта",url:"URL",emoji:"емоџи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO датум и време",date:"ISO датум",time:"ISO време",duration:"ISO времетраење",ipv4:"IPv4 адреса",ipv6:"IPv6 адреса",cidrv4:"IPv4 опсег",cidrv6:"IPv6 опсег",base64:"base64-енкодирана низа",base64url:"base64url-енкодирана низа",json_string:"JSON низа",e164:"E.164 број",jwt:"JWT",template_literal:"внес"};return c=>{switch(c.code){case"invalid_type":return`Грешен внес: се очекува ${c.expected}, примено ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"број";case"object":if(Array.isArray(a))return"низа";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Invalid input: expected ${am.stringifyPrimitive(c.values[0])}`;return`Грешана опција: се очекува една ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Премногу голем: се очекува ${c.origin??"вредноста"} да има ${b}${c.maximum.toString()} ${d.unit??"елементи"}`;return`Премногу голем: се очекува ${c.origin??"вредноста"} да биде ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Премногу мал: се очекува ${c.origin} да има ${b}${c.minimum.toString()} ${d.unit}`;return`Премногу мал: се очекува ${c.origin} да биде ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Неважечка низа: мора да започнува со "${c.prefix}"`;if("ends_with"===c.format)return`Неважечка низа: мора да завршува со "${c.suffix}"`;if("includes"===c.format)return`Неважечка низа: мора да вклучува "${c.includes}"`;if("regex"===c.format)return`Неважечка низа: мора да одгоара на патернот ${c.pattern}`;return`Invalid ${b[c.format]??c.format}`;case"not_multiple_of":return`Грешен број: мора да биде делив со ${c.divisor}`;case"unrecognized_keys":return`${c.keys.length>1?"Непрепознаени клучеви":"Непрепознаен клуч"}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Грешен клуч во ${c.origin}`;case"invalid_union":return"Грешен внес";case"invalid_element":return`Грешна вредност во ${c.origin}`;default:return`Грешен внес`}}})()}}function cC(){return{localeError:(()=>{let a={string:{unit:"aksara",verb:"mempunyai"},file:{unit:"bait",verb:"mempunyai"},array:{unit:"elemen",verb:"mempunyai"},set:{unit:"elemen",verb:"mempunyai"}},b={regex:"input",email:"alamat e-mel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tarikh masa ISO",date:"tarikh ISO",time:"masa ISO",duration:"tempoh ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"julat IPv4",cidrv6:"julat IPv6",base64:"string dikodkan base64",base64url:"string dikodkan base64url",json_string:"string JSON",e164:"nombor E.164",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Input tidak sah: dijangka ${c.expected}, diterima ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"nombor";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Input tidak sah: dijangka ${am.stringifyPrimitive(c.values[0])}`;return`Pilihan tidak sah: dijangka salah satu daripada ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Terlalu besar: dijangka ${c.origin??"nilai"} ${d.verb} ${b}${c.maximum.toString()} ${d.unit??"elemen"}`;return`Terlalu besar: dijangka ${c.origin??"nilai"} adalah ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Terlalu kecil: dijangka ${c.origin} ${d.verb} ${b}${c.minimum.toString()} ${d.unit}`;return`Terlalu kecil: dijangka ${c.origin} adalah ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`String tidak sah: mesti bermula dengan "${c.prefix}"`;if("ends_with"===c.format)return`String tidak sah: mesti berakhir dengan "${c.suffix}"`;if("includes"===c.format)return`String tidak sah: mesti mengandungi "${c.includes}"`;if("regex"===c.format)return`String tidak sah: mesti sepadan dengan corak ${c.pattern}`;return`${b[c.format]??c.format} tidak sah`;case"not_multiple_of":return`Nombor tidak sah: perlu gandaan ${c.divisor}`;case"unrecognized_keys":return`Kunci tidak dikenali: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Kunci tidak sah dalam ${c.origin}`;case"invalid_union":default:return"Input tidak sah";case"invalid_element":return`Nilai tidak sah dalam ${c.origin}`}}})()}}function cD(){return{localeError:(()=>{let a={string:{unit:"tekens"},file:{unit:"bytes"},array:{unit:"elementen"},set:{unit:"elementen"}},b={regex:"invoer",email:"emailadres",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum en tijd",date:"ISO datum",time:"ISO tijd",duration:"ISO duur",ipv4:"IPv4-adres",ipv6:"IPv6-adres",cidrv4:"IPv4-bereik",cidrv6:"IPv6-bereik",base64:"base64-gecodeerde tekst",base64url:"base64 URL-gecodeerde tekst",json_string:"JSON string",e164:"E.164-nummer",jwt:"JWT",template_literal:"invoer"};return c=>{switch(c.code){case"invalid_type":return`Ongeldige invoer: verwacht ${c.expected}, ontving ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"getal";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ongeldige invoer: verwacht ${am.stringifyPrimitive(c.values[0])}`;return`Ongeldige optie: verwacht \xe9\xe9n van ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Te lang: verwacht dat ${c.origin??"waarde"} ${b}${c.maximum.toString()} ${d.unit??"elementen"} bevat`;return`Te lang: verwacht dat ${c.origin??"waarde"} ${b}${c.maximum.toString()} is`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Te kort: verwacht dat ${c.origin} ${b}${c.minimum.toString()} ${d.unit} bevat`;return`Te kort: verwacht dat ${c.origin} ${b}${c.minimum.toString()} is`}case"invalid_format":if("starts_with"===c.format)return`Ongeldige tekst: moet met "${c.prefix}" beginnen`;if("ends_with"===c.format)return`Ongeldige tekst: moet op "${c.suffix}" eindigen`;if("includes"===c.format)return`Ongeldige tekst: moet "${c.includes}" bevatten`;if("regex"===c.format)return`Ongeldige tekst: moet overeenkomen met patroon ${c.pattern}`;return`Ongeldig: ${b[c.format]??c.format}`;case"not_multiple_of":return`Ongeldig getal: moet een veelvoud van ${c.divisor} zijn`;case"unrecognized_keys":return`Onbekende key${c.keys.length>1?"s":""}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Ongeldige key in ${c.origin}`;case"invalid_union":default:return"Ongeldige invoer";case"invalid_element":return`Ongeldige waarde in ${c.origin}`}}})()}}function cE(){return{localeError:(()=>{let a={string:{unit:"tegn",verb:"å ha"},file:{unit:"bytes",verb:"å ha"},array:{unit:"elementer",verb:"å inneholde"},set:{unit:"elementer",verb:"å inneholde"}},b={regex:"input",email:"e-postadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslett",date:"ISO-dato",time:"ISO-klokkeslett",duration:"ISO-varighet",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spekter",cidrv6:"IPv6-spekter",base64:"base64-enkodet streng",base64url:"base64url-enkodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Ugyldig input: forventet ${c.expected}, fikk ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"tall";case"object":if(Array.isArray(a))return"liste";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ugyldig verdi: forventet ${am.stringifyPrimitive(c.values[0])}`;return`Ugyldig valg: forventet en av ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`For stor(t): forventet ${c.origin??"value"} til \xe5 ha ${b}${c.maximum.toString()} ${d.unit??"elementer"}`;return`For stor(t): forventet ${c.origin??"value"} til \xe5 ha ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`For lite(n): forventet ${c.origin} til \xe5 ha ${b}${c.minimum.toString()} ${d.unit}`;return`For lite(n): forventet ${c.origin} til \xe5 ha ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Ugyldig streng: m\xe5 starte med "${c.prefix}"`;if("ends_with"===c.format)return`Ugyldig streng: m\xe5 ende med "${c.suffix}"`;if("includes"===c.format)return`Ugyldig streng: m\xe5 inneholde "${c.includes}"`;if("regex"===c.format)return`Ugyldig streng: m\xe5 matche m\xf8nsteret ${c.pattern}`;return`Ugyldig ${b[c.format]??c.format}`;case"not_multiple_of":return`Ugyldig tall: m\xe5 v\xe6re et multiplum av ${c.divisor}`;case"unrecognized_keys":return`${c.keys.length>1?"Ukjente nøkler":"Ukjent nøkkel"}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Ugyldig n\xf8kkel i ${c.origin}`;case"invalid_union":default:return"Ugyldig input";case"invalid_element":return`Ugyldig verdi i ${c.origin}`}}})()}}function cF(){return{localeError:(()=>{let a={string:{unit:"harf",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"unsur",verb:"olmalıdır"},set:{unit:"unsur",verb:"olmalıdır"}},b={regex:"giren",email:"epostagâh",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO hengâmı",date:"ISO tarihi",time:"ISO zamanı",duration:"ISO müddeti",ipv4:"IPv4 nişânı",ipv6:"IPv6 nişânı",cidrv4:"IPv4 menzili",cidrv6:"IPv6 menzili",base64:"base64-şifreli metin",base64url:"base64url-şifreli metin",json_string:"JSON metin",e164:"E.164 sayısı",jwt:"JWT",template_literal:"giren"};return c=>{switch(c.code){case"invalid_type":return`F\xe2sit giren: umulan ${c.expected}, alınan ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"numara";case"object":if(Array.isArray(a))return"saf";if(null===a)return"gayb";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`F\xe2sit giren: umulan ${am.stringifyPrimitive(c.values[0])}`;return`F\xe2sit tercih: m\xfbteberler ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Fazla b\xfcy\xfck: ${c.origin??"value"}, ${b}${c.maximum.toString()} ${d.unit??"elements"} sahip olmalıydı.`;return`Fazla b\xfcy\xfck: ${c.origin??"value"}, ${b}${c.maximum.toString()} olmalıydı.`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Fazla k\xfc\xe7\xfck: ${c.origin}, ${b}${c.minimum.toString()} ${d.unit} sahip olmalıydı.`;return`Fazla k\xfc\xe7\xfck: ${c.origin}, ${b}${c.minimum.toString()} olmalıydı.`}case"invalid_format":if("starts_with"===c.format)return`F\xe2sit metin: "${c.prefix}" ile başlamalı.`;if("ends_with"===c.format)return`F\xe2sit metin: "${c.suffix}" ile bitmeli.`;if("includes"===c.format)return`F\xe2sit metin: "${c.includes}" ihtiv\xe2 etmeli.`;if("regex"===c.format)return`F\xe2sit metin: ${c.pattern} nakşına uymalı.`;return`F\xe2sit ${b[c.format]??c.format}`;case"not_multiple_of":return`F\xe2sit sayı: ${c.divisor} katı olmalıydı.`;case"unrecognized_keys":return`Tanınmayan anahtar ${c.keys.length>1?"s":""}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`${c.origin} i\xe7in tanınmayan anahtar var.`;case"invalid_union":return"Giren tanınamadı.";case"invalid_element":return`${c.origin} i\xe7in tanınmayan kıymet var.`;default:return`Kıymet tanınamadı.`}}})()}}function cG(){return{localeError:(()=>{let a={string:{unit:"توکي",verb:"ولري"},file:{unit:"بایټس",verb:"ولري"},array:{unit:"توکي",verb:"ولري"},set:{unit:"توکي",verb:"ولري"}},b={regex:"ورودي",email:"بریښنالیک",url:"یو آر ال",emoji:"ایموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"نیټه او وخت",date:"نېټه",time:"وخت",duration:"موده",ipv4:"د IPv4 پته",ipv6:"د IPv6 پته",cidrv4:"د IPv4 ساحه",cidrv6:"د IPv6 ساحه",base64:"base64-encoded متن",base64url:"base64url-encoded متن",json_string:"JSON متن",e164:"د E.164 شمېره",jwt:"JWT",template_literal:"ورودي"};return c=>{switch(c.code){case"invalid_type":return`ناسم ورودي: باید ${c.expected} وای, مګر ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"عدد";case"object":if(Array.isArray(a))return"ارې";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} ترلاسه شو`;case"invalid_value":if(1===c.values.length)return`ناسم ورودي: باید ${am.stringifyPrimitive(c.values[0])} وای`;return`ناسم انتخاب: باید یو له ${am.joinValues(c.values,"|")} څخه وای`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`ډیر لوی: ${c.origin??"ارزښت"} باید ${b}${c.maximum.toString()} ${d.unit??"عنصرونه"} ولري`;return`ډیر لوی: ${c.origin??"ارزښت"} باید ${b}${c.maximum.toString()} وي`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`ډیر کوچنی: ${c.origin} باید ${b}${c.minimum.toString()} ${d.unit} ولري`;return`ډیر کوچنی: ${c.origin} باید ${b}${c.minimum.toString()} وي`}case"invalid_format":if("starts_with"===c.format)return`ناسم متن: باید د "${c.prefix}" سره پیل شي`;if("ends_with"===c.format)return`ناسم متن: باید د "${c.suffix}" سره پای ته ورسيږي`;if("includes"===c.format)return`ناسم متن: باید "${c.includes}" ولري`;if("regex"===c.format)return`ناسم متن: باید د ${c.pattern} سره مطابقت ولري`;return`${b[c.format]??c.format} ناسم دی`;case"not_multiple_of":return`ناسم عدد: باید د ${c.divisor} مضرب وي`;case"unrecognized_keys":return`ناسم ${c.keys.length>1?"کلیډونه":"کلیډ"}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`ناسم کلیډ په ${c.origin} کې`;case"invalid_union":default:return`ناسمه ورودي`;case"invalid_element":return`ناسم عنصر په ${c.origin} کې`}}})()}}function cH(){return{localeError:(()=>{let a={string:{unit:"znaków",verb:"mieć"},file:{unit:"bajtów",verb:"mieć"},array:{unit:"elementów",verb:"mieć"},set:{unit:"elementów",verb:"mieć"}},b={regex:"wyrażenie",email:"adres email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i godzina w formacie ISO",date:"data w formacie ISO",time:"godzina w formacie ISO",duration:"czas trwania ISO",ipv4:"adres IPv4",ipv6:"adres IPv6",cidrv4:"zakres IPv4",cidrv6:"zakres IPv6",base64:"ciąg znaków zakodowany w formacie base64",base64url:"ciąg znaków zakodowany w formacie base64url",json_string:"ciąg znaków w formacie JSON",e164:"liczba E.164",jwt:"JWT",template_literal:"wejście"};return c=>{switch(c.code){case"invalid_type":return`Nieprawidłowe dane wejściowe: oczekiwano ${c.expected}, otrzymano ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"liczba";case"object":if(Array.isArray(a))return"tablica";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Nieprawidłowe dane wejściowe: oczekiwano ${am.stringifyPrimitive(c.values[0])}`;return`Nieprawidłowa opcja: oczekiwano jednej z wartości ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Za duża wartość: oczekiwano, że ${c.origin??"wartość"} będzie mieć ${b}${c.maximum.toString()} ${d.unit??"elementów"}`;return`Zbyt duż(y/a/e): oczekiwano, że ${c.origin??"wartość"} będzie wynosić ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Za mała wartość: oczekiwano, że ${c.origin??"wartość"} będzie mieć ${b}${c.minimum.toString()} ${d.unit??"elementów"}`;return`Zbyt mał(y/a/e): oczekiwano, że ${c.origin??"wartość"} będzie wynosić ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Nieprawidłowy ciąg znak\xf3w: musi zaczynać się od "${c.prefix}"`;if("ends_with"===c.format)return`Nieprawidłowy ciąg znak\xf3w: musi kończyć się na "${c.suffix}"`;if("includes"===c.format)return`Nieprawidłowy ciąg znak\xf3w: musi zawierać "${c.includes}"`;if("regex"===c.format)return`Nieprawidłowy ciąg znak\xf3w: musi odpowiadać wzorcowi ${c.pattern}`;return`Nieprawidłow(y/a/e) ${b[c.format]??c.format}`;case"not_multiple_of":return`Nieprawidłowa liczba: musi być wielokrotnością ${c.divisor}`;case"unrecognized_keys":return`Nierozpoznane klucze${c.keys.length>1?"s":""}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Nieprawidłowy klucz w ${c.origin}`;case"invalid_union":return"Nieprawidłowe dane wejściowe";case"invalid_element":return`Nieprawidłowa wartość w ${c.origin}`;default:return`Nieprawidłowe dane wejściowe`}}})()}}function cI(){return{localeError:(()=>{let a={string:{unit:"caracteres",verb:"ter"},file:{unit:"bytes",verb:"ter"},array:{unit:"itens",verb:"ter"},set:{unit:"itens",verb:"ter"}},b={regex:"padrão",email:"endereço de e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e hora ISO",date:"data ISO",time:"hora ISO",duration:"duração ISO",ipv4:"endereço IPv4",ipv6:"endereço IPv6",cidrv4:"faixa de IPv4",cidrv6:"faixa de IPv6",base64:"texto codificado em base64",base64url:"URL codificada em base64",json_string:"texto JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return c=>{switch(c.code){case"invalid_type":return`Tipo inv\xe1lido: esperado ${c.expected}, recebido ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"número";case"object":if(Array.isArray(a))return"array";if(null===a)return"nulo";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Entrada inv\xe1lida: esperado ${am.stringifyPrimitive(c.values[0])}`;return`Op\xe7\xe3o inv\xe1lida: esperada uma das ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Muito grande: esperado que ${c.origin??"valor"} tivesse ${b}${c.maximum.toString()} ${d.unit??"elementos"}`;return`Muito grande: esperado que ${c.origin??"valor"} fosse ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Muito pequeno: esperado que ${c.origin} tivesse ${b}${c.minimum.toString()} ${d.unit}`;return`Muito pequeno: esperado que ${c.origin} fosse ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Texto inv\xe1lido: deve come\xe7ar com "${c.prefix}"`;if("ends_with"===c.format)return`Texto inv\xe1lido: deve terminar com "${c.suffix}"`;if("includes"===c.format)return`Texto inv\xe1lido: deve incluir "${c.includes}"`;if("regex"===c.format)return`Texto inv\xe1lido: deve corresponder ao padr\xe3o ${c.pattern}`;return`${b[c.format]??c.format} inv\xe1lido`;case"not_multiple_of":return`N\xfamero inv\xe1lido: deve ser m\xfaltiplo de ${c.divisor}`;case"unrecognized_keys":return`Chave${c.keys.length>1?"s":""} desconhecida${c.keys.length>1?"s":""}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Chave inv\xe1lida em ${c.origin}`;case"invalid_union":return"Entrada inválida";case"invalid_element":return`Valor inv\xe1lido em ${c.origin}`;default:return`Campo inv\xe1lido`}}})()}}function cJ(a,b,c,d){let e=Math.abs(a),f=e%10,g=e%100;return g>=11&&g<=19?d:1===f?b:f>=2&&f<=4?c:d}function cK(){return{localeError:(()=>{let a={string:{unit:{one:"символ",few:"символа",many:"символов"},verb:"иметь"},file:{unit:{one:"байт",few:"байта",many:"байт"},verb:"иметь"},array:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"},set:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"}},b={regex:"ввод",email:"email адрес",url:"URL",emoji:"эмодзи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата и время",date:"ISO дата",time:"ISO время",duration:"ISO длительность",ipv4:"IPv4 адрес",ipv6:"IPv6 адрес",cidrv4:"IPv4 диапазон",cidrv6:"IPv6 диапазон",base64:"строка в формате base64",base64url:"строка в формате base64url",json_string:"JSON строка",e164:"номер E.164",jwt:"JWT",template_literal:"ввод"};return c=>{switch(c.code){case"invalid_type":return`Неверный ввод: ожидалось ${c.expected}, получено ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"число";case"object":if(Array.isArray(a))return"массив";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Неверный ввод: ожидалось ${am.stringifyPrimitive(c.values[0])}`;return`Неверный вариант: ожидалось одно из ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d){let a=cJ(Number(c.maximum),d.unit.one,d.unit.few,d.unit.many);return`Слишком большое значение: ожидалось, что ${c.origin??"значение"} будет иметь ${b}${c.maximum.toString()} ${a}`}return`Слишком большое значение: ожидалось, что ${c.origin??"значение"} будет ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d){let a=cJ(Number(c.minimum),d.unit.one,d.unit.few,d.unit.many);return`Слишком маленькое значение: ожидалось, что ${c.origin} будет иметь ${b}${c.minimum.toString()} ${a}`}return`Слишком маленькое значение: ожидалось, что ${c.origin} будет ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Неверная строка: должна начинаться с "${c.prefix}"`;if("ends_with"===c.format)return`Неверная строка: должна заканчиваться на "${c.suffix}"`;if("includes"===c.format)return`Неверная строка: должна содержать "${c.includes}"`;if("regex"===c.format)return`Неверная строка: должна соответствовать шаблону ${c.pattern}`;return`Неверный ${b[c.format]??c.format}`;case"not_multiple_of":return`Неверное число: должно быть кратным ${c.divisor}`;case"unrecognized_keys":return`Нераспознанн${c.keys.length>1?"ые":"ый"} ключ${c.keys.length>1?"и":""}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Неверный ключ в ${c.origin}`;case"invalid_union":return"Неверные входные данные";case"invalid_element":return`Неверное значение в ${c.origin}`;default:return`Неверные входные данные`}}})()}}function cL(){return{localeError:(()=>{let a={string:{unit:"znakov",verb:"imeti"},file:{unit:"bajtov",verb:"imeti"},array:{unit:"elementov",verb:"imeti"},set:{unit:"elementov",verb:"imeti"}},b={regex:"vnos",email:"e-poštni naslov",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum in čas",date:"ISO datum",time:"ISO čas",duration:"ISO trajanje",ipv4:"IPv4 naslov",ipv6:"IPv6 naslov",cidrv4:"obseg IPv4",cidrv6:"obseg IPv6",base64:"base64 kodiran niz",base64url:"base64url kodiran niz",json_string:"JSON niz",e164:"E.164 številka",jwt:"JWT",template_literal:"vnos"};return c=>{switch(c.code){case"invalid_type":return`Neveljaven vnos: pričakovano ${c.expected}, prejeto ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"število";case"object":if(Array.isArray(a))return"tabela";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Neveljaven vnos: pričakovano ${am.stringifyPrimitive(c.values[0])}`;return`Neveljavna možnost: pričakovano eno izmed ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Preveliko: pričakovano, da bo ${c.origin??"vrednost"} imelo ${b}${c.maximum.toString()} ${d.unit??"elementov"}`;return`Preveliko: pričakovano, da bo ${c.origin??"vrednost"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Premajhno: pričakovano, da bo ${c.origin} imelo ${b}${c.minimum.toString()} ${d.unit}`;return`Premajhno: pričakovano, da bo ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Neveljaven niz: mora se začeti z "${c.prefix}"`;if("ends_with"===c.format)return`Neveljaven niz: mora se končati z "${c.suffix}"`;if("includes"===c.format)return`Neveljaven niz: mora vsebovati "${c.includes}"`;if("regex"===c.format)return`Neveljaven niz: mora ustrezati vzorcu ${c.pattern}`;return`Neveljaven ${b[c.format]??c.format}`;case"not_multiple_of":return`Neveljavno število: mora biti večkratnik ${c.divisor}`;case"unrecognized_keys":return`Neprepoznan${c.keys.length>1?"i ključi":" ključ"}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Neveljaven ključ v ${c.origin}`;case"invalid_union":default:return"Neveljaven vnos";case"invalid_element":return`Neveljavna vrednost v ${c.origin}`}}})()}}function cM(){return{localeError:(()=>{let a={string:{unit:"tecken",verb:"att ha"},file:{unit:"bytes",verb:"att ha"},array:{unit:"objekt",verb:"att innehålla"},set:{unit:"objekt",verb:"att innehålla"}},b={regex:"reguljärt uttryck",email:"e-postadress",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datum och tid",date:"ISO-datum",time:"ISO-tid",duration:"ISO-varaktighet",ipv4:"IPv4-intervall",ipv6:"IPv6-intervall",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodad sträng",base64url:"base64url-kodad sträng",json_string:"JSON-sträng",e164:"E.164-nummer",jwt:"JWT",template_literal:"mall-literal"};return c=>{switch(c.code){case"invalid_type":return`Ogiltig inmatning: f\xf6rv\xe4ntat ${c.expected}, fick ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"antal";case"object":if(Array.isArray(a))return"lista";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ogiltig inmatning: f\xf6rv\xe4ntat ${am.stringifyPrimitive(c.values[0])}`;return`Ogiltigt val: f\xf6rv\xe4ntade en av ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`F\xf6r stor(t): f\xf6rv\xe4ntade ${c.origin??"värdet"} att ha ${b}${c.maximum.toString()} ${d.unit??"element"}`;return`F\xf6r stor(t): f\xf6rv\xe4ntat ${c.origin??"värdet"} att ha ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`F\xf6r lite(t): f\xf6rv\xe4ntade ${c.origin??"värdet"} att ha ${b}${c.minimum.toString()} ${d.unit}`;return`F\xf6r lite(t): f\xf6rv\xe4ntade ${c.origin??"värdet"} att ha ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Ogiltig str\xe4ng: m\xe5ste b\xf6rja med "${c.prefix}"`;if("ends_with"===c.format)return`Ogiltig str\xe4ng: m\xe5ste sluta med "${c.suffix}"`;if("includes"===c.format)return`Ogiltig str\xe4ng: m\xe5ste inneh\xe5lla "${c.includes}"`;if("regex"===c.format)return`Ogiltig str\xe4ng: m\xe5ste matcha m\xf6nstret "${c.pattern}"`;return`Ogiltig(t) ${b[c.format]??c.format}`;case"not_multiple_of":return`Ogiltigt tal: m\xe5ste vara en multipel av ${c.divisor}`;case"unrecognized_keys":return`${c.keys.length>1?"Okända nycklar":"Okänd nyckel"}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Ogiltig nyckel i ${c.origin??"värdet"}`;case"invalid_union":default:return"Ogiltig input";case"invalid_element":return`Ogiltigt v\xe4rde i ${c.origin??"värdet"}`}}})()}}function cN(){return{localeError:(()=>{let a={string:{unit:"எழுத்துக்கள்",verb:"கொண்டிருக்க வேண்டும்"},file:{unit:"பைட்டுகள்",verb:"கொண்டிருக்க வேண்டும்"},array:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"},set:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"}},b={regex:"உள்ளீடு",email:"மின்னஞ்சல் முகவரி",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO தேதி நேரம்",date:"ISO தேதி",time:"ISO நேரம்",duration:"ISO கால அளவு",ipv4:"IPv4 முகவரி",ipv6:"IPv6 முகவரி",cidrv4:"IPv4 வரம்பு",cidrv6:"IPv6 வரம்பு",base64:"base64-encoded சரம்",base64url:"base64url-encoded சரம்",json_string:"JSON சரம்",e164:"E.164 எண்",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ${c.expected}, பெறப்பட்டது ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"எண் அல்லாதது":"எண்";case"object":if(Array.isArray(a))return"அணி";if(null===a)return"வெறுமை";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ${am.stringifyPrimitive(c.values[0])}`;return`தவறான விருப்பம்: எதிர்பார்க்கப்பட்டது ${am.joinValues(c.values,"|")} இல் ஒன்று`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`மிக பெரியது: எதிர்பார்க்கப்பட்டது ${c.origin??"மதிப்பு"} ${b}${c.maximum.toString()} ${d.unit??"உறுப்புகள்"} ஆக இருக்க வேண்டும்`;return`மிக பெரியது: எதிர்பார்க்கப்பட்டது ${c.origin??"மதிப்பு"} ${b}${c.maximum.toString()} ஆக இருக்க வேண்டும்`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ${c.origin} ${b}${c.minimum.toString()} ${d.unit} ஆக இருக்க வேண்டும்`;return`மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ${c.origin} ${b}${c.minimum.toString()} ஆக இருக்க வேண்டும்`}case"invalid_format":if("starts_with"===c.format)return`தவறான சரம்: "${c.prefix}" இல் தொடங்க வேண்டும்`;if("ends_with"===c.format)return`தவறான சரம்: "${c.suffix}" இல் முடிவடைய வேண்டும்`;if("includes"===c.format)return`தவறான சரம்: "${c.includes}" ஐ உள்ளடக்க வேண்டும்`;if("regex"===c.format)return`தவறான சரம்: ${c.pattern} முறைபாட்டுடன் பொருந்த வேண்டும்`;return`தவறான ${b[c.format]??c.format}`;case"not_multiple_of":return`தவறான எண்: ${c.divisor} இன் பலமாக இருக்க வேண்டும்`;case"unrecognized_keys":return`அடையாளம் தெரியாத விசை${c.keys.length>1?"கள்":""}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`${c.origin} இல் தவறான விசை`;case"invalid_union":return"தவறான உள்ளீடு";case"invalid_element":return`${c.origin} இல் தவறான மதிப்பு`;default:return`தவறான உள்ளீடு`}}})()}}function cO(){return{localeError:(()=>{let a={string:{unit:"ตัวอักษร",verb:"ควรมี"},file:{unit:"ไบต์",verb:"ควรมี"},array:{unit:"รายการ",verb:"ควรมี"},set:{unit:"รายการ",verb:"ควรมี"}},b={regex:"ข้อมูลที่ป้อน",email:"ที่อยู่อีเมล",url:"URL",emoji:"อิโมจิ",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"วันที่เวลาแบบ ISO",date:"วันที่แบบ ISO",time:"เวลาแบบ ISO",duration:"ช่วงเวลาแบบ ISO",ipv4:"ที่อยู่ IPv4",ipv6:"ที่อยู่ IPv6",cidrv4:"ช่วง IP แบบ IPv4",cidrv6:"ช่วง IP แบบ IPv6",base64:"ข้อความแบบ Base64",base64url:"ข้อความแบบ Base64 สำหรับ URL",json_string:"ข้อความแบบ JSON",e164:"เบอร์โทรศัพท์ระหว่างประเทศ (E.164)",jwt:"โทเคน JWT",template_literal:"ข้อมูลที่ป้อน"};return c=>{switch(c.code){case"invalid_type":return`ประเภทข้อมูลไม่ถูกต้อง: ควรเป็น ${c.expected} แต่ได้รับ ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"ไม่ใช่ตัวเลข (NaN)":"ตัวเลข";case"object":if(Array.isArray(a))return"อาร์เรย์ (Array)";if(null===a)return"ไม่มีค่า (null)";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`ค่าไม่ถูกต้อง: ควรเป็น ${am.stringifyPrimitive(c.values[0])}`;return`ตัวเลือกไม่ถูกต้อง: ควรเป็นหนึ่งใน ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"ไม่เกิน":"น้อยกว่า",d=a[c.origin]??null;if(d)return`เกินกำหนด: ${c.origin??"ค่า"} ควรมี${b} ${c.maximum.toString()} ${d.unit??"รายการ"}`;return`เกินกำหนด: ${c.origin??"ค่า"} ควรมี${b} ${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?"อย่างน้อย":"มากกว่า",d=a[c.origin]??null;if(d)return`น้อยกว่ากำหนด: ${c.origin} ควรมี${b} ${c.minimum.toString()} ${d.unit}`;return`น้อยกว่ากำหนด: ${c.origin} ควรมี${b} ${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`รูปแบบไม่ถูกต้อง: ข้อความต้องขึ้นต้นด้วย "${c.prefix}"`;if("ends_with"===c.format)return`รูปแบบไม่ถูกต้อง: ข้อความต้องลงท้ายด้วย "${c.suffix}"`;if("includes"===c.format)return`รูปแบบไม่ถูกต้อง: ข้อความต้องมี "${c.includes}" อยู่ในข้อความ`;if("regex"===c.format)return`รูปแบบไม่ถูกต้อง: ต้องตรงกับรูปแบบที่กำหนด ${c.pattern}`;return`รูปแบบไม่ถูกต้อง: ${b[c.format]??c.format}`;case"not_multiple_of":return`ตัวเลขไม่ถูกต้อง: ต้องเป็นจำนวนที่หารด้วย ${c.divisor} ได้ลงตัว`;case"unrecognized_keys":return`พบคีย์ที่ไม่รู้จัก: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`คีย์ไม่ถูกต้องใน ${c.origin}`;case"invalid_union":return"ข้อมูลไม่ถูกต้อง: ไม่ตรงกับรูปแบบยูเนียนที่กำหนดไว้";case"invalid_element":return`ข้อมูลไม่ถูกต้องใน ${c.origin}`;default:return`ข้อมูลไม่ถูกต้อง`}}})()}}function cP(){return{localeError:(()=>{let a={string:{unit:"karakter",verb:"olmalı"},file:{unit:"bayt",verb:"olmalı"},array:{unit:"öğe",verb:"olmalı"},set:{unit:"öğe",verb:"olmalı"}},b={regex:"girdi",email:"e-posta adresi",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO tarih ve saat",date:"ISO tarih",time:"ISO saat",duration:"ISO süre",ipv4:"IPv4 adresi",ipv6:"IPv6 adresi",cidrv4:"IPv4 aralığı",cidrv6:"IPv6 aralığı",base64:"base64 ile şifrelenmiş metin",base64url:"base64url ile şifrelenmiş metin",json_string:"JSON dizesi",e164:"E.164 sayısı",jwt:"JWT",template_literal:"Şablon dizesi"};return c=>{switch(c.code){case"invalid_type":return`Ge\xe7ersiz değer: beklenen ${c.expected}, alınan ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ge\xe7ersiz değer: beklenen ${am.stringifyPrimitive(c.values[0])}`;return`Ge\xe7ersiz se\xe7enek: aşağıdakilerden biri olmalı: ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`\xc7ok b\xfcy\xfck: beklenen ${c.origin??"değer"} ${b}${c.maximum.toString()} ${d.unit??"öğe"}`;return`\xc7ok b\xfcy\xfck: beklenen ${c.origin??"değer"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`\xc7ok k\xfc\xe7\xfck: beklenen ${c.origin} ${b}${c.minimum.toString()} ${d.unit}`;return`\xc7ok k\xfc\xe7\xfck: beklenen ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Ge\xe7ersiz metin: "${c.prefix}" ile başlamalı`;if("ends_with"===c.format)return`Ge\xe7ersiz metin: "${c.suffix}" ile bitmeli`;if("includes"===c.format)return`Ge\xe7ersiz metin: "${c.includes}" i\xe7ermeli`;if("regex"===c.format)return`Ge\xe7ersiz metin: ${c.pattern} desenine uymalı`;return`Ge\xe7ersiz ${b[c.format]??c.format}`;case"not_multiple_of":return`Ge\xe7ersiz sayı: ${c.divisor} ile tam b\xf6l\xfcnebilmeli`;case"unrecognized_keys":return`Tanınmayan anahtar${c.keys.length>1?"lar":""}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`${c.origin} i\xe7inde ge\xe7ersiz anahtar`;case"invalid_union":return"Geçersiz değer";case"invalid_element":return`${c.origin} i\xe7inde ge\xe7ersiz değer`;default:return`Ge\xe7ersiz değer`}}})()}}function cQ(){return{localeError:(()=>{let a={string:{unit:"символів",verb:"матиме"},file:{unit:"байтів",verb:"матиме"},array:{unit:"елементів",verb:"матиме"},set:{unit:"елементів",verb:"матиме"}},b={regex:"вхідні дані",email:"адреса електронної пошти",url:"URL",emoji:"емодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"дата та час ISO",date:"дата ISO",time:"час ISO",duration:"тривалість ISO",ipv4:"адреса IPv4",ipv6:"адреса IPv6",cidrv4:"діапазон IPv4",cidrv6:"діапазон IPv6",base64:"рядок у кодуванні base64",base64url:"рядок у кодуванні base64url",json_string:"рядок JSON",e164:"номер E.164",jwt:"JWT",template_literal:"вхідні дані"};return c=>{switch(c.code){case"invalid_type":return`Неправильні вхідні дані: очікується ${c.expected}, отримано ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"число";case"object":if(Array.isArray(a))return"масив";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Неправильні вхідні дані: очікується ${am.stringifyPrimitive(c.values[0])}`;return`Неправильна опція: очікується одне з ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Занадто велике: очікується, що ${c.origin??"значення"} ${d.verb} ${b}${c.maximum.toString()} ${d.unit??"елементів"}`;return`Занадто велике: очікується, що ${c.origin??"значення"} буде ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Занадто мале: очікується, що ${c.origin} ${d.verb} ${b}${c.minimum.toString()} ${d.unit}`;return`Занадто мале: очікується, що ${c.origin} буде ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Неправильний рядок: повинен починатися з "${c.prefix}"`;if("ends_with"===c.format)return`Неправильний рядок: повинен закінчуватися на "${c.suffix}"`;if("includes"===c.format)return`Неправильний рядок: повинен містити "${c.includes}"`;if("regex"===c.format)return`Неправильний рядок: повинен відповідати шаблону ${c.pattern}`;return`Неправильний ${b[c.format]??c.format}`;case"not_multiple_of":return`Неправильне число: повинно бути кратним ${c.divisor}`;case"unrecognized_keys":return`Нерозпізнаний ключ${c.keys.length>1?"і":""}: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Неправильний ключ у ${c.origin}`;case"invalid_union":return"Неправильні вхідні дані";case"invalid_element":return`Неправильне значення у ${c.origin}`;default:return`Неправильні вхідні дані`}}})()}}function cR(){return{localeError:(()=>{let a={string:{unit:"حروف",verb:"ہونا"},file:{unit:"بائٹس",verb:"ہونا"},array:{unit:"آئٹمز",verb:"ہونا"},set:{unit:"آئٹمز",verb:"ہونا"}},b={regex:"ان پٹ",email:"ای میل ایڈریس",url:"یو آر ایل",emoji:"ایموجی",uuid:"یو یو آئی ڈی",uuidv4:"یو یو آئی ڈی وی 4",uuidv6:"یو یو آئی ڈی وی 6",nanoid:"نینو آئی ڈی",guid:"جی یو آئی ڈی",cuid:"سی یو آئی ڈی",cuid2:"سی یو آئی ڈی 2",ulid:"یو ایل آئی ڈی",xid:"ایکس آئی ڈی",ksuid:"کے ایس یو آئی ڈی",datetime:"آئی ایس او ڈیٹ ٹائم",date:"آئی ایس او تاریخ",time:"آئی ایس او وقت",duration:"آئی ایس او مدت",ipv4:"آئی پی وی 4 ایڈریس",ipv6:"آئی پی وی 6 ایڈریس",cidrv4:"آئی پی وی 4 رینج",cidrv6:"آئی پی وی 6 رینج",base64:"بیس 64 ان کوڈڈ سٹرنگ",base64url:"بیس 64 یو آر ایل ان کوڈڈ سٹرنگ",json_string:"جے ایس او این سٹرنگ",e164:"ای 164 نمبر",jwt:"جے ڈبلیو ٹی",template_literal:"ان پٹ"};return c=>{switch(c.code){case"invalid_type":return`غلط ان پٹ: ${c.expected} متوقع تھا، ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"نمبر";case"object":if(Array.isArray(a))return"آرے";if(null===a)return"نل";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} موصول ہوا`;case"invalid_value":if(1===c.values.length)return`غلط ان پٹ: ${am.stringifyPrimitive(c.values[0])} متوقع تھا`;return`غلط آپشن: ${am.joinValues(c.values,"|")} میں سے ایک متوقع تھا`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`بہت بڑا: ${c.origin??"ویلیو"} کے ${b}${c.maximum.toString()} ${d.unit??"عناصر"} ہونے متوقع تھے`;return`بہت بڑا: ${c.origin??"ویلیو"} کا ${b}${c.maximum.toString()} ہونا متوقع تھا`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`بہت چھوٹا: ${c.origin} کے ${b}${c.minimum.toString()} ${d.unit} ہونے متوقع تھے`;return`بہت چھوٹا: ${c.origin} کا ${b}${c.minimum.toString()} ہونا متوقع تھا`}case"invalid_format":if("starts_with"===c.format)return`غلط سٹرنگ: "${c.prefix}" سے شروع ہونا چاہیے`;if("ends_with"===c.format)return`غلط سٹرنگ: "${c.suffix}" پر ختم ہونا چاہیے`;if("includes"===c.format)return`غلط سٹرنگ: "${c.includes}" شامل ہونا چاہیے`;if("regex"===c.format)return`غلط سٹرنگ: پیٹرن ${c.pattern} سے میچ ہونا چاہیے`;return`غلط ${b[c.format]??c.format}`;case"not_multiple_of":return`غلط نمبر: ${c.divisor} کا مضاعف ہونا چاہیے`;case"unrecognized_keys":return`غیر تسلیم شدہ کی${c.keys.length>1?"ز":""}: ${am.joinValues(c.keys,"، ")}`;case"invalid_key":return`${c.origin} میں غلط کی`;case"invalid_union":return"غلط ان پٹ";case"invalid_element":return`${c.origin} میں غلط ویلیو`;default:return`غلط ان پٹ`}}})()}}function cS(){return{localeError:(()=>{let a={string:{unit:"ký tự",verb:"có"},file:{unit:"byte",verb:"có"},array:{unit:"phần tử",verb:"có"},set:{unit:"phần tử",verb:"có"}},b={regex:"đầu vào",email:"địa chỉ email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ngày giờ ISO",date:"ngày ISO",time:"giờ ISO",duration:"khoảng thời gian ISO",ipv4:"địa chỉ IPv4",ipv6:"địa chỉ IPv6",cidrv4:"dải IPv4",cidrv6:"dải IPv6",base64:"chuỗi mã hóa base64",base64url:"chuỗi mã hóa base64url",json_string:"chuỗi JSON",e164:"số E.164",jwt:"JWT",template_literal:"đầu vào"};return c=>{switch(c.code){case"invalid_type":return`Đầu v\xe0o kh\xf4ng hợp lệ: mong đợi ${c.expected}, nhận được ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"số";case"object":if(Array.isArray(a))return"mảng";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Đầu v\xe0o kh\xf4ng hợp lệ: mong đợi ${am.stringifyPrimitive(c.values[0])}`;return`T\xf9y chọn kh\xf4ng hợp lệ: mong đợi một trong c\xe1c gi\xe1 trị ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Qu\xe1 lớn: mong đợi ${c.origin??"giá trị"} ${d.verb} ${b}${c.maximum.toString()} ${d.unit??"phần tử"}`;return`Qu\xe1 lớn: mong đợi ${c.origin??"giá trị"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Qu\xe1 nhỏ: mong đợi ${c.origin} ${d.verb} ${b}${c.minimum.toString()} ${d.unit}`;return`Qu\xe1 nhỏ: mong đợi ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Chuỗi kh\xf4ng hợp lệ: phải bắt đầu bằng "${c.prefix}"`;if("ends_with"===c.format)return`Chuỗi kh\xf4ng hợp lệ: phải kết th\xfac bằng "${c.suffix}"`;if("includes"===c.format)return`Chuỗi kh\xf4ng hợp lệ: phải bao gồm "${c.includes}"`;if("regex"===c.format)return`Chuỗi kh\xf4ng hợp lệ: phải khớp với mẫu ${c.pattern}`;return`${b[c.format]??c.format} kh\xf4ng hợp lệ`;case"not_multiple_of":return`Số kh\xf4ng hợp lệ: phải l\xe0 bội số của ${c.divisor}`;case"unrecognized_keys":return`Kh\xf3a kh\xf4ng được nhận dạng: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Kh\xf3a kh\xf4ng hợp lệ trong ${c.origin}`;case"invalid_union":return"Đầu vào không hợp lệ";case"invalid_element":return`Gi\xe1 trị kh\xf4ng hợp lệ trong ${c.origin}`;default:return`Đầu v\xe0o kh\xf4ng hợp lệ`}}})()}}function cT(){return{localeError:(()=>{let a={string:{unit:"字符",verb:"包含"},file:{unit:"字节",verb:"包含"},array:{unit:"项",verb:"包含"},set:{unit:"项",verb:"包含"}},b={regex:"输入",email:"电子邮件",url:"URL",emoji:"表情符号",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日期时间",date:"ISO日期",time:"ISO时间",duration:"ISO时长",ipv4:"IPv4地址",ipv6:"IPv6地址",cidrv4:"IPv4网段",cidrv6:"IPv6网段",base64:"base64编码字符串",base64url:"base64url编码字符串",json_string:"JSON字符串",e164:"E.164号码",jwt:"JWT",template_literal:"输入"};return c=>{switch(c.code){case"invalid_type":return`无效输入：期望 ${c.expected}，实际接收 ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"非数字(NaN)":"数字";case"object":if(Array.isArray(a))return"数组";if(null===a)return"空值(null)";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`无效输入：期望 ${am.stringifyPrimitive(c.values[0])}`;return`无效选项：期望以下之一 ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`数值过大：期望 ${c.origin??"值"} ${b}${c.maximum.toString()} ${d.unit??"个元素"}`;return`数值过大：期望 ${c.origin??"值"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`数值过小：期望 ${c.origin} ${b}${c.minimum.toString()} ${d.unit}`;return`数值过小：期望 ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`无效字符串：必须以 "${c.prefix}" 开头`;if("ends_with"===c.format)return`无效字符串：必须以 "${c.suffix}" 结尾`;if("includes"===c.format)return`无效字符串：必须包含 "${c.includes}"`;if("regex"===c.format)return`无效字符串：必须满足正则表达式 ${c.pattern}`;return`无效${b[c.format]??c.format}`;case"not_multiple_of":return`无效数字：必须是 ${c.divisor} 的倍数`;case"unrecognized_keys":return`出现未知的键(key): ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`${c.origin} 中的键(key)无效`;case"invalid_union":return"无效输入";case"invalid_element":return`${c.origin} 中包含无效值(value)`;default:return`无效输入`}}})()}}function cU(){return{localeError:(()=>{let a={string:{unit:"字元",verb:"擁有"},file:{unit:"位元組",verb:"擁有"},array:{unit:"項目",verb:"擁有"},set:{unit:"項目",verb:"擁有"}},b={regex:"輸入",email:"郵件地址",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 日期時間",date:"ISO 日期",time:"ISO 時間",duration:"ISO 期間",ipv4:"IPv4 位址",ipv6:"IPv6 位址",cidrv4:"IPv4 範圍",cidrv6:"IPv6 範圍",base64:"base64 編碼字串",base64url:"base64url 編碼字串",json_string:"JSON 字串",e164:"E.164 數值",jwt:"JWT",template_literal:"輸入"};return c=>{switch(c.code){case"invalid_type":return`無效的輸入值：預期為 ${c.expected}，但收到 ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`無效的輸入值：預期為 ${am.stringifyPrimitive(c.values[0])}`;return`無效的選項：預期為以下其中之一 ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`數值過大：預期 ${c.origin??"值"} 應為 ${b}${c.maximum.toString()} ${d.unit??"個元素"}`;return`數值過大：預期 ${c.origin??"值"} 應為 ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`數值過小：預期 ${c.origin} 應為 ${b}${c.minimum.toString()} ${d.unit}`;return`數值過小：預期 ${c.origin} 應為 ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`無效的字串：必須以 "${c.prefix}" 開頭`;if("ends_with"===c.format)return`無效的字串：必須以 "${c.suffix}" 結尾`;if("includes"===c.format)return`無效的字串：必須包含 "${c.includes}"`;if("regex"===c.format)return`無效的字串：必須符合格式 ${c.pattern}`;return`無效的 ${b[c.format]??c.format}`;case"not_multiple_of":return`無效的數字：必須為 ${c.divisor} 的倍數`;case"unrecognized_keys":return`無法識別的鍵值${c.keys.length>1?"們":""}：${am.joinValues(c.keys,"、")}`;case"invalid_key":return`${c.origin} 中有無效的鍵值`;case"invalid_union":return"無效的輸入值";case"invalid_element":return`${c.origin} 中有無效的值`;default:return`無效的輸入值`}}})()}}function cV(){return{localeError:(()=>{let a={string:{unit:"àmi",verb:"ní"},file:{unit:"bytes",verb:"ní"},array:{unit:"nkan",verb:"ní"},set:{unit:"nkan",verb:"ní"}},b={regex:"ẹ̀rọ ìbáwọlé",email:"àdírẹ́sì ìmẹ́lì",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"àkókò ISO",date:"ọjọ́ ISO",time:"àkókò ISO",duration:"àkókò tó pé ISO",ipv4:"àdírẹ́sì IPv4",ipv6:"àdírẹ́sì IPv6",cidrv4:"àgbègbè IPv4",cidrv6:"àgbègbè IPv6",base64:"ọ̀rọ̀ tí a kọ́ ní base64",base64url:"ọ̀rọ̀ base64url",json_string:"ọ̀rọ̀ JSON",e164:"nọ́mbà E.164",jwt:"JWT",template_literal:"ẹ̀rọ ìbáwọlé"};return c=>{switch(c.code){case"invalid_type":return`\xccb\xe1wọl\xe9 aṣ\xecṣe: a n\xed l\xe1ti fi ${c.expected}, \xe0mọ̀ a r\xed ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"nọ́mbà";case"object":if(Array.isArray(a))return"akopọ";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`\xccb\xe1wọl\xe9 aṣ\xecṣe: a n\xed l\xe1ti fi ${am.stringifyPrimitive(c.values[0])}`;return`\xc0ṣ\xe0y\xe0n aṣ\xecṣe: yan ọ̀kan l\xe1ra ${am.joinValues(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`T\xf3 pọ̀ j\xf9: a n\xed l\xe1ti jẹ́ p\xe9 ${c.origin??"iye"} ${d.verb} ${b}${c.maximum} ${d.unit}`;return`T\xf3 pọ̀ j\xf9: a n\xed l\xe1ti jẹ́ ${b}${c.maximum}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`K\xe9r\xe9 ju: a n\xed l\xe1ti jẹ́ p\xe9 ${c.origin} ${d.verb} ${b}${c.minimum} ${d.unit}`;return`K\xe9r\xe9 ju: a n\xed l\xe1ti jẹ́ ${b}${c.minimum}`}case"invalid_format":if("starts_with"===c.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ bẹ̀rẹ̀ pẹ̀l\xfa "${c.prefix}"`;if("ends_with"===c.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ par\xed pẹ̀l\xfa "${c.suffix}"`;if("includes"===c.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ n\xed "${c.includes}"`;if("regex"===c.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ b\xe1 \xe0pẹẹrẹ mu ${c.pattern}`;return`Aṣ\xecṣe: ${b[c.format]??c.format}`;case"not_multiple_of":return`Nọ́mb\xe0 aṣ\xecṣe: gbọ́dọ̀ jẹ́ \xe8y\xe0 p\xedp\xedn ti ${c.divisor}`;case"unrecognized_keys":return`Bọt\xecn\xec \xe0\xecmọ̀: ${am.joinValues(c.keys,", ")}`;case"invalid_key":return`Bọt\xecn\xec aṣ\xecṣe n\xedn\xfa ${c.origin}`;case"invalid_union":default:return"Ìbáwọlé aṣìṣe";case"invalid_element":return`Iye aṣ\xecṣe n\xedn\xfa ${c.origin}`}}})()}}var cW=a.i(43153);a.s(["$ZodRegistry",()=>cZ,"$input",()=>cY,"$output",()=>cX,"globalRegistry",()=>c_,"registry",()=>c$],2865);let cX=Symbol("ZodOutput"),cY=Symbol("ZodInput");class cZ{constructor(){this._map=new Map,this._idmap=new Map}add(a,...b){let c=b[0];if(this._map.set(a,c),c&&"object"==typeof c&&"id"in c){if(this._idmap.has(c.id))throw Error(`ID ${c.id} already exists in the registry`);this._idmap.set(c.id,a)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(a){let b=this._map.get(a);return b&&"object"==typeof b&&"id"in b&&this._idmap.delete(b.id),this._map.delete(a),this}get(a){let b=a._zod.parent;if(b){let c={...this.get(b)??{}};delete c.id;let d={...c,...this._map.get(a)};return Object.keys(d).length?d:void 0}return this._map.get(a)}has(a){return this._map.has(a)}}function c$(){return new cZ}let c_=c$();function c0(a,b){return new a({type:"string",...am.normalizeParams(b)})}function c1(a,b){return new a({type:"string",coerce:!0,...am.normalizeParams(b)})}function c2(a,b){return new a({type:"string",format:"email",check:"string_format",abort:!1,...am.normalizeParams(b)})}function c3(a,b){return new a({type:"string",format:"guid",check:"string_format",abort:!1,...am.normalizeParams(b)})}function c4(a,b){return new a({type:"string",format:"uuid",check:"string_format",abort:!1,...am.normalizeParams(b)})}function c5(a,b){return new a({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...am.normalizeParams(b)})}function c6(a,b){return new a({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...am.normalizeParams(b)})}function c7(a,b){return new a({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...am.normalizeParams(b)})}function c8(a,b){return new a({type:"string",format:"url",check:"string_format",abort:!1,...am.normalizeParams(b)})}function c9(a,b){return new a({type:"string",format:"emoji",check:"string_format",abort:!1,...am.normalizeParams(b)})}function da(a,b){return new a({type:"string",format:"nanoid",check:"string_format",abort:!1,...am.normalizeParams(b)})}function db(a,b){return new a({type:"string",format:"cuid",check:"string_format",abort:!1,...am.normalizeParams(b)})}function dc(a,b){return new a({type:"string",format:"cuid2",check:"string_format",abort:!1,...am.normalizeParams(b)})}function dd(a,b){return new a({type:"string",format:"ulid",check:"string_format",abort:!1,...am.normalizeParams(b)})}function de(a,b){return new a({type:"string",format:"xid",check:"string_format",abort:!1,...am.normalizeParams(b)})}function df(a,b){return new a({type:"string",format:"ksuid",check:"string_format",abort:!1,...am.normalizeParams(b)})}function dg(a,b){return new a({type:"string",format:"ipv4",check:"string_format",abort:!1,...am.normalizeParams(b)})}function dh(a,b){return new a({type:"string",format:"ipv6",check:"string_format",abort:!1,...am.normalizeParams(b)})}function di(a,b){return new a({type:"string",format:"cidrv4",check:"string_format",abort:!1,...am.normalizeParams(b)})}function dj(a,b){return new a({type:"string",format:"cidrv6",check:"string_format",abort:!1,...am.normalizeParams(b)})}function dk(a,b){return new a({type:"string",format:"base64",check:"string_format",abort:!1,...am.normalizeParams(b)})}function dl(a,b){return new a({type:"string",format:"base64url",check:"string_format",abort:!1,...am.normalizeParams(b)})}function dm(a,b){return new a({type:"string",format:"e164",check:"string_format",abort:!1,...am.normalizeParams(b)})}function dn(a,b){return new a({type:"string",format:"jwt",check:"string_format",abort:!1,...am.normalizeParams(b)})}a.i(2865),a.i(37144),a.s(["TimePrecision",()=>dp,"_any",()=>dK,"_array",()=>eh,"_base64",()=>dk,"_base64url",()=>dl,"_bigint",()=>dD,"_boolean",()=>dB,"_catch",()=>ez,"_check",()=>eI,"_cidrv4",()=>di,"_cidrv6",()=>dj,"_coercedBigint",()=>dE,"_coercedBoolean",()=>dC,"_coercedDate",()=>dP,"_coercedNumber",()=>dv,"_coercedString",()=>c1,"_cuid",()=>db,"_cuid2",()=>dc,"_custom",()=>eF,"_date",()=>dO,"_default",()=>ew,"_discriminatedUnion",()=>ej,"_e164",()=>dm,"_email",()=>c2,"_emoji",()=>c9,"_endsWith",()=>d9,"_enum",()=>ep,"_file",()=>es,"_float32",()=>dx,"_float64",()=>dy,"_gt",()=>dT,"_gte",()=>dU,"_guid",()=>c3,"_includes",()=>d7,"_int",()=>dw,"_int32",()=>dz,"_int64",()=>dF,"_intersection",()=>ek,"_ipv4",()=>dg,"_ipv6",()=>dh,"_isoDate",()=>dr,"_isoDateTime",()=>dq,"_isoDuration",()=>dt,"_isoTime",()=>ds,"_jwt",()=>dn,"_ksuid",()=>df,"_lazy",()=>eD,"_length",()=>d3,"_literal",()=>er,"_lowercase",()=>d5,"_lt",()=>dR,"_lte",()=>dS,"_map",()=>en,"_max",()=>dS,"_maxLength",()=>d1,"_maxSize",()=>d$,"_mime",()=>eb,"_min",()=>dU,"_minLength",()=>d2,"_minSize",()=>d_,"_multipleOf",()=>dZ,"_nan",()=>dQ,"_nanoid",()=>da,"_nativeEnum",()=>eq,"_negative",()=>dW,"_never",()=>dM,"_nonnegative",()=>dY,"_nonoptional",()=>ex,"_nonpositive",()=>dX,"_normalize",()=>ed,"_null",()=>dJ,"_nullable",()=>ev,"_number",()=>du,"_optional",()=>eu,"_overwrite",()=>ec,"_pipe",()=>eA,"_positive",()=>dV,"_promise",()=>eE,"_property",()=>ea,"_readonly",()=>eB,"_record",()=>em,"_refine",()=>eG,"_regex",()=>d4,"_set",()=>eo,"_size",()=>d0,"_startsWith",()=>d8,"_string",()=>c0,"_stringFormat",()=>eK,"_stringbool",()=>eJ,"_success",()=>ey,"_superRefine",()=>eH,"_symbol",()=>dH,"_templateLiteral",()=>eC,"_toLowerCase",()=>ef,"_toUpperCase",()=>eg,"_transform",()=>et,"_trim",()=>ee,"_tuple",()=>el,"_uint32",()=>dA,"_uint64",()=>dG,"_ulid",()=>dd,"_undefined",()=>dI,"_union",()=>ei,"_unknown",()=>dL,"_uppercase",()=>d6,"_url",()=>c8,"_uuid",()=>c4,"_uuidv4",()=>c5,"_uuidv6",()=>c6,"_uuidv7",()=>c7,"_void",()=>dN,"_xid",()=>de],17649);let dp={Any:null,Minute:-1,Second:0,Millisecond:3,Microsecond:6};function dq(a,b){return new a({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...am.normalizeParams(b)})}function dr(a,b){return new a({type:"string",format:"date",check:"string_format",...am.normalizeParams(b)})}function ds(a,b){return new a({type:"string",format:"time",check:"string_format",precision:null,...am.normalizeParams(b)})}function dt(a,b){return new a({type:"string",format:"duration",check:"string_format",...am.normalizeParams(b)})}function du(a,b){return new a({type:"number",checks:[],...am.normalizeParams(b)})}function dv(a,b){return new a({type:"number",coerce:!0,checks:[],...am.normalizeParams(b)})}function dw(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"safeint",...am.normalizeParams(b)})}function dx(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"float32",...am.normalizeParams(b)})}function dy(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"float64",...am.normalizeParams(b)})}function dz(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"int32",...am.normalizeParams(b)})}function dA(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"uint32",...am.normalizeParams(b)})}function dB(a,b){return new a({type:"boolean",...am.normalizeParams(b)})}function dC(a,b){return new a({type:"boolean",coerce:!0,...am.normalizeParams(b)})}function dD(a,b){return new a({type:"bigint",...am.normalizeParams(b)})}function dE(a,b){return new a({type:"bigint",coerce:!0,...am.normalizeParams(b)})}function dF(a,b){return new a({type:"bigint",check:"bigint_format",abort:!1,format:"int64",...am.normalizeParams(b)})}function dG(a,b){return new a({type:"bigint",check:"bigint_format",abort:!1,format:"uint64",...am.normalizeParams(b)})}function dH(a,b){return new a({type:"symbol",...am.normalizeParams(b)})}function dI(a,b){return new a({type:"undefined",...am.normalizeParams(b)})}function dJ(a,b){return new a({type:"null",...am.normalizeParams(b)})}function dK(a){return new a({type:"any"})}function dL(a){return new a({type:"unknown"})}function dM(a,b){return new a({type:"never",...am.normalizeParams(b)})}function dN(a,b){return new a({type:"void",...am.normalizeParams(b)})}function dO(a,b){return new a({type:"date",...am.normalizeParams(b)})}function dP(a,b){return new a({type:"date",coerce:!0,...am.normalizeParams(b)})}function dQ(a,b){return new a({type:"nan",...am.normalizeParams(b)})}function dR(a,b){return new ap({check:"less_than",...am.normalizeParams(b),value:a,inclusive:!1})}function dS(a,b){return new ap({check:"less_than",...am.normalizeParams(b),value:a,inclusive:!0})}function dT(a,b){return new aq({check:"greater_than",...am.normalizeParams(b),value:a,inclusive:!1})}function dU(a,b){return new aq({check:"greater_than",...am.normalizeParams(b),value:a,inclusive:!0})}function dV(a){return dT(0,a)}function dW(a){return dR(0,a)}function dX(a){return dS(0,a)}function dY(a){return dU(0,a)}function dZ(a,b){return new ar({check:"multiple_of",...am.normalizeParams(b),value:a})}function d$(a,b){return new au({check:"max_size",...am.normalizeParams(b),maximum:a})}function d_(a,b){return new av({check:"min_size",...am.normalizeParams(b),minimum:a})}function d0(a,b){return new aw({check:"size_equals",...am.normalizeParams(b),size:a})}function d1(a,b){return new ax({check:"max_length",...am.normalizeParams(b),maximum:a})}function d2(a,b){return new ay({check:"min_length",...am.normalizeParams(b),minimum:a})}function d3(a,b){return new az({check:"length_equals",...am.normalizeParams(b),length:a})}function d4(a,b){return new aB({check:"string_format",format:"regex",...am.normalizeParams(b),pattern:a})}function d5(a){return new aC({check:"string_format",format:"lowercase",...am.normalizeParams(a)})}function d6(a){return new aD({check:"string_format",format:"uppercase",...am.normalizeParams(a)})}function d7(a,b){return new aE({check:"string_format",format:"includes",...am.normalizeParams(b),includes:a})}function d8(a,b){return new aF({check:"string_format",format:"starts_with",...am.normalizeParams(b),prefix:a})}function d9(a,b){return new aG({check:"string_format",format:"ends_with",...am.normalizeParams(b),suffix:a})}function ea(a,b,c){return new aI({check:"property",property:a,schema:b,...am.normalizeParams(c)})}function eb(a,b){return new aJ({check:"mime_type",mime:a,...am.normalizeParams(b)})}function ec(a){return new aK({check:"overwrite",tx:a})}function ed(a){return ec(b=>b.normalize(a))}function ee(){return ec(a=>a.trim())}function ef(){return ec(a=>a.toLowerCase())}function eg(){return ec(a=>a.toUpperCase())}function eh(a,b,c){return new a({type:"array",element:b,...am.normalizeParams(c)})}function ei(a,b,c){return new a({type:"union",options:b,...am.normalizeParams(c)})}function ej(a,b,c,d){return new a({type:"union",options:c,discriminator:b,...am.normalizeParams(d)})}function ek(a,b,c){return new a({type:"intersection",left:b,right:c})}function el(a,b,c,d){let e=c instanceof aN,f=e?d:c;return new a({type:"tuple",items:b,rest:e?c:null,...am.normalizeParams(f)})}function em(a,b,c,d){return new a({type:"record",keyType:b,valueType:c,...am.normalizeParams(d)})}function en(a,b,c,d){return new a({type:"map",keyType:b,valueType:c,...am.normalizeParams(d)})}function eo(a,b,c){return new a({type:"set",valueType:b,...am.normalizeParams(c)})}function ep(a,b,c){return new a({type:"enum",entries:Array.isArray(b)?Object.fromEntries(b.map(a=>[a,a])):b,...am.normalizeParams(c)})}function eq(a,b,c){return new a({type:"enum",entries:b,...am.normalizeParams(c)})}function er(a,b,c){return new a({type:"literal",values:Array.isArray(b)?b:[b],...am.normalizeParams(c)})}function es(a,b){return new a({type:"file",...am.normalizeParams(b)})}function et(a,b){return new a({type:"transform",transform:b})}function eu(a,b){return new a({type:"optional",innerType:b})}function ev(a,b){return new a({type:"nullable",innerType:b})}function ew(a,b,c){return new a({type:"default",innerType:b,get defaultValue(){return"function"==typeof c?c():am.shallowClone(c)}})}function ex(a,b,c){return new a({type:"nonoptional",innerType:b,...am.normalizeParams(c)})}function ey(a,b){return new a({type:"success",innerType:b})}function ez(a,b,c){return new a({type:"catch",innerType:b,catchValue:"function"==typeof c?c:()=>c})}function eA(a,b,c){return new a({type:"pipe",in:b,out:c})}function eB(a,b){return new a({type:"readonly",innerType:b})}function eC(a,b,c){return new a({type:"template_literal",parts:b,...am.normalizeParams(c)})}function eD(a,b){return new a({type:"lazy",getter:b})}function eE(a,b){return new a({type:"promise",innerType:b})}function eF(a,b,c){let d=am.normalizeParams(c);return d.abort??(d.abort=!0),new a({type:"custom",check:"custom",fn:b,...d})}function eG(a,b,c){return new a({type:"custom",check:"custom",fn:b,...am.normalizeParams(c)})}function eH(a){let b=eI(c=>(c.addIssue=a=>{"string"==typeof a?c.issues.push(am.issue(a,c.value,b._zod.def)):(a.fatal&&(a.continue=!1),a.code??(a.code="custom"),a.input??(a.input=c.value),a.inst??(a.inst=b),a.continue??(a.continue=!b._zod.def.abort),c.issues.push(am.issue(a)))},a(c.value,c)));return b}function eI(a,b){let c=new an({check:"custom",...am.normalizeParams(b)});return c._zod.check=a,c}function eJ(a,b){let c=am.normalizeParams(b),d=c.truthy??["true","1","yes","on","y","enabled"],e=c.falsy??["false","0","no","off","n","disabled"];"sensitive"!==c.case&&(d=d.map(a=>"string"==typeof a?a.toLowerCase():a),e=e.map(a=>"string"==typeof a?a.toLowerCase():a));let f=new Set(d),g=new Set(e),h=a.Codec??b0,i=a.Boolean??bh,j=new h({type:"pipe",in:new(a.String??aO)({type:"string",error:c.error}),out:new i({type:"boolean",error:c.error}),transform:(a,b)=>{let d=a;return"sensitive"!==c.case&&(d=d.toLowerCase()),!!f.has(d)||!g.has(d)&&(b.issues.push({code:"invalid_value",expected:"stringbool",values:[...f,...g],input:b.value,inst:j,continue:!1}),{})},reverseTransform:(a,b)=>!0===a?d[0]||"true":e[0]||"false",error:c.error});return j}function eK(a,b,c,d={}){let e=am.normalizeParams(d),f={...am.normalizeParams(d),check:"string_format",type:"string",format:b,fn:"function"==typeof c?c:a=>c.test(a),...e};return c instanceof RegExp&&(f.pattern=c),new a(f)}a.i(17649),a.s(["JSONSchemaGenerator",()=>eL,"toJSONSchema",()=>eM],40952);class eL{constructor(a){this.counter=0,this.metadataRegistry=a?.metadata??c_,this.target=a?.target??"draft-2020-12",this.unrepresentable=a?.unrepresentable??"throw",this.override=a?.override??(()=>{}),this.io=a?.io??"output",this.seen=new Map}process(a,b={path:[],schemaPath:[]}){var c;let d=a._zod.def,e=this.seen.get(a);if(e)return e.count++,b.schemaPath.includes(a)&&(e.cycle=b.path),e.schema;let f={schema:{},count:1,cycle:void 0,path:b.path};this.seen.set(a,f);let g=a._zod.toJSONSchema?.();if(g)f.schema=g;else{let c={...b,schemaPath:[...b.schemaPath,a],path:b.path},e=a._zod.parent;if(e)f.ref=e,this.process(e,c),this.seen.get(e).isParent=!0;else{let b=f.schema;switch(d.type){case"string":{b.type="string";let{minimum:c,maximum:d,format:e,patterns:g,contentEncoding:h}=a._zod.bag;if("number"==typeof c&&(b.minLength=c),"number"==typeof d&&(b.maxLength=d),e&&(b.format=({guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""})[e]??e,""===b.format&&delete b.format),h&&(b.contentEncoding=h),g&&g.size>0){let a=[...g];1===a.length?b.pattern=a[0].source:a.length>1&&(f.schema.allOf=[...a.map(a=>({..."draft-7"===this.target||"draft-4"===this.target||"openapi-3.0"===this.target?{type:"string"}:{},pattern:a.source}))])}break}case"number":{let{minimum:c,maximum:d,format:e,multipleOf:f,exclusiveMaximum:g,exclusiveMinimum:h}=a._zod.bag;"string"==typeof e&&e.includes("int")?b.type="integer":b.type="number","number"==typeof h&&("draft-4"===this.target||"openapi-3.0"===this.target?(b.minimum=h,b.exclusiveMinimum=!0):b.exclusiveMinimum=h),"number"==typeof c&&(b.minimum=c,"number"==typeof h&&"draft-4"!==this.target&&(h>=c?delete b.minimum:delete b.exclusiveMinimum)),"number"==typeof g&&("draft-4"===this.target||"openapi-3.0"===this.target?(b.maximum=g,b.exclusiveMaximum=!0):b.exclusiveMaximum=g),"number"==typeof d&&(b.maximum=d,"number"==typeof g&&"draft-4"!==this.target&&(g<=d?delete b.maximum:delete b.exclusiveMaximum)),"number"==typeof f&&(b.multipleOf=f);break}case"boolean":case"success":b.type="boolean";break;case"bigint":if("throw"===this.unrepresentable)throw Error("BigInt cannot be represented in JSON Schema");break;case"symbol":if("throw"===this.unrepresentable)throw Error("Symbols cannot be represented in JSON Schema");break;case"null":"openapi-3.0"===this.target?(b.type="string",b.nullable=!0,b.enum=[null]):b.type="null";break;case"any":case"unknown":break;case"undefined":if("throw"===this.unrepresentable)throw Error("Undefined cannot be represented in JSON Schema");break;case"void":if("throw"===this.unrepresentable)throw Error("Void cannot be represented in JSON Schema");break;case"never":b.not={};break;case"date":if("throw"===this.unrepresentable)throw Error("Date cannot be represented in JSON Schema");break;case"array":{let{minimum:e,maximum:f}=a._zod.bag;"number"==typeof e&&(b.minItems=e),"number"==typeof f&&(b.maxItems=f),b.type="array",b.items=this.process(d.element,{...c,path:[...c.path,"items"]});break}case"object":{b.type="object",b.properties={};let a=d.shape;for(let d in a)b.properties[d]=this.process(a[d],{...c,path:[...c.path,"properties",d]});let e=new Set([...new Set(Object.keys(a))].filter(a=>{let b=d.shape[a]._zod;return"input"===this.io?void 0===b.optin:void 0===b.optout}));e.size>0&&(b.required=Array.from(e)),d.catchall?._zod.def.type==="never"?b.additionalProperties=!1:d.catchall?d.catchall&&(b.additionalProperties=this.process(d.catchall,{...c,path:[...c.path,"additionalProperties"]})):"output"===this.io&&(b.additionalProperties=!1);break}case"union":b.anyOf=d.options.map((a,b)=>this.process(a,{...c,path:[...c.path,"anyOf",b]}));break;case"intersection":{let a=this.process(d.left,{...c,path:[...c.path,"allOf",0]}),e=this.process(d.right,{...c,path:[...c.path,"allOf",1]}),f=a=>"allOf"in a&&1===Object.keys(a).length;b.allOf=[...f(a)?a.allOf:[a],...f(e)?e.allOf:[e]];break}case"tuple":{b.type="array";let e="draft-2020-12"===this.target?"prefixItems":"items",f="draft-2020-12"===this.target||"openapi-3.0"===this.target?"items":"additionalItems",g=d.items.map((a,b)=>this.process(a,{...c,path:[...c.path,e,b]})),h=d.rest?this.process(d.rest,{...c,path:[...c.path,f,..."openapi-3.0"===this.target?[d.items.length]:[]]}):null;"draft-2020-12"===this.target?(b.prefixItems=g,h&&(b.items=h)):"openapi-3.0"===this.target?(b.items={anyOf:g},h&&b.items.anyOf.push(h),b.minItems=g.length,h||(b.maxItems=g.length)):(b.items=g,h&&(b.additionalItems=h));let{minimum:i,maximum:j}=a._zod.bag;"number"==typeof i&&(b.minItems=i),"number"==typeof j&&(b.maxItems=j);break}case"record":b.type="object",("draft-7"===this.target||"draft-2020-12"===this.target)&&(b.propertyNames=this.process(d.keyType,{...c,path:[...c.path,"propertyNames"]})),b.additionalProperties=this.process(d.valueType,{...c,path:[...c.path,"additionalProperties"]});break;case"map":if("throw"===this.unrepresentable)throw Error("Map cannot be represented in JSON Schema");break;case"set":if("throw"===this.unrepresentable)throw Error("Set cannot be represented in JSON Schema");break;case"enum":{let a=(0,am.getEnumValues)(d.entries);a.every(a=>"number"==typeof a)&&(b.type="number"),a.every(a=>"string"==typeof a)&&(b.type="string"),b.enum=a;break}case"literal":{let a=[];for(let b of d.values)if(void 0===b){if("throw"===this.unrepresentable)throw Error("Literal `undefined` cannot be represented in JSON Schema")}else if("bigint"==typeof b)if("throw"===this.unrepresentable)throw Error("BigInt literals cannot be represented in JSON Schema");else a.push(Number(b));else a.push(b);if(0===a.length);else if(1===a.length){let c=a[0];b.type=null===c?"null":typeof c,"draft-4"===this.target||"openapi-3.0"===this.target?b.enum=[c]:b.const=c}else a.every(a=>"number"==typeof a)&&(b.type="number"),a.every(a=>"string"==typeof a)&&(b.type="string"),a.every(a=>"boolean"==typeof a)&&(b.type="string"),a.every(a=>null===a)&&(b.type="null"),b.enum=a;break}case"file":{let c={type:"string",format:"binary",contentEncoding:"binary"},{minimum:d,maximum:e,mime:f}=a._zod.bag;void 0!==d&&(c.minLength=d),void 0!==e&&(c.maxLength=e),f?1===f.length?(c.contentMediaType=f[0],Object.assign(b,c)):b.anyOf=f.map(a=>({...c,contentMediaType:a})):Object.assign(b,c);break}case"transform":if("throw"===this.unrepresentable)throw Error("Transforms cannot be represented in JSON Schema");break;case"nullable":{let a=this.process(d.innerType,c);"openapi-3.0"===this.target?(f.ref=d.innerType,b.nullable=!0):b.anyOf=[a,{type:"null"}];break}case"nonoptional":case"promise":case"optional":this.process(d.innerType,c),f.ref=d.innerType;break;case"default":this.process(d.innerType,c),f.ref=d.innerType,b.default=JSON.parse(JSON.stringify(d.defaultValue));break;case"prefault":this.process(d.innerType,c),f.ref=d.innerType,"input"===this.io&&(b._prefault=JSON.parse(JSON.stringify(d.defaultValue)));break;case"catch":{let a;this.process(d.innerType,c),f.ref=d.innerType;try{a=d.catchValue(void 0)}catch{throw Error("Dynamic catch values are not supported in JSON Schema")}b.default=a;break}case"nan":if("throw"===this.unrepresentable)throw Error("NaN cannot be represented in JSON Schema");break;case"template_literal":{let c=a._zod.pattern;if(!c)throw Error("Pattern not found in template literal");b.type="string",b.pattern=c.source;break}case"pipe":{let a="input"===this.io?"transform"===d.in._zod.def.type?d.out:d.in:d.out;this.process(a,c),f.ref=a;break}case"readonly":this.process(d.innerType,c),f.ref=d.innerType,b.readOnly=!0;break;case"lazy":{let b=a._zod.innerType;this.process(b,c),f.ref=b;break}case"custom":if("throw"===this.unrepresentable)throw Error("Custom types cannot be represented in JSON Schema");break;case"function":if("throw"===this.unrepresentable)throw Error("Function types cannot be represented in JSON Schema")}}}let h=this.metadataRegistry.get(a);return h&&Object.assign(f.schema,h),"input"===this.io&&function a(b,c){let d=c??{seen:new Set};if(d.seen.has(b))return!1;d.seen.add(b);let e=b._zod.def;switch(e.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":case"custom":case"success":case"catch":case"function":return!1;case"array":return a(e.element,d);case"object":for(let b in e.shape)if(a(e.shape[b],d))return!0;return!1;case"union":for(let b of e.options)if(a(b,d))return!0;return!1;case"intersection":return a(e.left,d)||a(e.right,d);case"tuple":for(let b of e.items)if(a(b,d))return!0;if(e.rest&&a(e.rest,d))return!0;return!1;case"record":case"map":return a(e.keyType,d)||a(e.valueType,d);case"set":return a(e.valueType,d);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":case"default":case"prefault":return a(e.innerType,d);case"lazy":return a(e.getter(),d);case"transform":return!0;case"pipe":return a(e.in,d)||a(e.out,d)}throw Error(`Unknown schema type: ${e.type}`)}(a)&&(delete f.schema.examples,delete f.schema.default),"input"===this.io&&f.schema._prefault&&((c=f.schema).default??(c.default=f.schema._prefault)),delete f.schema._prefault,this.seen.get(a).schema}emit(a,b){let c={cycles:b?.cycles??"ref",reused:b?.reused??"inline",external:b?.external??void 0},d=this.seen.get(a);if(!d)throw Error("Unprocessed schema. This is a bug in Zod.");let e=a=>{let b="draft-2020-12"===this.target?"$defs":"definitions";if(c.external){let d=c.external.registry.get(a[0])?.id,e=c.external.uri??(a=>a);if(d)return{ref:e(d)};let f=a[1].defId??a[1].schema.id??`schema${this.counter++}`;return a[1].defId=f,{defId:f,ref:`${e("__shared")}#/${b}/${f}`}}if(a[1]===d)return{ref:"#"};let e=`#/${b}/`,f=a[1].schema.id??`__schema${this.counter++}`;return{defId:f,ref:e+f}},f=a=>{if(a[1].schema.$ref)return;let b=a[1],{ref:c,defId:d}=e(a);b.def={...b.schema},d&&(b.defId=d);let f=b.schema;for(let a in f)delete f[a];f.$ref=c};if("throw"===c.cycles)for(let a of this.seen.entries()){let b=a[1];if(b.cycle)throw Error(`Cycle detected: #/${b.cycle?.join("/")}/<root>

Set the \`cycles\` parameter to \`"ref"\` to resolve cyclical schemas with defs.`)}for(let b of this.seen.entries()){let d=b[1];if(a===b[0]){f(b);continue}if(c.external){let d=c.external.registry.get(b[0])?.id;if(a!==b[0]&&d){f(b);continue}}if(this.metadataRegistry.get(b[0])?.id||d.cycle||d.count>1&&"ref"===c.reused){f(b);continue}}let g=(a,b)=>{let c=this.seen.get(a),d=c.def??c.schema,e={...d};if(null===c.ref)return;let f=c.ref;if(c.ref=null,f){g(f,b);let a=this.seen.get(f).schema;a.$ref&&("draft-7"===b.target||"draft-4"===b.target||"openapi-3.0"===b.target)?(d.allOf=d.allOf??[],d.allOf.push(a)):(Object.assign(d,a),Object.assign(d,e))}c.isParent||this.override({zodSchema:a,jsonSchema:d,path:c.path??[]})};for(let a of[...this.seen.entries()].reverse())g(a[0],{target:this.target});let h={};if("draft-2020-12"===this.target?h.$schema="https://json-schema.org/draft/2020-12/schema":"draft-7"===this.target?h.$schema="http://json-schema.org/draft-07/schema#":"draft-4"===this.target?h.$schema="http://json-schema.org/draft-04/schema#":"openapi-3.0"===this.target||console.warn(`Invalid target: ${this.target}`),c.external?.uri){let b=c.external.registry.get(a)?.id;if(!b)throw Error("Schema is missing an `id` property");h.$id=c.external.uri(b)}Object.assign(h,d.def);let i=c.external?.defs??{};for(let a of this.seen.entries()){let b=a[1];b.def&&b.defId&&(i[b.defId]=b.def)}c.external||Object.keys(i).length>0&&("draft-2020-12"===this.target?h.$defs=i:h.definitions=i);try{return JSON.parse(JSON.stringify(h))}catch(a){throw Error("Error converting schema to JSON.")}}}function eM(a,b){if(a instanceof cZ){let c=new eL(b),d={};for(let b of a._idmap.entries()){let[a,d]=b;c.process(d)}let e={},f={registry:a,uri:b?.uri,defs:d};for(let d of a._idmap.entries()){let[a,g]=d;e[a]=c.emit(g,{...b,external:f})}return Object.keys(d).length>0&&(e.__shared={["draft-2020-12"===c.target?"$defs":"definitions"]:d}),{schemas:e}}let c=new eL(b);return c.process(a),c.emit(a,b)}a.i(40952),a.s([],10322);var eN=a.i(10322),eO=a.i(99490);a.s(["ZodAny",()=>gl,"ZodArray",()=>gv,"ZodBase64",()=>fP,"ZodBase64URL",()=>fR,"ZodBigInt",()=>ga,"ZodBigIntFormat",()=>gc,"ZodBoolean",()=>f8,"ZodCIDRv4",()=>fL,"ZodCIDRv6",()=>fN,"ZodCUID",()=>fx,"ZodCUID2",()=>fz,"ZodCatch",()=>hb,"ZodCodec",()=>hh,"ZodCustom",()=>ht,"ZodCustomStringFormat",()=>fX,"ZodDate",()=>gt,"ZodDefault",()=>g3,"ZodDiscriminatedUnion",()=>gE,"ZodE164",()=>fT,"ZodEmail",()=>fh,"ZodEmoji",()=>ft,"ZodEnum",()=>gR,"ZodFile",()=>gW,"ZodFunction",()=>hr,"ZodGUID",()=>fj,"ZodIPv4",()=>fH,"ZodIPv6",()=>fJ,"ZodIntersection",()=>gG,"ZodJWT",()=>fV,"ZodKSUID",()=>fF,"ZodLazy",()=>hn,"ZodLiteral",()=>gU,"ZodMap",()=>gN,"ZodNaN",()=>hd,"ZodNanoID",()=>fv,"ZodNever",()=>gp,"ZodNonOptional",()=>g7,"ZodNull",()=>gj,"ZodNullable",()=>g0,"ZodNumber",()=>f0,"ZodNumberFormat",()=>f2,"ZodObject",()=>gy,"ZodOptional",()=>g$,"ZodPipe",()=>hf,"ZodPrefault",()=>g5,"ZodPromise",()=>hp,"ZodReadonly",()=>hj,"ZodRecord",()=>gK,"ZodSet",()=>gP,"ZodString",()=>fe,"ZodStringFormat",()=>fg,"ZodSuccess",()=>g9,"ZodSymbol",()=>gf,"ZodTemplateLiteral",()=>hl,"ZodTransform",()=>gY,"ZodTuple",()=>gI,"ZodType",()=>fc,"ZodULID",()=>fB,"ZodURL",()=>fq,"ZodUUID",()=>fl,"ZodUndefined",()=>gh,"ZodUnion",()=>gC,"ZodUnknown",()=>gn,"ZodVoid",()=>gr,"ZodXID",()=>fD,"_ZodString",()=>fd,"_default",()=>g4,"_function",()=>hs,"any",()=>gm,"array",()=>gw,"base64",()=>fQ,"base64url",()=>fS,"bigint",()=>gb,"boolean",()=>f9,"catch",()=>hc,"check",()=>hu,"cidrv4",()=>fM,"cidrv6",()=>fO,"codec",()=>hi,"cuid",()=>fy,"cuid2",()=>fA,"custom",()=>hv,"date",()=>gu,"discriminatedUnion",()=>gF,"e164",()=>fU,"email",()=>fi,"emoji",()=>fu,"enum",()=>gS,"file",()=>gX,"float32",()=>f4,"float64",()=>f5,"function",()=>hs,"guid",()=>fk,"hash",()=>f_,"hex",()=>f$,"hostname",()=>fZ,"httpUrl",()=>fs,"instanceof",()=>hy,"int",()=>f3,"int32",()=>f6,"int64",()=>gd,"intersection",()=>gH,"ipv4",()=>fI,"ipv6",()=>fK,"json",()=>hA,"jwt",()=>fW,"keyof",()=>gx,"ksuid",()=>fG,"lazy",()=>ho,"literal",()=>gV,"looseObject",()=>gB,"map",()=>gO,"nan",()=>he,"nanoid",()=>fw,"nativeEnum",()=>gT,"never",()=>gq,"nonoptional",()=>g8,"null",()=>gk,"nullable",()=>g1,"nullish",()=>g2,"number",()=>f1,"object",()=>gz,"optional",()=>g_,"partialRecord",()=>gM,"pipe",()=>hg,"prefault",()=>g6,"preprocess",()=>hB,"promise",()=>hq,"readonly",()=>hk,"record",()=>gL,"refine",()=>hw,"set",()=>gQ,"strictObject",()=>gA,"string",()=>ff,"stringFormat",()=>fY,"stringbool",()=>hz,"success",()=>ha,"superRefine",()=>hx,"symbol",()=>gg,"templateLiteral",()=>hm,"transform",()=>gZ,"tuple",()=>gJ,"uint32",()=>f7,"uint64",()=>ge,"ulid",()=>fC,"undefined",()=>gi,"union",()=>gD,"unknown",()=>go,"url",()=>fr,"uuid",()=>fm,"uuidv4",()=>fn,"uuidv6",()=>fo,"uuidv7",()=>fp,"void",()=>gs,"xid",()=>fE],56284);var eP=cd,eQ=am;a.s(["ZodISODate",()=>eT,"ZodISODateTime",()=>eR,"ZodISODuration",()=>eX,"ZodISOTime",()=>eV,"date",()=>eU,"datetime",()=>eS,"duration",()=>eY,"time",()=>eW],3757);let eR=c.$constructor("ZodISODateTime",(a,b)=>{a_.init(a,b),fg.init(a,b)});function eS(a){return dq(eR,a)}let eT=c.$constructor("ZodISODate",(a,b)=>{a0.init(a,b),fg.init(a,b)});function eU(a){return dr(eT,a)}let eV=c.$constructor("ZodISOTime",(a,b)=>{a1.init(a,b),fg.init(a,b)});function eW(a){return ds(eV,a)}let eX=c.$constructor("ZodISODuration",(a,b)=>{a2.init(a,b),fg.init(a,b)});function eY(a){return dt(eX,a)}a.s(["decode",()=>e5,"decodeAsync",()=>e7,"encode",()=>e4,"encodeAsync",()=>e6,"parse",()=>e0,"parseAsync",()=>e1,"safeDecode",()=>e9,"safeDecodeAsync",()=>fb,"safeEncode",()=>e8,"safeEncodeAsync",()=>fa,"safeParse",()=>e2,"safeParseAsync",()=>e3],65357),a.s(["ZodError",()=>e$,"ZodRealError",()=>e_],38626);let eZ=(a,b)=>{e.$ZodError.init(a,b),a.name="ZodError",Object.defineProperties(a,{format:{value:b=>e.formatError(a,b)},flatten:{value:b=>e.flattenError(a,b)},addIssue:{value:b=>{a.issues.push(b),a.message=JSON.stringify(a.issues,am.jsonStringifyReplacer,2)}},addIssues:{value:b=>{a.issues.push(...b),a.message=JSON.stringify(a.issues,am.jsonStringifyReplacer,2)}},isEmpty:{get:()=>0===a.issues.length}})},e$=c.$constructor("ZodError",eZ),e_=c.$constructor("ZodError",eZ,{Parent:Error}),e0=d._parse(e_),e1=d._parseAsync(e_),e2=d._safeParse(e_),e3=d._safeParseAsync(e_),e4=d._encode(e_),e5=d._decode(e_),e6=d._encodeAsync(e_),e7=d._decodeAsync(e_),e8=d._safeEncode(e_),e9=d._safeDecode(e_),fa=d._safeEncodeAsync(e_),fb=d._safeDecodeAsync(e_),fc=c.$constructor("ZodType",(a,b)=>(aN.init(a,b),a.def=b,a.type=b.type,Object.defineProperty(a,"_def",{value:b}),a.check=(...c)=>a.clone({...b,checks:[...b.checks??[],...c.map(a=>"function"==typeof a?{_zod:{check:a,def:{check:"custom"},onattach:[]}}:a)]}),a.clone=(b,c)=>am.clone(a,b,c),a.brand=()=>a,a.register=(b,c)=>(b.add(a,c),a),a.parse=(b,c)=>e0(a,b,c,{callee:a.parse}),a.safeParse=(b,c)=>e2(a,b,c),a.parseAsync=async(b,c)=>e1(a,b,c,{callee:a.parseAsync}),a.safeParseAsync=async(b,c)=>e3(a,b,c),a.spa=a.safeParseAsync,a.encode=(b,c)=>e4(a,b,c),a.decode=(b,c)=>e5(a,b,c),a.encodeAsync=async(b,c)=>e6(a,b,c),a.decodeAsync=async(b,c)=>e7(a,b,c),a.safeEncode=(b,c)=>e8(a,b,c),a.safeDecode=(b,c)=>e9(a,b,c),a.safeEncodeAsync=async(b,c)=>fa(a,b,c),a.safeDecodeAsync=async(b,c)=>fb(a,b,c),a.refine=(b,c)=>a.check(hw(b,c)),a.superRefine=b=>a.check(eH(b)),a.overwrite=b=>a.check(ec(b)),a.optional=()=>g_(a),a.nullable=()=>g1(a),a.nullish=()=>g_(g1(a)),a.nonoptional=b=>g8(a,b),a.array=()=>gw(a),a.or=b=>gD([a,b]),a.and=b=>gH(a,b),a.transform=b=>hg(a,gZ(b)),a.default=b=>g4(a,b),a.prefault=b=>g6(a,b),a.catch=b=>hc(a,b),a.pipe=b=>hg(a,b),a.readonly=()=>hk(a),a.describe=b=>{let c=a.clone();return c_.add(c,{description:b}),c},Object.defineProperty(a,"description",{get:()=>c_.get(a)?.description,configurable:!0}),a.meta=(...b)=>{if(0===b.length)return c_.get(a);let c=a.clone();return c_.add(c,b[0]),c},a.isOptional=()=>a.safeParse(void 0).success,a.isNullable=()=>a.safeParse(null).success,a)),fd=c.$constructor("_ZodString",(a,b)=>{aO.init(a,b),fc.init(a,b);let c=a._zod.bag;a.format=c.format??null,a.minLength=c.minimum??null,a.maxLength=c.maximum??null,a.regex=(...b)=>a.check(d4(...b)),a.includes=(...b)=>a.check(d7(...b)),a.startsWith=(...b)=>a.check(d8(...b)),a.endsWith=(...b)=>a.check(d9(...b)),a.min=(...b)=>a.check(d2(...b)),a.max=(...b)=>a.check(d1(...b)),a.length=(...b)=>a.check(d3(...b)),a.nonempty=(...b)=>a.check(d2(1,...b)),a.lowercase=b=>a.check(d5(b)),a.uppercase=b=>a.check(d6(b)),a.trim=()=>a.check(ee()),a.normalize=(...b)=>a.check(ed(...b)),a.toLowerCase=()=>a.check(ef()),a.toUpperCase=()=>a.check(eg())}),fe=c.$constructor("ZodString",(a,b)=>{aO.init(a,b),fd.init(a,b),a.email=b=>a.check(c2(fh,b)),a.url=b=>a.check(c8(fq,b)),a.jwt=b=>a.check(dn(fV,b)),a.emoji=b=>a.check(c9(ft,b)),a.guid=b=>a.check(c3(fj,b)),a.uuid=b=>a.check(c4(fl,b)),a.uuidv4=b=>a.check(c5(fl,b)),a.uuidv6=b=>a.check(c6(fl,b)),a.uuidv7=b=>a.check(c7(fl,b)),a.nanoid=b=>a.check(da(fv,b)),a.guid=b=>a.check(c3(fj,b)),a.cuid=b=>a.check(db(fx,b)),a.cuid2=b=>a.check(dc(fz,b)),a.ulid=b=>a.check(dd(fB,b)),a.base64=b=>a.check(dk(fP,b)),a.base64url=b=>a.check(dl(fR,b)),a.xid=b=>a.check(de(fD,b)),a.ksuid=b=>a.check(df(fF,b)),a.ipv4=b=>a.check(dg(fH,b)),a.ipv6=b=>a.check(dh(fJ,b)),a.cidrv4=b=>a.check(di(fL,b)),a.cidrv6=b=>a.check(dj(fN,b)),a.e164=b=>a.check(dm(fT,b)),a.datetime=b=>a.check(eS(b)),a.date=b=>a.check(eU(b)),a.time=b=>a.check(eW(b)),a.duration=b=>a.check(eY(b))});function ff(a){return c0(fe,a)}let fg=c.$constructor("ZodStringFormat",(a,b)=>{aP.init(a,b),fd.init(a,b)}),fh=c.$constructor("ZodEmail",(a,b)=>{aS.init(a,b),fg.init(a,b)});function fi(a){return c2(fh,a)}let fj=c.$constructor("ZodGUID",(a,b)=>{aQ.init(a,b),fg.init(a,b)});function fk(a){return c3(fj,a)}let fl=c.$constructor("ZodUUID",(a,b)=>{aR.init(a,b),fg.init(a,b)});function fm(a){return c4(fl,a)}function fn(a){return c5(fl,a)}function fo(a){return c6(fl,a)}function fp(a){return c7(fl,a)}let fq=c.$constructor("ZodURL",(a,b)=>{aT.init(a,b),fg.init(a,b)});function fr(a){return c8(fq,a)}function fs(a){return c8(fq,{protocol:/^https?$/,hostname:eP.domain,...eQ.normalizeParams(a)})}let ft=c.$constructor("ZodEmoji",(a,b)=>{aU.init(a,b),fg.init(a,b)});function fu(a){return c9(ft,a)}let fv=c.$constructor("ZodNanoID",(a,b)=>{aV.init(a,b),fg.init(a,b)});function fw(a){return da(fv,a)}let fx=c.$constructor("ZodCUID",(a,b)=>{aW.init(a,b),fg.init(a,b)});function fy(a){return db(fx,a)}let fz=c.$constructor("ZodCUID2",(a,b)=>{aX.init(a,b),fg.init(a,b)});function fA(a){return dc(fz,a)}let fB=c.$constructor("ZodULID",(a,b)=>{aY.init(a,b),fg.init(a,b)});function fC(a){return dd(fB,a)}let fD=c.$constructor("ZodXID",(a,b)=>{aZ.init(a,b),fg.init(a,b)});function fE(a){return de(fD,a)}let fF=c.$constructor("ZodKSUID",(a,b)=>{a$.init(a,b),fg.init(a,b)});function fG(a){return df(fF,a)}let fH=c.$constructor("ZodIPv4",(a,b)=>{a3.init(a,b),fg.init(a,b)});function fI(a){return dg(fH,a)}let fJ=c.$constructor("ZodIPv6",(a,b)=>{a4.init(a,b),fg.init(a,b)});function fK(a){return dh(fJ,a)}let fL=c.$constructor("ZodCIDRv4",(a,b)=>{a5.init(a,b),fg.init(a,b)});function fM(a){return di(fL,a)}let fN=c.$constructor("ZodCIDRv6",(a,b)=>{a6.init(a,b),fg.init(a,b)});function fO(a){return dj(fN,a)}let fP=c.$constructor("ZodBase64",(a,b)=>{a8.init(a,b),fg.init(a,b)});function fQ(a){return dk(fP,a)}let fR=c.$constructor("ZodBase64URL",(a,b)=>{ba.init(a,b),fg.init(a,b)});function fS(a){return dl(fR,a)}let fT=c.$constructor("ZodE164",(a,b)=>{bb.init(a,b),fg.init(a,b)});function fU(a){return dm(fT,a)}let fV=c.$constructor("ZodJWT",(a,b)=>{bd.init(a,b),fg.init(a,b)});function fW(a){return dn(fV,a)}let fX=c.$constructor("ZodCustomStringFormat",(a,b)=>{be.init(a,b),fg.init(a,b)});function fY(a,b,c={}){return eK(fX,a,b,c)}function fZ(a){return eK(fX,"hostname",eP.hostname,a)}function f$(a){return eK(fX,"hex",eP.hex,a)}function f_(a,b){let c=b?.enc??"hex",d=`${a}_${c}`,e=eP[d];if(!e)throw Error(`Unrecognized hash format: ${d}`);return eK(fX,d,e,b)}let f0=c.$constructor("ZodNumber",(a,b)=>{bf.init(a,b),fc.init(a,b),a.gt=(b,c)=>a.check(dT(b,c)),a.gte=(b,c)=>a.check(dU(b,c)),a.min=(b,c)=>a.check(dU(b,c)),a.lt=(b,c)=>a.check(dR(b,c)),a.lte=(b,c)=>a.check(dS(b,c)),a.max=(b,c)=>a.check(dS(b,c)),a.int=b=>a.check(f3(b)),a.safe=b=>a.check(f3(b)),a.positive=b=>a.check(dT(0,b)),a.nonnegative=b=>a.check(dU(0,b)),a.negative=b=>a.check(dR(0,b)),a.nonpositive=b=>a.check(dS(0,b)),a.multipleOf=(b,c)=>a.check(dZ(b,c)),a.step=(b,c)=>a.check(dZ(b,c)),a.finite=()=>a;let c=a._zod.bag;a.minValue=Math.max(c.minimum??-1/0,c.exclusiveMinimum??-1/0)??null,a.maxValue=Math.min(c.maximum??1/0,c.exclusiveMaximum??1/0)??null,a.isInt=(c.format??"").includes("int")||Number.isSafeInteger(c.multipleOf??.5),a.isFinite=!0,a.format=c.format??null});function f1(a){return du(f0,a)}let f2=c.$constructor("ZodNumberFormat",(a,b)=>{bg.init(a,b),f0.init(a,b)});function f3(a){return dw(f2,a)}function f4(a){return dx(f2,a)}function f5(a){return dy(f2,a)}function f6(a){return dz(f2,a)}function f7(a){return dA(f2,a)}let f8=c.$constructor("ZodBoolean",(a,b)=>{bh.init(a,b),fc.init(a,b)});function f9(a){return dB(f8,a)}let ga=c.$constructor("ZodBigInt",(a,b)=>{bi.init(a,b),fc.init(a,b),a.gte=(b,c)=>a.check(dU(b,c)),a.min=(b,c)=>a.check(dU(b,c)),a.gt=(b,c)=>a.check(dT(b,c)),a.gte=(b,c)=>a.check(dU(b,c)),a.min=(b,c)=>a.check(dU(b,c)),a.lt=(b,c)=>a.check(dR(b,c)),a.lte=(b,c)=>a.check(dS(b,c)),a.max=(b,c)=>a.check(dS(b,c)),a.positive=b=>a.check(dT(BigInt(0),b)),a.negative=b=>a.check(dR(BigInt(0),b)),a.nonpositive=b=>a.check(dS(BigInt(0),b)),a.nonnegative=b=>a.check(dU(BigInt(0),b)),a.multipleOf=(b,c)=>a.check(dZ(b,c));let c=a._zod.bag;a.minValue=c.minimum??null,a.maxValue=c.maximum??null,a.format=c.format??null});function gb(a){return dD(ga,a)}let gc=c.$constructor("ZodBigIntFormat",(a,b)=>{bj.init(a,b),ga.init(a,b)});function gd(a){return dF(gc,a)}function ge(a){return dG(gc,a)}let gf=c.$constructor("ZodSymbol",(a,b)=>{bk.init(a,b),fc.init(a,b)});function gg(a){return dH(gf,a)}let gh=c.$constructor("ZodUndefined",(a,b)=>{bl.init(a,b),fc.init(a,b)});function gi(a){return dI(gh,a)}let gj=c.$constructor("ZodNull",(a,b)=>{bm.init(a,b),fc.init(a,b)});function gk(a){return dJ(gj,a)}let gl=c.$constructor("ZodAny",(a,b)=>{bn.init(a,b),fc.init(a,b)});function gm(){return dK(gl)}let gn=c.$constructor("ZodUnknown",(a,b)=>{bo.init(a,b),fc.init(a,b)});function go(){return dL(gn)}let gp=c.$constructor("ZodNever",(a,b)=>{bp.init(a,b),fc.init(a,b)});function gq(a){return dM(gp,a)}let gr=c.$constructor("ZodVoid",(a,b)=>{bq.init(a,b),fc.init(a,b)});function gs(a){return dN(gr,a)}let gt=c.$constructor("ZodDate",(a,b)=>{br.init(a,b),fc.init(a,b),a.min=(b,c)=>a.check(dU(b,c)),a.max=(b,c)=>a.check(dS(b,c));let c=a._zod.bag;a.minDate=c.minimum?new Date(c.minimum):null,a.maxDate=c.maximum?new Date(c.maximum):null});function gu(a){return dO(gt,a)}let gv=c.$constructor("ZodArray",(a,b)=>{bt.init(a,b),fc.init(a,b),a.element=b.element,a.min=(b,c)=>a.check(d2(b,c)),a.nonempty=b=>a.check(d2(1,b)),a.max=(b,c)=>a.check(d1(b,c)),a.length=(b,c)=>a.check(d3(b,c)),a.unwrap=()=>a.element});function gw(a,b){return eh(gv,a,b)}function gx(a){return gS(Object.keys(a._zod.def.shape))}let gy=c.$constructor("ZodObject",(a,b)=>{by.init(a,b),fc.init(a,b),eQ.defineLazy(a,"shape",()=>b.shape),a.keyof=()=>gS(Object.keys(a._zod.def.shape)),a.catchall=b=>a.clone({...a._zod.def,catchall:b}),a.passthrough=()=>a.clone({...a._zod.def,catchall:go()}),a.loose=()=>a.clone({...a._zod.def,catchall:go()}),a.strict=()=>a.clone({...a._zod.def,catchall:gq()}),a.strip=()=>a.clone({...a._zod.def,catchall:void 0}),a.extend=b=>eQ.extend(a,b),a.safeExtend=b=>eQ.safeExtend(a,b),a.merge=b=>eQ.merge(a,b),a.pick=b=>eQ.pick(a,b),a.omit=b=>eQ.omit(a,b),a.partial=(...b)=>eQ.partial(g$,a,b[0]),a.required=(...b)=>eQ.required(g7,a,b[0])});function gz(a,b){return new gy({type:"object",get shape(){return eQ.assignProp(this,"shape",a?eQ.objectClone(a):{}),this.shape},...eQ.normalizeParams(b)})}function gA(a,b){return new gy({type:"object",get shape(){return eQ.assignProp(this,"shape",eQ.objectClone(a)),this.shape},catchall:gq(),...eQ.normalizeParams(b)})}function gB(a,b){return new gy({type:"object",get shape(){return eQ.assignProp(this,"shape",eQ.objectClone(a)),this.shape},catchall:go(),...eQ.normalizeParams(b)})}let gC=c.$constructor("ZodUnion",(a,b)=>{bA.init(a,b),fc.init(a,b),a.options=b.options});function gD(a,b){return new gC({type:"union",options:a,...eQ.normalizeParams(b)})}let gE=c.$constructor("ZodDiscriminatedUnion",(a,b)=>{gC.init(a,b),bB.init(a,b)});function gF(a,b,c){return new gE({type:"union",options:b,discriminator:a,...eQ.normalizeParams(c)})}let gG=c.$constructor("ZodIntersection",(a,b)=>{bC.init(a,b),fc.init(a,b)});function gH(a,b){return new gG({type:"intersection",left:a,right:b})}let gI=c.$constructor("ZodTuple",(a,b)=>{bE.init(a,b),fc.init(a,b),a.rest=b=>a.clone({...a._zod.def,rest:b})});function gJ(a,b,c){let d=b instanceof aN,e=d?c:b;return new gI({type:"tuple",items:a,rest:d?b:null,...eQ.normalizeParams(e)})}let gK=c.$constructor("ZodRecord",(a,b)=>{bG.init(a,b),fc.init(a,b),a.keyType=b.keyType,a.valueType=b.valueType});function gL(a,b,c){return new gK({type:"record",keyType:a,valueType:b,...eQ.normalizeParams(c)})}function gM(a,b,c){let d=am.clone(a);return d._zod.values=void 0,new gK({type:"record",keyType:d,valueType:b,...eQ.normalizeParams(c)})}let gN=c.$constructor("ZodMap",(a,b)=>{bH.init(a,b),fc.init(a,b),a.keyType=b.keyType,a.valueType=b.valueType});function gO(a,b,c){return new gN({type:"map",keyType:a,valueType:b,...eQ.normalizeParams(c)})}let gP=c.$constructor("ZodSet",(a,b)=>{bJ.init(a,b),fc.init(a,b),a.min=(...b)=>a.check(d_(...b)),a.nonempty=b=>a.check(d_(1,b)),a.max=(...b)=>a.check(d$(...b)),a.size=(...b)=>a.check(d0(...b))});function gQ(a,b){return new gP({type:"set",valueType:a,...eQ.normalizeParams(b)})}let gR=c.$constructor("ZodEnum",(a,b)=>{bL.init(a,b),fc.init(a,b),a.enum=b.entries,a.options=Object.values(b.entries);let c=new Set(Object.keys(b.entries));a.extract=(a,d)=>{let e={};for(let d of a)if(c.has(d))e[d]=b.entries[d];else throw Error(`Key ${d} not found in enum`);return new gR({...b,checks:[],...eQ.normalizeParams(d),entries:e})},a.exclude=(a,d)=>{let e={...b.entries};for(let b of a)if(c.has(b))delete e[b];else throw Error(`Key ${b} not found in enum`);return new gR({...b,checks:[],...eQ.normalizeParams(d),entries:e})}});function gS(a,b){return new gR({type:"enum",entries:Array.isArray(a)?Object.fromEntries(a.map(a=>[a,a])):a,...eQ.normalizeParams(b)})}function gT(a,b){return new gR({type:"enum",entries:a,...eQ.normalizeParams(b)})}let gU=c.$constructor("ZodLiteral",(a,b)=>{bM.init(a,b),fc.init(a,b),a.values=new Set(b.values),Object.defineProperty(a,"value",{get(){if(b.values.length>1)throw Error("This schema contains multiple valid literal values. Use `.values` instead.");return b.values[0]}})});function gV(a,b){return new gU({type:"literal",values:Array.isArray(a)?a:[a],...eQ.normalizeParams(b)})}let gW=c.$constructor("ZodFile",(a,b)=>{bN.init(a,b),fc.init(a,b),a.min=(b,c)=>a.check(d_(b,c)),a.max=(b,c)=>a.check(d$(b,c)),a.mime=(b,c)=>a.check(eb(Array.isArray(b)?b:[b],c))});function gX(a){return es(gW,a)}let gY=c.$constructor("ZodTransform",(a,b)=>{bO.init(a,b),fc.init(a,b),a._zod.parse=(d,e)=>{if("backward"===e.direction)throw new c.$ZodEncodeError(a.constructor.name);d.addIssue=c=>{"string"==typeof c?d.issues.push(eQ.issue(c,d.value,b)):(c.fatal&&(c.continue=!1),c.code??(c.code="custom"),c.input??(c.input=d.value),c.inst??(c.inst=a),d.issues.push(eQ.issue(c)))};let f=b.transform(d.value,d);return f instanceof Promise?f.then(a=>(d.value=a,d)):(d.value=f,d)}});function gZ(a){return new gY({type:"transform",transform:a})}let g$=c.$constructor("ZodOptional",(a,b)=>{bQ.init(a,b),fc.init(a,b),a.unwrap=()=>a._zod.def.innerType});function g_(a){return new g$({type:"optional",innerType:a})}let g0=c.$constructor("ZodNullable",(a,b)=>{bR.init(a,b),fc.init(a,b),a.unwrap=()=>a._zod.def.innerType});function g1(a){return new g0({type:"nullable",innerType:a})}function g2(a){return g_(g1(a))}let g3=c.$constructor("ZodDefault",(a,b)=>{bS.init(a,b),fc.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeDefault=a.unwrap});function g4(a,b){return new g3({type:"default",innerType:a,get defaultValue(){return"function"==typeof b?b():eQ.shallowClone(b)}})}let g5=c.$constructor("ZodPrefault",(a,b)=>{bU.init(a,b),fc.init(a,b),a.unwrap=()=>a._zod.def.innerType});function g6(a,b){return new g5({type:"prefault",innerType:a,get defaultValue(){return"function"==typeof b?b():eQ.shallowClone(b)}})}let g7=c.$constructor("ZodNonOptional",(a,b)=>{bV.init(a,b),fc.init(a,b),a.unwrap=()=>a._zod.def.innerType});function g8(a,b){return new g7({type:"nonoptional",innerType:a,...eQ.normalizeParams(b)})}let g9=c.$constructor("ZodSuccess",(a,b)=>{bX.init(a,b),fc.init(a,b),a.unwrap=()=>a._zod.def.innerType});function ha(a){return new g9({type:"success",innerType:a})}let hb=c.$constructor("ZodCatch",(a,b)=>{bY.init(a,b),fc.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeCatch=a.unwrap});function hc(a,b){return new hb({type:"catch",innerType:a,catchValue:"function"==typeof b?b:()=>b})}let hd=c.$constructor("ZodNaN",(a,b)=>{bZ.init(a,b),fc.init(a,b)});function he(a){return dQ(hd,a)}let hf=c.$constructor("ZodPipe",(a,b)=>{b$.init(a,b),fc.init(a,b),a.in=b.in,a.out=b.out});function hg(a,b){return new hf({type:"pipe",in:a,out:b})}let hh=c.$constructor("ZodCodec",(a,b)=>{hf.init(a,b),b0.init(a,b)});function hi(a,b,c){return new hh({type:"pipe",in:a,out:b,transform:c.decode,reverseTransform:c.encode})}let hj=c.$constructor("ZodReadonly",(a,b)=>{b3.init(a,b),fc.init(a,b),a.unwrap=()=>a._zod.def.innerType});function hk(a){return new hj({type:"readonly",innerType:a})}let hl=c.$constructor("ZodTemplateLiteral",(a,b)=>{b5.init(a,b),fc.init(a,b)});function hm(a,b){return new hl({type:"template_literal",parts:a,...eQ.normalizeParams(b)})}let hn=c.$constructor("ZodLazy",(a,b)=>{b8.init(a,b),fc.init(a,b),a.unwrap=()=>a._zod.def.getter()});function ho(a){return new hn({type:"lazy",getter:a})}let hp=c.$constructor("ZodPromise",(a,b)=>{b7.init(a,b),fc.init(a,b),a.unwrap=()=>a._zod.def.innerType});function hq(a){return new hp({type:"promise",innerType:a})}let hr=c.$constructor("ZodFunction",(a,b)=>{b6.init(a,b),fc.init(a,b)});function hs(a){return new hr({type:"function",input:Array.isArray(a?.input)?gJ(a?.input):a?.input??gw(go()),output:a?.output??go()})}let ht=c.$constructor("ZodCustom",(a,b)=>{b9.init(a,b),fc.init(a,b)});function hu(a){let b=new an({check:"custom"});return b._zod.check=a,b}function hv(a,b){return eF(ht,a??(()=>!0),b)}function hw(a,b={}){return eG(ht,a,b)}function hx(a){return eH(a)}function hy(a,b={error:`Input not instance of ${a.name}`}){let c=new ht({type:"custom",check:"custom",fn:b=>b instanceof a,abort:!0,...eQ.normalizeParams(b)});return c._zod.bag.Class=a,c}let hz=(...a)=>eJ({Codec:hh,Boolean:f8,String:fe},...a);function hA(a){let b=ho(()=>gD([ff(a),f1(),f9(),gk(),gw(b),gL(ff(),b)]));return b}function hB(a,b){return hg(gZ(a),b)}a.i(56284),a.s(["endsWith",()=>d9,"gt",()=>dT,"gte",()=>dU,"includes",()=>d7,"length",()=>d3,"lowercase",()=>d5,"lt",()=>dR,"lte",()=>dS,"maxLength",()=>d1,"maxSize",()=>d$,"mime",()=>eb,"minLength",()=>d2,"minSize",()=>d_,"multipleOf",()=>dZ,"negative",()=>dW,"nonnegative",()=>dY,"nonpositive",()=>dX,"normalize",()=>ed,"overwrite",()=>ec,"positive",()=>dV,"property",()=>ea,"regex",()=>d4,"size",()=>d0,"startsWith",()=>d8,"toLowerCase",()=>ef,"toUpperCase",()=>eg,"trim",()=>ee,"uppercase",()=>d6],70346),a.s([],33802),a.i(33802),a.i(70346),a.i(38626),a.i(65357),a.s(["$brand",()=>hF.$brand,"ZodFirstPartyTypeKind",()=>b,"ZodIssueCode",()=>hC,"config",()=>hF.config,"getErrorMap",()=>hE,"setErrorMap",()=>hD],67366),a.s(["ZodFirstPartyTypeKind",()=>b,"ZodIssueCode",()=>hC,"getErrorMap",()=>hE,"setErrorMap",()=>hD],62611);let hC={invalid_type:"invalid_type",too_big:"too_big",too_small:"too_small",invalid_format:"invalid_format",not_multiple_of:"not_multiple_of",unrecognized_keys:"unrecognized_keys",invalid_union:"invalid_union",invalid_key:"invalid_key",invalid_element:"invalid_element",invalid_value:"invalid_value",custom:"custom"};function hD(a){c.config({customError:a})}function hE(){return c.config().customError}b||(b={}),a.i(62611);var hF=c;a.i(67366);var hG=c,hH=am,eP=cd,hI=e,eQ=am,hJ=cW,hK=a.i(3757);function hL(a){return c1(fe,a)}function hM(a){return dv(f0,a)}function hN(a){return dC(f8,a)}function hO(a){return dE(ga,a)}function hP(a){return dP(gt,a)}a.s(["bigint",()=>hO,"boolean",()=>hN,"date",()=>hP,"number",()=>hM,"string",()=>hL],21312);var hQ=a.i(21312),hR=a.i(47361)}];

//# sourceMappingURL=frontend_430c4d07._.js.map