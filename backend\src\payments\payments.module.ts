import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PaymentsController } from './payments.controller';
import { PaymentsService } from './payments.service';
import { Payment, PaymentSchema } from './schemas/payment.schema';
import { Caisse, CaisseSchema } from '../caisses/schemas/caisse.schema';
import { ExitOrder, ExitOrderSchema } from '../caisses/schemas/exit-order.schema';
import { SessionMember, SessionMemberSchema } from '../sessions/schemas/session-member.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Payment.name, schema: PaymentSchema },
      { name: Caisse.name, schema: CaisseSchema },
      { name: ExitOrder.name, schema: ExitOrderSchema },
      { name: SessionMember.name, schema: SessionMemberSchema },
    ]),
  ],
  controllers: [PaymentsController],
  providers: [PaymentsService],
  exports: [PaymentsService],
})
export class PaymentsModule {}