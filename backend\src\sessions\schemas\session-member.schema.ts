import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

// Intermediate model between Session and Member
export type SessionMemberDocument = HydratedDocument<SessionMember>;

@Schema({ timestamps: true })
export class SessionMember {
  @Prop({ type: Types.ObjectId, ref: 'Session', required: true })
  sessionId!: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Member', required: true })
  memberId!: Types.ObjectId;

  // number of parts chosen by member
  @Prop({ type: Number, required: true, min: 1 })
  parts!: number;

  // Total amount expected for the whole session
  @Prop({ type: Number, required: true, min: 0 })
  totalDue!: number;

  // Sum actually paid so far
  @Prop({ type: Number, required: true, min: 0, default: 0 })
  paidSoFar!: number;

  // Expected amount up to now considering meetings passed (updated by scheduler)
  @Prop({ type: Number, required: true, min: 0, default: 0 })
  expectedToDate!: number;

  // Delay amount = expectedToDate - paidSoFar (kept for convenient querying)
  @Prop({ type: Number, required: true, min: 0, default: 0 })
  overdueAmount!: number;
}

export const SessionMemberSchema = SchemaFactory.createForClass(SessionMember);