"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ArrowLeft, Calendar, DollarSign } from "lucide-react";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { useApi } from "@/hooks/use-api";
import { SessionForm } from "@/types";

const sessionSchema = z
	.object({
		annee: z
			.number()
			.min(2020, "L'année doit être supérieure à 2020")
			.max(2050, "L'année doit être inférieure à 2050"),
		dateDebut: z.string().min(1, "La date de début est requise"),
		dateFin: z.string().min(1, "La date de fin est requise"),
		partFixe: z
			.number()
			.min(1, "La part fixe doit être supérieure à 0")
			.max(1000000, "La part fixe ne peut pas dépasser 1,000,000 FCFA"),
	})
	.refine(
		(data) => {
			const debut = new Date(data.dateDebut);
			const fin = new Date(data.dateFin);
			return fin > debut;
		},
		{
			message: "La date de fin doit être postérieure à la date de début",
			path: ["dateFin"],
		},
	);

export default function NewSessionPage() {
	const { data: session } = useSession();
	const router = useRouter();
	const api = useApi();

	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Vérifier les permissions
	const canCreateSessions =
		session?.user && session.user.role === "secretary_general";

	const form = useForm<SessionForm>({
		resolver: zodResolver(sessionSchema),
		defaultValues: {
			annee: new Date().getFullYear(),
			dateDebut: "",
			dateFin: "",
			partFixe: 0,
		},
	});

	const onSubmit = async (data: SessionForm) => {
		if (!canCreateSessions) {
			setError("Vous n'avez pas les permissions pour créer une session");
			return;
		}

		try {
			setIsLoading(true);
			setError(null);

			await api.createSession(data);
			router.push("/dashboard/sessions");
		} catch (error: any) {
			console.error("Erreur lors de la création:", error);
			setError(error.message || "Une erreur est survenue lors de la création");
		} finally {
			setIsLoading(false);
		}
	};

	if (!canCreateSessions) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Button variant="outline" size="icon" asChild>
						<Link href="/dashboard/sessions">
							<ArrowLeft className="h-4 w-4" />
						</Link>
					</Button>
					<div>
						<h1 className="text-3xl font-bold tracking-tight">
							Nouvelle Session
						</h1>
						<p className="text-muted-foreground">
							Créer une nouvelle session de tontine
						</p>
					</div>
				</div>

				<Card>
					<CardContent className="pt-6">
						<div className="text-center py-8">
							<p className="text-muted-foreground">
								Vous n'avez pas les permissions pour créer une session.
							</p>
							<p className="text-sm text-muted-foreground mt-2">
								Seuls les administrateurs peuvent créer des sessions.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex items-center gap-4">
				<Button variant="outline" size="icon" asChild>
					<Link href="/dashboard/sessions">
						<ArrowLeft className="h-4 w-4" />
					</Link>
				</Button>
				<div>
					<h1 className="text-3xl font-bold tracking-tight">
						Nouvelle Session
					</h1>
					<p className="text-muted-foreground">
						Créer une nouvelle session de tontine
					</p>
				</div>
			</div>

			{/* Formulaire */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Calendar className="h-5 w-5" />
						Informations de la session
					</CardTitle>
					<CardDescription>
						Définissez les paramètres de la nouvelle session. Les réunions
						seront automatiquement générées pour chaque dimanche de la période.
					</CardDescription>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							{error && (
								<div className="bg-red-50 border border-red-200 rounded-md p-4">
									<p className="text-sm text-red-600">{error}</p>
								</div>
							)}

							<div className="grid gap-6 md:grid-cols-2">
								<FormField
									control={form.control}
									name="annee"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Année</FormLabel>
											<FormControl>
												<Input
													type="number"
													{...field}
													onChange={(e) =>
														field.onChange(parseInt(e.target.value) || 0)
													}
												/>
											</FormControl>
											<FormDescription>
												L'année de la session de tontine
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="partFixe"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="flex items-center gap-2">
												<DollarSign className="h-4 w-4" />
												Part Fixe (FCFA)
											</FormLabel>
											<FormControl>
												<Input
													type="number"
													{...field}
													onChange={(e) =>
														field.onChange(parseInt(e.target.value) || 0)
													}
												/>
											</FormControl>
											<FormDescription>
												Montant de la cotisation fixe par réunion
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<div className="grid gap-6 md:grid-cols-2">
								<FormField
									control={form.control}
									name="dateDebut"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Date de début</FormLabel>
											<FormControl>
												<Input type="date" {...field} />
											</FormControl>
											<FormDescription>
												Date de début de la session
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="dateFin"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Date de fin</FormLabel>
											<FormControl>
												<Input type="date" {...field} />
											</FormControl>
											<FormDescription>
												Date de fin de la session
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<div className="flex justify-end gap-4">
								<Button variant="outline" asChild>
									<Link href="/dashboard/sessions">Annuler</Link>
								</Button>
								<Button type="submit" disabled={isLoading}>
									{isLoading ? "Création..." : "Créer la session"}
								</Button>
							</div>
						</form>
					</Form>
				</CardContent>
			</Card>

			{/* Informations supplémentaires */}
			<Card>
				<CardHeader>
					<CardTitle>À propos des sessions</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-2 text-sm text-muted-foreground">
						<p>
							• Les réunions seront automatiquement créées pour chaque dimanche
							dans la période définie
						</p>
						<p>
							• La part fixe représente le montant que chaque membre doit
							cotiser à chaque réunion
						</p>
						<p>
							• Une fois créée, la session ne peut être modifiée que par un
							administrateur
						</p>
						<p>
							• La suppression d'une session supprimera également toutes les
							réunions associées
						</p>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
