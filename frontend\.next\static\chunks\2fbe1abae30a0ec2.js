(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,74332,28189,89683,e=>{"use strict";e.s(["createCollection",()=>d],74332);var t,r=e.i(61816),s=e.i(40003),i=e.i(76233),n=e.i(38477),a=e.i(1767),l=e.i(44636),o=e.i(81221),c=e.i(4051);function d(e){let t=e+"CollectionProvider",[r,s]=(0,a.createContextScope)(t),[i,d]=r(t,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:t,children:r}=e,s=n.default.useRef(null),a=n.default.useRef(new Map).current;return(0,c.jsx)(i,{scope:t,itemMap:a,collectionRef:s,children:r})};u.displayName=t;let h=e+"CollectionSlot",f=(0,o.createSlot)(h),m=n.default.forwardRef((e,t)=>{let{scope:r,children:s}=e,i=d(h,r),n=(0,l.useComposedRefs)(t,i.collectionRef);return(0,c.jsx)(f,{ref:n,children:s})});m.displayName=h;let p=e+"CollectionItemSlot",x="data-radix-collection-item",y=(0,o.createSlot)(p),b=n.default.forwardRef((e,t)=>{let{scope:r,children:s,...i}=e,a=n.default.useRef(null),o=(0,l.useComposedRefs)(t,a),u=d(p,r);return n.default.useEffect(()=>(u.itemMap.set(a,{ref:a,...i}),()=>void u.itemMap.delete(a))),(0,c.jsx)(y,{...{[x]:""},ref:o,children:s})});return b.displayName=p,[{Provider:u,Slot:m,ItemSlot:b},function(t){let r=d(e+"CollectionConsumer",t);return n.default.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(x,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},s]}var u=new WeakMap;function h(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,s=f(t),i=s>=0?s:r+s;return i<0||i>=r?-1:i}(e,t);return -1===r?void 0:e[r]}function f(e){return e!=e||0===e?0:Math.trunc(e)}t=new WeakMap,(class e extends Map{set(e,s){return u.get(this)&&(this.has(e)?(0,r._)(this,t)[(0,r._)(this,t).indexOf(e)]=e:(0,r._)(this,t).push(e)),super.set(e,s),this}insert(e,s,i){let n,a=this.has(s),l=(0,r._)(this,t).length,o=f(e),c=o>=0?o:l+o,d=c<0||c>=l?-1:c;if(d===this.size||a&&d===this.size-1||-1===d)return this.set(s,i),this;let u=this.size+ +!a;o<0&&c++;let h=[...(0,r._)(this,t)],m=!1;for(let e=c;e<u;e++)if(c===e){let t=h[e];h[e]===s&&(t=h[e+1]),a&&this.delete(s),n=this.get(t),this.set(s,i)}else{m||h[e-1]!==s||(m=!0);let t=h[m?e:e-1],r=n;n=this.get(t),this.delete(t),this.set(t,r)}return this}with(t,r,s){let i=new e(this);return i.insert(t,r,s),i}before(e){let s=(0,r._)(this,t).indexOf(e)-1;if(!(s<0))return this.entryAt(s)}setBefore(e,s,i){let n=(0,r._)(this,t).indexOf(e);return -1===n?this:this.insert(n,s,i)}after(e){let s=(0,r._)(this,t).indexOf(e);if(-1!==(s=-1===s||s===this.size-1?-1:s+1))return this.entryAt(s)}setAfter(e,s,i){let n=(0,r._)(this,t).indexOf(e);return -1===n?this:this.insert(n+1,s,i)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return(0,i._)(this,t,[]),super.clear()}delete(e){let s=super.delete(e);return s&&(0,r._)(this,t).splice((0,r._)(this,t).indexOf(e),1),s}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let s=h((0,r._)(this,t),e);if(void 0!==s)return this.get(s)}entryAt(e){let s=h((0,r._)(this,t),e);if(void 0!==s)return[s,this.get(s)]}indexOf(e){return(0,r._)(this,t).indexOf(e)}keyAt(e){return h((0,r._)(this,t),e)}from(e,t){let r=this.indexOf(e);if(-1===r)return;let s=r+t;return s<0&&(s=0),s>=this.size&&(s=this.size-1),this.at(s)}keyFrom(e,t){let r=this.indexOf(e);if(-1===r)return;let s=r+t;return s<0&&(s=0),s>=this.size&&(s=this.size-1),this.keyAt(s)}find(e,t){let r=0;for(let s of this){if(Reflect.apply(e,t,[s,r,this]))return s;r++}}findIndex(e,t){let r=0;for(let s of this){if(Reflect.apply(e,t,[s,r,this]))return r;r++}return -1}filter(t,r){let s=[],i=0;for(let e of this)Reflect.apply(t,r,[e,i,this])&&s.push(e),i++;return new e(s)}map(t,r){let s=[],i=0;for(let e of this)s.push([e[0],Reflect.apply(t,r,[e,i,this])]),i++;return new e(s)}reduce(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[s,i]=t,n=0,a=null!=i?i:this.at(0);for(let e of this)a=0===n&&1===t.length?e:Reflect.apply(s,this,[a,e,n,this]),n++;return a}reduceRight(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[s,i]=t,n=null!=i?i:this.at(-1);for(let e=this.size-1;e>=0;e--){let r=this.at(e);n=e===this.size-1&&1===t.length?r:Reflect.apply(s,this,[n,r,e,this])}return n}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let r=this.keyAt(e),s=this.get(r);t.set(r,s)}return t}toSpliced(){for(var t=arguments.length,r=Array(t),s=0;s<t;s++)r[s]=arguments[s];let i=[...this.entries()];return i.splice(...r),new e(i)}slice(t,r){let s=new e,i=this.size-1;if(void 0===t)return s;t<0&&(t+=this.size),void 0!==r&&r>0&&(i=r-1);for(let e=t;e<=i;e++){let t=this.keyAt(e),r=this.get(t);s.set(t,r)}return s}every(e,t){let r=0;for(let s of this){if(!Reflect.apply(e,t,[s,r,this]))return!1;r++}return!0}some(e,t){let r=0;for(let s of this){if(Reflect.apply(e,t,[s,r,this]))return!0;r++}return!1}constructor(e){super(e),(0,s._)(this,t,{writable:!0,value:void 0}),(0,i._)(this,t,[...super.keys()]),u.set(this,!0)}}),e.s(["useDirection",()=>p],28189);var m=n.createContext(void 0);function p(e){let t=n.useContext(m);return e||t||"ltr"}e.s(["CheckIcon",()=>x],89683);let x=(0,e.i(44571).default)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},44640,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ReflectAdapter",{enumerable:!0,get:function(){return s}});class s{static get(e,t,r){let s=Reflect.get(e,t,r);return"function"==typeof s?s.bind(e):s}static set(e,t,r,s){return Reflect.set(e,t,r,s)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},8356,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorageInstance",{enumerable:!0,get:function(){return s}});let s=(0,e.r(97918).createAsyncLocalStorage)()},62355,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorage",{enumerable:!0,get:function(){return s.afterTaskAsyncStorageInstance}});let s=e.r(8356)},17939,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{isRequestAPICallableInsideAfter:function(){return o},throwForSearchParamsAccessInUseCache:function(){return l},throwWithStaticGenerationBailoutError:function(){return n},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return a}});let s=e.r(85115),i=e.r(62355);function n(e,t){throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(e," couldn't be rendered statically because it used ").concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function a(e,t){throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(e,' with `dynamic = "error"` couldn\'t be rendered statically because it used ').concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function l(e,t){let r=Object.defineProperty(Error("Route ".concat(e.route,' used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(r,t),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=r),r}function o(){let e=i.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},93168,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{describeHasCheckingStringProperty:function(){return n},describeStringPropertyAccess:function(){return i},wellKnownProperties:function(){return a}});let s=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function i(e,t){return s.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function n(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let a=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},63470,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return o}});let s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var l=n?Object.getOwnPropertyDescriptor(e,a):null;l&&(l.get||l.set)?Object.defineProperty(s,a,l):s[a]=e[a]}return s.default=e,r&&r.set(e,s),s}(e.r(38477));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}let n={current:null},a="function"==typeof s.cache?s.cache:e=>e,l=console.warn;function o(e){return function(){for(var t=arguments.length,r=Array(t),s=0;s<t;s++)r[s]=arguments[s];l(e(...r))}}a(e=>{try{l(n.current)}finally{n.current=null}})},4467,e=>{"use strict";e.s(["useApi",()=>s]);var t=e.i(1269),r=e.i(1831);function s(){let{data:e}=(0,t.useSession)(),s=async function(t){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(null==e?void 0:e.accessToken))throw Error("Non authentifié");return r.apiService.authenticatedRequest(t,e.accessToken,s)};return{login:r.apiService.login.bind(r.apiService),register:r.apiService.register.bind(r.apiService),authenticatedRequest:s,getUsers:()=>s("/users"),getUser:e=>s("/users/".concat(e)),createUser:e=>s("/users",{method:"POST",body:JSON.stringify(e)}),updateUser:(e,t)=>s("/users/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteUser:e=>s("/users/".concat(e),{method:"DELETE"}),getSessions:()=>s("/sessions"),getSession:e=>s("/sessions/".concat(e)),createSession:e=>s("/sessions",{method:"POST",body:JSON.stringify(e)}),updateSession:(e,t)=>s("/sessions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteSession:e=>s("/sessions/".concat(e),{method:"DELETE"}),getCaisses:()=>s("/caisses"),getCaisse:e=>s("/caisses/".concat(e)),createCaisse:e=>s("/caisses",{method:"POST",body:JSON.stringify(e)}),updateCaisse:(e,t)=>s("/caisses/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteCaisse:e=>s("/caisses/".concat(e),{method:"DELETE"}),emargerCaisse:e=>s("/caisses/".concat(e,"/emarger"),{method:"POST"}),getReunions:()=>s("/reunions"),getReunion:e=>s("/reunions/".concat(e)),updateReunion:(e,t)=>s("/reunions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),getMembers:()=>s("/members"),getMember:e=>s("/members/".concat(e)),createMember:e=>s("/members",{method:"POST",body:JSON.stringify(e)}),updateMember:(e,t)=>s("/members/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteMember:e=>s("/members/".concat(e),{method:"DELETE"}),getMemberDebrief:(e,t)=>{let r=new URLSearchParams;(null==t?void 0:t.dateFrom)&&r.append("dateFrom",t.dateFrom),(null==t?void 0:t.dateTo)&&r.append("dateTo",t.dateTo),(null==t?void 0:t.sessionId)&&r.append("sessionId",t.sessionId);let i=r.toString()?"?".concat(r.toString()):"";return s("/members/".concat(e,"/debrief").concat(i))},createPayment:e=>s("/payments",{method:"POST",body:JSON.stringify(e)}),getSessionMembers:e=>s("/sessions/".concat(e,"/members")),addSessionMember:e=>s("/session-members",{method:"POST",body:JSON.stringify(e)}),removeSessionMember:(e,t)=>s("/sessions/".concat(e,"/members/").concat(t),{method:"DELETE"})}}},12058,e=>{"use strict";e.s(["CaisseType",()=>t,"PaymentDirection",()=>s,"PaymentFunction",()=>i,"UserRole",()=>r]);var t=function(e){return e.PRINCIPALE="PRINCIPALE",e.REUNION="REUNION",e}({}),r=function(e){return e.SECRETARY_GENERAL="secretary_general",e.CONTROLLER="controller",e.CASHIER="cashier",e}({}),s=function(e){return e.IN="IN",e.OUT="OUT",e}({}),i=function(e){return e.CONTRIBUTION="cotisation",e.TRANSFER="transfert",e.EXTERNAL="exterieur",e}({})},81787,e=>{"use strict";e.s(["Plus",()=>t],81787);let t=(0,e.i(44571).default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},2470,e=>{"use strict";e.s(["Badge",()=>a]);var t=e.i(4051),r=e.i(81221),s=e.i(62244),i=e.i(41428);let n=(0,s.cva)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function a(e){let{className:s,variant:a,asChild:l=!1,...o}=e,c=l?r.Slot:"span";return(0,t.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(n({variant:a}),s),...o})}},27973,e=>{"use strict";e.s(["Trash2",()=>t],27973);let t=(0,e.i(44571).default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},41850,e=>{"use strict";e.s(["Table",()=>s,"TableBody",()=>n,"TableCell",()=>o,"TableHead",()=>l,"TableHeader",()=>i,"TableRow",()=>a]);var t=e.i(4051),r=e.i(41428);function s(e){let{className:s,...i}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",s),...i})})}function i(e){let{className:s,...i}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",s),...i})}function n(e){let{className:s,...i}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",s),...i})}function a(e){let{className:s,...i}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...i})}function l(e){let{className:s,...i}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...i})}function o(e){let{className:s,...i}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...i})}},53917,80712,23738,e=>{"use strict";e.s(["Mail",()=>r],53917);var t=e.i(44571);let r=(0,t.default)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);e.s(["Phone",()=>s],80712);let s=(0,t.default)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);e.s(["MapPin",()=>i],23738);let i=(0,t.default)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},15592,e=>{"use strict";e.s(["default",()=>N],15592);var t=e.i(4051),r=e.i(38477),s=e.i(1269),i=e.i(81787),n=e.i(60019),a=e.i(3840),l=e.i(4818),o=e.i(27973);let c=(0,e.i(44571).default)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var d=e.i(53917),u=e.i(80712),h=e.i(23738),f=e.i(5085),m=e.i(85205),p=e.i(96134),x=e.i(75680),y=e.i(41850),b=e.i(35369),g=e.i(2470),v=e.i(4467),j=e.i(12058);function N(){let{data:e}=(0,s.useSession)(),N=(0,v.useApi)(),[C,w]=(0,r.useState)([]),[S,T]=(0,r.useState)(null),[R,O]=(0,r.useState)(!0),[E,A]=(0,r.useState)(""),P=(null==e?void 0:e.user)&&(e.user.role===j.UserRole.SECRETARY_GENERAL||e.user.role===j.UserRole.CONTROLLER),_=(null==e?void 0:e.user)&&e.user.role===j.UserRole.SECRETARY_GENERAL,M=async()=>{try{O(!0);let[e]=await Promise.all([N.getMembers()]),t=e;if(E){let r=E.toLowerCase();t=e.filter(e=>{var t,s;return e.firstName.toLowerCase().includes(r)||e.lastName.toLowerCase().includes(r)||(null==(t=e.email)?void 0:t.toLowerCase().includes(r))||(null==(s=e.phone)?void 0:s.includes(r))})}w(t);let r={total:e.length,withEmail:e.filter(e=>e.email).length,withPhone:e.filter(e=>e.phone).length,withAddress:e.filter(e=>e.address).length};T(r)}catch(e){console.error("Erreur lors du chargement des données:",e)}finally{O(!1)}};(0,r.useEffect)(()=>{(null==e?void 0:e.accessToken)&&M()},[e,E]);let k=async e=>{if(confirm("Êtes-vous sûr de vouloir supprimer ce membre ?"))try{await N.deleteMember(e),M()}catch(e){console.error("Erreur lors de la suppression:",e)}};return P?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Gestion des Membres"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Gérez les membres de votre tontine"})]}),_&&(0,t.jsx)(f.default,{href:"/dashboard/members/new",children:(0,t.jsxs)(m.Button,{children:[(0,t.jsx)(i.Plus,{className:"h-4 w-4 mr-2"}),"Nouveau membre"]})})]}),S&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)(x.Card,{children:[(0,t.jsx)(x.CardHeader,{className:"pb-2",children:(0,t.jsx)(x.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Total Membres"})}),(0,t.jsx)(x.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:S.total})})]}),(0,t.jsxs)(x.Card,{children:[(0,t.jsx)(x.CardHeader,{className:"pb-2",children:(0,t.jsx)(x.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Avec Email"})}),(0,t.jsx)(x.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:S.withEmail})})]}),(0,t.jsxs)(x.Card,{children:[(0,t.jsx)(x.CardHeader,{className:"pb-2",children:(0,t.jsx)(x.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Avec Téléphone"})}),(0,t.jsx)(x.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:S.withPhone})})]}),(0,t.jsxs)(x.Card,{children:[(0,t.jsx)(x.CardHeader,{className:"pb-2",children:(0,t.jsx)(x.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Avec Adresse"})}),(0,t.jsx)(x.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:S.withAddress})})]})]}),(0,t.jsxs)(x.Card,{children:[(0,t.jsx)(x.CardHeader,{children:(0,t.jsx)(x.CardTitle,{children:"Recherche"})}),(0,t.jsx)(x.CardContent,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.Search,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,t.jsx)(p.Input,{placeholder:"Rechercher par nom, prénom, email, téléphone...",value:E,onChange:e=>A(e.target.value),className:"pl-10"})]})})]}),(0,t.jsxs)(x.Card,{children:[(0,t.jsxs)(x.CardHeader,{children:[(0,t.jsx)(x.CardTitle,{children:"Liste des Membres"}),(0,t.jsxs)(x.CardDescription,{children:[C.length," membre(s) trouvé(s)"]})]}),(0,t.jsx)(x.CardContent,{children:R?(0,t.jsx)("div",{className:"flex justify-center py-8",children:(0,t.jsx)("div",{className:"text-gray-500",children:"Chargement..."})}):(0,t.jsxs)(y.Table,{children:[(0,t.jsx)(y.TableHeader,{children:(0,t.jsxs)(y.TableRow,{children:[(0,t.jsx)(y.TableHead,{children:"Nom"}),(0,t.jsx)(y.TableHead,{children:"Contact"}),(0,t.jsx)(y.TableHead,{children:"Adresse"}),(0,t.jsx)(y.TableHead,{children:"Informations"}),_&&(0,t.jsx)(y.TableHead,{children:"Actions"})]})}),(0,t.jsx)(y.TableBody,{children:C.map(e=>(0,t.jsxs)(y.TableRow,{children:[(0,t.jsx)(y.TableCell,{children:(0,t.jsxs)("div",{className:"font-medium",children:[e.firstName," ",e.lastName]})}),(0,t.jsx)(y.TableCell,{children:(0,t.jsxs)("div",{className:"space-y-1",children:[e.email&&(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)(d.Mail,{className:"h-3 w-3 mr-1"}),e.email]}),e.phone&&(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)(u.Phone,{className:"h-3 w-3 mr-1"}),e.phone]})]})}),(0,t.jsx)(y.TableCell,{children:e.address&&(0,t.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)(h.MapPin,{className:"h-3 w-3 mr-1"}),e.address]})}),(0,t.jsx)(y.TableCell,{children:(0,t.jsxs)("div",{className:"flex gap-1",children:[e.email&&(0,t.jsx)(g.Badge,{variant:"secondary",children:"Email"}),e.phone&&(0,t.jsx)(g.Badge,{variant:"secondary",children:"Tél"}),e.address&&(0,t.jsx)(g.Badge,{variant:"secondary",children:"Adresse"})]})}),_&&(0,t.jsx)(y.TableCell,{children:(0,t.jsxs)(b.DropdownMenu,{children:[(0,t.jsx)(b.DropdownMenuTrigger,{asChild:!0,children:(0,t.jsx)(m.Button,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,t.jsx)(a.MoreHorizontal,{className:"h-4 w-4"})})}),(0,t.jsxs)(b.DropdownMenuContent,{align:"end",children:[(0,t.jsx)(b.DropdownMenuLabel,{children:"Actions"}),(0,t.jsx)(b.DropdownMenuItem,{asChild:!0,children:(0,t.jsxs)(f.default,{href:"/dashboard/members/".concat(e._id),children:[(0,t.jsx)(c,{className:"h-4 w-4 mr-2"}),"Voir détails"]})}),(0,t.jsx)(b.DropdownMenuItem,{asChild:!0,children:(0,t.jsxs)(f.default,{href:"/dashboard/members/".concat(e._id,"/edit"),children:[(0,t.jsx)(l.Edit,{className:"h-4 w-4 mr-2"}),"Modifier"]})}),(0,t.jsx)(b.DropdownMenuSeparator,{}),(0,t.jsxs)(b.DropdownMenuItem,{onClick:()=>k(e._id),className:"text-red-600",children:[(0,t.jsx)(o.Trash2,{className:"h-4 w-4 mr-2"}),"Supprimer"]})]})]})})]},e._id))})]})})]})]}):(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Accès refusé"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Vous n'avez pas les permissions pour accéder à cette page."})]})})}}]);