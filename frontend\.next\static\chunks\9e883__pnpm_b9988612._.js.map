{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/lucide-react%400.542.0_react%4019.1.0/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAY,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAY,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC3C;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,IAAA,CAAA,CAAA,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iQAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ellipsis.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/lucide-react%400.542.0_react%4019.1.0/node_modules/lucide-react/src/icons/ellipsis.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '1', key: '41hilf' }],\n  ['circle', { cx: '19', cy: '12', r: '1', key: '1wjl8i' }],\n  ['circle', { cx: '5', cy: '12', r: '1', key: '1pcz8c' }],\n];\n\n/**\n * @component @name Ellipsis\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxIiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iMTIiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iNSIgY3k9IjEyIiByPSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/ellipsis\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Ellipsis = createLucideIcon('ellipsis', __iconNode);\n\nexport default Ellipsis;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,QAAA,CAAA;QAAU,CAAA;YAAE,EAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAA,CAAI,IAAA,CAAA;YAAM,CAAA,EAAG,CAAA,CAAA,CAAA,CAAA;YAAK,GAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,QAAA,CAAA;QAAU,CAAA;YAAE,EAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAA,CAAI,IAAA,CAAA;YAAM,CAAA,EAAG,CAAA,CAAA,CAAA,CAAA;YAAK,GAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU;YAAE,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA;YAAK,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA;YAAK,CAAA,CAAA,CAAA,CAAA,CAAK,QAAA;QAAA,CAAU;KAAA;CACzD;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,QAAA,CAAA,CAAA,KAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iQAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/lucide-react%400.542.0_react%4019.1.0/node_modules/lucide-react/src/icons/square-pen.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7', key: '1m0v6g' }],\n  [\n    'path',\n    {\n      d: 'M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z',\n      key: 'ohrbg2',\n    },\n  ],\n];\n\n/**\n * @component @name SquarePen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM0g1YTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxNGEyIDIgMCAwIDAgMi0ydi03IiAvPgogIDxwYXRoIGQ9Ik0xOC4zNzUgMi42MjVhMSAxIDAgMCAxIDMgM2wtOS4wMTMgOS4wMTRhMiAyIDAgMCAxLS44NTMuNTA1bC0yLjg3My44NGEuNS41IDAgMCAxLS42Mi0uNjJsLjg0LTIuODczYTIgMiAwIDAgMSAuNTA2LS44NTJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square-pen\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SquarePen = createLucideIcon('square-pen', __iconNode);\n\nexport default SquarePen;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA8D,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC3F;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,SAAA,CAAA,CAAA,KAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iQAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/lucide-react%400.542.0_react%4019.1.0/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 11v6', key: 'nco0om' }],\n  ['path', { d: 'M14 11v6', key: 'outv1u' }],\n  ['path', { d: 'M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6', key: 'miytrc' }],\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2', key: 'e791ji' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTF2NiIgLz4KICA8cGF0aCBkPSJNMTQgMTF2NiIgLz4KICA8cGF0aCBkPSJNMTkgNnYxNGEyIDIgMCAwIDEtMiAySDdhMiAyIDAgMCAxLTItMlY2IiAvPgogIDxwYXRoIGQ9Ik0zIDZoMTgiIC8+CiAgPHBhdGggZD0iTTggNlY0YTIgMiAwIDAgMSAyLTJoNGEyIDIgMCAwIDEgMiAydjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAY,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAY,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA4C,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAW,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA0C,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzE;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,MAAA,CAAA,CAAA,KAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iQAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/dollar-sign.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/lucide-react%400.542.0_react%4019.1.0/node_modules/lucide-react/src/icons/dollar-sign.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,EAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA;YAAK,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAqD,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACpF;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,KAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iQAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/check.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/lucide-react%400.542.0_react%4019.1.0/node_modules/lucide-react/src/icons/check.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M20 6 9 17l-5-5', key: '1gmf2c' }]];\n\n/**\n * @component @name Check\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgNiA5IDE3bC01LTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Check = createLucideIcon('check', __iconNode);\n\nexport default Check;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,iBAAA,CAAA;YAAmB,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAC;CAAA;AAatF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,KAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iQAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/lucide-react%400.542.0_react%4019.1.0/node_modules/lucide-react/src/icons/chevron-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm9 18 6-6-6-6', key: 'mthhwq' }]];\n\n/**\n * @component @name ChevronRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSAxOCA2LTYtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRight = createLucideIcon('chevron-right', __iconNode);\n\nexport default ChevronRight;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,eAAA,CAAA;YAAiB,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAC;CAAA;AAapF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,YAAA,CAAA,CAAA,KAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iQAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/lucide-react%400.542.0_react%4019.1.0/node_modules/lucide-react/src/icons/circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }]];\n\n/**\n * @component @name Circle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Circle = createLucideIcon('circle', __iconNode);\n\nexport default Circle;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAuB;IAAC;QAAC,QAAA,CAAA;QAAU,CAAA;YAAE,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,EAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAC;CAAA;AAa/F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,MAAA,CAAA,CAAA,KAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iQAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/lucide-react%400.542.0_react%4019.1.0/node_modules/lucide-react/src/icons/chevron-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }]];\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('chevron-down', __iconNode);\n\nexport default ChevronDown;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,cAAA,CAAA;YAAgB,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAC;CAAA;AAanF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,WAAA,CAAA,CAAA,KAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iQAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-up.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/lucide-react%400.542.0_react%4019.1.0/node_modules/lucide-react/src/icons/chevron-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm18 15-6-6-6 6', key: '153udz' }]];\n\n/**\n * @component @name ChevronUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTggMTUtNi02LTYgNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronUp = createLucideIcon('chevron-up', __iconNode);\n\nexport default ChevronUp;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,gBAAA,CAAA;YAAkB,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAC;CAAA;AAarF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,SAAA,CAAA,CAAA,KAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iQAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/esm/_class_apply_descriptor_get.js"], "sourcesContent": ["function _class_apply_descriptor_get(receiver, descriptor) {\n    if (descriptor.get) return descriptor.get.call(receiver);\n\n    return descriptor.value;\n}\nexport { _class_apply_descriptor_get as _ };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,4BAA4B,QAAQ,EAAE,UAAU;IACrD,IAAI,WAAW,GAAG,EAAE,OAAO,WAAW,GAAG,CAAC,IAAI,CAAC;IAE/C,OAAO,WAAW,KAAK;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/esm/_class_extract_field_descriptor.js"], "sourcesContent": ["function _class_extract_field_descriptor(receiver, privateMap, action) {\n    if (!privateMap.has(receiver)) throw new TypeError(\"attempted to \" + action + \" private field on non-instance\");\n\n    return privateMap.get(receiver);\n}\nexport { _class_extract_field_descriptor as _ };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,gCAAgC,QAAQ,EAAE,UAAU,EAAE,MAAM;IACjE,IAAI,CAAC,WAAW,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU,kBAAkB,SAAS;IAE9E,OAAO,WAAW,GAAG,CAAC;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/esm/_class_private_field_get.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_get } from \"./_class_apply_descriptor_get.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_get(receiver, privateMap) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"get\");\n    return _class_apply_descriptor_get(receiver, descriptor);\n}\nexport { _class_private_field_get as _ };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,SAAS,yBAAyB,QAAQ,EAAE,UAAU;IAClD,IAAI,aAAa,IAAA,4PAA+B,EAAC,UAAU,YAAY;IACvE,OAAO,IAAA,wPAA2B,EAAC,UAAU;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/esm/_check_private_redeclaration.js"], "sourcesContent": ["function _check_private_redeclaration(obj, privateCollection) {\n    if (privateCollection.has(obj)) {\n        throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n    }\n}\nexport { _check_private_redeclaration as _ };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,6BAA6B,GAAG,EAAE,iBAAiB;IACxD,IAAI,kBAAkB,GAAG,CAAC,MAAM;QAC5B,MAAM,IAAI,UAAU;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/esm/_class_private_field_init.js"], "sourcesContent": ["import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_field_init(obj, privateMap, value) {\n    _check_private_redeclaration(obj, privateMap);\n    privateMap.set(obj, value);\n}\nexport { _class_private_field_init as _ };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,SAAS,0BAA0B,GAAG,EAAE,UAAU,EAAE,KAAK;IACrD,IAAA,yPAA4B,EAAC,KAAK;IAClC,WAAW,GAAG,CAAC,KAAK;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/esm/_class_apply_descriptor_set.js"], "sourcesContent": ["function _class_apply_descriptor_set(receiver, descriptor, value) {\n    if (descriptor.set) descriptor.set.call(receiver, value);\n    else {\n        if (!descriptor.writable) {\n            // This should only throw in strict mode, but class bodies are\n            // always strict and private fields can only be used inside\n            // class bodies.\n            throw new TypeError(\"attempted to set read only private field\");\n        }\n        descriptor.value = value;\n    }\n}\nexport { _class_apply_descriptor_set as _ };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,4BAA4B,QAAQ,EAAE,UAAU,EAAE,KAAK;IAC5D,IAAI,WAAW,GAAG,EAAE,WAAW,GAAG,CAAC,IAAI,CAAC,UAAU;SAC7C;QACD,IAAI,CAAC,WAAW,QAAQ,EAAE;YACtB,8DAA8D;YAC9D,2DAA2D;YAC3D,gBAAgB;YAChB,MAAM,IAAI,UAAU;QACxB;QACA,WAAW,KAAK,GAAG;IACvB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/esm/_class_private_field_set.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_set } from \"./_class_apply_descriptor_set.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_set(receiver, privateMap, value) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"set\");\n    _class_apply_descriptor_set(receiver, descriptor, value);\n    return value;\n}\nexport { _class_private_field_set as _ };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,SAAS,yBAAyB,QAAQ,EAAE,UAAU,EAAE,KAAK;IACzD,IAAI,aAAa,IAAA,4PAA+B,EAAC,UAAU,YAAY;IACvE,IAAA,wPAA2B,EAAC,UAAU,YAAY;IAClD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/esm/_define_property.js"], "sourcesContent": ["function _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });\n    } else obj[key] = value;\n\n    return obj;\n}\nexport { _define_property as _ };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,iBAAiB,GAAG,EAAE,GAAG,EAAE,KAAK;IACrC,IAAI,OAAO,KAAK;QACZ,OAAO,cAAc,CAAC,KAAK,KAAK;YAAE,OAAO;YAAO,YAAY;YAAM,cAAc;YAAM,UAAU;QAAK;IACzG,OAAO,GAAG,CAAC,IAAI,GAAG;IAElB,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/esm/_class_private_method_get.js"], "sourcesContent": ["function _class_private_method_get(receiver, privateSet, fn) {\n    if (!privateSet.has(receiver)) throw new TypeError(\"attempted to get private field on non-instance\");\n\n    return fn;\n}\nexport { _class_private_method_get as _ };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,0BAA0B,QAAQ,EAAE,UAAU,EAAE,EAAE;IACvD,IAAI,CAAC,WAAW,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IAEnD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/esm/_class_private_method_init.js"], "sourcesContent": ["import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_method_init(obj, privateSet) {\n    _check_private_redeclaration(obj, privateSet);\n    privateSet.add(obj);\n}\nexport { _class_private_method_init as _ };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,SAAS,2BAA2B,GAAG,EAAE,UAAU;IAC/C,IAAA,yPAA4B,EAAC,KAAK;IAClC,WAAW,GAAG,CAAC;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 617, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40radix-ui%2Breact-collection%40_579cf419d1ba9c23c3c58a897c3f75b1/node_modules/%40radix-ui/react-collection/src/collection-legacy.tsx", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40radix-ui%2Breact-collection%40_579cf419d1ba9c23c3c58a897c3f75b1/node_modules/%40radix-ui/react-collection/src/collection.tsx", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40radix-ui%2Breact-collection%40_579cf419d1ba9c23c3c58a897c3f75b1/node_modules/%40radix-ui/react-collection/src/ordered-dictionary.ts"], "sourcesContent": ["import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement | null>;\n    itemMap: Map<\n      React.RefObject<ItemElement | null>,\n      { ref: React.RefObject<ItemElement | null> } & ItemData\n    >;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\nimport type { EntryOf } from './ordered-dictionary';\nimport { OrderedDict } from './ordered-dictionary';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\ninterface BaseItemData {\n  id?: string;\n}\n\ntype ItemDataWithElement<\n  ItemData extends BaseItemData,\n  ItemElement extends HTMLElement,\n> = ItemData & {\n  element: ItemElement;\n};\n\ntype ItemMap<ItemElement extends HTMLElement, ItemData extends BaseItemData> = OrderedDict<\n  ItemElement,\n  ItemDataWithElement<ItemData, ItemElement>\n>;\n\nfunction createCollection<\n  ItemElement extends HTMLElement,\n  ItemData extends BaseItemData = BaseItemData,\n>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionElement: CollectionElement | null;\n    collectionRef: React.Ref<CollectionElement | null>;\n    collectionRefObject: React.RefObject<CollectionElement | null>;\n    itemMap: ItemMap<ItemElement, ItemData>;\n    setItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>;\n  };\n\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0,\n    }\n  );\n\n  type CollectionState = [\n    ItemMap: ItemMap<ItemElement, ItemData>,\n    SetItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>,\n  ];\n\n  const CollectionProvider: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state?: CollectionState;\n  }> = ({ state, ...props }) => {\n    return state ? (\n      <CollectionProviderImpl {...props} state={state} />\n    ) : (\n      <CollectionInit {...props} />\n    );\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  const CollectionInit: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n  }> = (props) => {\n    const state = useInitCollection();\n    return <CollectionProviderImpl {...props} state={state} />;\n  };\n  CollectionInit.displayName = PROVIDER_NAME + 'Init';\n\n  const CollectionProviderImpl: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state: CollectionState;\n  }> = (props) => {\n    const { scope, children, state } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const [collectionElement, setCollectionElement] = React.useState<CollectionElement | null>(\n      null\n    );\n    const composeRefs = useComposedRefs(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n\n    React.useEffect(() => {\n      if (!collectionElement) return;\n\n      const observer = getChildListObserver(() => {\n        // setItemMap((map) => {\n        //   const copy = new OrderedDict(map).toSorted(([, a], [, b]) =>\n        //     !a.element || !b.element ? 0 : isElementPreceding(a.element, b.element) ? -1 : 1\n        //   );\n        //   // check if the order has changed\n        //   let index = -1;\n        //   for (const entry of copy) {\n        //     index++;\n        //     const key = map.keyAt(index)!;\n        //     const [copyKey] = entry;\n        //     if (key !== copyKey) {\n        //       // order has changed!\n        //       return copy;\n        //     }\n        //   }\n        //   return map;\n        // });\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true,\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n\n    return (\n      <CollectionContextProvider\n        scope={scope}\n        itemMap={itemMap}\n        setItemMap={setItemMap}\n        collectionRef={composeRefs}\n        collectionRefObject={ref}\n        collectionElement={collectionElement}\n      >\n        {children}\n      </CollectionContextProvider>\n    );\n  };\n\n  CollectionProviderImpl.displayName = PROVIDER_NAME + 'Impl';\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const [element, setElement] = React.useState<ItemElement | null>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      const { setItemMap } = context;\n\n      const itemDataRef = React.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n\n      React.useEffect(() => {\n        const itemData = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n\n          if (!map.has(element)) {\n            map.set(element, { ...(itemData as unknown as ItemData), element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n\n          return map\n            .set(element, { ...(itemData as unknown as ItemData), element })\n            .toSorted(sortByDocumentPosition);\n        });\n\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs as any}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useInitCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useInitCollection() {\n    return React.useState<ItemMap<ItemElement, ItemData>>(new OrderedDict());\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const { itemMap } = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    return itemMap;\n  }\n\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection,\n  };\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n\nfunction shallowEqual(a: any, b: any) {\n  if (a === b) return true;\n  if (typeof a !== 'object' || typeof b !== 'object') return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\n\nfunction isElementPreceding(a: Element, b: Element) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\n\nfunction sortByDocumentPosition<E extends HTMLElement, T extends BaseItemData>(\n  a: EntryOf<ItemMap<E, T>>,\n  b: EntryOf<ItemMap<E, T>>\n) {\n  return !a[1].element || !b[1].element\n    ? 0\n    : isElementPreceding(a[1].element, b[1].element)\n      ? -1\n      : 1;\n}\n\nfunction getChildListObserver(callback: () => void) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === 'childList') {\n        callback();\n        return;\n      }\n    }\n  });\n\n  return observer;\n}\n", "// Not a real member because it shouldn't be accessible, but the super class\n// calls `set` which needs to read the instanciation state, so it can't be a\n// private member.\nconst __instanciated = new WeakMap<OrderedDict<any, any>, boolean>();\nexport class OrderedDict<K, V> extends Map<K, V> {\n  #keys: K[];\n\n  constructor(iterable?: Iterable<readonly [K, V]> | null | undefined);\n  constructor(entries?: readonly (readonly [K, V])[] | null) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n\n  set(key: K, value: V) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n\n  insert(index: number, key: K, value: V) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n\n    if (safeIndex === this.size || (has && safeIndex === this.size - 1) || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n\n    const size = this.size + (has ? 0 : 1);\n\n    // If you insert at, say, -2, without this bit you'd replace the\n    // second-to-last item and push the rest up one, which means the new item is\n    // 3rd to last. This isn't very intuitive; inserting at -2 is more like\n    // saying \"make this item the second to last\".\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n\n    const keys = [...this.#keys];\n    let nextValue: V | undefined;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i]!;\n        if (keys[i] === key) {\n          nextKey = keys[i + 1]!;\n        }\n        if (has) {\n          // delete first to ensure that the item is moved to the end\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1]!;\n        const currentValue = nextValue!;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n\n  with(index: number, key: K, value: V) {\n    const copy = new OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n\n  before(key: K) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n\n  after(key: K) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n\n  first() {\n    return this.entryAt(0);\n  }\n\n  last() {\n    return this.entryAt(-1);\n  }\n\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n\n  delete(key: K) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n\n  deleteAt(index: number) {\n    const key = this.keyAt(index);\n    if (key !== undefined) {\n      return this.delete(key);\n    }\n    return false;\n  }\n\n  at(index: number) {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return this.get(key);\n    }\n  }\n\n  entryAt(index: number): [K, V] | undefined {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return [key, this.get(key)!];\n    }\n  }\n\n  indexOf(key: K) {\n    return this.#keys.indexOf(key);\n  }\n\n  keyAt(index: number) {\n    return at(this.#keys, index);\n  }\n\n  from(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n\n  keyFrom(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n\n  find(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return undefined;\n  }\n\n  findIndex(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n\n  filter<KK extends K, VV extends V>(\n    predicate: (entry: [K, V], index: number, dict: OrderedDict<K, V>) => entry is [KK, VV],\n    thisArg?: any\n  ): OrderedDict<KK, VV>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ): OrderedDict<K, V>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    const entries: Array<[K, V]> = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  map<U>(\n    callbackfn: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => U,\n    thisArg?: any\n  ): OrderedDict<K, U> {\n    const entries: [K, U][] = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduce<U>(\n    callbackfn: (\n      previousValue: U,\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduce<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0)!;\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduceRight<U>(\n    callbackfn: (\n      previousValue: [K, V],\n      currentValue: U,\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduceRight<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1)!;\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index)!;\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n\n  toSorted(compareFn?: (a: [K, V], b: [K, V]) => number): OrderedDict<K, V> {\n    const entries = [...this.entries()].sort(compareFn);\n    return new OrderedDict(entries);\n  }\n\n  toReversed(): OrderedDict<K, V> {\n    const reversed = new OrderedDict<K, V>();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n\n  toSpliced(start: number, deleteCount?: number): OrderedDict<K, V>;\n  toSpliced(start: number, deleteCount: number, ...items: [K, V][]): OrderedDict<K, V>;\n\n  toSpliced(...args: [start: number, deleteCount: number, ...items: [K, V][]]) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new OrderedDict(entries);\n  }\n\n  slice(start?: number, end?: number) {\n    const result = new OrderedDict<K, V>();\n    let stop = this.size - 1;\n\n    if (start === undefined) {\n      return result;\n    }\n\n    if (start < 0) {\n      start = start + this.size;\n    }\n\n    if (end !== undefined && end > 0) {\n      stop = end - 1;\n    }\n\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      result.set(key, element);\n    }\n    return result;\n  }\n\n  every(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n\n  some(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n}\n\nexport type KeyOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<infer K, any> ? K : never;\nexport type ValueOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<any, infer V> ? V : never;\nexport type EntryOf<D extends OrderedDict<any, any>> = [KeyOf<D>, ValueOf<D>];\nexport type KeyFrom<E extends EntryOf<any>> = E[0];\nexport type ValueFrom<E extends EntryOf<any>> = E[1];\n\nfunction at<T>(array: ArrayLike<T>, index: number): T | undefined {\n  if ('at' in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? undefined : array[actualIndex];\n}\n\nfunction toSafeIndex(array: ArrayLike<any>, index: number) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\n\nfunction toSafeInteger(number: number) {\n  // eslint-disable-next-line no-self-compare\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n"], "names": ["React", "createContextScope", "useComposedRefs", "createSlot", "jsx", "createCollection", "createContextScope", "React", "useComposedRefs", "createSlot", "itemData"], "mappings": ";;;;;;;;;;AAAA,OAAO,WAAW;AAClB,SAAS,0BAA0B;AACnC,SAAS,uBAAuB;AAChC,SAAS,kBAA6B;AAuChC;;;;;;;;;;;AA1BN,SAAS,iBAAiE,IAAA,EAAc;IAKtF,MAAM,gBAAgB,OAAO;IAC7B,MAAM,CAAC,yBAAyB,qBAAqB,CAAA,OAAI,kTAAA,EAAmB,aAAa;IAUzF,MAAM,CAAC,wBAAwB,oBAAoB,CAAA,GAAI,wBACrD,eACA;QAAE,eAAe;YAAE,SAAS;QAAK;QAAG,SAAS,aAAA,GAAA,IAAI,IAAI;IAAE;IAGzD,MAAM,qBAA2E,CAAC,UAAU;QAC1F,MAAM,EAAE,KAAA,EAAO,QAAA,CAAS,CAAA,GAAI;QAC5B,MAAM,MAAM,mTAAA,CAAM,MAAA,CAA0B,IAAI;QAChD,MAAM,UAAU,mTAAA,CAAM,MAAA,CAAgC,aAAA,GAAA,IAAI,IAAI,CAAC,EAAE,OAAA;QACjE,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,wBAAA;YAAuB;YAAc;YAAkB,eAAe;YACpE;QAAA,CACH;IAEJ;IAEA,mBAAmB,WAAA,GAAc;IAMjC,MAAM,uBAAuB,OAAO;IAEpC,MAAM,yBAAqB,kUAAA,EAAW,oBAAoB;IAC1D,MAAM,iBAAiB,mTAAA,CAAM,UAAA,CAC3B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,CAAS,CAAA,GAAI;QAC5B,MAAM,UAAU,qBAAqB,sBAAsB,KAAK;QAChE,MAAM,mBAAe,oTAAA,EAAgB,cAAc,QAAQ,aAAa;QACxE,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAC,oBAAA;YAAmB,KAAK;YAAe;QAAA,CAAS;IAC1D;IAGF,eAAe,WAAA,GAAc;IAM7B,MAAM,iBAAiB,OAAO;IAC9B,MAAM,iBAAiB;IAOvB,MAAM,yBAAyB,sUAAA,EAAW,cAAc;IACxD,MAAM,qBAAqB,mTAAA,CAAM,UAAA,CAC/B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,SAAS,CAAA,GAAI;QACzC,MAAM,MAAM,mTAAA,CAAM,MAAA,CAAoB,IAAI;QAC1C,MAAM,mBAAe,oTAAA,EAAgB,cAAc,GAAG;QACtD,MAAM,UAAU,qBAAqB,gBAAgB,KAAK;QAE1D,mTAAA,CAAM,SAAA;6DAAU,MAAM;gBACpB,QAAQ,OAAA,CAAQ,GAAA,CAAI,KAAK;oBAAE;oBAAK,GAAI,QAAA;gBAAiC,CAAC;gBACtE;qEAAO,IAAM,KAAK,QAAQ,OAAA,CAAQ,MAAA,CAAO,GAAG;;YAC9C,CAAC;;QAED,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,wBAAA;YAAwB,GAAG;gBAAE,CAAC,cAAc,CAAA,EAAG;YAAG,CAAA;YAAG,KAAK;YACxD;QAAA,CACH;IAEJ;IAGF,mBAAmB,WAAA,GAAc;IAMjC,SAAS,cAAc,KAAA,EAAY;QACjC,MAAM,UAAU,qBAAqB,OAAO,sBAAsB,KAAK;QAEvE,MAAM,WAAW,mTAAA,CAAM,WAAA;oEAAY,MAAM;gBACvC,MAAM,iBAAiB,QAAQ,aAAA,CAAc,OAAA;gBAC7C,IAAI,CAAC,eAAgB,CAAA,OAAO,CAAC,CAAA;gBAC7B,MAAM,eAAe,MAAM,IAAA,CAAK,eAAe,gBAAA,CAAiB,IAAkB,OAAd,cAAc,EAAA,EAAG,CAAC;gBACtF,MAAM,QAAQ,MAAM,IAAA,CAAK,QAAQ,OAAA,CAAQ,MAAA,CAAO,CAAC;gBACjD,MAAM,eAAe,MAAM,IAAA;yFACzB,CAAC,GAAG,IAAM,aAAa,OAAA,CAAQ,EAAE,GAAA,CAAI,OAAQ,IAAI,aAAa,OAAA,CAAQ,EAAE,GAAA,CAAI,OAAQ;;gBAEtF,OAAO;YACT;mEAAG;YAAC,QAAQ,aAAA;YAAe,QAAQ,OAAO;SAAC;QAE3C,OAAO;IACT;IAEA,OAAO;QACL;YAAE,UAAU;YAAoB,MAAM;YAAgB,UAAU;QAAmB;QACnF;QACA;KACF;AACF;;;;;;AE9HA,IAAM,iBAAiB,aAAA,GAAA,IAAI,QAAwC;AAC5D,IAAM,oDAAN,MAAM,qBAA0B,IAAU;IAU/C,IAAI,GAAA,EAAQ,KAAA,EAAU;QACpB,IAAI,eAAe,GAAA,CAAI,IAAI,GAAG;YAC5B,IAAI,IAAA,CAAK,GAAA,CAAI,GAAG,GAAG;gBACjB,2PAAA,IAAA,EAAK,MAAA,4PAAM,IAAA,EAAK,OAAM,OAAA,CAAQ,GAAG,CAAC,CAAA,GAAI;YACxC,OAAO;gBACL,2PAAA,IAAA,EAAK,OAAM,IAAA,CAAK,GAAG;YACrB;QACF;QACA,KAAA,CAAM,IAAI,KAAK,KAAK;QACpB,OAAO,IAAA;IACT;IAEA,OAAO,KAAA,EAAe,GAAA,EAAQ,KAAA,EAAU;QACtC,MAAM,MAAM,IAAA,CAAK,GAAA,CAAI,GAAG;QACxB,MAAM,oQAAS,IAAA,EAAK,OAAM,MAAA;QAC1B,MAAM,gBAAgB,cAAc,KAAK;QACzC,IAAI,cAAc,iBAAiB,IAAI,gBAAgB,SAAS;QAChE,MAAM,YAAY,cAAc,KAAK,eAAe,SAAS,CAAA,IAAK;QAElE,IAAI,cAAc,IAAA,CAAK,IAAA,IAAS,OAAO,cAAc,IAAA,CAAK,IAAA,GAAO,KAAM,cAAc,CAAA,GAAI;YACvF,IAAA,CAAK,GAAA,CAAI,KAAK,KAAK;YACnB,OAAO,IAAA;QACT;QAEA,MAAM,OAAO,IAAA,CAAK,IAAA,GAAA,CAAQ,MAAM,IAAI,CAAA;QAMpC,IAAI,gBAAgB,GAAG;YACrB;QACF;QAEA,MAAM,OAAO,CAAC;0QAAG,IAAA,EAAK,KAAK;SAAA;QAC3B,IAAI;QACJ,IAAI,aAAa;QACjB,IAAA,IAAS,IAAI,aAAa,IAAI,MAAM,IAAK;YACvC,IAAI,gBAAgB,GAAG;gBACrB,IAAI,UAAU,IAAA,CAAK,CAAC,CAAA;gBACpB,IAAI,IAAA,CAAK,CAAC,CAAA,KAAM,KAAK;oBACnB,UAAU,IAAA,CAAK,IAAI,CAAC,CAAA;gBACtB;gBACA,IAAI,KAAK;oBAEP,IAAA,CAAK,MAAA,CAAO,GAAG;gBACjB;gBACA,YAAY,IAAA,CAAK,GAAA,CAAI,OAAO;gBAC5B,IAAA,CAAK,GAAA,CAAI,KAAK,KAAK;YACrB,OAAO;gBACL,IAAI,CAAC,cAAc,IAAA,CAAK,IAAI,CAAC,CAAA,KAAM,KAAK;oBACtC,aAAa;gBACf;gBACA,MAAM,aAAa,IAAA,CAAK,aAAa,IAAI,IAAI,CAAC,CAAA;gBAC9C,MAAM,eAAe;gBACrB,YAAY,IAAA,CAAK,GAAA,CAAI,UAAU;gBAC/B,IAAA,CAAK,MAAA,CAAO,UAAU;gBACtB,IAAA,CAAK,GAAA,CAAI,YAAY,YAAY;YACnC;QACF;QACA,OAAO,IAAA;IACT;IAEA,KAAK,KAAA,EAAe,GAAA,EAAQ,KAAA,EAAU;QACpC,MAAM,OAAO,IAAI,aAAY,IAAI;QACjC,KAAK,MAAA,CAAO,OAAO,KAAK,KAAK;QAC7B,OAAO;IACT;IAEA,OAAO,GAAA,EAAQ;QACb,MAAM,QAAQ,+PAAA,EAAK,OAAM,OAAA,CAAQ,GAAG,IAAI;QACxC,IAAI,QAAQ,GAAG;YACb,OAAO,KAAA;QACT;QACA,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAK;IAC3B;IAAA;;GAAA,GAKA,UAAU,GAAA,EAAQ,MAAA,EAAW,KAAA,EAAU;QACrC,MAAM,QAAQ,+PAAA,EAAK,OAAM,OAAA,CAAQ,GAAG;QACpC,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,IAAA;QACT;QACA,OAAO,IAAA,CAAK,MAAA,CAAO,OAAO,QAAQ,KAAK;IACzC;IAEA,MAAM,GAAA,EAAQ;QACZ,IAAI,mQAAQ,IAAA,EAAK,OAAM,OAAA,CAAQ,GAAG;QAClC,QAAQ,UAAU,CAAA,KAAM,UAAU,IAAA,CAAK,IAAA,GAAO,IAAI,CAAA,IAAK,QAAQ;QAC/D,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,KAAA;QACT;QACA,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAK;IAC3B;IAAA;;GAAA,GAKA,SAAS,GAAA,EAAQ,MAAA,EAAW,KAAA,EAAU;QACpC,MAAM,mQAAQ,IAAA,EAAK,OAAM,OAAA,CAAQ,GAAG;QACpC,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,IAAA;QACT;QACA,OAAO,IAAA,CAAK,MAAA,CAAO,QAAQ,GAAG,QAAQ,KAAK;IAC7C;IAEA,QAAQ;QACN,OAAO,IAAA,CAAK,OAAA,CAAQ,CAAC;IACvB;IAEA,OAAO;QACL,OAAO,IAAA,CAAK,OAAA,CAAQ,CAAA,CAAE;IACxB;IAEA,QAAQ;yQACD,OAAQ,CAAC,CAAA;QACd,OAAO,KAAA,CAAM,MAAM;IACrB;IAEA,OAAO,GAAA,EAAQ;QACb,MAAM,UAAU,KAAA,CAAM,OAAO,GAAG;QAChC,IAAI,SAAS;YACX,2PAAA,IAAA,EAAK,OAAM,MAAA,4PAAO,IAAA,EAAK,OAAM,OAAA,CAAQ,GAAG,GAAG,CAAC;QAC9C;QACA,OAAO;IACT;IAEA,SAAS,KAAA,EAAe;QACtB,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAK;QAC5B,IAAI,QAAQ,KAAA,GAAW;YACrB,OAAO,IAAA,CAAK,MAAA,CAAO,GAAG;QACxB;QACA,OAAO;IACT;IAEA,GAAG,KAAA,EAAe;QAChB,MAAM,MAAM,8PAAG,IAAA,EAAK,QAAO,KAAK;QAChC,IAAI,QAAQ,KAAA,GAAW;YACrB,OAAO,IAAA,CAAK,GAAA,CAAI,GAAG;QACrB;IACF;IAEA,QAAQ,KAAA,EAAmC;QACzC,MAAM,MAAM,8PAAG,IAAA,EAAK,QAAO,KAAK;QAChC,IAAI,QAAQ,KAAA,GAAW;YACrB,OAAO;gBAAC;gBAAK,IAAA,CAAK,GAAA,CAAI,GAAG,CAAE;aAAA;QAC7B;IACF;IAEA,QAAQ,GAAA,EAAQ;QACd,kQAAO,IAAA,EAAK,OAAM,OAAA,CAAQ,GAAG;IAC/B;IAEA,MAAM,KAAA,EAAe;QACnB,OAAO,8PAAG,IAAA,EAAK,QAAO,KAAK;IAC7B;IAEA,KAAK,GAAA,EAAQ,MAAA,EAAgB;QAC3B,MAAM,QAAQ,IAAA,CAAK,OAAA,CAAQ,GAAG;QAC9B,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,KAAA;QACT;QACA,IAAI,OAAO,QAAQ;QACnB,IAAI,OAAO,EAAG,CAAA,OAAO;QACrB,IAAI,QAAQ,IAAA,CAAK,IAAA,CAAM,CAAA,OAAO,IAAA,CAAK,IAAA,GAAO;QAC1C,OAAO,IAAA,CAAK,EAAA,CAAG,IAAI;IACrB;IAEA,QAAQ,GAAA,EAAQ,MAAA,EAAgB;QAC9B,MAAM,QAAQ,IAAA,CAAK,OAAA,CAAQ,GAAG;QAC9B,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,KAAA;QACT;QACA,IAAI,OAAO,QAAQ;QACnB,IAAI,OAAO,EAAG,CAAA,OAAO;QACrB,IAAI,QAAQ,IAAA,CAAK,IAAA,CAAM,CAAA,OAAO,IAAA,CAAK,IAAA,GAAO;QAC1C,OAAO,IAAA,CAAK,KAAA,CAAM,IAAI;IACxB;IAEA,KACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,OAAO;YACT;YACA;QACF;QACA,OAAO,KAAA;IACT;IAEA,UACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,OAAO;YACT;YACA;QACF;QACA,OAAO,CAAA;IACT;IAYA,OACE,SAAA,EACA,OAAA,EACA;QACA,MAAM,UAAyB,CAAC,CAAA;QAChC,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,QAAQ,IAAA,CAAK,KAAK;YACpB;YACA;QACF;QACA,OAAO,IAAI,aAAY,OAAO;IAChC;IAEA,IACE,UAAA,EACA,OAAA,EACmB;QACnB,MAAM,UAAoB,CAAC,CAAA;QAC3B,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,QAAQ,IAAA,CAAK;gBAAC,KAAA,CAAM,CAAC,CAAA;gBAAG,QAAQ,KAAA,CAAM,YAAY,SAAS;oBAAC;oBAAO;oBAAO,IAAI;iBAAC,CAAC;aAAC;YACjF;QACF;QACA,OAAO,IAAI,aAAY,OAAO;IAChC;IA6BA,SAUE;QAVF,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;YACK,KADL,QAAA,SAAA,CAAA,KACK;;QAUH,MAAM,CAAC,YAAY,YAAY,CAAA,GAAI;QACnC,IAAI,QAAQ;QACZ,IAAI,cAAc,kEAAgB,IAAA,CAAK,EAAA,CAAG,CAAC;QAC3C,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,UAAU,KAAK,KAAK,MAAA,KAAW,GAAG;gBACpC,cAAc;YAChB,OAAO;gBACL,cAAc,QAAQ,KAAA,CAAM,YAAY,IAAA,EAAM;oBAAC;oBAAa;oBAAO;oBAAO,IAAI;iBAAC;YACjF;YACA;QACF;QACA,OAAO;IACT;IA6BA,cAUE;QAVF,IAAA,IAAA,OAAA,UAAA,QAAA,AACK,OADL,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;iBAAA,QAAA,SAAA,CAAA,KACK;;QAUH,MAAM,CAAC,YAAY,YAAY,CAAA,GAAI;QACnC,IAAI,iEAAc,eAAgB,IAAA,CAAK,EAAA,CAAG,CAAA,CAAE;QAC5C,IAAA,IAAS,QAAQ,IAAA,CAAK,IAAA,GAAO,GAAG,SAAS,GAAG,QAAS;YACnD,MAAM,QAAQ,IAAA,CAAK,EAAA,CAAG,KAAK;YAC3B,IAAI,UAAU,IAAA,CAAK,IAAA,GAAO,KAAK,KAAK,MAAA,KAAW,GAAG;gBAChD,cAAc;YAChB,OAAO;gBACL,cAAc,QAAQ,KAAA,CAAM,YAAY,IAAA,EAAM;oBAAC;oBAAa;oBAAO;oBAAO,IAAI;iBAAC;YACjF;QACF;QACA,OAAO;IACT;IAEA,SAAS,SAAA,EAAiE;QACxE,MAAM,UAAU,CAAC;eAAG,IAAA,CAAK,OAAA,CAAQ,CAAC;SAAA,CAAE,IAAA,CAAK,SAAS;QAClD,OAAO,IAAI,aAAY,OAAO;IAChC;IAEA,aAAgC;QAC9B,MAAM,WAAW,IAAI,aAAkB;QACvC,IAAA,IAAS,QAAQ,IAAA,CAAK,IAAA,GAAO,GAAG,SAAS,GAAG,QAAS;YACnD,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAK;YAC5B,MAAM,UAAU,IAAA,CAAK,GAAA,CAAI,GAAG;YAC5B,SAAS,GAAA,CAAI,KAAK,OAAO;QAC3B;QACA,OAAO;IACT;IAKA,YAA6E;QAA7E,IAAA,IAAA,OAAA,UAAA,QAAA,OAAA,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;YAAa,KAAb,QAAA,SAAA,CAAA,KAAa;;QACX,MAAM,UAAU,CAAC;eAAG,IAAA,CAAK,OAAA,CAAQ,CAAC;SAAA;QAClC,QAAQ,MAAA,CAAO,GAAG,IAAI;QACtB,OAAO,IAAI,aAAY,OAAO;IAChC;IAEA,MAAM,KAAA,EAAgB,GAAA,EAAc;QAClC,MAAM,SAAS,IAAI,aAAkB;QACrC,IAAI,OAAO,IAAA,CAAK,IAAA,GAAO;QAEvB,IAAI,UAAU,KAAA,GAAW;YACvB,OAAO;QACT;QAEA,IAAI,QAAQ,GAAG;YACb,QAAQ,QAAQ,IAAA,CAAK,IAAA;QACvB;QAEA,IAAI,QAAQ,KAAA,KAAa,MAAM,GAAG;YAChC,OAAO,MAAM;QACf;QAEA,IAAA,IAAS,QAAQ,OAAO,SAAS,MAAM,QAAS;YAC9C,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAK;YAC5B,MAAM,UAAU,IAAA,CAAK,GAAA,CAAI,GAAG;YAC5B,OAAO,GAAA,CAAI,KAAK,OAAO;QACzB;QACA,OAAO;IACT;IAEA,MACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,CAAC,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC5D,OAAO;YACT;YACA;QACF;QACA,OAAO;IACT;IAEA,KACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,OAAO;YACT;YACA;QACF;QACA,OAAO;IACT;IA9aA,YAAY,OAAA,CAA+C;QACzD,KAAA,CAAM,OAAO;;wBAJf;;yQAKO,OAAQ,CAAC;eAAG,KAAA,CAAM,KAAK,CAAC;SAAA;QAC7B,eAAe,GAAA,CAAI,IAAA,EAAM,IAAI;IAC/B;AA2aF;AAUA,SAAS,GAAM,KAAA,EAAqB,KAAA,EAA8B;IAChE,IAAI,QAAQ,MAAM,SAAA,EAAW;QAC3B,OAAO,MAAM,SAAA,CAAU,EAAA,CAAG,IAAA,CAAK,OAAO,KAAK;IAC7C;IACA,MAAM,cAAc,YAAY,OAAO,KAAK;IAC5C,OAAO,gBAAgB,CAAA,IAAK,KAAA,IAAY,KAAA,CAAM,WAAW,CAAA;AAC3D;AAEA,SAAS,YAAY,KAAA,EAAuB,KAAA,EAAe;IACzD,MAAM,SAAS,MAAM,MAAA;IACrB,MAAM,gBAAgB,cAAc,KAAK;IACzC,MAAM,cAAc,iBAAiB,IAAI,gBAAgB,SAAS;IAClE,OAAO,cAAc,KAAK,eAAe,SAAS,CAAA,IAAK;AACzD;AAEA,SAAS,cAAc,MAAA,EAAgB;IAErC,OAAO,WAAW,UAAU,WAAW,IAAI,IAAI,KAAK,KAAA,CAAM,MAAM;AAClE;;ADtbA,SAASK,kBAGP,IAAA,EAAc;IAKd,MAAM,gBAAgB,OAAO;IAC7B,MAAM,CAAC,yBAAyB,qBAAqB,CAAA,OAAIC,kTAAAA,EAAmB,aAAa;IAUzF,MAAM,CAAC,2BAA2B,oBAAoB,CAAA,GAAI,wBACxD,eACA;QACE,mBAAmB;QACnB,eAAe;YAAE,SAAS;QAAK;QAC/B,qBAAqB;YAAE,SAAS;QAAK;QACrC,SAAS,IAAI,YAAY;QACzB,YAAY,IAAM,KAAA;IACpB;IAQF,MAAM,qBAID;YAAC,EAAE,KAAA,EAAO,GAAG,MAAM,CAAA,KAAM;QAC5B,OAAO,QACL,aAAA,GAAAF,4TAAAA,EAAC,wBAAA;YAAwB,GAAG,KAAA;YAAO;QAAA,CAAc,IAEjD,aAAA,OAAAA,wTAAAA,EAAC,gBAAA;YAAgB,GAAG,KAAA;QAAA,CAAO;IAE/B;IACA,mBAAmB,WAAA,GAAc;IAEjC,MAAM,iBAGD,CAAC,UAAU;QACd,MAAM,QAAQ,kBAAkB;QAChC,OAAO,aAAA,OAAAA,wTAAAA,EAAC,wBAAA;YAAwB,GAAG,KAAA;YAAO;QAAA,CAAc;IAC1D;IACA,eAAe,WAAA,GAAc,gBAAgB;IAE7C,MAAM,yBAID,CAAC,UAAU;QACd,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,KAAA,CAAM,CAAA,GAAI;QACnC,MAAM,MAAMG,mTAAAA,CAAM,MAAA,CAA0B,IAAI;QAChD,MAAM,CAAC,mBAAmB,oBAAoB,CAAA,GAAIA,mTAAAA,CAAM,QAAA,CACtD;QAEF,MAAM,kBAAcC,oTAAAA,EAAgB,KAAK,oBAAoB;QAC7D,MAAM,CAAC,SAAS,UAAU,CAAA,GAAI;QAE9BD,mTAAAA,CAAM,SAAA;kEAAU,MAAM;gBACpB,IAAI,CAAC,kBAAmB,CAAA;gBAExB,MAAM,WAAW;mFAAqB,KAkBtC,CAlB4C,AAkB3C;;gBACD,SAAS,OAAA,CAAQ,mBAAmB;oBAClC,WAAW;oBACX,SAAS;gBACX,CAAC;gBACD;0EAAO,MAAM;wBACX,SAAS,UAAA,CAAW;oBACtB;;YACF;iEAAG;YAAC,iBAAiB;SAAC;QAEtB,OACE,aAAA,OAAAH,wTAAAA,EAAC,2BAAA;YACC;YACA;YACA;YACA,eAAe;YACf,qBAAqB;YACrB;YAEC;QAAA;IAGP;IAEA,uBAAuB,WAAA,GAAc,gBAAgB;IAMrD,MAAM,uBAAuB,OAAO;IAEpC,MAAM,qBAAqBK,sUAAAA,EAAW,oBAAoB;IAC1D,MAAM,iBAAiBF,mTAAAA,CAAM,UAAA,CAC3B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,CAAS,CAAA,GAAI;QAC5B,MAAM,UAAU,qBAAqB,sBAAsB,KAAK;QAChE,MAAM,mBAAeC,oTAAAA,EAAgB,cAAc,QAAQ,aAAa;QACxE,OAAO,aAAA,OAAAJ,wTAAAA,EAAC,oBAAA;YAAmB,KAAK;YAAe;QAAA,CAAS;IAC1D;IAGF,eAAe,WAAA,GAAc;IAM7B,MAAM,iBAAiB,OAAO;IAC9B,MAAM,iBAAiB;IAOvB,MAAM,6BAAyBK,kUAAAA,EAAW,cAAc;IACxD,MAAM,qBAAqBF,mTAAAA,CAAM,UAAA,CAC/B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,SAAS,CAAA,GAAI;QACzC,MAAM,MAAMA,mTAAAA,CAAM,MAAA,CAAoB,IAAI;QAC1C,MAAM,CAAC,SAAS,UAAU,CAAA,GAAIA,mTAAAA,CAAM,QAAA,CAA6B,IAAI;QACrE,MAAM,mBAAeC,oTAAAA,EAAgB,cAAc,KAAK,UAAU;QAClE,MAAM,UAAU,qBAAqB,gBAAgB,KAAK;QAE1D,MAAM,EAAE,UAAA,CAAW,CAAA,GAAI;QAEvB,MAAM,cAAcD,mTAAAA,CAAM,MAAA,CAAO,QAAQ;QACzC,IAAI,CAAC,aAAa,YAAY,OAAA,EAAS,QAAQ,GAAG;YAChD,YAAY,OAAA,GAAU;QACxB;QACA,MAAM,mBAAmB,YAAY,OAAA;QAErCA,mTAAAA,CAAM,SAAA;8DAAU,MAAM;gBACpB,MAAMG,YAAW;gBACjB;sEAAW,CAAC,QAAQ;wBAClB,IAAI,CAAC,SAAS;4BACZ,OAAO;wBACT;wBAEA,IAAI,CAAC,IAAI,GAAA,CAAI,OAAO,GAAG;4BACrB,IAAI,GAAA,CAAI,SAAS;gCAAE,GAAIA,SAAAA;gCAAkC;4BAAQ,CAAC;4BAClE,OAAO,IAAI,QAAA,CAAS,sBAAsB;wBAC5C;wBAEA,OAAO,IACJ,GAAA,CAAI,SAAS;4BAAE,GAAIA,SAAAA;4BAAkC;wBAAQ,CAAC,EAC9D,QAAA,CAAS,sBAAsB;oBACpC,CAAC;;gBAED;sEAAO,MAAM;wBACX;8EAAW,CAAC,QAAQ;gCAClB,IAAI,CAAC,WAAW,CAAC,IAAI,GAAA,CAAI,OAAO,GAAG;oCACjC,OAAO;gCACT;gCACA,IAAI,MAAA,CAAO,OAAO;gCAClB,OAAO,IAAI,YAAY,GAAG;4BAC5B,CAAC;;oBACH;;YACF;6DAAG;YAAC;YAAS;YAAkB,UAAU;SAAC;QAE1C,OACE,aAAA,OAAAN,wTAAAA,EAAC,wBAAA;YAAwB,GAAG;gBAAE,CAAC,cAAc,CAAA,EAAG;YAAG,CAAA;YAAG,KAAK;YACxD;QAAA,CACH;IAEJ;IAGF,mBAAmB,WAAA,GAAc;IAMjC,SAAS,oBAAoB;QAC3B,OAAOG,mTAAAA,CAAM,QAAA,CAAyC,IAAI,YAAY,CAAC;IACzE;IAMA,SAAS,cAAc,KAAA,EAAY;QACjC,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI,qBAAqB,OAAO,sBAAsB,KAAK;QAE3E,OAAO;IACT;IAEA,MAAM,YAAY;QAChB;QACA;QACA;IACF;IAEA,OAAO;QACL;YAAE,UAAU;YAAoB,MAAM;YAAgB,UAAU;QAAmB;QACnF;KACF;AACF;AAKA,SAAS,aAAa,CAAA,EAAQ,CAAA,EAAQ;IACpC,IAAI,MAAM,EAAG,CAAA,OAAO;IACpB,IAAI,OAAO,MAAM,YAAY,OAAO,MAAM,SAAU,CAAA,OAAO;IAC3D,IAAI,KAAK,QAAQ,KAAK,KAAM,CAAA,OAAO;IACnC,MAAM,QAAQ,OAAO,IAAA,CAAK,CAAC;IAC3B,MAAM,QAAQ,OAAO,IAAA,CAAK,CAAC;IAC3B,IAAI,MAAM,MAAA,KAAW,MAAM,MAAA,CAAQ,CAAA,OAAO;IAC1C,KAAA,MAAW,OAAO,MAAO;QACvB,IAAI,CAAC,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,GAAG,GAAG,EAAG,CAAA,OAAO;QAC1D,IAAI,CAAA,CAAE,GAAG,CAAA,KAAM,CAAA,CAAE,GAAG,CAAA,CAAG,CAAA,OAAO;IAChC;IACA,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAA,EAAY,CAAA,EAAY;IAClD,OAAO,CAAC,CAAA,CAAE,EAAE,uBAAA,CAAwB,CAAC,IAAI,KAAK,2BAAA;AAChD;AAEA,SAAS,uBACP,CAAA,EACA,CAAA,EACA;IACA,OAAO,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,OAAA,IAAW,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,OAAA,GAC1B,IACA,mBAAmB,CAAA,CAAE,CAAC,CAAA,CAAE,OAAA,EAAS,CAAA,CAAE,CAAC,CAAA,CAAE,OAAO,IAC3C,CAAA,IACA;AACR;AAEA,SAAS,qBAAqB,QAAA,EAAsB;IAClD,MAAM,WAAW,IAAI,iBAAiB,CAAC,kBAAkB;QACvD,KAAA,MAAW,YAAY,cAAe;YACpC,IAAI,SAAS,IAAA,KAAS,aAAa;gBACjC,SAAS;gBACT;YACF;QACF;IACF,CAAC;IAED,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1299, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40radix-ui%2Breact-direction%401_f60df28923be704a001f3f7fab7b8a5c/node_modules/%40radix-ui/react-direction/src/direction.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype Direction = 'ltr' | 'rtl';\nconst DirectionContext = React.createContext<Direction | undefined>(undefined);\n\n/* -------------------------------------------------------------------------------------------------\n * Direction\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DirectionProviderProps {\n  children?: React.ReactNode;\n  dir: Direction;\n}\nconst DirectionProvider: React.FC<DirectionProviderProps> = (props) => {\n  const { dir, children } = props;\n  return <DirectionContext.Provider value={dir}>{children}</DirectionContext.Provider>;\n};\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useDirection(localDir?: Direction) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || 'ltr';\n}\n\nconst Provider = DirectionProvider;\n\nexport {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA,YAAY,WAAW;AAed;;;AAZT,IAAM,mBAAyB,yTAAA,CAAqC,KAAA,CAAS;AAU7E,IAAM,oBAAsD,CAAC,UAAU;IACrE,MAAM,EAAE,GAAA,EAAK,QAAA,CAAS,CAAA,GAAI;IAC1B,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAC,iBAAiB,QAAA,EAAjB;QAA0B,OAAO;QAAM;IAAA,CAAS;AAC1D;AAIA,SAAS,aAAa,QAAA,EAAsB;IAC1C,MAAM,YAAkB,sTAAA,CAAW,gBAAgB;IACnD,OAAO,YAAY,aAAa;AAClC;AAEA,IAAM,WAAW", "debugId": null}}, {"offset": {"line": 1331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40radix-ui%2Breact-roving-focu_212b51427a855c81088e3653515bf4fb/node_modules/%40radix-ui/react-roving-focus/src/roving-focus-group.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n  preventScrollOnEntryFocus?: boolean;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes, preventScrollOnEntryFocus);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends Omit<PrimitiveSpanProps, 'children'> {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n  children?:\n    | React.ReactNode\n    | ((props: { hasTabStop: boolean; isCurrentTabStop: boolean }) => React.ReactNode);\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        >\n          {typeof children === 'function'\n            ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null })\n            : children}\n        </Primitive.span>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[], preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,wBAAwB;AACjC,SAAS,uBAAuB;AAChC,SAAS,0BAA0B;AACnC,SAAS,aAAa;AACtB,SAAS,iBAAiB;AAC1B,SAAS,sBAAsB;AAC/B,SAAS,4BAA4B;AACrC,SAAS,oBAAoB;AAgEnB;;;;;;;;;;;;;AA5DV,IAAM,cAAc;AACpB,IAAM,gBAAgB;IAAE,SAAS;IAAO,YAAY;AAAK;AAMzD,IAAM,aAAa;AAGnB,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,GAAI,oTAAA,EAGzD,UAAU;AAGZ,IAAM,CAAC,+BAA+B,2BAA2B,CAAA,OAAI,kTAAA,EACnE,YACA;IAAC,qBAAqB;CAAA;AA+BxB,IAAM,CAAC,qBAAqB,qBAAqB,CAAA,GAC/C,8BAAkD,UAAU;AAK9D,IAAM,mBAAyB,sTAAA,CAC7B,CAAC,OAA2C,iBAAiB;IAC3D,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO,MAAM,uBAAA;QAChC,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,WAAW,IAAA,EAAX;YAAgB,OAAO,MAAM,uBAAA;YAC5B,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,sBAAA;gBAAsB,GAAG,KAAA;gBAAO,KAAK;YAAA,CAAc;QAAA,CACtD;IAAA,CACF;AAEJ;AAGF,iBAAiB,WAAA,GAAc;AAgB/B,IAAM,uBAA6B,sTAAA,CAGjC,CAAC,OAA+C,iBAAiB;IACjE,MAAM,EACJ,uBAAA,EACA,WAAA,EACA,OAAO,KAAA,EACP,GAAA,EACA,kBAAkB,oBAAA,EAClB,uBAAA,EACA,wBAAA,EACA,YAAA,EACA,4BAA4B,KAAA,EAC5B,GAAG,YACL,GAAI;IACJ,MAAM,MAAY,kTAAA,CAAoC,IAAI;IAC1D,MAAM,mBAAe,oTAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,gBAAY,2SAAA,EAAa,GAAG;IAClC,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,GAAI,0UAAA,EAAqB;QACnE,MAAM;QACN,sFAAa,0BAA2B;QACxC,UAAU;QACV,QAAQ;IACV,CAAC;IACD,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,GAAU,oTAAA,CAAS,KAAK;IACpE,MAAM,uBAAmB,0TAAA,EAAe,YAAY;IACpD,MAAM,WAAW,cAAc,uBAAuB;IACtD,MAAM,kBAAwB,kTAAA,CAAO,KAAK;IAC1C,MAAM,CAAC,qBAAqB,sBAAsB,CAAA,GAAU,oTAAA,CAAS,CAAC;IAEhE,qTAAA;0CAAU,MAAM;YACpB,MAAM,OAAO,IAAI,OAAA;YACjB,IAAI,MAAM;gBACR,KAAK,gBAAA,CAAiB,aAAa,gBAAgB;gBACnD;sDAAO,IAAM,KAAK,mBAAA,CAAoB,aAAa,gBAAgB;;YACrE;QACF;yCAAG;QAAC,gBAAgB;KAAC;IAErB,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,qBAAA;QACC,OAAO;QACP;QACA,KAAK;QACL;QACA;QACA,aAAmB,uTAAA;gDACjB,CAAC,YAAc,oBAAoB,SAAS;+CAC5C;YAAC,mBAAmB;SAAA;QAEtB,gBAAsB,uTAAA;gDAAY,IAAM,oBAAoB,IAAI;+CAAG,CAAC,CAAC;QACrE,oBAA0B,uTAAA;gDACxB,IAAM;wDAAuB,CAAC,YAAc,YAAY,CAAC;;+CACzD,CAAC,CAAA;QAEH,uBAA6B,uTAAA;gDAC3B,IAAM;wDAAuB,CAAC,YAAc,YAAY,CAAC;;+CACzD,CAAC,CAAA;QAGH,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,wSAAA,CAAU,GAAA,EAAV;YACC,UAAU,oBAAoB,wBAAwB,IAAI,CAAA,IAAK;YAC/D,oBAAkB;YACjB,GAAG,UAAA;YACJ,KAAK;YACL,OAAO;gBAAE,SAAS;gBAAQ,GAAG,MAAM,KAAA;YAAM;YACzC,iBAAa,0QAAA,EAAqB,MAAM,WAAA,EAAa,MAAM;gBACzD,gBAAgB,OAAA,GAAU;YAC5B,CAAC;YACD,aAAS,0QAAA,EAAqB,MAAM,OAAA,EAAS,CAAC,UAAU;gBAKtD,MAAM,kBAAkB,CAAC,gBAAgB,OAAA;gBAEzC,IAAI,MAAM,MAAA,KAAW,MAAM,aAAA,IAAiB,mBAAmB,CAAC,kBAAkB;oBAChF,MAAM,kBAAkB,IAAI,YAAY,aAAa,aAAa;oBAClE,MAAM,aAAA,CAAc,aAAA,CAAc,eAAe;oBAEjD,IAAI,CAAC,gBAAgB,gBAAA,EAAkB;wBACrC,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,KAAK,SAAS;wBACxD,MAAM,aAAa,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,MAAM;wBACnD,MAAM,cAAc,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,EAAA,KAAO,gBAAgB;wBACrE,MAAM,iBAAiB;4BAAC;4BAAY,aAAa;+BAAG,KAAK;yBAAA,CAAE,MAAA,CACzD;wBAEF,MAAM,iBAAiB,eAAe,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;wBACrE,WAAW,gBAAgB,yBAAyB;oBACtD;gBACF;gBAEA,gBAAgB,OAAA,GAAU;YAC5B,CAAC;YACD,YAAQ,0QAAA,EAAqB,MAAM,MAAA,EAAQ,IAAM,oBAAoB,KAAK,CAAC;QAAA;IAC7E;AAGN,CAAC;AAMD,IAAM,YAAY;AAalB,IAAM,uBAA6B,sTAAA,CACjC,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EACJ,uBAAA,EACA,YAAY,IAAA,EACZ,SAAS,KAAA,EACT,SAAA,EACA,QAAA,EACA,GAAG,WACL,GAAI;IACJ,MAAM,aAAS,yTAAA,CAAM;IACrB,MAAM,KAAK,aAAa;IACxB,MAAM,UAAU,sBAAsB,WAAW,uBAAuB;IACxE,MAAM,mBAAmB,QAAQ,gBAAA,KAAqB;IACtD,MAAM,WAAW,cAAc,uBAAuB;IAEtD,MAAM,EAAE,kBAAA,EAAoB,qBAAA,EAAuB,gBAAA,CAAiB,CAAA,GAAI;IAElE,qTAAA;0CAAU,MAAM;YACpB,IAAI,WAAW;gBACb,mBAAmB;gBACnB;sDAAO,IAAM,sBAAsB;;YACrC;QACF;yCAAG;QAAC;QAAW;QAAoB,qBAAqB;KAAC;IAEzD,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,WAAW,QAAA,EAAX;QACC,OAAO;QACP;QACA;QACA;QAEA,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,wSAAA,CAAU,IAAA,EAAV;YACC,UAAU,mBAAmB,IAAI,CAAA;YACjC,oBAAkB,QAAQ,WAAA;YACzB,GAAG,SAAA;YACJ,KAAK;YACL,iBAAa,0QAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gBAG9D,IAAI,CAAC,UAAW,CAAA,MAAM,cAAA,CAAe;qBAEhC,QAAQ,WAAA,CAAY,EAAE;YAC7B,CAAC;YACD,aAAS,0QAAA,EAAqB,MAAM,OAAA,EAAS,IAAM,QAAQ,WAAA,CAAY,EAAE,CAAC;YAC1E,eAAW,0QAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,IAAI,MAAM,GAAA,KAAQ,SAAS,MAAM,QAAA,EAAU;oBACzC,QAAQ,cAAA,CAAe;oBACvB;gBACF;gBAEA,IAAI,MAAM,MAAA,KAAW,MAAM,aAAA,CAAe,CAAA;gBAE1C,MAAM,cAAc,eAAe,OAAO,QAAQ,WAAA,EAAa,QAAQ,GAAG;gBAE1E,IAAI,gBAAgB,KAAA,GAAW;oBAC7B,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,MAAA,IAAU,MAAM,QAAA,CAAU,CAAA;oBACtE,MAAM,cAAA,CAAe;oBACrB,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,KAAK,SAAS;oBACxD,IAAI,iBAAiB,MAAM,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;oBAE1D,IAAI,gBAAgB,OAAQ,CAAA,eAAe,OAAA,CAAQ;yBAAA,IAC1C,gBAAgB,UAAU,gBAAgB,QAAQ;wBACzD,IAAI,gBAAgB,OAAQ,CAAA,eAAe,OAAA,CAAQ;wBACnD,MAAM,eAAe,eAAe,OAAA,CAAQ,MAAM,aAAa;wBAC/D,iBAAiB,QAAQ,IAAA,GACrB,UAAU,gBAAgB,eAAe,CAAC,IAC1C,eAAe,KAAA,CAAM,eAAe,CAAC;oBAC3C;oBAMA,WAAW,IAAM,WAAW,cAAc,CAAC;gBAC7C;YACF,CAAC;YAEA,UAAA,OAAO,aAAa,aACjB,SAAS;gBAAE;gBAAkB,YAAY,oBAAoB;YAAK,CAAC,IACnE;QAAA;IACN;AAGN;AAGF,qBAAqB,WAAA,GAAc;AAKnC,IAAM,0BAAuD;IAC3D,WAAW;IAAQ,SAAS;IAC5B,YAAY;IAAQ,WAAW;IAC/B,QAAQ;IAAS,MAAM;IACvB,UAAU;IAAQ,KAAK;AACzB;AAEA,SAAS,qBAAqB,GAAA,EAAa,GAAA,EAAiB;IAC1D,IAAI,QAAQ,MAAO,CAAA,OAAO;IAC1B,OAAO,QAAQ,cAAc,eAAe,QAAQ,eAAe,cAAc;AACnF;AAIA,SAAS,eAAe,KAAA,EAA4B,WAAA,EAA2B,GAAA,EAAiB;IAC9F,MAAM,MAAM,qBAAqB,MAAM,GAAA,EAAK,GAAG;IAC/C,IAAI,gBAAgB,cAAc;QAAC;QAAa,YAAY;KAAA,CAAE,QAAA,CAAS,GAAG,EAAG,CAAA,OAAO,KAAA;IACpF,IAAI,gBAAgB,gBAAgB;QAAC;QAAW,WAAW;KAAA,CAAE,QAAA,CAAS,GAAG,EAAG,CAAA,OAAO,KAAA;IACnF,OAAO,uBAAA,CAAwB,GAAG,CAAA;AACpC;AAEA,SAAS,WAAW,UAAA;wBAA2B,iEAAgB,OAAO;IACpE,MAAM,6BAA6B,SAAS,aAAA;IAC5C,KAAA,MAAW,aAAa,WAAY;QAElC,IAAI,cAAc,2BAA4B,CAAA;QAC9C,UAAU,KAAA,CAAM;YAAE;QAAc,CAAC;QACjC,IAAI,SAAS,aAAA,KAAkB,2BAA4B,CAAA;IAC7D;AACF;AAMA,SAAS,UAAa,KAAA,EAAY,UAAA,EAAoB;IACpD,OAAO,MAAM,GAAA,CAAO,CAAC,GAAG,QAAU,KAAA,CAAA,CAAO,aAAa,KAAA,IAAS,MAAM,MAAM,CAAE;AAC/E;AAEA,IAAM,OAAO;AACb,IAAM,OAAO", "debugId": null}}, {"offset": {"line": 1595, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40radix-ui%2Breact-menu%402.1.16_900d93adafc779075db578116b92345f/node_modules/%40radix-ui/react-menu/src/menu.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs, composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst SELECTION_KEYS = ['Enter', ' '];\nconst FIRST_KEYS = ['ArrowDown', 'PageUp', 'Home'];\nconst LAST_KEYS = ['ArrowUp', 'PageDown', 'End'];\nconst FIRST_LAST_KEYS = [...FIRST_KEYS, ...LAST_KEYS];\nconst SUB_OPEN_KEYS: Record<Direction, string[]> = {\n  ltr: [...SELECTION_KEYS, 'ArrowRight'],\n  rtl: [...SELECTION_KEYS, 'ArrowLeft'],\n};\nconst SUB_CLOSE_KEYS: Record<Direction, string[]> = {\n  ltr: ['ArrowLeft'],\n  rtl: ['ArrowRight'],\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Menu\n * -----------------------------------------------------------------------------------------------*/\n\nconst MENU_NAME = 'Menu';\n\ntype ItemData = { disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  MenuItemElement,\n  ItemData\n>(MENU_NAME);\n\ntype ScopedProps<P> = P & { __scopeMenu?: Scope };\nconst [createMenuContext, createMenuScope] = createContextScope(MENU_NAME, [\n  createCollectionScope,\n  createPopperScope,\n  createRovingFocusGroupScope,\n]);\nconst usePopperScope = createPopperScope();\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype MenuContextValue = {\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  content: MenuContentElement | null;\n  onContentChange(content: MenuContentElement | null): void;\n};\n\nconst [MenuProvider, useMenuContext] = createMenuContext<MenuContextValue>(MENU_NAME);\n\ntype MenuRootContextValue = {\n  onClose(): void;\n  isUsingKeyboardRef: React.RefObject<boolean>;\n  dir: Direction;\n  modal: boolean;\n};\n\nconst [MenuRootProvider, useMenuRootContext] = createMenuContext<MenuRootContextValue>(MENU_NAME);\n\ninterface MenuProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  modal?: boolean;\n}\n\nconst Menu: React.FC<MenuProps> = (props: ScopedProps<MenuProps>) => {\n  const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n  const popperScope = usePopperScope(__scopeMenu);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const isUsingKeyboardRef = React.useRef(false);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n  const direction = useDirection(dir);\n\n  React.useEffect(() => {\n    // Capture phase ensures we set the boolean before any side effects execute\n    // in response to the key or pointer event as they might depend on this value.\n    const handleKeyDown = () => {\n      isUsingKeyboardRef.current = true;\n      document.addEventListener('pointerdown', handlePointer, { capture: true, once: true });\n      document.addEventListener('pointermove', handlePointer, { capture: true, once: true });\n    };\n    const handlePointer = () => (isUsingKeyboardRef.current = false);\n    document.addEventListener('keydown', handleKeyDown, { capture: true });\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown, { capture: true });\n      document.removeEventListener('pointerdown', handlePointer, { capture: true });\n      document.removeEventListener('pointermove', handlePointer, { capture: true });\n    };\n  }, []);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuRootProvider\n          scope={__scopeMenu}\n          onClose={React.useCallback(() => handleOpenChange(false), [handleOpenChange])}\n          isUsingKeyboardRef={isUsingKeyboardRef}\n          dir={direction}\n          modal={modal}\n        >\n          {children}\n        </MenuRootProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenu.displayName = MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'MenuAnchor';\n\ntype MenuAnchorElement = React.ComponentRef<typeof PopperPrimitive.Anchor>;\ntype PopperAnchorProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Anchor>;\ninterface MenuAnchorProps extends PopperAnchorProps {}\n\nconst MenuAnchor = React.forwardRef<MenuAnchorElement, MenuAnchorProps>(\n  (props: ScopedProps<MenuAnchorProps>, forwardedRef) => {\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Anchor {...popperScope} {...anchorProps} ref={forwardedRef} />;\n  }\n);\n\nMenuAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'MenuPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createMenuContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface MenuPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuPortal: React.FC<MenuPortalProps> = (props: ScopedProps<MenuPortalProps>) => {\n  const { __scopeMenu, forceMount, children, container } = props;\n  const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n  return (\n    <PortalProvider scope={__scopeMenu} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'MenuContent';\n\ntype MenuContentContextValue = {\n  onItemEnter(event: React.PointerEvent): void;\n  onItemLeave(event: React.PointerEvent): void;\n  onTriggerLeave(event: React.PointerEvent): void;\n  searchRef: React.RefObject<string>;\n  pointerGraceTimerRef: React.MutableRefObject<number>;\n  onPointerGraceIntentChange(intent: GraceIntent | null): void;\n};\nconst [MenuContentProvider, useMenuContentContext] =\n  createMenuContext<MenuContentContextValue>(CONTENT_NAME);\n\ntype MenuContentElement = MenuRootContentTypeElement;\n/**\n * We purposefully don't union MenuRootContent and MenuSubContent props here because\n * they have conflicting prop types. We agreed that we would allow MenuSubContent to\n * accept props that it would just ignore.\n */\ninterface MenuContentProps extends MenuRootContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuContent = React.forwardRef<MenuContentElement, MenuContentProps>(\n  (props: ScopedProps<MenuContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            {rootContext.modal ? (\n              <MenuRootContentModal {...contentProps} ref={forwardedRef} />\n            ) : (\n              <MenuRootContentNonModal {...contentProps} ref={forwardedRef} />\n            )}\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuRootContentTypeElement = MenuContentImplElement;\ninterface MenuRootContentTypeProps\n  extends Omit<MenuContentImplProps, keyof MenuContentImplPrivateProps> {}\n\nconst MenuRootContentModal = React.forwardRef<MenuRootContentTypeElement, MenuRootContentTypeProps>(\n  (props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuRootContentTypeElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    // Hide everything from ARIA except the `MenuContent`\n    React.useEffect(() => {\n      const content = ref.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <MenuContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure we're not trapping once it's been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        // make sure to only disable pointer events when open\n        // this avoids blocking interactions while animating out\n        disableOutsidePointerEvents={context.open}\n        disableOutsideScroll\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault(),\n          { checkForDefaultPrevented: false }\n        )}\n        onDismiss={() => context.onOpenChange(false)}\n      />\n    );\n  }\n);\n\nconst MenuRootContentNonModal = React.forwardRef<\n  MenuRootContentTypeElement,\n  MenuRootContentTypeProps\n>((props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n  const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n  return (\n    <MenuContentImpl\n      {...props}\n      ref={forwardedRef}\n      trapFocus={false}\n      disableOutsidePointerEvents={false}\n      disableOutsideScroll={false}\n      onDismiss={() => context.onOpenChange(false)}\n    />\n  );\n});\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuContentImplElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ntype MenuContentImplPrivateProps = {\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n  onDismiss?: DismissableLayerProps['onDismiss'];\n  disableOutsidePointerEvents?: DismissableLayerProps['disableOutsidePointerEvents'];\n\n  /**\n   * Whether scrolling outside the `MenuContent` should be prevented\n   * (default: `false`)\n   */\n  disableOutsideScroll?: boolean;\n\n  /**\n   * Whether focus should be trapped within the `MenuContent`\n   * (default: false)\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n};\ninterface MenuContentImplProps\n  extends MenuContentImplPrivateProps,\n    Omit<PopperContentProps, 'dir' | 'onPlaced'> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: RovingFocusGroupProps['loop'];\n\n  onEntryFocus?: RovingFocusGroupProps['onEntryFocus'];\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n  onFocusOutside?: DismissableLayerProps['onFocusOutside'];\n  onInteractOutside?: DismissableLayerProps['onInteractOutside'];\n}\n\nconst Slot = createSlot('MenuContent.ScrollLock');\n\nconst MenuContentImpl = React.forwardRef<MenuContentImplElement, MenuContentImplProps>(\n  (props: ScopedProps<MenuContentImplProps>, forwardedRef) => {\n    const {\n      __scopeMenu,\n      loop = false,\n      trapFocus,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      disableOutsidePointerEvents,\n      onEntryFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      disableOutsideScroll,\n      ...contentProps\n    } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = React.useState<string | null>(null);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = React.useRef(0);\n    const searchRef = React.useRef('');\n    const pointerGraceTimerRef = React.useRef(0);\n    const pointerGraceIntentRef = React.useRef<GraceIntent | null>(null);\n    const pointerDirRef = React.useRef<Side>('right');\n    const lastPointerXRef = React.useRef(0);\n\n    const ScrollLockWrapper = disableOutsideScroll ? RemoveScroll : React.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll\n      ? { as: Slot, allowPinchZoom: true }\n      : undefined;\n\n    const handleTypeaheadSearch = (key: string) => {\n      const search = searchRef.current + key;\n      const items = getItems().filter((item) => !item.disabled);\n      const currentItem = document.activeElement;\n      const currentMatch = items.find((item) => item.ref.current === currentItem)?.textValue;\n      const values = items.map((item) => item.textValue);\n      const nextMatch = getNextMatch(values, search, currentMatch);\n      const newItem = items.find((item) => item.textValue === nextMatch)?.ref.current;\n\n      // Reset `searchRef` 1 second after it was last updated\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n\n      if (newItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (newItem as HTMLElement).focus());\n      }\n    };\n\n    React.useEffect(() => {\n      return () => window.clearTimeout(timerRef.current);\n    }, []);\n\n    // Make sure the whole tree has focus guards as our `MenuContent` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const isPointerMovingToSubmenu = React.useCallback((event: React.PointerEvent) => {\n      const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n      return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n    }, []);\n\n    return (\n      <MenuContentProvider\n        scope={__scopeMenu}\n        searchRef={searchRef}\n        onItemEnter={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onItemLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) return;\n            contentRef.current?.focus();\n            setCurrentItemId(null);\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onTriggerLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        pointerGraceTimerRef={pointerGraceTimerRef}\n        onPointerGraceIntentChange={React.useCallback((intent) => {\n          pointerGraceIntentRef.current = intent;\n        }, [])}\n      >\n        <ScrollLockWrapper {...scrollLockWrapperProps}>\n          <FocusScope\n            asChild\n            trapped={trapFocus}\n            onMountAutoFocus={composeEventHandlers(onOpenAutoFocus, (event) => {\n              // when opening, explicitly focus the content area only and leave\n              // `onEntryFocus` in  control of focusing first item\n              event.preventDefault();\n              contentRef.current?.focus({ preventScroll: true });\n            })}\n            onUnmountAutoFocus={onCloseAutoFocus}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents={disableOutsidePointerEvents}\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              onFocusOutside={onFocusOutside}\n              onInteractOutside={onInteractOutside}\n              onDismiss={onDismiss}\n            >\n              <RovingFocusGroup.Root\n                asChild\n                {...rovingFocusGroupScope}\n                dir={rootContext.dir}\n                orientation=\"vertical\"\n                loop={loop}\n                currentTabStopId={currentItemId}\n                onCurrentTabStopIdChange={setCurrentItemId}\n                onEntryFocus={composeEventHandlers(onEntryFocus, (event) => {\n                  // only focus first item when using keyboard\n                  if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                })}\n                preventScrollOnEntryFocus\n              >\n                <PopperPrimitive.Content\n                  role=\"menu\"\n                  aria-orientation=\"vertical\"\n                  data-state={getOpenState(context.open)}\n                  data-radix-menu-content=\"\"\n                  dir={rootContext.dir}\n                  {...popperScope}\n                  {...contentProps}\n                  ref={composedRefs}\n                  style={{ outline: 'none', ...contentProps.style }}\n                  onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                    // submenu key events bubble through portals. We only care about keys in this menu.\n                    const target = event.target as HTMLElement;\n                    const isKeyDownInside =\n                      target.closest('[data-radix-menu-content]') === event.currentTarget;\n                    const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                    const isCharacterKey = event.key.length === 1;\n                    if (isKeyDownInside) {\n                      // menus should not be navigated using tab key so we prevent it\n                      if (event.key === 'Tab') event.preventDefault();\n                      if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                    }\n                    // focus first/last item based on key pressed\n                    const content = contentRef.current;\n                    if (event.target !== content) return;\n                    if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item) => !item.disabled);\n                    const candidateNodes = items.map((item) => item.ref.current!);\n                    if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                    focusFirst(candidateNodes);\n                  })}\n                  onBlur={composeEventHandlers(props.onBlur, (event) => {\n                    // clear search buffer when leaving the menu\n                    if (!event.currentTarget.contains(event.target)) {\n                      window.clearTimeout(timerRef.current);\n                      searchRef.current = '';\n                    }\n                  })}\n                  onPointerMove={composeEventHandlers(\n                    props.onPointerMove,\n                    whenMouse((event) => {\n                      const target = event.target as HTMLElement;\n                      const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n\n                      // We don't use `event.movementX` for this check because Safari will\n                      // always return `0` on a pointer event.\n                      if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                        const newDir = event.clientX > lastPointerXRef.current ? 'right' : 'left';\n                        pointerDirRef.current = newDir;\n                        lastPointerXRef.current = event.clientX;\n                      }\n                    })\n                  )}\n                />\n              </RovingFocusGroup.Root>\n            </DismissableLayer>\n          </FocusScope>\n        </ScrollLockWrapper>\n      </MenuContentProvider>\n    );\n  }\n);\n\nMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'MenuGroup';\n\ntype MenuGroupElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface MenuGroupProps extends PrimitiveDivProps {}\n\nconst MenuGroup = React.forwardRef<MenuGroupElement, MenuGroupProps>(\n  (props: ScopedProps<MenuGroupProps>, forwardedRef) => {\n    const { __scopeMenu, ...groupProps } = props;\n    return <Primitive.div role=\"group\" {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'MenuLabel';\n\ntype MenuLabelElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuLabelProps extends PrimitiveDivProps {}\n\nconst MenuLabel = React.forwardRef<MenuLabelElement, MenuLabelProps>(\n  (props: ScopedProps<MenuLabelProps>, forwardedRef) => {\n    const { __scopeMenu, ...labelProps } = props;\n    return <Primitive.div {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'MenuItem';\nconst ITEM_SELECT = 'menu.itemSelect';\n\ntype MenuItemElement = MenuItemImplElement;\ninterface MenuItemProps extends Omit<MenuItemImplProps, 'onSelect'> {\n  onSelect?: (event: Event) => void;\n}\n\nconst MenuItem = React.forwardRef<MenuItemElement, MenuItemProps>(\n  (props: ScopedProps<MenuItemProps>, forwardedRef) => {\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = React.useRef<HTMLDivElement>(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const isPointerDownRef = React.useRef(false);\n\n    const handleSelect = () => {\n      const menuItem = ref.current;\n      if (!disabled && menuItem) {\n        const itemSelectEvent = new CustomEvent(ITEM_SELECT, { bubbles: true, cancelable: true });\n        menuItem.addEventListener(ITEM_SELECT, (event) => onSelect?.(event), { once: true });\n        dispatchDiscreteCustomEvent(menuItem, itemSelectEvent);\n        if (itemSelectEvent.defaultPrevented) {\n          isPointerDownRef.current = false;\n        } else {\n          rootContext.onClose();\n        }\n      }\n    };\n\n    return (\n      <MenuItemImpl\n        {...itemProps}\n        ref={composedRefs}\n        disabled={disabled}\n        onClick={composeEventHandlers(props.onClick, handleSelect)}\n        onPointerDown={(event) => {\n          props.onPointerDown?.(event);\n          isPointerDownRef.current = true;\n        }}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          // Pointer down can move to a different menu item which should activate it on pointer up.\n          // We dispatch a click for selection to allow composition with click based triggers and to\n          // prevent Firefox from getting stuck in text selection mode when the menu closes.\n          if (!isPointerDownRef.current) event.currentTarget?.click();\n        })}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          const isTypingAhead = contentContext.searchRef.current !== '';\n          if (disabled || (isTypingAhead && event.key === ' ')) return;\n          if (SELECTION_KEYS.includes(event.key)) {\n            event.currentTarget.click();\n            /**\n             * We prevent default browser behaviour for selection keys as they should trigger\n             * a selection only:\n             * - prevents space from scrolling the page.\n             * - if keydown causes focus to move, prevents keydown from firing on the new target.\n             */\n            event.preventDefault();\n          }\n        })}\n      />\n    );\n  }\n);\n\nMenuItem.displayName = ITEM_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuItemImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuItemImplProps extends PrimitiveDivProps {\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst MenuItemImpl = React.forwardRef<MenuItemImplElement, MenuItemImplProps>(\n  (props: ScopedProps<MenuItemImplProps>, forwardedRef) => {\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const [isFocused, setIsFocused] = React.useState(false);\n\n    // get the item's `.textContent` as default strategy for typeahead `textValue`\n    const [textContent, setTextContent] = React.useState('');\n    React.useEffect(() => {\n      const menuItem = ref.current;\n      if (menuItem) {\n        setTextContent((menuItem.textContent ?? '').trim());\n      }\n    }, [itemProps.children]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeMenu}\n        disabled={disabled}\n        textValue={textValue ?? textContent}\n      >\n        <RovingFocusGroup.Item asChild {...rovingFocusGroupScope} focusable={!disabled}>\n          <Primitive.div\n            role=\"menuitem\"\n            data-highlighted={isFocused ? '' : undefined}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            {...itemProps}\n            ref={composedRefs}\n            /**\n             * We focus items on `pointerMove` to achieve the following:\n             *\n             * - Mouse over an item (it focuses)\n             * - Leave mouse where it is and use keyboard to focus a different item\n             * - Wiggle mouse without it leaving previously focused item\n             * - Previously focused item should re-focus\n             *\n             * If we used `mouseOver`/`mouseEnter` it would not re-focus when the mouse\n             * wiggles. This is to match native menu implementation.\n             */\n            onPointerMove={composeEventHandlers(\n              props.onPointerMove,\n              whenMouse((event) => {\n                if (disabled) {\n                  contentContext.onItemLeave(event);\n                } else {\n                  contentContext.onItemEnter(event);\n                  if (!event.defaultPrevented) {\n                    const item = event.currentTarget;\n                    item.focus({ preventScroll: true });\n                  }\n                }\n              })\n            )}\n            onPointerLeave={composeEventHandlers(\n              props.onPointerLeave,\n              whenMouse((event) => contentContext.onItemLeave(event))\n            )}\n            onFocus={composeEventHandlers(props.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(props.onBlur, () => setIsFocused(false))}\n          />\n        </RovingFocusGroup.Item>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * MenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'MenuCheckboxItem';\n\ntype MenuCheckboxItemElement = MenuItemElement;\n\ntype CheckedState = boolean | 'indeterminate';\n\ninterface MenuCheckboxItemProps extends MenuItemProps {\n  checked?: CheckedState;\n  // `onCheckedChange` can never be called with `\"indeterminate\"` from the inside\n  onCheckedChange?: (checked: boolean) => void;\n}\n\nconst MenuCheckboxItem = React.forwardRef<MenuCheckboxItemElement, MenuCheckboxItemProps>(\n  (props: ScopedProps<MenuCheckboxItemProps>, forwardedRef) => {\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemcheckbox\"\n          aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n          {...checkboxItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            checkboxItemProps.onSelect,\n            () => onCheckedChange?.(isIndeterminate(checked) ? true : !checked),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'MenuRadioGroup';\n\nconst [RadioGroupProvider, useRadioGroupContext] = createMenuContext<MenuRadioGroupProps>(\n  RADIO_GROUP_NAME,\n  { value: undefined, onValueChange: () => {} }\n);\n\ntype MenuRadioGroupElement = React.ComponentRef<typeof MenuGroup>;\ninterface MenuRadioGroupProps extends MenuGroupProps {\n  value?: string;\n  onValueChange?: (value: string) => void;\n}\n\nconst MenuRadioGroup = React.forwardRef<MenuRadioGroupElement, MenuRadioGroupProps>(\n  (props: ScopedProps<MenuRadioGroupProps>, forwardedRef) => {\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = useCallbackRef(onValueChange);\n    return (\n      <RadioGroupProvider scope={props.__scopeMenu} value={value} onValueChange={handleValueChange}>\n        <MenuGroup {...groupProps} ref={forwardedRef} />\n      </RadioGroupProvider>\n    );\n  }\n);\n\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'MenuRadioItem';\n\ntype MenuRadioItemElement = React.ComponentRef<typeof MenuItem>;\ninterface MenuRadioItemProps extends MenuItemProps {\n  value: string;\n}\n\nconst MenuRadioItem = React.forwardRef<MenuRadioItemElement, MenuRadioItemProps>(\n  (props: ScopedProps<MenuRadioItemProps>, forwardedRef) => {\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemradio\"\n          aria-checked={checked}\n          {...radioItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            radioItemProps.onSelect,\n            () => context.onValueChange?.(value),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'MenuItemIndicator';\n\ntype CheckboxContextValue = { checked: CheckedState };\n\nconst [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext<CheckboxContextValue>(\n  ITEM_INDICATOR_NAME,\n  { checked: false }\n);\n\ntype MenuItemIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface MenuItemIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuItemIndicator = React.forwardRef<MenuItemIndicatorElement, MenuItemIndicatorProps>(\n  (props: ScopedProps<MenuItemIndicatorProps>, forwardedRef) => {\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return (\n      <Presence\n        present={\n          forceMount ||\n          isIndeterminate(indicatorContext.checked) ||\n          indicatorContext.checked === true\n        }\n      >\n        <Primitive.span\n          {...itemIndicatorProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(indicatorContext.checked)}\n        />\n      </Presence>\n    );\n  }\n);\n\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'MenuSeparator';\n\ntype MenuSeparatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuSeparatorProps extends PrimitiveDivProps {}\n\nconst MenuSeparator = React.forwardRef<MenuSeparatorElement, MenuSeparatorProps>(\n  (props: ScopedProps<MenuSeparatorProps>, forwardedRef) => {\n    const { __scopeMenu, ...separatorProps } = props;\n    return (\n      <Primitive.div\n        role=\"separator\"\n        aria-orientation=\"horizontal\"\n        {...separatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'MenuArrow';\n\ntype MenuArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface MenuArrowProps extends PopperArrowProps {}\n\nconst MenuArrow = React.forwardRef<MenuArrowElement, MenuArrowProps>(\n  (props: ScopedProps<MenuArrowProps>, forwardedRef) => {\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'MenuSub';\n\ntype MenuSubContextValue = {\n  contentId: string;\n  triggerId: string;\n  trigger: MenuSubTriggerElement | null;\n  onTriggerChange(trigger: MenuSubTriggerElement | null): void;\n};\n\nconst [MenuSubProvider, useMenuSubContext] = createMenuContext<MenuSubContextValue>(SUB_NAME);\n\ninterface MenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst MenuSub: React.FC<MenuSubProps> = (props: ScopedProps<MenuSubProps>) => {\n  const { __scopeMenu, children, open = false, onOpenChange } = props;\n  const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n  const popperScope = usePopperScope(__scopeMenu);\n  const [trigger, setTrigger] = React.useState<MenuSubTriggerElement | null>(null);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n\n  // Prevent the parent menu from reopening with open submenus.\n  React.useEffect(() => {\n    if (parentMenuContext.open === false) handleOpenChange(false);\n    return () => handleOpenChange(false);\n  }, [parentMenuContext.open, handleOpenChange]);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuSubProvider\n          scope={__scopeMenu}\n          contentId={useId()}\n          triggerId={useId()}\n          trigger={trigger}\n          onTriggerChange={setTrigger}\n        >\n          {children}\n        </MenuSubProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenuSub.displayName = SUB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'MenuSubTrigger';\n\ntype MenuSubTriggerElement = MenuItemImplElement;\ninterface MenuSubTriggerProps extends MenuItemImplProps {}\n\nconst MenuSubTrigger = React.forwardRef<MenuSubTriggerElement, MenuSubTriggerProps>(\n  (props: ScopedProps<MenuSubTriggerProps>, forwardedRef) => {\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = React.useRef<number | null>(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = { __scopeMenu: props.__scopeMenu };\n\n    const clearOpenTimer = React.useCallback(() => {\n      if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n      openTimerRef.current = null;\n    }, []);\n\n    React.useEffect(() => clearOpenTimer, [clearOpenTimer]);\n\n    React.useEffect(() => {\n      const pointerGraceTimer = pointerGraceTimerRef.current;\n      return () => {\n        window.clearTimeout(pointerGraceTimer);\n        onPointerGraceIntentChange(null);\n      };\n    }, [pointerGraceTimerRef, onPointerGraceIntentChange]);\n\n    return (\n      <MenuAnchor asChild {...scope}>\n        <MenuItemImpl\n          id={subContext.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={subContext.contentId}\n          data-state={getOpenState(context.open)}\n          {...props}\n          ref={composeRefs(forwardedRef, subContext.onTriggerChange)}\n          // This is redundant for mouse users but we cannot determine pointer type from\n          // click event and we cannot use pointerup event (see git history for reasons why)\n          onClick={(event) => {\n            props.onClick?.(event);\n            if (props.disabled || event.defaultPrevented) return;\n            /**\n             * We manually focus because iOS Safari doesn't always focus on click (e.g. buttons)\n             * and we rely heavily on `onFocusOutside` for submenus to close when switching\n             * between separate submenus.\n             */\n            event.currentTarget.focus();\n            if (!context.open) context.onOpenChange(true);\n          }}\n          onPointerMove={composeEventHandlers(\n            props.onPointerMove,\n            whenMouse((event) => {\n              contentContext.onItemEnter(event);\n              if (event.defaultPrevented) return;\n              if (!props.disabled && !context.open && !openTimerRef.current) {\n                contentContext.onPointerGraceIntentChange(null);\n                openTimerRef.current = window.setTimeout(() => {\n                  context.onOpenChange(true);\n                  clearOpenTimer();\n                }, 100);\n              }\n            })\n          )}\n          onPointerLeave={composeEventHandlers(\n            props.onPointerLeave,\n            whenMouse((event) => {\n              clearOpenTimer();\n\n              const contentRect = context.content?.getBoundingClientRect();\n              if (contentRect) {\n                // TODO: make sure to update this when we change positioning logic\n                const side = context.content?.dataset.side as Side;\n                const rightSide = side === 'right';\n                const bleed = rightSide ? -5 : +5;\n                const contentNearEdge = contentRect[rightSide ? 'left' : 'right'];\n                const contentFarEdge = contentRect[rightSide ? 'right' : 'left'];\n\n                contentContext.onPointerGraceIntentChange({\n                  area: [\n                    // Apply a bleed on clientX to ensure that our exit point is\n                    // consistently within polygon bounds\n                    { x: event.clientX + bleed, y: event.clientY },\n                    { x: contentNearEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.bottom },\n                    { x: contentNearEdge, y: contentRect.bottom },\n                  ],\n                  side,\n                });\n\n                window.clearTimeout(pointerGraceTimerRef.current);\n                pointerGraceTimerRef.current = window.setTimeout(\n                  () => contentContext.onPointerGraceIntentChange(null),\n                  300\n                );\n              } else {\n                contentContext.onTriggerLeave(event);\n                if (event.defaultPrevented) return;\n\n                // There's 100ms where the user may leave an item before the submenu was opened.\n                contentContext.onPointerGraceIntentChange(null);\n              }\n            })\n          )}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            const isTypingAhead = contentContext.searchRef.current !== '';\n            if (props.disabled || (isTypingAhead && event.key === ' ')) return;\n            if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n              context.onOpenChange(true);\n              // The trigger may hold focus if opened via pointer interaction\n              // so we ensure content is given focus again when switching to keyboard.\n              context.content?.focus();\n              // prevent window from scrolling\n              event.preventDefault();\n            }\n          })}\n        />\n      </MenuAnchor>\n    );\n  }\n);\n\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'MenuSubContent';\n\ntype MenuSubContentElement = MenuContentImplElement;\ninterface MenuSubContentProps\n  extends Omit<\n    MenuContentImplProps,\n    keyof MenuContentImplPrivateProps | 'onCloseAutoFocus' | 'onEntryFocus' | 'side' | 'align'\n  > {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuSubContent = React.forwardRef<MenuSubContentElement, MenuSubContentProps>(\n  (props: ScopedProps<MenuSubContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuSubContentElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            <MenuContentImpl\n              id={subContext.contentId}\n              aria-labelledby={subContext.triggerId}\n              {...subContentProps}\n              ref={composedRefs}\n              align=\"start\"\n              side={rootContext.dir === 'rtl' ? 'left' : 'right'}\n              disableOutsidePointerEvents={false}\n              disableOutsideScroll={false}\n              trapFocus={false}\n              onOpenAutoFocus={(event) => {\n                // when opening a submenu, focus content for keyboard users only\n                if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                event.preventDefault();\n              }}\n              // The menu might close because of focusing another menu item in the parent menu. We\n              // don't want it to refocus the trigger in that case so we handle trigger focus ourselves.\n              onCloseAutoFocus={(event) => event.preventDefault()}\n              onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) => {\n                // We prevent closing when the trigger is focused to avoid triggering a re-open animation\n                // on pointer interaction.\n                if (event.target !== subContext.trigger) context.onOpenChange(false);\n              })}\n              onEscapeKeyDown={composeEventHandlers(props.onEscapeKeyDown, (event) => {\n                rootContext.onClose();\n                // ensure pressing escape in submenu doesn't escape full screen mode\n                event.preventDefault();\n              })}\n              onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                // Submenu key events bubble through portals. We only care about keys in this menu.\n                const isKeyDownInside = event.currentTarget.contains(event.target as HTMLElement);\n                const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                if (isKeyDownInside && isCloseKey) {\n                  context.onOpenChange(false);\n                  // We focus manually because we prevented it in `onCloseAutoFocus`\n                  subContext.trigger?.focus();\n                  // prevent window from scrolling\n                  event.preventDefault();\n                }\n              })}\n            />\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\nMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getOpenState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getCheckedState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus();\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in all the values,\n * the search and the current match, and returns the next match (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through options starting with that character)\n *\n * We also reorder the values by wrapping the array around the current match.\n * This is so we always look forward from the current match, and picking the first\n * match will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current match from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current match still matches.\n */\nfunction getNextMatch(values: string[], search: string, currentMatch?: string) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n  let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n  const excludeCurrentMatch = normalizedSearch.length === 1;\n  if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v) => v !== currentMatch);\n  const nextMatch = wrappedValues.find((value) =>\n    value.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextMatch !== currentMatch ? nextMatch : undefined;\n}\n\ntype Point = { x: number; y: number };\ntype Polygon = Point[];\ntype Side = 'left' | 'right';\ntype GraceIntent = { area: Polygon; side: Side };\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const ii = polygon[i]!;\n    const jj = polygon[j]!;\n    const xi = ii.x;\n    const yi = ii.y;\n    const xj = jj.x;\n    const yj = jj.y;\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n    if (intersect) inside = !inside;\n  }\n\n  return inside;\n}\n\nfunction isPointerInGraceArea(event: React.PointerEvent, area?: Polygon) {\n  if (!area) return false;\n  const cursorPos = { x: event.clientX, y: event.clientY };\n  return isPointInPolygon(cursorPos, area);\n}\n\nfunction whenMouse<E>(handler: React.PointerEventHandler<E>): React.PointerEventHandler<E> {\n  return (event) => (event.pointerType === 'mouse' ? handler(event) : undefined);\n}\n\nconst Root = Menu;\nconst Anchor = MenuAnchor;\nconst Portal = MenuPortal;\nconst Content = MenuContent;\nconst Group = MenuGroup;\nconst Label = MenuLabel;\nconst Item = MenuItem;\nconst CheckboxItem = MenuCheckboxItem;\nconst RadioGroup = MenuRadioGroup;\nconst RadioItem = MenuRadioItem;\nconst ItemIndicator = MenuItemIndicator;\nconst Separator = MenuSeparator;\nconst Arrow = MenuArrow;\nconst Sub = MenuSub;\nconst SubTrigger = MenuSubTrigger;\nconst SubContent = MenuSubContent;\n\nexport {\n  createMenuScope,\n  //\n  Menu,\n  MenuAnchor,\n  MenuPortal,\n  MenuContent,\n  MenuGroup,\n  MenuLabel,\n  MenuItem,\n  MenuCheckboxItem,\n  MenuRadioGroup,\n  MenuRadioItem,\n  MenuItemIndicator,\n  MenuSeparator,\n  MenuArrow,\n  MenuSub,\n  MenuSubTrigger,\n  MenuSubContent,\n  //\n  Root,\n  Anchor,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  MenuProps,\n  MenuAnchorProps,\n  MenuPortalProps,\n  MenuContentProps,\n  MenuGroupProps,\n  MenuLabelProps,\n  MenuItemProps,\n  MenuCheckboxItemProps,\n  MenuRadioGroupProps,\n  MenuRadioItemProps,\n  MenuItemIndicatorProps,\n  MenuSeparatorProps,\n  MenuArrowProps,\n  MenuSubProps,\n  MenuSubTriggerProps,\n  MenuSubContentProps,\n};\n"], "names": ["Root", "<PERSON><PERSON>", "Content", "<PERSON><PERSON>", "Arrow"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,wBAAwB;AACjC,SAAS,iBAAiB,mBAAmB;AAC7C,SAAS,0BAA0B;AACnC,SAAS,oBAAoB;AAC7B,SAAS,wBAAwB;AACjC,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAC3B,SAAS,aAAa;AACtB,YAAY,qBAAqB;AAEjC,SAAS,UAAU,uBAAuB;AAC1C,SAAS,gBAAgB;AACzB,SAAS,WAAW,mCAAmC;AACvD,YAAY,sBAAsB;AAElC,SAAS,kBAAkB;AAC3B,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAC3B,SAAS,oBAAoB;AAoGrB;;;;;;;;;;;;;;;;;;;;;;;;AA9FR,IAAM,iBAAiB;IAAC;IAAS,GAAG;CAAA;AACpC,IAAM,aAAa;IAAC;IAAa;IAAU,MAAM;CAAA;AACjD,IAAM,YAAY;IAAC;IAAW;IAAY,KAAK;CAAA;AAC/C,IAAM,kBAAkB,CAAC;OAAG,YAAY;OAAG,SAAS;CAAA;AACpD,IAAM,gBAA6C;IACjD,KAAK,CAAC;WAAG;QAAgB,YAAY;KAAA;IACrC,KAAK,CAAC;WAAG;QAAgB,WAAW;KAAA;AACtC;AACA,IAAM,iBAA8C;IAClD,KAAK;QAAC,WAAW;KAAA;IACjB,KAAK;QAAC,YAAY;KAAA;AACpB;AAMA,IAAM,YAAY;AAGlB,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,OAAI,gTAAA,EAGzD,SAAS;AAGX,IAAM,CAAC,mBAAmB,eAAe,CAAA,OAAI,kTAAA,EAAmB,WAAW;IACzE;IACA,mTAAA;IACA,gUAAA;CACD;AACD,IAAM,qBAAiB,mTAAA,CAAkB;AACzC,IAAM,+BAA2B,gUAAA,CAA4B;AAS7D,IAAM,CAAC,cAAc,cAAc,CAAA,GAAI,kBAAoC,SAAS;AASpF,IAAM,CAAC,kBAAkB,kBAAkB,CAAA,GAAI,kBAAwC,SAAS;AAUhG,IAAM,OAA4B,CAAC,UAAkC;IACnE,MAAM,EAAE,WAAA,EAAa,OAAO,KAAA,EAAO,QAAA,EAAU,GAAA,EAAK,YAAA,EAAc,QAAQ,IAAA,CAAK,CAAA,GAAI;IACjF,MAAM,cAAc,eAAe,WAAW;IAC9C,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,oTAAA,CAAoC,IAAI;IAC5E,MAAM,qBAA2B,kTAAA,CAAO,KAAK;IAC7C,MAAM,uBAAmB,0TAAA,EAAe,YAAY;IACpD,MAAM,gBAAY,2SAAA,EAAa,GAAG;IAE5B,qTAAA;0BAAU,MAAM;YAGpB,MAAM;gDAAgB,MAAM;oBAC1B,mBAAmB,OAAA,GAAU;oBAC7B,SAAS,gBAAA,CAAiB,eAAe,eAAe;wBAAE,SAAS;wBAAM,MAAM;oBAAK,CAAC;oBACrF,SAAS,gBAAA,CAAiB,eAAe,eAAe;wBAAE,SAAS;wBAAM,MAAM;oBAAK,CAAC;gBACvF;;YACA,MAAM;gDAAgB,IAAO,mBAAmB,OAAA,GAAU;;YAC1D,SAAS,gBAAA,CAAiB,WAAW,eAAe;gBAAE,SAAS;YAAK,CAAC;YACrE;kCAAO,MAAM;oBACX,SAAS,mBAAA,CAAoB,WAAW,eAAe;wBAAE,SAAS;oBAAK,CAAC;oBACxE,SAAS,mBAAA,CAAoB,eAAe,eAAe;wBAAE,SAAS;oBAAK,CAAC;oBAC5E,SAAS,mBAAA,CAAoB,eAAe,eAAe;wBAAE,SAAS;oBAAK,CAAC;gBAC9E;;QACF;yBAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,IAAA,wTAAA,EAAiB,sSAAA,EAAhB;QAAsB,GAAG,WAAA;QACxB,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,cAAA;YACC,OAAO;YACP;YACA,cAAc;YACd;YACA,iBAAiB;YAEjB,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,kBAAA;gBACC,OAAO;gBACP,SAAe,uTAAA;wCAAY,IAAM,iBAAiB,KAAK;uCAAG;oBAAC,gBAAgB;iBAAC;gBAC5E;gBACA,KAAK;gBACL;gBAEC;YAAA;QACH;IACF,CACF;AAEJ;AAEA,KAAK,WAAA,GAAc;AAMnB,IAAM,cAAc;AAMpB,IAAM,aAAmB,sTAAA,CACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EAAE,WAAA,EAAa,GAAG,YAAY,CAAA,GAAI;IACxC,MAAM,cAAc,eAAe,WAAW;IAC9C,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAiB,wSAAA,EAAhB;QAAwB,GAAG,WAAA;QAAc,GAAG,WAAA;QAAa,KAAK;IAAA,CAAc;AACtF;AAGF,WAAW,WAAA,GAAc;AAMzB,IAAM,cAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,kBAAsC,aAAa;IAC5F,YAAY,KAAA;AACd,CAAC;AAgBD,IAAM,aAAwC,CAAC,UAAwC;IACrF,MAAM,EAAE,WAAA,EAAa,UAAA,EAAY,QAAA,EAAU,SAAA,CAAU,CAAA,GAAI;IACzD,MAAM,UAAU,eAAe,aAAa,WAAW;IACvD,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,gBAAA;QAAe,OAAO;QAAa;QAClC,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,ySAAA,EAAA;YAAS,SAAS,cAAc,QAAQ,IAAA;YACvC,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,wSAAA,EAAA;gBAAgB,SAAO;gBAAC;gBACtB;YAAA,CACH;QAAA,CACF;IAAA,CACF;AAEJ;AAEA,WAAW,WAAA,GAAc;AAMzB,IAAM,eAAe;AAUrB,IAAM,CAAC,qBAAqB,qBAAqB,CAAA,GAC/C,kBAA2C,YAAY;AAgBzD,IAAM,cAAoB,sTAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,WAAW;IACtE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,eAAe,cAAc,MAAM,WAAW;IAC9D,MAAM,cAAc,mBAAmB,cAAc,MAAM,WAAW;IAEtE,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO,MAAM,WAAA;QAChC,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,ySAAA,EAAA;YAAS,SAAS,cAAc,QAAQ,IAAA;YACvC,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,WAAW,IAAA,EAAX;gBAAgB,OAAO,MAAM,WAAA;gBAC3B,UAAA,YAAY,KAAA,GACX,aAAA,GAAA,IAAA,wTAAA,EAAC,sBAAA;oBAAsB,GAAG,YAAA;oBAAc,KAAK;gBAAA,CAAc,IAE3D,aAAA,GAAA,IAAA,wTAAA,EAAC,yBAAA;oBAAyB,GAAG,YAAA;oBAAc,KAAK;gBAAA,CAAc;YAAA,CAElE;QAAA,CACF;IAAA,CACF;AAEJ;AASF,IAAM,uBAA6B,sTAAA,CACjC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,UAAU,eAAe,cAAc,MAAM,WAAW;IAC9D,MAAM,MAAY,kTAAA,CAAmC,IAAI;IACzD,MAAM,mBAAe,oTAAA,EAAgB,cAAc,GAAG;IAGhD,qTAAA;0CAAU,MAAM;YACpB,MAAM,UAAU,IAAI,OAAA;YACpB,IAAI,QAAS,CAAA,WAAO,iPAAA,EAAW,OAAO;QACxC;yCAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,iBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QAGL,WAAW,QAAQ,IAAA;QAGnB,6BAA6B,QAAQ,IAAA;QACrC,sBAAoB;QAGpB,oBAAgB,0QAAA,EACd,MAAM,cAAA,EACN,CAAC,QAAU,MAAM,cAAA,CAAe,GAChC;YAAE,0BAA0B;QAAM;QAEpC,WAAW,IAAM,QAAQ,YAAA,CAAa,KAAK;IAAA;AAGjD;AAGF,IAAM,0BAAgC,sTAAA,CAGpC,CAAC,OAA8C,iBAAiB;IAChE,MAAM,UAAU,eAAe,cAAc,MAAM,WAAW;IAC9D,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,iBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QACL,WAAW;QACX,6BAA6B;QAC7B,sBAAsB;QACtB,WAAW,IAAM,QAAQ,YAAA,CAAa,KAAK;IAAA;AAGjD,CAAC;AAgDD,IAAM,WAAO,kUAAA,EAAW,wBAAwB;AAEhD,IAAM,kBAAwB,sTAAA,CAC5B,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EACJ,WAAA,EACA,OAAO,KAAA,EACP,SAAA,EACA,eAAA,EACA,gBAAA,EACA,2BAAA,EACA,YAAA,EACA,eAAA,EACA,oBAAA,EACA,cAAA,EACA,iBAAA,EACA,SAAA,EACA,oBAAA,EACA,GAAG,cACL,GAAI;IACJ,MAAM,UAAU,eAAe,cAAc,WAAW;IACxD,MAAM,cAAc,mBAAmB,cAAc,WAAW;IAChE,MAAM,cAAc,eAAe,WAAW;IAC9C,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,MAAM,WAAW,cAAc,WAAW;IAC1C,MAAM,CAAC,eAAe,gBAAgB,CAAA,GAAU,oTAAA,CAAwB,IAAI;IAC5E,MAAM,aAAmB,kTAAA,CAAuB,IAAI;IACpD,MAAM,mBAAe,oTAAA,EAAgB,cAAc,YAAY,QAAQ,eAAe;IACtF,MAAM,WAAiB,kTAAA,CAAO,CAAC;IAC/B,MAAM,YAAkB,kTAAA,CAAO,EAAE;IACjC,MAAM,uBAA6B,kTAAA,CAAO,CAAC;IAC3C,MAAM,wBAA8B,kTAAA,CAA2B,IAAI;IACnE,MAAM,gBAAsB,kTAAA,CAAa,OAAO;IAChD,MAAM,kBAAwB,kTAAA,CAAO,CAAC;IAEtC,MAAM,oBAAoB,uBAAuB,oXAAA,GAAqB,oTAAA;IACtE,MAAM,yBAAyB,uBAC3B;QAAE,IAAI;QAAM,gBAAgB;IAAK,IACjC,KAAA;IAEJ,MAAM,wBAAwB,CAAC,QAAgB;;QAC7C,MAAM,SAAS,UAAU,OAAA,GAAU;QACnC,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;QACxD,MAAM,cAAc,SAAS,aAAA;QAC7B,MAAM,oCAAqB,IAAA,CAAK,CAAC,OAAS,KAAK,GAAA,CAAI,OAAA,KAAY,WAAW,iDAArD,YAAwD,SAAA;QAC7E,MAAM,SAAS,MAAM,GAAA,CAAI,CAAC,OAAS,KAAK,SAAS;QACjD,MAAM,YAAY,aAAa,QAAQ,QAAQ,YAAY;QAC3D,MAAM,0BAAU,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,SAAA,KAAc,SAAS,+DAAG,GAAA,CAAI,OAAA;QAGxE,CAAC,SAAS,aAAa,KAAA,EAAe;YACpC,UAAU,OAAA,GAAU;YACpB,OAAO,YAAA,CAAa,SAAS,OAAO;YACpC,IAAI,UAAU,GAAI,CAAA,SAAS,OAAA,GAAU,OAAO,UAAA,CAAW,IAAM,aAAa,EAAE,GAAG,GAAI;QACrF,CAAA,EAAG,MAAM;QAET,IAAI,SAAS;YAKX,WAAW,IAAO,QAAwB,KAAA,CAAM,CAAC;QACnD;IACF;IAEM,qTAAA;qCAAU,MAAM;YACpB;6CAAO,IAAM,OAAO,YAAA,CAAa,SAAS,OAAO;;QACnD;oCAAG,CAAC,CAAC;IAIL,IAAA,mTAAA,CAAe;IAEf,MAAM,2BAAiC,uTAAA;iEAAY,CAAC,UAA8B;;YAChF,MAAM,kBAAkB,cAAc,OAAA,wCAAY,sBAAsB,OAAA,kGAAS,IAAA;YACjF,OAAO,mBAAmB,qBAAqB,0CAAO,sBAAsB,OAAA,oGAAS,IAAI;QAC3F;gEAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,qBAAA;QACC,OAAO;QACP;QACA,aAAmB,uTAAA;2CACjB,CAAC,UAAU;gBACT,IAAI,yBAAyB,KAAK,EAAG,CAAA,MAAM,cAAA,CAAe;YAC5D;0CACA;YAAC,wBAAwB;SAAA;QAE3B,aAAmB,uTAAA;2CACjB,CAAC,UAAU;oBAET;gBADA,IAAI,yBAAyB,KAAK,EAAG,CAAA;iBACrC,sBAAA,WAAW,OAAA,cAAX,0CAAA,oBAAoB,KAAA,CAAM;gBAC1B,iBAAiB,IAAI;YACvB;0CACA;YAAC,wBAAwB;SAAA;QAE3B,gBAAsB,uTAAA;2CACpB,CAAC,UAAU;gBACT,IAAI,yBAAyB,KAAK,EAAG,CAAA,MAAM,cAAA,CAAe;YAC5D;0CACA;YAAC,wBAAwB;SAAA;QAE3B;QACA,4BAAkC,uTAAA;2CAAY,CAAC,WAAW;gBACxD,sBAAsB,OAAA,GAAU;YAClC;0CAAG,CAAC,CAAC;QAEL,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,mBAAA;YAAmB,GAAG,sBAAA;YACrB,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,8SAAA,EAAA;gBACC,SAAO;gBACP,SAAS;gBACT,sBAAkB,0QAAA,EAAqB,iBAAiB,CAAC,UAAU;wBAIjE;oBADA,MAAM,cAAA,CAAe;qBACrB,sBAAA,WAAW,OAAA,cAAX,0CAAA,oBAAoB,KAAA,CAAM;wBAAE,eAAe;oBAAK,CAAC;gBACnD,CAAC;gBACD,oBAAoB;gBAEpB,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,uTAAA,EAAA;oBACC,SAAO;oBACP;oBACA;oBACA;oBACA;oBACA;oBACA;oBAEA,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAkB,ySAAA,EAAjB;wBACC,SAAO;wBACN,GAAG,qBAAA;wBACJ,KAAK,YAAY,GAAA;wBACjB,aAAY;wBACZ;wBACA,kBAAkB;wBAClB,0BAA0B;wBAC1B,cAAc,8QAAA,EAAqB,cAAc,CAAC,UAAU;4BAE1D,IAAI,CAAC,YAAY,kBAAA,CAAmB,OAAA,CAAS,CAAA,MAAM,cAAA,CAAe;wBACpE,CAAC;wBACD,2BAAyB;wBAEzB,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAiB,ySAAA,EAAhB;4BACC,MAAK;4BACL,oBAAiB;4BACjB,cAAY,aAAa,QAAQ,IAAI;4BACrC,2BAAwB;4BACxB,KAAK,YAAY,GAAA;4BAChB,GAAG,WAAA;4BACH,GAAG,YAAA;4BACJ,KAAK;4BACL,OAAO;gCAAE,SAAS;gCAAQ,GAAG,aAAa,KAAA;4BAAM;4BAChD,eAAW,0QAAA,EAAqB,aAAa,SAAA,EAAW,CAAC,UAAU;gCAEjE,MAAM,SAAS,MAAM,MAAA;gCACrB,MAAM,kBACJ,OAAO,OAAA,CAAQ,2BAA2B,MAAM,MAAM,aAAA;gCACxD,MAAM,gBAAgB,MAAM,OAAA,IAAW,MAAM,MAAA,IAAU,MAAM,OAAA;gCAC7D,MAAM,iBAAiB,MAAM,GAAA,CAAI,MAAA,KAAW;gCAC5C,IAAI,iBAAiB;oCAEnB,IAAI,MAAM,GAAA,KAAQ,MAAO,CAAA,MAAM,cAAA,CAAe;oCAC9C,IAAI,CAAC,iBAAiB,eAAgB,CAAA,sBAAsB,MAAM,GAAG;gCACvE;gCAEA,MAAM,UAAU,WAAW,OAAA;gCAC3B,IAAI,MAAM,MAAA,KAAW,QAAS,CAAA;gCAC9B,IAAI,CAAC,gBAAgB,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA;gCAC1C,MAAM,cAAA,CAAe;gCACrB,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;gCACxD,MAAM,iBAAiB,MAAM,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;gCAC5D,IAAI,UAAU,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,eAAe,OAAA,CAAQ;gCAC1D,WAAW,cAAc;4BAC3B,CAAC;4BACD,YAAQ,0QAAA,EAAqB,MAAM,MAAA,EAAQ,CAAC,UAAU;gCAEpD,IAAI,CAAC,MAAM,aAAA,CAAc,QAAA,CAAS,MAAM,MAAM,GAAG;oCAC/C,OAAO,YAAA,CAAa,SAAS,OAAO;oCACpC,UAAU,OAAA,GAAU;gCACtB;4BACF,CAAC;4BACD,mBAAe,0QAAA,EACb,MAAM,aAAA,EACN,UAAU,CAAC,UAAU;gCACnB,MAAM,SAAS,MAAM,MAAA;gCACrB,MAAM,qBAAqB,gBAAgB,OAAA,KAAY,MAAM,OAAA;gCAI7D,IAAI,MAAM,aAAA,CAAc,QAAA,CAAS,MAAM,KAAK,oBAAoB;oCAC9D,MAAM,SAAS,MAAM,OAAA,GAAU,gBAAgB,OAAA,GAAU,UAAU;oCACnE,cAAc,OAAA,GAAU;oCACxB,gBAAgB,OAAA,GAAU,MAAM,OAAA;gCAClC;4BACF,CAAC;wBACH;oBACF;gBACF;YACF;QACF,CACF;IAAA;AAGN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,aAAa;AAMnB,IAAM,YAAkB,sTAAA,CACtB,CAAC,OAAoC,iBAAiB;IACpD,MAAM,EAAE,WAAA,EAAa,GAAG,WAAW,CAAA,GAAI;IACvC,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAC,wSAAA,CAAU,GAAA,EAAV;QAAc,MAAK;QAAS,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AACxE;AAGF,UAAU,WAAA,GAAc;AAMxB,IAAM,aAAa;AAKnB,IAAM,YAAkB,sTAAA,CACtB,CAAC,OAAoC,iBAAiB;IACpD,MAAM,EAAE,WAAA,EAAa,GAAG,WAAW,CAAA,GAAI;IACvC,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAC,wSAAA,CAAU,GAAA,EAAV;QAAe,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAC3D;AAGF,UAAU,WAAA,GAAc;AAMxB,IAAM,YAAY;AAClB,IAAM,cAAc;AAOpB,IAAM,WAAiB,sTAAA,CACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EAAE,WAAW,KAAA,EAAO,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;IACrD,MAAM,MAAY,kTAAA,CAAuB,IAAI;IAC7C,MAAM,cAAc,mBAAmB,WAAW,MAAM,WAAW;IACnE,MAAM,iBAAiB,sBAAsB,WAAW,MAAM,WAAW;IACzE,MAAM,mBAAe,oTAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,mBAAyB,kTAAA,CAAO,KAAK;IAE3C,MAAM,eAAe,MAAM;QACzB,MAAM,WAAW,IAAI,OAAA;QACrB,IAAI,CAAC,YAAY,UAAU;YACzB,MAAM,kBAAkB,IAAI,YAAY,aAAa;gBAAE,SAAS;gBAAM,YAAY;YAAK,CAAC;YACxF,SAAS,gBAAA,CAAiB,aAAa,CAAC,4DAAU,SAAW,KAAK,GAAG;gBAAE,MAAM;YAAK,CAAC;YACnF,IAAA,0TAAA,EAA4B,UAAU,eAAe;YACrD,IAAI,gBAAgB,gBAAA,EAAkB;gBACpC,iBAAiB,OAAA,GAAU;YAC7B,OAAO;gBACL,YAAY,OAAA,CAAQ;YACtB;QACF;IACF;IAEA,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,cAAA;QACE,GAAG,SAAA;QACJ,KAAK;QACL;QACA,SAAS,8QAAA,EAAqB,MAAM,OAAA,EAAS,YAAY;QACzD,eAAe,CAAC,UAAU;gBACxB;aAAA,uBAAA,MAAM,aAAA,cAAN,2CAAA,0BAAA,OAAsB,KAAK;YAC3B,iBAAiB,OAAA,GAAU;QAC7B;QACA,aAAa,8QAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gBAI/B;YAA/B,IAAI,CAAC,iBAAiB,OAAA,CAAS,EAAA,uBAAA,MAAM,aAAA,cAAN,2CAAA,qBAAqB,KAAA,CAAM;QAC5D,CAAC;QACD,eAAW,0QAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;YAC1D,MAAM,gBAAgB,eAAe,SAAA,CAAU,OAAA,KAAY;YAC3D,IAAI,YAAa,iBAAiB,MAAM,GAAA,KAAQ,IAAM,CAAA;YACtD,IAAI,eAAe,QAAA,CAAS,MAAM,GAAG,GAAG;gBACtC,MAAM,aAAA,CAAc,KAAA,CAAM;gBAO1B,MAAM,cAAA,CAAe;YACvB;QACF,CAAC;IAAA;AAGP;AAGF,SAAS,WAAA,GAAc;AAUvB,IAAM,eAAqB,sTAAA,CACzB,CAAC,OAAuC,iBAAiB;IACvD,MAAM,EAAE,WAAA,EAAa,WAAW,KAAA,EAAO,SAAA,EAAW,GAAG,UAAU,CAAA,GAAI;IACnE,MAAM,iBAAiB,sBAAsB,WAAW,WAAW;IACnE,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,MAAM,MAAY,kTAAA,CAAuB,IAAI;IAC7C,MAAM,mBAAe,oTAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,CAAC,WAAW,YAAY,CAAA,GAAU,oTAAA,CAAS,KAAK;IAGtD,MAAM,CAAC,aAAa,cAAc,CAAA,GAAU,oTAAA,CAAS,EAAE;IACjD,qTAAA;kCAAU,MAAM;YACpB,MAAM,WAAW,IAAI,OAAA;YACrB,IAAI,UAAU;;gBACZ,eAAA,mCAAyB,WAAA,iDAAT,wBAAwB,EAAA,EAAI,IAAA,CAAK,CAAC;YACpD;QACF;iCAAG;QAAC,UAAU,QAAQ;KAAC;IAEvB,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,WAAW,QAAA,EAAX;QACC,OAAO;QACP;QACA,wDAAW,YAAa;QAExB,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAkB,ySAAA,EAAjB;YAAsB,SAAO;YAAE,GAAG,qBAAA;YAAuB,WAAW,CAAC;YACpE,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,wSAAA,CAAU,GAAA,EAAV;gBACC,MAAK;gBACL,oBAAkB,YAAY,KAAK,KAAA;gBACnC,iBAAe,YAAY,KAAA;gBAC3B,iBAAe,WAAW,KAAK,KAAA;gBAC9B,GAAG,SAAA;gBACJ,KAAK;gBAYL,mBAAe,0QAAA,EACb,MAAM,aAAA,EACN,UAAU,CAAC,UAAU;oBACnB,IAAI,UAAU;wBACZ,eAAe,WAAA,CAAY,KAAK;oBAClC,OAAO;wBACL,eAAe,WAAA,CAAY,KAAK;wBAChC,IAAI,CAAC,MAAM,gBAAA,EAAkB;4BAC3B,MAAM,OAAO,MAAM,aAAA;4BACnB,KAAK,KAAA,CAAM;gCAAE,eAAe;4BAAK,CAAC;wBACpC;oBACF;gBACF,CAAC;gBAEH,oBAAgB,0QAAA,EACd,MAAM,cAAA,EACN,UAAU,CAAC,QAAU,eAAe,WAAA,CAAY,KAAK,CAAC;gBAExD,aAAS,0QAAA,EAAqB,MAAM,OAAA,EAAS,IAAM,aAAa,IAAI,CAAC;gBACrE,YAAQ,0QAAA,EAAqB,MAAM,MAAA,EAAQ,IAAM,aAAa,KAAK,CAAC;YAAA;QACtE,CACF;IAAA;AAGN;AAOF,IAAM,qBAAqB;AAY3B,IAAM,mBAAyB,sTAAA,CAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EAAE,UAAU,KAAA,EAAO,eAAA,EAAiB,GAAG,kBAAkB,CAAA,GAAI;IACnE,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,uBAAA;QAAsB,OAAO,MAAM,WAAA;QAAa;QAC/C,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,UAAA;YACC,MAAK;YACL,gBAAc,gBAAgB,OAAO,IAAI,UAAU;YAClD,GAAG,iBAAA;YACJ,KAAK;YACL,cAAY,gBAAgB,OAAO;YACnC,UAAU,8QAAA,EACR,kBAAkB,QAAA,EAClB,sEAAM,gBAAkB,gBAAgB,OAAO,IAAI,OAAO,CAAC,OAAO,GAClE;gBAAE,0BAA0B;YAAM;QACpC;IACF,CACF;AAEJ;AAGF,iBAAiB,WAAA,GAAc;AAM/B,IAAM,mBAAmB;AAEzB,IAAM,CAAC,oBAAoB,oBAAoB,CAAA,GAAI,kBACjD,kBACA;IAAE,OAAO,KAAA;IAAW,eAAe,KAAO,CAAD;AAAG;AAS9C,IAAM,iBAAuB,sTAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,KAAA,EAAO,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IAChD,MAAM,wBAAoB,0TAAA,EAAe,aAAa;IACtD,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,oBAAA;QAAmB,OAAO,MAAM,WAAA;QAAa;QAAc,eAAe;QACzE,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,WAAA;YAAW,GAAG,UAAA;YAAY,KAAK;QAAA,CAAc;IAAA,CAChD;AAEJ;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,kBAAkB;AAOxB,IAAM,gBAAsB,sTAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,KAAA,EAAO,GAAG,eAAe,CAAA,GAAI;IACrC,MAAM,UAAU,qBAAqB,iBAAiB,MAAM,WAAW;IACvE,MAAM,UAAU,UAAU,QAAQ,KAAA;IAClC,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,uBAAA;QAAsB,OAAO,MAAM,WAAA;QAAa;QAC/C,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,UAAA;YACC,MAAK;YACL,gBAAc;YACb,GAAG,cAAA;YACJ,KAAK;YACL,cAAY,gBAAgB,OAAO;YACnC,cAAU,0QAAA,EACR,eAAe,QAAA,EACf;;yDAAc,aAAA,uFAAR,SAAwB,KAAK;eACnC;gBAAE,0BAA0B;YAAM;QACpC;IACF,CACF;AAEJ;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,sBAAsB;AAI5B,IAAM,CAAC,uBAAuB,uBAAuB,CAAA,GAAI,kBACvD,qBACA;IAAE,SAAS;AAAM;AAanB,IAAM,oBAA0B,sTAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,WAAA,EAAa,UAAA,EAAY,GAAG,mBAAmB,CAAA,GAAI;IAC3D,MAAM,mBAAmB,wBAAwB,qBAAqB,WAAW;IACjF,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,ySAAA,EAAA;QACC,SACE,cACA,gBAAgB,iBAAiB,OAAO,KACxC,iBAAiB,OAAA,KAAY;QAG/B,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,wSAAA,CAAU,IAAA,EAAV;YACE,GAAG,kBAAA;YACJ,KAAK;YACL,cAAY,gBAAgB,iBAAiB,OAAO;QAAA;IACtD;AAGN;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,iBAAiB;AAKvB,IAAM,gBAAsB,sTAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,WAAA,EAAa,GAAG,eAAe,CAAA,GAAI;IAC3C,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,wSAAA,CAAU,GAAA,EAAV;QACC,MAAK;QACL,oBAAiB;QAChB,GAAG,cAAA;QACJ,KAAK;IAAA;AAGX;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,aAAa;AAMnB,IAAM,YAAkB,sTAAA,CACtB,CAAC,OAAoC,iBAAiB;IACpD,MAAM,EAAE,WAAA,EAAa,GAAG,WAAW,CAAA,GAAI;IACvC,MAAM,cAAc,eAAe,WAAW;IAC9C,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAiB,uSAAA,EAAhB;QAAuB,GAAG,WAAA;QAAc,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AACpF;AAGF,UAAU,WAAA,GAAc;AAMxB,IAAM,WAAW;AASjB,IAAM,CAAC,iBAAiB,iBAAiB,CAAA,GAAI,kBAAuC,QAAQ;AAQ5F,IAAM,UAAkC,CAAC,UAAqC;IAC5E,MAAM,EAAE,WAAA,EAAa,QAAA,EAAU,OAAO,KAAA,EAAO,YAAA,CAAa,CAAA,GAAI;IAC9D,MAAM,oBAAoB,eAAe,UAAU,WAAW;IAC9D,MAAM,cAAc,eAAe,WAAW;IAC9C,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,oTAAA,CAAuC,IAAI;IAC/E,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,oTAAA,CAAoC,IAAI;IAC5E,MAAM,uBAAmB,0TAAA,EAAe,YAAY;IAG9C,qTAAA;6BAAU,MAAM;YACpB,IAAI,kBAAkB,IAAA,KAAS,MAAO,CAAA,iBAAiB,KAAK;YAC5D;qCAAO,IAAM,iBAAiB,KAAK;;QACrC;4BAAG;QAAC,kBAAkB,IAAA;QAAM,gBAAgB;KAAC;IAE7C,OACE,aAAA,GAAA,IAAA,wTAAA,EAAiB,sSAAA,EAAhB;QAAsB,GAAG,WAAA;QACxB,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,cAAA;YACC,OAAO;YACP;YACA,cAAc;YACd;YACA,iBAAiB;YAEjB,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,iBAAA;gBACC,OAAO;gBACP,WAAW,6TAAA,CAAM;gBACjB,eAAW,yTAAA,CAAM;gBACjB;gBACA,iBAAiB;gBAEhB;YAAA;QACH;IACF,CACF;AAEJ;AAEA,QAAQ,WAAA,GAAc;AAMtB,IAAM,mBAAmB;AAKzB,IAAM,iBAAuB,sTAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,UAAU,eAAe,kBAAkB,MAAM,WAAW;IAClE,MAAM,cAAc,mBAAmB,kBAAkB,MAAM,WAAW;IAC1E,MAAM,aAAa,kBAAkB,kBAAkB,MAAM,WAAW;IACxE,MAAM,iBAAiB,sBAAsB,kBAAkB,MAAM,WAAW;IAChF,MAAM,eAAqB,kTAAA,CAAsB,IAAI;IACrD,MAAM,EAAE,oBAAA,EAAsB,0BAAA,CAA2B,CAAA,GAAI;IAC7D,MAAM,QAAQ;QAAE,aAAa,MAAM,WAAA;IAAY;IAE/C,MAAM,iBAAuB,uTAAA;sDAAY,MAAM;YAC7C,IAAI,aAAa,OAAA,CAAS,CAAA,OAAO,YAAA,CAAa,aAAa,OAAO;YAClE,aAAa,OAAA,GAAU;QACzB;qDAAG,CAAC,CAAC;IAEC,qTAAA;oCAAU,IAAM;mCAAgB;QAAC,cAAc;KAAC;IAEhD,qTAAA;oCAAU,MAAM;YACpB,MAAM,oBAAoB,qBAAqB,OAAA;YAC/C;4CAAO,MAAM;oBACX,OAAO,YAAA,CAAa,iBAAiB;oBACrC,2BAA2B,IAAI;gBACjC;;QACF;mCAAG;QAAC;QAAsB,0BAA0B;KAAC;IAErD,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,YAAA;QAAW,SAAO;QAAE,GAAG,KAAA;QACtB,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,cAAA;YACC,IAAI,WAAW,SAAA;YACf,iBAAc;YACd,iBAAe,QAAQ,IAAA;YACvB,iBAAe,WAAW,SAAA;YAC1B,cAAY,aAAa,QAAQ,IAAI;YACpC,GAAG,KAAA;YACJ,SAAK,gTAAA,EAAY,cAAc,WAAW,eAAe;YAGzD,SAAS,CAAC,UAAU;oBAClB;iBAAA,iBAAA,MAAM,OAAA,cAAN,qCAAA,oBAAA,OAAgB,KAAK;gBACrB,IAAI,MAAM,QAAA,IAAY,MAAM,gBAAA,CAAkB,CAAA;gBAM9C,MAAM,aAAA,CAAc,KAAA,CAAM;gBAC1B,IAAI,CAAC,QAAQ,IAAA,CAAM,CAAA,QAAQ,YAAA,CAAa,IAAI;YAC9C;YACA,eAAe,8QAAA,EACb,MAAM,aAAA,EACN,UAAU,CAAC,UAAU;gBACnB,eAAe,WAAA,CAAY,KAAK;gBAChC,IAAI,MAAM,gBAAA,CAAkB,CAAA;gBAC5B,IAAI,CAAC,MAAM,QAAA,IAAY,CAAC,QAAQ,IAAA,IAAQ,CAAC,aAAa,OAAA,EAAS;oBAC7D,eAAe,0BAAA,CAA2B,IAAI;oBAC9C,aAAa,OAAA,GAAU,OAAO,UAAA,CAAW,MAAM;wBAC7C,QAAQ,YAAA,CAAa,IAAI;wBACzB,eAAe;oBACjB,GAAG,GAAG;gBACR;YACF,CAAC;YAEH,oBAAgB,0QAAA,EACd,MAAM,cAAA,EACN,UAAU,CAAC,UAAU;;gBACnB,eAAe;gBAEf,MAAM,0CAAsB,OAAA,qDAAR,iBAAiB,qBAAA,CAAsB;gBAC3D,IAAI,aAAa;;oBAEf,MAAM,oCAAe,OAAA,sDAAR,kBAAiB,OAAA,CAAQ,IAAA;oBACtC,MAAM,YAAY,SAAS;oBAC3B,MAAM,QAAQ,YAAY,CAAA,IAAK;oBAC/B,MAAM,kBAAkB,WAAA,CAAY,YAAY,SAAS,OAAO,CAAA;oBAChE,MAAM,iBAAiB,WAAA,CAAY,YAAY,UAAU,MAAM,CAAA;oBAE/D,eAAe,0BAAA,CAA2B;wBACxC,MAAM;4BAAA,4DAAA;4BAAA,qCAAA;4BAGJ;gCAAE,GAAG,MAAM,OAAA,GAAU;gCAAO,GAAG,MAAM,OAAA;4BAAQ;4BAC7C;gCAAE,GAAG;gCAAiB,GAAG,YAAY,GAAA;4BAAI;4BACzC;gCAAE,GAAG;gCAAgB,GAAG,YAAY,GAAA;4BAAI;4BACxC;gCAAE,GAAG;gCAAgB,GAAG,YAAY,MAAA;4BAAO;4BAC3C;gCAAE,GAAG;gCAAiB,GAAG,YAAY,MAAA;4BAAO;yBAC9C;wBACA;oBACF,CAAC;oBAED,OAAO,YAAA,CAAa,qBAAqB,OAAO;oBAChD,qBAAqB,OAAA,GAAU,OAAO,UAAA,CACpC,IAAM,eAAe,0BAAA,CAA2B,IAAI,GACpD;gBAEJ,OAAO;oBACL,eAAe,cAAA,CAAe,KAAK;oBACnC,IAAI,MAAM,gBAAA,CAAkB,CAAA;oBAG5B,eAAe,0BAAA,CAA2B,IAAI;gBAChD;YACF,CAAC;YAEH,eAAW,0QAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,MAAM,gBAAgB,eAAe,SAAA,CAAU,OAAA,KAAY;gBAC3D,IAAI,MAAM,QAAA,IAAa,iBAAiB,MAAM,GAAA,KAAQ,IAAM,CAAA;gBAC5D,IAAI,aAAA,CAAc,YAAY,GAAG,CAAA,CAAE,QAAA,CAAS,MAAM,GAAG,GAAG;wBAItD;oBAHA,QAAQ,YAAA,CAAa,IAAI;qBAGzB,mBAAA,QAAQ,OAAA,cAAR,uCAAA,iBAAiB,KAAA,CAAM;oBAEvB,MAAM,cAAA,CAAe;gBACvB;YACF,CAAC;QAAA;IACH,CACF;AAEJ;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,mBAAmB;AAezB,IAAM,iBAAuB,sTAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,WAAW;IACtE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,GAAG,gBAAgB,CAAA,GAAI;IACtE,MAAM,UAAU,eAAe,cAAc,MAAM,WAAW;IAC9D,MAAM,cAAc,mBAAmB,cAAc,MAAM,WAAW;IACtE,MAAM,aAAa,kBAAkB,kBAAkB,MAAM,WAAW;IACxE,MAAM,MAAY,kTAAA,CAA8B,IAAI;IACpD,MAAM,mBAAe,oTAAA,EAAgB,cAAc,GAAG;IACtD,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO,MAAM,WAAA;QAChC,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,ySAAA,EAAA;YAAS,SAAS,cAAc,QAAQ,IAAA;YACvC,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,WAAW,IAAA,EAAX;gBAAgB,OAAO,MAAM,WAAA;gBAC5B,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,iBAAA;oBACC,IAAI,WAAW,SAAA;oBACf,mBAAiB,WAAW,SAAA;oBAC3B,GAAG,eAAA;oBACJ,KAAK;oBACL,OAAM;oBACN,MAAM,YAAY,GAAA,KAAQ,QAAQ,SAAS;oBAC3C,6BAA6B;oBAC7B,sBAAsB;oBACtB,WAAW;oBACX,iBAAiB,CAAC,UAAU;4BAEkB;wBAA5C,IAAI,YAAY,kBAAA,CAAmB,OAAA,CAAS,EAAA,eAAA,IAAI,OAAA,cAAJ,mCAAA,aAAa,KAAA,CAAM;wBAC/D,MAAM,cAAA,CAAe;oBACvB;oBAGA,kBAAkB,CAAC,QAAU,MAAM,cAAA,CAAe;oBAClD,oBAAgB,0QAAA,EAAqB,MAAM,cAAA,EAAgB,CAAC,UAAU;wBAGpE,IAAI,MAAM,MAAA,KAAW,WAAW,OAAA,CAAS,CAAA,QAAQ,YAAA,CAAa,KAAK;oBACrE,CAAC;oBACD,qBAAiB,0QAAA,EAAqB,MAAM,eAAA,EAAiB,CAAC,UAAU;wBACtE,YAAY,OAAA,CAAQ;wBAEpB,MAAM,cAAA,CAAe;oBACvB,CAAC;oBACD,eAAW,0QAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;wBAE1D,MAAM,kBAAkB,MAAM,aAAA,CAAc,QAAA,CAAS,MAAM,MAAqB;wBAChF,MAAM,aAAa,cAAA,CAAe,YAAY,GAAG,CAAA,CAAE,QAAA,CAAS,MAAM,GAAG;wBACrE,IAAI,mBAAmB,YAAY;gCAGjC;4BAFA,QAAQ,YAAA,CAAa,KAAK;6BAE1B,sBAAA,WAAW,OAAA,cAAX,0CAAA,oBAAoB,KAAA,CAAM;4BAE1B,MAAM,cAAA,CAAe;wBACvB;oBACF,CAAC;gBAAA;YACH,CACF;QAAA,CACF;IAAA,CACF;AAEJ;AAGF,eAAe,WAAA,GAAc;AAI7B,SAAS,aAAa,IAAA,EAAe;IACnC,OAAO,OAAO,SAAS;AACzB;AAEA,SAAS,gBAAgB,OAAA,EAAoD;IAC3E,OAAO,YAAY;AACrB;AAEA,SAAS,gBAAgB,OAAA,EAAuB;IAC9C,OAAO,gBAAgB,OAAO,IAAI,kBAAkB,UAAU,YAAY;AAC5E;AAEA,SAAS,WAAW,UAAA,EAA2B;IAC7C,MAAM,6BAA6B,SAAS,aAAA;IAC5C,KAAA,MAAW,aAAa,WAAY;QAElC,IAAI,cAAc,2BAA4B,CAAA;QAC9C,UAAU,KAAA,CAAM;QAChB,IAAI,SAAS,aAAA,KAAkB,2BAA4B,CAAA;IAC7D;AACF;AAMA,SAAS,UAAa,KAAA,EAAY,UAAA,EAAoB;IACpD,OAAO,MAAM,GAAA,CAAO,CAAC,GAAG,QAAU,KAAA,CAAA,CAAO,aAAa,KAAA,IAAS,MAAM,MAAM,CAAE;AAC/E;AAmBA,SAAS,aAAa,MAAA,EAAkB,MAAA,EAAgB,YAAA,EAAuB;IAC7E,MAAM,aAAa,OAAO,MAAA,GAAS,KAAK,MAAM,IAAA,CAAK,MAAM,EAAE,KAAA,CAAM,CAAC,OAAS,SAAS,MAAA,CAAO,CAAC,CAAC;IAC7F,MAAM,mBAAmB,aAAa,MAAA,CAAO,CAAC,CAAA,GAAK;IACnD,MAAM,oBAAoB,eAAe,OAAO,OAAA,CAAQ,YAAY,IAAI,CAAA;IACxE,IAAI,gBAAgB,UAAU,QAAQ,KAAK,GAAA,CAAI,mBAAmB,CAAC,CAAC;IACpE,MAAM,sBAAsB,iBAAiB,MAAA,KAAW;IACxD,IAAI,oBAAqB,CAAA,gBAAgB,cAAc,MAAA,CAAO,CAAC,IAAM,MAAM,YAAY;IACvF,MAAM,YAAY,cAAc,IAAA,CAAK,CAAC,QACpC,MAAM,WAAA,CAAY,EAAE,UAAA,CAAW,iBAAiB,WAAA,CAAY,CAAC;IAE/D,OAAO,cAAc,eAAe,YAAY,KAAA;AAClD;AASA,SAAS,iBAAiB,KAAA,EAAc,OAAA,EAAkB;IACxD,MAAM,EAAE,CAAA,EAAG,CAAA,CAAE,CAAA,GAAI;IACjB,IAAI,SAAS;IACb,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,MAAA,GAAS,GAAG,IAAI,QAAQ,MAAA,EAAQ,IAAI,IAAK;QACnE,MAAM,KAAK,OAAA,CAAQ,CAAC,CAAA;QACpB,MAAM,KAAK,OAAA,CAAQ,CAAC,CAAA;QACpB,MAAM,KAAK,GAAG,CAAA;QACd,MAAM,KAAK,GAAG,CAAA;QACd,MAAM,KAAK,GAAG,CAAA;QACd,MAAM,KAAK,GAAG,CAAA;QAGd,MAAM,YAAc,KAAK,MAAQ,KAAK,KAAQ,IAAA,CAAK,KAAK,EAAA,IAAA,CAAO,IAAI,EAAA,IAAA,CAAO,KAAK,EAAA,IAAM;QACrF,IAAI,UAAW,CAAA,SAAS,CAAC;IAC3B;IAEA,OAAO;AACT;AAEA,SAAS,qBAAqB,KAAA,EAA2B,IAAA,EAAgB;IACvE,IAAI,CAAC,KAAM,CAAA,OAAO;IAClB,MAAM,YAAY;QAAE,GAAG,MAAM,OAAA;QAAS,GAAG,MAAM,OAAA;IAAQ;IACvD,OAAO,iBAAiB,WAAW,IAAI;AACzC;AAEA,SAAS,UAAa,OAAA,EAAqE;IACzF,OAAO,CAAC,QAAW,MAAM,WAAA,KAAgB,UAAU,QAAQ,KAAK,IAAI,KAAA;AACtE;AAEA,IAAMA,QAAO;AACb,IAAMC,UAAS;AACf,IAAM,SAAS;AACf,IAAMC,WAAU;AAChB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAMC,QAAO;AACb,IAAM,eAAe;AACrB,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,YAAY;AAClB,IAAMC,SAAQ;AACd,IAAM,MAAM;AACZ,IAAM,aAAa;AACnB,IAAM,aAAa", "debugId": null}}, {"offset": {"line": 2608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40radix-ui%2Breact-dropdown-me_0d24bc2194d27e4ed225fdbe9ca396a8/node_modules/%40radix-ui/react-dropdown-menu/src/dropdown-menu.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as MenuPrimitive from '@radix-ui/react-menu';\nimport { createMenuScope } from '@radix-ui/react-menu';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst DROPDOWN_MENU_NAME = 'DropdownMenu';\n\ntype ScopedProps<P> = P & { __scopeDropdownMenu?: Scope };\nconst [createDropdownMenuContext, createDropdownMenuScope] = createContextScope(\n  DROPDOWN_MENU_NAME,\n  [createMenuScope]\n);\nconst useMenuScope = createMenuScope();\n\ntype DropdownMenuContextValue = {\n  triggerId: string;\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DropdownMenuProvider, useDropdownMenuContext] =\n  createDropdownMenuContext<DropdownMenuContextValue>(DROPDOWN_MENU_NAME);\n\ninterface DropdownMenuProps {\n  children?: React.ReactNode;\n  dir?: Direction;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst DropdownMenu: React.FC<DropdownMenuProps> = (props: ScopedProps<DropdownMenuProps>) => {\n  const {\n    __scopeDropdownMenu,\n    children,\n    dir,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DROPDOWN_MENU_NAME,\n  });\n\n  return (\n    <DropdownMenuProvider\n      scope={__scopeDropdownMenu}\n      triggerId={useId()}\n      triggerRef={triggerRef}\n      contentId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      <MenuPrimitive.Root {...menuScope} open={open} onOpenChange={setOpen} dir={dir} modal={modal}>\n        {children}\n      </MenuPrimitive.Root>\n    </DropdownMenuProvider>\n  );\n};\n\nDropdownMenu.displayName = DROPDOWN_MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DropdownMenuTrigger';\n\ntype DropdownMenuTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DropdownMenuTriggerProps extends PrimitiveButtonProps {}\n\nconst DropdownMenuTrigger = React.forwardRef<DropdownMenuTriggerElement, DropdownMenuTriggerProps>(\n  (props: ScopedProps<DropdownMenuTriggerProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, disabled = false, ...triggerProps } = props;\n    const context = useDropdownMenuContext(TRIGGER_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return (\n      <MenuPrimitive.Anchor asChild {...menuScope}>\n        <Primitive.button\n          type=\"button\"\n          id={context.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={context.open ? context.contentId : undefined}\n          data-state={context.open ? 'open' : 'closed'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          {...triggerProps}\n          ref={composeRefs(forwardedRef, context.triggerRef)}\n          onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onOpenToggle();\n              // prevent trigger focusing when opening\n              // this allows the content to be given focus without competition\n              if (!context.open) event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (disabled) return;\n            if (['Enter', ' '].includes(event.key)) context.onOpenToggle();\n            if (event.key === 'ArrowDown') context.onOpenChange(true);\n            // prevent keydown from scrolling window / first focused item to execute\n            // that keydown (inadvertently closing the menu)\n            if (['Enter', ' ', 'ArrowDown'].includes(event.key)) event.preventDefault();\n          })}\n        />\n      </MenuPrimitive.Anchor>\n    );\n  }\n);\n\nDropdownMenuTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DropdownMenuPortal';\n\ntype MenuPortalProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Portal>;\ninterface DropdownMenuPortalProps extends MenuPortalProps {}\n\nconst DropdownMenuPortal: React.FC<DropdownMenuPortalProps> = (\n  props: ScopedProps<DropdownMenuPortalProps>\n) => {\n  const { __scopeDropdownMenu, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Portal {...menuScope} {...portalProps} />;\n};\n\nDropdownMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DropdownMenuContent';\n\ntype DropdownMenuContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Content>;\ninterface DropdownMenuContentProps extends Omit<MenuContentProps, 'onEntryFocus'> {}\n\nconst DropdownMenuContent = React.forwardRef<DropdownMenuContentElement, DropdownMenuContentProps>(\n  (props: ScopedProps<DropdownMenuContentProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...contentProps } = props;\n    const context = useDropdownMenuContext(CONTENT_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const hasInteractedOutsideRef = React.useRef(false);\n\n    return (\n      <MenuPrimitive.Content\n        id={context.contentId}\n        aria-labelledby={context.triggerId}\n        {...menuScope}\n        {...contentProps}\n        ref={forwardedRef}\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n          hasInteractedOutsideRef.current = false;\n          // Always prevent auto focus because we either focus manually or want user agent focus\n          event.preventDefault();\n        })}\n        onInteractOutside={composeEventHandlers(props.onInteractOutside, (event) => {\n          const originalEvent = event.detail.originalEvent as PointerEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n          if (!context.modal || isRightClick) hasInteractedOutsideRef.current = true;\n        })}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-dropdown-menu-content-transform-origin':\n              'var(--radix-popper-transform-origin)',\n            '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-dropdown-menu-content-available-height':\n              'var(--radix-popper-available-height)',\n            '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nDropdownMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'DropdownMenuGroup';\n\ntype DropdownMenuGroupElement = React.ComponentRef<typeof MenuPrimitive.Group>;\ntype MenuGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Group>;\ninterface DropdownMenuGroupProps extends MenuGroupProps {}\n\nconst DropdownMenuGroup = React.forwardRef<DropdownMenuGroupElement, DropdownMenuGroupProps>(\n  (props: ScopedProps<DropdownMenuGroupProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Group {...menuScope} {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'DropdownMenuLabel';\n\ntype DropdownMenuLabelElement = React.ComponentRef<typeof MenuPrimitive.Label>;\ntype MenuLabelProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Label>;\ninterface DropdownMenuLabelProps extends MenuLabelProps {}\n\nconst DropdownMenuLabel = React.forwardRef<DropdownMenuLabelElement, DropdownMenuLabelProps>(\n  (props: ScopedProps<DropdownMenuLabelProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Label {...menuScope} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'DropdownMenuItem';\n\ntype DropdownMenuItemElement = React.ComponentRef<typeof MenuPrimitive.Item>;\ntype MenuItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Item>;\ninterface DropdownMenuItemProps extends MenuItemProps {}\n\nconst DropdownMenuItem = React.forwardRef<DropdownMenuItemElement, DropdownMenuItemProps>(\n  (props: ScopedProps<DropdownMenuItemProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Item {...menuScope} {...itemProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'DropdownMenuCheckboxItem';\n\ntype DropdownMenuCheckboxItemElement = React.ComponentRef<typeof MenuPrimitive.CheckboxItem>;\ntype MenuCheckboxItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.CheckboxItem>;\ninterface DropdownMenuCheckboxItemProps extends MenuCheckboxItemProps {}\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  DropdownMenuCheckboxItemElement,\n  DropdownMenuCheckboxItemProps\n>((props: ScopedProps<DropdownMenuCheckboxItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...checkboxItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.CheckboxItem {...menuScope} {...checkboxItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'DropdownMenuRadioGroup';\n\ntype DropdownMenuRadioGroupElement = React.ComponentRef<typeof MenuPrimitive.RadioGroup>;\ntype MenuRadioGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioGroup>;\ninterface DropdownMenuRadioGroupProps extends MenuRadioGroupProps {}\n\nconst DropdownMenuRadioGroup = React.forwardRef<\n  DropdownMenuRadioGroupElement,\n  DropdownMenuRadioGroupProps\n>((props: ScopedProps<DropdownMenuRadioGroupProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioGroupProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioGroup {...menuScope} {...radioGroupProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'DropdownMenuRadioItem';\n\ntype DropdownMenuRadioItemElement = React.ComponentRef<typeof MenuPrimitive.RadioItem>;\ntype MenuRadioItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioItem>;\ninterface DropdownMenuRadioItemProps extends MenuRadioItemProps {}\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  DropdownMenuRadioItemElement,\n  DropdownMenuRadioItemProps\n>((props: ScopedProps<DropdownMenuRadioItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioItem {...menuScope} {...radioItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'DropdownMenuItemIndicator';\n\ntype DropdownMenuItemIndicatorElement = React.ComponentRef<typeof MenuPrimitive.ItemIndicator>;\ntype MenuItemIndicatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.ItemIndicator>;\ninterface DropdownMenuItemIndicatorProps extends MenuItemIndicatorProps {}\n\nconst DropdownMenuItemIndicator = React.forwardRef<\n  DropdownMenuItemIndicatorElement,\n  DropdownMenuItemIndicatorProps\n>((props: ScopedProps<DropdownMenuItemIndicatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.ItemIndicator {...menuScope} {...itemIndicatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuItemIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'DropdownMenuSeparator';\n\ntype DropdownMenuSeparatorElement = React.ComponentRef<typeof MenuPrimitive.Separator>;\ntype MenuSeparatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Separator>;\ninterface DropdownMenuSeparatorProps extends MenuSeparatorProps {}\n\nconst DropdownMenuSeparator = React.forwardRef<\n  DropdownMenuSeparatorElement,\n  DropdownMenuSeparatorProps\n>((props: ScopedProps<DropdownMenuSeparatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...separatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Separator {...menuScope} {...separatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'DropdownMenuArrow';\n\ntype DropdownMenuArrowElement = React.ComponentRef<typeof MenuPrimitive.Arrow>;\ntype MenuArrowProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Arrow>;\ninterface DropdownMenuArrowProps extends MenuArrowProps {}\n\nconst DropdownMenuArrow = React.forwardRef<DropdownMenuArrowElement, DropdownMenuArrowProps>(\n  (props: ScopedProps<DropdownMenuArrowProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Arrow {...menuScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSub\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DropdownMenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst DropdownMenuSub: React.FC<DropdownMenuSubProps> = (\n  props: ScopedProps<DropdownMenuSubProps>\n) => {\n  const { __scopeDropdownMenu, children, open: openProp, onOpenChange, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: 'DropdownMenuSub',\n  });\n\n  return (\n    <MenuPrimitive.Sub {...menuScope} open={open} onOpenChange={setOpen}>\n      {children}\n    </MenuPrimitive.Sub>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'DropdownMenuSubTrigger';\n\ntype DropdownMenuSubTriggerElement = React.ComponentRef<typeof MenuPrimitive.SubTrigger>;\ntype MenuSubTriggerProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubTrigger>;\ninterface DropdownMenuSubTriggerProps extends MenuSubTriggerProps {}\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  DropdownMenuSubTriggerElement,\n  DropdownMenuSubTriggerProps\n>((props: ScopedProps<DropdownMenuSubTriggerProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subTriggerProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.SubTrigger {...menuScope} {...subTriggerProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'DropdownMenuSubContent';\n\ntype DropdownMenuSubContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuSubContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubContent>;\ninterface DropdownMenuSubContentProps extends MenuSubContentProps {}\n\nconst DropdownMenuSubContent = React.forwardRef<\n  DropdownMenuSubContentElement,\n  DropdownMenuSubContentProps\n>((props: ScopedProps<DropdownMenuSubContentProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subContentProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n\n  return (\n    <MenuPrimitive.SubContent\n      {...menuScope}\n      {...subContentProps}\n      ref={forwardedRef}\n      style={{\n        ...props.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-dropdown-menu-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-dropdown-menu-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nDropdownMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = DropdownMenu;\nconst Trigger = DropdownMenuTrigger;\nconst Portal = DropdownMenuPortal;\nconst Content = DropdownMenuContent;\nconst Group = DropdownMenuGroup;\nconst Label = DropdownMenuLabel;\nconst Item = DropdownMenuItem;\nconst CheckboxItem = DropdownMenuCheckboxItem;\nconst RadioGroup = DropdownMenuRadioGroup;\nconst RadioItem = DropdownMenuRadioItem;\nconst ItemIndicator = DropdownMenuItemIndicator;\nconst Separator = DropdownMenuSeparator;\nconst Arrow = DropdownMenuArrow;\nconst Sub = DropdownMenuSub;\nconst SubTrigger = DropdownMenuSubTrigger;\nconst SubContent = DropdownMenuSubContent;\n\nexport {\n  createDropdownMenuScope,\n  //\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuPortal,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuItemIndicator,\n  DropdownMenuSeparator,\n  DropdownMenuArrow,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  DropdownMenuProps,\n  DropdownMenuTriggerProps,\n  DropdownMenuPortalProps,\n  DropdownMenuContentProps,\n  DropdownMenuGroupProps,\n  DropdownMenuLabelProps,\n  DropdownMenuItemProps,\n  DropdownMenuCheckboxItemProps,\n  DropdownMenuRadioGroupProps,\n  DropdownMenuRadioItemProps,\n  DropdownMenuItemIndicatorProps,\n  DropdownMenuSeparatorProps,\n  DropdownMenuArrowProps,\n  DropdownMenuSubProps,\n  DropdownMenuSubTriggerProps,\n  DropdownMenuSubContentProps,\n};\n"], "names": ["Root", "Portal", "Content", "Group", "Label", "<PERSON><PERSON>", "CheckboxItem", "RadioGroup", "RadioItem", "ItemIndicator", "Separator", "Arrow", "Sub", "SubTrigger", "SubContent"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,mBAAmB;AAC5B,SAAS,0BAA0B;AACnC,SAAS,4BAA4B;AACrC,SAAS,iBAAiB;AAC1B,YAAY,mBAAmB;AAE/B,SAAS,aAAa;AAuEhB;;;;;;;;;;;;AA7DN,IAAM,qBAAqB;AAG3B,IAAM,CAAC,2BAA2B,uBAAuB,CAAA,OAAI,kTAAA,EAC3D,oBACA;IAAC,+SAAe;CAAA;AAElB,IAAM,mBAAe,+SAAA,CAAgB;AAYrC,IAAM,CAAC,sBAAsB,sBAAsB,CAAA,GACjD,0BAAoD,kBAAkB;AAWxE,IAAM,eAA4C,CAAC,UAA0C;IAC3F,MAAM,EACJ,mBAAA,EACA,QAAA,EACA,GAAA,EACA,MAAM,QAAA,EACN,WAAA,EACA,YAAA,EACA,QAAQ,IAAA,EACV,GAAI;IACJ,MAAM,YAAY,aAAa,mBAAmB;IAClD,MAAM,aAAmB,kTAAA,CAA0B,IAAI;IACvD,MAAM,CAAC,MAAM,OAAO,CAAA,OAAI,sUAAA,EAAqB;QAC3C,MAAM;QACN,8DAAa,cAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,IAAA,wTAAA,EAAC,sBAAA;QACC,OAAO;QACP,eAAW,yTAAA,CAAM;QACjB;QACA,eAAW,yTAAA,CAAM;QACjB;QACA,cAAc;QACd,cAAoB,uTAAA;wCAAY,IAAM;gDAAQ,CAAC,WAAa,CAAC,QAAQ;;uCAAG;YAAC,OAAO;SAAC;QACjF;QAEA,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAe,oSAAA,EAAd;YAAoB,GAAG,SAAA;YAAW;YAAY,cAAc;YAAS;YAAU;YAC7E;QAAA,CACH;IAAA;AAGN;AAEA,aAAa,WAAA,GAAc;AAM3B,IAAM,eAAe;AAMrB,IAAM,sBAA4B,sTAAA,CAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EAAE,mBAAA,EAAqB,WAAW,KAAA,EAAO,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,uBAAuB,cAAc,mBAAmB;IACxE,MAAM,YAAY,aAAa,mBAAmB;IAClD,OACE,aAAA,GAAA,IAAA,wTAAA,EAAe,sSAAA,EAAd;QAAqB,SAAO;QAAE,GAAG,SAAA;QAChC,UAAA,aAAA,GAAA,IAAA,wTAAA,EAAC,wSAAA,CAAU,MAAA,EAAV;YACC,MAAK;YACL,IAAI,QAAQ,SAAA;YACZ,iBAAc;YACd,iBAAe,QAAQ,IAAA;YACvB,iBAAe,QAAQ,IAAA,GAAO,QAAQ,SAAA,GAAY,KAAA;YAClD,cAAY,QAAQ,IAAA,GAAO,SAAS;YACpC,iBAAe,WAAW,KAAK,KAAA;YAC/B;YACC,GAAG,YAAA;YACJ,SAAK,gTAAA,EAAY,cAAc,QAAQ,UAAU;YACjD,mBAAe,0QAAA,EAAqB,MAAM,aAAA,EAAe,CAAC,UAAU;gBAGlE,IAAI,CAAC,YAAY,MAAM,MAAA,KAAW,KAAK,MAAM,OAAA,KAAY,OAAO;oBAC9D,QAAQ,YAAA,CAAa;oBAGrB,IAAI,CAAC,QAAQ,IAAA,CAAM,CAAA,MAAM,cAAA,CAAe;gBAC1C;YACF,CAAC;YACD,eAAW,0QAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,IAAI,SAAU,CAAA;gBACd,IAAI;oBAAC;oBAAS,GAAG;iBAAA,CAAE,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,QAAQ,YAAA,CAAa;gBAC7D,IAAI,MAAM,GAAA,KAAQ,YAAa,CAAA,QAAQ,YAAA,CAAa,IAAI;gBAGxD,IAAI;oBAAC;oBAAS;oBAAK,WAAW;iBAAA,CAAE,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,MAAM,cAAA,CAAe;YAC5E,CAAC;QAAA;IACH,CACF;AAEJ;AAGF,oBAAoB,WAAA,GAAc;AAMlC,IAAM,cAAc;AAKpB,IAAM,qBAAwD,CAC5D,UACG;IACH,MAAM,EAAE,mBAAA,EAAqB,GAAG,YAAY,CAAA,GAAI;IAChD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAe,sSAAA,EAAd;QAAsB,GAAG,SAAA;QAAY,GAAG,WAAA;IAAA,CAAa;AAC/D;AAEA,mBAAmB,WAAA,GAAc;AAMjC,IAAM,eAAe;AAMrB,IAAM,sBAA4B,sTAAA,CAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EAAE,mBAAA,EAAqB,GAAG,aAAa,CAAA,GAAI;IACjD,MAAM,UAAU,uBAAuB,cAAc,mBAAmB;IACxE,MAAM,YAAY,aAAa,mBAAmB;IAClD,MAAM,0BAAgC,kTAAA,CAAO,KAAK;IAElD,OACE,aAAA,GAAA,IAAA,wTAAA,EAAe,uSAAA,EAAd;QACC,IAAI,QAAQ,SAAA;QACZ,mBAAiB,QAAQ,SAAA;QACxB,GAAG,SAAA;QACH,GAAG,YAAA;QACJ,KAAK;QACL,sBAAkB,0QAAA,EAAqB,MAAM,gBAAA,EAAkB,CAAC,UAAU;gBAClC;YAAtC,IAAI,CAAC,wBAAwB,OAAA,CAAS,EAAA,8BAAA,QAAQ,UAAA,CAAW,OAAA,cAAnB,kDAAA,4BAA4B,KAAA,CAAM;YACxE,wBAAwB,OAAA,GAAU;YAElC,MAAM,cAAA,CAAe;QACvB,CAAC;QACD,uBAAmB,0QAAA,EAAqB,MAAM,iBAAA,EAAmB,CAAC,UAAU;YAC1E,MAAM,gBAAgB,MAAM,MAAA,CAAO,aAAA;YACnC,MAAM,gBAAgB,cAAc,MAAA,KAAW,KAAK,cAAc,OAAA,KAAY;YAC9E,MAAM,eAAe,cAAc,MAAA,KAAW,KAAK;YACnD,IAAI,CAAC,QAAQ,KAAA,IAAS,aAAc,CAAA,wBAAwB,OAAA,GAAU;QACxE,CAAC;QACD,OAAO;YACL,GAAG,MAAM,KAAA;YAAA,iDAAA;YAET,GAAG;gBACD,kDACE;gBACF,iDAAiD;gBACjD,kDACE;gBACF,uCAAuC;gBACvC,wCAAwC;YAC1C,CAAA;QACF;IAAA;AAGN;AAGF,oBAAoB,WAAA,GAAc;AAMlC,IAAM,aAAa;AAMnB,IAAM,oBAA0B,sTAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,mBAAA,EAAqB,GAAG,WAAW,CAAA,GAAI;IAC/C,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAe,qSAAA,EAAd;QAAqB,GAAG,SAAA;QAAY,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAChF;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,aAAa;AAMnB,IAAM,oBAA0B,sTAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,mBAAA,EAAqB,GAAG,WAAW,CAAA,GAAI;IAC/C,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAe,qSAAA,EAAd;QAAqB,GAAG,SAAA;QAAY,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAChF;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,YAAY;AAMlB,IAAM,mBAAyB,sTAAA,CAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EAAE,mBAAA,EAAqB,GAAG,UAAU,CAAA,GAAI;IAC9C,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAe,oSAAA,EAAd;QAAoB,GAAG,SAAA;QAAY,GAAG,SAAA;QAAW,KAAK;IAAA,CAAc;AAC9E;AAGF,iBAAiB,WAAA,GAAc;AAM/B,IAAM,qBAAqB;AAM3B,IAAM,2BAAiC,sTAAA,CAGrC,CAAC,OAAmD,iBAAiB;IACrE,MAAM,EAAE,mBAAA,EAAqB,GAAG,kBAAkB,CAAA,GAAI;IACtD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAe,4SAAA,EAAd;QAA4B,GAAG,SAAA;QAAY,GAAG,iBAAA;QAAmB,KAAK;IAAA,CAAc;AAC9F,CAAC;AAED,yBAAyB,WAAA,GAAc;AAMvC,IAAM,mBAAmB;AAMzB,IAAM,yBAA+B,sTAAA,CAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,mBAAA,EAAqB,GAAG,gBAAgB,CAAA,GAAI;IACpD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAe,0SAAA,EAAd;QAA0B,GAAG,SAAA;QAAY,GAAG,eAAA;QAAiB,KAAK;IAAA,CAAc;AAC1F,CAAC;AAED,uBAAuB,WAAA,GAAc;AAMrC,IAAM,kBAAkB;AAMxB,IAAM,wBAA8B,sTAAA,CAGlC,CAAC,OAAgD,iBAAiB;IAClE,MAAM,EAAE,mBAAA,EAAqB,GAAG,eAAe,CAAA,GAAI;IACnD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAe,ySAAA,EAAd;QAAyB,GAAG,SAAA;QAAY,GAAG,cAAA;QAAgB,KAAK;IAAA,CAAc;AACxF,CAAC;AAED,sBAAsB,WAAA,GAAc;AAMpC,IAAM,iBAAiB;AAMvB,IAAM,4BAAkC,sTAAA,CAGtC,CAAC,OAAoD,iBAAiB;IACtE,MAAM,EAAE,mBAAA,EAAqB,GAAG,mBAAmB,CAAA,GAAI;IACvD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAe,6SAAA,EAAd;QAA6B,GAAG,SAAA;QAAY,GAAG,kBAAA;QAAoB,KAAK;IAAA,CAAc;AAChG,CAAC;AAED,0BAA0B,WAAA,GAAc;AAMxC,IAAM,iBAAiB;AAMvB,IAAM,wBAA8B,sTAAA,CAGlC,CAAC,OAAgD,iBAAiB;IAClE,MAAM,EAAE,mBAAA,EAAqB,GAAG,eAAe,CAAA,GAAI;IACnD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAe,ySAAA,EAAd;QAAyB,GAAG,SAAA;QAAY,GAAG,cAAA;QAAgB,KAAK;IAAA,CAAc;AACxF,CAAC;AAED,sBAAsB,WAAA,GAAc;AAMpC,IAAM,aAAa;AAMnB,IAAM,oBAA0B,sTAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,mBAAA,EAAqB,GAAG,WAAW,CAAA,GAAI;IAC/C,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAe,qSAAA,EAAd;QAAqB,GAAG,SAAA;QAAY,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAChF;AAGF,kBAAkB,WAAA,GAAc;AAahC,IAAM,kBAAkD,CACtD,UACG;IACH,MAAM,EAAE,mBAAA,EAAqB,QAAA,EAAU,MAAM,QAAA,EAAU,YAAA,EAAc,WAAA,CAAY,CAAA,GAAI;IACrF,MAAM,YAAY,aAAa,mBAAmB;IAClD,MAAM,CAAC,MAAM,OAAO,CAAA,OAAI,sUAAA,EAAqB;QAC3C,MAAM;QACN,aAAa,+DAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,IAAA,wTAAA,EAAe,mSAAA,EAAd;QAAmB,GAAG,SAAA;QAAW;QAAY,cAAc;QACzD;IAAA,CACH;AAEJ;AAMA,IAAM,mBAAmB;AAMzB,IAAM,yBAA+B,sTAAA,CAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,mBAAA,EAAqB,GAAG,gBAAgB,CAAA,GAAI;IACpD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,IAAA,wTAAA,EAAe,0SAAA,EAAd;QAA0B,GAAG,SAAA;QAAY,GAAG,eAAA;QAAiB,KAAK;IAAA,CAAc;AAC1F,CAAC;AAED,uBAAuB,WAAA,GAAc;AAMrC,IAAM,mBAAmB;AAMzB,IAAM,yBAA+B,sTAAA,CAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,mBAAA,EAAqB,GAAG,gBAAgB,CAAA,GAAI;IACpD,MAAM,YAAY,aAAa,mBAAmB;IAElD,OACE,aAAA,GAAA,IAAA,wTAAA,EAAe,0SAAA,EAAd;QACE,GAAG,SAAA;QACH,GAAG,eAAA;QACJ,KAAK;QACL,OAAO;YACL,GAAG,MAAM,KAAA;YAAA,iDAAA;YAET,GAAG;gBACD,kDAAkD;gBAClD,iDAAiD;gBACjD,kDAAkD;gBAClD,uCAAuC;gBACvC,wCAAwC;YAC1C,CAAA;QACF;IAAA;AAGN,CAAC;AAED,uBAAuB,WAAA,GAAc;AAIrC,IAAMA,QAAO;AACb,IAAM,UAAU;AAChB,IAAMC,UAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,SAAQ;AACd,IAAMC,SAAQ;AACd,IAAMC,QAAO;AACb,IAAMC,gBAAe;AACrB,IAAMC,cAAa;AACnB,IAAMC,aAAY;AAClB,IAAMC,iBAAgB;AACtB,IAAMC,aAAY;AAClB,IAAMC,SAAQ;AACd,IAAMC,OAAM;AACZ,IAAMC,cAAa;AACnB,IAAMC,cAAa", "debugId": null}}, {"offset": {"line": 2999, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40radix-ui%2Bnumber%401.1.1/node_modules/%40radix-ui/number/src/number.ts"], "sourcesContent": ["function clamp(value: number, [min, max]: [number, number]): number {\n  return Math.min(max, Math.max(min, value));\n}\n\nexport { clamp };\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,MAAM,KAAA,OAAwB;SAAR,KAAK,GAAG,CAAA,EAA6B,CAAtC;IAC5B,OAAO,KAAK,GAAA,CAAI,KAAK,KAAK,GAAA,CAAI,KAAK,KAAK,CAAC;AAC3C", "debugId": null}}, {"offset": {"line": 3014, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40radix-ui%2Breact-use-previou_913befb2c5ba91b732d33e96587c3761/node_modules/%40radix-ui/react-use-previous/src/use-previous.tsx"], "sourcesContent": ["import * as React from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = React.useRef({ value, previous: value });\n\n  // We compare values before making an update to ensure that\n  // a change has been made. This ensures the previous value is\n  // persisted correctly between renders.\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\nexport { usePrevious };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;;AAEvB,SAAS,YAAe,KAAA,EAAU;IAChC,MAAM,MAAY,kTAAA,CAAO;QAAE;QAAO,UAAU;IAAM,CAAC;IAKnD,OAAa,mTAAA;+BAAQ,MAAM;YACzB,IAAI,IAAI,OAAA,CAAQ,KAAA,KAAU,OAAO;gBAC/B,IAAI,OAAA,CAAQ,QAAA,GAAW,IAAI,OAAA,CAAQ,KAAA;gBACnC,IAAI,OAAA,CAAQ,KAAA,GAAQ;YACtB;YACA,OAAO,IAAI,OAAA,CAAQ,QAAA;QACrB;8BAAG;QAAC,KAAK;KAAC;AACZ", "debugId": null}}, {"offset": {"line": 3044, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40panva%2Bhkdf%401.2.1/node_modules/%40panva/hkdf/dist/web/runtime/hkdf.js"], "sourcesContent": ["const getGlobal = () => {\n    if (typeof globalThis !== 'undefined')\n        return globalThis;\n    if (typeof self !== 'undefined')\n        return self;\n    if (typeof window !== 'undefined')\n        return window;\n    throw new Error('unable to locate global object');\n};\nexport default async (digest, ikm, salt, info, keylen) => {\n    const { crypto: { subtle }, } = getGlobal();\n    return new Uint8Array(await subtle.deriveBits({\n        name: 'HKDF',\n        hash: `SHA-${digest.substr(3)}`,\n        salt,\n        info,\n    }, await subtle.importKey('raw', ikm, 'HKDF', false, ['deriveBits']), keylen << 3));\n};\n"], "names": [], "mappings": ";;;;AAAA,MAAM,YAAY;IACd,IAAI,OAAO,eAAe,aACtB,OAAO;IACX,IAAI,OAAO,SAAS,aAChB,OAAO;IACX,IAAI,OAAO,WAAW,aAClB,OAAO;IACX,MAAM,IAAI,MAAM;AACpB;uCACe,OAAO,QAAQ,KAAK,MAAM,MAAM;IAC3C,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAG,GAAG;IAChC,OAAO,IAAI,WAAW,MAAM,OAAO,UAAU,CAAC;QAC1C,MAAM;QACN,MAAM,AAAC,OAAuB,OAAjB,OAAO,MAAM,CAAC;QAC3B;QACA;IACJ,GAAG,MAAM,OAAO,SAAS,CAAC,OAAO,KAAK,QAAQ,OAAO;QAAC;KAAa,GAAG,UAAU;AACpF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3069, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40panva%2Bhkdf%401.2.1/node_modules/%40panva/hkdf/dist/web/index.js"], "sourcesContent": ["import derive from './runtime/hkdf.js';\nfunction normalizeDigest(digest) {\n    switch (digest) {\n        case 'sha256':\n        case 'sha384':\n        case 'sha512':\n        case 'sha1':\n            return digest;\n        default:\n            throw new TypeError('unsupported \"digest\" value');\n    }\n}\nfunction normalizeUint8Array(input, label) {\n    if (typeof input === 'string')\n        return new TextEncoder().encode(input);\n    if (!(input instanceof Uint8Array))\n        throw new TypeError(`\"${label}\"\" must be an instance of Uint8Array or a string`);\n    return input;\n}\nfunction normalizeIkm(input) {\n    const ikm = normalizeUint8Array(input, 'ikm');\n    if (!ikm.byteLength)\n        throw new TypeError(`\"ikm\" must be at least one byte in length`);\n    return ikm;\n}\nfunction normalizeInfo(input) {\n    const info = normalizeUint8Array(input, 'info');\n    if (info.byteLength > 1024) {\n        throw TypeError('\"info\" must not contain more than 1024 bytes');\n    }\n    return info;\n}\nfunction normalizeKeylen(input, digest) {\n    if (typeof input !== 'number' || !Number.isInteger(input) || input < 1) {\n        throw new TypeError('\"keylen\" must be a positive integer');\n    }\n    const hashlen = parseInt(digest.substr(3), 10) >> 3 || 20;\n    if (input > 255 * hashlen) {\n        throw new TypeError('\"keylen\" too large');\n    }\n    return input;\n}\nasync function hkdf(digest, ikm, salt, info, keylen) {\n    return derive(normalizeDigest(digest), normalizeIkm(ikm), normalizeUint8Array(salt, 'salt'), normalizeInfo(info), normalizeKeylen(keylen, digest));\n}\nexport { hkdf, hkdf as default };\n"], "names": [], "mappings": ";;;;;;AAAA;;AACA,SAAS,gBAAgB,MAAM;IAC3B,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,UAAU;IAC5B;AACJ;AACA,SAAS,oBAAoB,KAAK,EAAE,KAAK;IACrC,IAAI,OAAO,UAAU,UACjB,OAAO,IAAI,cAAc,MAAM,CAAC;IACpC,IAAI,CAAC,CAAC,iBAAiB,UAAU,GAC7B,MAAM,IAAI,UAAU,AAAC,IAAS,OAAN,OAAM;IAClC,OAAO;AACX;AACA,SAAS,aAAa,KAAK;IACvB,MAAM,MAAM,oBAAoB,OAAO;IACvC,IAAI,CAAC,IAAI,UAAU,EACf,MAAM,IAAI,UAAW;IACzB,OAAO;AACX;AACA,SAAS,cAAc,KAAK;IACxB,MAAM,OAAO,oBAAoB,OAAO;IACxC,IAAI,KAAK,UAAU,GAAG,MAAM;QACxB,MAAM,UAAU;IACpB;IACA,OAAO;AACX;AACA,SAAS,gBAAgB,KAAK,EAAE,MAAM;IAClC,IAAI,OAAO,UAAU,YAAY,CAAC,OAAO,SAAS,CAAC,UAAU,QAAQ,GAAG;QACpE,MAAM,IAAI,UAAU;IACxB;IACA,MAAM,UAAU,SAAS,OAAO,MAAM,CAAC,IAAI,OAAO,KAAK;IACvD,IAAI,QAAQ,MAAM,SAAS;QACvB,MAAM,IAAI,UAAU;IACxB;IACA,OAAO;AACX;AACA,eAAe,KAAK,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM;IAC/C,OAAO,IAAA,uPAAM,EAAC,gBAAgB,SAAS,aAAa,MAAM,oBAAoB,MAAM,SAAS,cAAc,OAAO,gBAAgB,QAAQ;AAC9I", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3123, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/preact@10.24.3/node_modules/preact/dist/preact.module.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/src/constants.js", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/src/util.js", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/src/options.js", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/src/create-element.js", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/src/component.js", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/src/diff/props.js", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/src/create-context.js", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/src/diff/children.js", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/src/diff/index.js", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/src/render.js", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/src/clone-element.js", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/src/diff/catch-error.js"], "sourcesContent": ["/** Normal hydration that attaches to a DOM tree but does not diff it. */\nexport const MODE_HYDRATE = 1 << 5;\n/** Signifies this VNode suspended on the previous render */\nexport const MODE_SUSPENDED = 1 << 7;\n/** Indicates that this node needs to be inserted while patching children */\nexport const INSERT_VNODE = 1 << 16;\n/** Indicates a VNode has been matched with another VNode in the diff */\nexport const MATCHED = 1 << 17;\n\n/** Reset all mode flags */\nexport const RESET_MODE = ~(MODE_HYDRATE | MODE_SUSPENDED);\n\nexport const EMPTY_OBJ = /** @type {any} */ ({});\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL =\n\t/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { EMPTY_ARR } from './constants';\n\nexport const isArray = Array.isArray;\n\n/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\t// @ts-expect-error We change the type of `obj` to be `O & P`\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Remove a child node from its parent if attached. This is a workaround for\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\n * is smaller than including a dedicated polyfill.\n * @param {preact.ContainerNode} node The node to remove\n */\nexport function removeNode(node) {\n\tif (node && node.parentNode) node.parentNode.removeChild(node);\n}\n\nexport const slice = EMPTY_ARR.slice;\n", "import { _catchError } from './diff/catch-error';\n\n/**\n * The `option` object can potentially contain callback functions\n * that are called during various stages of our renderer. This is the\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\n * for a full list of available option hooks (most editors/IDEs allow you to\n * ctrl+click or cmd+click on mac the type definition below).\n * @type {Options}\n */\nconst options = {\n\t_catchError\n};\n\nexport default options;\n", "import { slice } from './util';\nimport options from './options';\n\nlet vnodeId = 0;\n\n/**\n * Create an virtual node (used for JSX)\n * @param {VNode[\"type\"]} type The node name or Component constructor for this\n * virtual node\n * @param {object | null | undefined} [props] The properties of the virtual node\n * @param {Array<import('.').ComponentChildren>} [children] The children of the\n * virtual node\n * @returns {VNode}\n */\nexport function createElement(type, props, children) {\n\tlet normalizedProps = {},\n\t\tkey,\n\t\tref,\n\t\ti;\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse normalizedProps[i] = props[i];\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\t// If a Component VNode, check for and apply defaultProps\n\t// Note: type may be undefined in development, must never error here.\n\tif (typeof type == 'function' && type.defaultProps != null) {\n\t\tfor (i in type.defaultProps) {\n\t\t\tif (normalizedProps[i] === undefined) {\n\t\t\t\tnormalizedProps[i] = type.defaultProps[i];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn createVNode(type, normalizedProps, key, ref, null);\n}\n\n/**\n * Create a VNode (used internally by Preact)\n * @param {VNode[\"type\"]} type The node name or Component\n * Constructor for this virtual node\n * @param {object | string | number | null} props The properties of this virtual node.\n * If this virtual node represents a text node, this is the text of the node (string or number).\n * @param {string | number | null} key The key for this virtual node, used when\n * diffing it against its children\n * @param {VNode[\"ref\"]} ref The ref property that will\n * receive a reference to its created child\n * @returns {VNode}\n */\nexport function createVNode(type, props, key, ref, original) {\n\t// V8 seems to be better at detecting type shapes if the object is allocated from the same call site\n\t// Do not inline into createElement and coerceToVNode!\n\t/** @type {VNode} */\n\tconst vnode = {\n\t\ttype,\n\t\tprops,\n\t\tkey,\n\t\tref,\n\t\t_children: null,\n\t\t_parent: null,\n\t\t_depth: 0,\n\t\t_dom: null,\n\t\t// _nextDom must be initialized to undefined b/c it will eventually\n\t\t// be set to dom.nextSibling which can return `null` and it is important\n\t\t// to be able to distinguish between an uninitialized _nextDom and\n\t\t// a _nextDom that has been set to `null`\n\t\t_nextDom: undefined,\n\t\t_component: null,\n\t\tconstructor: undefined,\n\t\t_original: original == null ? ++vnodeId : original,\n\t\t_index: -1,\n\t\t_flags: 0\n\t};\n\n\t// Only invoke the vnode hook if this was *not* a direct copy:\n\tif (original == null && options.vnode != null) options.vnode(vnode);\n\n\treturn vnode;\n}\n\nexport function createRef() {\n\treturn { current: null };\n}\n\nexport function Fragment(props) {\n\treturn props.children;\n}\n\n/**\n * Check if a the argument is a valid Preact VNode.\n * @param {*} vnode\n * @returns {vnode is VNode}\n */\nexport const isValidElement = vnode =>\n\tvnode != null && vnode.constructor == undefined;\n", "import { assign } from './util';\nimport { diff, commitRoot } from './diff/index';\nimport options from './options';\nimport { Fragment } from './create-element';\nimport { MODE_HYDRATE } from './constants';\n\n/**\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\n * trigger rendering\n * @param {object} props The initial component props\n * @param {object} context The initial context from parent components'\n * getChildContext\n */\nexport function BaseComponent(props, context) {\n\tthis.props = props;\n\tthis.context = context;\n}\n\n/**\n * Update component state and schedule a re-render.\n * @this {Component}\n * @param {object | ((s: object, p: object) => object)} update A hash of state\n * properties to update with new values or a function that given the current\n * state and props returns a new partial state\n * @param {() => void} [callback] A function to be called once component state is\n * updated\n */\nBaseComponent.prototype.setState = function (update, callback) {\n\t// only clone state when copying to nextState the first time.\n\tlet s;\n\tif (this._nextState != null && this._nextState !== this.state) {\n\t\ts = this._nextState;\n\t} else {\n\t\ts = this._nextState = assign({}, this.state);\n\t}\n\n\tif (typeof update == 'function') {\n\t\t// Some libraries like `immer` mark the current state as readonly,\n\t\t// preventing us from mutating it, so we need to clone it. See #2716\n\t\tupdate = update(assign({}, s), this.props);\n\t}\n\n\tif (update) {\n\t\tassign(s, update);\n\t}\n\n\t// Skip update if updater function returned null\n\tif (update == null) return;\n\n\tif (this._vnode) {\n\t\tif (callback) {\n\t\t\tthis._stateCallbacks.push(callback);\n\t\t}\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Immediately perform a synchronous re-render of the component\n * @this {Component}\n * @param {() => void} [callback] A function to be called after component is\n * re-rendered\n */\nBaseComponent.prototype.forceUpdate = function (callback) {\n\tif (this._vnode) {\n\t\t// Set render mode so that we can differentiate where the render request\n\t\t// is coming from. We need this because forceUpdate should never call\n\t\t// shouldComponentUpdate\n\t\tthis._force = true;\n\t\tif (callback) this._renderCallbacks.push(callback);\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\n * Virtual DOM is generally constructed via [JSX](http://jasonformat.com/wtf-is-jsx).\n * @param {object} props Props (eg: JSX attributes) received from parent\n * element/component\n * @param {object} state The component's current state\n * @param {object} context Context object, as returned by the nearest\n * ancestor's `getChildContext()`\n * @returns {ComponentChildren | void}\n */\nBaseComponent.prototype.render = Fragment;\n\n/**\n * @param {VNode} vnode\n * @param {number | null} [childIndex]\n */\nexport function getDomSibling(vnode, childIndex) {\n\tif (childIndex == null) {\n\t\t// Use childIndex==null as a signal to resume the search from the vnode's sibling\n\t\treturn vnode._parent\n\t\t\t? getDomSibling(vnode._parent, vnode._index + 1)\n\t\t\t: null;\n\t}\n\n\tlet sibling;\n\tfor (; childIndex < vnode._children.length; childIndex++) {\n\t\tsibling = vnode._children[childIndex];\n\n\t\tif (sibling != null && sibling._dom != null) {\n\t\t\t// Since updateParentDomPointers keeps _dom pointer correct,\n\t\t\t// we can rely on _dom to tell us if this subtree contains a\n\t\t\t// rendered DOM node, and what the first rendered DOM node is\n\t\t\treturn sibling._dom;\n\t\t}\n\t}\n\n\t// If we get here, we have not found a DOM node in this vnode's children.\n\t// We must resume from this vnode's sibling (in it's parent _children array)\n\t// Only climb up and search the parent if we aren't searching through a DOM\n\t// VNode (meaning we reached the DOM parent of the original vnode that began\n\t// the search)\n\treturn typeof vnode.type == 'function' ? getDomSibling(vnode) : null;\n}\n\n/**\n * Trigger in-place re-rendering of a component.\n * @param {Component} component The component to rerender\n */\nfunction renderComponent(component) {\n\tlet oldVNode = component._vnode,\n\t\toldDom = oldVNode._dom,\n\t\tcommitQueue = [],\n\t\trefQueue = [];\n\n\tif (component._parentDom) {\n\t\tconst newVNode = assign({}, oldVNode);\n\t\tnewVNode._original = oldVNode._original + 1;\n\t\tif (options.vnode) options.vnode(newVNode);\n\n\t\tdiff(\n\t\t\tcomponent._parentDom,\n\t\t\tnewVNode,\n\t\t\toldVNode,\n\t\t\tcomponent._globalContext,\n\t\t\tcomponent._parentDom.namespaceURI,\n\t\t\toldVNode._flags & MODE_HYDRATE ? [oldDom] : null,\n\t\t\tcommitQueue,\n\t\t\toldDom == null ? getDomSibling(oldVNode) : oldDom,\n\t\t\t!!(oldVNode._flags & MODE_HYDRATE),\n\t\t\trefQueue\n\t\t);\n\n\t\tnewVNode._original = oldVNode._original;\n\t\tnewVNode._parent._children[newVNode._index] = newVNode;\n\t\tcommitRoot(commitQueue, newVNode, refQueue);\n\n\t\tif (newVNode._dom != oldDom) {\n\t\t\tupdateParentDomPointers(newVNode);\n\t\t}\n\t}\n}\n\n/**\n * @param {VNode} vnode\n */\nfunction updateParentDomPointers(vnode) {\n\tif ((vnode = vnode._parent) != null && vnode._component != null) {\n\t\tvnode._dom = vnode._component.base = null;\n\t\tfor (let i = 0; i < vnode._children.length; i++) {\n\t\t\tlet child = vnode._children[i];\n\t\t\tif (child != null && child._dom != null) {\n\t\t\t\tvnode._dom = vnode._component.base = child._dom;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn updateParentDomPointers(vnode);\n\t}\n}\n\n/**\n * The render queue\n * @type {Array<Component>}\n */\nlet rerenderQueue = [];\n\n/*\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\n * do, and when their effects will be applied. See the links below for some further reading on designing\n * asynchronous APIs.\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\n */\n\nlet prevDebounce;\n\nconst defer =\n\ttypeof Promise == 'function'\n\t\t? Promise.prototype.then.bind(Promise.resolve())\n\t\t: setTimeout;\n\n/**\n * Enqueue a rerender of a component\n * @param {Component} c The component to rerender\n */\nexport function enqueueRender(c) {\n\tif (\n\t\t(!c._dirty &&\n\t\t\t(c._dirty = true) &&\n\t\t\trerenderQueue.push(c) &&\n\t\t\t!process._rerenderCount++) ||\n\t\tprevDebounce !== options.debounceRendering\n\t) {\n\t\tprevDebounce = options.debounceRendering;\n\t\t(prevDebounce || defer)(process);\n\t}\n}\n\n/**\n * @param {Component} a\n * @param {Component} b\n */\nconst depthSort = (a, b) => a._vnode._depth - b._vnode._depth;\n\n/** Flush the render queue by rerendering all queued components */\nfunction process() {\n\tlet c;\n\trerenderQueue.sort(depthSort);\n\t// Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\n\t// process() calls from getting scheduled while `queue` is still being consumed.\n\twhile ((c = rerenderQueue.shift())) {\n\t\tif (c._dirty) {\n\t\t\tlet renderQueueLength = rerenderQueue.length;\n\t\t\trenderComponent(c);\n\t\t\tif (rerenderQueue.length > renderQueueLength) {\n\t\t\t\t// When i.e. rerendering a provider additional new items can be injected, we want to\n\t\t\t\t// keep the order from top to bottom with those new items so we can handle them in a\n\t\t\t\t// single pass\n\t\t\t\trerenderQueue.sort(depthSort);\n\t\t\t}\n\t\t}\n\t}\n\tprocess._rerenderCount = 0;\n}\n\nprocess._rerenderCount = 0;\n", "import { IS_NON_DIMENSIONAL } from '../constants';\nimport options from '../options';\n\nfunction setStyle(style, key, value) {\n\tif (key[0] === '-') {\n\t\tstyle.setProperty(key, value == null ? '' : value);\n\t} else if (value == null) {\n\t\tstyle[key] = '';\n\t} else if (typeof value != 'number' || IS_NON_DIMENSIONAL.test(key)) {\n\t\tstyle[key] = value;\n\t} else {\n\t\tstyle[key] = value + 'px';\n\t}\n}\n\n// A logical clock to solve issues like https://github.com/preactjs/preact/issues/3927.\n// When the DOM performs an event it leaves micro-ticks in between bubbling up which means that\n// an event can trigger on a newly reated DOM-node while the event bubbles up.\n//\n// Originally inspired by Vue\n// (https://github.com/vuejs/core/blob/caeb8a68811a1b0f79/packages/runtime-dom/src/modules/events.ts#L90-L101),\n// but modified to use a logical clock instead of Date.now() in case event handlers get attached\n// and events get dispatched during the same millisecond.\n//\n// The clock is incremented after each new event dispatch. This allows 1 000 000 new events\n// per second for over 280 years before the value reaches Number.MAX_SAFE_INTEGER (2**53 - 1).\nlet eventClock = 0;\n\n/**\n * Set a property value on a DOM node\n * @param {PreactElement} dom The DOM node to modify\n * @param {string} name The name of the property to set\n * @param {*} value The value to set the property to\n * @param {*} oldValue The old value the property had\n * @param {string} namespace Whether or not this DOM node is an SVG node or not\n */\nexport function setProperty(dom, name, value, oldValue, namespace) {\n\tlet useCapture;\n\n\to: if (name === 'style') {\n\t\tif (typeof value == 'string') {\n\t\t\tdom.style.cssText = value;\n\t\t} else {\n\t\t\tif (typeof oldValue == 'string') {\n\t\t\t\tdom.style.cssText = oldValue = '';\n\t\t\t}\n\n\t\t\tif (oldValue) {\n\t\t\t\tfor (name in oldValue) {\n\t\t\t\t\tif (!(value && name in value)) {\n\t\t\t\t\t\tsetStyle(dom.style, name, '');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (value) {\n\t\t\t\tfor (name in value) {\n\t\t\t\t\tif (!oldValue || value[name] !== oldValue[name]) {\n\t\t\t\t\t\tsetStyle(dom.style, name, value[name]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t// Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\n\telse if (name[0] === 'o' && name[1] === 'n') {\n\t\tuseCapture =\n\t\t\tname !== (name = name.replace(/(PointerCapture)$|Capture$/i, '$1'));\n\n\t\t// Infer correct casing for DOM built-in events:\n\t\tif (\n\t\t\tname.toLowerCase() in dom ||\n\t\t\tname === 'onFocusOut' ||\n\t\t\tname === 'onFocusIn'\n\t\t)\n\t\t\tname = name.toLowerCase().slice(2);\n\t\telse name = name.slice(2);\n\n\t\tif (!dom._listeners) dom._listeners = {};\n\t\tdom._listeners[name + useCapture] = value;\n\n\t\tif (value) {\n\t\t\tif (!oldValue) {\n\t\t\t\tvalue._attached = eventClock;\n\t\t\t\tdom.addEventListener(\n\t\t\t\t\tname,\n\t\t\t\t\tuseCapture ? eventProxyCapture : eventProxy,\n\t\t\t\t\tuseCapture\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tvalue._attached = oldValue._attached;\n\t\t\t}\n\t\t} else {\n\t\t\tdom.removeEventListener(\n\t\t\t\tname,\n\t\t\t\tuseCapture ? eventProxyCapture : eventProxy,\n\t\t\t\tuseCapture\n\t\t\t);\n\t\t}\n\t} else {\n\t\tif (namespace == 'http://www.w3.org/2000/svg') {\n\t\t\t// Normalize incorrect prop usage for SVG:\n\t\t\t// - xlink:href / xlinkHref --> href (xlink:href was removed from SVG and isn't needed)\n\t\t\t// - className --> class\n\t\t\tname = name.replace(/xlink(H|:h)/, 'h').replace(/sName$/, 's');\n\t\t} else if (\n\t\t\tname != 'width' &&\n\t\t\tname != 'height' &&\n\t\t\tname != 'href' &&\n\t\t\tname != 'list' &&\n\t\t\tname != 'form' &&\n\t\t\t// Default value in browsers is `-1` and an empty string is\n\t\t\t// cast to `0` instead\n\t\t\tname != 'tabIndex' &&\n\t\t\tname != 'download' &&\n\t\t\tname != 'rowSpan' &&\n\t\t\tname != 'colSpan' &&\n\t\t\tname != 'role' &&\n\t\t\tname != 'popover' &&\n\t\t\tname in dom\n\t\t) {\n\t\t\ttry {\n\t\t\t\tdom[name] = value == null ? '' : value;\n\t\t\t\t// labelled break is 1b smaller here than a return statement (sorry)\n\t\t\t\tbreak o;\n\t\t\t} catch (e) {}\n\t\t}\n\n\t\t// aria- and data- attributes have no boolean representation.\n\t\t// A `false` value is different from the attribute not being\n\t\t// present, so we can't remove it. For non-boolean aria\n\t\t// attributes we could treat false as a removal, but the\n\t\t// amount of exceptions would cost too many bytes. On top of\n\t\t// that other frameworks generally stringify `false`.\n\n\t\tif (typeof value == 'function') {\n\t\t\t// never serialize functions as attribute values\n\t\t} else if (value != null && (value !== false || name[4] === '-')) {\n\t\t\tdom.setAttribute(name, name == 'popover' && value == true ? '' : value);\n\t\t} else {\n\t\t\tdom.removeAttribute(name);\n\t\t}\n\t}\n}\n\n/**\n * Create an event proxy function.\n * @param {boolean} useCapture Is the event handler for the capture phase.\n * @private\n */\nfunction createEventProxy(useCapture) {\n\t/**\n\t * Proxy an event to hooked event handlers\n\t * @param {PreactEvent} e The event object from the browser\n\t * @private\n\t */\n\treturn function (e) {\n\t\tif (this._listeners) {\n\t\t\tconst eventHandler = this._listeners[e.type + useCapture];\n\t\t\tif (e._dispatched == null) {\n\t\t\t\te._dispatched = eventClock++;\n\n\t\t\t\t// When `e._dispatched` is smaller than the time when the targeted event\n\t\t\t\t// handler was attached we know we have bubbled up to an element that was added\n\t\t\t\t// during patching the DOM.\n\t\t\t} else if (e._dispatched < eventHandler._attached) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\treturn eventHandler(options.event ? options.event(e) : e);\n\t\t}\n\t};\n}\n\nconst eventProxy = createEventProxy(false);\nconst eventProxyCapture = createEventProxy(true);\n", "import { enqueueRender } from './component';\n\nexport let i = 0;\n\nexport function createContext(defaultValue, contextId) {\n\tcontextId = '__cC' + i++;\n\n\tconst context = {\n\t\t_id: contextId,\n\t\t_defaultValue: defaultValue,\n\t\t/** @type {FunctionComponent} */\n\t\tConsumer(props, contextValue) {\n\t\t\t// return props.children(\n\t\t\t// \tcontext[contextId] ? context[contextId].props.value : defaultValue\n\t\t\t// );\n\t\t\treturn props.children(contextValue);\n\t\t},\n\t\t/** @type {FunctionComponent} */\n\t\tProvider(props) {\n\t\t\tif (!this.getChildContext) {\n\t\t\t\t/** @type {Set<Component> | null} */\n\t\t\t\tlet subs = new Set();\n\t\t\t\tlet ctx = {};\n\t\t\t\tctx[contextId] = this;\n\n\t\t\t\tthis.getChildContext = () => ctx;\n\n\t\t\t\tthis.componentWillUnmount = () => {\n\t\t\t\t\tsubs = null;\n\t\t\t\t};\n\n\t\t\t\tthis.shouldComponentUpdate = function (_props) {\n\t\t\t\t\tif (this.props.value !== _props.value) {\n\t\t\t\t\t\tsubs.forEach(c => {\n\t\t\t\t\t\t\tc._force = true;\n\t\t\t\t\t\t\tenqueueRender(c);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\tthis.sub = c => {\n\t\t\t\t\tsubs.add(c);\n\t\t\t\t\tlet old = c.componentWillUnmount;\n\t\t\t\t\tc.componentWillUnmount = () => {\n\t\t\t\t\t\tif (subs) {\n\t\t\t\t\t\t\tsubs.delete(c);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (old) old.call(c);\n\t\t\t\t\t};\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn props.children;\n\t\t}\n\t};\n\n\t// Devtools needs access to the context object when it\n\t// encounters a Provider. This is necessary to support\n\t// setting `displayName` on the context object instead\n\t// of on the component itself. See:\n\t// https://reactjs.org/docs/context.html#contextdisplayname\n\n\treturn (context.Provider._contextRef = context.Consumer.contextType =\n\t\tcontext);\n}\n", "import { diff, unmount, applyRef } from './index';\nimport { createVNode, Fragment } from '../create-element';\nimport { EMPTY_OBJ, EMPTY_ARR, INSERT_VNODE, MATCHED } from '../constants';\nimport { isArray } from '../util';\nimport { getDomSibling } from '../component';\n\n/**\n * Diff the children of a virtual node\n * @param {PreactElement} parentDom The DOM element whose children are being\n * diffed\n * @param {ComponentChildren[]} renderResult\n * @param {VNode} newParentVNode The new virtual node whose children should be\n * diff'ed against oldParentVNode\n * @param {VNode} oldParentVNode The old virtual node whose children should be\n * diff'ed against newParentVNode\n * @param {object} globalContext The current context object - modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nexport function diffChildren(\n\tparentDom,\n\trenderResult,\n\tnewParentVNode,\n\toldParentVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating,\n\trefQueue\n) {\n\tlet i,\n\t\t/** @type {VNode} */\n\t\toldVNode,\n\t\t/** @type {VNode} */\n\t\tchildVNode,\n\t\t/** @type {PreactElement} */\n\t\tnewDom,\n\t\t/** @type {PreactElement} */\n\t\tfirstChildDom;\n\n\t// This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\n\t// as EMPTY_OBJ._children should be `undefined`.\n\t/** @type {VNode[]} */\n\tlet oldChildren = (oldParentVNode && oldParentVNode._children) || EMPTY_ARR;\n\n\tlet newChildrenLength = renderResult.length;\n\n\tnewParentVNode._nextDom = oldDom;\n\tconstructNewChildrenArray(newParentVNode, renderResult, oldChildren);\n\toldDom = newParentVNode._nextDom;\n\n\tfor (i = 0; i < newChildrenLength; i++) {\n\t\tchildVNode = newParentVNode._children[i];\n\t\tif (childVNode == null) continue;\n\n\t\t// At this point, constructNewChildrenArray has assigned _index to be the\n\t\t// matchingIndex for this VNode's oldVNode (or -1 if there is no oldVNode).\n\t\tif (childVNode._index === -1) {\n\t\t\toldVNode = EMPTY_OBJ;\n\t\t} else {\n\t\t\toldVNode = oldChildren[childVNode._index] || EMPTY_OBJ;\n\t\t}\n\n\t\t// Update childVNode._index to its final index\n\t\tchildVNode._index = i;\n\n\t\t// Morph the old element into the new one, but don't append it to the dom yet\n\t\tdiff(\n\t\t\tparentDom,\n\t\t\tchildVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tnamespace,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\toldDom,\n\t\t\tisHydrating,\n\t\t\trefQueue\n\t\t);\n\n\t\t// Adjust DOM nodes\n\t\tnewDom = childVNode._dom;\n\t\tif (childVNode.ref && oldVNode.ref != childVNode.ref) {\n\t\t\tif (oldVNode.ref) {\n\t\t\t\tapplyRef(oldVNode.ref, null, childVNode);\n\t\t\t}\n\t\t\trefQueue.push(\n\t\t\t\tchildVNode.ref,\n\t\t\t\tchildVNode._component || newDom,\n\t\t\t\tchildVNode\n\t\t\t);\n\t\t}\n\n\t\tif (firstChildDom == null && newDom != null) {\n\t\t\tfirstChildDom = newDom;\n\t\t}\n\n\t\tif (\n\t\t\tchildVNode._flags & INSERT_VNODE ||\n\t\t\toldVNode._children === childVNode._children\n\t\t) {\n\t\t\toldDom = insert(childVNode, oldDom, parentDom);\n\t\t} else if (\n\t\t\ttypeof childVNode.type == 'function' &&\n\t\t\tchildVNode._nextDom !== undefined\n\t\t) {\n\t\t\t// Since Fragments or components that return Fragment like VNodes can\n\t\t\t// contain multiple DOM nodes as the same level, continue the diff from\n\t\t\t// the sibling of last DOM child of this child VNode\n\t\t\toldDom = childVNode._nextDom;\n\t\t} else if (newDom) {\n\t\t\toldDom = newDom.nextSibling;\n\t\t}\n\n\t\t// Eagerly cleanup _nextDom. We don't need to persist the value because it\n\t\t// is only used by `diffChildren` to determine where to resume the diff\n\t\t// after diffing Components and Fragments. Once we store it the nextDOM\n\t\t// local var, we can clean up the property. Also prevents us hanging on to\n\t\t// DOM nodes that may have been unmounted.\n\t\tchildVNode._nextDom = undefined;\n\n\t\t// Unset diffing flags\n\t\tchildVNode._flags &= ~(INSERT_VNODE | MATCHED);\n\t}\n\n\t// TODO: With new child diffing algo, consider alt ways to diff Fragments.\n\t// Such as dropping oldDom and moving fragments in place\n\t//\n\t// Because the newParentVNode is Fragment-like, we need to set it's\n\t// _nextDom property to the nextSibling of its last child DOM node.\n\t//\n\t// `oldDom` contains the correct value here because if the last child\n\t// is a Fragment-like, then oldDom has already been set to that child's _nextDom.\n\t// If the last child is a DOM VNode, then oldDom will be set to that DOM\n\t// node's nextSibling.\n\tnewParentVNode._nextDom = oldDom;\n\tnewParentVNode._dom = firstChildDom;\n}\n\n/**\n * @param {VNode} newParentVNode\n * @param {ComponentChildren[]} renderResult\n * @param {VNode[]} oldChildren\n */\nfunction constructNewChildrenArray(newParentVNode, renderResult, oldChildren) {\n\t/** @type {number} */\n\tlet i;\n\t/** @type {VNode} */\n\tlet childVNode;\n\t/** @type {VNode} */\n\tlet oldVNode;\n\n\tconst newChildrenLength = renderResult.length;\n\tlet oldChildrenLength = oldChildren.length,\n\t\tremainingOldChildren = oldChildrenLength;\n\n\tlet skew = 0;\n\n\tnewParentVNode._children = [];\n\tfor (i = 0; i < newChildrenLength; i++) {\n\t\t// @ts-expect-error We are reusing the childVNode variable to hold both the\n\t\t// pre and post normalized childVNode\n\t\tchildVNode = renderResult[i];\n\n\t\tif (\n\t\t\tchildVNode == null ||\n\t\t\ttypeof childVNode == 'boolean' ||\n\t\t\ttypeof childVNode == 'function'\n\t\t) {\n\t\t\tchildVNode = newParentVNode._children[i] = null;\n\t\t\tcontinue;\n\t\t}\n\t\t// If this newVNode is being reused (e.g. <div>{reuse}{reuse}</div>) in the same diff,\n\t\t// or we are rendering a component (e.g. setState) copy the oldVNodes so it can have\n\t\t// it's own DOM & etc. pointers\n\t\telse if (\n\t\t\ttypeof childVNode == 'string' ||\n\t\t\ttypeof childVNode == 'number' ||\n\t\t\t// eslint-disable-next-line valid-typeof\n\t\t\ttypeof childVNode == 'bigint' ||\n\t\t\tchildVNode.constructor == String\n\t\t) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tnull,\n\t\t\t\tchildVNode,\n\t\t\t\tnull,\n\t\t\t\tnull,\n\t\t\t\tnull\n\t\t\t);\n\t\t} else if (isArray(childVNode)) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tFragment,\n\t\t\t\t{ children: childVNode },\n\t\t\t\tnull,\n\t\t\t\tnull,\n\t\t\t\tnull\n\t\t\t);\n\t\t} else if (childVNode.constructor === undefined && childVNode._depth > 0) {\n\t\t\t// VNode is already in use, clone it. This can happen in the following\n\t\t\t// scenario:\n\t\t\t//   const reuse = <div />\n\t\t\t//   <div>{reuse}<span />{reuse}</div>\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tchildVNode.type,\n\t\t\t\tchildVNode.props,\n\t\t\t\tchildVNode.key,\n\t\t\t\tchildVNode.ref ? childVNode.ref : null,\n\t\t\t\tchildVNode._original\n\t\t\t);\n\t\t} else {\n\t\t\tchildVNode = newParentVNode._children[i] = childVNode;\n\t\t}\n\n\t\tconst skewedIndex = i + skew;\n\t\tchildVNode._parent = newParentVNode;\n\t\tchildVNode._depth = newParentVNode._depth + 1;\n\n\t\t// Temporarily store the matchingIndex on the _index property so we can pull\n\t\t// out the oldVNode in diffChildren. We'll override this to the VNode's\n\t\t// final index after using this property to get the oldVNode\n\t\tconst matchingIndex = (childVNode._index = findMatchingIndex(\n\t\t\tchildVNode,\n\t\t\toldChildren,\n\t\t\tskewedIndex,\n\t\t\tremainingOldChildren\n\t\t));\n\n\t\toldVNode = null;\n\t\tif (matchingIndex !== -1) {\n\t\t\toldVNode = oldChildren[matchingIndex];\n\t\t\tremainingOldChildren--;\n\t\t\tif (oldVNode) {\n\t\t\t\toldVNode._flags |= MATCHED;\n\t\t\t}\n\t\t}\n\n\t\t// Here, we define isMounting for the purposes of the skew diffing\n\t\t// algorithm. Nodes that are unsuspending are considered mounting and we detect\n\t\t// this by checking if oldVNode._original === null\n\t\tconst isMounting = oldVNode == null || oldVNode._original === null;\n\n\t\tif (isMounting) {\n\t\t\tif (matchingIndex == -1) {\n\t\t\t\tskew--;\n\t\t\t}\n\n\t\t\t// If we are mounting a DOM VNode, mark it for insertion\n\t\t\tif (typeof childVNode.type != 'function') {\n\t\t\t\tchildVNode._flags |= INSERT_VNODE;\n\t\t\t}\n\t\t} else if (matchingIndex !== skewedIndex) {\n\t\t\t// When we move elements around i.e. [0, 1, 2] --> [1, 0, 2]\n\t\t\t// --> we diff 1, we find it at position 1 while our skewed index is 0 and our skew is 0\n\t\t\t//     we set the skew to 1 as we found an offset.\n\t\t\t// --> we diff 0, we find it at position 0 while our skewed index is at 2 and our skew is 1\n\t\t\t//     this makes us increase the skew again.\n\t\t\t// --> we diff 2, we find it at position 2 while our skewed index is at 4 and our skew is 2\n\t\t\t//\n\t\t\t// this becomes an optimization question where currently we see a 1 element offset as an insertion\n\t\t\t// or deletion i.e. we optimize for [0, 1, 2] --> [9, 0, 1, 2]\n\t\t\t// while a more than 1 offset we see as a swap.\n\t\t\t// We could probably build heuristics for having an optimized course of action here as well, but\n\t\t\t// might go at the cost of some bytes.\n\t\t\t//\n\t\t\t// If we wanted to optimize for i.e. only swaps we'd just do the last two code-branches and have\n\t\t\t// only the first item be a re-scouting and all the others fall in their skewed counter-part.\n\t\t\t// We could also further optimize for swaps\n\t\t\tif (matchingIndex == skewedIndex - 1) {\n\t\t\t\tskew--;\n\t\t\t} else if (matchingIndex == skewedIndex + 1) {\n\t\t\t\tskew++;\n\t\t\t} else {\n\t\t\t\tif (matchingIndex > skewedIndex) {\n\t\t\t\t\tskew--;\n\t\t\t\t} else {\n\t\t\t\t\tskew++;\n\t\t\t\t}\n\n\t\t\t\t// Move this VNode's DOM if the original index (matchingIndex) doesn't\n\t\t\t\t// match the new skew index (i + new skew)\n\t\t\t\t// In the former two branches we know that it matches after skewing\n\t\t\t\tchildVNode._flags |= INSERT_VNODE;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Remove remaining oldChildren if there are any. Loop forwards so that as we\n\t// unmount DOM from the beginning of the oldChildren, we can adjust oldDom to\n\t// point to the next child, which needs to be the first DOM node that won't be\n\t// unmounted.\n\tif (remainingOldChildren) {\n\t\tfor (i = 0; i < oldChildrenLength; i++) {\n\t\t\toldVNode = oldChildren[i];\n\t\t\tif (oldVNode != null && (oldVNode._flags & MATCHED) === 0) {\n\t\t\t\tif (oldVNode._dom == newParentVNode._nextDom) {\n\t\t\t\t\tnewParentVNode._nextDom = getDomSibling(oldVNode);\n\t\t\t\t}\n\n\t\t\t\tunmount(oldVNode, oldVNode);\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * @param {VNode} parentVNode\n * @param {PreactElement} oldDom\n * @param {PreactElement} parentDom\n * @returns {PreactElement}\n */\nfunction insert(parentVNode, oldDom, parentDom) {\n\t// Note: VNodes in nested suspended trees may be missing _children.\n\n\tif (typeof parentVNode.type == 'function') {\n\t\tlet children = parentVNode._children;\n\t\tfor (let i = 0; children && i < children.length; i++) {\n\t\t\tif (children[i]) {\n\t\t\t\t// If we enter this code path on sCU bailout, where we copy\n\t\t\t\t// oldVNode._children to newVNode._children, we need to update the old\n\t\t\t\t// children's _parent pointer to point to the newVNode (parentVNode\n\t\t\t\t// here).\n\t\t\t\tchildren[i]._parent = parentVNode;\n\t\t\t\toldDom = insert(children[i], oldDom, parentDom);\n\t\t\t}\n\t\t}\n\n\t\treturn oldDom;\n\t} else if (parentVNode._dom != oldDom) {\n\t\tif (oldDom && parentVNode.type && !parentDom.contains(oldDom)) {\n\t\t\toldDom = getDomSibling(parentVNode);\n\t\t}\n\t\tparentDom.insertBefore(parentVNode._dom, oldDom || null);\n\t\toldDom = parentVNode._dom;\n\t}\n\n\tdo {\n\t\toldDom = oldDom && oldDom.nextSibling;\n\t} while (oldDom != null && oldDom.nodeType === 8);\n\n\treturn oldDom;\n}\n\n/**\n * Flatten and loop through the children of a virtual node\n * @param {ComponentChildren} children The unflattened children of a virtual\n * node\n * @returns {VNode[]}\n */\nexport function toChildArray(children, out) {\n\tout = out || [];\n\tif (children == null || typeof children == 'boolean') {\n\t} else if (isArray(children)) {\n\t\tchildren.some(child => {\n\t\t\ttoChildArray(child, out);\n\t\t});\n\t} else {\n\t\tout.push(children);\n\t}\n\treturn out;\n}\n\n/**\n * @param {VNode} childVNode\n * @param {VNode[]} oldChildren\n * @param {number} skewedIndex\n * @param {number} remainingOldChildren\n * @returns {number}\n */\nfunction findMatchingIndex(\n\tchildVNode,\n\toldChildren,\n\tskewedIndex,\n\tremainingOldChildren\n) {\n\tconst key = childVNode.key;\n\tconst type = childVNode.type;\n\tlet x = skewedIndex - 1;\n\tlet y = skewedIndex + 1;\n\tlet oldVNode = oldChildren[skewedIndex];\n\n\t// We only need to perform a search if there are more children\n\t// (remainingOldChildren) to search. However, if the oldVNode we just looked\n\t// at skewedIndex was not already used in this diff, then there must be at\n\t// least 1 other (so greater than 1) remainingOldChildren to attempt to match\n\t// against. So the following condition checks that ensuring\n\t// remainingOldChildren > 1 if the oldVNode is not already used/matched. Else\n\t// if the oldVNode was null or matched, then there could needs to be at least\n\t// 1 (aka `remainingOldChildren > 0`) children to find and compare against.\n\tlet shouldSearch =\n\t\tremainingOldChildren >\n\t\t(oldVNode != null && (oldVNode._flags & MATCHED) === 0 ? 1 : 0);\n\n\tif (\n\t\toldVNode === null ||\n\t\t(oldVNode &&\n\t\t\tkey == oldVNode.key &&\n\t\t\ttype === oldVNode.type &&\n\t\t\t(oldVNode._flags & MATCHED) === 0)\n\t) {\n\t\treturn skewedIndex;\n\t} else if (shouldSearch) {\n\t\twhile (x >= 0 || y < oldChildren.length) {\n\t\t\tif (x >= 0) {\n\t\t\t\toldVNode = oldChildren[x];\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\t(oldVNode._flags & MATCHED) === 0 &&\n\t\t\t\t\tkey == oldVNode.key &&\n\t\t\t\t\ttype === oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\treturn x;\n\t\t\t\t}\n\t\t\t\tx--;\n\t\t\t}\n\n\t\t\tif (y < oldChildren.length) {\n\t\t\t\toldVNode = oldChildren[y];\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\t(oldVNode._flags & MATCHED) === 0 &&\n\t\t\t\t\tkey == oldVNode.key &&\n\t\t\t\t\ttype === oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\treturn y;\n\t\t\t\t}\n\t\t\t\ty++;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn -1;\n}\n", "import {\n\tEMPTY_OBJ,\n\tMODE_HYDRATE,\n\tMODE_SUSPENDED,\n\tRESET_MODE\n} from '../constants';\nimport { BaseComponent, getDomSibling } from '../component';\nimport { Fragment } from '../create-element';\nimport { diffChildren } from './children';\nimport { setProperty } from './props';\nimport { assign, isArray, removeNode, slice } from '../util';\nimport options from '../options';\n\n/**\n * Diff two virtual nodes and apply proper changes to the DOM\n * @param {PreactElement} parentDom The parent of the DOM element\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object. Modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nexport function diff(\n\tparentDom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating,\n\trefQueue\n) {\n\t/** @type {any} */\n\tlet tmp,\n\t\tnewType = newVNode.type;\n\n\t// When passing through createElement it assigns the object\n\t// constructor as undefined. This to prevent JSON-injection.\n\tif (newVNode.constructor !== undefined) return null;\n\n\t// If the previous diff bailed out, resume creating/hydrating.\n\tif (oldVNode._flags & MODE_SUSPENDED) {\n\t\tisHydrating = !!(oldVNode._flags & MODE_HYDRATE);\n\t\toldDom = newVNode._dom = oldVNode._dom;\n\t\texcessDomChildren = [oldDom];\n\t}\n\n\tif ((tmp = options._diff)) tmp(newVNode);\n\n\touter: if (typeof newType == 'function') {\n\t\ttry {\n\t\t\tlet c, isNew, oldProps, oldState, snapshot, clearProcessingException;\n\t\t\tlet newProps = newVNode.props;\n\t\t\tconst isClassComponent =\n\t\t\t\t'prototype' in newType && newType.prototype.render;\n\n\t\t\t// Necessary for createContext api. Setting this property will pass\n\t\t\t// the context value as `this.context` just for this component.\n\t\t\ttmp = newType.contextType;\n\t\t\tlet provider = tmp && globalContext[tmp._id];\n\t\t\tlet componentContext = tmp\n\t\t\t\t? provider\n\t\t\t\t\t? provider.props.value\n\t\t\t\t\t: tmp._defaultValue\n\t\t\t\t: globalContext;\n\n\t\t\t// Get component and set it to `c`\n\t\t\tif (oldVNode._component) {\n\t\t\t\tc = newVNode._component = oldVNode._component;\n\t\t\t\tclearProcessingException = c._processingException = c._pendingError;\n\t\t\t} else {\n\t\t\t\t// Instantiate the new component\n\t\t\t\tif (isClassComponent) {\n\t\t\t\t\t// @ts-expect-error The check above verifies that newType is suppose to be constructed\n\t\t\t\t\tnewVNode._component = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\n\t\t\t\t} else {\n\t\t\t\t\t// @ts-expect-error Trust me, Component implements the interface we want\n\t\t\t\t\tnewVNode._component = c = new BaseComponent(\n\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t);\n\t\t\t\t\tc.constructor = newType;\n\t\t\t\t\tc.render = doRender;\n\t\t\t\t}\n\t\t\t\tif (provider) provider.sub(c);\n\n\t\t\t\tc.props = newProps;\n\t\t\t\tif (!c.state) c.state = {};\n\t\t\t\tc.context = componentContext;\n\t\t\t\tc._globalContext = globalContext;\n\t\t\t\tisNew = c._dirty = true;\n\t\t\t\tc._renderCallbacks = [];\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t}\n\n\t\t\t// Invoke getDerivedStateFromProps\n\t\t\tif (isClassComponent && c._nextState == null) {\n\t\t\t\tc._nextState = c.state;\n\t\t\t}\n\n\t\t\tif (isClassComponent && newType.getDerivedStateFromProps != null) {\n\t\t\t\tif (c._nextState == c.state) {\n\t\t\t\t\tc._nextState = assign({}, c._nextState);\n\t\t\t\t}\n\n\t\t\t\tassign(\n\t\t\t\t\tc._nextState,\n\t\t\t\t\tnewType.getDerivedStateFromProps(newProps, c._nextState)\n\t\t\t\t);\n\t\t\t}\n\n\t\t\toldProps = c.props;\n\t\t\toldState = c.state;\n\t\t\tc._vnode = newVNode;\n\n\t\t\t// Invoke pre-render lifecycle methods\n\t\t\tif (isNew) {\n\t\t\t\tif (\n\t\t\t\t\tisClassComponent &&\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\n\t\t\t\t\tc.componentWillMount != null\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillMount();\n\t\t\t\t}\n\n\t\t\t\tif (isClassComponent && c.componentDidMount != null) {\n\t\t\t\t\tc._renderCallbacks.push(c.componentDidMount);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (\n\t\t\t\t\tisClassComponent &&\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\n\t\t\t\t\tnewProps !== oldProps &&\n\t\t\t\t\tc.componentWillReceiveProps != null\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillReceiveProps(newProps, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\t!c._force &&\n\t\t\t\t\t((c.shouldComponentUpdate != null &&\n\t\t\t\t\t\tc.shouldComponentUpdate(\n\t\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\t\tc._nextState,\n\t\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t\t) === false) ||\n\t\t\t\t\t\tnewVNode._original === oldVNode._original)\n\t\t\t\t) {\n\t\t\t\t\t// More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\n\t\t\t\t\tif (newVNode._original !== oldVNode._original) {\n\t\t\t\t\t\t// When we are dealing with a bail because of sCU we have to update\n\t\t\t\t\t\t// the props, state and dirty-state.\n\t\t\t\t\t\t// when we are dealing with strict-equality we don't as the child could still\n\t\t\t\t\t\t// be dirtied see #3883\n\t\t\t\t\t\tc.props = newProps;\n\t\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t\t\tc._dirty = false;\n\t\t\t\t\t}\n\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t\tnewVNode._children.some(vnode => {\n\t\t\t\t\t\tif (vnode) vnode._parent = newVNode;\n\t\t\t\t\t});\n\n\t\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t\t}\n\t\t\t\t\tc._stateCallbacks = [];\n\n\t\t\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\t\t\tcommitQueue.push(c);\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\n\t\t\t\tif (c.componentWillUpdate != null) {\n\t\t\t\t\tc.componentWillUpdate(newProps, c._nextState, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (isClassComponent && c.componentDidUpdate != null) {\n\t\t\t\t\tc._renderCallbacks.push(() => {\n\t\t\t\t\t\tc.componentDidUpdate(oldProps, oldState, snapshot);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tc.context = componentContext;\n\t\t\tc.props = newProps;\n\t\t\tc._parentDom = parentDom;\n\t\t\tc._force = false;\n\n\t\t\tlet renderHook = options._render,\n\t\t\t\tcount = 0;\n\t\t\tif (isClassComponent) {\n\t\t\t\tc.state = c._nextState;\n\t\t\t\tc._dirty = false;\n\n\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t}\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t} else {\n\t\t\t\tdo {\n\t\t\t\t\tc._dirty = false;\n\t\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\t\t// Handle setState called in render, see #2553\n\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t} while (c._dirty && ++count < 25);\n\t\t\t}\n\n\t\t\t// Handle setState called in render, see #2553\n\t\t\tc.state = c._nextState;\n\n\t\t\tif (c.getChildContext != null) {\n\t\t\t\tglobalContext = assign(assign({}, globalContext), c.getChildContext());\n\t\t\t}\n\n\t\t\tif (isClassComponent && !isNew && c.getSnapshotBeforeUpdate != null) {\n\t\t\t\tsnapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\n\t\t\t}\n\n\t\t\tlet isTopLevelFragment =\n\t\t\t\ttmp != null && tmp.type === Fragment && tmp.key == null;\n\t\t\tlet renderResult = isTopLevelFragment ? tmp.props.children : tmp;\n\n\t\t\tdiffChildren(\n\t\t\t\tparentDom,\n\t\t\t\tisArray(renderResult) ? renderResult : [renderResult],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnamespace,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\toldDom,\n\t\t\t\tisHydrating,\n\t\t\t\trefQueue\n\t\t\t);\n\n\t\t\tc.base = newVNode._dom;\n\n\t\t\t// We successfully rendered this VNode, unset any stored hydration/bailout state:\n\t\t\tnewVNode._flags &= RESET_MODE;\n\n\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\tcommitQueue.push(c);\n\t\t\t}\n\n\t\t\tif (clearProcessingException) {\n\t\t\t\tc._pendingError = c._processingException = null;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tnewVNode._original = null;\n\t\t\t// if hydrating or creating initial tree, bailout preserves DOM:\n\t\t\tif (isHydrating || excessDomChildren != null) {\n\t\t\t\tnewVNode._flags |= isHydrating\n\t\t\t\t\t? MODE_HYDRATE | MODE_SUSPENDED\n\t\t\t\t\t: MODE_SUSPENDED;\n\n\t\t\t\twhile (oldDom && oldDom.nodeType === 8 && oldDom.nextSibling) {\n\t\t\t\t\toldDom = oldDom.nextSibling;\n\t\t\t\t}\n\t\t\t\texcessDomChildren[excessDomChildren.indexOf(oldDom)] = null;\n\t\t\t\tnewVNode._dom = oldDom;\n\t\t\t} else {\n\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t}\n\t\t\toptions._catchError(e, newVNode, oldVNode);\n\t\t}\n\t} else if (\n\t\texcessDomChildren == null &&\n\t\tnewVNode._original === oldVNode._original\n\t) {\n\t\tnewVNode._children = oldVNode._children;\n\t\tnewVNode._dom = oldVNode._dom;\n\t} else {\n\t\tnewVNode._dom = diffElementNodes(\n\t\t\toldVNode._dom,\n\t\t\tnewVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tnamespace,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\tisHydrating,\n\t\t\trefQueue\n\t\t);\n\t}\n\n\tif ((tmp = options.diffed)) tmp(newVNode);\n}\n\n/**\n * @param {Array<Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {VNode} root\n */\nexport function commitRoot(commitQueue, root, refQueue) {\n\troot._nextDom = undefined;\n\n\tfor (let i = 0; i < refQueue.length; i++) {\n\t\tapplyRef(refQueue[i], refQueue[++i], refQueue[++i]);\n\t}\n\n\tif (options._commit) options._commit(root, commitQueue);\n\n\tcommitQueue.some(c => {\n\t\ttry {\n\t\t\t// @ts-expect-error Reuse the commitQueue variable here so the type changes\n\t\t\tcommitQueue = c._renderCallbacks;\n\t\t\tc._renderCallbacks = [];\n\t\t\tcommitQueue.some(cb => {\n\t\t\t\t// @ts-expect-error See above comment on commitQueue\n\t\t\t\tcb.call(c);\n\t\t\t});\n\t\t} catch (e) {\n\t\t\toptions._catchError(e, c._vnode);\n\t\t}\n\t});\n}\n\n/**\n * Diff two virtual nodes representing DOM element\n * @param {PreactElement} dom The DOM element representing the virtual nodes\n * being diffed\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n * @returns {PreactElement}\n */\nfunction diffElementNodes(\n\tdom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\tisHydrating,\n\trefQueue\n) {\n\tlet oldProps = oldVNode.props;\n\tlet newProps = newVNode.props;\n\tlet nodeType = /** @type {string} */ (newVNode.type);\n\t/** @type {any} */\n\tlet i;\n\t/** @type {{ __html?: string }} */\n\tlet newHtml;\n\t/** @type {{ __html?: string }} */\n\tlet oldHtml;\n\t/** @type {ComponentChildren} */\n\tlet newChildren;\n\tlet value;\n\tlet inputValue;\n\tlet checked;\n\n\t// Tracks entering and exiting namespaces when descending through the tree.\n\tif (nodeType === 'svg') namespace = 'http://www.w3.org/2000/svg';\n\telse if (nodeType === 'math')\n\t\tnamespace = 'http://www.w3.org/1998/Math/MathML';\n\telse if (!namespace) namespace = 'http://www.w3.org/1999/xhtml';\n\n\tif (excessDomChildren != null) {\n\t\tfor (i = 0; i < excessDomChildren.length; i++) {\n\t\t\tvalue = excessDomChildren[i];\n\n\t\t\t// if newVNode matches an element in excessDomChildren or the `dom`\n\t\t\t// argument matches an element in excessDomChildren, remove it from\n\t\t\t// excessDomChildren so it isn't later removed in diffChildren\n\t\t\tif (\n\t\t\t\tvalue &&\n\t\t\t\t'setAttribute' in value === !!nodeType &&\n\t\t\t\t(nodeType ? value.localName === nodeType : value.nodeType === 3)\n\t\t\t) {\n\t\t\t\tdom = value;\n\t\t\t\texcessDomChildren[i] = null;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (dom == null) {\n\t\tif (nodeType === null) {\n\t\t\treturn document.createTextNode(newProps);\n\t\t}\n\n\t\tdom = document.createElementNS(\n\t\t\tnamespace,\n\t\t\tnodeType,\n\t\t\tnewProps.is && newProps\n\t\t);\n\n\t\t// we are creating a new node, so we can assume this is a new subtree (in\n\t\t// case we are hydrating), this deopts the hydrate\n\t\tif (isHydrating) {\n\t\t\tif (options._hydrationMismatch)\n\t\t\t\toptions._hydrationMismatch(newVNode, excessDomChildren);\n\t\t\tisHydrating = false;\n\t\t}\n\t\t// we created a new parent, so none of the previously attached children can be reused:\n\t\texcessDomChildren = null;\n\t}\n\n\tif (nodeType === null) {\n\t\t// During hydration, we still have to split merged text from SSR'd HTML.\n\t\tif (oldProps !== newProps && (!isHydrating || dom.data !== newProps)) {\n\t\t\tdom.data = newProps;\n\t\t}\n\t} else {\n\t\t// If excessDomChildren was not null, repopulate it with the current element's children:\n\t\texcessDomChildren = excessDomChildren && slice.call(dom.childNodes);\n\n\t\toldProps = oldVNode.props || EMPTY_OBJ;\n\n\t\t// If we are in a situation where we are not hydrating but are using\n\t\t// existing DOM (e.g. replaceNode) we should read the existing DOM\n\t\t// attributes to diff them\n\t\tif (!isHydrating && excessDomChildren != null) {\n\t\t\toldProps = {};\n\t\t\tfor (i = 0; i < dom.attributes.length; i++) {\n\t\t\t\tvalue = dom.attributes[i];\n\t\t\t\toldProps[value.name] = value.value;\n\t\t\t}\n\t\t}\n\n\t\tfor (i in oldProps) {\n\t\t\tvalue = oldProps[i];\n\t\t\tif (i == 'children') {\n\t\t\t} else if (i == 'dangerouslySetInnerHTML') {\n\t\t\t\toldHtml = value;\n\t\t\t} else if (!(i in newProps)) {\n\t\t\t\tif (\n\t\t\t\t\t(i == 'value' && 'defaultValue' in newProps) ||\n\t\t\t\t\t(i == 'checked' && 'defaultChecked' in newProps)\n\t\t\t\t) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tsetProperty(dom, i, null, value, namespace);\n\t\t\t}\n\t\t}\n\n\t\t// During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\n\t\t// @TODO we should warn in debug mode when props don't match here.\n\t\tfor (i in newProps) {\n\t\t\tvalue = newProps[i];\n\t\t\tif (i == 'children') {\n\t\t\t\tnewChildren = value;\n\t\t\t} else if (i == 'dangerouslySetInnerHTML') {\n\t\t\t\tnewHtml = value;\n\t\t\t} else if (i == 'value') {\n\t\t\t\tinputValue = value;\n\t\t\t} else if (i == 'checked') {\n\t\t\t\tchecked = value;\n\t\t\t} else if (\n\t\t\t\t(!isHydrating || typeof value == 'function') &&\n\t\t\t\toldProps[i] !== value\n\t\t\t) {\n\t\t\t\tsetProperty(dom, i, value, oldProps[i], namespace);\n\t\t\t}\n\t\t}\n\n\t\t// If the new vnode didn't have dangerouslySetInnerHTML, diff its children\n\t\tif (newHtml) {\n\t\t\t// Avoid re-applying the same '__html' if it did not changed between re-render\n\t\t\tif (\n\t\t\t\t!isHydrating &&\n\t\t\t\t(!oldHtml ||\n\t\t\t\t\t(newHtml.__html !== oldHtml.__html &&\n\t\t\t\t\t\tnewHtml.__html !== dom.innerHTML))\n\t\t\t) {\n\t\t\t\tdom.innerHTML = newHtml.__html;\n\t\t\t}\n\n\t\t\tnewVNode._children = [];\n\t\t} else {\n\t\t\tif (oldHtml) dom.innerHTML = '';\n\n\t\t\tdiffChildren(\n\t\t\t\tdom,\n\t\t\t\tisArray(newChildren) ? newChildren : [newChildren],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnodeType === 'foreignObject'\n\t\t\t\t\t? 'http://www.w3.org/1999/xhtml'\n\t\t\t\t\t: namespace,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\texcessDomChildren\n\t\t\t\t\t? excessDomChildren[0]\n\t\t\t\t\t: oldVNode._children && getDomSibling(oldVNode, 0),\n\t\t\t\tisHydrating,\n\t\t\t\trefQueue\n\t\t\t);\n\n\t\t\t// Remove children that are not part of any vnode.\n\t\t\tif (excessDomChildren != null) {\n\t\t\t\tfor (i = excessDomChildren.length; i--; ) {\n\t\t\t\t\tremoveNode(excessDomChildren[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// As above, don't diff props during hydration\n\t\tif (!isHydrating) {\n\t\t\ti = 'value';\n\t\t\tif (nodeType === 'progress' && inputValue == null) {\n\t\t\t\tdom.removeAttribute('value');\n\t\t\t} else if (\n\t\t\t\tinputValue !== undefined &&\n\t\t\t\t// #2756 For the <progress>-element the initial value is 0,\n\t\t\t\t// despite the attribute not being present. When the attribute\n\t\t\t\t// is missing the progress bar is treated as indeterminate.\n\t\t\t\t// To fix that we'll always update it when it is 0 for progress elements\n\t\t\t\t(inputValue !== dom[i] ||\n\t\t\t\t\t(nodeType === 'progress' && !inputValue) ||\n\t\t\t\t\t// This is only for IE 11 to fix <select> value not being updated.\n\t\t\t\t\t// To avoid a stale select value we need to set the option.value\n\t\t\t\t\t// again, which triggers IE11 to re-evaluate the select value\n\t\t\t\t\t(nodeType === 'option' && inputValue !== oldProps[i]))\n\t\t\t) {\n\t\t\t\tsetProperty(dom, i, inputValue, oldProps[i], namespace);\n\t\t\t}\n\n\t\t\ti = 'checked';\n\t\t\tif (checked !== undefined && checked !== dom[i]) {\n\t\t\t\tsetProperty(dom, i, checked, oldProps[i], namespace);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn dom;\n}\n\n/**\n * Invoke or update a ref, depending on whether it is a function or object ref.\n * @param {Ref<any> & { _unmount?: unknown }} ref\n * @param {any} value\n * @param {VNode} vnode\n */\nexport function applyRef(ref, value, vnode) {\n\ttry {\n\t\tif (typeof ref == 'function') {\n\t\t\tlet hasRefUnmount = typeof ref._unmount == 'function';\n\t\t\tif (hasRefUnmount) {\n\t\t\t\t// @ts-ignore TS doesn't like moving narrowing checks into variables\n\t\t\t\tref._unmount();\n\t\t\t}\n\n\t\t\tif (!hasRefUnmount || value != null) {\n\t\t\t\t// Store the cleanup function on the function\n\t\t\t\t// instance object itself to avoid shape\n\t\t\t\t// transitioning vnode\n\t\t\t\tref._unmount = ref(value);\n\t\t\t}\n\t\t} else ref.current = value;\n\t} catch (e) {\n\t\toptions._catchError(e, vnode);\n\t}\n}\n\n/**\n * Unmount a virtual node from the tree and apply DOM changes\n * @param {VNode} vnode The virtual node to unmount\n * @param {VNode} parentVNode The parent of the VNode that initiated the unmount\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\n * current element is already detached from the DOM.\n */\nexport function unmount(vnode, parentVNode, skipRemove) {\n\tlet r;\n\tif (options.unmount) options.unmount(vnode);\n\n\tif ((r = vnode.ref)) {\n\t\tif (!r.current || r.current === vnode._dom) {\n\t\t\tapplyRef(r, null, parentVNode);\n\t\t}\n\t}\n\n\tif ((r = vnode._component) != null) {\n\t\tif (r.componentWillUnmount) {\n\t\t\ttry {\n\t\t\t\tr.componentWillUnmount();\n\t\t\t} catch (e) {\n\t\t\t\toptions._catchError(e, parentVNode);\n\t\t\t}\n\t\t}\n\n\t\tr.base = r._parentDom = null;\n\t}\n\n\tif ((r = vnode._children)) {\n\t\tfor (let i = 0; i < r.length; i++) {\n\t\t\tif (r[i]) {\n\t\t\t\tunmount(\n\t\t\t\t\tr[i],\n\t\t\t\t\tparentVNode,\n\t\t\t\t\tskipRemove || typeof vnode.type != 'function'\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (!skipRemove) {\n\t\tremoveNode(vnode._dom);\n\t}\n\n\t// Must be set to `undefined` to properly clean up `_nextDom`\n\t// for which `null` is a valid value. See comment in `create-element.js`\n\tvnode._component = vnode._parent = vnode._dom = vnode._nextDom = undefined;\n}\n\n/** The `.render()` method for a PFC backing instance. */\nfunction doRender(props, state, context) {\n\treturn this.constructor(props, context);\n}\n", "import { EMPTY_OBJ } from './constants';\nimport { commitRoot, diff } from './diff/index';\nimport { createElement, Fragment } from './create-element';\nimport options from './options';\nimport { slice } from './util';\n\n/**\n * Render a Preact virtual node into a DOM element\n * @param {ComponentChild} vnode The virtual node to render\n * @param {PreactElement} parentDom The DOM element to render into\n * @param {PreactElement | object} [replaceNode] Optional: Attempt to re-use an\n * existing DOM tree rooted at `replaceNode`\n */\nexport function render(vnode, parentDom, replaceNode) {\n\tif (options._root) options._root(vnode, parentDom);\n\n\t// We abuse the `replaceNode` parameter in `hydrate()` to signal if we are in\n\t// hydration mode or not by passing the `hydrate` function instead of a DOM\n\t// element..\n\tlet isHydrating = typeof replaceNode == 'function';\n\n\t// To be able to support calling `render()` multiple times on the same\n\t// DOM node, we need to obtain a reference to the previous tree. We do\n\t// this by assigning a new `_children` property to DOM nodes which points\n\t// to the last rendered tree. By default this property is not present, which\n\t// means that we are mounting a new tree for the first time.\n\tlet oldVNode = isHydrating\n\t\t? null\n\t\t: (replaceNode && replaceNode._children) || parentDom._children;\n\n\tvnode = ((!isHydrating && replaceNode) || parentDom)._children =\n\t\tcreateElement(Fragment, null, [vnode]);\n\n\t// List of effects that need to be called after diffing.\n\tlet commitQueue = [],\n\t\trefQueue = [];\n\tdiff(\n\t\tparentDom,\n\t\t// Determine the new vnode tree and store it on the DOM element on\n\t\t// our custom `_children` property.\n\t\tvnode,\n\t\toldVNode || EMPTY_OBJ,\n\t\tEMPTY_OBJ,\n\t\tparentDom.namespaceURI,\n\t\t!isHydrating && replaceNode\n\t\t\t? [replaceNode]\n\t\t\t: oldVNode\n\t\t\t\t? null\n\t\t\t\t: parentDom.firstChild\n\t\t\t\t\t? slice.call(parentDom.childNodes)\n\t\t\t\t\t: null,\n\t\tcommitQueue,\n\t\t!isHydrating && replaceNode\n\t\t\t? replaceNode\n\t\t\t: oldVNode\n\t\t\t\t? oldVNode._dom\n\t\t\t\t: parentDom.firstChild,\n\t\tisHydrating,\n\t\trefQueue\n\t);\n\n\t// Flush all queued effects\n\tcommitRoot(commitQueue, vnode, refQueue);\n}\n\n/**\n * Update an existing DOM element with data from a Preact virtual node\n * @param {ComponentChild} vnode The virtual node to render\n * @param {PreactElement} parentDom The DOM element to update\n */\nexport function hydrate(vnode, parentDom) {\n\trender(vnode, parentDom, hydrate);\n}\n", "import { assign, slice } from './util';\nimport { createVNode } from './create-element';\n\n/**\n * Clones the given VNode, optionally adding attributes/props and replacing its\n * children.\n * @param {VNode} vnode The virtual DOM element to clone\n * @param {object} props Attributes/props to add when cloning\n * @param {Array<ComponentChildren>} rest Any additional arguments will be used\n * as replacement children.\n * @returns {VNode}\n */\nexport function cloneElement(vnode, props, children) {\n\tlet normalizedProps = assign({}, vnode.props),\n\t\tkey,\n\t\tref,\n\t\ti;\n\n\tlet defaultProps;\n\n\tif (vnode.type && vnode.type.defaultProps) {\n\t\tdefaultProps = vnode.type.defaultProps;\n\t}\n\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse if (props[i] === undefined && defaultProps !== undefined) {\n\t\t\tnormalizedProps[i] = defaultProps[i];\n\t\t} else {\n\t\t\tnormalizedProps[i] = props[i];\n\t\t}\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\treturn createVNode(\n\t\tvnode.type,\n\t\tnormalizedProps,\n\t\tkey || vnode.key,\n\t\tref || vnode.ref,\n\t\tnull\n\t);\n}\n", "/**\n * Find the closest error boundary to a thrown error and call it\n * @param {object} error The thrown value\n * @param {VNode} vnode The vnode that threw the error that was caught (except\n * for unmounting when this parameter is the highest parent that was being\n * unmounted)\n * @param {VNode} [oldVNode]\n * @param {ErrorInfo} [errorInfo]\n */\nexport function _catchError(error, vnode, oldVNode, errorInfo) {\n\t/** @type {Component} */\n\tlet component,\n\t\t/** @type {ComponentType} */\n\t\tctor,\n\t\t/** @type {boolean} */\n\t\thandled;\n\n\tfor (; (vnode = vnode._parent); ) {\n\t\tif ((component = vnode._component) && !component._processingException) {\n\t\t\ttry {\n\t\t\t\tctor = component.constructor;\n\n\t\t\t\tif (ctor && ctor.getDerivedStateFromError != null) {\n\t\t\t\t\tcomponent.setState(ctor.getDerivedStateFromError(error));\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\tif (component.componentDidCatch != null) {\n\t\t\t\t\tcomponent.componentDidCatch(error, errorInfo || {});\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\t// This is an error boundary. Mark it as having bailed out, and whether it was mid-hydration.\n\t\t\t\tif (handled) {\n\t\t\t\t\treturn (component._pendingError = component);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\terror = e;\n\t\t\t}\n\t\t}\n\t}\n\n\tthrow error;\n}\n"], "names": ["slice", "options", "vnodeId", "isValidElement", "rerenderQueue", "prevDebounce", "defer", "depthSort", "eventClock", "eventProxy", "eventProxyCapture", "i", "EMPTY_OBJ", "EMPTY_ARR", "IS_NON_DIMENSIONAL", "isArray", "Array", "assign", "obj", "props", "removeNode", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "type", "children", "key", "ref", "normalizedProps", "arguments", "length", "call", "defaultProps", "undefined", "createVNode", "original", "vnode", "__k", "__", "__b", "__e", "__d", "__c", "constructor", "__v", "__i", "__u", "createRef", "current", "Fragment", "BaseComponent", "context", "this", "getDomSibling", "childIndex", "sibling", "updateParentDomPointers", "child", "base", "enqueueRender", "c", "push", "process", "__r", "debounceRendering", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "component", "newVNode", "oldVNode", "oldDom", "commitQueue", "refQueue", "sort", "shift", "__P", "diff", "__n", "namespaceURI", "commitRoot", "diff<PERSON><PERSON><PERSON><PERSON>", "parentDom", "renderResult", "newParentVNode", "oldParentVNode", "globalContext", "namespace", "excessDomChildren", "isHydrating", "childVNode", "newDom", "firstChildDom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructNewChildrenArray", "applyRef", "insert", "nextS<PERSON>ling", "skewedIndex", "matchingIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remainingOldChildren", "skew", "String", "findMatchingIndex", "unmount", "parentVNode", "contains", "insertBefore", "nodeType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "out", "some", "x", "y", "setStyle", "style", "value", "setProperty", "test", "dom", "name", "oldValue", "useCapture", "o", "cssText", "replace", "toLowerCase", "l", "_attached", "addEventListener", "removeEventListener", "e", "removeAttribute", "setAttribute", "createEventProxy", "<PERSON><PERSON><PERSON><PERSON>", "_dispatched", "event", "tmp", "isNew", "oldProps", "oldState", "snapshot", "clearProcessingException", "newProps", "isClassComponent", "provider", "componentContext", "renderHook", "count", "newType", "outer", "prototype", "render", "contextType", "__E", "doR<PERSON>", "sub", "state", "__h", "_sb", "__s", "getDerivedStateFromProps", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "componentWillUpdate", "componentDidUpdate", "getChildContext", "getSnapshotBeforeUpdate", "MODE_HYDRATE", "indexOf", "diffElementNodes", "diffed", "root", "cb", "newHtml", "oldHtml", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputValue", "checked", "localName", "document", "createTextNode", "createElementNS", "is", "__m", "data", "childNodes", "attributes", "__html", "innerHTML", "hasRefUnmount", "<PERSON><PERSON><PERSON><PERSON>", "r", "componentWillUnmount", "replaceNode", "<PERSON><PERSON><PERSON><PERSON>", "hydrate", "cloneElement", "createContext", "defaultValue", "contextId", "Consumer", "contextValue", "Provider", "subs", "ctx", "Set", "_props", "for<PERSON>ach", "add", "old", "delete", "error", "errorInfo", "ctor", "handled", "getDerivedStateFromError", "setState", "componentDidCatch", "update", "callback", "s", "forceUpdate", "Promise", "then", "bind", "resolve", "setTimeout", "a", "b"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACa,IC0BAA,GChBPC,GCRFC,GAgGSC,GC+ETC,GAWAC,GAEEC,GA0BAC,GC/LFC,GAmJEC,GACAC,GC5KKC,GNUEC,IAAgC,CAAA,GAChCC,IAAY,EAAA,EACZC,IACZ,qECbYC,IAAUC,MAAMD,OAAAA;AAStB,SAASE,EAAOC,CAAAA,EAAKC,CAAAA;IAE3B,IAAK,IAAIR,KAAKQ,EAAOD,CAAAA,CAAIP,EAAAA,GAAKQ,CAAAA,CAAMR,EAAAA;IACpC,OAA6BO;AAC9B;AAQgB,SAAAE,EAAWC,CAAAA;IACtBA,KAAQA,EAAKC,UAAAA,IAAYD,EAAKC,UAAAA,CAAWC,WAAAA,CAAYF;AAC1D;AEXO,SAASG,EAAcC,CAAAA,EAAMN,CAAAA,EAAOO,CAAAA;IAC1C,IACCC,GACAC,GACAjB,GAHGkB,IAAkB,CAAA;IAItB,IAAKlB,KAAKQ,EACA,SAALR,IAAYgB,IAAMR,CAAAA,CAAMR,EAAAA,GACd,SAALA,IAAYiB,IAAMT,CAAAA,CAAMR,EAAAA,GAC5BkB,CAAAA,CAAgBlB,EAAAA,GAAKQ,CAAAA,CAAMR,EAAAA;IAUjC,IAPImB,UAAUC,MAAAA,GAAS,KAAA,CACtBF,EAAgBH,QAAAA,GACfI,UAAUC,MAAAA,GAAS,IAAI/B,EAAMgC,IAAAA,CAAKF,WAAW,KAAKJ,CAAAA,GAKjC,cAAA,OAARD,KAA2C,QAArBA,EAAKQ,YAAAA,EACrC,IAAKtB,KAAKc,EAAKQ,YAAAA,CAAAA,KACaC,MAAvBL,CAAAA,CAAgBlB,EAAAA,IAAAA,CACnBkB,CAAAA,CAAgBlB,EAAAA,GAAKc,EAAKQ,YAAAA,CAAatB,EAAAA;IAK1C,OAAOwB,EAAYV,GAAMI,GAAiBF,GAAKC,GAAK;AACrD;AAcO,SAASO,EAAYV,CAAAA,EAAMN,CAAAA,EAAOQ,CAAAA,EAAKC,CAAAA,EAAKQ,CAAAA;IAIlD,IAAMC,IAAQ;QACbZ,MAAAA;QACAN,OAAAA;QACAQ,KAAAA;QACAC,KAAAA;QACAU,KAAW;QACXC,IAAS;QACTC,KAAQ;QACRC,KAAM;QAKNC,KAAAA,KAAUR;QACVS,KAAY;QACZC,aAAAA,KAAaV;QACbW,KAAuB,QAAZT,IAAAA,EAAqBlC,IAAUkC;QAC1CU,KAAAA,CAAS;QACTC,KAAQ;IAAA;IAMT,OAFgB,QAAZX,KAAqC,QAAjBnC,EAAQoC,KAAAA,IAAepC,EAAQoC,KAAAA,CAAMA,IAEtDA;AACR;AAEO,SAASW;IACf,OAAO;QAAEC,SAAS;IAAA;AACnB;AAAA,SAEgBC,EAAS/B,CAAAA;IACxB,OAAOA,EAAMO;AACd;AAAA,SC/EgByB,EAAchC,CAAAA,EAAOiC,CAAAA;IACpCC,IAAAA,CAAKlC,KAAAA,GAAQA,GACbkC,IAAAA,CAAKD,OAAAA,GAAUA;AAChB;AA0EgB,SAAAE,EAAcjB,CAAAA,EAAOkB,CAAAA;IACpC,IAAkB,QAAdA,GAEH,OAAOlB,EAAKE,EAAAA,GACTe,EAAcjB,EAAKE,EAAAA,EAAUF,EAAKS,GAAAA,GAAU,KAC5C;IAIJ,IADA,IAAIU,GACGD,IAAalB,EAAKC,GAAAA,CAAWP,MAAAA,EAAQwB,IAG3C,IAAe,QAAA,CAFfC,IAAUnB,EAAKC,GAAAA,CAAWiB,EAAAA,KAEa,QAAhBC,EAAOf,GAAAA,EAI7B,OAAOe,EAAOf,GAAAA;IAShB,OAA4B,cAAA,OAAdJ,EAAMZ,IAAAA,GAAqB6B,EAAcjB,KAAS;AACjE;AA2CA,SAASoB,EAAwBpB,CAAAA;IAAjC,IAGW1B,GACJ+C;IAHN,IAA+B,QAAA,CAA1BrB,IAAQA,EAAKE,EAAAA,KAAyC,QAApBF,EAAKM,GAAAA,EAAqB;QAEhE,IADAN,EAAKI,GAAAA,GAAQJ,EAAKM,GAAAA,CAAYgB,IAAAA,GAAO,MAC5BhD,IAAI,GAAGA,IAAI0B,EAAKC,GAAAA,CAAWP,MAAAA,EAAQpB,IAE3C,IAAa,QAAA,CADT+C,IAAQrB,EAAKC,GAAAA,CAAW3B,EAAAA,KACO,QAAd+C,EAAKjB,GAAAA,EAAe;YACxCJ,EAAKI,GAAAA,GAAQJ,EAAKM,GAAAA,CAAYgB,IAAAA,GAAOD,EAAKjB,GAAAA;YAC1C;QACD;QAGD,OAAOgB,EAAwBpB;IAChC;AACD;AA4BgB,SAAAuB,EAAcC,CAAAA;IAAAA,CAAAA,CAE1BA,EAACnB,GAAAA,IAAAA,CACDmB,EAACnB,GAAAA,GAAAA,CAAU,CAAA,KACZtC,EAAc0D,IAAAA,CAAKD,MAAAA,CAClBE,EAAOC,GAAAA,MACT3D,MAAiBJ,EAAQgE,iBAAAA,KAAAA,CAAAA,CAEzB5D,IAAeJ,EAAQgE,iBAAAA,KACN3D,CAAAA,EAAOyD;AAE1B;AASA,SAASA;IAAT,IACKF,GAMEK,GAzGkBC,GAOjBC,GANHC,GACHC,GACAC,GACAC;IAmGD,IAHApE,EAAcqE,IAAAA,CAAKlE,IAGXsD,IAAIzD,EAAcsE,KAAAA,IACrBb,EAACnB,GAAAA,IAAAA,CACAwB,IAAoB9D,EAAc2B,MAAAA,EAlGjCqC,IAAAA,KAAAA,GALNE,IAAAA,CADGD,IAAAA,CADoBF,IA0GNN,CAAAA,EAzGMhB,GAAAA,EACNJ,GAAAA,EACjB8B,IAAc,EAAA,EACdC,IAAW,EAAA,EAERL,EAASQ,GAAAA,IAAAA,CAAAA,CACNP,IAAWnD,EAAO,CAAA,GAAIoD,EAAAA,EACpBxB,GAAAA,GAAawB,EAAQxB,GAAAA,GAAa,GACtC5C,EAAQoC,KAAAA,IAAOpC,EAAQoC,KAAAA,CAAM+B,IAEjCQ,EACCT,EAASQ,GAAAA,EACTP,GACAC,GACAF,EAASU,GAAAA,EACTV,EAASQ,GAAAA,CAAYG,YAAAA,EJzII,KI0IzBT,EAAQtB,GAAAA,GAAyB;QAACuB;KAAAA,GAAU,MAC5CC,GACU,QAAVD,IAAiBhB,EAAce,KAAYC,GAAAA,CAAAA,CAAAA,CJ5IlB,KI6ItBD,EAAQtB,GAAAA,GACXyB,IAGDJ,EAAQvB,GAAAA,GAAawB,EAAQxB,GAAAA,EAC7BuB,EAAQ7B,EAAAA,CAAAD,GAAAA,CAAmB8B,EAAQtB,GAAAA,CAAAA,GAAWsB,GAC9CW,EAAWR,GAAaH,GAAUI,IAE9BJ,EAAQ3B,GAAAA,IAAS6B,KACpBb,EAAwBW,EAAAA,GA8EpBhE,EAAc2B,MAAAA,GAASmC,KAI1B9D,EAAcqE,IAAAA,CAAKlE,EAAAA;IAItBwD,EAAOC,GAAAA,GAAkB;AAC1B;AGlNO,SAASgB,EACfC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAhB,CAAAA,EACAD,CAAAA,EACAkB,CAAAA,EACAhB,CAAAA;IAXM,IAaF7D,GAEH0D,GAEAoB,GAEAC,GAEAC,GAKGC,IAAeR,KAAkBA,EAAc9C,GAAAA,IAAezB,GAE9DgF,IAAoBX,EAAanD,MAAAA;IAMrC,IAJAoD,EAAczC,GAAAA,GAAY4B,GAC1BwB,EAA0BX,GAAgBD,GAAcU,IACxDtB,IAASa,EAAczC,GAAAA,EAElB/B,IAAI,GAAGA,IAAIkF,GAAmBlF,IAEhB,QAAA,CADlB8E,IAAaN,EAAc7C,GAAAA,CAAW3B,EAAAA,KAAAA,CAMrC0D,IAAAA,CAD0B,MAAvBoB,EAAU3C,GAAAA,GACFlC,IAEAgF,CAAAA,CAAYH,EAAU3C,GAAAA,CAAAA,IAAYlC,GAI9C6E,EAAU3C,GAAAA,GAAUnC,GAGpBiE,EACCK,GACAQ,GACApB,GACAgB,GACAC,GACAC,GACAhB,GACAD,GACAkB,GACAhB,IAIDkB,IAASD,EAAUhD,GAAAA,EACfgD,EAAW7D,GAAAA,IAAOyC,EAASzC,GAAAA,IAAO6D,EAAW7D,GAAAA,IAAAA,CAC5CyC,EAASzC,GAAAA,IACZmE,EAAS1B,EAASzC,GAAAA,EAAK,MAAM6D,IAE9BjB,EAASV,IAAAA,CACR2B,EAAW7D,GAAAA,EACX6D,EAAU9C,GAAAA,IAAe+C,GACzBD,EAAAA,GAImB,QAAjBE,KAAmC,QAAVD,KAAAA,CAC5BC,IAAgBD,CAAAA,GPpGS,QOwGzBD,EAAU1C,GAAAA,IACVsB,EAAQ/B,GAAAA,KAAemD,EAAUnD,GAAAA,GAEjCgC,IAAS0B,EAAOP,GAAYnB,GAAQW,KAEV,cAAA,OAAnBQ,EAAWhE,IAAAA,IAAAA,KACMS,MAAxBuD,EAAU/C,GAAAA,GAKV4B,IAASmB,EAAU/C,GAAAA,GACTgD,KAAAA,CACVpB,IAASoB,EAAOO,WAAAA,GAQjBR,EAAU/C,GAAAA,GAAAA,KAAYR,GAGtBuD,EAAU1C,GAAAA,IAAAA,CAAW,MAAA;IAatBoC,EAAczC,GAAAA,GAAY4B,GAC1Ba,EAAc1C,GAAAA,GAAQkD;AACvB;AAOA,SAASG,EAA0BX,CAAAA,EAAgBD,CAAAA,EAAcU,CAAAA;IAAjE,IAEKjF,GAEA8E,GAEApB,GA+DG6B,GAOAC,GApEDN,IAAoBX,EAAanD,MAAAA,EACnCqE,IAAoBR,EAAY7D,MAAAA,EACnCsE,IAAuBD,GAEpBE,IAAO;IAGX,IADAnB,EAAc7C,GAAAA,GAAa,EAAA,EACtB3B,IAAI,GAAGA,IAAIkF,GAAmBlF,IAMnB,QAAA,CAHf8E,IAAaP,CAAAA,CAAavE,EAAAA,KAIJ,aAAA,OAAd8E,KACc,cAAA,OAAdA,IAAAA,CA8CFS,IAAcvF,IAAI2F,GAAAA,CA/BvBb,IAAaN,EAAc7C,GAAAA,CAAW3B,EAAAA,GANjB,YAAA,OAAd8E,KACc,YAAA,OAAdA,KAEc,YAAA,OAAdA,KACPA,EAAW7C,WAAAA,IAAe2D,SAEiBpE,EAC1C,MACAsD,GACA,MACA,MACA,QAES1E,EAAQ0E,KACyBtD,EAC1Ce,GACA;QAAExB,UAAU+D;IAAAA,GACZ,MACA,MACA,QAAA,KAEoCvD,MAA3BuD,EAAW7C,WAAAA,IAA6B6C,EAAUjD,GAAAA,GAAU,IAK3BL,EAC1CsD,EAAWhE,IAAAA,EACXgE,EAAWtE,KAAAA,EACXsE,EAAW9D,GAAAA,EACX8D,EAAW7D,GAAAA,GAAM6D,EAAW7D,GAAAA,GAAM,MAClC6D,EAAU5C,GAAAA,IAGgC4C,CAAAA,EAIlClD,EAAAA,GAAW4C,GACrBM,EAAUjD,GAAAA,GAAU2C,EAAc3C,GAAAA,GAAU,GAY5C6B,IAAW,MAAA,CACY,MAAA,CARjB8B,IAAiBV,EAAU3C,GAAAA,GAAU0D,EAC1Cf,GACAG,GACAM,GACAG,EAAAA,KAAAA,CAMAA,KAAAA,CADAhC,IAAWuB,CAAAA,CAAYO,EAAAA,KAAAA,CAGtB9B,EAAQtB,GAAAA,IP5OW,MAAA,CAAA,GOmPU,QAAZsB,KAA2C,SAAvBA,EAAQxB,GAAAA,GAAAA,CAAAA,CAGxB,KAAlBsD,KACHG,KAI6B,cAAA,OAAnBb,EAAWhE,IAAAA,IAAAA,CACrBgE,EAAU1C,GAAAA,IP9Pc,KAAA,CAAA,IOgQfoD,MAAkBD,KAAAA,CAiBxBC,KAAiBD,IAAc,IAClCI,MACUH,KAAiBD,IAAc,IACzCI,MAAAA,CAEIH,IAAgBD,IACnBI,MAEAA,KAMDb,EAAU1C,GAAAA,IP/Rc,KAAA,CAAA,CAAA,IO+KzB0C,IAAaN,EAAc7C,GAAAA,CAAW3B,EAAAA,GAAK;IAyH7C,IAAI0F,GACH,IAAK1F,IAAI,GAAGA,IAAIyF,GAAmBzF,IAElB,QAAA,CADhB0D,IAAWuB,CAAAA,CAAYjF,EAAAA,KACiC,KAAA,CPzSpC,SOySK0D,EAAQtB,GAAAA,KAAAA,CAC5BsB,EAAQ5B,GAAAA,IAAS0C,EAAczC,GAAAA,IAAAA,CAClCyC,EAAczC,GAAAA,GAAYY,EAAce,EAAAA,GAGzCoC,EAAQpC,GAAUA,EAAAA;AAItB;AAQA,SAAS2B,EAAOU,CAAAA,EAAapC,CAAAA,EAAQW,CAAAA;IAArC,IAIMvD,GACKf;IAFV,IAA+B,cAAA,OAApB+F,EAAYjF,IAAAA,EAAoB;QAE1C,IADIC,IAAWgF,EAAWpE,GAAAA,EACjB3B,IAAI,GAAGe,KAAYf,IAAIe,EAASK,MAAAA,EAAQpB,IAC5Ce,CAAAA,CAASf,EAAAA,IAAAA,CAKZe,CAAAA,CAASf,EAAAA,CAAE4B,EAAAA,GAAWmE,GACtBpC,IAAS0B,EAAOtE,CAAAA,CAASf,EAAAA,EAAI2D,GAAQW,EAAAA;QAIvC,OAAOX;IACR;IAAWoC,EAAWjE,GAAAA,IAAS6B,KAAAA,CAC1BA,KAAUoC,EAAYjF,IAAAA,IAAAA,CAASwD,EAAU0B,QAAAA,CAASrC,MAAAA,CACrDA,IAAShB,EAAcoD,EAAAA,GAExBzB,EAAU2B,YAAAA,CAAaF,EAAWjE,GAAAA,EAAO6B,KAAU,OACnDA,IAASoC,EAAWjE,GAAAA;IAGrB,GAAA;QACC6B,IAASA,KAAUA,EAAO2B,WAAAA;IAAAA,QACR,QAAV3B,KAAsC,MAApBA,EAAOuC,QAAAA;IAElC,OAAOvC;AACR;AAQgB,SAAAwC,EAAapF,CAAAA,EAAUqF,CAAAA;IAUtC,OATAA,IAAMA,KAAO,EAAA,EACG,QAAZrF,KAAuC,aAAA,OAAZA,KAAAA,CACpBX,EAAQW,KAClBA,EAASsF,IAAAA,CAAK,SAAAtD,CAAAA;QACboD,EAAapD,GAAOqD;IACrB,KAEAA,EAAIjD,IAAAA,CAAKpC,EAAAA,GAEHqF;AACR;AASA,SAASP,EACRf,CAAAA,EACAG,CAAAA,EACAM,CAAAA,EACAG,CAAAA;IAJD,IAMO1E,IAAM8D,EAAW9D,GAAAA,EACjBF,IAAOgE,EAAWhE,IAAAA,EACpBwF,IAAIf,IAAc,GAClBgB,IAAIhB,IAAc,GAClB7B,IAAWuB,CAAAA,CAAYM,EAAAA;IAc3B,IACc,SAAb7B,KACCA,KACA1C,KAAO0C,EAAS1C,GAAAA,IAChBF,MAAS4C,EAAS5C,IAAAA,IACc,KAAA,CPjZZ,SOiZnB4C,EAAQtB,GAAAA,GAEV,OAAOmD;IACD,IAXNG,IAAAA,CACa,QAAZhC,KAAoD,KAAA,CP1YhC,SO0YCA,EAAQtB,GAAAA,IAA2B,IAAI,CAAA,GAW7D,MAAOkE,KAAK,KAAKC,IAAItB,EAAY7D,MAAAA,EAAQ;QACxC,IAAIkF,KAAK,GAAG;YAEX,IAAA,CADA5C,IAAWuB,CAAAA,CAAYqB,EAAAA,KAGU,KAAA,CP1Zd,SO0ZjB5C,EAAQtB,GAAAA,KACTpB,KAAO0C,EAAS1C,GAAAA,IAChBF,MAAS4C,EAAS5C,IAAAA,EAElB,OAAOwF;YAERA;QACD;QAEA,IAAIC,IAAItB,EAAY7D,MAAAA,EAAQ;YAE3B,IAAA,CADAsC,IAAWuB,CAAAA,CAAYsB,EAAAA,KAGU,KAAA,CPvad,SOuajB7C,EAAQtB,GAAAA,KACTpB,KAAO0C,EAAS1C,GAAAA,IAChBF,MAAS4C,EAAS5C,IAAAA,EAElB,OAAOyF;YAERA;QACD;IACD;IAGD,OAAA,CAAQ;AACT;AFvbA,SAASC,EAASC,CAAAA,EAAOzF,CAAAA,EAAK0F,CAAAA;IACd,QAAX1F,CAAAA,CAAI,EAAA,GACPyF,EAAME,WAAAA,CAAY3F,GAAc,QAAT0F,IAAgB,KAAKA,KAE5CD,CAAAA,CAAMzF,EAAAA,GADa,QAAT0F,IACG,KACa,YAAA,OAATA,KAAqBvG,EAAmByG,IAAAA,CAAK5F,KACjD0F,IAEAA,IAAQ;AAEvB;AAuBO,SAASC,EAAYE,CAAAA,EAAKC,CAAAA,EAAMJ,CAAAA,EAAOK,CAAAA,EAAUpC,CAAAA;IACvD,IAAIqC;IAEJC,GAAG,IAAa,YAATH,GACN,IAAoB,YAAA,OAATJ,GACVG,EAAIJ,KAAAA,CAAMS,OAAAA,GAAUR;SACd;QAKN,IAJuB,YAAA,OAAZK,KAAAA,CACVF,EAAIJ,KAAAA,CAAMS,OAAAA,GAAUH,IAAW,EAAA,GAG5BA,GACH,IAAKD,KAAQC,EACNL,KAASI,KAAQJ,KACtBF,EAASK,EAAIJ,KAAAA,EAAOK,GAAM;QAK7B,IAAIJ,GACH,IAAKI,KAAQJ,EACPK,KAAYL,CAAAA,CAAMI,EAAAA,KAAUC,CAAAA,CAASD,EAAAA,IACzCN,EAASK,EAAIJ,KAAAA,EAAOK,GAAMJ,CAAAA,CAAMI,EAAAA;IAIpC;SAGQA,IAAY,QAAZA,CAAAA,CAAK,EAAA,IAA0B,QAAZA,CAAAA,CAAK,EAAA,EAChCE,IACCF,MAAAA,CAAUA,IAAOA,EAAKK,OAAAA,CAAQ,+BAA+B,KAAA,GAQ7DL,IAJAA,EAAKM,WAAAA,MAAiBP,KACb,iBAATC,KACS,gBAATA,IAEOA,EAAKM,WAAAA,GAAc/H,KAAAA,CAAM,KACrByH,EAAKzH,KAAAA,CAAM,IAElBwH,EAAGQ,CAAAA,IAAAA,CAAaR,EAAGQ,CAAAA,GAAc,CAAA,CAAA,GACtCR,EAAGQ,CAAAA,CAAYP,IAAOE,EAAAA,GAAcN,GAEhCA,IACEK,IAQJL,EAAMY,CAAAA,GAAYP,EAASO,CAAAA,GAAAA,CAP3BZ,EAAMY,CAAAA,GAAYzH,GAClBgH,EAAIU,gBAAAA,CACHT,GACAE,IAAajH,IAAoBD,GACjCkH,EAAAA,IAMFH,EAAIW,mBAAAA,CACHV,GACAE,IAAajH,IAAoBD,GACjCkH;SAGI;QACN,IAAiB,gCAAbrC,GAIHmC,IAAOA,EAAKK,OAAAA,CAAQ,eAAe,KAAKA,OAAAA,CAAQ,UAAU;aACpD,IACE,WAARL,KACQ,YAARA,KACQ,UAARA,KACQ,UAARA,KACQ,UAARA,KAGQ,cAARA,KACQ,cAARA,KACQ,aAARA,KACQ,aAARA,KACQ,UAARA,KACQ,aAARA,KACAA,KAAQD,GAER,IAAA;YACCA,CAAAA,CAAIC,EAAAA,GAAiB,QAATJ,IAAgB,KAAKA;YAEjC,MAAMO;QACK,EAAV,OAAOQ,GAAAA,CAAG;QAUO,cAAA,OAATf,KAAAA,CAES,QAATA,KAAAA,CAA4B,MAAVA,KAA+B,QAAZI,CAAAA,CAAK,EAAA,GAGpDD,EAAIa,eAAAA,CAAgBZ,KAFpBD,EAAIc,YAAAA,CAAab,GAAc,aAARA,KAA8B,KAATJ,IAAgB,KAAKA,EAAAA;IAInE;AACD;AAOA,SAASkB,EAAiBZ,CAAAA;IAMzB,OAAiBS,SAAAA,CAAAA;QAChB,IAAI/E,IAAAA,CAAI2E,CAAAA,EAAa;YACpB,IAAMQ,IAAenF,IAAAA,CAAI2E,CAAAA,CAAYI,EAAE3G,IAAAA,GAAOkG,EAAAA;YAC9C,IAAqB,QAAjBS,EAAEK,CAAAA,EACLL,EAAEK,CAAAA,GAAcjI;iBAKN4H,IAAAA,EAAEK,CAAAA,GAAcD,EAAaP,CAAAA,EACvC;YAED,OAAOO,EAAavI,EAAQyI,KAAAA,GAAQzI,EAAQyI,KAAAA,CAAMN,KAAKA;QACxD;IACD;AACD;AG5IgB,SAAAxD,EACfK,CAAAA,EACAb,CAAAA,EACAC,CAAAA,EACAgB,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAhB,CAAAA,EACAD,CAAAA,EACAkB,CAAAA,EACAhB,CAAAA;IAVe,IAaXmE,GAkBE9E,GAAG+E,GAAOC,GAAUC,GAAUC,GAAUC,GACxCC,GACEC,GAMFC,GACAC,GAyGOzI,GA4BP0I,GACHC,GASS3I,GA6BNuE,GAtMLqE,IAAUnF,EAAS3C,IAAAA;IAIpB,IAAA,KAA6BS,MAAzBkC,EAASxB,WAAAA,EAA2B,OAAW;IR9CtB,MQiDzByB,EAAQtB,GAAAA,IAAAA,CACXyC,IAAAA,CAAAA,CAAAA,CRpD0B,KQoDTnB,EAAQtB,GAAAA,GAEzBwC,IAAoB;QADpBjB,IAASF,EAAQ3B,GAAAA,GAAQ4B,EAAQ5B,GAAAA;KAAAA,GAAAA,CAI7BkG,IAAM1I,EAAOuC,GAAAA,KAASmG,EAAIvE;IAE/BoF,GAAO,IAAsB,cAAA,OAAXD,GACjB,IAAA;QAkEC,IAhEIN,IAAW7E,EAASjD,KAAAA,EAClB+H,IACL,eAAeK,KAAWA,EAAQE,SAAAA,CAAUC,MAAAA,EAKzCP,IAAAA,CADJR,IAAMY,EAAQI,WAAAA,KACQtE,CAAAA,CAAcsD,EAAGhG,GAAAA,CAAAA,EACnCyG,IAAmBT,IACpBQ,IACCA,EAAShI,KAAAA,CAAMkG,KAAAA,GACfsB,EAAGpG,EAAAA,GACJ8C,GAGChB,EAAQ1B,GAAAA,GAEXqG,IAAAA,CADAnF,IAAIO,EAAQzB,GAAAA,GAAc0B,EAAQ1B,GAAAA,EACNJ,EAAAA,GAAwBsB,EAAC+F,GAAAA,GAAAA,CAGjDV,IAEH9E,EAAQzB,GAAAA,GAAckB,IAAI,IAAI0F,EAAQN,GAAUG,KAAAA,CAGhDhF,EAAQzB,GAAAA,GAAckB,IAAI,IAAIV,EAC7B8F,GACAG,IAEDvF,EAAEjB,WAAAA,GAAc2G,GAChB1F,EAAE6F,MAAAA,GAASG,CAAAA,GAERV,KAAUA,EAASW,GAAAA,CAAIjG,IAE3BA,EAAE1C,KAAAA,GAAQ8H,GACLpF,EAAEkG,KAAAA,IAAAA,CAAOlG,EAAEkG,KAAAA,GAAQ,CAAE,CAAA,GAC1BlG,EAAET,OAAAA,GAAUgG,GACZvF,EAACgB,GAAAA,GAAkBQ,GACnBuD,IAAQ/E,EAACnB,GAAAA,GAAAA,CAAU,GACnBmB,EAACmG,GAAAA,GAAoB,EAAA,EACrBnG,EAACoG,GAAAA,GAAmB,EAAA,GAIjBf,KAAoC,QAAhBrF,EAACqG,GAAAA,IAAAA,CACxBrG,EAACqG,GAAAA,GAAcrG,EAAEkG,KAAAA,GAGdb,KAAwD,QAApCK,EAAQY,wBAAAA,IAAAA,CAC3BtG,EAACqG,GAAAA,IAAerG,EAAEkG,KAAAA,IAAAA,CACrBlG,EAACqG,GAAAA,GAAcjJ,EAAO,CAAA,GAAI4C,EAACqG,GAAAA,CAAAA,GAG5BjJ,EACC4C,EAACqG,GAAAA,EACDX,EAAQY,wBAAAA,CAAyBlB,GAAUpF,EAACqG,GAAAA,EAAAA,GAI9CrB,IAAWhF,EAAE1C,KAAAA,EACb2H,IAAWjF,EAAEkG,KAAAA,EACblG,EAAChB,GAAAA,GAAUuB,GAGPwE,GAEFM,KACoC,QAApCK,EAAQY,wBAAAA,IACgB,QAAxBtG,EAAEuG,kBAAAA,IAEFvG,EAAEuG,kBAAAA,IAGClB,KAA2C,QAAvBrF,EAAEwG,iBAAAA,IACzBxG,EAACmG,GAAAA,CAAkBlG,IAAAA,CAAKD,EAAEwG,iBAAAA;aAErB;YAUN,IARCnB,KACoC,QAApCK,EAAQY,wBAAAA,IACRlB,MAAaJ,KACkB,QAA/BhF,EAAEyG,yBAAAA,IAEFzG,EAAEyG,yBAAAA,CAA0BrB,GAAUG,IAAAA,CAIrCvF,EAACpB,GAAAA,IAAAA,CAC2B,QAA3BoB,EAAE0G,qBAAAA,IAAAA,CAKG,MAJN1G,EAAE0G,qBAAAA,CACDtB,GACApF,EAACqG,GAAAA,EACDd,MAEDhF,EAAQvB,GAAAA,KAAewB,EAAQxB,GAAAA,GAC/B;gBAkBD,IAhBIuB,EAAQvB,GAAAA,KAAewB,EAAQxB,GAAAA,IAAAA,CAKlCgB,EAAE1C,KAAAA,GAAQ8H,GACVpF,EAAEkG,KAAAA,GAAQlG,EAACqG,GAAAA,EACXrG,EAACnB,GAAAA,GAAAA,CAAU,CAAA,GAGZ0B,EAAQ3B,GAAAA,GAAQ4B,EAAQ5B,GAAAA,EACxB2B,EAAQ9B,GAAAA,GAAa+B,EAAQ/B,GAAAA,EAC7B8B,EAAQ9B,GAAAA,CAAW0E,IAAAA,CAAK,SAAA3E,CAAAA;oBACnBA,KAAAA,CAAOA,EAAKE,EAAAA,GAAW6B,CAAAA;gBAC5B,IAESzD,IAAI,GAAGA,IAAIkD,EAACoG,GAAAA,CAAiBlI,MAAAA,EAAQpB,IAC7CkD,EAACmG,GAAAA,CAAkBlG,IAAAA,CAAKD,EAACoG,GAAAA,CAAiBtJ,EAAAA;gBAE3CkD,EAACoG,GAAAA,GAAmB,EAAA,EAEhBpG,EAACmG,GAAAA,CAAkBjI,MAAAA,IACtBwC,EAAYT,IAAAA,CAAKD;gBAGlB,MAAM2F;YACP;YAE6B,QAAzB3F,EAAE2G,mBAAAA,IACL3G,EAAE2G,mBAAAA,CAAoBvB,GAAUpF,EAACqG,GAAAA,EAAad,IAG3CF,KAA4C,QAAxBrF,EAAE4G,kBAAAA,IACzB5G,EAACmG,GAAAA,CAAkBlG,IAAAA,CAAK;gBACvBD,EAAE4G,kBAAAA,CAAmB5B,GAAUC,GAAUC;YAC1C;QAEF;QASA,IAPAlF,EAAET,OAAAA,GAAUgG,GACZvF,EAAE1C,KAAAA,GAAQ8H,GACVpF,EAACc,GAAAA,GAAcM,GACfpB,EAACpB,GAAAA,GAAAA,CAAU,GAEP4G,IAAapJ,EAAO+D,GAAAA,EACvBsF,IAAQ,GACLJ,GAAkB;YAQrB,IAPArF,EAAEkG,KAAAA,GAAQlG,EAACqG,GAAAA,EACXrG,EAACnB,GAAAA,GAAAA,CAAU,GAEP2G,KAAYA,EAAWjF,IAE3BuE,IAAM9E,EAAE6F,MAAAA,CAAO7F,EAAE1C,KAAAA,EAAO0C,EAAEkG,KAAAA,EAAOlG,EAAET,OAAAA,GAE1BzC,IAAI,GAAGA,IAAIkD,EAACoG,GAAAA,CAAiBlI,MAAAA,EAAQpB,IAC7CkD,EAACmG,GAAAA,CAAkBlG,IAAAA,CAAKD,EAACoG,GAAAA,CAAiBtJ,EAAAA;YAE3CkD,EAACoG,GAAAA,GAAmB;QACrB,OACC,GAAA;YACCpG,EAACnB,GAAAA,GAAAA,CAAU,GACP2G,KAAYA,EAAWjF,IAE3BuE,IAAM9E,EAAE6F,MAAAA,CAAO7F,EAAE1C,KAAAA,EAAO0C,EAAEkG,KAAAA,EAAOlG,EAAET,OAAAA,GAGnCS,EAAEkG,KAAAA,GAAQlG,EAACqG,GAAAA;QAAAA,QACHrG,EAACnB,GAAAA,IAAAA,EAAa4G,IAAQ;QAIhCzF,EAAEkG,KAAAA,GAAQlG,EAACqG,GAAAA,EAEc,QAArBrG,EAAE6G,eAAAA,IAAAA,CACLrF,IAAgBpE,EAAOA,EAAO,CAAA,GAAIoE,IAAgBxB,EAAE6G,eAAAA,GAAAA,GAGjDxB,KAAAA,CAAqBN,KAAsC,QAA7B/E,EAAE8G,uBAAAA,IAAAA,CACnC5B,IAAWlF,EAAE8G,uBAAAA,CAAwB9B,GAAUC,EAAAA,GAOhD9D,EACCC,GACAlE,EAJGmE,IADI,QAAPyD,KAAeA,EAAIlH,IAAAA,KAASyB,KAAuB,QAAXyF,EAAIhH,GAAAA,GACLgH,EAAIxH,KAAAA,CAAMO,QAAAA,GAAWiH,KAIpCzD,IAAe;YAACA;SAAAA,EACxCd,GACAC,GACAgB,GACAC,GACAC,GACAhB,GACAD,GACAkB,GACAhB,IAGDX,EAAEF,IAAAA,GAAOS,EAAQ3B,GAAAA,EAGjB2B,EAAQrB,GAAAA,IAAAA,CR5Pe,KQ8PnBc,EAACmG,GAAAA,CAAkBjI,MAAAA,IACtBwC,EAAYT,IAAAA,CAAKD,IAGdmF,KAAAA,CACHnF,EAAC+F,GAAAA,GAAiB/F,EAACtB,EAAAA,GAAwB,IAAA;IAoB7C,EAlBE,OAAO6F,GAAAA;QAGR,IAFAhE,EAAQvB,GAAAA,GAAa,MAEjB2C,KAAoC,QAArBD,GAA2B;YAK7C,IAJAnB,EAAQrB,GAAAA,IAAWyC,IAChBoF,MRjRuB,KQoRnBtG,KAA8B,MAApBA,EAAOuC,QAAAA,IAAkBvC,EAAO2B,WAAAA,EAChD3B,IAASA,EAAO2B,WAAAA;YAEjBV,CAAAA,CAAkBA,EAAkBsF,OAAAA,CAAQvG,GAAAA,GAAW,MACvDF,EAAQ3B,GAAAA,GAAQ6B;QACjB,OACCF,EAAQ3B,GAAAA,GAAQ4B,EAAQ5B,GAAAA,EACxB2B,EAAQ9B,GAAAA,GAAa+B,EAAQ/B,GAAAA;QAE9BrC,EAAOwC,GAAAA,CAAa2F,GAAGhE,GAAUC;IAClC;SAEqB,QAArBkB,KACAnB,EAAQvB,GAAAA,KAAewB,EAAQxB,GAAAA,GAAAA,CAE/BuB,EAAQ9B,GAAAA,GAAa+B,EAAQ/B,GAAAA,EAC7B8B,EAAQ3B,GAAAA,GAAQ4B,EAAQ5B,GAAAA,IAExB2B,EAAQ3B,GAAAA,GAAQqI,EACfzG,EAAQ5B,GAAAA,EACR2B,GACAC,GACAgB,GACAC,GACAC,GACAhB,GACAiB,GACAhB;IAAAA,CAIGmE,IAAM1I,EAAQ8K,MAAAA,KAASpC,EAAIvE;AACjC;AAOgB,SAAAW,EAAWR,CAAAA,EAAayG,CAAAA,EAAMxG,CAAAA;IAC7CwG,EAAItI,GAAAA,GAAAA,KAAYR;IAEhB,IAAK,IAAIvB,IAAI,GAAGA,IAAI6D,EAASzC,MAAAA,EAAQpB,IACpCoF,EAASvB,CAAAA,CAAS7D,EAAAA,EAAI6D,CAAAA,CAAAA,EAAW7D,EAAAA,EAAI6D,CAAAA,CAAAA,EAAW7D,EAAAA;IAG7CV,EAAO0C,GAAAA,IAAU1C,EAAO0C,GAAAA,CAASqI,GAAMzG,IAE3CA,EAAYyC,IAAAA,CAAK,SAAAnD,CAAAA;QAChB,IAAA;YAECU,IAAcV,EAACmG,GAAAA,EACfnG,EAACmG,GAAAA,GAAoB,EAAA,EACrBzF,EAAYyC,IAAAA,CAAK,SAAAiE,CAAAA;gBAEhBA,EAAGjJ,IAAAA,CAAK6B;YACT;QAGD,EAFE,OAAOuE,GAAAA;YACRnI,EAAOwC,GAAAA,CAAa2F,GAAGvE,EAAChB,GAAAA;QACzB;IACD;AACD;AAiBA,SAASiI,EACRtD,CAAAA,EACApD,CAAAA,EACAC,CAAAA,EACAgB,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAhB,CAAAA,EACAiB,CAAAA,EACAhB,CAAAA;IATD,IAeK7D,GAEAuK,GAEAC,GAEAC,GACA/D,GACAgE,GACAC,GAbAzC,IAAWxE,EAASlD,KAAAA,EACpB8H,IAAW7E,EAASjD,KAAAA,EACpB0F,IAAkCzC,EAAS3C,IAAAA;IAmB/C,IALiB,UAAboF,IAAoBvB,IAAY,+BACd,WAAbuB,IACRvB,IAAY,uCACHA,KAAAA,CAAWA,IAAY,8BAAA,GAER,QAArBC;QACH,IAAK5E,IAAI,GAAGA,IAAI4E,EAAkBxD,MAAAA,EAAQpB,IAMzC,IAAA,CALA0G,IAAQ9B,CAAAA,CAAkB5E,EAAAA,KAOzB,kBAAkB0G,KAAAA,CAAAA,CAAYR,KAAAA,CAC7BA,IAAWQ,EAAMkE,SAAAA,KAAc1E,IAA8B,MAAnBQ,EAAMR,QAAAA,GAChD;YACDW,IAAMH,GACN9B,CAAAA,CAAkB5E,EAAAA,GAAK;YACvB;;IACD;IAIF,IAAW,QAAP6G,GAAa;QAChB,IAAiB,SAAbX,GACH,OAAO2E,SAASC,cAAAA,CAAexC;QAGhCzB,IAAMgE,SAASE,eAAAA,CACdpG,GACAuB,GACAoC,EAAS0C,EAAAA,IAAM1C,IAKZzD,KAAAA,CACCvF,EAAO2L,GAAAA,IACV3L,EAAO2L,GAAAA,CAAoBxH,GAAUmB,IACtCC,IAAAA,CAAc,CAAA,GAGfD,IAAoB;IACrB;IAEA,IAAiB,SAAbsB,GAECgC,MAAaI,KAAczD,KAAegC,EAAIqE,IAAAA,KAAS5C,KAAAA,CAC1DzB,EAAIqE,IAAAA,GAAO5C,CAAAA;SAEN;QASN,IAPA1D,IAAoBA,KAAqBvF,EAAMgC,IAAAA,CAAKwF,EAAIsE,UAAAA,GAExDjD,IAAWxE,EAASlD,KAAAA,IAASP,GAAAA,CAKxB4E,KAAoC,QAArBD,GAEnB,IADAsD,IAAW,CAAE,GACRlI,IAAI,GAAGA,IAAI6G,EAAIuE,UAAAA,CAAWhK,MAAAA,EAAQpB,IAEtCkI,CAAAA,CAAAA,CADAxB,IAAQG,EAAIuE,UAAAA,CAAWpL,EAAAA,EACR8G,IAAAA,CAAAA,GAAQJ,EAAMA,KAAAA;QAI/B,IAAK1G,KAAKkI,EAET,IADAxB,IAAQwB,CAAAA,CAASlI,EAAAA,EACR,cAALA;aACG,IAAS,6BAALA,GACVwK,IAAU9D;aACA,IAAA,CAAA,CAAE1G,KAAKsI,CAAAA,GAAW;YAC5B,IACO,WAALtI,KAAgB,kBAAkBsI,KAC7B,aAALtI,KAAkB,oBAAoBsI,GAEvC;YAED3B,EAAYE,GAAK7G,GAAG,MAAM0G,GAAO/B;QAClC;QAKD,IAAK3E,KAAKsI,EACT5B,IAAQ4B,CAAAA,CAAStI,EAAAA,EACR,cAALA,IACHyK,IAAc/D,IACC,6BAAL1G,IACVuK,IAAU7D,IACK,WAAL1G,IACV0K,IAAahE,IACE,aAAL1G,IACV2K,IAAUjE,IAER7B,KAA+B,cAAA,OAAT6B,KACxBwB,CAAAA,CAASlI,EAAAA,KAAO0G,KAEhBC,EAAYE,GAAK7G,GAAG0G,GAAOwB,CAAAA,CAASlI,EAAAA,EAAI2E;QAK1C,IAAI4F,GAGD1F,KACC2F,KAAAA,CACAD,EAAOc,MAAAA,KAAYb,EAAOa,MAAAA,IAC1Bd,EAAOc,MAAAA,KAAYxE,EAAIyE,SAAAA,KAAAA,CAEzBzE,EAAIyE,SAAAA,GAAYf,EAAOc,MAAAA,GAGxB5H,EAAQ9B,GAAAA,GAAa,EAAA;aAuBrB,IArBI6I,KAAAA,CAAS3D,EAAIyE,SAAAA,GAAY,EAAA,GAE7BjH,EACCwC,GACAzG,EAAQqK,KAAeA,IAAc;YAACA;SAAAA,EACtChH,GACAC,GACAgB,GACa,oBAAbwB,IACG,iCACAvB,GACHC,GACAhB,GACAgB,IACGA,CAAAA,CAAkB,EAAA,GAClBlB,EAAQ/B,GAAAA,IAAcgB,EAAce,GAAU,IACjDmB,GACAhB,IAIwB,QAArBe,GACH,IAAK5E,IAAI4E,EAAkBxD,MAAAA,EAAQpB,KAClCS,EAAWmE,CAAAA,CAAkB5E,EAAAA;QAM3B6E,KAAAA,CACJ7E,IAAI,SACa,eAAbkG,KAAyC,QAAdwE,IAC9B7D,EAAIa,eAAAA,CAAgB,WAAA,KAELnG,MAAfmJ,KAAAA,CAKCA,MAAe7D,CAAAA,CAAI7G,EAAAA,IACL,eAAbkG,KAAAA,CAA4BwE,KAIf,aAAbxE,KAAyBwE,MAAexC,CAAAA,CAASlI,EAAAA,KAEnD2G,EAAYE,GAAK7G,GAAG0K,GAAYxC,CAAAA,CAASlI,EAAAA,EAAI2E,IAG9C3E,IAAI,WAAA,KACYuB,MAAZoJ,KAAyBA,MAAY9D,CAAAA,CAAI7G,EAAAA,IAC5C2G,EAAYE,GAAK7G,GAAG2K,GAASzC,CAAAA,CAASlI,EAAAA,EAAI2E,EAAAA;IAG7C;IAEA,OAAOkC;AACR;AAQgB,SAAAzB,EAASnE,CAAAA,EAAKyF,CAAAA,EAAOhF,CAAAA;IACpC,IAAA;QACC,IAAkB,cAAA,OAAPT,GAAmB;YAC7B,IAAIsK,IAAuC,cAAA,OAAhBtK,EAAGmB,GAAAA;YAC1BmJ,KAEHtK,EAAGmB,GAAAA,IAGCmJ,KAA0B,QAAT7E,KAAAA,CAIrBzF,EAAGmB,GAAAA,GAAYnB,EAAIyF,EAAAA;QAErB,OAAOzF,EAAIqB,OAAAA,GAAUoE;IAGtB,EAFE,OAAOe,GAAAA;QACRnI,EAAOwC,GAAAA,CAAa2F,GAAG/F;IACxB;AACD;AASgB,SAAAoE,EAAQpE,CAAAA,EAAOqE,CAAAA,EAAayF,CAAAA;IAA5B,IACXC,GAsBMzL;IAbV,IARIV,EAAQwG,OAAAA,IAASxG,EAAQwG,OAAAA,CAAQpE,IAAAA,CAEhC+J,IAAI/J,EAAMT,GAAAA,KAAAA,CACTwK,EAAEnJ,OAAAA,IAAWmJ,EAAEnJ,OAAAA,KAAYZ,EAAKI,GAAAA,IACpCsD,EAASqG,GAAG,MAAM1F,EAAAA,GAIU,QAAA,CAAzB0F,IAAI/J,EAAKM,GAAAA,GAAsB;QACnC,IAAIyJ,EAAEC,oBAAAA,EACL,IAAA;YACCD,EAAEC,oBAAAA;QAGH,EAFE,OAAOjE,GAAAA;YACRnI,EAAOwC,GAAAA,CAAa2F,GAAG1B;QACxB;QAGD0F,EAAEzI,IAAAA,GAAOyI,EAACzH,GAAAA,GAAc;IACzB;IAEA,IAAKyH,IAAI/J,EAAKC,GAAAA,EACb,IAAS3B,IAAI,GAAGA,IAAIyL,EAAErK,MAAAA,EAAQpB,IACzByL,CAAAA,CAAEzL,EAAAA,IACL8F,EACC2F,CAAAA,CAAEzL,EAAAA,EACF+F,GACAyF,KAAmC,cAAA,OAAd9J,EAAMZ,IAAAA;IAM1B0K,KACJ/K,EAAWiB,EAAKI,GAAAA,GAKjBJ,EAAKM,GAAAA,GAAcN,EAAKE,EAAAA,GAAWF,EAAKI,GAAAA,GAAQJ,EAAKK,GAAAA,GAAAA,KAAYR;AAClE;AAGA,SAAS2H,EAAS1I,CAAAA,EAAO4I,CAAAA,EAAO3G,CAAAA;IAC/B,OAAOC,IAAAA,CAAKT,WAAAA,CAAYzB,GAAOiC;AAChC;AAAA,SCpnBgBsG,EAAOrH,CAAAA,EAAO4C,CAAAA,EAAWqH,CAAAA;IAAAA,IAMpC9G,GAOAnB,GAQAE,GACHC;IArBGvE,EAAOsC,EAAAA,IAAQtC,EAAOsC,EAAAA,CAAOF,GAAO4C,IAYpCZ,IAAAA,CAPAmB,IAAoC,cAAA,OAAf8G,CAAAA,IAQtB,OACCA,KAAeA,EAAWhK,GAAAA,IAAe2C,EAAS3C,GAAAA,EAMlDiC,IAAc,EAAA,EACjBC,IAAW,EAAA,EACZI,EACCK,GAPD5C,IAAAA,CAAAA,CAAWmD,KAAe8G,KAAgBrH,CAAAA,EAAS3C,GAAAA,GAClDd,EAAc0B,GAAU,MAAM;QAACb;KAAAA,GAU/BgC,KAAYzD,GACZA,GACAqE,EAAUH,YAAAA,EAAAA,CACTU,KAAe8G,IACb;QAACA;KAAAA,GACDjI,IACC,OACAY,EAAUsH,UAAAA,GACTvM,EAAMgC,IAAAA,CAAKiD,EAAU6G,UAAAA,IACrB,MACLvH,GAAAA,CACCiB,KAAe8G,IACbA,IACAjI,IACCA,EAAQ5B,GAAAA,GACRwC,EAAUsH,UAAAA,EACd/G,GACAhB,IAIDO,EAAWR,GAAalC,GAAOmC;AAChC;AAOgB,SAAAgI,EAAQnK,CAAAA,EAAO4C,CAAAA;IAC9ByE,EAAOrH,GAAO4C,GAAWuH;AAC1B;AC5DO,SAASC,EAAapK,CAAAA,EAAOlB,CAAAA,EAAOO,CAAAA;IAApC,IAELC,GACAC,GACAjB,GAEGsB,GALAJ,IAAkBZ,EAAO,CAAE,GAAEoB,EAAMlB,KAAAA;IAWvC,IAAKR,KAJD0B,EAAMZ,IAAAA,IAAQY,EAAMZ,IAAAA,CAAKQ,YAAAA,IAAAA,CAC5BA,IAAeI,EAAMZ,IAAAA,CAAKQ,YAAAA,GAGjBd,EACA,SAALR,IAAYgB,IAAMR,CAAAA,CAAMR,EAAAA,GACd,SAALA,IAAYiB,IAAMT,CAAAA,CAAMR,EAAAA,GAEhCkB,CAAAA,CAAgBlB,EAAAA,GAAAA,KADKuB,MAAbf,CAAAA,CAAMR,EAAAA,IAAAA,KAAqCuB,MAAjBD,IACbA,CAAAA,CAAatB,EAAAA,GAEbQ,CAAAA,CAAMR,EAAAA;IAS7B,OALImB,UAAUC,MAAAA,GAAS,KAAA,CACtBF,EAAgBH,QAAAA,GACfI,UAAUC,MAAAA,GAAS,IAAI/B,EAAMgC,IAAAA,CAAKF,WAAW,KAAKJ,CAAAA,GAG7CS,EACNE,EAAMZ,IAAAA,EACNI,GACAF,KAAOU,EAAMV,GAAAA,EACbC,KAAOS,EAAMT,GAAAA,EACb;AAEF;AAAA,SJ1CgB8K,EAAcC,CAAAA,EAAcC,CAAAA;IAG3C,IAAMxJ,IAAU;QACfT,KAHDiK,IAAY,SAASjM;QAIpB4B,IAAeoK;QAEfE,UAAQA,SAAC1L,CAAAA,EAAO2L,CAAAA;YAIf,OAAO3L,EAAMO,QAAAA,CAASoL;QACvB;QAEAC,UAAQ,SAAC5L,CAAAA;YAAD,IAGF6L,GACAC;YA8BL,OAjCK5J,IAAAA,CAAKqH,eAAAA,IAAAA,CAELsC,IAAO,IAAIE,KAAAA,CACXD,IAAM,CAAA,CAAA,CAAA,CACNL,EAAAA,GAAavJ,IAAAA,EAEjBA,IAAAA,CAAKqH,eAAAA,GAAkB;gBAAM,OAAAuC;YAAG,GAEhC5J,IAAAA,CAAKgJ,oBAAAA,GAAuB;gBAC3BW,IAAO;YACR,GAEA3J,IAAAA,CAAKkH,qBAAAA,GAAwB,SAAU4C,CAAAA;gBAClC9J,IAAAA,CAAKlC,KAAAA,CAAMkG,KAAAA,KAAU8F,EAAO9F,KAAAA,IAC/B2F,EAAKI,OAAAA,CAAQ,SAAAvJ,CAAAA;oBACZA,EAACpB,GAAAA,GAAAA,CAAU,GACXmB,EAAcC;gBACf;YAEF,GAEAR,IAAAA,CAAKyG,GAAAA,GAAM,SAAAjG,CAAAA;gBACVmJ,EAAKK,GAAAA,CAAIxJ;gBACT,IAAIyJ,IAAMzJ,EAAEwI,oBAAAA;gBACZxI,EAAEwI,oBAAAA,GAAuB;oBACpBW,KACHA,EAAKO,MAAAA,CAAO1J,IAETyJ,KAAKA,EAAItL,IAAAA,CAAK6B;gBACnB;YACD,CAAA,GAGM1C,EAAMO;QACd;IAAA;IASD,OAAQ0B,EAAQ2J,QAAAA,CAAQxK,EAAAA,GAAea,EAAQyJ,QAAAA,CAASlD,WAAAA,GACvDvG;AACF;ALrCapD,IAAQa,EAAUb,KAAAA,EChBzBC,IAAU;IACfwC,KSHe,SAAY+K,CAAAA,EAAOnL,CAAAA,EAAOgC,CAAAA,EAAUoJ,CAAAA;QAQnD,IANA,IAAItJ,GAEHuJ,GAEAC,GAEOtL,IAAQA,EAAKE,EAAAA,EACpB,IAAA,CAAK4B,IAAY9B,EAAKM,GAAAA,KAAAA,CAAiBwB,EAAS5B,EAAAA,EAC/C,IAAA;YAcC,IAAA,CAbAmL,IAAOvJ,EAAUvB,WAAAA,KAE4B,QAAjC8K,EAAKE,wBAAAA,IAAAA,CAChBzJ,EAAU0J,QAAAA,CAASH,EAAKE,wBAAAA,CAAyBJ,KACjDG,IAAUxJ,EAASzB,GAAAA,GAGe,QAA/ByB,EAAU2J,iBAAAA,IAAAA,CACb3J,EAAU2J,iBAAAA,CAAkBN,GAAOC,KAAa,CAAE,IAClDE,IAAUxJ,EAASzB,GAAAA,GAIhBiL,GACH,OAAQxJ,EAASyF,GAAAA,GAAiBzF;QAIpC,EAFE,OAAOiE,GAAAA;YACRoF,IAAQpF;QACT;QAIF,MAAMoF;IACP;AAAA,GRxCItN,IAAU,GAgGDC,IAAiB,SAAAkC,CAAAA;IAAK,OACzB,QAATA,KAAsCH,QAArBG,EAAMO;AAAwB,GCzEhDO,EAAcsG,SAAAA,CAAUoE,QAAAA,GAAW,SAAUE,CAAAA,EAAQC,CAAAA;IAEpD,IAAIC;IAEHA,IADsB,QAAnB5K,IAAAA,CAAI6G,GAAAA,IAAuB7G,IAAAA,CAAI6G,GAAAA,KAAgB7G,IAAAA,CAAK0G,KAAAA,GACnD1G,IAAAA,CAAI6G,GAAAA,GAEJ7G,IAAAA,CAAI6G,GAAAA,GAAcjJ,EAAO,CAAE,GAAEoC,IAAAA,CAAK0G,KAAAA,GAGlB,cAAA,OAAVgE,KAAAA,CAGVA,IAASA,EAAO9M,EAAO,CAAA,GAAIgN,IAAI5K,IAAAA,CAAKlC,KAAAA,CAAAA,GAGjC4M,KACH9M,EAAOgN,GAAGF,IAIG,QAAVA,KAEA1K,IAAAA,CAAIR,GAAAA,IAAAA,CACHmL,KACH3K,IAAAA,CAAI4G,GAAAA,CAAiBnG,IAAAA,CAAKkK,IAE3BpK,EAAcP,IAAAA,CAAAA;AAEhB,GAQAF,EAAcsG,SAAAA,CAAUyE,WAAAA,GAAc,SAAUF,CAAAA;IAC3C3K,IAAAA,CAAIR,GAAAA,IAAAA,CAIPQ,IAAAA,CAAIZ,GAAAA,GAAAA,CAAU,GACVuL,KAAU3K,IAAAA,CAAI2G,GAAAA,CAAkBlG,IAAAA,CAAKkK,IACzCpK,EAAcP,IAAAA,CAAAA;AAEhB,GAYAF,EAAcsG,SAAAA,CAAUC,MAAAA,GAASxG,GA8F7B9C,IAAgB,EAAA,EAadE,IACa,cAAA,OAAX6N,UACJA,QAAQ1E,SAAAA,CAAU2E,IAAAA,CAAKC,IAAAA,CAAKF,QAAQG,OAAAA,MACpCC,YAuBEhO,IAAY,SAACiO,CAAAA,EAAGC,CAAAA;IAAM,OAAAD,EAAC3L,GAAAA,CAAAL,GAAAA,GAAiBiM,EAAC5L,GAAAA,CAAAL;AAAc,GAuB7DuB,EAAOC,GAAAA,GAAkB,GCtNrBxD,IAAa,GAmJXC,IAAa8H,EAAAA,CAAiB,IAC9B7H,IAAoB6H,EAAAA,CAAiB,IC5KhC5H,IAAI", "debugId": null}}, {"offset": {"line": 3469, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/preact@10.24.3/node_modules/preact/jsx-runtime/dist/jsxRuntime.module.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/jsx-runtime/src/utils.js", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/src/constants.js", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact%4010.24.3/node_modules/preact/jsx-runtime/src/index.js"], "sourcesContent": ["const ENCODED_ENTITIES = /[\"&<]/;\n\n/** @param {string} str */\nexport function encodeEntities(str) {\n\t// Skip all work for strings with no entities needing encoding:\n\tif (str.length === 0 || ENCODED_ENTITIES.test(str) === false) return str;\n\n\tlet last = 0,\n\t\ti = 0,\n\t\tout = '',\n\t\tch = '';\n\n\t// Seek forward in str until the next entity char:\n\tfor (; i < str.length; i++) {\n\t\tswitch (str.charCodeAt(i)) {\n\t\t\tcase 34:\n\t\t\t\tch = '&quot;';\n\t\t\t\tbreak;\n\t\t\tcase 38:\n\t\t\t\tch = '&amp;';\n\t\t\t\tbreak;\n\t\t\tcase 60:\n\t\t\t\tch = '&lt;';\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tcontinue;\n\t\t}\n\t\t// Append skipped/buffered characters and the encoded entity:\n\t\tif (i !== last) out += str.slice(last, i);\n\t\tout += ch;\n\t\t// Start the next seek/buffer after the entity's offset:\n\t\tlast = i + 1;\n\t}\n\tif (i !== last) out += str.slice(last, i);\n\treturn out;\n}\n", "/** Normal hydration that attaches to a DOM tree but does not diff it. */\nexport const MODE_HYDRATE = 1 << 5;\n/** Signifies this VNode suspended on the previous render */\nexport const MODE_SUSPENDED = 1 << 7;\n/** Indicates that this node needs to be inserted while patching children */\nexport const INSERT_VNODE = 1 << 16;\n/** Indicates a VNode has been matched with another VNode in the diff */\nexport const MATCHED = 1 << 17;\n\n/** Reset all mode flags */\nexport const RESET_MODE = ~(MODE_HYDRATE | MODE_SUSPENDED);\n\nexport const EMPTY_OBJ = /** @type {any} */ ({});\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL =\n\t/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { options, Fragment } from 'preact';\nimport { encodeEntities } from './utils';\nimport { IS_NON_DIMENSIONAL } from '../../src/constants';\n\nlet vnodeId = 0;\n\nconst isArray = Array.isArray;\n\n/**\n * @fileoverview\n * This file exports various methods that implement Babel's \"automatic\" JSX runtime API:\n * - jsx(type, props, key)\n * - jsxs(type, props, key)\n * - jsxDEV(type, props, key, __source, __self)\n *\n * The implementation of createVNode here is optimized for performance.\n * Benchmarks: https://esbench.com/bench/5f6b54a0b4632100a7dcd2b3\n */\n\n/**\n * JSX.Element factory used by Babel's {runtime:\"automatic\"} JSX transform\n * @param {VNode['type']} type\n * @param {VNode['props']} props\n * @param {VNode['key']} [key]\n * @param {unknown} [isStaticChildren]\n * @param {unknown} [__source]\n * @param {unknown} [__self]\n */\nfunction createVNode(type, props, key, isStaticChildren, __source, __self) {\n\tif (!props) props = {};\n\t// We'll want to preserve `ref` in props to get rid of the need for\n\t// forwardRef components in the future, but that should happen via\n\t// a separate PR.\n\tlet normalizedProps = props,\n\t\tref,\n\t\ti;\n\n\tif ('ref' in props) {\n\t\tref = props.ref;\n\t\tdelete props.ref;\n\t}\n\n\t/** @type {VNode & { __source: any; __self: any }} */\n\tconst vnode = {\n\t\ttype,\n\t\tprops: normalizedProps,\n\t\tkey,\n\t\tref,\n\t\t_children: null,\n\t\t_parent: null,\n\t\t_depth: 0,\n\t\t_dom: null,\n\t\t_nextDom: undefined,\n\t\t_component: null,\n\t\tconstructor: undefined,\n\t\t_original: --vnodeId,\n\t\t_index: -1,\n\t\t_flags: 0,\n\t\t__source,\n\t\t__self\n\t};\n\n\t// If a Component VNode, check for and apply defaultProps.\n\t// Note: `type` is often a String, and can be `undefined` in development.\n\tif (typeof type === 'function' && (ref = type.defaultProps)) {\n\t\tfor (i in ref)\n\t\t\tif (typeof normalizedProps[i] === 'undefined') {\n\t\t\t\tnormalizedProps[i] = ref[i];\n\t\t\t}\n\t}\n\n\tif (options.vnode) options.vnode(vnode);\n\treturn vnode;\n}\n\n/**\n * Create a template vnode. This function is not expected to be\n * used directly, but rather through a precompile JSX transform\n * @param {string[]} templates\n * @param  {Array<string | null | VNode>} exprs\n * @returns {VNode}\n */\nfunction jsxTemplate(templates, ...exprs) {\n\tconst vnode = createVNode(Fragment, { tpl: templates, exprs });\n\t// Bypass render to string top level Fragment optimization\n\tvnode.key = vnode._vnode;\n\treturn vnode;\n}\n\nconst JS_TO_CSS = {};\nconst CSS_REGEX = /[A-Z]/g;\n\n/**\n * Serialize an HTML attribute to a string. This function is not\n * expected to be used directly, but rather through a precompile\n * JSX transform\n * @param {string} name The attribute name\n * @param {*} value The attribute value\n * @returns {string}\n */\nfunction jsxAttr(name, value) {\n\tif (options.attr) {\n\t\tconst result = options.attr(name, value);\n\t\tif (typeof result === 'string') return result;\n\t}\n\n\tif (name === 'ref' || name === 'key') return '';\n\tif (name === 'style' && typeof value === 'object') {\n\t\tlet str = '';\n\t\tfor (let prop in value) {\n\t\t\tlet val = value[prop];\n\t\t\tif (val != null && val !== '') {\n\t\t\t\tconst name =\n\t\t\t\t\tprop[0] == '-'\n\t\t\t\t\t\t? prop\n\t\t\t\t\t\t: JS_TO_CSS[prop] ||\n\t\t\t\t\t\t\t(JS_TO_CSS[prop] = prop.replace(CSS_REGEX, '-$&').toLowerCase());\n\n\t\t\t\tlet suffix = ';';\n\t\t\t\tif (\n\t\t\t\t\ttypeof val === 'number' &&\n\t\t\t\t\t// Exclude custom-attributes\n\t\t\t\t\t!name.startsWith('--') &&\n\t\t\t\t\t!IS_NON_DIMENSIONAL.test(name)\n\t\t\t\t) {\n\t\t\t\t\tsuffix = 'px;';\n\t\t\t\t}\n\t\t\t\tstr = str + name + ':' + val + suffix;\n\t\t\t}\n\t\t}\n\t\treturn name + '=\"' + str + '\"';\n\t}\n\n\tif (\n\t\tvalue == null ||\n\t\tvalue === false ||\n\t\ttypeof value === 'function' ||\n\t\ttypeof value === 'object'\n\t) {\n\t\treturn '';\n\t} else if (value === true) return name;\n\n\treturn name + '=\"' + encodeEntities(value) + '\"';\n}\n\n/**\n * Escape a dynamic child passed to `jsxTemplate`. This function\n * is not expected to be used directly, but rather through a\n * precompile JSX transform\n * @param {*} value\n * @returns {string | null | VNode | Array<string | null | VNode>}\n */\nfunction jsxEscape(value) {\n\tif (\n\t\tvalue == null ||\n\t\ttypeof value === 'boolean' ||\n\t\ttypeof value === 'function'\n\t) {\n\t\treturn null;\n\t}\n\n\tif (typeof value === 'object') {\n\t\t// Check for VNode\n\t\tif (value.constructor === undefined) return value;\n\n\t\tif (isArray(value)) {\n\t\t\tfor (let i = 0; i < value.length; i++) {\n\t\t\t\tvalue[i] = jsxEscape(value[i]);\n\t\t\t}\n\t\t\treturn value;\n\t\t}\n\t}\n\n\treturn encodeEntities('' + value);\n}\n\nexport {\n\tcreateVNode as jsx,\n\tcreateVNode as jsxs,\n\tcreateVNode as jsxDEV,\n\tFragment,\n\t// precompiled JSX transform\n\tjsxTemplate,\n\tjsxAttr,\n\tjsxEscape\n};\n"], "names": ["ENCODED_ENTITIES", "encodeEntities", "str", "length", "test", "last", "i", "out", "ch", "charCodeAt", "slice", "IS_NON_DIMENSIONAL", "vnodeId", "isArray", "Array", "createVNode", "type", "props", "key", "isStaticChildren", "__source", "__self", "ref", "normalizedProps", "vnode", "__k", "__", "__b", "__e", "__d", "undefined", "__c", "constructor", "__v", "__i", "__u", "defaultProps", "options", "jsxTemplate", "templates", "Fragment", "tpl", "exprs", "call", "arguments", "JS_TO_CSS", "CSS_REGEX", "jsxAttr", "name", "value", "attr", "result", "prop", "val", "replace", "toLowerCase", "suffix", "startsWith", "jsxEscape"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAMA,IAAmB;AAGlB,SAASC,EAAeC,CAAAA;IAE9B,IAAmB,MAAfA,EAAIC,MAAAA,IAAAA,CAA+C,MAA/BH,EAAiBI,IAAAA,CAAKF,IAAgB,OAAOA;IAQrE,IANA,IAAIG,IAAO,GACVC,IAAI,GACJC,IAAM,IACNC,IAAK,IAGCF,IAAIJ,EAAIC,MAAAA,EAAQG,IAAK;QAC3B,OAAQJ,EAAIO,UAAAA,CAAWH;YACtB,KAAK;gBACJE,IAAK;gBACL;YACD,KAAO;gBACNA,IAAK;gBACL;YACD,KAAK;gBACJA,IAAK;gBACL;YACD;gBACC;QAAA;QAGEF,MAAMD,KAAAA,CAAME,KAAOL,EAAIQ,KAAAA,CAAML,GAAMC,EAAAA,GACvCC,KAAOC,GAEPH,IAAOC,IAAI;IACZ;IAEA,OADIA,MAAMD,KAAAA,CAAME,KAAOL,EAAIQ,KAAAA,CAAML,GAAMC,EAAAA,GAChCC;AACR;ACrBO,IAAMI,IACZ,qECXGC,IAAU,GAERC,IAAUC,MAAMD,OAAAA;AAsBtB,SAASE,EAAYC,CAAAA,EAAMC,CAAAA,EAAOC,CAAAA,EAAKC,CAAAA,EAAkBC,CAAAA,EAAUC,CAAAA;IAC7DJ,KAAAA,CAAOA,IAAQ,CAAA,CAAA;IAIpB,IACCK,GACAhB,GAFGiB,IAAkBN;IAIlB,SAASA,KAAAA,CACZK,IAAML,EAAMK,GAAAA,EAAAA,OACLL,EAAMK,GAAAA;IAId,IAAME,IAAQ;QACbR,MAAAA;QACAC,OAAOM;QACPL,KAAAA;QACAI,KAAAA;QACAG,KAAW;QACXC,IAAS;QACTC,KAAQ;QACRC,KAAM;QACNC,KAAAA,KAAUC;QACVC,KAAY;QACZC,aAAAA,KAAaF;QACbG,KAAAA,EAAarB;QACbsB,KAAAA,CAAS;QACTC,KAAQ;QACRf,UAAAA;QACAC,QAAAA;IAAAA;IAKD,IAAoB,cAAA,OAATL,KAAAA,CAAwBM,IAAMN,EAAKoB,YAAAA,GAC7C,IAAK9B,KAAKgB,EAAAA,KACyB,MAAvBC,CAAAA,CAAgBjB,EAAAA,IAAAA,CAC1BiB,CAAAA,CAAgBjB,EAAAA,GAAKgB,CAAAA,CAAIhB,EAAAA;IAK5B,OADI+B,iOAAAA,CAAQb,KAAAA,IAAOa,iOAAAA,CAAQb,KAAAA,CAAMA,IAC1BA;AACR;AASA,SAASc,EAAYC,CAAAA;IACpB,IAAMf,IAAQT,EAAYyB,kOAAAA,EAAU;QAAEC,KAAKF;QAAWG,OAAAA,EAAAA,CAAKhC,KAAAA,CAAAiC,IAAAA,CAAAC,WAAC;IAAA;IAG5D,OADApB,EAAMN,GAAAA,GAAMM,EAAKS,GAAAA,EACVT;AACR;AAEA,IAAMqB,IAAY,CAAA,GACZC,IAAY;AAUlB,SAASC,EAAQC,CAAAA,EAAMC,CAAAA;IACtB,IAAIZ,iOAAAA,CAAQa,IAAAA,EAAM;QACjB,IAAMC,IAASd,iOAAAA,CAAQa,IAAAA,CAAKF,GAAMC;QAClC,IAAsB,YAAA,OAAXE,GAAqB,OAAOA;IACxC;IAEA,IAAa,UAATH,KAA2B,UAATA,GAAgB,OAAO;IAC7C,IAAa,YAATA,KAAqC,YAAA,OAAVC,GAAoB;QAClD,IAAI/C,IAAM;QACV,IAAK,IAAIkD,KAAQH,EAAO;YACvB,IAAII,IAAMJ,CAAAA,CAAMG,EAAAA;YAChB,IAAW,QAAPC,KAAuB,OAARA,GAAY;gBAC9B,IAAML,IACM,OAAXI,CAAAA,CAAK,EAAA,GACFA,IACAP,CAAAA,CAAUO,EAAAA,IAAAA,CACVP,CAAAA,CAAUO,EAAAA,GAAQA,EAAKE,OAAAA,CAAQR,GAAW,OAAOS,WAAAA,EAAAA,GAEjDC,IAAS;gBAEG,YAAA,OAARH,KAENL,EAAKS,UAAAA,CAAW,SAChB9C,EAAmBP,IAAAA,CAAK4C,MAAAA,CAEzBQ,IAAS,KAAA,GAEVtD,IAAMA,IAAM8C,IAAO,MAAMK,IAAMG;YAChC;QACD;QACA,OAAOR,IAAO,OAAO9C,IAAM;IAC5B;IAEA,OACU,QAAT+C,KAAAA,CACU,MAAVA,KACiB,cAAA,OAAVA,KACU,YAAA,OAAVA,IAEA,KAAA,CACa,MAAVA,IAAuBD,IAE3BA,IAAO,OAAO/C,EAAegD,KAAS;AAC9C;AASA,SAASS,EAAUT,CAAAA;IAClB,IACU,QAATA,KACiB,aAAA,OAAVA,KACU,cAAA,OAAVA,GAEP,OAAO;IAGR,IAAqB,YAAA,OAAVA,GAAoB;QAE9B,IAAA,KAA0BnB,MAAtBmB,EAAMjB,WAAAA,EAA2B,OAAOiB;QAE5C,IAAIpC,EAAQoC,IAAQ;YACnB,IAAK,IAAI3C,IAAI,GAAGA,IAAI2C,EAAM9C,MAAAA,EAAQG,IACjC2C,CAAAA,CAAM3C,EAAAA,GAAKoD,EAAUT,CAAAA,CAAM3C,EAAAA;YAE5B,OAAO2C;QACR;IACD;IAEA,OAAOhD,EAAe,KAAKgD;AAC5B", "debugId": null}}, {"offset": {"line": 3577, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/frontend/node_modules/.pnpm/preact-render-to-string@6.5.11_preact@10.24.3/node_modules/preact-render-to-string/dist/index.module.js", "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact-render-to-string%406.5.11_preact%4010.24.3/node_modules/preact-render-to-string/src/lib/util.js", "file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/preact-render-to-string%406.5.11_preact%4010.24.3/node_modules/preact-render-to-string/src/index.js"], "sourcesContent": ["export const VOID_ELEMENTS = /^(?:area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/;\nexport const UNSAFE_NAME = /[\\s\\n\\\\/='\"\\0<>]/;\nexport const NAMESPACE_REPLACE_REGEX = /^(xlink|xmlns|xml)([A-Z])/;\nexport const HTML_LOWER_CASE = /^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/;\nexport const SVG_CAMEL_CASE = /^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/;\n\n// Boolean DOM properties that translate to enumerated ('true'/'false') attributes\nexport const HTML_ENUMERATED = new Set(['draggable', 'spellcheck']);\n\n// DOM properties that should NOT have \"px\" added when numeric\nconst ENCODED_ENTITIES = /[\"&<]/;\n\n/** @param {string} str */\nexport function encodeEntities(str) {\n\t// Skip all work for strings with no entities needing encoding:\n\tif (str.length === 0 || ENCODED_ENTITIES.test(str) === false) return str;\n\n\tlet last = 0,\n\t\ti = 0,\n\t\tout = '',\n\t\tch = '';\n\n\t// Seek forward in str until the next entity char:\n\tfor (; i < str.length; i++) {\n\t\tswitch (str.charCodeAt(i)) {\n\t\t\tcase 34:\n\t\t\t\tch = '&quot;';\n\t\t\t\tbreak;\n\t\t\tcase 38:\n\t\t\t\tch = '&amp;';\n\t\t\t\tbreak;\n\t\t\tcase 60:\n\t\t\t\tch = '&lt;';\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tcontinue;\n\t\t}\n\t\t// Append skipped/buffered characters and the encoded entity:\n\t\tif (i !== last) out = out + str.slice(last, i);\n\t\tout = out + ch;\n\t\t// Start the next seek/buffer after the entity's offset:\n\t\tlast = i + 1;\n\t}\n\tif (i !== last) out = out + str.slice(last, i);\n\treturn out;\n}\n\nexport let indent = (s, char) =>\n\tString(s).replace(/(\\n+)/g, '$1' + (char || '\\t'));\n\nexport let isLargeString = (s, length, ignoreLines) =>\n\tString(s).length > (length || 40) ||\n\t(!ignoreLines && String(s).indexOf('\\n') !== -1) ||\n\tString(s).indexOf('<') !== -1;\n\nconst JS_TO_CSS = {};\n\nconst IS_NON_DIMENSIONAL = new Set([\n\t'animation-iteration-count',\n\t'border-image-outset',\n\t'border-image-slice',\n\t'border-image-width',\n\t'box-flex',\n\t'box-flex-group',\n\t'box-ordinal-group',\n\t'column-count',\n\t'fill-opacity',\n\t'flex',\n\t'flex-grow',\n\t'flex-negative',\n\t'flex-order',\n\t'flex-positive',\n\t'flex-shrink',\n\t'flood-opacity',\n\t'font-weight',\n\t'grid-column',\n\t'grid-row',\n\t'line-clamp',\n\t'line-height',\n\t'opacity',\n\t'order',\n\t'orphans',\n\t'stop-opacity',\n\t'stroke-dasharray',\n\t'stroke-dashoffset',\n\t'stroke-miterlimit',\n\t'stroke-opacity',\n\t'stroke-width',\n\t'tab-size',\n\t'widows',\n\t'z-index',\n\t'zoom'\n]);\n\nconst CSS_REGEX = /[A-Z]/g;\n// Convert an Object style to a CSSText string\nexport function styleObjToCss(s) {\n\tlet str = '';\n\tfor (let prop in s) {\n\t\tlet val = s[prop];\n\t\tif (val != null && val !== '') {\n\t\t\tconst name =\n\t\t\t\tprop[0] == '-'\n\t\t\t\t\t? prop\n\t\t\t\t\t: JS_TO_CSS[prop] ||\n\t\t\t\t\t  (JS_TO_CSS[prop] = prop.replace(CSS_REGEX, '-$&').toLowerCase());\n\n\t\t\tlet suffix = ';';\n\t\t\tif (\n\t\t\t\ttypeof val === 'number' &&\n\t\t\t\t// Exclude custom-attributes\n\t\t\t\t!name.startsWith('--') &&\n\t\t\t\t!IS_NON_DIMENSIONAL.has(name)\n\t\t\t) {\n\t\t\t\tsuffix = 'px;';\n\t\t\t}\n\t\t\tstr = str + name + ':' + val + suffix;\n\t\t}\n\t}\n\treturn str || undefined;\n}\n\n/**\n * Get flattened children from the children prop\n * @param {Array} accumulator\n * @param {any} children A `props.children` opaque object.\n * @returns {Array} accumulator\n * @private\n */\nexport function getChildren(accumulator, children) {\n\tif (Array.isArray(children)) {\n\t\tchildren.reduce(getChildren, accumulator);\n\t} else if (children != null && children !== false) {\n\t\taccumulator.push(children);\n\t}\n\treturn accumulator;\n}\n\nfunction markAsDirty() {\n\tthis.__d = true;\n}\n\nexport function createComponent(vnode, context) {\n\treturn {\n\t\t__v: vnode,\n\t\tcontext,\n\t\tprops: vnode.props,\n\t\t// silently drop state updates\n\t\tsetState: markAsDirty,\n\t\tforceUpdate: markAsDirty,\n\t\t__d: true,\n\t\t// hooks\n\t\t__h: new Array(0)\n\t};\n}\n\n// Necessary for createContext api. Setting this property will pass\n// the context value as `this.context` just for this component.\nexport function getContext(nodeName, context) {\n\tlet cxType = nodeName.contextType;\n\tlet provider = cxType && context[cxType.__c];\n\treturn cxType != null\n\t\t? provider\n\t\t\t? provider.props.value\n\t\t\t: cxType.__\n\t\t: context;\n}\n\n/**\n * @template T\n */\nexport class Deferred {\n\tconstructor() {\n\t\t// eslint-disable-next-line lines-around-comment\n\t\t/** @type {Promise<T>} */\n\t\tthis.promise = new Promise((resolve, reject) => {\n\t\t\tthis.resolve = resolve;\n\t\t\tthis.reject = reject;\n\t\t});\n\t}\n}\n", "import {\n\tencodeEntities,\n\tstyleObjToCss,\n\tUNSAFE_NAME,\n\tNAMESPACE_REPLACE_REGEX,\n\tHTML_LOWER_CASE,\n\tHTML_ENUMERATED,\n\tSVG_CAMEL_CASE,\n\tcreateComponent\n} from './lib/util.js';\nimport { options, h, Fragment } from 'preact';\nimport {\n\tCHILDREN,\n\tCOMMIT,\n\tCOMPONENT,\n\tDIFF,\n\tDIFFED,\n\tDIRTY,\n\tNEXT_STATE,\n\tPARENT,\n\tRENDER,\n\tSKIP_EFFECTS,\n\tVNODE,\n\tCATCH_ERROR\n} from './lib/constants.js';\n\nconst EMPTY_OBJ = {};\nconst EMPTY_ARR = [];\nconst isArray = Array.isArray;\nconst assign = Object.assign;\nconst EMPTY_STR = '';\n\n// Global state for the current render pass\nlet beforeDiff, afterDiff, renderHook, ummountHook;\n\n/**\n * Render Preact JSX + Components to an HTML string.\n * @param {VNode} vnode\tJSX Element / VNode to render\n * @param {Object} [context={}] Initial root context object\n * @param {RendererState} [_rendererState] for internal use\n * @returns {string} serialized HTML\n */\nexport function renderToString(vnode, context, _rendererState) {\n\t// Performance optimization: `renderToString` is synchronous and we\n\t// therefore don't execute any effects. To do that we pass an empty\n\t// array to `options._commit` (`__c`). But we can go one step further\n\t// and avoid a lot of dirty checks and allocations by setting\n\t// `options._skipEffects` (`__s`) too.\n\tconst previousSkipEffects = options[SKIP_EFFECTS];\n\toptions[SKIP_EFFECTS] = true;\n\n\t// store options hooks once before each synchronous render call\n\tbeforeDiff = options[DIFF];\n\tafterDiff = options[DIFFED];\n\trenderHook = options[RENDER];\n\tummountHook = options.unmount;\n\n\tconst parent = h(Fragment, null);\n\tparent[CHILDREN] = [vnode];\n\n\ttry {\n\t\tconst rendered = _renderToString(\n\t\t\tvnode,\n\t\t\tcontext || EMPTY_OBJ,\n\t\t\tfalse,\n\t\t\tundefined,\n\t\t\tparent,\n\t\t\tfalse,\n\t\t\t_rendererState\n\t\t);\n\n\t\tif (isArray(rendered)) {\n\t\t\treturn rendered.join(EMPTY_STR);\n\t\t}\n\t\treturn rendered;\n\t} catch (e) {\n\t\tif (e.then) {\n\t\t\tthrow new Error('Use \"renderToStringAsync\" for suspenseful rendering.');\n\t\t}\n\n\t\tthrow e;\n\t} finally {\n\t\t// options._commit, we don't schedule any effects in this library right now,\n\t\t// so we can pass an empty queue to this hook.\n\t\tif (options[COMMIT]) options[COMMIT](vnode, EMPTY_ARR);\n\t\toptions[SKIP_EFFECTS] = previousSkipEffects;\n\t\tEMPTY_ARR.length = 0;\n\t}\n}\n\n/**\n * Render Preact JSX + Components to an HTML string.\n * @param {VNode} vnode\tJSX Element / VNode to render\n * @param {Object} [context={}] Initial root context object\n * @returns {string} serialized HTML\n */\nexport async function renderToStringAsync(vnode, context) {\n\t// Performance optimization: `renderToString` is synchronous and we\n\t// therefore don't execute any effects. To do that we pass an empty\n\t// array to `options._commit` (`__c`). But we can go one step further\n\t// and avoid a lot of dirty checks and allocations by setting\n\t// `options._skipEffects` (`__s`) too.\n\tconst previousSkipEffects = options[SKIP_EFFECTS];\n\toptions[SKIP_EFFECTS] = true;\n\n\t// store options hooks once before each synchronous render call\n\tbeforeDiff = options[DIFF];\n\tafterDiff = options[DIFFED];\n\trenderHook = options[RENDER];\n\tummountHook = options.unmount;\n\n\tconst parent = h(Fragment, null);\n\tparent[CHILDREN] = [vnode];\n\n\ttry {\n\t\tconst rendered = await _renderToString(\n\t\t\tvnode,\n\t\t\tcontext || EMPTY_OBJ,\n\t\t\tfalse,\n\t\t\tundefined,\n\t\t\tparent,\n\t\t\ttrue,\n\t\t\tundefined\n\t\t);\n\n\t\tif (isArray(rendered)) {\n\t\t\tlet count = 0;\n\t\t\tlet resolved = rendered;\n\n\t\t\t// Resolving nested Promises with a maximum depth of 25\n\t\t\twhile (\n\t\t\t\tresolved.some(\n\t\t\t\t\t(element) => element && typeof element.then === 'function'\n\t\t\t\t) &&\n\t\t\t\tcount++ < 25\n\t\t\t) {\n\t\t\t\tresolved = (await Promise.all(resolved)).flat();\n\t\t\t}\n\n\t\t\treturn resolved.join(EMPTY_STR);\n\t\t}\n\n\t\treturn rendered;\n\t} finally {\n\t\t// options._commit, we don't schedule any effects in this library right now,\n\t\t// so we can pass an empty queue to this hook.\n\t\tif (options[COMMIT]) options[COMMIT](vnode, EMPTY_ARR);\n\t\toptions[SKIP_EFFECTS] = previousSkipEffects;\n\t\tEMPTY_ARR.length = 0;\n\t}\n}\n\n/**\n * @param {VNode} vnode\n * @param {Record<string, unknown>} context\n */\nfunction renderClassComponent(vnode, context) {\n\tlet type = /** @type {import(\"preact\").ComponentClass<typeof vnode.props>} */ (vnode.type);\n\n\tlet isMounting = true;\n\tlet c;\n\tif (vnode[COMPONENT]) {\n\t\tisMounting = false;\n\t\tc = vnode[COMPONENT];\n\t\tc.state = c[NEXT_STATE];\n\t} else {\n\t\tc = new type(vnode.props, context);\n\t}\n\n\tvnode[COMPONENT] = c;\n\tc[VNODE] = vnode;\n\n\tc.props = vnode.props;\n\tc.context = context;\n\t// turn off stateful re-rendering:\n\tc[DIRTY] = true;\n\n\tif (c.state == null) c.state = EMPTY_OBJ;\n\n\tif (c[NEXT_STATE] == null) {\n\t\tc[NEXT_STATE] = c.state;\n\t}\n\n\tif (type.getDerivedStateFromProps) {\n\t\tc.state = assign(\n\t\t\t{},\n\t\t\tc.state,\n\t\t\ttype.getDerivedStateFromProps(c.props, c.state)\n\t\t);\n\t} else if (isMounting && c.componentWillMount) {\n\t\tc.componentWillMount();\n\n\t\t// If the user called setState in cWM we need to flush pending,\n\t\t// state updates. This is the same behaviour in React.\n\t\tc.state = c[NEXT_STATE] !== c.state ? c[NEXT_STATE] : c.state;\n\t} else if (!isMounting && c.componentWillUpdate) {\n\t\tc.componentWillUpdate();\n\t}\n\n\tif (renderHook) renderHook(vnode);\n\n\treturn c.render(c.props, c.state, context);\n}\n\n/**\n * Recursively render VNodes to HTML.\n * @param {VNode|any} vnode\n * @param {any} context\n * @param {boolean} isSvgMode\n * @param {any} selectValue\n * @param {VNode} parent\n * @param {boolean} asyncMode\n * @param {RendererState | undefined} [renderer]\n * @returns {string | Promise<string> | (string | Promise<string>)[]}\n */\nfunction _renderToString(\n\tvnode,\n\tcontext,\n\tisSvgMode,\n\tselectValue,\n\tparent,\n\tasyncMode,\n\trenderer\n) {\n\t// Ignore non-rendered VNodes/values\n\tif (\n\t\tvnode == null ||\n\t\tvnode === true ||\n\t\tvnode === false ||\n\t\tvnode === EMPTY_STR\n\t) {\n\t\treturn EMPTY_STR;\n\t}\n\n\tlet vnodeType = typeof vnode;\n\t// Text VNodes: escape as HTML\n\tif (vnodeType != 'object') {\n\t\tif (vnodeType == 'function') return EMPTY_STR;\n\t\treturn vnodeType == 'string' ? encodeEntities(vnode) : vnode + EMPTY_STR;\n\t}\n\n\t// Recurse into children / Arrays\n\tif (isArray(vnode)) {\n\t\tlet rendered = EMPTY_STR,\n\t\t\trenderArray;\n\t\tparent[CHILDREN] = vnode;\n\t\tfor (let i = 0; i < vnode.length; i++) {\n\t\t\tlet child = vnode[i];\n\t\t\tif (child == null || typeof child == 'boolean') continue;\n\n\t\t\tconst childRender = _renderToString(\n\t\t\t\tchild,\n\t\t\t\tcontext,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue,\n\t\t\t\tparent,\n\t\t\t\tasyncMode,\n\t\t\t\trenderer\n\t\t\t);\n\n\t\t\tif (typeof childRender == 'string') {\n\t\t\t\trendered = rendered + childRender;\n\t\t\t} else {\n\t\t\t\tif (!renderArray) {\n\t\t\t\t\trenderArray = [];\n\t\t\t\t}\n\n\t\t\t\tif (rendered) renderArray.push(rendered);\n\n\t\t\t\trendered = EMPTY_STR;\n\n\t\t\t\tif (isArray(childRender)) {\n\t\t\t\t\trenderArray.push(...childRender);\n\t\t\t\t} else {\n\t\t\t\t\trenderArray.push(childRender);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (renderArray) {\n\t\t\tif (rendered) renderArray.push(rendered);\n\t\t\treturn renderArray;\n\t\t}\n\n\t\treturn rendered;\n\t}\n\n\t// VNodes have {constructor:undefined} to prevent JSON injection:\n\tif (vnode.constructor !== undefined) return EMPTY_STR;\n\n\tvnode[PARENT] = parent;\n\tif (beforeDiff) beforeDiff(vnode);\n\n\tlet type = vnode.type,\n\t\tprops = vnode.props;\n\n\t// Invoke rendering on Components\n\tif (typeof type == 'function') {\n\t\tlet cctx = context,\n\t\t\tcontextType,\n\t\t\trendered,\n\t\t\tcomponent;\n\t\tif (type === Fragment) {\n\t\t\t// Serialized precompiled JSX.\n\t\t\tif ('tpl' in props) {\n\t\t\t\tlet out = EMPTY_STR;\n\t\t\t\tfor (let i = 0; i < props.tpl.length; i++) {\n\t\t\t\t\tout = out + props.tpl[i];\n\n\t\t\t\t\tif (props.exprs && i < props.exprs.length) {\n\t\t\t\t\t\tconst value = props.exprs[i];\n\t\t\t\t\t\tif (value == null) continue;\n\n\t\t\t\t\t\t// Check if we're dealing with a vnode or an array of nodes\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\ttypeof value == 'object' &&\n\t\t\t\t\t\t\t(value.constructor === undefined || isArray(value))\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tout =\n\t\t\t\t\t\t\t\tout +\n\t\t\t\t\t\t\t\t_renderToString(\n\t\t\t\t\t\t\t\t\tvalue,\n\t\t\t\t\t\t\t\t\tcontext,\n\t\t\t\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\t\t\t\tvnode,\n\t\t\t\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\t\t\t\trenderer\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// Values are pre-escaped by the JSX transform\n\t\t\t\t\t\t\tout = out + value;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn out;\n\t\t\t} else if ('UNSTABLE_comment' in props) {\n\t\t\t\t// Fragments are the least used components of core that's why\n\t\t\t\t// branching here for comments has the least effect on perf.\n\t\t\t\treturn '<!--' + encodeEntities(props.UNSTABLE_comment) + '-->';\n\t\t\t}\n\n\t\t\trendered = props.children;\n\t\t} else {\n\t\t\tcontextType = type.contextType;\n\t\t\tif (contextType != null) {\n\t\t\t\tlet provider = context[contextType.__c];\n\t\t\t\tcctx = provider ? provider.props.value : contextType.__;\n\t\t\t}\n\n\t\t\tlet isClassComponent =\n\t\t\t\ttype.prototype && typeof type.prototype.render == 'function';\n\t\t\tif (isClassComponent) {\n\t\t\t\trendered = /**#__NOINLINE__**/ renderClassComponent(vnode, cctx);\n\t\t\t\tcomponent = vnode[COMPONENT];\n\t\t\t} else {\n\t\t\t\tvnode[COMPONENT] = component = /**#__NOINLINE__**/ createComponent(\n\t\t\t\t\tvnode,\n\t\t\t\t\tcctx\n\t\t\t\t);\n\n\t\t\t\t// If a hook invokes setState() to invalidate the component during rendering,\n\t\t\t\t// re-render it up to 25 times to allow \"settling\" of memoized states.\n\t\t\t\t// Note:\n\t\t\t\t//   This will need to be updated for Preact 11 to use internal.flags rather than component._dirty:\n\t\t\t\t//   https://github.com/preactjs/preact/blob/d4ca6fdb19bc715e49fd144e69f7296b2f4daa40/src/diff/component.js#L35-L44\n\t\t\t\tlet count = 0;\n\t\t\t\twhile (component[DIRTY] && count++ < 25) {\n\t\t\t\t\tcomponent[DIRTY] = false;\n\n\t\t\t\t\tif (renderHook) renderHook(vnode);\n\n\t\t\t\t\trendered = type.call(component, props, cctx);\n\t\t\t\t}\n\t\t\t\tcomponent[DIRTY] = true;\n\t\t\t}\n\n\t\t\tif (component.getChildContext != null) {\n\t\t\t\tcontext = assign({}, context, component.getChildContext());\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\tisClassComponent &&\n\t\t\t\toptions.errorBoundaries &&\n\t\t\t\t(type.getDerivedStateFromError || component.componentDidCatch)\n\t\t\t) {\n\t\t\t\t// When a component returns a Fragment node we flatten it in core, so we\n\t\t\t\t// need to mirror that logic here too\n\t\t\t\tlet isTopLevelFragment =\n\t\t\t\t\trendered != null &&\n\t\t\t\t\trendered.type === Fragment &&\n\t\t\t\t\trendered.key == null &&\n\t\t\t\t\trendered.props.tpl == null;\n\t\t\t\trendered = isTopLevelFragment ? rendered.props.children : rendered;\n\n\t\t\t\ttry {\n\t\t\t\t\treturn _renderToString(\n\t\t\t\t\t\trendered,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\tvnode,\n\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\trenderer\n\t\t\t\t\t);\n\t\t\t\t} catch (err) {\n\t\t\t\t\tif (type.getDerivedStateFromError) {\n\t\t\t\t\t\tcomponent[NEXT_STATE] = type.getDerivedStateFromError(err);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (component.componentDidCatch) {\n\t\t\t\t\t\tcomponent.componentDidCatch(err, EMPTY_OBJ);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (component[DIRTY]) {\n\t\t\t\t\t\trendered = renderClassComponent(vnode, context);\n\t\t\t\t\t\tcomponent = vnode[COMPONENT];\n\n\t\t\t\t\t\tif (component.getChildContext != null) {\n\t\t\t\t\t\t\tcontext = assign({}, context, component.getChildContext());\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlet isTopLevelFragment =\n\t\t\t\t\t\t\trendered != null &&\n\t\t\t\t\t\t\trendered.type === Fragment &&\n\t\t\t\t\t\t\trendered.key == null &&\n\t\t\t\t\t\t\trendered.props.tpl == null;\n\t\t\t\t\t\trendered = isTopLevelFragment ? rendered.props.children : rendered;\n\n\t\t\t\t\t\treturn _renderToString(\n\t\t\t\t\t\t\trendered,\n\t\t\t\t\t\t\tcontext,\n\t\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\t\tvnode,\n\t\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\t\trenderer\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn EMPTY_STR;\n\t\t\t\t} finally {\n\t\t\t\t\tif (afterDiff) afterDiff(vnode);\n\t\t\t\t\tvnode[PARENT] = null;\n\n\t\t\t\t\tif (ummountHook) ummountHook(vnode);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// When a component returns a Fragment node we flatten it in core, so we\n\t\t// need to mirror that logic here too\n\t\tlet isTopLevelFragment =\n\t\t\trendered != null &&\n\t\t\trendered.type === Fragment &&\n\t\t\trendered.key == null &&\n\t\t\trendered.props.tpl == null;\n\t\trendered = isTopLevelFragment ? rendered.props.children : rendered;\n\n\t\ttry {\n\t\t\t// Recurse into children before invoking the after-diff hook\n\t\t\tconst str = _renderToString(\n\t\t\t\trendered,\n\t\t\t\tcontext,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue,\n\t\t\t\tvnode,\n\t\t\t\tasyncMode,\n\t\t\t\trenderer\n\t\t\t);\n\n\t\t\tif (afterDiff) afterDiff(vnode);\n\t\t\t// when we are dealing with suspense we can't do this...\n\t\t\tvnode[PARENT] = null;\n\n\t\t\tif (options.unmount) options.unmount(vnode);\n\n\t\t\treturn str;\n\t\t} catch (error) {\n\t\t\tif (!asyncMode && renderer && renderer.onError) {\n\t\t\t\tlet res = renderer.onError(error, vnode, (child) =>\n\t\t\t\t\t_renderToString(\n\t\t\t\t\t\tchild,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\tvnode,\n\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\trenderer\n\t\t\t\t\t)\n\t\t\t\t);\n\n\t\t\t\tif (res !== undefined) return res;\n\n\t\t\t\tlet errorHook = options[CATCH_ERROR];\n\t\t\t\tif (errorHook) errorHook(error, vnode);\n\t\t\t\treturn EMPTY_STR;\n\t\t\t}\n\n\t\t\tif (!asyncMode) throw error;\n\n\t\t\tif (!error || typeof error.then != 'function') throw error;\n\n\t\t\tconst renderNestedChildren = () => {\n\t\t\t\ttry {\n\t\t\t\t\treturn _renderToString(\n\t\t\t\t\t\trendered,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\tvnode,\n\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\trenderer\n\t\t\t\t\t);\n\t\t\t\t} catch (e) {\n\t\t\t\t\tif (!e || typeof e.then != 'function') throw e;\n\n\t\t\t\t\treturn e.then(\n\t\t\t\t\t\t() =>\n\t\t\t\t\t\t\t_renderToString(\n\t\t\t\t\t\t\t\trendered,\n\t\t\t\t\t\t\t\tcontext,\n\t\t\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\t\t\tvnode,\n\t\t\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\t\t\trenderer\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\trenderNestedChildren\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t};\n\n\t\t\treturn error.then(renderNestedChildren);\n\t\t}\n\t}\n\n\t// Serialize Element VNodes to HTML\n\tlet s = '<' + type,\n\t\thtml = EMPTY_STR,\n\t\tchildren;\n\n\tfor (let name in props) {\n\t\tlet v = props[name];\n\n\t\tif (typeof v == 'function' && name !== 'class' && name !== 'className') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tswitch (name) {\n\t\t\tcase 'children':\n\t\t\t\tchildren = v;\n\t\t\t\tcontinue;\n\n\t\t\t// VDOM-specific props\n\t\t\tcase 'key':\n\t\t\tcase 'ref':\n\t\t\tcase '__self':\n\t\t\tcase '__source':\n\t\t\t\tcontinue;\n\n\t\t\t// prefer for/class over htmlFor/className\n\t\t\tcase 'htmlFor':\n\t\t\t\tif ('for' in props) continue;\n\t\t\t\tname = 'for';\n\t\t\t\tbreak;\n\t\t\tcase 'className':\n\t\t\t\tif ('class' in props) continue;\n\t\t\t\tname = 'class';\n\t\t\t\tbreak;\n\n\t\t\t// Form element reflected properties\n\t\t\tcase 'defaultChecked':\n\t\t\t\tname = 'checked';\n\t\t\t\tbreak;\n\t\t\tcase 'defaultSelected':\n\t\t\t\tname = 'selected';\n\t\t\t\tbreak;\n\n\t\t\t// Special value attribute handling\n\t\t\tcase 'defaultValue':\n\t\t\tcase 'value':\n\t\t\t\tname = 'value';\n\t\t\t\tswitch (type) {\n\t\t\t\t\t// <textarea value=\"a&b\"> --> <textarea>a&amp;b</textarea>\n\t\t\t\t\tcase 'textarea':\n\t\t\t\t\t\tchildren = v;\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t// <select value> is serialized as a selected attribute on the matching option child\n\t\t\t\t\tcase 'select':\n\t\t\t\t\t\tselectValue = v;\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t// Add a selected attribute to <option> if its value matches the parent <select> value\n\t\t\t\t\tcase 'option':\n\t\t\t\t\t\tif (selectValue == v && !('selected' in props)) {\n\t\t\t\t\t\t\ts = s + ' selected';\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tbreak;\n\n\t\t\tcase 'dangerouslySetInnerHTML':\n\t\t\t\thtml = v && v.__html;\n\t\t\t\tcontinue;\n\n\t\t\t// serialize object styles to a CSS string\n\t\t\tcase 'style':\n\t\t\t\tif (typeof v === 'object') {\n\t\t\t\t\tv = styleObjToCss(v);\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'acceptCharset':\n\t\t\t\tname = 'accept-charset';\n\t\t\t\tbreak;\n\t\t\tcase 'httpEquiv':\n\t\t\t\tname = 'http-equiv';\n\t\t\t\tbreak;\n\n\t\t\tdefault: {\n\t\t\t\tif (NAMESPACE_REPLACE_REGEX.test(name)) {\n\t\t\t\t\tname = name.replace(NAMESPACE_REPLACE_REGEX, '$1:$2').toLowerCase();\n\t\t\t\t} else if (UNSAFE_NAME.test(name)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t} else if (\n\t\t\t\t\t(name[4] === '-' || HTML_ENUMERATED.has(name)) &&\n\t\t\t\t\tv != null\n\t\t\t\t) {\n\t\t\t\t\t// serialize boolean aria-xyz or enumerated attribute values as strings\n\t\t\t\t\tv = v + EMPTY_STR;\n\t\t\t\t} else if (isSvgMode) {\n\t\t\t\t\tif (SVG_CAMEL_CASE.test(name)) {\n\t\t\t\t\t\tname =\n\t\t\t\t\t\t\tname === 'panose1'\n\t\t\t\t\t\t\t\t? 'panose-1'\n\t\t\t\t\t\t\t\t: name.replace(/([A-Z])/g, '-$1').toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t} else if (HTML_LOWER_CASE.test(name)) {\n\t\t\t\t\tname = name.toLowerCase();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// write this attribute to the buffer\n\t\tif (v != null && v !== false) {\n\t\t\tif (v === true || v === EMPTY_STR) {\n\t\t\t\ts = s + ' ' + name;\n\t\t\t} else {\n\t\t\t\ts =\n\t\t\t\t\ts +\n\t\t\t\t\t' ' +\n\t\t\t\t\tname +\n\t\t\t\t\t'=\"' +\n\t\t\t\t\t(typeof v == 'string' ? encodeEntities(v) : v + EMPTY_STR) +\n\t\t\t\t\t'\"';\n\t\t\t}\n\t\t}\n\t}\n\n\tif (UNSAFE_NAME.test(type)) {\n\t\t// this seems to performs a lot better than throwing\n\t\t// return '<!-- -->';\n\t\tthrow new Error(`${type} is not a valid HTML tag name in ${s}>`);\n\t}\n\n\tif (html) {\n\t\t// dangerouslySetInnerHTML defined this node's contents\n\t} else if (typeof children === 'string') {\n\t\t// single text child\n\t\thtml = encodeEntities(children);\n\t} else if (children != null && children !== false && children !== true) {\n\t\t// recurse into this element VNode's children\n\t\tlet childSvgMode =\n\t\t\ttype === 'svg' || (type !== 'foreignObject' && isSvgMode);\n\t\thtml = _renderToString(\n\t\t\tchildren,\n\t\t\tcontext,\n\t\t\tchildSvgMode,\n\t\t\tselectValue,\n\t\t\tvnode,\n\t\t\tasyncMode,\n\t\t\trenderer\n\t\t);\n\t}\n\n\tif (afterDiff) afterDiff(vnode);\n\n\t// TODO: this was commented before\n\tvnode[PARENT] = null;\n\n\tif (ummountHook) ummountHook(vnode);\n\n\t// Emit self-closing tag for empty void elements:\n\tif (!html && SELF_CLOSING.has(type)) {\n\t\treturn s + '/>';\n\t}\n\n\tconst endTag = '</' + type + '>';\n\tconst startTag = s + '>';\n\n\tif (isArray(html)) return [startTag, ...html, endTag];\n\telse if (typeof html != 'string') return [startTag, html, endTag];\n\treturn startTag + html + endTag;\n}\n\nconst SELF_CLOSING = new Set([\n\t'area',\n\t'base',\n\t'br',\n\t'col',\n\t'command',\n\t'embed',\n\t'hr',\n\t'img',\n\t'input',\n\t'keygen',\n\t'link',\n\t'meta',\n\t'param',\n\t'source',\n\t'track',\n\t'wbr'\n]);\n\nexport default renderToString;\nexport const render = renderToString;\nexport const renderToStaticMarkup = renderToString;\n"], "names": ["UNSAFE_NAME", "NAMESPACE_REPLACE_REGEX", "HTML_LOWER_CASE", "SVG_CAMEL_CASE", "HTML_ENUMERATED", "Set", "ENCODED_ENTITIES", "encodeEntities", "str", "length", "test", "last", "i", "out", "ch", "charCodeAt", "slice", "JS_TO_CSS", "IS_NON_DIMENSIONAL", "CSS_REGEX", "styleObjToCss", "s", "prop", "val", "name", "replace", "toLowerCase", "suffix", "startsWith", "has", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "this", "__d", "createComponent", "vnode", "context", "__v", "props", "setState", "forceUpdate", "__h", "Array", "pact", "state", "value", "o", "_settle", "bind", "v", "then", "observer", "_Pact", "prototype", "onFulfilled", "onRejected", "result", "callback", "e", "_this", "thenable", "update", "body", "stage", "shouldC<PERSON><PERSON>ue", "_isSettledPact", "updateValue", "reject", "_resumeAfterTest", "_resumeAfterBody", "_resumeAfterUpdate", "finalizer", "renderToStringAsync", "beforeDiff", "afterDiff", "renderHook", "ummountHook", "previousSkipEffects", "options", "unmount", "parent", "h", "Fragment", "_renderToString", "EMPTY_OBJ", "rendered", "isArray", "resolved", "join", "EMPTY_STR", "count", "some", "element", "Promise", "all", "_Promise$all", "flat", "EMPTY_ARR", "assign", "Object", "renderToString", "_rendererState", "Error", "renderClassComponent", "c", "type", "isMounting", "getDerivedStateFromProps", "componentWillMount", "componentWillUpdate", "render", "isSvgMode", "selectValue", "asyncMode", "renderer", "vnodeType", "renderArray", "child", "childRender", "push", "constructor", "contextType", "component", "cctx", "tpl", "exprs", "UNSTABLE_comment", "children", "provider", "__c", "__", "isClassComponent", "call", "getChildContext", "errorBoundaries", "getDerivedStateFromError", "componentDidCatch", "key", "err", "error", "onError", "res", "errorHook", "renderNestedChildren", "html", "__html", "SELF_CLOSING", "endTag", "startTag", "renderToStaticMarkup"], "mappings": ";;;;;;;;;;;;;;IACaA,IAAc,oBACdC,IAA0B,6BAC1BC,IAAkB,+JAClBC,IAAiB,0QAGjBC,IAAkB,IAAIC,IAAI;IAAC;IAAa;CAAA,GAG/CC,IAAmB;AAAA,SAGTC,EAAeC,CAAAA;IAE9B,IAAmB,MAAfA,EAAIC,MAAAA,IAAAA,CAA+C,MAA/BH,EAAiBI,IAAAA,CAAKF,IAAgB,OAAOA;IAQrE,IANA,IAAIG,IAAO,GACVC,IAAI,GACJC,IAAM,IACNC,IAAK,IAGCF,IAAIJ,EAAIC,MAAAA,EAAQG,IAAK;QAC3B,OAAQJ,EAAIO,UAAAA,CAAWH;YACtB,KAAA;gBACCE,IAAK;gBACL;YACD,KAAA;gBACCA,IAAK;gBACL;YACD,KAAA;gBACCA,IAAK;gBACL;YACD;gBACC;QAAA;QAGEF,MAAMD,KAAAA,CAAME,KAAYL,EAAIQ,KAAAA,CAAML,GAAMC,EAAAA,GAC5CC,KAAYC,GAEZH,IAAOC,IAAI;IACX;IAED,OADIA,MAAMD,KAAAA,CAAME,KAAYL,EAAIQ,KAAAA,CAAML,GAAMC,EAAAA,GACrCC;AACP;AAUD,IAAMI,IAAY,CAAA,GAEZC,IAAqB,IAAIb,IAAI;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CAAA,GAGKc,IAAY;AAAA,SAEFC,EAAcC,CAAAA;IAC7B,IAAIb,IAAM;IACV,IAAK,IAAIc,KAAQD,EAAG;QACnB,IAAIE,IAAMF,CAAAA,CAAEC,EAAAA;QACZ,IAAW,QAAPC,KAAuB,OAARA,GAAY;YAC9B,IAAMC,IACM,OAAXF,CAAAA,CAAK,EAAA,GACFA,IACAL,CAAAA,CAAUK,EAAAA,IAAAA,CACTL,CAAAA,CAAUK,EAAAA,GAAQA,EAAKG,OAAAA,CAAQN,GAAW,OAAOO,WAAAA,EAAAA,GAElDC,IAAS;YAEG,YAAA,OAARJ,KAENC,EAAKI,UAAAA,CAAW,SAChBV,EAAmBW,GAAAA,CAAIL,MAAAA,CAExBG,IAAS,KAAA,GAEVnB,IAAMA,IAAMgB,IAAO,MAAMD,IAAMI;QAC/B;IACD;IACD,OAAOnB,KAAAA,KAAOsB;AACd;AAkBD,SAASC;IACRC,IAAAA,CAAKC,GAAAA,GAAAA,CAAM;AACX;AAAA,SAEeC,EAAgBC,CAAAA,EAAOC,CAAAA;IACtC,OAAO;QACNC,KAAKF;QACLC,SAAAA;QACAE,OAAOH,EAAMG,KAAAA;QAEbC,UAAUR;QACVS,aAAaT;QACbE,KAAAA,CAAK;QAELQ,KAAK,IAAIC,MAAM;IAAA;AAEhB;ACnHM,SAAA,EAAiBC,CAAAA,EAAMC,CAAAA,EAAOC,CAAAA;IACpC,IAAA,CAAKF,EAAKtB,CAAAA,EAAG;QACZ,IAAIwB,aAAAA,GAAwB;YAC3B,IAAA,CAAIA,EAAMxB,CAAAA,EAOT,OAAA,KAAA,CADAwB,EAAMC,CAAAA,GAAIC,EAAQC,IAAAA,CAAK,MAAML,GAAMC,EAAAA;YALvB,IAARA,KAAAA,CACHA,IAAQC,EAAMxB,CAAAA,GAEfwB,IAAQA,EAAMI;QAKf;QACD,IAAIJ,KAASA,EAAMK,IAAAA,EAElB,OAAA,KADAL,EAAMK,IAAAA,CAAKH,EAAQC,IAAAA,CAAK,MAAML,GAAMC,IAAQG,EAAQC,IAAAA,CAAK,MAAML,GAAM;QAGtEA,EAAKtB,CAAAA,GAAIuB,GACTD,EAAKM,CAAAA,GAAIJ;QACT,MAAMM,IAAWR,EAAKG,CAAAA;QAClBK,KACHA,EAASR;IAEV;AACD;AA9DM,IAAA,IAAA,WAAA,GAA4B;IAClC,SAAA,KAAA;IAiCA,OAhCAS,EAAMC,SAAAA,CAAUH,IAAAA,GAAO,SAASI,CAAAA,EAAaC,CAAAA;QAC5C,IAAMC,IAAS,IAAA,GACTZ,IAAQZ,IAAAA,CAAKX,CAAAA;QACnB,IAAIuB,GAAO;YACV,IAAMa,IAAmB,IAARb,IAAYU,IAAcC;YAC3C,IAAIE,GAAU;gBACb,IAAA;oBACCV,EAAQS,GAAQ,GAAGC,EAASzB,IAAAA,CAAKiB,CAAAA;gBAGjC,EAFC,OAAOS,GAAAA;oBACRX,EAAQS,GAAQ,GAAGE;gBACnB;gBACD,OAAOF;YACP;YACA,OAAA;QAED;QAeD,OAdAxB,IAAAA,CAAKc,CAAAA,GAAI,SAASa,CAAAA;YACjB,IAAA;gBACC,IAAMd,IAAQc,EAAMV,CAAAA;gBACN,IAAVU,EAAMtC,CAAAA,GACT0B,EAAQS,GAAQ,GAAGF,IAAcA,EAAYT,KAASA,KAC5CU,IACVR,EAAQS,GAAQ,GAAGD,EAAWV,MAE9BE,EAAQS,GAAQ,GAAGX;YAIpB,EAFC,OAAOa,GAAAA;gBACRX,EAAQS,GAAQ,GAAGE;YACnB;QACD,GACMF;IACP,GAAA;AAED,CAnCkC;AAgE5B,SAAA,EAAwBI,CAAAA;IAC9B,OAAOA,aAAAA,KAA0C,IAAbA,EAASvC;AAC7C;AA4LM,SAAA,EAAcX,CAAAA,EAAMmD,CAAAA,EAAQC,CAAAA;IAElC,IADA,IAAIC,IACK;QACR,IAAIC,IAAiBtD;QAIrB,IAHIuD,EAAeD,MAAAA,CAClBA,IAAiBA,EAAef,CAAAA,GAAAA,CAE5Be,GACJ,OAAOR;QAER,IAAIQ,EAAed,IAAAA,EAAM;YACxBa,IAAQ;YACR;QACA;QACD,IAAIP,IAASM;QACb,IAAIN,KAAUA,EAAON,IAAAA,EAAM;YAC1B,IAAA,CAAIe,EAAeT,IAEZ;gBACNO,IAAQ;gBACR;YACA;YAJAP,IAASA,EAAOnC;QAKjB;QACD,IAAIwC,GAAQ;YACX,IAAIK,IAAcL;YAClB,IAAIK,KAAeA,EAAYhB,IAAAA,IAAAA,CAASe,EAAeC,IAAc;gBACpEH,IAAQ;gBACR;YACA;QACD;IACD;IACD,IAAIpB,IAAO,IAAA,GACPwB,IAASpB,EAAQC,IAAAA,CAAK,MAAML,GAAM;IAEtC,OAAA,CADW,MAAVoB,IAAcC,EAAed,IAAAA,CAAKkB,KAA8B,MAAVL,IAAcP,EAAON,IAAAA,CAAKmB,KAAoBH,EAAYhB,IAAAA,CAAKoB,EAAAA,EAAqBpB,IAAAA,CAAAA,KAAK,GAAQiB,IACjJxB;;;IACP,SAAS0B,EAAiBxB,CAAAA;QACzBW,IAASX;QACT,GAAG;YACF,IAAIgB,KAAAA,CACHK,IAAcL,GAAAA,KACKK,EAAYhB,IAAAA,IAAAA,CAASe,EAAeC,IAEtD,OAAA,KADAA,EAAYhB,IAAAA,CAAKoB,GAAoBpB,IAAAA,CAAAA,KAAK,GAAQiB;YAKpD,IAAA,CAAA,CADAH,IAAiBtD,GAAAA,KACOuD,EAAeD,MAAAA,CAAoBA,EAAef,CAAAA,EAEzE,OAAA,KADAF,EAAQJ,GAAM,GAAGa;YAGlB,IAAIQ,EAAed,IAAAA,EAElB,OAAA,KADAc,EAAed,IAAAA,CAAKkB,GAAkBlB,IAAAA,CAAAA,KAAK,GAAQiB;YAIhDF,EADJT,IAASM,QAAAA,CAERN,IAASA,EAAOP,CAAAA;QAEjB,QAAA,CAASO,KAAAA,CAAWA,EAAON,IAAAA;QAC5BM,EAAON,IAAAA,CAAKmB,GAAkBnB,IAAAA,CAAAA,KAAK,GAAQiB;IAC3C;IACD,SAASC,EAAiBJ,CAAAA;QACrBA,IAAAA,CACHR,IAASM,GAAAA,KACKN,EAAON,IAAAA,GACpBM,EAAON,IAAAA,CAAKmB,GAAkBnB,IAAAA,CAAAA,KAAK,GAAQiB,KAE3CE,EAAiBb,KAGlBT,EAAQJ,GAAM,GAAGa;IAElB;IACD,SAASc;QAAAA,CACJN,IAAiBtD,GAAAA,IAChBsD,EAAed,IAAAA,GAClBc,EAAed,IAAAA,CAAKkB,GAAkBlB,IAAAA,CAAAA,KAAK,GAAQiB,KAEnDC,EAAiBJ,KAGlBjB,EAAQJ,GAAM,GAAGa;IAElB;AACD;AA4OM,SAAA,EAA0BM,CAAAA,EAAMS,CAAAA;IACtC,IAAA;QACC,IAAIf,IAASM;IAGb,EAFC,OAAOJ,GAAAA;QACR,OAAOa,EAAAA,CAAU,GAAMb;IACvB;IACD,OAAIF,KAAUA,EAAON,IAAAA,GACbM,EAAON,IAAAA,CAAKqB,EAAUvB,IAAAA,CAAK,MAAA,CAAM,IAAQuB,EAAUvB,IAAAA,CAAK,MAAA,CAAM,MAE/DuB,EAAAA,CAAU,GAAOf;AACxB;AAzeqBgB,IA/DlBC,GAAYC,GAAWC,GAAYC,GA+DjBJ,IAAAA,SAAoBrC,CAAAA,EAAOC,CAAAA;IAAAA,IAAAA;QAMhD,IAAMyC,IAAsBC,iOAAAA,CAAO,GAAA;QACnCA,iOAAAA,CAAO,GAAA,GAAA,CAAiB,GAGxBL,IAAaK,iOAAAA,CAAO,GAAA,EACpBJ,IAAYI,iOAAAA,CAAO,MAAA,EACnBH,IAAaG,iOAAAA,CAAO,GAAA,EACpBF,IAAcE,iOAAAA,CAAQC,OAAAA;QAEtB,IAAMC,QAASC,2NAAAA,EAAEC,kOAAAA,EAAU;QAf8B,OAgBzDF,EAAM,GAAA,GAAa;YAAC7C;SAAAA,EAAAA,QAAAA,OAAAA,CAAAA,EAAAA;YAAAA,OAAAA,QAAAA,OAAAA,CAGIgD,EACtBhD,GACAC,KAAWgD,GAAAA,CACX,GAAA,KACAtD,GACAkD,GAAAA,CACA,GAAA,KACAlD,IAAAA,IAAAA,CAAAA,SAPKuD,CAAAA;gBAAAA,IAAAA,GAAAA,IAAAA;oBAAAA,IAUFC,EAAQD,IAAAA;wBAAAA,IAAAA,IAAAA;4BAAAA,IAAAA,IAcJE,EAASC,IAAAA,CAAKC;4BAAAA,OAAAA,IAAAA,GAAAA;wBAAAA,GAbjBC,IAAQ,GACRH,IAAWF,GAAAA,IAAAA,EAAAA;4BAAAA,OAAAA,CAAAA,CAIdE,EAASI,IAAAA,CACR,SAACC,CAAAA;gCAAAA,OAAYA,KAAmC,cAAA,OAAjBA,EAAQ1C;4BAAvC,MAEDwC,MAAU;wBApBT,GAAA,KAAA,GAAA;4BAAA,OAAA,QAAA,OAAA,CAsBiBG,QAAQC,GAAAA,CAAIP,IAAAA,IAAAA,CAAAA,SAAAA,CAAAA;gCAA9BA,IAAWQ,EAA8BC,IAAAA;4BADxC;wBAED;wBAAA,OAAA,KAAA,EAAA,IAAA,GAAA,EAAA,IAAA,CAAA,KAAA;oBAAA;gBAAA;gBAAA,OAAA,KAAA,EAAA,IAAA,GAAA,EAAA,IAAA,CAAA,SAAA,CAAA;oBAAA,OAAA,IAAA,IAKKX;gBA5BJ,KAAA,IAAA,IA4BIA;YA5BJ;QA6BH,GAAA,SAAA,CAAA,EAAA,CAAA;YA/CwD,IAkDpDP,iOAAAA,CAAO,GAAA,IAAUA,iOAAAA,CAAO,GAAA,CAAS3C,GAAO8D,IAC5CnB,iOAAAA,CAAO,GAAA,GAAiBD,GACxBoB,EAAUxF,MAAAA,GAAS,GAAA,GAAA,MAAA;YAAA,OAAA;QAAA;IAAA,EApDrB,OAAA,GAAA;QAAA,OAAA,QAAA,MAAA,CAAA;IAAA;AAAA,GAtEM2E,IAAY,CAAA,GACZa,IAAY,EAAA,EACZX,IAAU5C,MAAM4C,OAAAA,EAChBY,IAASC,OAAOD,MAAAA,EAChBT,IAAY;AAAA,SAYFW,EAAejE,CAAAA,EAAOC,CAAAA,EAASiE,CAAAA;IAM9C,IAAMxB,IAAsBC,iOAAAA,CAAO,GAAA;IACnCA,iOAAAA,CAAO,GAAA,GAAA,CAAiB,GAGxBL,IAAaK,iOAAAA,CAAO,GAAA,EACpBJ,IAAYI,iOAAAA,CAAO,MAAA,EACnBH,IAAaG,iOAAAA,CAAO,GAAA,EACpBF,IAAcE,iOAAAA,CAAQC,OAAAA;IAEtB,IAAMC,QAASC,2NAAAA,EAAEC,kOAAAA,EAAU;IAC3BF,EAAM,GAAA,GAAa;QAAC7C;KAAAA;IAEpB,IAAA;QACC,IAAMkD,IAAWF,EAChBhD,GACAC,KAAWgD,GAAAA,CACX,GAAA,KACAtD,GACAkD,GAAAA,CACA,GACAqB;QAGD,OAAIf,EAAQD,KACJA,EAASG,IAAAA,CAAKC,KAEfJ;IAaP,EAZC,OAAO3B,GAAAA;QACR,IAAIA,EAAER,IAAAA,EACL,MAAA,IAAUoD,MAAM;QAGjB,MAAM5C;IACN,CArBD,QAAA;QAwBKoB,iOAAAA,CAAO,GAAA,IAAUA,iOAAAA,CAAO,GAAA,CAAS3C,GAAO8D,IAC5CnB,iOAAAA,CAAO,GAAA,GAAiBD,GACxBoB,EAAUxF,MAAAA,GAAS;IACnB;AACD;AAoED,SAAS8F,EAAqBpE,CAAAA,EAAOC,CAAAA;IACpC,IAGIoE,GAHAC,IAA2EtE,EAAMsE,IAAAA,EAEjFC,IAAAA,CAAa;IA0CjB,OAxCIvE,EAAK,GAAA,GAAA,CACRuE,IAAAA,CAAa,GAAA,CACbF,IAAIrE,EAAK,GAAA,EACPS,KAAAA,GAAQ4D,EAAC,GAAA,IAEXA,IAAI,IAAIC,EAAKtE,EAAMG,KAAAA,EAAOF,IAG3BD,EAAK,GAAA,GAAcqE,GACnBA,EAAC,GAAA,GAAUrE,GAEXqE,EAAElE,KAAAA,GAAQH,EAAMG,KAAAA,EAChBkE,EAAEpE,OAAAA,GAAUA,GAEZoE,EAAC,GAAA,GAAA,CAAU,GAEI,QAAXA,EAAE5D,KAAAA,IAAAA,CAAe4D,EAAE5D,KAAAA,GAAQwC,CAAAA,GAEV,QAAjBoB,EAAC,GAAA,IAAA,CACJA,EAAC,GAAA,GAAeA,EAAE5D,KAAAA,GAGf6D,EAAKE,wBAAAA,GACRH,EAAE5D,KAAAA,GAAQsD,EACT,CAAA,GACAM,EAAE5D,KAAAA,EACF6D,EAAKE,wBAAAA,CAAyBH,EAAElE,KAAAA,EAAOkE,EAAE5D,KAAAA,KAEhC8D,KAAcF,EAAEI,kBAAAA,GAAAA,CAC1BJ,EAAEI,kBAAAA,IAIFJ,EAAE5D,KAAAA,GAAQ4D,EAAC,GAAA,KAAiBA,EAAE5D,KAAAA,GAAQ4D,EAAC,GAAA,GAAeA,EAAE5D,KAAAA,IAAAA,CAC7C8D,KAAcF,EAAEK,mBAAAA,IAC3BL,EAAEK,mBAAAA,IAGClC,KAAYA,EAAWxC,IAEpBqE,EAAEM,MAAAA,CAAON,EAAElE,KAAAA,EAAOkE,EAAE5D,KAAAA,EAAOR;AAClC;AAaD,SAAS+C,EACRhD,CAAAA,EACAC,CAAAA,EACA2E,CAAAA,EACAC,CAAAA,EACAhC,CAAAA,EACAiC,CAAAA,EACAC,CAAAA;IAGA,IACU,QAAT/E,KAAAA,CACU,MAAVA,KAAAA,CACU,MAAVA,KACAA,MAAUsD,GAEV,OAAOA;IAGR,IAAI0B,IAAAA,OAAmBhF;IAEvB,IAAiB,YAAbgF,GACH,OAAiB,cAAbA,IAAgC1B,IAChB,YAAb0B,IAAwB5G,EAAe4B,KAASA,IAAQsD;IAIhE,IAAIH,EAAQnD,IAAQ;QACnB,IACCiF,GADG/B,IAAWI;QAEfT,EAAM,GAAA,GAAa7C;QACnB,IAAK,IAAIvB,IAAI,GAAGA,IAAIuB,EAAM1B,MAAAA,EAAQG,IAAK;YACtC,IAAIyG,IAAQlF,CAAAA,CAAMvB,EAAAA;YAClB,IAAa,QAATyG,KAAiC,aAAA,OAATA,GAA5B;gBAEA,IAAA,GAAMC,IAAcnC,EACnBkC,GACAjF,GACA2E,GACAC,GACAhC,GACAiC,GACAC;gBAGyB,YAAA,OAAfI,IACVjC,KAAsBiC,IAAAA,CAEjBF,KAAAA,CACJA,IAAc,EAAA,GAGX/B,KAAU+B,EAAYG,IAAAA,CAAKlC,IAE/BA,IAAWI,GAEPH,EAAQgC,KAAAA,CAAAA,IACXF,CAAAA,EAAYG,IAAAA,CAAAA,KAAAA,CAAAA,GAAQD,KAEpBF,EAAYG,IAAAA,CAAKD,EAAAA;YAAAA;QAGnB;QAED,OAAIF,IAAAA,CACC/B,KAAU+B,EAAYG,IAAAA,CAAKlC,IACxB+B,CAAAA,IAGD/B;IACP;IAGD,IAAA,KAA0BvD,MAAtBK,EAAMqF,WAAAA,EAA2B,OAAO/B;IAE5CtD,EAAK,EAAA,GAAW6C,GACZP,KAAYA,EAAWtC;IAE3B,IAAIsE,IAAOtE,EAAMsE,IAAAA,EAChBnE,IAAQH,EAAMG,KAAAA;IAGf,IAAmB,cAAA,OAARmE,GAAoB;QAC9B,IACCgB,GACApC,GACAqC,GAHGC,IAAOvF;QAIX,IAAIqE,MAASvB,kOAAAA,EAAU;YAEtB,IAAI,SAAS5C,GAAO;gBAEnB,IADA,IAAIzB,IAAM4E,GACD7E,IAAI,GAAGA,IAAI0B,EAAMsF,GAAAA,CAAInH,MAAAA,EAAQG,IAGrC,IAFAC,KAAYyB,EAAMsF,GAAAA,CAAIhH,EAAAA,EAElB0B,EAAMuF,KAAAA,IAASjH,IAAI0B,EAAMuF,KAAAA,CAAMpH,MAAAA,EAAQ;oBAC1C,IAAMoC,IAAQP,EAAMuF,KAAAA,CAAMjH,EAAAA;oBAC1B,IAAa,QAATiC,GAAe;oBAIF,YAAA,OAATA,KAAAA,KACgBf,MAAtBe,EAAM2E,WAAAA,IAAAA,CAA6BlC,EAAQzC,KAe5ChC,KAAYgC,IAbZhC,KAECsE,EACCtC,GACAT,GACA2E,GACAC,GACA7E,GACA8E,GACAC;gBAMH;gBAGF,OAAOrG;YACP;YAAA,IAAU,sBAAsByB,GAGhC,OAAO,YAAS/B,EAAe+B,EAAMwF,gBAAAA,IAAoB;YAG1DzC,IAAW/C,EAAMyF;QACjB,OAAM;YAEN,IAAmB,QAAA,CADnBN,IAAchB,EAAKgB,WAAAA,GACM;gBACxB,IAAIO,IAAW5F,CAAAA,CAAQqF,EAAYQ,GAAAA,CAAAA;gBACnCN,IAAOK,IAAWA,EAAS1F,KAAAA,CAAMO,KAAAA,GAAQ4E,EAAYS;YACrD;YAED,IAAIC,IACH1B,EAAKpD,SAAAA,IAA6C,cAAA,OAAzBoD,EAAKpD,SAAAA,CAAUyD,MAAAA;YACzC,IAAIqB,GACH9C,IAA+BkB,EAAqBpE,GAAOwF,IAC3DD,IAAYvF,EAAK,GAAA;iBACX;gBACNA,EAAK,GAAA,GAAcuF,IAAgCxF,EAClDC,GACAwF;gBASD,IADA,IAAIjC,IAAQ,GACLgC,EAAS,GAAA,IAAWhC,MAAU,IACpCgC,EAAS,GAAA,GAAA,CAAU,GAEf/C,KAAYA,EAAWxC,IAE3BkD,IAAWoB,EAAK2B,IAAAA,CAAKV,GAAWpF,GAAOqF;gBAExCD,EAAS,GAAA,GAAA,CAAU;YACnB;YAMD,IAJiC,QAA7BA,EAAUW,eAAAA,IAAAA,CACbjG,IAAU8D,EAAO,CAAA,GAAI9D,GAASsF,EAAUW,eAAAA,GAAAA,GAIxCF,KACArD,iOAAAA,CAAQwD,eAAAA,IAAAA,CACP7B,EAAK8B,wBAAAA,IAA4Bb,EAAUc,iBAAAA,GAC3C;gBAQDnD,IAJa,QAAZA,KACAA,EAASoB,IAAAA,KAASvB,kOAAAA,IACF,QAAhBG,EAASoD,GAAAA,IACa,QAAtBpD,EAAS/C,KAAAA,CAAMsF,GAAAA,GACgBvC,EAAS/C,KAAAA,CAAMyF,QAAAA,GAAW1C;gBAE1D,IAAA;oBACC,OAAOF,EACNE,GACAjD,GACA2E,GACAC,GACA7E,GACA8E,GACAC;gBA2CD,EAzCC,OAAOwB,GAAAA;oBASR,OARIjC,EAAK8B,wBAAAA,IAAAA,CACRb,EAAS,GAAA,GAAejB,EAAK8B,wBAAAA,CAAyBG,EAAAA,GAGnDhB,EAAUc,iBAAAA,IACbd,EAAUc,iBAAAA,CAAkBE,GAAKtD,IAG9BsC,EAAS,GAAA,GAAA,CACZrC,IAAWkB,EAAqBpE,GAAOC,IAGN,QAAA,CAFjCsF,IAAYvF,EAAK,GAAA,EAEHkG,eAAAA,IAAAA,CACbjG,IAAU8D,EAAO,CAAA,GAAI9D,GAASsF,EAAUW,eAAAA,GAAAA,GAUlClD,EAFPE,IAJa,QAAZA,KACAA,EAASoB,IAAAA,KAASvB,kOAAAA,IACF,QAAhBG,EAASoD,GAAAA,IACa,QAAtBpD,EAAS/C,KAAAA,CAAMsF,GAAAA,GACgBvC,EAAS/C,KAAAA,CAAMyF,QAAAA,GAAW1C,GAIzDjD,GACA2E,GACAC,GACA7E,GACA8E,GACAC,EAAAA,IAIKzB;gBACP,CA9CD,QAAA;oBA+CKf,KAAWA,EAAUvC,IACzBA,EAAK,EAAA,GAAW,MAEZyC,KAAaA,EAAYzC;gBAC7B;YACD;QACD;QASDkD,IAJa,QAAZA,KACAA,EAASoB,IAAAA,KAASvB,kOAAAA,IACF,QAAhBG,EAASoD,GAAAA,IACa,QAAtBpD,EAAS/C,KAAAA,CAAMsF,GAAAA,GACgBvC,EAAS/C,KAAAA,CAAMyF,QAAAA,GAAW1C;QAE1D,IAAA;YAEC,IAAM7E,IAAM2E,EACXE,GACAjD,GACA2E,GACAC,GACA7E,GACA8E,GACAC;YASD,OANIxC,KAAWA,EAAUvC,IAEzBA,EAAK,EAAA,GAAW,MAEZ2C,iOAAAA,CAAQC,OAAAA,IAASD,iOAAAA,CAAQC,OAAAA,CAAQ5C,IAE9B3B;QAyDP,EAxDC,OAAOmI,GAAAA;YACR,IAAA,CAAK1B,KAAaC,KAAYA,EAAS0B,OAAAA,EAAS;gBAC/C,IAAIC,IAAM3B,EAAS0B,OAAAA,CAAQD,GAAOxG,GAAO,SAACkF,CAAAA;oBAAAA,OACzClC,EACCkC,GACAjF,GACA2E,GACAC,GACA7E,GACA8E,GACAC;gBARuC;gBAYzC,IAAA,KAAYpF,MAAR+G,GAAmB,OAAOA;gBAE9B,IAAIC,IAAYhE,iOAAAA,CAAO,GAAA;gBAEvB,OADIgE,KAAWA,EAAUH,GAAOxG,IACzBsD;YACP;YAED,IAAA,CAAKwB,GAAW,MAAM0B;YAEtB,IAAA,CAAKA,KAA8B,cAAA,OAAdA,EAAMzF,IAAAA,EAAoB,MAAMyF;YAgCrD,OAAOA,EAAMzF,IAAAA,CA9BgB,SAAvB6F;gBACL,IAAA;oBACC,OAAO5D,EACNE,GACAjD,GACA2E,GACAC,GACA7E,GACA8E,GACAC;gBAkBD,EAhBC,OAAOxD,GAAAA;oBACR,IAAA,CAAKA,KAAsB,cAAA,OAAVA,EAAER,IAAAA,EAAoB,MAAMQ;oBAE7C,OAAOA,EAAER,IAAAA,CACR;wBAAA,OACCiC,EACCE,GACAjD,GACA2E,GACAC,GACA7E,GACA8E,GACAC;oBARF,GAUA6B;gBAED;YACD;QAGD;IACD;IAGD,IAEChB,GAFG1G,IAAI,MAAMoF,GACbuC,IAAOvD;IAGR,IAAK,IAAIjE,KAAQc,EAAO;QACvB,IAAIW,KAAIX,CAAAA,CAAMd,EAAAA;QAEd,IAAgB,cAAA,OAALyB,MAA4B,YAATzB,KAA6B,gBAATA,GAAlD;YAIA,OAAQA;gBACP,KAAK;oBACJuG,IAAW9E;oBACX;gBAGD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACJ;gBAGD,KAAK;oBACJ,IAAI,SAASX,GAAO;oBACpBd,IAAO;oBACP;gBACD,KAAK;oBACJ,IAAI,WAAWc,GAAO;oBACtBd,IAAO;oBACP;gBAGD,KAAK;oBACJA,IAAO;oBACP;gBACD,KAAK;oBACJA,IAAO;oBACP;gBAGD,KAAK;gBACL,KAAK;oBAEJ,OADAA,IAAO,SACCiF;wBAEP,KAAK;4BACJsB,IAAW9E;4BACX;wBAGD,KAAK;4BACJ+D,IAAc/D;4BACd;wBAGD,KAAK;4BACA+D,KAAe/D,MAAO,cAAcX,KAAAA,CACvCjB,KAAQ,WAAA;oBAAA;oBAIX;gBAED,KAAK;oBACJ2H,IAAO/F,MAAKA,GAAEgG,MAAAA;oBACd;gBAGD,KAAK;oBACa,YAAA,OAANhG,MAAAA,CACVA,KAAI7B,EAAc6B,GAAAA;oBAEnB;gBACD,KAAK;oBACJzB,IAAO;oBACP;gBACD,KAAK;oBACJA,IAAO;oBACP;gBAED;oBACC,IAAIvB,EAAwBS,IAAAA,CAAKc,IAChCA,IAAOA,EAAKC,OAAAA,CAAQxB,GAAyB,SAASyB,WAAAA;yBAAAA;wBAAAA,IAC5C1B,EAAYU,IAAAA,CAAKc,IAC3B;wBAEa,QAAZA,CAAAA,CAAK,EAAA,IAAA,CAAcpB,EAAgByB,GAAAA,CAAIL,MACnC,QAALyB,KAIU8D,IACN5G,EAAeO,IAAAA,CAAKc,MAAAA,CACvBA,IACU,cAATA,IACG,aACAA,EAAKC,OAAAA,CAAQ,YAAY,OAAOC,WAAAA,EAAAA,IAE3BxB,EAAgBQ,IAAAA,CAAKc,MAAAA,CAC/BA,IAAOA,EAAKE,WAAAA,EAAAA,IATZuB,MAAQwC;oBAUR;YAAA;YAKM,QAALxC,MAAAA,CAAmB,MAANA,MAAAA,CAEf5B,IAAAA,CADS,MAAN4B,MAAcA,OAAMwC,IACnBpE,IAAI,MAAMG,IAGbH,IACA,MACAG,IACA,OAAA,CACa,YAAA,OAALyB,KAAgB1C,EAAe0C,MAAKA,KAAIwC,CAAAA,IAChD,GAAA;QA5GF;IA+GD;IAED,IAAIzF,EAAYU,IAAAA,CAAK+F,IAGpB,MAAA,IAAUH,MAASG,IAAAA,sCAAwCpF,IAAAA;IA+B5D,IA5BI2H,KAAAA,CAE2B,YAAA,OAAbjB,IAEjBiB,IAAOzI,EAAewH,KACA,QAAZA,KAAAA,CAAiC,MAAbA,KAAAA,CAAmC,MAAbA,KAAAA,CAIpDiB,IAAO7D,EACN4C,GACA3F,GAHS,UAATqE,KAA4B,oBAATA,KAA4BM,GAK/CC,GACA7E,GACA8E,GACAC,EAAAA,CAAAA,GAIExC,KAAWA,EAAUvC,IAGzBA,EAAK,EAAA,GAAW,MAEZyC,KAAaA,EAAYzC,IAAAA,CAGxB6G,KAAQE,EAAarH,GAAAA,CAAI4E,IAC7B,OAAOpF,IAAI;IAGZ,IAAM8H,KAAS,OAAO1C,IAAO,KACvB2C,KAAW/H,IAAI;IAErB,OAAIiE,EAAQ0D,KAAAA;QAAeI;KAAAA,CAAAA,MAAAA,CAAaJ,GAAAA;QAAMG;KAAAA,IACtB,YAAA,OAARH,IAAyB;QAACI;QAAUJ;QAAMG;KAAAA,GACnDC,KAAWJ,IAAOG;AACzB;AAED,IAAMD,IAAe,IAAI7I,IAAI;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CAAA,GAIYyG,IAASV,GACTiD,IAAuBjD;uCAAAA", "debugId": null}}, {"offset": {"line": 4011, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/next-auth%405.0.0-beta.29_nex_626fd582465e254ed3dcaa950f0f297e/node_modules/next-auth/lib/env.js"], "sourcesContent": ["// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\nimport { NextRequest } from \"next/server\";\nimport { setEnvDefaults as coreSetEnvDefaults } from \"@auth/core\";\n/** If `NEXTAUTH_URL` or `AUTH_URL` is defined, override the request's URL. */\nexport function reqWithEnvURL(req) {\n    const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n    if (!url)\n        return req;\n    const { origin: envOrigin } = new URL(url);\n    const { href, origin } = req.nextUrl;\n    return new NextRequest(href.replace(origin, envOrigin), req);\n}\n/**\n * For backwards compatibility, `next-auth` checks for `NEXTAUTH_URL`\n * and the `basePath` by default is `/api/auth` instead of `/auth`\n * (which is the default for all other Auth.js integrations).\n *\n * For the same reason, `NEXTAUTH_SECRET` is also checked.\n */\nexport function setEnvDefaults(config) {\n    try {\n        config.secret ?? (config.secret = process.env.AUTH_SECRET ?? process.env.NEXTAUTH_SECRET);\n        const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n        if (!url)\n            return;\n        const { pathname } = new URL(url);\n        if (pathname === \"/\")\n            return;\n        config.basePath || (config.basePath = pathname);\n    }\n    catch {\n        // Catching and swallowing potential URL parsing errors, we'll fall\n        // back to `/api/auth` below.\n    }\n    finally {\n        config.basePath || (config.basePath = \"/api/auth\");\n        coreSetEnvDefaults(process.env, config, true);\n    }\n}\n"], "names": [], "mappings": "AAAA,uFAAuF;;;;;;;AAKvE;AAJhB;AACA;AAAA;;;AAEO,SAAS,cAAc,GAAG;QACjB;IAAZ,MAAM,MAAM,CAAA,wBAAA,sTAAO,CAAC,GAAG,CAAC,QAAQ,cAApB,mCAAA,wBAAwB,sTAAO,CAAC,GAAG,CAAC,YAAY;IAC5D,IAAI,CAAC,KACD,OAAO;IACX,MAAM,EAAE,QAAQ,SAAS,EAAE,GAAG,IAAI,IAAI;IACtC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,OAAO;IACpC,OAAO,IAAI,2RAAW,CAAC,KAAK,OAAO,CAAC,QAAQ,YAAY;AAC5D;AAQO,SAAS,eAAe,MAAM;IACjC,IAAI;YACkC,0BAAlC;QAAA,CAAA,iBAAA,OAAO,MAAM,cAAb,4BAAA,iBAAkB,OAAO,MAAM,GAAG,CAAA,2BAAA,sTAAO,CAAC,GAAG,CAAC,WAAW,cAAvB,sCAAA,2BAA2B,sTAAO,CAAC,GAAG,CAAC,eAAe;YAC5E;QAAZ,MAAM,MAAM,CAAA,wBAAA,sTAAO,CAAC,GAAG,CAAC,QAAQ,cAApB,mCAAA,wBAAwB,sTAAO,CAAC,GAAG,CAAC,YAAY;QAC5D,IAAI,CAAC,KACD;QACJ,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,IAAI;QAC7B,IAAI,aAAa,KACb;QACJ,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,GAAG,QAAQ;IAClD,EACA,UAAM;IACF,mEAAmE;IACnE,6BAA6B;IACjC,SACQ;QACJ,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,GAAG,WAAW;QACjD,IAAA,kPAAkB,EAAC,sTAAO,CAAC,GAAG,EAAE,QAAQ;IAC5C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4054, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/next-auth%405.0.0-beta.29_nex_626fd582465e254ed3dcaa950f0f297e/node_modules/next-auth/lib/index.js"], "sourcesContent": ["import { Auth, createActionURL } from \"@auth/core\";\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\nimport { headers } from \"next/headers\";\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\nimport { NextResponse } from \"next/server\";\nimport { reqWithEnvURL } from \"./env.js\";\nasync function getSession(headers, config) {\n    const url = createActionURL(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const request = new Request(url, {\n        headers: { cookie: headers.get(\"cookie\") ?? \"\" },\n    });\n    return Auth(request, {\n        ...config,\n        callbacks: {\n            ...config.callbacks,\n            // Since we are server-side, we don't need to filter out the session data\n            // See https://authjs.dev/getting-started/migrating-to-v5#authenticating-server-side\n            // TODO: Taint the session data to prevent accidental leakage to the client\n            // https://react.dev/reference/react/experimental_taintObjectReference\n            async session(...args) {\n                const session = \n                // If the user defined a custom session callback, use that instead\n                (await config.callbacks?.session?.(...args)) ?? {\n                    ...args[0].session,\n                    expires: args[0].session.expires?.toISOString?.() ??\n                        args[0].session.expires,\n                };\n                const user = args[0].user ?? args[0].token;\n                return { user, ...session };\n            },\n        },\n    });\n}\nfunction isReqWrapper(arg) {\n    return typeof arg === \"function\";\n}\nexport function initAuth(config, onLazyLoad // To set the default env vars\n) {\n    if (typeof config === \"function\") {\n        return async (...args) => {\n            if (!args.length) {\n                // React Server Components\n                const _headers = await headers();\n                const _config = await config(undefined); // Review: Should we pass headers() here instead?\n                onLazyLoad?.(_config);\n                return getSession(_headers, _config).then((r) => r.json());\n            }\n            if (args[0] instanceof Request) {\n                // middleware.ts inline\n                // export { auth as default } from \"auth\"\n                const req = args[0];\n                const ev = args[1];\n                const _config = await config(req);\n                onLazyLoad?.(_config);\n                // args[0] is supposed to be NextRequest but the instanceof check is failing.\n                return handleAuth([req, ev], _config);\n            }\n            if (isReqWrapper(args[0])) {\n                // middleware.ts wrapper/route.ts\n                // import { auth } from \"auth\"\n                // export default auth((req) => { console.log(req.auth) }})\n                const userMiddlewareOrRoute = args[0];\n                return async (...args) => {\n                    const _config = await config(args[0]);\n                    onLazyLoad?.(_config);\n                    return handleAuth(args, _config, userMiddlewareOrRoute);\n                };\n            }\n            // API Routes, getServerSideProps\n            const request = \"req\" in args[0] ? args[0].req : args[0];\n            const response = \"res\" in args[0] ? args[0].res : args[1];\n            const _config = await config(request);\n            onLazyLoad?.(_config);\n            // @ts-expect-error -- request is NextRequest\n            return getSession(new Headers(request.headers), _config).then(async (authResponse) => {\n                const auth = await authResponse.json();\n                for (const cookie of authResponse.headers.getSetCookie())\n                    if (\"headers\" in response)\n                        response.headers.append(\"set-cookie\", cookie);\n                    else\n                        response.appendHeader(\"set-cookie\", cookie);\n                return auth;\n            });\n        };\n    }\n    return (...args) => {\n        if (!args.length) {\n            // React Server Components\n            return Promise.resolve(headers()).then((h) => getSession(h, config).then((r) => r.json()));\n        }\n        if (args[0] instanceof Request) {\n            // middleware.ts inline\n            // export { auth as default } from \"auth\"\n            const req = args[0];\n            const ev = args[1];\n            return handleAuth([req, ev], config);\n        }\n        if (isReqWrapper(args[0])) {\n            // middleware.ts wrapper/route.ts\n            // import { auth } from \"auth\"\n            // export default auth((req) => { console.log(req.auth) }})\n            const userMiddlewareOrRoute = args[0];\n            return async (...args) => {\n                return handleAuth(args, config, userMiddlewareOrRoute).then((res) => {\n                    return res;\n                });\n            };\n        }\n        // API Routes, getServerSideProps\n        const request = \"req\" in args[0] ? args[0].req : args[0];\n        const response = \"res\" in args[0] ? args[0].res : args[1];\n        return getSession(\n        // @ts-expect-error\n        new Headers(request.headers), config).then(async (authResponse) => {\n            const auth = await authResponse.json();\n            for (const cookie of authResponse.headers.getSetCookie())\n                if (\"headers\" in response)\n                    response.headers.append(\"set-cookie\", cookie);\n                else\n                    response.appendHeader(\"set-cookie\", cookie);\n            return auth;\n        });\n    };\n}\nasync function handleAuth(args, config, userMiddlewareOrRoute) {\n    const request = reqWithEnvURL(args[0]);\n    const sessionResponse = await getSession(request.headers, config);\n    const auth = await sessionResponse.json();\n    let authorized = true;\n    if (config.callbacks?.authorized) {\n        authorized = await config.callbacks.authorized({ request, auth });\n    }\n    let response = NextResponse.next?.();\n    if (authorized instanceof Response) {\n        // User returned a custom response, like redirecting to a page or 401, respect it\n        response = authorized;\n        const redirect = authorized.headers.get(\"Location\");\n        const { pathname } = request.nextUrl;\n        // If the user is redirecting to the same NextAuth.js action path as the current request,\n        // don't allow the redirect to prevent an infinite loop\n        if (redirect &&\n            isSameAuthAction(pathname, new URL(redirect).pathname, config)) {\n            authorized = true;\n        }\n    }\n    else if (userMiddlewareOrRoute) {\n        // Execute user's middleware/handler with the augmented request\n        const augmentedReq = request;\n        augmentedReq.auth = auth;\n        response =\n            (await userMiddlewareOrRoute(augmentedReq, args[1])) ??\n                NextResponse.next();\n    }\n    else if (!authorized) {\n        const signInPage = config.pages?.signIn ?? `${config.basePath}/signin`;\n        if (request.nextUrl.pathname !== signInPage) {\n            // Redirect to signin page by default if not authorized\n            const signInUrl = request.nextUrl.clone();\n            signInUrl.pathname = signInPage;\n            signInUrl.searchParams.set(\"callbackUrl\", request.nextUrl.href);\n            response = NextResponse.redirect(signInUrl);\n        }\n    }\n    const finalResponse = new Response(response?.body, response);\n    // Preserve cookies from the session response\n    for (const cookie of sessionResponse.headers.getSetCookie())\n        finalResponse.headers.append(\"set-cookie\", cookie);\n    return finalResponse;\n}\nfunction isSameAuthAction(requestPath, redirectPath, config) {\n    const action = redirectPath.replace(`${requestPath}/`, \"\");\n    const pages = Object.values(config.pages ?? {});\n    return ((actions.has(action) || pages.includes(redirectPath)) &&\n        redirectPath === requestPath);\n}\nconst actions = new Set([\n    \"providers\",\n    \"session\",\n    \"csrf\",\n    \"signin\",\n    \"signout\",\n    \"callback\",\n    \"verify-request\",\n    \"error\",\n]);\n"], "names": [], "mappings": ";;;;AAS+C;AAT/C;AAAA;AACA,uFAAuF;AACvF;AACA,uFAAuF;AACvF;AACA;;;;;AACA,eAAe,WAAW,OAAO,EAAE,MAAM;IACrC,MAAM,MAAM,IAAA,mPAAe,EAAC,WAC5B,mFAAmF;IACnF,QAAQ,GAAG,CAAC,sBAAsB,SAAS,sTAAO,CAAC,GAAG,EAAE;QAEjC;IADvB,MAAM,UAAU,IAAI,QAAQ,KAAK;QAC7B,SAAS;YAAE,QAAQ,CAAA,eAAA,QAAQ,GAAG,CAAC,uBAAZ,0BAAA,eAAyB;QAAG;IACnD;IACA,OAAO,IAAA,0OAAI,EAAC,SAAS;QACjB,GAAG,MAAM;QACT,WAAW;YACP,GAAG,OAAO,SAAS;YACnB,yEAAyE;YACzE,oFAAoF;YACpF,2EAA2E;YAC3E,sEAAsE;YACtE,MAAM;gBAAQ,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;oBAAG,KAAH,QAAA,SAAA,CAAA,KAAO;;oBAGV,2BAAA,mBAEM,oCAAA;oBAAA,qCAHb,kEAAkE;gBAClE;gBAFA,MAAM,UAEN,CAAA,OAAC,QAAM,oBAAA,OAAO,SAAS,cAAhB,yCAAA,4BAAA,kBAAkB,OAAO,cAAzB,gDAAA,+BAAA,sBAA+B,oBAAtC,kBAAA,OAAgD;oBAC5C,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO;oBAClB,SAAS,CAAA,uCAAA,yBAAA,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,cAAvB,8CAAA,qCAAA,uBAAyB,WAAW,cAApC,yDAAA,wCAAA,qCAAA,iDAAA,sCACL,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;gBAC/B;oBACa;gBAAb,MAAM,OAAO,CAAA,cAAA,IAAI,CAAC,EAAE,CAAC,IAAI,cAAZ,yBAAA,cAAgB,IAAI,CAAC,EAAE,CAAC,KAAK;gBAC1C,OAAO;oBAAE;oBAAM,GAAG,OAAO;gBAAC;YAC9B;QACJ;IACJ;AACJ;AACA,SAAS,aAAa,GAAG;IACrB,OAAO,OAAO,QAAQ;AAC1B;AACO,SAAS,SAAS,MAAM,EAAE,WAAW,8BAA8B;AAA/B;IAEvC,IAAI,OAAO,WAAW,YAAY;QAC9B,OAAO;6CAAU;gBAAA;;YACb,IAAI,CAAC,KAAK,MAAM,EAAE;gBACd,0BAA0B;gBAC1B,MAAM,WAAW,MAAM,IAAA,wRAAO;gBAC9B,MAAM,UAAU,MAAM,OAAO,YAAY,iDAAiD;gBAC1F,uBAAA,iCAAA,WAAa;gBACb,OAAO,WAAW,UAAU,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI;YAC3D;YACA,IAAI,IAAI,CAAC,EAAE,YAAY,SAAS;gBAC5B,uBAAuB;gBACvB,yCAAyC;gBACzC,MAAM,MAAM,IAAI,CAAC,EAAE;gBACnB,MAAM,KAAK,IAAI,CAAC,EAAE;gBAClB,MAAM,UAAU,MAAM,OAAO;gBAC7B,uBAAA,iCAAA,WAAa;gBACb,6EAA6E;gBAC7E,OAAO,WAAW;oBAAC;oBAAK;iBAAG,EAAE;YACjC;YACA,IAAI,aAAa,IAAI,CAAC,EAAE,GAAG;gBACvB,iCAAiC;gBACjC,8BAA8B;gBAC9B,2DAA2D;gBAC3D,MAAM,wBAAwB,IAAI,CAAC,EAAE;gBACrC,OAAO;qDAAU;wBAAA;;oBACb,MAAM,UAAU,MAAM,OAAO,IAAI,CAAC,EAAE;oBACpC,uBAAA,iCAAA,WAAa;oBACb,OAAO,WAAW,MAAM,SAAS;gBACrC;YACJ;YACA,iCAAiC;YACjC,MAAM,UAAU,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;YACxD,MAAM,WAAW,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;YACzD,MAAM,UAAU,MAAM,OAAO;YAC7B,uBAAA,iCAAA,WAAa;YACb,6CAA6C;YAC7C,OAAO,WAAW,IAAI,QAAQ,QAAQ,OAAO,GAAG,SAAS,IAAI,CAAC,OAAO;gBACjE,MAAM,OAAO,MAAM,aAAa,IAAI;gBACpC,KAAK,MAAM,UAAU,aAAa,OAAO,CAAC,YAAY,GAClD,IAAI,aAAa,UACb,SAAS,OAAO,CAAC,MAAM,CAAC,cAAc;qBAEtC,SAAS,YAAY,CAAC,cAAc;gBAC5C,OAAO;YACX;QACJ;IACJ;IACA,OAAO;yCAAI;YAAA;;QACP,IAAI,CAAC,KAAK,MAAM,EAAE;YACd,0BAA0B;YAC1B,OAAO,QAAQ,OAAO,CAAC,IAAA,wRAAO,KAAI,IAAI,CAAC,CAAC,IAAM,WAAW,GAAG,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI;QAC1F;QACA,IAAI,IAAI,CAAC,EAAE,YAAY,SAAS;YAC5B,uBAAuB;YACvB,yCAAyC;YACzC,MAAM,MAAM,IAAI,CAAC,EAAE;YACnB,MAAM,KAAK,IAAI,CAAC,EAAE;YAClB,OAAO,WAAW;gBAAC;gBAAK;aAAG,EAAE;QACjC;QACA,IAAI,aAAa,IAAI,CAAC,EAAE,GAAG;YACvB,iCAAiC;YACjC,8BAA8B;YAC9B,2DAA2D;YAC3D,MAAM,wBAAwB,IAAI,CAAC,EAAE;YACrC,OAAO;iDAAU;oBAAA;;gBACb,OAAO,WAAW,MAAM,QAAQ,uBAAuB,IAAI,CAAC,CAAC;oBACzD,OAAO;gBACX;YACJ;QACJ;QACA,iCAAiC;QACjC,MAAM,UAAU,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;QACxD,MAAM,WAAW,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;QACzD,OAAO,WACP,mBAAmB;QACnB,IAAI,QAAQ,QAAQ,OAAO,GAAG,QAAQ,IAAI,CAAC,OAAO;YAC9C,MAAM,OAAO,MAAM,aAAa,IAAI;YACpC,KAAK,MAAM,UAAU,aAAa,OAAO,CAAC,YAAY,GAClD,IAAI,aAAa,UACb,SAAS,OAAO,CAAC,MAAM,CAAC,cAAc;iBAEtC,SAAS,YAAY,CAAC,cAAc;YAC5C,OAAO;QACX;IACJ;AACJ;AACA,eAAe,WAAW,IAAI,EAAE,MAAM,EAAE,qBAAqB;QAKrD,mBAGW;IAPf,MAAM,UAAU,IAAA,sRAAa,EAAC,IAAI,CAAC,EAAE;IACrC,MAAM,kBAAkB,MAAM,WAAW,QAAQ,OAAO,EAAE;IAC1D,MAAM,OAAO,MAAM,gBAAgB,IAAI;IACvC,IAAI,aAAa;IACjB,KAAI,oBAAA,OAAO,SAAS,cAAhB,wCAAA,kBAAkB,UAAU,EAAE;QAC9B,aAAa,MAAM,OAAO,SAAS,CAAC,UAAU,CAAC;YAAE;YAAS;QAAK;IACnE;IACA,IAAI,YAAW,qBAAA,4RAAY,CAAC,IAAI,cAAjB,yCAAA,wBAAA,4RAAY;IAC3B,IAAI,sBAAsB,UAAU;QAChC,iFAAiF;QACjF,WAAW;QACX,MAAM,WAAW,WAAW,OAAO,CAAC,GAAG,CAAC;QACxC,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;QACpC,yFAAyF;QACzF,uDAAuD;QACvD,IAAI,YACA,iBAAiB,UAAU,IAAI,IAAI,UAAU,QAAQ,EAAE,SAAS;YAChE,aAAa;QACjB;IACJ,OACK,IAAI,uBAAuB;QAC5B,+DAA+D;QAC/D,MAAM,eAAe;QACrB,aAAa,IAAI,GAAG;YAEhB;QADJ,WACI,CAAA,OAAC,MAAM,sBAAsB,cAAc,IAAI,CAAC,EAAE,eAAlD,kBAAA,OACI,4RAAY,CAAC,IAAI;IAC7B,OACK,IAAI,CAAC,YAAY;YACC;YAAA;QAAnB,MAAM,aAAa,CAAA,wBAAA,gBAAA,OAAO,KAAK,cAAZ,oCAAA,cAAc,MAAM,cAApB,kCAAA,uBAAwB,AAAC,GAAkB,OAAhB,OAAO,QAAQ,EAAC;QAC9D,IAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,YAAY;YACzC,uDAAuD;YACvD,MAAM,YAAY,QAAQ,OAAO,CAAC,KAAK;YACvC,UAAU,QAAQ,GAAG;YACrB,UAAU,YAAY,CAAC,GAAG,CAAC,eAAe,QAAQ,OAAO,CAAC,IAAI;YAC9D,WAAW,4RAAY,CAAC,QAAQ,CAAC;QACrC;IACJ;IACA,MAAM,gBAAgB,IAAI,SAAS,qBAAA,+BAAA,SAAU,IAAI,EAAE;IACnD,6CAA6C;IAC7C,KAAK,MAAM,UAAU,gBAAgB,OAAO,CAAC,YAAY,GACrD,cAAc,OAAO,CAAC,MAAM,CAAC,cAAc;IAC/C,OAAO;AACX;AACA,SAAS,iBAAiB,WAAW,EAAE,YAAY,EAAE,MAAM;IACvD,MAAM,SAAS,aAAa,OAAO,CAAC,AAAC,GAAc,OAAZ,aAAY,MAAI;QAC3B;IAA5B,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAA,gBAAA,OAAO,KAAK,cAAZ,2BAAA,gBAAgB,CAAC;IAC7C,OAAQ,CAAC,QAAQ,GAAG,CAAC,WAAW,MAAM,QAAQ,CAAC,aAAa,KACxD,iBAAiB;AACzB;AACA,MAAM,UAAU,IAAI,IAAI;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/next-auth%405.0.0-beta.29_nex_626fd582465e254ed3dcaa950f0f297e/node_modules/next-auth/lib/actions.js"], "sourcesContent": ["import { Auth, raw, skipCSR<PERSON>heck, createActionURL } from \"@auth/core\";\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\nimport { headers as nextHeaders, cookies } from \"next/headers\";\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\nimport { redirect } from \"next/navigation\";\nexport async function signIn(provider, options = {}, authorizationParams, config) {\n    const headers = new Headers(await nextHeaders());\n    const { redirect: shouldRedirect = true, redirectTo, ...rest } = options instanceof FormData ? Object.fromEntries(options) : options;\n    const callbackUrl = redirectTo?.toString() ?? headers.get(\"Referer\") ?? \"/\";\n    const signInURL = createActionURL(\"signin\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    if (!provider) {\n        signInURL.searchParams.append(\"callbackUrl\", callbackUrl);\n        if (shouldRedirect)\n            redirect(signInURL.toString());\n        return signInURL.toString();\n    }\n    let url = `${signInURL}/${provider}?${new URLSearchParams(authorizationParams)}`;\n    let foundProvider = {};\n    for (const providerConfig of config.providers) {\n        const { options, ...defaults } = typeof providerConfig === \"function\" ? providerConfig() : providerConfig;\n        const id = options?.id ?? defaults.id;\n        if (id === provider) {\n            foundProvider = {\n                id,\n                type: options?.type ?? defaults.type,\n            };\n            break;\n        }\n    }\n    if (!foundProvider.id) {\n        const url = `${signInURL}?${new URLSearchParams({ callbackUrl })}`;\n        if (shouldRedirect)\n            redirect(url);\n        return url;\n    }\n    if (foundProvider.type === \"credentials\") {\n        url = url.replace(\"signin\", \"callback\");\n    }\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const body = new URLSearchParams({ ...rest, callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await Auth(req, { ...config, raw, skipCSRFCheck });\n    const cookieJar = await cookies();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    const responseUrl = res instanceof Response ? res.headers.get(\"Location\") : res.redirect;\n    // NOTE: if for some unexpected reason the responseUrl is not set,\n    // we redirect to the original url\n    const redirectUrl = responseUrl ?? url;\n    if (shouldRedirect)\n        return redirect(redirectUrl);\n    return redirectUrl;\n}\nexport async function signOut(options, config) {\n    const headers = new Headers(await nextHeaders());\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const url = createActionURL(\"signout\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const callbackUrl = options?.redirectTo ?? headers.get(\"Referer\") ?? \"/\";\n    const body = new URLSearchParams({ callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await Auth(req, { ...config, raw, skipCSRFCheck });\n    const cookieJar = await cookies();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    if (options?.redirect ?? true)\n        return redirect(res.redirect);\n    return res;\n}\nexport async function update(data, config) {\n    const headers = new Headers(await nextHeaders());\n    headers.set(\"Content-Type\", \"application/json\");\n    const url = createActionURL(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const body = JSON.stringify({ data });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await Auth(req, { ...config, raw, skipCSRFCheck });\n    const cookieJar = await cookies();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    return res.body;\n}\n"], "names": [], "mappings": ";;;;;;;;AAW+C;AAX/C;AAAA;AAAA;AACA,uFAAuF;AACvF;AACA,uFAAuF;AACvF;;;;AACO,eAAe,OAAO,QAAQ;QAAE,UAAA,iEAAU,CAAC,GAAG,oEAAqB;IACtE,MAAM,UAAU,IAAI,QAAQ,MAAM,IAAA,wRAAW;IAC7C,MAAM,EAAE,UAAU,iBAAiB,IAAI,EAAE,UAAU,EAAE,GAAG,MAAM,GAAG,mBAAmB,WAAW,OAAO,WAAW,CAAC,WAAW;QACzG,sBAAA;IAApB,MAAM,cAAc,CAAA,OAAA,CAAA,uBAAA,uBAAA,iCAAA,WAAY,QAAQ,gBAApB,kCAAA,uBAA0B,QAAQ,GAAG,CAAC,wBAAtC,kBAAA,OAAoD;IACxE,MAAM,YAAY,IAAA,mPAAe,EAAC,UAClC,mFAAmF;IACnF,QAAQ,GAAG,CAAC,sBAAsB,SAAS,sTAAO,CAAC,GAAG,EAAE;IACxD,IAAI,CAAC,UAAU;QACX,UAAU,YAAY,CAAC,MAAM,CAAC,eAAe;QAC7C,IAAI,gBACA,IAAA,4RAAQ,EAAC,UAAU,QAAQ;QAC/B,OAAO,UAAU,QAAQ;IAC7B;IACA,IAAI,MAAM,AAAC,GAAe,OAAb,WAAU,KAAe,OAAZ,UAAS,KAA4C,OAAzC,IAAI,gBAAgB;IAC1D,IAAI,gBAAgB,CAAC;IACrB,KAAK,MAAM,kBAAkB,OAAO,SAAS,CAAE;QAC3C,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,GAAG,OAAO,mBAAmB,aAAa,mBAAmB;YAChF;QAAX,MAAM,KAAK,CAAA,cAAA,oBAAA,8BAAA,QAAS,EAAE,cAAX,yBAAA,cAAe,SAAS,EAAE;QACrC,IAAI,OAAO,UAAU;gBAGP;YAFV,gBAAgB;gBACZ;gBACA,MAAM,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB,SAAS,IAAI;YACxC;YACA;QACJ;IACJ;IACA,IAAI,CAAC,cAAc,EAAE,EAAE;QACnB,MAAM,MAAM,AAAC,GAAe,OAAb,WAAU,KAAwC,OAArC,IAAI,gBAAgB;YAAE;QAAY;QAC9D,IAAI,gBACA,IAAA,4RAAQ,EAAC;QACb,OAAO;IACX;IACA,IAAI,cAAc,IAAI,KAAK,eAAe;QACtC,MAAM,IAAI,OAAO,CAAC,UAAU;IAChC;IACA,QAAQ,GAAG,CAAC,gBAAgB;IAC5B,MAAM,OAAO,IAAI,gBAAgB;QAAE,GAAG,IAAI;QAAE;IAAY;IACxD,MAAM,MAAM,IAAI,QAAQ,KAAK;QAAE,QAAQ;QAAQ;QAAS;IAAK;IAC7D,MAAM,MAAM,MAAM,IAAA,0OAAI,EAAC,KAAK;QAAE,GAAG,MAAM;QAAE,KAAA,kOAAG;QAAE,eAAA,4OAAa;IAAC;IAC5D,MAAM,YAAY,MAAM,IAAA,wRAAO;QACf;IAAhB,KAAK,MAAM,KAAK,CAAA,eAAA,gBAAA,0BAAA,IAAK,OAAO,cAAZ,0BAAA,eAAgB,EAAE,CAC9B,UAAU,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,OAAO;IAC5C,MAAM,cAAc,eAAe,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,QAAQ;IACxF,kEAAkE;IAClE,kCAAkC;IAClC,MAAM,cAAc,wBAAA,yBAAA,cAAe;IACnC,IAAI,gBACA,OAAO,IAAA,4RAAQ,EAAC;IACpB,OAAO;AACX;AACO,eAAe,QAAQ,OAAO,EAAE,MAAM;IACzC,MAAM,UAAU,IAAI,QAAQ,MAAM,IAAA,wRAAW;IAC7C,QAAQ,GAAG,CAAC,gBAAgB;IAC5B,MAAM,MAAM,IAAA,mPAAe,EAAC,WAC5B,mFAAmF;IACnF,QAAQ,GAAG,CAAC,sBAAsB,SAAS,sTAAO,CAAC,GAAG,EAAE;QACpC,qBAAA;IAApB,MAAM,cAAc,CAAA,OAAA,CAAA,sBAAA,oBAAA,8BAAA,QAAS,UAAU,cAAnB,iCAAA,sBAAuB,QAAQ,GAAG,CAAC,wBAAnC,kBAAA,OAAiD;IACrE,MAAM,OAAO,IAAI,gBAAgB;QAAE;IAAY;IAC/C,MAAM,MAAM,IAAI,QAAQ,KAAK;QAAE,QAAQ;QAAQ;QAAS;IAAK;IAC7D,MAAM,MAAM,MAAM,IAAA,0OAAI,EAAC,KAAK;QAAE,GAAG,MAAM;QAAE,KAAA,kOAAG;QAAE,eAAA,4OAAa;IAAC;IAC5D,MAAM,YAAY,MAAM,IAAA,wRAAO;QACf;IAAhB,KAAK,MAAM,KAAK,CAAA,eAAA,gBAAA,0BAAA,IAAK,OAAO,cAAZ,0BAAA,eAAgB,EAAE,CAC9B,UAAU,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,OAAO;QACxC;IAAJ,IAAI,CAAA,oBAAA,oBAAA,8BAAA,QAAS,QAAQ,cAAjB,+BAAA,oBAAqB,MACrB,OAAO,IAAA,4RAAQ,EAAC,IAAI,QAAQ;IAChC,OAAO;AACX;AACO,eAAe,OAAO,IAAI,EAAE,MAAM;IACrC,MAAM,UAAU,IAAI,QAAQ,MAAM,IAAA,wRAAW;IAC7C,QAAQ,GAAG,CAAC,gBAAgB;IAC5B,MAAM,MAAM,IAAA,mPAAe,EAAC,WAC5B,mFAAmF;IACnF,QAAQ,GAAG,CAAC,sBAAsB,SAAS,sTAAO,CAAC,GAAG,EAAE;IACxD,MAAM,OAAO,KAAK,SAAS,CAAC;QAAE;IAAK;IACnC,MAAM,MAAM,IAAI,QAAQ,KAAK;QAAE,QAAQ;QAAQ;QAAS;IAAK;IAC7D,MAAM,MAAM,MAAM,IAAA,0OAAI,EAAC,KAAK;QAAE,GAAG,MAAM;QAAE,KAAA,kOAAG;QAAE,eAAA,4OAAa;IAAC;IAC5D,MAAM,YAAY,MAAM,IAAA,wRAAO;QACf;IAAhB,KAAK,MAAM,KAAK,CAAA,eAAA,gBAAA,0BAAA,IAAK,OAAO,cAAZ,0BAAA,eAAgB,EAAE,CAC9B,UAAU,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,OAAO;IAC5C,OAAO,IAAI,IAAI;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/next-auth%405.0.0-beta.29_nex_626fd582465e254ed3dcaa950f0f297e/node_modules/next-auth/index.js"], "sourcesContent": ["/**\n * _If you are looking to migrate from v4, visit the [Upgrade Guide (v5)](https://authjs.dev/getting-started/migrating-to-v5)._\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install next-auth@beta\n * ```\n *\n * ## Environment variable inference\n *\n * `NEXTAUTH_URL` and `NEXTAUTH_SECRET` have been inferred since v4.\n *\n * Since NextAuth.js v5 can also automatically infer environment variables that are prefixed with `AUTH_`.\n *\n * For example `AUTH_GITHUB_ID` and `AUTH_GITHUB_SECRET` will be used as the `clientId` and `clientSecret` options for the GitHub provider.\n *\n * :::tip\n * The environment variable name inferring has the following format for OAuth providers: `AUTH_{PROVIDER}_{ID|SECRET}`.\n *\n * `PROVIDER` is the uppercase snake case version of the provider's id, followed by either `ID` or `SECRET` respectively.\n * :::\n *\n * `AUTH_SECRET` and `AUTH_URL` are also aliased for `NEXTAUTH_SECRET` and `NEXTAUTH_URL` for consistency.\n *\n * To add social login to your app, the configuration becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth({ providers: [ GitHub ] })\n * ```\n *\n * And the `.env.local` file:\n *\n * ```sh title=\".env.local\"\n * AUTH_GITHUB_ID=...\n * AUTH_GITHUB_SECRET=...\n * AUTH_SECRET=...\n * ```\n *\n * :::tip\n * In production, `AUTH_SECRET` is a required environment variable - if not set, NextAuth.js will throw an error. See [MissingSecretError](https://authjs.dev/reference/core/errors#missingsecret) for more details.\n * :::\n *\n * If you need to override the default values for a provider, you can still call it as a function `GitHub({...})` as before.\n *\n * ## Lazy initialization\n * You can also initialize NextAuth.js lazily (previously known as advanced intialization), which allows you to access the request context in the configuration in some cases, like Route Handlers, Middleware, API Routes or `getServerSideProps`.\n * The above example becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth(req => {\n *  if (req) {\n *   console.log(req) // do something with the request\n *  }\n *  return { providers: [ GitHub ] }\n * })\n * ```\n *\n * :::tip\n * This is useful if you want to customize the configuration based on the request, for example, to add a different provider in staging/dev environments.\n * :::\n *\n * @module next-auth\n */\nimport { Auth, customFetch } from \"@auth/core\";\nimport { reqWithEnvURL, setEnvDefaults } from \"./lib/env.js\";\nimport { initAuth } from \"./lib/index.js\";\nimport { signIn, signOut, update } from \"./lib/actions.js\";\nexport { AuthError, CredentialsSignin } from \"@auth/core/errors\";\nexport { customFetch };\n/**\n *  Initialize NextAuth.js.\n *\n *  @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth({ providers: [GitHub] })\n * ```\n *\n * Lazy initialization:\n *\n * @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth(async (req) => {\n *   console.log(req) // do something with the request\n *   return {\n *     providers: [GitHub],\n *   },\n * })\n * ```\n */\nexport default function NextAuth(config) {\n    if (typeof config === \"function\") {\n        const httpHandler = async (req) => {\n            const _config = await config(req);\n            setEnvDefaults(_config);\n            return Auth(reqWithEnvURL(req), _config);\n        };\n        return {\n            handlers: { GET: httpHandler, POST: httpHandler },\n            // @ts-expect-error\n            auth: initAuth(config, (c) => setEnvDefaults(c)),\n            signIn: async (provider, options, authorizationParams) => {\n                const _config = await config(undefined);\n                setEnvDefaults(_config);\n                return signIn(provider, options, authorizationParams, _config);\n            },\n            signOut: async (options) => {\n                const _config = await config(undefined);\n                setEnvDefaults(_config);\n                return signOut(options, _config);\n            },\n            unstable_update: async (data) => {\n                const _config = await config(undefined);\n                setEnvDefaults(_config);\n                return update(data, _config);\n            },\n        };\n    }\n    setEnvDefaults(config);\n    const httpHandler = (req) => Auth(reqWithEnvURL(req), config);\n    return {\n        handlers: { GET: httpHandler, POST: httpHandler },\n        // @ts-expect-error\n        auth: initAuth(config),\n        signIn: (provider, options, authorizationParams) => {\n            return signIn(provider, options, authorizationParams, config);\n        },\n        signOut: (options) => {\n            return signOut(options, config);\n        },\n        unstable_update: (data) => {\n            return update(data, config);\n        },\n    };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmEC;;;;AACD;AAAA;AACA;AACA;AACA;AACA;;;;;;;AA4Be,SAAS,SAAS,MAAM;IACnC,IAAI,OAAO,WAAW,YAAY;QAC9B,MAAM,cAAc,OAAO;YACvB,MAAM,UAAU,MAAM,OAAO;YAC7B,IAAA,uRAAc,EAAC;YACf,OAAO,IAAA,0OAAI,EAAC,IAAA,sRAAa,EAAC,MAAM;QACpC;QACA,OAAO;YACH,UAAU;gBAAE,KAAK;gBAAa,MAAM;YAAY;YAChD,mBAAmB;YACnB,MAAM,IAAA,mRAAQ,EAAC,QAAQ,CAAC,IAAM,IAAA,uRAAc,EAAC;YAC7C,QAAQ,OAAO,UAAU,SAAS;gBAC9B,MAAM,UAAU,MAAM,OAAO;gBAC7B,IAAA,uRAAc,EAAC;gBACf,OAAO,IAAA,mRAAM,EAAC,UAAU,SAAS,qBAAqB;YAC1D;YACA,SAAS,OAAO;gBACZ,MAAM,UAAU,MAAM,OAAO;gBAC7B,IAAA,uRAAc,EAAC;gBACf,OAAO,IAAA,oRAAO,EAAC,SAAS;YAC5B;YACA,iBAAiB,OAAO;gBACpB,MAAM,UAAU,MAAM,OAAO;gBAC7B,IAAA,uRAAc,EAAC;gBACf,OAAO,IAAA,mRAAM,EAAC,MAAM;YACxB;QACJ;IACJ;IACA,IAAA,uRAAc,EAAC;IACf,MAAM,cAAc,CAAC,MAAQ,IAAA,0OAAI,EAAC,IAAA,sRAAa,EAAC,MAAM;IACtD,OAAO;QACH,UAAU;YAAE,KAAK;YAAa,MAAM;QAAY;QAChD,mBAAmB;QACnB,MAAM,IAAA,mRAAQ,EAAC;QACf,QAAQ,CAAC,UAAU,SAAS;YACxB,OAAO,IAAA,mRAAM,EAAC,UAAU,SAAS,qBAAqB;QAC1D;QACA,SAAS,CAAC;YACN,OAAO,IAAA,oRAAO,EAAC,SAAS;QAC5B;QACA,iBAAiB,CAAC;YACd,OAAO,IAAA,mRAAM,EAAC,MAAM;QACxB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4550, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/next-auth%405.0.0-beta.29_nex_626fd582465e254ed3dcaa950f0f297e/node_modules/next-auth/providers/credentials.js"], "sourcesContent": ["export * from \"@auth/core/providers/credentials\";\nexport { default } from \"@auth/core/providers/credentials\";\n"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}]}