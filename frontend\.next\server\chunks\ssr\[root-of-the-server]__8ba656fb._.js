module.exports=[18622,(a,b,c)=>{b.exports=a.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},39665,(a,b,c)=>{"use strict";b.exports=a.r(18622)},68116,(a,b,c)=>{"use strict";b.exports=a.r(39665).vendored["react-ssr"].ReactJsxRuntime},128,(a,b,c)=>{"use strict";b.exports=a.r(39665).vendored["react-ssr"].React},81223,89610,a=>{"use strict";a.s(["SessionContext",()=>W,"SessionProvider",()=>ab,"__NEXTAUTH",()=>R,"getCsrfToken",()=>Z,"getProviders",()=>$,"getSession",()=>Y,"signIn",()=>_,"signOut",()=>aa,"useSession",()=>X],81223);var b=a.i(68116),c=a.i(128);a.s(["AccessDenied",()=>g,"AccountNotLinked",()=>J,"AdapterError",()=>f,"AuthError",()=>d,"CallbackRouteError",()=>h,"CredentialsSignin",()=>l,"DuplicateConditionalUI",()=>G,"ErrorPageLoop",()=>i,"EventError",()=>j,"ExperimentalFeatureNotEnabled",()=>K,"InvalidCallbackUrl",()=>k,"InvalidCheck",()=>n,"InvalidEndpoints",()=>m,"InvalidProvider",()=>A,"JWTSessionError",()=>o,"MissingAdapter",()=>p,"MissingAdapterMethods",()=>q,"MissingAuthorize",()=>r,"MissingCSRF",()=>D,"MissingSecret",()=>s,"MissingWebAuthnAutocomplete",()=>H,"OAuthAccountNotLinked",()=>t,"OAuthCallbackError",()=>u,"OAuthProfileParseError",()=>v,"SessionTokenError",()=>w,"SignOutError",()=>x,"UnknownAction",()=>y,"UnsupportedStrategy",()=>z,"UntrustedHost",()=>B,"Verification",()=>C,"WebAuthnVerificationError",()=>I,"isClientError",()=>F],89610);class d extends Error{constructor(a,b){a instanceof Error?super(void 0,{cause:{err:a,...a.cause,...b}}):"string"==typeof a?(b instanceof Error&&(b={err:b,...b.cause}),super(a,b)):super(void 0,a),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let c=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${c}`}}class e extends d{}e.kind="signIn";class f extends d{}f.type="AdapterError";class g extends d{}g.type="AccessDenied";class h extends d{}h.type="CallbackRouteError";class i extends d{}i.type="ErrorPageLoop";class j extends d{}j.type="EventError";class k extends d{}k.type="InvalidCallbackUrl";class l extends e{constructor(){super(...arguments),this.code="credentials"}}l.type="CredentialsSignin";class m extends d{}m.type="InvalidEndpoints";class n extends d{}n.type="InvalidCheck";class o extends d{}o.type="JWTSessionError";class p extends d{}p.type="MissingAdapter";class q extends d{}q.type="MissingAdapterMethods";class r extends d{}r.type="MissingAuthorize";class s extends d{}s.type="MissingSecret";class t extends e{}t.type="OAuthAccountNotLinked";class u extends e{}u.type="OAuthCallbackError";class v extends d{}v.type="OAuthProfileParseError";class w extends d{}w.type="SessionTokenError";class x extends d{}x.type="SignOutError";class y extends d{}y.type="UnknownAction";class z extends d{}z.type="UnsupportedStrategy";class A extends d{}A.type="InvalidProvider";class B extends d{}B.type="UntrustedHost";class C extends d{}C.type="Verification";class D extends e{}D.type="MissingCSRF";let E=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);function F(a){return a instanceof d&&E.has(a.type)}class G extends d{}G.type="DuplicateConditionalUI";class H extends d{}H.type="MissingWebAuthnAutocomplete";class I extends d{}I.type="WebAuthnVerificationError";class J extends e{}J.type="AccountNotLinked";class K extends d{}K.type="ExperimentalFeatureNotEnabled";class L extends d{}class M extends d{}async function N(a,b,c,d={}){let e=`${O(b)}/${a}`;try{let a={headers:{"Content-Type":"application/json",...d?.headers?.cookie?{cookie:d.headers.cookie}:{}}};d?.body&&(a.body=JSON.stringify(d.body),a.method="POST");let b=await fetch(e,a),c=await b.json();if(!b.ok)throw c;return c}catch(a){return c.error(new L(a.message,a)),null}}function O(a){return`${a.baseUrlServer}${a.basePathServer}`}function P(){return Math.floor(Date.now()/1e3)}function Q(a){let b=new URL("http://localhost:3000/api/auth");a&&!a.startsWith("http")&&(a=`https://${a}`);let c=new URL(a||b),d=("/"===c.pathname?b.pathname:c.pathname).replace(/\/$/,""),e=`${c.origin}${d}`;return{origin:c.origin,host:c.host,path:d,base:e,toString:()=>e}}let R={baseUrl:Q(process.env.NEXTAUTH_URL??process.env.VERCEL_URL).origin,basePath:Q(process.env.NEXTAUTH_URL).path,baseUrlServer:Q(process.env.NEXTAUTH_URL_INTERNAL??process.env.NEXTAUTH_URL??process.env.VERCEL_URL).origin,basePathServer:Q(process.env.NEXTAUTH_URL_INTERNAL??process.env.NEXTAUTH_URL).path,_lastSync:0,_session:void 0,_getSession:()=>{}},S=null;function T(){return"undefined"==typeof BroadcastChannel?{postMessage:()=>{},addEventListener:()=>{},removeEventListener:()=>{},name:"next-auth",onmessage:null,onmessageerror:null,close:()=>{},dispatchEvent:()=>!1}:new BroadcastChannel("next-auth")}function U(){return null===S&&(S=T()),S}let V={debug:console.debug,error:console.error,warn:console.warn},W=c.createContext?.(void 0);function X(a){if(!W)throw Error("React Context is unavailable in Server Components");let b=c.useContext(W),{required:d,onUnauthenticated:e}=a??{},f=d&&"unauthenticated"===b.status;return(c.useEffect(()=>{if(f){let a=`${R.basePath}/signin?${new URLSearchParams({error:"SessionRequired",callbackUrl:window.location.href})}`;e?e():window.location.href=a}},[f,e]),f)?{data:b.data,update:b.update,status:"loading"}:b}async function Y(a){let b=await N("session",R,V,a);return(a?.broadcast??!0)&&T().postMessage({event:"session",data:{trigger:"getSession"}}),b}async function Z(){let a=await N("csrf",R,V);return a?.csrfToken??""}async function $(){return N("providers",R,V)}async function _(a,b,c){let{callbackUrl:d,...e}=b??{},{redirect:f=!0,redirectTo:g=d??window.location.href,...h}=e,i=O(R),j=await $();if(!j){let a=`${i}/error`;window.location.href=a;return}if(!a||!j[a]){let a=`${i}/signin?${new URLSearchParams({callbackUrl:g})}`;window.location.href=a;return}let k=j[a].type;if("webauthn"===k)throw TypeError(`Provider id "${a}" refers to a WebAuthn provider.
Please use \`import { signIn } from "next-auth/webauthn"\` instead.`);let l=`${i}/${"credentials"===k?"callback":"signin"}/${a}`,m=await Z(),n=await fetch(`${l}?${new URLSearchParams(c)}`,{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({...h,csrfToken:m,callbackUrl:g})}),o=await n.json();if(f){let a=o.url??g;window.location.href=a,a.includes("#")&&window.location.reload();return}let p=new URL(o.url).searchParams.get("error")??void 0,q=new URL(o.url).searchParams.get("code")??void 0;return n.ok&&await R._getSession({event:"storage"}),{error:p,code:q,status:n.status,ok:n.ok,url:p?null:o.url}}async function aa(a){let{redirect:b=!0,redirectTo:c=a?.callbackUrl??window.location.href}=a??{},d=O(R),e=await Z(),f=await fetch(`${d}/signout`,{method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded","X-Auth-Return-Redirect":"1"},body:new URLSearchParams({csrfToken:e,callbackUrl:c})}),g=await f.json();if(U().postMessage({event:"session",data:{trigger:"signout"}}),b){let a=g.url??c;window.location.href=a,a.includes("#")&&window.location.reload();return}return await R._getSession({event:"storage"}),g}function ab(a){if(!W)throw Error("React Context is unavailable in Server Components");let{children:d,basePath:e,refetchInterval:f,refetchWhenOffline:g}=a;e&&(R.basePath=e);let h=void 0!==a.session;R._lastSync=h?P():0;let[i,j]=c.useState(()=>(h&&(R._session=a.session),a.session)),[k,l]=c.useState(!h);c.useEffect(()=>(R._getSession=async({event:a}={})=>{try{let b="storage"===a;if(b||void 0===R._session){R._lastSync=P(),R._session=await Y({broadcast:!b}),j(R._session);return}if(!a||null===R._session||P()<R._lastSync)return;R._lastSync=P(),R._session=await Y(),j(R._session)}catch(a){V.error(new M(a.message,a))}finally{l(!1)}},R._getSession(),()=>{R._lastSync=0,R._session=void 0,R._getSession=()=>{}}),[]),c.useEffect(()=>{let a=()=>R._getSession({event:"storage"});return U().addEventListener("message",a),()=>U().removeEventListener("message",a)},[]),c.useEffect(()=>{let{refetchOnWindowFocus:b=!0}=a,c=()=>{b&&"visible"===document.visibilityState&&R._getSession({event:"visibilitychange"})};return document.addEventListener("visibilitychange",c,!1),()=>document.removeEventListener("visibilitychange",c,!1)},[a.refetchOnWindowFocus]);let m=function(){let[a,b]=c.useState("undefined"!=typeof navigator&&navigator.onLine),d=()=>b(!0),e=()=>b(!1);return c.useEffect(()=>(window.addEventListener("online",d),window.addEventListener("offline",e),()=>{window.removeEventListener("online",d),window.removeEventListener("offline",e)}),[]),a}(),n=!1!==g||m;c.useEffect(()=>{if(f&&n){let a=setInterval(()=>{R._session&&R._getSession({event:"poll"})},1e3*f);return()=>clearInterval(a)}},[f,n]);let o=c.useMemo(()=>({data:i,status:k?"loading":i?"authenticated":"unauthenticated",async update(a){if(k)return;l(!0);let b=await N("session",R,V,void 0===a?void 0:{body:{csrfToken:await Z(),data:a}});return l(!1),b&&(j(b),U().postMessage({event:"session",data:{trigger:"getSession"}})),b}}),[i,k]);return(0,b.jsx)(W.Provider,{value:o,children:d})}},34554,a=>{"use strict";a.s(["default",()=>u],34554);var b=a.i(68116),c=a.i(81223),d=a.i(70208),e=function(){return null},f=a.i(128),g=a.i(84536),h=a.i(25405),i=a.i(99731),j=a.i(20475),k=class extends j.Subscribable{constructor(a={}){super(),this.config=a,this.#a=new Map}#a;build(a,b,c){let d=b.queryKey,e=b.queryHash??(0,g.hashQueryKeyByOptions)(d,b),f=this.get(e);return f||(f=new h.Query({client:a,queryKey:d,queryHash:e,options:a.defaultQueryOptions(b),state:c,defaultOptions:a.getQueryDefaults(d)}),this.add(f)),f}add(a){this.#a.has(a.queryHash)||(this.#a.set(a.queryHash,a),this.notify({type:"added",query:a}))}remove(a){let b=this.#a.get(a.queryHash);b&&(a.destroy(),b===a&&this.#a.delete(a.queryHash),this.notify({type:"removed",query:a}))}clear(){i.notifyManager.batch(()=>{this.getAll().forEach(a=>{this.remove(a)})})}get(a){return this.#a.get(a)}getAll(){return[...this.#a.values()]}find(a){let b={exact:!0,...a};return this.getAll().find(a=>(0,g.matchQuery)(b,a))}findAll(a={}){let b=this.getAll();return Object.keys(a).length>0?b.filter(b=>(0,g.matchQuery)(a,b)):b}notify(a){i.notifyManager.batch(()=>{this.listeners.forEach(b=>{b(a)})})}onFocus(){i.notifyManager.batch(()=>{this.getAll().forEach(a=>{a.onFocus()})})}onOnline(){i.notifyManager.batch(()=>{this.getAll().forEach(a=>{a.onOnline()})})}},l=a.i(61289),m=j,n=class extends m.Subscribable{constructor(a={}){super(),this.config=a,this.#b=new Set,this.#c=new Map,this.#d=0}#b;#c;#d;build(a,b,c){let d=new l.Mutation({mutationCache:this,mutationId:++this.#d,options:a.defaultMutationOptions(b),state:c});return this.add(d),d}add(a){this.#b.add(a);let b=o(a);if("string"==typeof b){let c=this.#c.get(b);c?c.push(a):this.#c.set(b,[a])}this.notify({type:"added",mutation:a})}remove(a){if(this.#b.delete(a)){let b=o(a);if("string"==typeof b){let c=this.#c.get(b);if(c)if(c.length>1){let b=c.indexOf(a);-1!==b&&c.splice(b,1)}else c[0]===a&&this.#c.delete(b)}}this.notify({type:"removed",mutation:a})}canRun(a){let b=o(a);if("string"!=typeof b)return!0;{let c=this.#c.get(b),d=c?.find(a=>"pending"===a.state.status);return!d||d===a}}runNext(a){let b=o(a);if("string"!=typeof b)return Promise.resolve();{let c=this.#c.get(b)?.find(b=>b!==a&&b.state.isPaused);return c?.continue()??Promise.resolve()}}clear(){i.notifyManager.batch(()=>{this.#b.forEach(a=>{this.notify({type:"removed",mutation:a})}),this.#b.clear(),this.#c.clear()})}getAll(){return Array.from(this.#b)}find(a){let b={exact:!0,...a};return this.getAll().find(a=>(0,g.matchMutation)(b,a))}findAll(a={}){return this.getAll().filter(b=>(0,g.matchMutation)(a,b))}notify(a){i.notifyManager.batch(()=>{this.listeners.forEach(b=>{b(a)})})}resumePausedMutations(){let a=this.getAll().filter(a=>a.state.isPaused);return i.notifyManager.batch(()=>Promise.all(a.map(a=>a.continue().catch(g.noop))))}};function o(a){return a.options.scope?.id}var p=a.i(15285),q=a.i(92467);function r(a){return{onFetch:(b,c)=>{let d=b.options,e=b.fetchOptions?.meta?.fetchMore?.direction,f=b.state.data?.pages||[],h=b.state.data?.pageParams||[],i={pages:[],pageParams:[]},j=0,k=async()=>{let c=!1,k=(0,g.ensureQueryFn)(b.options,b.fetchOptions),l=async(a,d,e)=>{if(c)return Promise.reject();if(null==d&&a.pages.length)return Promise.resolve(a);let f=(()=>{let a={client:b.client,queryKey:b.queryKey,pageParam:d,direction:e?"backward":"forward",meta:b.options.meta};return Object.defineProperty(a,"signal",{enumerable:!0,get:()=>(b.signal.aborted?c=!0:b.signal.addEventListener("abort",()=>{c=!0}),b.signal)}),a})(),h=await k(f),{maxPages:i}=b.options,j=e?g.addToStart:g.addToEnd;return{pages:j(a.pages,h,i),pageParams:j(a.pageParams,d,i)}};if(e&&f.length){let a="backward"===e,b={pages:f,pageParams:h},c=(a?function(a,{pages:b,pageParams:c}){return b.length>0?a.getPreviousPageParam?.(b[0],b,c[0],c):void 0}:s)(d,b);i=await l(b,c,a)}else{let b=a??f.length;do{let a=0===j?h[0]??d.initialPageParam:s(d,i);if(j>0&&null==a)break;i=await l(i,a),j++}while(j<b)}return i};b.options.persister?b.fetchFn=()=>b.options.persister?.(k,{client:b.client,queryKey:b.queryKey,meta:b.options.meta,signal:b.signal},c):b.fetchFn=k}}}function s(a,{pages:b,pageParams:c}){let d=b.length-1;return b.length>0?a.getNextPageParam(b[d],b,c[d],c):void 0}var t=class{#e;#f;#g;#h;#i;#j;#k;#l;constructor(a={}){this.#e=a.queryCache||new k,this.#f=a.mutationCache||new n,this.#g=a.defaultOptions||{},this.#h=new Map,this.#i=new Map,this.#j=0}mount(){this.#j++,1===this.#j&&(this.#k=p.focusManager.subscribe(async a=>{a&&(await this.resumePausedMutations(),this.#e.onFocus())}),this.#l=q.onlineManager.subscribe(async a=>{a&&(await this.resumePausedMutations(),this.#e.onOnline())}))}unmount(){this.#j--,0===this.#j&&(this.#k?.(),this.#k=void 0,this.#l?.(),this.#l=void 0)}isFetching(a){return this.#e.findAll({...a,fetchStatus:"fetching"}).length}isMutating(a){return this.#f.findAll({...a,status:"pending"}).length}getQueryData(a){let b=this.defaultQueryOptions({queryKey:a});return this.#e.get(b.queryHash)?.state.data}ensureQueryData(a){let b=this.defaultQueryOptions(a),c=this.#e.build(this,b),d=c.state.data;return void 0===d?this.fetchQuery(a):(a.revalidateIfStale&&c.isStaleByTime((0,g.resolveStaleTime)(b.staleTime,c))&&this.prefetchQuery(b),Promise.resolve(d))}getQueriesData(a){return this.#e.findAll(a).map(({queryKey:a,state:b})=>[a,b.data])}setQueryData(a,b,c){let d=this.defaultQueryOptions({queryKey:a}),e=this.#e.get(d.queryHash),f=e?.state.data,h=(0,g.functionalUpdate)(b,f);if(void 0!==h)return this.#e.build(this,d).setData(h,{...c,manual:!0})}setQueriesData(a,b,c){return i.notifyManager.batch(()=>this.#e.findAll(a).map(({queryKey:a})=>[a,this.setQueryData(a,b,c)]))}getQueryState(a){let b=this.defaultQueryOptions({queryKey:a});return this.#e.get(b.queryHash)?.state}removeQueries(a){let b=this.#e;i.notifyManager.batch(()=>{b.findAll(a).forEach(a=>{b.remove(a)})})}resetQueries(a,b){let c=this.#e;return i.notifyManager.batch(()=>(c.findAll(a).forEach(a=>{a.reset()}),this.refetchQueries({type:"active",...a},b)))}cancelQueries(a,b={}){let c={revert:!0,...b};return Promise.all(i.notifyManager.batch(()=>this.#e.findAll(a).map(a=>a.cancel(c)))).then(g.noop).catch(g.noop)}invalidateQueries(a,b={}){return i.notifyManager.batch(()=>(this.#e.findAll(a).forEach(a=>{a.invalidate()}),a?.refetchType==="none")?Promise.resolve():this.refetchQueries({...a,type:a?.refetchType??a?.type??"active"},b))}refetchQueries(a,b={}){let c={...b,cancelRefetch:b.cancelRefetch??!0};return Promise.all(i.notifyManager.batch(()=>this.#e.findAll(a).filter(a=>!a.isDisabled()&&!a.isStatic()).map(a=>{let b=a.fetch(void 0,c);return c.throwOnError||(b=b.catch(g.noop)),"paused"===a.state.fetchStatus?Promise.resolve():b}))).then(g.noop)}fetchQuery(a){let b=this.defaultQueryOptions(a);void 0===b.retry&&(b.retry=!1);let c=this.#e.build(this,b);return c.isStaleByTime((0,g.resolveStaleTime)(b.staleTime,c))?c.fetch(b):Promise.resolve(c.state.data)}prefetchQuery(a){return this.fetchQuery(a).then(g.noop).catch(g.noop)}fetchInfiniteQuery(a){return a.behavior=r(a.pages),this.fetchQuery(a)}prefetchInfiniteQuery(a){return this.fetchInfiniteQuery(a).then(g.noop).catch(g.noop)}ensureInfiniteQueryData(a){return a.behavior=r(a.pages),this.ensureQueryData(a)}resumePausedMutations(){return q.onlineManager.isOnline()?this.#f.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#e}getMutationCache(){return this.#f}getDefaultOptions(){return this.#g}setDefaultOptions(a){this.#g=a}setQueryDefaults(a,b){this.#h.set((0,g.hashKey)(a),{queryKey:a,defaultOptions:b})}getQueryDefaults(a){let b=[...this.#h.values()],c={};return b.forEach(b=>{(0,g.partialMatchKey)(a,b.queryKey)&&Object.assign(c,b.defaultOptions)}),c}setMutationDefaults(a,b){this.#i.set((0,g.hashKey)(a),{mutationKey:a,defaultOptions:b})}getMutationDefaults(a){let b=[...this.#i.values()],c={};return b.forEach(b=>{(0,g.partialMatchKey)(a,b.mutationKey)&&Object.assign(c,b.defaultOptions)}),c}defaultQueryOptions(a){if(a._defaulted)return a;let b={...this.#g.queries,...this.getQueryDefaults(a.queryKey),...a,_defaulted:!0};return b.queryHash||(b.queryHash=(0,g.hashQueryKeyByOptions)(b.queryKey,b)),void 0===b.refetchOnReconnect&&(b.refetchOnReconnect="always"!==b.networkMode),void 0===b.throwOnError&&(b.throwOnError=!!b.suspense),!b.networkMode&&b.persister&&(b.networkMode="offlineFirst"),b.queryFn===g.skipToken&&(b.enabled=!1),b}defaultMutationOptions(a){return a?._defaulted?a:{...this.#g.mutations,...a?.mutationKey&&this.getMutationDefaults(a.mutationKey),...a,_defaulted:!0}}clear(){this.#e.clear(),this.#f.clear()}};let u=function({children:a}){let[g]=(0,f.useState)(()=>new t({defaultOptions:{queries:{staleTime:6e4,gcTime:3e5,retry:(a,b)=>{if(b instanceof Error&&"status"in b){let a=b.status;if(a>=400&&a<500)return!1}return a<3},retryDelay:a=>Math.min(1e3*2**a,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:!1,onError:a=>{console.error("Mutation error:",a)}}}}));return(0,b.jsx)(d.QueryClientProvider,{client:g,children:(0,b.jsxs)(c.SessionProvider,{children:[a,(0,b.jsx)(e,{initialIsOpen:!1,buttonPosition:"bottom-right"})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__8ba656fb._.js.map