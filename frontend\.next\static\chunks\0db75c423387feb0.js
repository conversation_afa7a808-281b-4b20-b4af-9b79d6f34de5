(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,44640,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ReflectAdapter",{enumerable:!0,get:function(){return n}});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},8356,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorageInstance",{enumerable:!0,get:function(){return n}});let n=(0,e.r(97918).createAsyncLocalStorage)()},62355,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorage",{enumerable:!0,get:function(){return n.afterTaskAsyncStorageInstance}});let n=e.r(8356)},17939,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return o},throwWithStaticGenerationBailoutError:function(){return i},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return a}});let n=e.r(85115),s=e.r(62355);function i(e,t){throw Object.defineProperty(new n.StaticGenBailoutError("Route ".concat(e," couldn't be rendered statically because it used ").concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function a(e,t){throw Object.defineProperty(new n.StaticGenBailoutError("Route ".concat(e,' with `dynamic = "error"` couldn\'t be rendered statically because it used ').concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function o(e,t){let r=Object.defineProperty(Error("Route ".concat(e.route,' used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(r,t),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=r),r}function c(){let e=s.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},93168,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{describeHasCheckingStringProperty:function(){return i},describeStringPropertyAccess:function(){return s},wellKnownProperties:function(){return a}});let n=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function s(e,t){return n.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function i(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let a=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},63470,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(n,a,o):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(e.r(38477));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}let i={current:null},a="function"==typeof n.cache?n.cache:e=>e,o=console.warn;function c(e){return function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];o(e(...r))}}a(e=>{try{o(i.current)}finally{i.current=null}})},4467,e=>{"use strict";e.s(["useApi",()=>n]);var t=e.i(1269),r=e.i(1831);function n(){let{data:e}=(0,t.useSession)(),n=async function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(null==e?void 0:e.accessToken))throw Error("Non authentifié");return r.apiService.authenticatedRequest(t,e.accessToken,n)};return{login:r.apiService.login.bind(r.apiService),register:r.apiService.register.bind(r.apiService),authenticatedRequest:n,getUsers:()=>n("/users"),getUser:e=>n("/users/".concat(e)),createUser:e=>n("/users",{method:"POST",body:JSON.stringify(e)}),updateUser:(e,t)=>n("/users/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteUser:e=>n("/users/".concat(e),{method:"DELETE"}),getSessions:()=>n("/sessions"),getSession:e=>n("/sessions/".concat(e)),createSession:e=>n("/sessions",{method:"POST",body:JSON.stringify(e)}),updateSession:(e,t)=>n("/sessions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteSession:e=>n("/sessions/".concat(e),{method:"DELETE"}),getCaisses:()=>n("/caisses"),getCaisse:e=>n("/caisses/".concat(e)),createCaisse:e=>n("/caisses",{method:"POST",body:JSON.stringify(e)}),updateCaisse:(e,t)=>n("/caisses/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteCaisse:e=>n("/caisses/".concat(e),{method:"DELETE"}),emargerCaisse:e=>n("/caisses/".concat(e,"/emarger"),{method:"POST"}),getReunions:()=>n("/reunions"),getReunion:e=>n("/reunions/".concat(e)),updateReunion:(e,t)=>n("/reunions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),getMembers:()=>n("/members"),getMember:e=>n("/members/".concat(e)),createMember:e=>n("/members",{method:"POST",body:JSON.stringify(e)}),updateMember:(e,t)=>n("/members/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteMember:e=>n("/members/".concat(e),{method:"DELETE"}),getMemberDebrief:(e,t)=>{let r=new URLSearchParams;(null==t?void 0:t.dateFrom)&&r.append("dateFrom",t.dateFrom),(null==t?void 0:t.dateTo)&&r.append("dateTo",t.dateTo),(null==t?void 0:t.sessionId)&&r.append("sessionId",t.sessionId);let s=r.toString()?"?".concat(r.toString()):"";return n("/members/".concat(e,"/debrief").concat(s))},createPayment:e=>n("/payments",{method:"POST",body:JSON.stringify(e)}),getSessionMembers:e=>n("/sessions/".concat(e,"/members")),addSessionMember:e=>n("/session-members",{method:"POST",body:JSON.stringify(e)}),removeSessionMember:(e,t)=>n("/sessions/".concat(e,"/members/").concat(t),{method:"DELETE"})}}},12058,e=>{"use strict";e.s(["CaisseType",()=>t,"PaymentDirection",()=>n,"PaymentFunction",()=>s,"UserRole",()=>r]);var t=function(e){return e.PRINCIPALE="PRINCIPALE",e.REUNION="REUNION",e}({}),r=function(e){return e.SECRETARY_GENERAL="secretary_general",e.CONTROLLER="controller",e.CASHIER="cashier",e}({}),n=function(e){return e.IN="IN",e.OUT="OUT",e}({}),s=function(e){return e.CONTRIBUTION="cotisation",e.TRANSFER="transfert",e.EXTERNAL="exterieur",e}({})},65429,e=>{"use strict";e.s(["Label",()=>a],65429);var t=e.i(4051),r=e.i(38477),n=e.i(38909),s=r.forwardRef((e,r)=>(0,t.jsx)(n.Primitive.label,{...e,ref:r,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var i=e.i(41428);function a(e){let{className:r,...n}=e;return(0,t.jsx)(s,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...n})}},14545,e=>{"use strict";e.s(["ArrowLeft",()=>t],14545);let t=(0,e.i(44571).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},547,e=>{"use strict";e.s(["Textarea",()=>n]);var t=e.i(4051),r=e.i(41428);function n(e){let{className:n,...s}=e;return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",n),...s})}},85381,e=>{"use strict";e.s(["default",()=>y]);var t=e.i(4051),r=e.i(38477),n=e.i(57691),s=e.i(1269),i=e.i(67967),a=e.i(78381),o=e.i(45086),c=e.i(14545),l=e.i(5085),d=e.i(85205),u=e.i(96134),m=e.i(547),h=e.i(75680),f=e.i(5647),g=e.i(83194),p=e.i(4467),x=e.i(12058);let b=o.z.object({direction:o.z.nativeEnum(x.PaymentDirection),func:o.z.nativeEnum(x.PaymentFunction),amount:o.z.number().positive("Le montant doit être positif"),caisseId:o.z.string().min(1,"Veuillez sélectionner une caisse"),receivingCaisseId:o.z.string().optional(),sessionId:o.z.string().optional(),reunionId:o.z.string().optional(),memberId:o.z.string().optional(),reason:o.z.string().optional()});function y(){let{data:e}=(0,s.useSession)(),o=(0,n.useRouter)(),y=(0,n.useSearchParams)(),j=(0,p.useApi)(),[S,v]=(0,r.useState)(!1),[I,C]=(0,r.useState)(null),[E,N]=(0,r.useState)([]),[O,P]=(0,r.useState)([]),[T,F]=(0,r.useState)([]),R=y.get("type"),_=(null==e?void 0:e.user)&&(e.user.role===x.UserRole.SECRETARY_GENERAL||e.user.role===x.UserRole.CONTROLLER||e.user.role===x.UserRole.CASHIER),A=(0,i.useForm)({resolver:(0,a.zodResolver)(b),defaultValues:{direction:x.PaymentDirection.IN,func:"contribution"===R?x.PaymentFunction.CONTRIBUTION:"transfer"===R?x.PaymentFunction.TRANSFER:"external"===R?x.PaymentFunction.EXTERNAL:x.PaymentFunction.CONTRIBUTION,amount:0,caisseId:"",receivingCaisseId:"",sessionId:"",reunionId:"",memberId:"",reason:""}}),w=A.watch("func"),M=A.watch("direction");(0,r.useEffect)(()=>{(null==e?void 0:e.accessToken)&&L()},[e]),(0,r.useEffect)(()=>{w===x.PaymentFunction.CONTRIBUTION&&A.setValue("direction",x.PaymentDirection.IN)},[w,A]);let L=async()=>{try{let[e,t,r]=await Promise.all([j.getCaisses(),j.getMembers(),j.getSessions()]);N(e),P(t),F(r)}catch(e){console.error("Erreur lors du chargement des données:",e)}},D=async e=>{v(!0),C(null);try{let t={direction:e.direction,func:e.func,amount:e.amount,caisseId:e.caisseId};e.receivingCaisseId&&(t.receivingCaisseId=e.receivingCaisseId),e.sessionId&&(t.sessionId=e.sessionId),e.reunionId&&(t.reunionId=e.reunionId),e.memberId&&(t.memberId=e.memberId),e.reason&&(t.reason=e.reason),await j.createPayment(t),o.push("/dashboard/payments")}catch(e){console.error("Erreur lors de la création du paiement:",e),e instanceof Error?C(e.message):C("Erreur lors de la création du paiement. Veuillez réessayer.")}finally{v(!1)}};return _?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(l.default,{href:"/dashboard/payments",children:(0,t.jsxs)(d.Button,{variant:"ghost",size:"sm",children:[(0,t.jsx)(c.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Nouveau Paiement"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Enregistrer un nouveau paiement"})]})]}),(0,t.jsxs)(h.Card,{className:"max-w-2xl",children:[(0,t.jsxs)(h.CardHeader,{children:[(0,t.jsx)(h.CardTitle,{children:"Informations du paiement"}),(0,t.jsx)(h.CardDescription,{children:"Remplissez les informations du paiement à enregistrer"})]}),(0,t.jsx)(h.CardContent,{children:(0,t.jsx)(f.Form,{...A,children:(0,t.jsxs)("form",{onSubmit:A.handleSubmit(D),className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(f.FormField,{control:A.control,name:"func",render:e=>{let{field:r}=e;return(0,t.jsxs)(f.FormItem,{children:[(0,t.jsx)(f.FormLabel,{children:"Fonction *"}),(0,t.jsxs)(g.Select,{onValueChange:r.onChange,value:r.value,children:[(0,t.jsx)(f.FormControl,{children:(0,t.jsx)(g.SelectTrigger,{disabled:S,children:(0,t.jsx)(g.SelectValue,{placeholder:"Type de paiement"})})}),(0,t.jsxs)(g.SelectContent,{children:[(0,t.jsx)(g.SelectItem,{value:x.PaymentFunction.CONTRIBUTION,children:"Cotisation"}),(0,t.jsx)(g.SelectItem,{value:x.PaymentFunction.TRANSFER,children:"Transfert"}),(0,t.jsx)(g.SelectItem,{value:x.PaymentFunction.EXTERNAL,children:"Externe"})]})]}),(0,t.jsx)(f.FormMessage,{})]})}}),(0,t.jsx)(f.FormField,{control:A.control,name:"direction",render:e=>{let{field:r}=e;return(0,t.jsxs)(f.FormItem,{children:[(0,t.jsx)(f.FormLabel,{children:"Direction *"}),(0,t.jsxs)(g.Select,{onValueChange:r.onChange,value:r.value,disabled:w===x.PaymentFunction.CONTRIBUTION,children:[(0,t.jsx)(f.FormControl,{children:(0,t.jsx)(g.SelectTrigger,{disabled:S,children:(0,t.jsx)(g.SelectValue,{placeholder:"Direction"})})}),(0,t.jsxs)(g.SelectContent,{children:[(0,t.jsx)(g.SelectItem,{value:x.PaymentDirection.IN,children:"Entrée"}),(0,t.jsx)(g.SelectItem,{value:x.PaymentDirection.OUT,children:"Sortie"})]})]}),(0,t.jsx)(f.FormMessage,{})]})}})]}),(0,t.jsx)(f.FormField,{control:A.control,name:"amount",render:e=>{let{field:r}=e;return(0,t.jsxs)(f.FormItem,{children:[(0,t.jsx)(f.FormLabel,{children:"Montant *"}),(0,t.jsx)(f.FormControl,{children:(0,t.jsx)(u.Input,{type:"number",placeholder:"0",...r,onChange:e=>r.onChange(Number(e.target.value)),disabled:S})}),(0,t.jsx)(f.FormMessage,{})]})}}),(0,t.jsx)(f.FormField,{control:A.control,name:"caisseId",render:e=>{let{field:r}=e;return(0,t.jsxs)(f.FormItem,{children:[(0,t.jsx)(f.FormLabel,{children:"Caisse *"}),(0,t.jsxs)(g.Select,{onValueChange:r.onChange,value:r.value,children:[(0,t.jsx)(f.FormControl,{children:(0,t.jsx)(g.SelectTrigger,{disabled:S,children:(0,t.jsx)(g.SelectValue,{placeholder:"Sélectionner une caisse"})})}),(0,t.jsx)(g.SelectContent,{children:E.map(e=>(0,t.jsxs)(g.SelectItem,{value:e._id,children:[e.nom," (",e.type,")"]},e._id))})]}),(0,t.jsx)(f.FormMessage,{})]})}}),w===x.PaymentFunction.TRANSFER&&(0,t.jsx)(f.FormField,{control:A.control,name:"receivingCaisseId",render:e=>{let{field:r}=e;return(0,t.jsxs)(f.FormItem,{children:[(0,t.jsx)(f.FormLabel,{children:"Caisse de destination *"}),(0,t.jsxs)(g.Select,{onValueChange:r.onChange,value:r.value,children:[(0,t.jsx)(f.FormControl,{children:(0,t.jsx)(g.SelectTrigger,{disabled:S,children:(0,t.jsx)(g.SelectValue,{placeholder:"Caisse de destination"})})}),(0,t.jsx)(g.SelectContent,{children:E.map(e=>(0,t.jsxs)(g.SelectItem,{value:e._id,children:[e.nom," (",e.type,")"]},e._id))})]}),(0,t.jsx)(f.FormMessage,{})]})}}),w===x.PaymentFunction.CONTRIBUTION&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(f.FormField,{control:A.control,name:"memberId",render:e=>{let{field:r}=e;return(0,t.jsxs)(f.FormItem,{children:[(0,t.jsx)(f.FormLabel,{children:"Membre *"}),(0,t.jsxs)(g.Select,{onValueChange:r.onChange,value:r.value,children:[(0,t.jsx)(f.FormControl,{children:(0,t.jsx)(g.SelectTrigger,{disabled:S,children:(0,t.jsx)(g.SelectValue,{placeholder:"Sélectionner un membre"})})}),(0,t.jsx)(g.SelectContent,{children:O.map(e=>(0,t.jsxs)(g.SelectItem,{value:e._id,children:[e.firstName," ",e.lastName]},e._id))})]}),(0,t.jsx)(f.FormMessage,{})]})}}),(0,t.jsx)(f.FormField,{control:A.control,name:"sessionId",render:e=>{let{field:r}=e;return(0,t.jsxs)(f.FormItem,{children:[(0,t.jsx)(f.FormLabel,{children:"Session"}),(0,t.jsxs)(g.Select,{onValueChange:r.onChange,value:r.value,children:[(0,t.jsx)(f.FormControl,{children:(0,t.jsx)(g.SelectTrigger,{disabled:S,children:(0,t.jsx)(g.SelectValue,{placeholder:"Sélectionner une session"})})}),(0,t.jsx)(g.SelectContent,{children:T.map(e=>(0,t.jsxs)(g.SelectItem,{value:e._id,children:["Session ",e.annee]},e._id))})]}),(0,t.jsx)(f.FormMessage,{})]})}})]}),w===x.PaymentFunction.EXTERNAL&&M===x.PaymentDirection.OUT&&(0,t.jsx)(f.FormField,{control:A.control,name:"reason",render:e=>{let{field:r}=e;return(0,t.jsxs)(f.FormItem,{children:[(0,t.jsx)(f.FormLabel,{children:"Motif *"}),(0,t.jsx)(f.FormControl,{children:(0,t.jsx)(m.Textarea,{placeholder:"Motif du paiement externe",...r,disabled:S,rows:3})}),(0,t.jsx)(f.FormMessage,{})]})}}),I&&(0,t.jsx)("div",{className:"text-red-600 text-sm bg-red-50 p-3 rounded",children:I}),(0,t.jsxs)("div",{className:"flex justify-end gap-4 pt-6",children:[(0,t.jsx)(l.default,{href:"/dashboard/payments",children:(0,t.jsx)(d.Button,{variant:"outline",disabled:S,children:"Annuler"})}),(0,t.jsx)(d.Button,{type:"submit",disabled:S,children:S?"Enregistrement...":"Enregistrer le paiement"})]})]})})})]})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsx)(l.default,{href:"/dashboard/payments",children:(0,t.jsxs)(d.Button,{variant:"ghost",size:"sm",children:[(0,t.jsx)(c.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Accès refusé"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Vous n'avez pas les permissions pour créer des paiements."})]})})]})}}]);