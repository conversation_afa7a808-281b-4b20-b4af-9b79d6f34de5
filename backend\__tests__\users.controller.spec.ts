import 'reflect-metadata';
import { Test } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { UsersController } from '../src/users/users.controller';
import { UsersService } from '../src/users/users.service';

const usersServiceMock = {
  findAll: jest.fn().mockResolvedValue([{ _id: 'u1' }]),
  update: jest.fn().mockResolvedValue({ _id: 'u1', username: 'a' }),
  findOne: jest.fn().mockResolvedValue({ _id: 'u1' }),
  remove: jest.fn().mockResolvedValue({}),
};

describe('UsersController', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [{ provide: UsersService, useValue: usersServiceMock }],
    })
      .overrideGuard(require('../src/auth/jwt-auth.guard').JwtAuthGuard)
      .useClass(require('./utils/mock-guards').MockJwtAuthGuard)
      .overrideGuard(require('../src/common/guards/role.guard').RolesGuard)
      .useClass(require('./utils/mock-guards').MockRolesGuard)
      .compile();

    app = moduleRef.createNestApplication();
    await app.init();
  });

  afterAll(async () => { await app.close(); });

  it('GET /users', async () => {
    await request(app.getHttpServer()).get('/users').expect(200);
  });

  it('PATCH /users/:id', async () => {
    await request(app.getHttpServer()).patch('/users/u1').set('x-user-role','secretary_general').send({ username: 'a' }).expect(200);
  });

  it('GET /users/:id', async () => {
    await request(app.getHttpServer()).get('/users/u1').expect(200);
  });

  it('DELETE /users/:id', async () => {
    await request(app.getHttpServer()).delete('/users/u1').expect(200);
  });
});