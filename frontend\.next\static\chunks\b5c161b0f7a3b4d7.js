(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,94798,1767,e=>{"use strict";function t(e,t){let{checkForDefaultPrevented:n=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function(r){if(null==e||e(r),!1===n||!r.defaultPrevented)return null==t?void 0:t(r)}}e.s(["composeEventHandlers",()=>t],94798),"undefined"!=typeof window&&window.document&&window.document.createElement,e.s(["createContext",()=>o,"createContextScope",()=>i],1767);var n=e.i(38477),r=e.i(4051);function o(e,t){let o=n.createContext(t),i=e=>{let{children:t,...i}=e,l=n.useMemo(()=>i,Object.values(i));return(0,r.jsx)(o.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(r){let i=n.useContext(o);if(i)return i;if(void 0!==t)return t;throw Error("`".concat(r,"` must be used within `").concat(e,"`"))}]}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=[],i=()=>{let t=o.map(e=>n.createContext(e));return function(r){let o=(null==r?void 0:r[e])||t;return n.useMemo(()=>({["__scope".concat(e)]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,i){let l=n.createContext(i),a=o.length;o=[...o,i];let u=t=>{var o;let{scope:i,children:u,...c}=t,s=(null==i||null==(o=i[e])?void 0:o[a])||l,f=n.useMemo(()=>c,Object.values(c));return(0,r.jsx)(s.Provider,{value:f,children:u})};return u.displayName=t+"Provider",[u,function(r,o){var u;let c=(null==o||null==(u=o[e])?void 0:u[a])||l,s=n.useContext(c);if(s)return s;if(void 0!==i)return i;throw Error("`".concat(r,"` must be used within `").concat(t,"`"))}]},function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let o=t[0];if(1===t.length)return o;let i=()=>{let e=t.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(t){let r=e.reduce((e,n)=>{let{useScope:r,scopeName:o}=n,i=r(t)["__scope".concat(o)];return{...e,...i}},{});return n.useMemo(()=>({["__scope".concat(o.scopeName)]:r}),[r])}};return i.scopeName=o.scopeName,i}(i,...t)]}},76355,48365,e=>{"use strict";e.s(["DismissableLayer",()=>s],76355);var t,n=e.i(38477),r=e.i(94798),o=e.i(38909),i=e.i(44636);function l(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>function(){for(var e,n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];return null==(e=t.current)?void 0:e.call(t,...r)},[])}e.s(["useCallbackRef",()=>l],48365);var a=e.i(4051),u="dismissableLayer.update",c=n.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),s=n.forwardRef((e,s)=>{var p,m;let{disableOutsidePointerEvents:h=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:b,...x}=e,E=n.useContext(c),[S,R]=n.useState(null),C=null!=(m=null==S?void 0:S.ownerDocument)?m:null==(p=globalThis)?void 0:p.document,[,A]=n.useState({}),L=(0,i.useComposedRefs)(s,e=>R(e)),P=Array.from(E.layers),[T]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),O=P.indexOf(T),k=S?P.indexOf(S):-1,N=E.layersWithOutsidePointerEventsDisabled.size>0,M=k>=O,D=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,o=l(e),i=n.useRef(!1),a=n.useRef(()=>{});return n.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){d("dismissableLayer.pointerDownOutside",o,n,{discrete:!0})},n={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",a.current),a.current=t,r.addEventListener("click",a.current,{once:!0})):t()}else r.removeEventListener("click",a.current);i.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",a.current)}},[r,o]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));M&&!n&&(null==g||g(e),null==w||w(e),e.defaultPrevented||null==b||b())},C),F=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,o=l(e),i=n.useRef(!1);return n.useEffect(()=>{let e=e=>{e.target&&!i.current&&d("dismissableLayer.focusOutside",o,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,o]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==y||y(e),null==w||w(e),e.defaultPrevented||null==b||b())},C);return!function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,o=l(e);n.useEffect(()=>{let e=e=>{"Escape"===e.key&&o(e)};return r.addEventListener("keydown",e,{capture:!0}),()=>r.removeEventListener("keydown",e,{capture:!0})},[o,r])}(e=>{k===E.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},C),n.useEffect(()=>{if(S)return h&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(t=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(S)),E.layers.add(S),f(),()=>{h&&1===E.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=t)}},[S,C,h,E]),n.useEffect(()=>()=>{S&&(E.layers.delete(S),E.layersWithOutsidePointerEventsDisabled.delete(S),f())},[S,E]),n.useEffect(()=>{let e=()=>A({});return document.addEventListener(u,e),()=>document.removeEventListener(u,e)},[]),(0,a.jsx)(o.Primitive.div,{...x,ref:L,style:{pointerEvents:N?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,r.composeEventHandlers)(e.onFocusCapture,F.onFocusCapture),onBlurCapture:(0,r.composeEventHandlers)(e.onBlurCapture,F.onBlurCapture),onPointerDownCapture:(0,r.composeEventHandlers)(e.onPointerDownCapture,D.onPointerDownCapture)})});function f(){let e=new CustomEvent(u);document.dispatchEvent(e)}function d(e,t,n,r){let{discrete:i}=r,l=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&l.addEventListener(e,t,{once:!0}),i?(0,o.dispatchDiscreteCustomEvent)(l,a):l.dispatchEvent(a)}s.displayName="DismissableLayer",n.forwardRef((e,t)=>{let r=n.useContext(c),l=n.useRef(null),u=(0,i.useComposedRefs)(t,l);return n.useEffect(()=>{let e=l.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,a.jsx)(o.Primitive.div,{...e,ref:u})}).displayName="DismissableLayerBranch"},15589,e=>{"use strict";e.s(["useFocusGuards",()=>r]);var t=e.i(38477),n=0;function r(){t.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=r[0])?e:o()),document.body.insertAdjacentElement("beforeend",null!=(t=r[1])?t:o()),n++,()=>{1===n&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),n--}},[])}function o(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},7535,e=>{"use strict";e.s(["FocusScope",()=>c]);var t=e.i(38477),n=e.i(44636),r=e.i(38909),o=e.i(48365),i=e.i(4051),l="focusScope.autoFocusOnMount",a="focusScope.autoFocusOnUnmount",u={bubbles:!1,cancelable:!0},c=t.forwardRef((e,c)=>{let{loop:m=!1,trapped:h=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=t.useState(null),x=(0,o.useCallbackRef)(v),E=(0,o.useCallbackRef)(g),S=t.useRef(null),R=(0,n.useComposedRefs)(c,e=>b(e)),C=t.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;t.useEffect(()=>{if(h){let e=function(e){if(C.paused||!w)return;let t=e.target;w.contains(t)?S.current=t:d(S.current,{select:!0})},t=function(e){if(C.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||d(S.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&d(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[h,w,C.paused]),t.useEffect(()=>{if(w){p.add(C);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(l,u);w.addEventListener(l,x),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(d(r,{select:t}),document.activeElement!==n)return}(s(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&d(w))}return()=>{w.removeEventListener(l,x),setTimeout(()=>{let t=new CustomEvent(a,u);w.addEventListener(a,E),w.dispatchEvent(t),t.defaultPrevented||d(null!=e?e:document.body,{select:!0}),w.removeEventListener(a,E),p.remove(C)},0)}}},[w,x,E,C]);let A=t.useCallback(e=>{if(!m&&!h||C.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[r,o]=function(e){let t=s(e);return[f(t,e),f(t.reverse(),e)]}(t);r&&o?e.shiftKey||n!==o?e.shiftKey&&n===r&&(e.preventDefault(),m&&d(o,{select:!0})):(e.preventDefault(),m&&d(r,{select:!0})):n===t&&e.preventDefault()}},[m,h,C.paused]);return(0,i.jsx)(r.Primitive.div,{tabIndex:-1,...y,ref:R,onKeyDown:A})});function s(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function f(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function d(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}c.displayName="FocusScope";var p=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=m(e,t)).unshift(t)},remove(t){var n;null==(n=(e=m(e,t))[0])||n.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},97945,e=>{"use strict";e.s(["useLayoutEffect",()=>r]);var t,n=e.i(38477),r=(null==(t=globalThis)?void 0:t.document)?n.useLayoutEffect:()=>{}},33348,e=>{"use strict";e.s(["useId",()=>i]);var t=e.i(38477),n=e.i(97945),r=t[" useId ".trim().toString()]||(()=>void 0),o=0;function i(e){let[i,l]=t.useState(r());return(0,n.useLayoutEffect)(()=>{e||l(e=>null!=e?e:String(o++))},[e]),e||(i?"radix-".concat(i):"")}},50724,e=>{"use strict";e.s(["Anchor",()=>eJ,"Arrow",()=>e0,"Content",()=>eQ,"Root",()=>eG,"createPopperScope",()=>eF],50724);var t=e.i(38477);let n=["top","right","bottom","left"],r=Math.min,o=Math.max,i=Math.round,l=Math.floor,a=e=>({x:e,y:e}),u={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function s(e,t){return"function"==typeof e?e(t):e}function f(e){return e.split("-")[0]}function d(e){return e.split("-")[1]}function p(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}let h=new Set(["top","bottom"]);function v(e){return h.has(f(e))?"y":"x"}function g(e){return e.replace(/start|end/g,e=>c[e])}let y=["left","right"],w=["right","left"],b=["top","bottom"],x=["bottom","top"];function E(e){return e.replace(/left|right|bottom|top/g,e=>u[e])}function S(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function R(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function C(e,t,n){let r,{reference:o,floating:i}=e,l=v(t),a=p(v(t)),u=m(a),c=f(t),s="y"===l,h=o.x+o.width/2-i.width/2,g=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(c){case"top":r={x:h,y:o.y-i.height};break;case"bottom":r={x:h,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:g};break;case"left":r={x:o.x-i.width,y:g};break;default:r={x:o.x,y:o.y}}switch(d(t)){case"start":r[a]-=y*(n&&s?-1:1);break;case"end":r[a]+=y*(n&&s?-1:1)}return r}let A=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=C(c,r,u),d=r,p={},m=0;for(let n=0;n<a.length;n++){let{name:i,fn:h}=a[n],{x:v,y:g,data:y,reset:w}=await h({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=v?v:s,f=null!=g?g:f,p={...p,[i]:{...p[i],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=C(c,d,u)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function L(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:p=!1,padding:m=0}=s(t,e),h=S(m),v=a[p?"floating"===d?"reference":"floating":d],g=R(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:f,strategy:u})),y="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),b=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},x=R(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-x.top+h.top)/b.y,bottom:(x.bottom-g.bottom+h.bottom)/b.y,left:(g.left-x.left+h.left)/b.x,right:(x.right-g.right+h.right)/b.x}}function P(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function T(e){return n.some(t=>e[t]>=0)}let O=new Set(["left","top"]);async function k(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=f(n),a=d(n),u="y"===v(n),c=O.has(l)?-1:1,p=i&&u?-1:1,m=s(t,e),{mainAxis:h,crossAxis:g,alignmentAxis:y}="number"==typeof m?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return a&&"number"==typeof y&&(g="end"===a?-1*y:y),u?{x:g*p,y:h*c}:{x:h*c,y:g*p}}function N(){return"undefined"!=typeof window}function M(e){return W(e)?(e.nodeName||"").toLowerCase():"#document"}function D(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function F(e){var t;return null==(t=(W(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function W(e){return!!N()&&(e instanceof Node||e instanceof D(e).Node)}function j(e){return!!N()&&(e instanceof Element||e instanceof D(e).Element)}function H(e){return!!N()&&(e instanceof HTMLElement||e instanceof D(e).HTMLElement)}function B(e){return!!N()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof D(e).ShadowRoot)}let I=new Set(["inline","contents"]);function _(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=J(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!I.has(o)}let z=new Set(["table","td","th"]),Y=[":popover-open",":modal"];function X(e){return Y.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let K=["transform","translate","scale","rotate","perspective"],V=["transform","translate","scale","rotate","perspective","filter"],U=["paint","layout","strict","content"];function q(e){let t=Z(),n=j(e)?J(e):e;return K.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||V.some(e=>(n.willChange||"").includes(e))||U.some(e=>(n.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let $=new Set(["html","body","#document"]);function G(e){return $.has(M(e))}function J(e){return D(e).getComputedStyle(e)}function Q(e){return j(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ee(e){if("html"===M(e))return e;let t=e.assignedSlot||e.parentNode||B(e)&&e.host||F(e);return B(t)?t.host:t}function et(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ee(t);return G(n)?t.ownerDocument?t.ownerDocument.body:t.body:H(n)&&_(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=D(o);if(i){let e=en(l);return t.concat(l,l.visualViewport||[],_(o)?o:[],e&&n?et(e):[])}return t.concat(o,et(o,[],n))}function en(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function er(e){let t=J(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=H(e),l=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=i(n)!==l||i(r)!==a;return u&&(n=l,r=a),{width:n,height:r,$:u}}function eo(e){return j(e)?e:e.contextElement}function ei(e){let t=eo(e);if(!H(t))return a(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=er(t),u=(l?i(n.width):n.width)/r,c=(l?i(n.height):n.height)/o;return u&&Number.isFinite(u)||(u=1),c&&Number.isFinite(c)||(c=1),{x:u,y:c}}let el=a(0);function ea(e){let t=D(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:el}function eu(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eo(e),u=a(1);t&&(r?j(r)&&(u=ei(r)):u=ei(e));let c=(void 0===(o=n)&&(o=!1),r&&(!o||r===D(l))&&o)?ea(l):a(0),s=(i.left+c.x)/u.x,f=(i.top+c.y)/u.y,d=i.width/u.x,p=i.height/u.y;if(l){let e=D(l),t=r&&j(r)?D(r):r,n=e,o=en(n);for(;o&&r&&t!==n;){let e=ei(o),t=o.getBoundingClientRect(),r=J(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=i,f+=l,o=en(n=D(o))}}return R({width:d,height:p,x:s,y:f})}function ec(e,t){let n=Q(e).scrollLeft;return t?t.left+n:eu(F(e)).left+n}function es(e,t){let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-ec(e,n),y:n.top+t.scrollTop}}let ef=new Set(["absolute","fixed"]);function ed(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=D(e),r=F(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=Z();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}let c=ec(r);if(c<=0){let e=r.ownerDocument,t=e.body,n=getComputedStyle(t),o="CSS1Compat"===e.compatMode&&parseFloat(n.marginLeft)+parseFloat(n.marginRight)||0,l=Math.abs(r.clientWidth-t.clientWidth-o);l<=25&&(i-=l)}else c<=25&&(i+=c);return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=F(e),n=Q(e),r=e.ownerDocument.body,i=o(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=o(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ec(e),u=-n.scrollTop;return"rtl"===J(r).direction&&(a+=o(t.clientWidth,r.clientWidth)-i),{width:i,height:l,x:a,y:u}}(F(e));else if(j(t))r=function(e,t){let n=eu(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=H(e)?ei(e):a(1),l=e.clientWidth*i.x,u=e.clientHeight*i.y;return{width:l,height:u,x:o*i.x,y:r*i.y}}(t,n);else{let n=ea(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return R(r)}function ep(e){return"static"===J(e).position}function em(e,t){if(!H(e)||"fixed"===J(e).position)return null;if(t)return t(e);let n=e.offsetParent;return F(e)===n&&(n=n.ownerDocument.body),n}function eh(e,t){var n;let r=D(e);if(X(e))return r;if(!H(e)){let t=ee(e);for(;t&&!G(t);){if(j(t)&&!ep(t))return t;t=ee(t)}return r}let o=em(e,t);for(;o&&(n=o,z.has(M(n)))&&ep(o);)o=em(o,t);return o&&G(o)&&ep(o)&&!q(o)?r:o||function(e){let t=ee(e);for(;H(t)&&!G(t);){if(q(t))return t;if(X(t))break;t=ee(t)}return null}(e)||r}let ev=async function(e){let t=this.getOffsetParent||eh,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=H(t),o=F(t),i="fixed"===n,l=eu(e,!0,i,t),u={scrollLeft:0,scrollTop:0},c=a(0);if(r||!r&&!i)if(("body"!==M(t)||_(o))&&(u=Q(t)),r){let e=eu(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=ec(o));i&&!r&&o&&(c.x=ec(o));let s=!o||r||i?a(0):es(o,u);return{x:l.left+u.scrollLeft-c.x-s.x,y:l.top+u.scrollTop-c.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eg={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=F(r),u=!!t&&X(t.floating);if(r===l||u&&i)return n;let c={scrollLeft:0,scrollTop:0},s=a(1),f=a(0),d=H(r);if((d||!d&&!i)&&(("body"!==M(r)||_(l))&&(c=Q(r)),H(r))){let e=eu(r);s=ei(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!l||d||i?a(0):es(l,c);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-c.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-c.scrollTop*s.y+f.y+p.y}},getDocumentElement:F,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:i,strategy:l}=e,a=[..."clippingAncestors"===n?X(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=et(e,[],!1).filter(e=>j(e)&&"body"!==M(e)),o=null,i="fixed"===J(e).position,l=i?ee(e):e;for(;j(l)&&!G(l);){let t=J(l),n=q(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ef.has(o.position)||_(l)&&!n&&function e(t,n){let r=ee(t);return!(r===n||!j(r)||G(r))&&("fixed"===J(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=ee(l)}return t.set(e,r),r}(t,this._c):[].concat(n),i],u=a[0],c=a.reduce((e,n)=>{let i=ed(t,n,l);return e.top=o(i.top,e.top),e.right=r(i.right,e.right),e.bottom=r(i.bottom,e.bottom),e.left=o(i.left,e.left),e},ed(t,u,l));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:eh,getElementRects:ev,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=er(e);return{width:t,height:n}},getScale:ei,isElement:j,isRTL:function(e){return"rtl"===J(e).direction}};function ey(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ew=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:i,placement:l,rects:a,platform:u,elements:c,middlewareData:f}=t,{element:h,padding:g=0}=s(e,t)||{};if(null==h)return{};let y=S(g),w={x:n,y:i},b=p(v(l)),x=m(b),E=await u.getDimensions(h),R="y"===b,C=R?"clientHeight":"clientWidth",A=a.reference[x]+a.reference[b]-w[b]-a.floating[x],L=w[b]-a.reference[b],P=await (null==u.getOffsetParent?void 0:u.getOffsetParent(h)),T=P?P[C]:0;T&&await (null==u.isElement?void 0:u.isElement(P))||(T=c.floating[C]||a.floating[x]);let O=T/2-E[x]/2-1,k=r(y[R?"top":"left"],O),N=r(y[R?"bottom":"right"],O),M=T-E[x]-N,D=T/2-E[x]/2+(A/2-L/2),F=o(k,r(D,M)),W=!f.arrow&&null!=d(l)&&D!==F&&a.reference[x]/2-(D<k?k:N)-E[x]/2<0,j=W?D<k?D-k:D-M:0;return{[b]:w[b]+j,data:{[b]:F,centerOffset:D-F-j,...W&&{alignmentOffset:j}},reset:W}}});var eb=e.i(41902),ex="undefined"!=typeof document?t.useLayoutEffect:function(){};function eE(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eE(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eE(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eS(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eR(e,t){let n=eS(e);return Math.round(t*n)/n}function eC(e){let n=t.useRef(e);return ex(()=>{n.current=e}),n}var eA=e.i(38909),eL=e.i(4051),eP=t.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eL.jsx)(eA.Primitive.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eL.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eP.displayName="Arrow";var eT=e.i(44636),eO=e.i(1767),ek=e.i(48365),eN=e.i(97945),eM="Popper",[eD,eF]=(0,eO.createContextScope)(eM),[eW,ej]=eD(eM),eH=e=>{let{__scopePopper:n,children:r}=e,[o,i]=t.useState(null);return(0,eL.jsx)(eW,{scope:n,anchor:o,onAnchorChange:i,children:r})};eH.displayName=eM;var eB="PopperAnchor",eI=t.forwardRef((e,n)=>{let{__scopePopper:r,virtualRef:o,...i}=e,l=ej(eB,r),a=t.useRef(null),u=(0,eT.useComposedRefs)(n,a),c=t.useRef(null);return t.useEffect(()=>{let e=c.current;c.current=(null==o?void 0:o.current)||a.current,e!==c.current&&l.onAnchorChange(c.current)}),o?null:(0,eL.jsx)(eA.Primitive.div,{...i,ref:u})});eI.displayName=eB;var e_="PopperContent",[ez,eY]=eD(e_),eX=t.forwardRef((e,n)=>{var i,a,u,c,h,S,R,C;let{__scopePopper:N,side:M="bottom",sideOffset:D=0,align:W="center",alignOffset:j=0,arrowPadding:H=0,avoidCollisions:B=!0,collisionBoundary:I=[],collisionPadding:_=0,sticky:z="partial",hideWhenDetached:Y=!1,updatePositionStrategy:X="optimized",onPlaced:K,...V}=e,U=ej(e_,N),[q,Z]=t.useState(null),$=(0,eT.useComposedRefs)(n,e=>Z(e)),[G,J]=t.useState(null),Q=function(e){let[n,r]=t.useState(void 0);return(0,eN.useLayoutEffect)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),n}(G),ee=null!=(R=null==Q?void 0:Q.width)?R:0,en=null!=(C=null==Q?void 0:Q.height)?C:0,er="number"==typeof _?_:{top:0,right:0,bottom:0,left:0,..._},ei=Array.isArray(I)?I:[I],el=ei.length>0,ea={padding:er,boundary:ei.filter(eq),altBoundary:el},{refs:ec,floatingStyles:es,placement:ef,isPositioned:ed,middlewareData:ep}=function(e){void 0===e&&(e={});let{placement:n="bottom",strategy:r="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[f,d]=t.useState({x:0,y:0,strategy:r,placement:n,middlewareData:{},isPositioned:!1}),[p,m]=t.useState(o);eE(p,o)||m(o);let[h,v]=t.useState(null),[g,y]=t.useState(null),w=t.useCallback(e=>{e!==S.current&&(S.current=e,v(e))},[]),b=t.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),x=l||h,E=a||g,S=t.useRef(null),R=t.useRef(null),C=t.useRef(f),L=null!=c,P=eC(c),T=eC(i),O=eC(s),k=t.useCallback(()=>{if(!S.current||!R.current)return;let e={placement:n,strategy:r,middleware:p};T.current&&(e.platform=T.current),((e,t,n)=>{let r=new Map,o={platform:eg,...n},i={...o.platform,_c:r};return A(e,t,{...o,platform:i})})(S.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};N.current&&!eE(C.current,t)&&(C.current=t,eb.flushSync(()=>{d(t)}))})},[p,n,r,T,O]);ex(()=>{!1===s&&C.current.isPositioned&&(C.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let N=t.useRef(!1);ex(()=>(N.current=!0,()=>{N.current=!1}),[]),ex(()=>{if(x&&(S.current=x),E&&(R.current=E),x&&E){if(P.current)return P.current(x,E,k);k()}},[x,E,k,P,L]);let M=t.useMemo(()=>({reference:S,floating:R,setReference:w,setFloating:b}),[w,b]),D=t.useMemo(()=>({reference:x,floating:E}),[x,E]),F=t.useMemo(()=>{let e={position:r,left:0,top:0};if(!D.floating)return e;let t=eR(D.floating,f.x),n=eR(D.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+n+"px)",...eS(D.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,u,D.floating,f.x,f.y]);return t.useMemo(()=>({...f,update:k,refs:M,elements:D,floatingStyles:F}),[f,k,M,D,F])}({strategy:"fixed",placement:M+("center"!==W?"-"+W:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,i){let a;void 0===i&&(i={});let{ancestorScroll:u=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=i,p=eo(e),m=u||c?[...p?et(p):[],...et(t)]:[];m.forEach(e=>{u&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let h=p&&f?function(e,t){let n,i=null,a=F(e);function u(){var e;clearTimeout(n),null==(e=i)||e.disconnect(),i=null}return!function c(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),u();let d=e.getBoundingClientRect(),{left:p,top:m,width:h,height:v}=d;if(s||t(),!h||!v)return;let g=l(m),y=l(a.clientWidth-(p+h)),w={rootMargin:-g+"px "+-y+"px "+-l(a.clientHeight-(m+v))+"px "+-l(p)+"px",threshold:o(0,r(1,f))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==f){if(!b)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||ey(d,e.getBoundingClientRect())||c(),b=!1}try{i=new IntersectionObserver(x,{...w,root:a.ownerDocument})}catch(e){i=new IntersectionObserver(x,w)}i.observe(e)}(!0),u}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?eu(e):null;return d&&function t(){let r=eu(e);y&&!ey(y,r)&&n(),y=r,a=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{u&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(a)}}(...t,{animationFrame:"always"===X})},elements:{reference:U.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await k(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}))({mainAxis:D+en,alignmentAxis:j}),B&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:i,placement:l}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...d}=s(e,t),m={x:n,y:i},h=await L(t,d),g=v(f(l)),y=p(g),w=m[y],b=m[g];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],i=w-h[t];w=o(n,r(w,i))}if(u){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=b+h[e],i=b-h[t];b=o(n,r(b,i))}let x=c.fn({...t,[y]:w,[g]:b});return{...x,data:{x:x.x-n,y:x.y-i,enabled:{[y]:a,[g]:u}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===z?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=s(e,t),d={x:n,y:r},m=v(o),h=p(m),g=d[h],y=d[m],w=s(a,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+b.mainAxis,n=i.reference[h]+i.reference[e]-b.mainAxis;g<t?g=t:g>n&&(g=n)}if(c){var x,E;let e="y"===h?"width":"height",t=O.has(f(o)),n=i.reference[m]-i.floating[e]+(t&&(null==(x=l.offset)?void 0:x[m])||0)+(t?0:b.crossAxis),r=i.reference[m]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[m])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:g,[m]:y}}}}(e),options:[e,t]}))():void 0,...ea}),B&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:h,platform:S,elements:R}=t,{mainAxis:C=!0,crossAxis:A=!0,fallbackPlacements:P,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:O="none",flipAlignment:k=!0,...N}=s(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let M=f(a),D=v(h),F=f(h)===h,W=await (null==S.isRTL?void 0:S.isRTL(R.floating)),j=P||(F||!k?[E(h)]:function(e){let t=E(e);return[g(e),t,g(t)]}(h)),H="none"!==O;!P&&H&&j.push(...function(e,t,n,r){let o=d(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?w:y;return t?y:w;case"left":case"right":return t?b:x;default:return[]}}(f(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(g)))),i}(h,k,O,W));let B=[h,...j],I=await L(t,N),_=[],z=(null==(r=u.flip)?void 0:r.overflows)||[];if(C&&_.push(I[M]),A){let e=function(e,t,n){void 0===n&&(n=!1);let r=d(e),o=p(v(e)),i=m(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=E(l)),[l,E(l)]}(a,c,W);_.push(I[e[0]],I[e[1]])}if(z=[...z,{placement:a,overflows:_}],!_.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=B[e];if(t&&("alignment"!==A||D===v(t)||z.every(e=>v(e.placement)!==D||e.overflows[0]>0)))return{data:{index:e,overflows:z},reset:{placement:t}};let n=null==(i=z.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(T){case"bestFit":{let e=null==(l=z.filter(e=>{if(H){let t=v(e.placement);return t===D||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=h}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...ea}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,i;let l,a,{placement:u,rects:c,platform:p,elements:m}=t,{apply:h=()=>{},...g}=s(e,t),y=await L(t,g),w=f(u),b=d(u),x="y"===v(u),{width:E,height:S}=c.floating;"top"===w||"bottom"===w?(l=w,a=b===(await (null==p.isRTL?void 0:p.isRTL(m.floating))?"start":"end")?"left":"right"):(a=w,l="end"===b?"top":"bottom");let R=S-y.top-y.bottom,C=E-y.left-y.right,A=r(S-y[l],R),P=r(E-y[a],C),T=!t.middlewareData.shift,O=A,k=P;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=C),null!=(i=t.middlewareData.shift)&&i.enabled.y&&(O=R),T&&!b){let e=o(y.left,0),t=o(y.right,0),n=o(y.top,0),r=o(y.bottom,0);x?k=E-2*(0!==e||0!==t?e+t:o(y.left,y.right)):O=S-2*(0!==n||0!==r?n+r:o(y.top,y.bottom))}await h({...t,availableWidth:k,availableHeight:O});let N=await p.getDimensions(m.floating);return E!==N.width||S!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...ea,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),G&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ew({element:n.current,padding:r}).fn(t):{}:n?ew({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:G,padding:H}),eZ({arrowWidth:ee,arrowHeight:en}),Y&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=s(e,t);switch(r){case"referenceHidden":{let e=P(await L(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:T(e)}}}case"escaped":{let e=P(await L(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:T(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...ea})]}),[em,eh]=e$(ef),ev=(0,ek.useCallbackRef)(K);(0,eN.useLayoutEffect)(()=>{ed&&(null==ev||ev())},[ed,ev]);let eP=null==(i=ep.arrow)?void 0:i.x,eO=null==(a=ep.arrow)?void 0:a.y,eM=(null==(u=ep.arrow)?void 0:u.centerOffset)!==0,[eD,eF]=t.useState();return(0,eN.useLayoutEffect)(()=>{q&&eF(window.getComputedStyle(q).zIndex)},[q]),(0,eL.jsx)("div",{ref:ec.setFloating,"data-radix-popper-content-wrapper":"",style:{...es,transform:ed?es.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eD,"--radix-popper-transform-origin":[null==(c=ep.transformOrigin)?void 0:c.x,null==(h=ep.transformOrigin)?void 0:h.y].join(" "),...(null==(S=ep.hide)?void 0:S.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eL.jsx)(ez,{scope:N,placedSide:em,onArrowChange:J,arrowX:eP,arrowY:eO,shouldHideArrow:eM,children:(0,eL.jsx)(eA.Primitive.div,{"data-side":em,"data-align":eh,...V,ref:$,style:{...V.style,animation:ed?void 0:"none"}})})})});eX.displayName=e_;var eK="PopperArrow",eV={top:"bottom",right:"left",bottom:"top",left:"right"},eU=t.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eY(eK,n),i=eV[o.placedSide];return(0,eL.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eL.jsx)(eP,{...r,ref:t,style:{...r.style,display:"block"}})})});function eq(e){return null!==e}eU.displayName=eK;var eZ=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,f=s?0:e.arrowWidth,d=s?0:e.arrowHeight,[p,m]=e$(a),h={start:"0%",center:"50%",end:"100%"}[m],v=(null!=(i=null==(r=c.arrow)?void 0:r.x)?i:0)+f/2,g=(null!=(l=null==(o=c.arrow)?void 0:o.y)?l:0)+d/2,y="",w="";return"bottom"===p?(y=s?h:"".concat(v,"px"),w="".concat(-d,"px")):"top"===p?(y=s?h:"".concat(v,"px"),w="".concat(u.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),w=s?h:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+d,"px"),w=s?h:"".concat(g,"px")),{data:{x:y,y:w}}}});function e$(e){let[t,n="center"]=e.split("-");return[t,n]}var eG=eH,eJ=eI,eQ=eX,e0=eU},37439,e=>{"use strict";e.s(["Portal",()=>l]);var t=e.i(38477),n=e.i(41902),r=e.i(38909),o=e.i(97945),i=e.i(4051),l=t.forwardRef((e,l)=>{var a,u;let{container:c,...s}=e,[f,d]=t.useState(!1);(0,o.useLayoutEffect)(()=>d(!0),[]);let p=c||f&&(null==(u=globalThis)||null==(a=u.document)?void 0:a.body);return p?n.default.createPortal((0,i.jsx)(r.Primitive.div,{...s,ref:l}),p):null});l.displayName="Portal"},26183,e=>{"use strict";e.s(["useControllableState",()=>o],26183);var t=e.i(38477),n=e.i(97945);t[" useEffectEvent ".trim().toString()],t[" useInsertionEffect ".trim().toString()];var r=t[" useInsertionEffect ".trim().toString()]||n.useLayoutEffect;function o(e){let{prop:n,defaultProp:o,onChange:i=()=>{},caller:l}=e,[a,u,c]=function(e){let{defaultProp:n,onChange:o}=e,[i,l]=t.useState(n),a=t.useRef(i),u=t.useRef(o);return r(()=>{u.current=o},[o]),t.useEffect(()=>{if(a.current!==i){var e;null==(e=u.current)||e.call(u,i),a.current=i}},[i,a]),[i,l,u]}({defaultProp:o,onChange:i}),s=void 0!==n,f=s?n:a;{let e=t.useRef(void 0!==n);t.useEffect(()=>{let t=e.current;if(t!==s){let e=s?"controlled":"uncontrolled";console.warn("".concat(l," is changing from ").concat(t?"controlled":"uncontrolled"," to ").concat(e,". Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component."))}e.current=s},[s,l])}return[f,t.useCallback(e=>{if(s){let r="function"==typeof e?e(n):e;if(r!==n){var t;null==(t=c.current)||t.call(c,r)}}else u(e)},[s,n,u,c])]}Symbol("RADIX:SYNC_STATE")},23952,e=>{"use strict";e.s(["hideOthers",()=>a]);var t=new WeakMap,n=new WeakMap,r={},o=0,i=function(e){return e&&(e.host||i(e.parentNode))},l=function(e,l,a,u){var c=(Array.isArray(e)?e:[e]).map(function(e){if(l.contains(e))return e;var t=i(e);return t&&l.contains(t)?t:(console.error("aria-hidden",e,"in not contained inside",l,". Doing nothing"),null)}).filter(function(e){return!!e});r[a]||(r[a]=new WeakMap);var s=r[a],f=[],d=new Set,p=new Set(c),m=function(e){!e||d.has(e)||(d.add(e),m(e.parentNode))};c.forEach(m);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))h(e);else try{var r=e.getAttribute(u),o=null!==r&&"false"!==r,i=(t.get(e)||0)+1,l=(s.get(e)||0)+1;t.set(e,i),s.set(e,l),f.push(e),1===i&&o&&n.set(e,!0),1===l&&e.setAttribute(a,"true"),o||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(l),d.clear(),o++,function(){f.forEach(function(e){var r=t.get(e)-1,o=s.get(e)-1;t.set(e,r),s.set(e,o),r||(n.has(e)||e.removeAttribute(u),n.delete(e)),o||e.removeAttribute(a)}),--o||(t=new WeakMap,t=new WeakMap,n=new WeakMap,r={})}},a=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),l(r,o,n,"aria-hidden")):function(){return null}}},54574,e=>{"use strict";e.s(["RemoveScroll",()=>X],54574);var t,n,r=function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var i=("function"==typeof SuppressedError&&SuppressedError,e.i(38477)),l="right-scroll-bar-position",a="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,s=new WeakMap;function f(e){return e}var d=function(e){void 0===e&&(e={});var t,n,o,i=(void 0===t&&(t=f),n=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var r=t(e,o);return n.push(r),function(){n=n.filter(function(e){return e!==r})}},assignSyncMedium:function(e){for(o=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){o=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return i.options=r({async:!0,ssr:!1},e),i}(),p=function(){},m=i.forwardRef(function(e,t){var n,l,a,f,m=i.useRef(null),h=i.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),v=h[0],g=h[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,S=e.shards,R=e.sideCar,C=e.noRelative,A=e.noIsolation,L=e.inert,P=e.allowPinchZoom,T=e.as,O=e.gapMode,k=o(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),N=(n=[m,t],l=function(e){return n.forEach(function(t){return u(t,e)})},(a=(0,i.useState)(function(){return{value:null,callback:l,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=l,f=a.facade,c(function(){var e=s.get(f);if(e){var t=new Set(e),r=new Set(n),o=f.current;t.forEach(function(e){r.has(e)||u(e,null)}),r.forEach(function(e){t.has(e)||u(e,o)})}s.set(f,n)},[n]),f),M=r(r({},k),v);return i.createElement(i.Fragment,null,E&&i.createElement(R,{sideCar:d,removeScrollBar:x,shards:S,noRelative:C,noIsolation:A,inert:L,setCallbacks:g,allowPinchZoom:!!P,lockRef:m,gapMode:O}),y?i.cloneElement(i.Children.only(w),r(r({},M),{ref:N})):i.createElement(void 0===T?"div":T,r({},M,{className:b,ref:N}),w))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:a,zeroRight:l};var h=function(e){var t=e.sideCar,n=o(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var l=t.read();if(!l)throw Error("Sidecar medium not found");return i.createElement(l,r({},n))};h.isSideCarExport=!0;var v=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=n||("undefined"!=typeof __webpack_nonce__?__webpack_nonce__:void 0);return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},g=function(){var e=v();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},y=function(){var e=g();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},b=function(e){return parseInt(e||"",10)||0},x=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[b(n),b(r),b(o)]},E=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=x(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},S=y(),R="data-scroll-locked",C=function(e,t,n,r){var o=e.left,i=e.top,u=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(u,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(a," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},A=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},L=function(){i.useEffect(function(){return document.body.setAttribute(R,(A()+1).toString()),function(){var e=A()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},P=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;L();var l=i.useMemo(function(){return E(o)},[o]);return i.createElement(S,{styles:C(l,!t,o,n?"":"!important")})},T=!1;if("undefined"!=typeof window)try{var O=Object.defineProperty({},"passive",{get:function(){return T=!0,!0}});window.addEventListener("test",O,O),window.removeEventListener("test",O,O)}catch(e){T=!1}var k=!!T&&{passive:!1},N=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},M=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),D(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body)return!1},D=function(e,t){return"v"===e?N(t,"overflowY"):N(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},W=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,f=a>0,d=0,p=0;do{if(!u)break;var m=F(e,u),h=m[0],v=m[1]-m[2]-l*h;(h||v)&&D(e,u)&&(d+=v,p+=h);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u))return f&&(o&&1>Math.abs(d)||!o&&a>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},j=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},H=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},I=0,_=[];let z=(t=function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(I++)[0],l=i.useState(y)[0],a=i.useRef(e);i.useEffect(function(){a.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=j(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,f=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=M(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=M(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return W(p,t,e,"h"===p?u:c,!0)},[]),c=i.useCallback(function(e){if(_.length&&_[_.length-1]===l){var n="deltaY"in e?H(e):j(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=i.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=i.useCallback(function(e){n.current=j(e),r.current=void 0},[]),d=i.useCallback(function(t){s(t.type,H(t),t.target,u(t,e.lockRef.current))},[]),p=i.useCallback(function(t){s(t.type,j(t),t.target,u(t,e.lockRef.current))},[]);i.useEffect(function(){return _.push(l),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",c,k),document.addEventListener("touchmove",c,k),document.addEventListener("touchstart",f,k),function(){_=_.filter(function(e){return e!==l}),document.removeEventListener("wheel",c,k),document.removeEventListener("touchmove",c,k),document.removeEventListener("touchstart",f,k)}},[]);var m=e.removeScrollBar,h=e.inert;return i.createElement(i.Fragment,null,h?i.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?i.createElement(P,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},d.useMedium(t),h);var Y=i.forwardRef(function(e,t){return i.createElement(m,r({},e,{ref:t,sideCar:z}))});Y.classNames=m.classNames;let X=Y}]);