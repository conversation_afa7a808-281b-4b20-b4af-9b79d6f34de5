(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,65429,e=>{"use strict";e.s(["Label",()=>i],65429);var t=e.i(4051),s=e.i(38477),r=e.i(38909),a=s.forwardRef((e,s)=>(0,t.jsx)(r.Primitive.label,{...e,ref:s,onMouseDown:t=>{var s;t.target.closest("button, input, select, textarea")||(null==(s=e.onMouseDown)||s.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var n=e.i(41428);function i(e){let{className:s,...r}=e;return(0,t.jsx)(a,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...r})}},14545,e=>{"use strict";e.s(["ArrowLeft",()=>t],14545);let t=(0,e.i(44571).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},44640,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,s){let r=Reflect.get(e,t,s);return"function"==typeof r?r.bind(e):r}static set(e,t,s,r){return Reflect.set(e,t,s,r)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},8356,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"afterTaskAsyncStorageInstance",{enumerable:!0,get:function(){return r}});let r=(0,e.r(97918).createAsyncLocalStorage)()},62355,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"afterTaskAsyncStorage",{enumerable:!0,get:function(){return r.afterTaskAsyncStorageInstance}});let r=e.r(8356)},17939,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(s,{isRequestAPICallableInsideAfter:function(){return o},throwForSearchParamsAccessInUseCache:function(){return l},throwWithStaticGenerationBailoutError:function(){return n},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return i}});let r=e.r(85115),a=e.r(62355);function n(e,t){throw Object.defineProperty(new r.StaticGenBailoutError("Route ".concat(e," couldn't be rendered statically because it used ").concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function i(e,t){throw Object.defineProperty(new r.StaticGenBailoutError("Route ".concat(e,' with `dynamic = "error"` couldn\'t be rendered statically because it used ').concat(t,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function l(e,t){let s=Object.defineProperty(Error("Route ".concat(e.route,' used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(s,t),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=s),s}function o(){let e=a.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},93168,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(s,{describeHasCheckingStringProperty:function(){return n},describeStringPropertyAccess:function(){return a},wellKnownProperties:function(){return i}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function a(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function n(e,t){let s=JSON.stringify(t);return"`Reflect.has("+e+", "+s+")`, `"+s+" in "+e+"`, or similar"}let i=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},63470,(e,t,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return o}});let r=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var s=a(t);if(s&&s.has(e))return s.get(e);var r={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=n?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(r,i,l):r[i]=e[i]}return r.default=e,s&&s.set(e,r),r}(e.r(38477));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,s=new WeakMap;return(a=function(e){return e?s:t})(e)}let n={current:null},i="function"==typeof r.cache?r.cache:e=>e,l=console.warn;function o(e){return function(){for(var t=arguments.length,s=Array(t),r=0;r<t;r++)s[r]=arguments[r];l(e(...s))}}i(e=>{try{l(n.current)}finally{n.current=null}})},4467,e=>{"use strict";e.s(["useApi",()=>r]);var t=e.i(1269),s=e.i(1831);function r(){let{data:e}=(0,t.useSession)(),r=async function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(null==e?void 0:e.accessToken))throw Error("Non authentifié");return s.apiService.authenticatedRequest(t,e.accessToken,r)};return{login:s.apiService.login.bind(s.apiService),register:s.apiService.register.bind(s.apiService),authenticatedRequest:r,getUsers:()=>r("/users"),getUser:e=>r("/users/".concat(e)),createUser:e=>r("/users",{method:"POST",body:JSON.stringify(e)}),updateUser:(e,t)=>r("/users/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteUser:e=>r("/users/".concat(e),{method:"DELETE"}),getSessions:()=>r("/sessions"),getSession:e=>r("/sessions/".concat(e)),createSession:e=>r("/sessions",{method:"POST",body:JSON.stringify(e)}),updateSession:(e,t)=>r("/sessions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteSession:e=>r("/sessions/".concat(e),{method:"DELETE"}),getCaisses:()=>r("/caisses"),getCaisse:e=>r("/caisses/".concat(e)),createCaisse:e=>r("/caisses",{method:"POST",body:JSON.stringify(e)}),updateCaisse:(e,t)=>r("/caisses/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteCaisse:e=>r("/caisses/".concat(e),{method:"DELETE"}),emargerCaisse:e=>r("/caisses/".concat(e,"/emarger"),{method:"POST"}),getReunions:()=>r("/reunions"),getReunion:e=>r("/reunions/".concat(e)),updateReunion:(e,t)=>r("/reunions/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),getMembers:()=>r("/members"),getMember:e=>r("/members/".concat(e)),createMember:e=>r("/members",{method:"POST",body:JSON.stringify(e)}),updateMember:(e,t)=>r("/members/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteMember:e=>r("/members/".concat(e),{method:"DELETE"}),getMemberDebrief:(e,t)=>{let s=new URLSearchParams;(null==t?void 0:t.dateFrom)&&s.append("dateFrom",t.dateFrom),(null==t?void 0:t.dateTo)&&s.append("dateTo",t.dateTo),(null==t?void 0:t.sessionId)&&s.append("sessionId",t.sessionId);let a=s.toString()?"?".concat(s.toString()):"";return r("/members/".concat(e,"/debrief").concat(a))},createPayment:e=>r("/payments",{method:"POST",body:JSON.stringify(e)}),getSessionMembers:e=>r("/sessions/".concat(e,"/members")),addSessionMember:e=>r("/session-members",{method:"POST",body:JSON.stringify(e)}),removeSessionMember:(e,t)=>r("/sessions/".concat(e,"/members/").concat(t),{method:"DELETE"})}}},12058,e=>{"use strict";e.s(["CaisseType",()=>t,"PaymentDirection",()=>r,"PaymentFunction",()=>a,"UserRole",()=>s]);var t=function(e){return e.PRINCIPALE="PRINCIPALE",e.REUNION="REUNION",e}({}),s=function(e){return e.SECRETARY_GENERAL="secretary_general",e.CONTROLLER="controller",e.CASHIER="cashier",e}({}),r=function(e){return e.IN="IN",e.OUT="OUT",e}({}),a=function(e){return e.CONTRIBUTION="cotisation",e.TRANSFER="transfert",e.EXTERNAL="exterieur",e}({})},81787,e=>{"use strict";e.s(["Plus",()=>t],81787);let t=(0,e.i(44571).default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},27973,e=>{"use strict";e.s(["Trash2",()=>t],27973);let t=(0,e.i(44571).default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},41850,e=>{"use strict";e.s(["Table",()=>r,"TableBody",()=>n,"TableCell",()=>o,"TableHead",()=>l,"TableHeader",()=>a,"TableRow",()=>i]);var t=e.i(4051),s=e.i(41428);function r(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",r),...a})})}function a(e){let{className:r,...a}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",r),...a})}function n(e){let{className:r,...a}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",r),...a})}function i(e){let{className:r,...a}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",r),...a})}function l(e){let{className:r,...a}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",r),...a})}function o(e){let{className:r,...a}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",r),...a})}},15153,e=>{"use strict";e.s(["default",()=>R],15153);var t=e.i(4051),s=e.i(38477),r=e.i(57691),a=e.i(1269),n=e.i(14545),i=e.i(81787),l=e.i(27973),o=e.i(5085),c=e.i(85205),d=e.i(75680),u=e.i(41850),m=e.i(28149),h=e.i(19203),h=h,f=e.i(41428);let x=m.Root,p=m.Trigger,b=m.Portal;m.Close;let j=s.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)(m.Overlay,{ref:s,className:(0,f.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...a})});j.displayName=m.Overlay.displayName;let g=s.forwardRef((e,s)=>{let{className:r,children:a,...n}=e;return(0,t.jsxs)(b,{children:[(0,t.jsx)(j,{}),(0,t.jsxs)(m.Content,{ref:s,className:(0,f.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...n,children:[a,(0,t.jsxs)(m.Close,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(h.default,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});g.displayName=m.Content.displayName;let y=e=>{let{className:s,...r}=e;return(0,t.jsx)("div",{className:(0,f.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...r})};y.displayName="DialogHeader";let N=s.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)(m.Title,{ref:s,className:(0,f.cn)("text-lg font-semibold leading-none tracking-tight",r),...a})});N.displayName=m.Title.displayName;let v=s.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)(m.Description,{ref:s,className:(0,f.cn)("text-sm text-muted-foreground",r),...a})});v.displayName=m.Description.displayName;var S=e.i(83194),T=e.i(96134),C=e.i(65429),w=e.i(4467),O=e.i(12058);function R(){let e=(0,r.useParams)();(0,r.useRouter)();let{data:m}=(0,a.useSession)(),h=(0,w.useApi)(),[f,b]=(0,s.useState)(null),[j,R]=(0,s.useState)([]),[P,E]=(0,s.useState)([]),[A,_]=(0,s.useState)(!0),[M,D]=(0,s.useState)(null),[k,I]=(0,s.useState)(!1),[L,H]=(0,s.useState)(""),[F,B]=(0,s.useState)(1),U=e.id,z=(null==m?void 0:m.user)&&(m.user.role===O.UserRole.SECRETARY_GENERAL||m.user.role===O.UserRole.CONTROLLER);(0,s.useEffect)(()=>{(null==m?void 0:m.accessToken)&&U&&J()},[m,U]);let J=async()=>{try{_(!0),D(null);let[e,t,s]=await Promise.all([h.getSession(U),h.getSessionMembers(U),h.getMembers()]);b(e),R(t);let r=t.map(e=>e.memberId),a=s.filter(e=>!r.includes(e._id));E(a)}catch(e){console.error("Erreur lors du chargement:",e),D("Erreur lors du chargement des données")}finally{_(!1)}},G=async()=>{if(L&&f)try{f.partFixe,await h.addSessionMember({sessionId:U,memberId:L,parts:F}),await J(),H(""),B(1),I(!1)}catch(e){console.error("Erreur lors de l'ajout du membre:",e)}},W=async e=>{if(confirm("Êtes-vous sûr de vouloir retirer ce membre de la session ?"))try{await h.removeSessionMember(U,e),await J()}catch(e){console.error("Erreur lors de la suppression:",e)}},q=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"XAF"}).format(e),V=e=>new Date(e).toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric"});if(!z)return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsx)(o.default,{href:"/dashboard/sessions",children:(0,t.jsxs)(c.Button,{variant:"ghost",size:"sm",children:[(0,t.jsx)(n.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Accès refusé"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Vous n'avez pas les permissions pour accéder à cette page."})]})})]});if(A)return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsx)(o.default,{href:"/dashboard/sessions",children:(0,t.jsxs)(c.Button,{variant:"ghost",size:"sm",children:[(0,t.jsx)(n.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,t.jsx)("div",{className:"flex justify-center py-8",children:(0,t.jsx)("div",{className:"text-gray-500",children:"Chargement..."})})]});if(M||!f)return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsx)(o.default,{href:"/dashboard/sessions",children:(0,t.jsxs)(c.Button,{variant:"ghost",size:"sm",children:[(0,t.jsx)(n.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Erreur"}),(0,t.jsx)("p",{className:"text-gray-600",children:M||"Session introuvable"})]})})]});let X=j.length,$=j.reduce((e,t)=>e+t.parts,0),K=j.reduce((e,t)=>e+t.totalDue,0),Y=j.reduce((e,t)=>e+t.paidSoFar,0);return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(o.default,{href:"/dashboard/sessions",children:(0,t.jsxs)(c.Button,{variant:"ghost",size:"sm",children:[(0,t.jsx)(n.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["Session ",f.annee]}),(0,t.jsxs)("p",{className:"text-gray-600",children:[V(f.dateDebut)," - ",V(f.dateFin)]})]})]}),(0,t.jsx)(o.default,{href:"/dashboard/sessions/".concat(f._id,"/edit"),children:(0,t.jsx)(c.Button,{children:"Modifier"})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{children:(0,t.jsx)(d.CardTitle,{children:"Informations de la session"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Année"}),(0,t.jsx)("p",{className:"text-gray-600",children:f.annee})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Part fixe"}),(0,t.jsx)("p",{className:"text-gray-600",children:q(f.partFixe)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Date de début"}),(0,t.jsx)("p",{className:"text-gray-600",children:V(f.dateDebut)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Date de fin"}),(0,t.jsx)("p",{className:"text-gray-600",children:V(f.dateFin)})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Membres inscrits"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold",children:X})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Total parts"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:$})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Montant dû"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:q(K)})})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{className:"pb-2",children:(0,t.jsx)(d.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Montant payé"})}),(0,t.jsx)(d.CardContent,{children:(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:q(Y)})})]})]}),(0,t.jsxs)(d.Card,{children:[(0,t.jsx)(d.CardHeader,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(d.CardTitle,{children:"Membres de la session"}),(0,t.jsxs)(d.CardDescription,{children:[X," membre(s) inscrit(s)"]})]}),(0,t.jsxs)(x,{open:k,onOpenChange:I,children:[(0,t.jsx)(p,{asChild:!0,children:(0,t.jsxs)(c.Button,{children:[(0,t.jsx)(i.Plus,{className:"h-4 w-4 mr-2"}),"Ajouter un membre"]})}),(0,t.jsxs)(g,{children:[(0,t.jsxs)(y,{children:[(0,t.jsx)(N,{children:"Ajouter un membre à la session"}),(0,t.jsx)(v,{children:"Sélectionnez un membre et définissez le nombre de parts"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(C.Label,{htmlFor:"member",children:"Membre"}),(0,t.jsxs)(S.Select,{value:L,onValueChange:H,children:[(0,t.jsx)(S.SelectTrigger,{children:(0,t.jsx)(S.SelectValue,{placeholder:"Sélectionner un membre"})}),(0,t.jsx)(S.SelectContent,{children:P.map(e=>(0,t.jsxs)(S.SelectItem,{value:e._id,children:[e.firstName," ",e.lastName]},e._id))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(C.Label,{htmlFor:"parts",children:"Nombre de parts"}),(0,t.jsx)(T.Input,{id:"parts",type:"number",min:"1",value:F,onChange:e=>B(Number(e.target.value))})]}),f&&F>0&&(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Montant total dû: ",q(f.partFixe*F)]}),(0,t.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,t.jsx)(c.Button,{variant:"outline",onClick:()=>I(!1),children:"Annuler"}),(0,t.jsx)(c.Button,{onClick:G,disabled:!L,children:"Ajouter"})]})]})]})]})]})}),(0,t.jsx)(d.CardContent,{children:j.length>0?(0,t.jsxs)(u.Table,{children:[(0,t.jsx)(u.TableHeader,{children:(0,t.jsxs)(u.TableRow,{children:[(0,t.jsx)(u.TableHead,{children:"Membre"}),(0,t.jsx)(u.TableHead,{children:"Parts"}),(0,t.jsx)(u.TableHead,{children:"Montant dû"}),(0,t.jsx)(u.TableHead,{children:"Payé"}),(0,t.jsx)(u.TableHead,{children:"Reste"}),(0,t.jsx)(u.TableHead,{children:"Actions"})]})}),(0,t.jsx)(u.TableBody,{children:j.map(e=>{let s=e.totalDue-e.paidSoFar;return(0,t.jsxs)(u.TableRow,{children:[(0,t.jsx)(u.TableCell,{children:(0,t.jsxs)("div",{className:"font-medium",children:["Membre ID: ",e.memberId]})}),(0,t.jsx)(u.TableCell,{children:e.parts}),(0,t.jsx)(u.TableCell,{children:q(e.totalDue)}),(0,t.jsx)(u.TableCell,{className:"text-green-600",children:q(e.paidSoFar)}),(0,t.jsx)(u.TableCell,{className:s>0?"text-red-600":"text-green-600",children:q(s)}),(0,t.jsx)(u.TableCell,{children:(0,t.jsx)(c.Button,{variant:"ghost",size:"sm",onClick:()=>W(e.memberId),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(l.Trash2,{className:"h-4 w-4"})})})]},e._id)})})]}):(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Aucun membre inscrit à cette session"})})]})]})}}]);