{"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-roving-focu_212b51427a855c81088e3653515bf4fb/node_modules/@radix-ui/react-roving-focus/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-menu@2.1.16_900d93adafc779075db578116b92345f/node_modules/@radix-ui/react-menu/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-dropdown-me_0d24bc2194d27e4ed225fdbe9ca396a8/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "turbopack:///[project]/frontend/src/components/ui/dropdown-menu.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/circle.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/chevron-right.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/ellipsis.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/square-pen.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-dropdown-me_0d24bc2194d27e4ed225fdbe9ca396a8/node_modules/@radix-ui/react-dropdown-menu/src/dropdown-menu.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-roving-focu_212b51427a855c81088e3653515bf4fb/node_modules/@radix-ui/react-roving-focus/src/roving-focus-group.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-menu@2.1.16_900d93adafc779075db578116b92345f/node_modules/@radix-ui/react-menu/src/menu.tsx"], "sourcesContent": ["\"use client\";\n\n// src/roving-focus-group.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { jsx } from \"react/jsx-runtime\";\nvar ENTRY_FOCUS = \"rovingFocusGroup.onEntryFocus\";\nvar EVENT_OPTIONS = { bubbles: false, cancelable: true };\nvar GROUP_NAME = \"RovingFocusGroup\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(GROUP_NAME);\nvar [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\nvar [RovingFocusProvider, useRovingFocusContext] = createRovingFocusGroupContext(GROUP_NAME);\nvar RovingFocusGroup = React.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ jsx(Collection.Provider, { scope: props.__scopeRovingFocusGroup, children: /* @__PURE__ */ jsx(Collection.Slot, { scope: props.__scopeRovingFocusGroup, children: /* @__PURE__ */ jsx(RovingFocusGroupImpl, { ...props, ref: forwardedRef }) }) });\n  }\n);\nRovingFocusGroup.displayName = GROUP_NAME;\nvar RovingFocusGroupImpl = React.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n  return /* @__PURE__ */ jsx(\n    RovingFocusProvider,\n    {\n      scope: __scopeRovingFocusGroup,\n      orientation,\n      dir: direction,\n      loop,\n      currentTabStopId,\n      onItemFocus: React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      ),\n      onItemShiftTab: React.useCallback(() => setIsTabbingBackOut(true), []),\n      onFocusableItemAdd: React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      ),\n      onFocusableItemRemove: React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      ),\n      children: /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          tabIndex: isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0,\n          \"data-orientation\": orientation,\n          ...groupProps,\n          ref: composedRefs,\n          style: { outline: \"none\", ...props.style },\n          onMouseDown: composeEventHandlers(props.onMouseDown, () => {\n            isClickFocusRef.current = true;\n          }),\n          onFocus: composeEventHandlers(props.onFocus, (event) => {\n            const isKeyboardFocus = !isClickFocusRef.current;\n            if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n              const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n              event.currentTarget.dispatchEvent(entryFocusEvent);\n              if (!entryFocusEvent.defaultPrevented) {\n                const items = getItems().filter((item) => item.focusable);\n                const activeItem = items.find((item) => item.active);\n                const currentItem = items.find((item) => item.id === currentTabStopId);\n                const candidateItems = [activeItem, currentItem, ...items].filter(\n                  Boolean\n                );\n                const candidateNodes = candidateItems.map((item) => item.ref.current);\n                focusFirst(candidateNodes, preventScrollOnEntryFocus);\n              }\n            }\n            isClickFocusRef.current = false;\n          }),\n          onBlur: composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))\n        }\n      )\n    }\n  );\n});\nvar ITEM_NAME = \"RovingFocusGroupItem\";\nvar RovingFocusGroupItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n    return /* @__PURE__ */ jsx(\n      Collection.ItemSlot,\n      {\n        scope: __scopeRovingFocusGroup,\n        id,\n        focusable,\n        active,\n        children: /* @__PURE__ */ jsx(\n          Primitive.span,\n          {\n            tabIndex: isCurrentTabStop ? 0 : -1,\n            \"data-orientation\": context.orientation,\n            ...itemProps,\n            ref: forwardedRef,\n            onMouseDown: composeEventHandlers(props.onMouseDown, (event) => {\n              if (!focusable) event.preventDefault();\n              else context.onItemFocus(id);\n            }),\n            onFocus: composeEventHandlers(props.onFocus, () => context.onItemFocus(id)),\n            onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n              if (event.key === \"Tab\" && event.shiftKey) {\n                context.onItemShiftTab();\n                return;\n              }\n              if (event.target !== event.currentTarget) return;\n              const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n              if (focusIntent !== void 0) {\n                if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n                event.preventDefault();\n                const items = getItems().filter((item) => item.focusable);\n                let candidateNodes = items.map((item) => item.ref.current);\n                if (focusIntent === \"last\") candidateNodes.reverse();\n                else if (focusIntent === \"prev\" || focusIntent === \"next\") {\n                  if (focusIntent === \"prev\") candidateNodes.reverse();\n                  const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                  candidateNodes = context.loop ? wrapArray(candidateNodes, currentIndex + 1) : candidateNodes.slice(currentIndex + 1);\n                }\n                setTimeout(() => focusFirst(candidateNodes));\n              }\n            }),\n            children: typeof children === \"function\" ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null }) : children\n          }\n        )\n      }\n    );\n  }\n);\nRovingFocusGroupItem.displayName = ITEM_NAME;\nvar MAP_KEY_TO_FOCUS_INTENT = {\n  ArrowLeft: \"prev\",\n  ArrowUp: \"prev\",\n  ArrowRight: \"next\",\n  ArrowDown: \"next\",\n  PageUp: \"first\",\n  Home: \"first\",\n  PageDown: \"last\",\n  End: \"last\"\n};\nfunction getDirectionAwareKey(key, dir) {\n  if (dir !== \"rtl\") return key;\n  return key === \"ArrowLeft\" ? \"ArrowRight\" : key === \"ArrowRight\" ? \"ArrowLeft\" : key;\n}\nfunction getFocusIntent(event, orientation, dir) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === \"vertical\" && [\"ArrowLeft\", \"ArrowRight\"].includes(key)) return void 0;\n  if (orientation === \"horizontal\" && [\"ArrowUp\", \"ArrowDown\"].includes(key)) return void 0;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\nfunction focusFirst(candidates, preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\nfunction wrapArray(array, startIndex) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\nvar Root = RovingFocusGroup;\nvar Item = RovingFocusGroupItem;\nexport {\n  Item,\n  Root,\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  createRovingFocusGroupScope\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/menu.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { useComposedRefs, composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { DismissableLayer } from \"@radix-ui/react-dismissable-layer\";\nimport { useFocusGuards } from \"@radix-ui/react-focus-guards\";\nimport { FocusScope } from \"@radix-ui/react-focus-scope\";\nimport { useId } from \"@radix-ui/react-id\";\nimport * as PopperPrimitive from \"@radix-ui/react-popper\";\nimport { createPopperScope } from \"@radix-ui/react-popper\";\nimport { Portal as PortalPrimitive } from \"@radix-ui/react-portal\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { Primitive, dispatchDiscreteCustomEvent } from \"@radix-ui/react-primitive\";\nimport * as RovingFocusGroup from \"@radix-ui/react-roving-focus\";\nimport { createRovingFocusGroupScope } from \"@radix-ui/react-roving-focus\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { hideOthers } from \"aria-hidden\";\nimport { RemoveScroll } from \"react-remove-scroll\";\nimport { jsx } from \"react/jsx-runtime\";\nvar SELECTION_KEYS = [\"Enter\", \" \"];\nvar FIRST_KEYS = [\"ArrowDown\", \"PageUp\", \"Home\"];\nvar LAST_KEYS = [\"ArrowUp\", \"PageDown\", \"End\"];\nvar FIRST_LAST_KEYS = [...FIRST_KEYS, ...LAST_KEYS];\nvar SUB_OPEN_KEYS = {\n  ltr: [...SELECTION_KEYS, \"ArrowRight\"],\n  rtl: [...SELECTION_KEYS, \"ArrowLeft\"]\n};\nvar SUB_CLOSE_KEYS = {\n  ltr: [\"ArrowLeft\"],\n  rtl: [\"ArrowRight\"]\n};\nvar MENU_NAME = \"Menu\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(MENU_NAME);\nvar [createMenuContext, createMenuScope] = createContextScope(MENU_NAME, [\n  createCollectionScope,\n  createPopperScope,\n  createRovingFocusGroupScope\n]);\nvar usePopperScope = createPopperScope();\nvar useRovingFocusGroupScope = createRovingFocusGroupScope();\nvar [MenuProvider, useMenuContext] = createMenuContext(MENU_NAME);\nvar [MenuRootProvider, useMenuRootContext] = createMenuContext(MENU_NAME);\nvar Menu = (props) => {\n  const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n  const popperScope = usePopperScope(__scopeMenu);\n  const [content, setContent] = React.useState(null);\n  const isUsingKeyboardRef = React.useRef(false);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n  const direction = useDirection(dir);\n  React.useEffect(() => {\n    const handleKeyDown = () => {\n      isUsingKeyboardRef.current = true;\n      document.addEventListener(\"pointerdown\", handlePointer, { capture: true, once: true });\n      document.addEventListener(\"pointermove\", handlePointer, { capture: true, once: true });\n    };\n    const handlePointer = () => isUsingKeyboardRef.current = false;\n    document.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => {\n      document.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n      document.removeEventListener(\"pointerdown\", handlePointer, { capture: true });\n      document.removeEventListener(\"pointermove\", handlePointer, { capture: true });\n    };\n  }, []);\n  return /* @__PURE__ */ jsx(PopperPrimitive.Root, { ...popperScope, children: /* @__PURE__ */ jsx(\n    MenuProvider,\n    {\n      scope: __scopeMenu,\n      open,\n      onOpenChange: handleOpenChange,\n      content,\n      onContentChange: setContent,\n      children: /* @__PURE__ */ jsx(\n        MenuRootProvider,\n        {\n          scope: __scopeMenu,\n          onClose: React.useCallback(() => handleOpenChange(false), [handleOpenChange]),\n          isUsingKeyboardRef,\n          dir: direction,\n          modal,\n          children\n        }\n      )\n    }\n  ) });\n};\nMenu.displayName = MENU_NAME;\nvar ANCHOR_NAME = \"MenuAnchor\";\nvar MenuAnchor = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return /* @__PURE__ */ jsx(PopperPrimitive.Anchor, { ...popperScope, ...anchorProps, ref: forwardedRef });\n  }\n);\nMenuAnchor.displayName = ANCHOR_NAME;\nvar PORTAL_NAME = \"MenuPortal\";\nvar [PortalProvider, usePortalContext] = createMenuContext(PORTAL_NAME, {\n  forceMount: void 0\n});\nvar MenuPortal = (props) => {\n  const { __scopeMenu, forceMount, children, container } = props;\n  const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n  return /* @__PURE__ */ jsx(PortalProvider, { scope: __scopeMenu, forceMount, children: /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(PortalPrimitive, { asChild: true, container, children }) }) });\n};\nMenuPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"MenuContent\";\nvar [MenuContentProvider, useMenuContentContext] = createMenuContext(CONTENT_NAME);\nvar MenuContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    return /* @__PURE__ */ jsx(Collection.Provider, { scope: props.__scopeMenu, children: /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(Collection.Slot, { scope: props.__scopeMenu, children: rootContext.modal ? /* @__PURE__ */ jsx(MenuRootContentModal, { ...contentProps, ref: forwardedRef }) : /* @__PURE__ */ jsx(MenuRootContentNonModal, { ...contentProps, ref: forwardedRef }) }) }) });\n  }\n);\nvar MenuRootContentModal = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    React.useEffect(() => {\n      const content = ref.current;\n      if (content) return hideOthers(content);\n    }, []);\n    return /* @__PURE__ */ jsx(\n      MenuContentImpl,\n      {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: context.open,\n        disableOutsideScroll: true,\n        onFocusOutside: composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault(),\n          { checkForDefaultPrevented: false }\n        ),\n        onDismiss: () => context.onOpenChange(false)\n      }\n    );\n  }\n);\nvar MenuRootContentNonModal = React.forwardRef((props, forwardedRef) => {\n  const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n  return /* @__PURE__ */ jsx(\n    MenuContentImpl,\n    {\n      ...props,\n      ref: forwardedRef,\n      trapFocus: false,\n      disableOutsidePointerEvents: false,\n      disableOutsideScroll: false,\n      onDismiss: () => context.onOpenChange(false)\n    }\n  );\n});\nvar Slot = createSlot(\"MenuContent.ScrollLock\");\nvar MenuContentImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeMenu,\n      loop = false,\n      trapFocus,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      disableOutsidePointerEvents,\n      onEntryFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      disableOutsideScroll,\n      ...contentProps\n    } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = React.useState(null);\n    const contentRef = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = React.useRef(0);\n    const searchRef = React.useRef(\"\");\n    const pointerGraceTimerRef = React.useRef(0);\n    const pointerGraceIntentRef = React.useRef(null);\n    const pointerDirRef = React.useRef(\"right\");\n    const lastPointerXRef = React.useRef(0);\n    const ScrollLockWrapper = disableOutsideScroll ? RemoveScroll : React.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll ? { as: Slot, allowPinchZoom: true } : void 0;\n    const handleTypeaheadSearch = (key) => {\n      const search = searchRef.current + key;\n      const items = getItems().filter((item) => !item.disabled);\n      const currentItem = document.activeElement;\n      const currentMatch = items.find((item) => item.ref.current === currentItem)?.textValue;\n      const values = items.map((item) => item.textValue);\n      const nextMatch = getNextMatch(values, search, currentMatch);\n      const newItem = items.find((item) => item.textValue === nextMatch)?.ref.current;\n      (function updateSearch(value) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== \"\") timerRef.current = window.setTimeout(() => updateSearch(\"\"), 1e3);\n      })(search);\n      if (newItem) {\n        setTimeout(() => newItem.focus());\n      }\n    };\n    React.useEffect(() => {\n      return () => window.clearTimeout(timerRef.current);\n    }, []);\n    useFocusGuards();\n    const isPointerMovingToSubmenu = React.useCallback((event) => {\n      const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n      return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n    }, []);\n    return /* @__PURE__ */ jsx(\n      MenuContentProvider,\n      {\n        scope: __scopeMenu,\n        searchRef,\n        onItemEnter: React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        ),\n        onItemLeave: React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) return;\n            contentRef.current?.focus();\n            setCurrentItemId(null);\n          },\n          [isPointerMovingToSubmenu]\n        ),\n        onTriggerLeave: React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        ),\n        pointerGraceTimerRef,\n        onPointerGraceIntentChange: React.useCallback((intent) => {\n          pointerGraceIntentRef.current = intent;\n        }, []),\n        children: /* @__PURE__ */ jsx(ScrollLockWrapper, { ...scrollLockWrapperProps, children: /* @__PURE__ */ jsx(\n          FocusScope,\n          {\n            asChild: true,\n            trapped: trapFocus,\n            onMountAutoFocus: composeEventHandlers(onOpenAutoFocus, (event) => {\n              event.preventDefault();\n              contentRef.current?.focus({ preventScroll: true });\n            }),\n            onUnmountAutoFocus: onCloseAutoFocus,\n            children: /* @__PURE__ */ jsx(\n              DismissableLayer,\n              {\n                asChild: true,\n                disableOutsidePointerEvents,\n                onEscapeKeyDown,\n                onPointerDownOutside,\n                onFocusOutside,\n                onInteractOutside,\n                onDismiss,\n                children: /* @__PURE__ */ jsx(\n                  RovingFocusGroup.Root,\n                  {\n                    asChild: true,\n                    ...rovingFocusGroupScope,\n                    dir: rootContext.dir,\n                    orientation: \"vertical\",\n                    loop,\n                    currentTabStopId: currentItemId,\n                    onCurrentTabStopIdChange: setCurrentItemId,\n                    onEntryFocus: composeEventHandlers(onEntryFocus, (event) => {\n                      if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                    }),\n                    preventScrollOnEntryFocus: true,\n                    children: /* @__PURE__ */ jsx(\n                      PopperPrimitive.Content,\n                      {\n                        role: \"menu\",\n                        \"aria-orientation\": \"vertical\",\n                        \"data-state\": getOpenState(context.open),\n                        \"data-radix-menu-content\": \"\",\n                        dir: rootContext.dir,\n                        ...popperScope,\n                        ...contentProps,\n                        ref: composedRefs,\n                        style: { outline: \"none\", ...contentProps.style },\n                        onKeyDown: composeEventHandlers(contentProps.onKeyDown, (event) => {\n                          const target = event.target;\n                          const isKeyDownInside = target.closest(\"[data-radix-menu-content]\") === event.currentTarget;\n                          const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                          const isCharacterKey = event.key.length === 1;\n                          if (isKeyDownInside) {\n                            if (event.key === \"Tab\") event.preventDefault();\n                            if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                          }\n                          const content = contentRef.current;\n                          if (event.target !== content) return;\n                          if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                          event.preventDefault();\n                          const items = getItems().filter((item) => !item.disabled);\n                          const candidateNodes = items.map((item) => item.ref.current);\n                          if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                          focusFirst(candidateNodes);\n                        }),\n                        onBlur: composeEventHandlers(props.onBlur, (event) => {\n                          if (!event.currentTarget.contains(event.target)) {\n                            window.clearTimeout(timerRef.current);\n                            searchRef.current = \"\";\n                          }\n                        }),\n                        onPointerMove: composeEventHandlers(\n                          props.onPointerMove,\n                          whenMouse((event) => {\n                            const target = event.target;\n                            const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n                            if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                              const newDir = event.clientX > lastPointerXRef.current ? \"right\" : \"left\";\n                              pointerDirRef.current = newDir;\n                              lastPointerXRef.current = event.clientX;\n                            }\n                          })\n                        )\n                      }\n                    )\n                  }\n                )\n              }\n            )\n          }\n        ) })\n      }\n    );\n  }\n);\nMenuContent.displayName = CONTENT_NAME;\nvar GROUP_NAME = \"MenuGroup\";\nvar MenuGroup = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenu, ...groupProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.div, { role: \"group\", ...groupProps, ref: forwardedRef });\n  }\n);\nMenuGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"MenuLabel\";\nvar MenuLabel = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenu, ...labelProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.div, { ...labelProps, ref: forwardedRef });\n  }\n);\nMenuLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"MenuItem\";\nvar ITEM_SELECT = \"menu.itemSelect\";\nvar MenuItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = React.useRef(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const isPointerDownRef = React.useRef(false);\n    const handleSelect = () => {\n      const menuItem = ref.current;\n      if (!disabled && menuItem) {\n        const itemSelectEvent = new CustomEvent(ITEM_SELECT, { bubbles: true, cancelable: true });\n        menuItem.addEventListener(ITEM_SELECT, (event) => onSelect?.(event), { once: true });\n        dispatchDiscreteCustomEvent(menuItem, itemSelectEvent);\n        if (itemSelectEvent.defaultPrevented) {\n          isPointerDownRef.current = false;\n        } else {\n          rootContext.onClose();\n        }\n      }\n    };\n    return /* @__PURE__ */ jsx(\n      MenuItemImpl,\n      {\n        ...itemProps,\n        ref: composedRefs,\n        disabled,\n        onClick: composeEventHandlers(props.onClick, handleSelect),\n        onPointerDown: (event) => {\n          props.onPointerDown?.(event);\n          isPointerDownRef.current = true;\n        },\n        onPointerUp: composeEventHandlers(props.onPointerUp, (event) => {\n          if (!isPointerDownRef.current) event.currentTarget?.click();\n        }),\n        onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n          const isTypingAhead = contentContext.searchRef.current !== \"\";\n          if (disabled || isTypingAhead && event.key === \" \") return;\n          if (SELECTION_KEYS.includes(event.key)) {\n            event.currentTarget.click();\n            event.preventDefault();\n          }\n        })\n      }\n    );\n  }\n);\nMenuItem.displayName = ITEM_NAME;\nvar MenuItemImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const [isFocused, setIsFocused] = React.useState(false);\n    const [textContent, setTextContent] = React.useState(\"\");\n    React.useEffect(() => {\n      const menuItem = ref.current;\n      if (menuItem) {\n        setTextContent((menuItem.textContent ?? \"\").trim());\n      }\n    }, [itemProps.children]);\n    return /* @__PURE__ */ jsx(\n      Collection.ItemSlot,\n      {\n        scope: __scopeMenu,\n        disabled,\n        textValue: textValue ?? textContent,\n        children: /* @__PURE__ */ jsx(RovingFocusGroup.Item, { asChild: true, ...rovingFocusGroupScope, focusable: !disabled, children: /* @__PURE__ */ jsx(\n          Primitive.div,\n          {\n            role: \"menuitem\",\n            \"data-highlighted\": isFocused ? \"\" : void 0,\n            \"aria-disabled\": disabled || void 0,\n            \"data-disabled\": disabled ? \"\" : void 0,\n            ...itemProps,\n            ref: composedRefs,\n            onPointerMove: composeEventHandlers(\n              props.onPointerMove,\n              whenMouse((event) => {\n                if (disabled) {\n                  contentContext.onItemLeave(event);\n                } else {\n                  contentContext.onItemEnter(event);\n                  if (!event.defaultPrevented) {\n                    const item = event.currentTarget;\n                    item.focus({ preventScroll: true });\n                  }\n                }\n              })\n            ),\n            onPointerLeave: composeEventHandlers(\n              props.onPointerLeave,\n              whenMouse((event) => contentContext.onItemLeave(event))\n            ),\n            onFocus: composeEventHandlers(props.onFocus, () => setIsFocused(true)),\n            onBlur: composeEventHandlers(props.onBlur, () => setIsFocused(false))\n          }\n        ) })\n      }\n    );\n  }\n);\nvar CHECKBOX_ITEM_NAME = \"MenuCheckboxItem\";\nvar MenuCheckboxItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return /* @__PURE__ */ jsx(ItemIndicatorProvider, { scope: props.__scopeMenu, checked, children: /* @__PURE__ */ jsx(\n      MenuItem,\n      {\n        role: \"menuitemcheckbox\",\n        \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n        ...checkboxItemProps,\n        ref: forwardedRef,\n        \"data-state\": getCheckedState(checked),\n        onSelect: composeEventHandlers(\n          checkboxItemProps.onSelect,\n          () => onCheckedChange?.(isIndeterminate(checked) ? true : !checked),\n          { checkForDefaultPrevented: false }\n        )\n      }\n    ) });\n  }\n);\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\nvar RADIO_GROUP_NAME = \"MenuRadioGroup\";\nvar [RadioGroupProvider, useRadioGroupContext] = createMenuContext(\n  RADIO_GROUP_NAME,\n  { value: void 0, onValueChange: () => {\n  } }\n);\nvar MenuRadioGroup = React.forwardRef(\n  (props, forwardedRef) => {\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = useCallbackRef(onValueChange);\n    return /* @__PURE__ */ jsx(RadioGroupProvider, { scope: props.__scopeMenu, value, onValueChange: handleValueChange, children: /* @__PURE__ */ jsx(MenuGroup, { ...groupProps, ref: forwardedRef }) });\n  }\n);\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\nvar RADIO_ITEM_NAME = \"MenuRadioItem\";\nvar MenuRadioItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return /* @__PURE__ */ jsx(ItemIndicatorProvider, { scope: props.__scopeMenu, checked, children: /* @__PURE__ */ jsx(\n      MenuItem,\n      {\n        role: \"menuitemradio\",\n        \"aria-checked\": checked,\n        ...radioItemProps,\n        ref: forwardedRef,\n        \"data-state\": getCheckedState(checked),\n        onSelect: composeEventHandlers(\n          radioItemProps.onSelect,\n          () => context.onValueChange?.(value),\n          { checkForDefaultPrevented: false }\n        )\n      }\n    ) });\n  }\n);\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\nvar ITEM_INDICATOR_NAME = \"MenuItemIndicator\";\nvar [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext(\n  ITEM_INDICATOR_NAME,\n  { checked: false }\n);\nvar MenuItemIndicator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return /* @__PURE__ */ jsx(\n      Presence,\n      {\n        present: forceMount || isIndeterminate(indicatorContext.checked) || indicatorContext.checked === true,\n        children: /* @__PURE__ */ jsx(\n          Primitive.span,\n          {\n            ...itemIndicatorProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(indicatorContext.checked)\n          }\n        )\n      }\n    );\n  }\n);\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SEPARATOR_NAME = \"MenuSeparator\";\nvar MenuSeparator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenu, ...separatorProps } = props;\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        role: \"separator\",\n        \"aria-orientation\": \"horizontal\",\n        ...separatorProps,\n        ref: forwardedRef\n      }\n    );\n  }\n);\nMenuSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"MenuArrow\";\nvar MenuArrow = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return /* @__PURE__ */ jsx(PopperPrimitive.Arrow, { ...popperScope, ...arrowProps, ref: forwardedRef });\n  }\n);\nMenuArrow.displayName = ARROW_NAME;\nvar SUB_NAME = \"MenuSub\";\nvar [MenuSubProvider, useMenuSubContext] = createMenuContext(SUB_NAME);\nvar MenuSub = (props) => {\n  const { __scopeMenu, children, open = false, onOpenChange } = props;\n  const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n  const popperScope = usePopperScope(__scopeMenu);\n  const [trigger, setTrigger] = React.useState(null);\n  const [content, setContent] = React.useState(null);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n  React.useEffect(() => {\n    if (parentMenuContext.open === false) handleOpenChange(false);\n    return () => handleOpenChange(false);\n  }, [parentMenuContext.open, handleOpenChange]);\n  return /* @__PURE__ */ jsx(PopperPrimitive.Root, { ...popperScope, children: /* @__PURE__ */ jsx(\n    MenuProvider,\n    {\n      scope: __scopeMenu,\n      open,\n      onOpenChange: handleOpenChange,\n      content,\n      onContentChange: setContent,\n      children: /* @__PURE__ */ jsx(\n        MenuSubProvider,\n        {\n          scope: __scopeMenu,\n          contentId: useId(),\n          triggerId: useId(),\n          trigger,\n          onTriggerChange: setTrigger,\n          children\n        }\n      )\n    }\n  ) });\n};\nMenuSub.displayName = SUB_NAME;\nvar SUB_TRIGGER_NAME = \"MenuSubTrigger\";\nvar MenuSubTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = React.useRef(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = { __scopeMenu: props.__scopeMenu };\n    const clearOpenTimer = React.useCallback(() => {\n      if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n      openTimerRef.current = null;\n    }, []);\n    React.useEffect(() => clearOpenTimer, [clearOpenTimer]);\n    React.useEffect(() => {\n      const pointerGraceTimer = pointerGraceTimerRef.current;\n      return () => {\n        window.clearTimeout(pointerGraceTimer);\n        onPointerGraceIntentChange(null);\n      };\n    }, [pointerGraceTimerRef, onPointerGraceIntentChange]);\n    return /* @__PURE__ */ jsx(MenuAnchor, { asChild: true, ...scope, children: /* @__PURE__ */ jsx(\n      MenuItemImpl,\n      {\n        id: subContext.triggerId,\n        \"aria-haspopup\": \"menu\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": subContext.contentId,\n        \"data-state\": getOpenState(context.open),\n        ...props,\n        ref: composeRefs(forwardedRef, subContext.onTriggerChange),\n        onClick: (event) => {\n          props.onClick?.(event);\n          if (props.disabled || event.defaultPrevented) return;\n          event.currentTarget.focus();\n          if (!context.open) context.onOpenChange(true);\n        },\n        onPointerMove: composeEventHandlers(\n          props.onPointerMove,\n          whenMouse((event) => {\n            contentContext.onItemEnter(event);\n            if (event.defaultPrevented) return;\n            if (!props.disabled && !context.open && !openTimerRef.current) {\n              contentContext.onPointerGraceIntentChange(null);\n              openTimerRef.current = window.setTimeout(() => {\n                context.onOpenChange(true);\n                clearOpenTimer();\n              }, 100);\n            }\n          })\n        ),\n        onPointerLeave: composeEventHandlers(\n          props.onPointerLeave,\n          whenMouse((event) => {\n            clearOpenTimer();\n            const contentRect = context.content?.getBoundingClientRect();\n            if (contentRect) {\n              const side = context.content?.dataset.side;\n              const rightSide = side === \"right\";\n              const bleed = rightSide ? -5 : 5;\n              const contentNearEdge = contentRect[rightSide ? \"left\" : \"right\"];\n              const contentFarEdge = contentRect[rightSide ? \"right\" : \"left\"];\n              contentContext.onPointerGraceIntentChange({\n                area: [\n                  // Apply a bleed on clientX to ensure that our exit point is\n                  // consistently within polygon bounds\n                  { x: event.clientX + bleed, y: event.clientY },\n                  { x: contentNearEdge, y: contentRect.top },\n                  { x: contentFarEdge, y: contentRect.top },\n                  { x: contentFarEdge, y: contentRect.bottom },\n                  { x: contentNearEdge, y: contentRect.bottom }\n                ],\n                side\n              });\n              window.clearTimeout(pointerGraceTimerRef.current);\n              pointerGraceTimerRef.current = window.setTimeout(\n                () => contentContext.onPointerGraceIntentChange(null),\n                300\n              );\n            } else {\n              contentContext.onTriggerLeave(event);\n              if (event.defaultPrevented) return;\n              contentContext.onPointerGraceIntentChange(null);\n            }\n          })\n        ),\n        onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n          const isTypingAhead = contentContext.searchRef.current !== \"\";\n          if (props.disabled || isTypingAhead && event.key === \" \") return;\n          if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n            context.onOpenChange(true);\n            context.content?.focus();\n            event.preventDefault();\n          }\n        })\n      }\n    ) });\n  }\n);\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\nvar SUB_CONTENT_NAME = \"MenuSubContent\";\nvar MenuSubContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    return /* @__PURE__ */ jsx(Collection.Provider, { scope: props.__scopeMenu, children: /* @__PURE__ */ jsx(Presence, { present: forceMount || context.open, children: /* @__PURE__ */ jsx(Collection.Slot, { scope: props.__scopeMenu, children: /* @__PURE__ */ jsx(\n      MenuContentImpl,\n      {\n        id: subContext.contentId,\n        \"aria-labelledby\": subContext.triggerId,\n        ...subContentProps,\n        ref: composedRefs,\n        align: \"start\",\n        side: rootContext.dir === \"rtl\" ? \"left\" : \"right\",\n        disableOutsidePointerEvents: false,\n        disableOutsideScroll: false,\n        trapFocus: false,\n        onOpenAutoFocus: (event) => {\n          if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n          event.preventDefault();\n        },\n        onCloseAutoFocus: (event) => event.preventDefault(),\n        onFocusOutside: composeEventHandlers(props.onFocusOutside, (event) => {\n          if (event.target !== subContext.trigger) context.onOpenChange(false);\n        }),\n        onEscapeKeyDown: composeEventHandlers(props.onEscapeKeyDown, (event) => {\n          rootContext.onClose();\n          event.preventDefault();\n        }),\n        onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n          const isKeyDownInside = event.currentTarget.contains(event.target);\n          const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n          if (isKeyDownInside && isCloseKey) {\n            context.onOpenChange(false);\n            subContext.trigger?.focus();\n            event.preventDefault();\n          }\n        })\n      }\n    ) }) }) });\n  }\n);\nMenuSubContent.displayName = SUB_CONTENT_NAME;\nfunction getOpenState(open) {\n  return open ? \"open\" : \"closed\";\n}\nfunction isIndeterminate(checked) {\n  return checked === \"indeterminate\";\n}\nfunction getCheckedState(checked) {\n  return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\nfunction focusFirst(candidates) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus();\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\nfunction wrapArray(array, startIndex) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\nfunction getNextMatch(values, search, currentMatch) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0] : search;\n  const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n  let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n  const excludeCurrentMatch = normalizedSearch.length === 1;\n  if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v) => v !== currentMatch);\n  const nextMatch = wrappedValues.find(\n    (value) => value.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextMatch !== currentMatch ? nextMatch : void 0;\n}\nfunction isPointInPolygon(point, polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const ii = polygon[i];\n    const jj = polygon[j];\n    const xi = ii.x;\n    const yi = ii.y;\n    const xj = jj.x;\n    const yj = jj.y;\n    const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n    if (intersect) inside = !inside;\n  }\n  return inside;\n}\nfunction isPointerInGraceArea(event, area) {\n  if (!area) return false;\n  const cursorPos = { x: event.clientX, y: event.clientY };\n  return isPointInPolygon(cursorPos, area);\n}\nfunction whenMouse(handler) {\n  return (event) => event.pointerType === \"mouse\" ? handler(event) : void 0;\n}\nvar Root3 = Menu;\nvar Anchor2 = MenuAnchor;\nvar Portal = MenuPortal;\nvar Content2 = MenuContent;\nvar Group = MenuGroup;\nvar Label = MenuLabel;\nvar Item2 = MenuItem;\nvar CheckboxItem = MenuCheckboxItem;\nvar RadioGroup = MenuRadioGroup;\nvar RadioItem = MenuRadioItem;\nvar ItemIndicator = MenuItemIndicator;\nvar Separator = MenuSeparator;\nvar Arrow2 = MenuArrow;\nvar Sub = MenuSub;\nvar SubTrigger = MenuSubTrigger;\nvar SubContent = MenuSubContent;\nexport {\n  Anchor2 as Anchor,\n  Arrow2 as Arrow,\n  CheckboxItem,\n  Content2 as Content,\n  Group,\n  Item2 as Item,\n  ItemIndicator,\n  Label,\n  Menu,\n  MenuAnchor,\n  MenuArrow,\n  MenuCheckboxItem,\n  MenuContent,\n  MenuGroup,\n  MenuItem,\n  MenuItemIndicator,\n  MenuLabel,\n  MenuPortal,\n  MenuRadioGroup,\n  MenuRadioItem,\n  MenuSeparator,\n  MenuSub,\n  MenuSubContent,\n  MenuSubTrigger,\n  Portal,\n  RadioGroup,\n  RadioItem,\n  Root3 as Root,\n  Separator,\n  Sub,\n  SubContent,\n  SubTrigger,\n  createMenuScope\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\";\n\n// src/dropdown-menu.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { composeRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport * as MenuPrimitive from \"@radix-ui/react-menu\";\nimport { createMenuScope } from \"@radix-ui/react-menu\";\nimport { useId } from \"@radix-ui/react-id\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DROPDOWN_MENU_NAME = \"DropdownMenu\";\nvar [createDropdownMenuContext, createDropdownMenuScope] = createContextScope(\n  DROPDOWN_MENU_NAME,\n  [createMenuScope]\n);\nvar useMenuScope = createMenuScope();\nvar [DropdownMenuProvider, useDropdownMenuContext] = createDropdownMenuContext(DROPDOWN_MENU_NAME);\nvar DropdownMenu = (props) => {\n  const {\n    __scopeDropdownMenu,\n    children,\n    dir,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true\n  } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const triggerRef = React.useRef(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DROPDOWN_MENU_NAME\n  });\n  return /* @__PURE__ */ jsx(\n    DropdownMenuProvider,\n    {\n      scope: __scopeDropdownMenu,\n      triggerId: useId(),\n      triggerRef,\n      contentId: useId(),\n      open,\n      onOpenChange: setOpen,\n      onOpenToggle: React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen]),\n      modal,\n      children: /* @__PURE__ */ jsx(MenuPrimitive.Root, { ...menuScope, open, onOpenChange: setOpen, dir, modal, children })\n    }\n  );\n};\nDropdownMenu.displayName = DROPDOWN_MENU_NAME;\nvar TRIGGER_NAME = \"DropdownMenuTrigger\";\nvar DropdownMenuTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDropdownMenu, disabled = false, ...triggerProps } = props;\n    const context = useDropdownMenuContext(TRIGGER_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Anchor, { asChild: true, ...menuScope, children: /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        id: context.triggerId,\n        \"aria-haspopup\": \"menu\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.open ? context.contentId : void 0,\n        \"data-state\": context.open ? \"open\" : \"closed\",\n        \"data-disabled\": disabled ? \"\" : void 0,\n        disabled,\n        ...triggerProps,\n        ref: composeRefs(forwardedRef, context.triggerRef),\n        onPointerDown: composeEventHandlers(props.onPointerDown, (event) => {\n          if (!disabled && event.button === 0 && event.ctrlKey === false) {\n            context.onOpenToggle();\n            if (!context.open) event.preventDefault();\n          }\n        }),\n        onKeyDown: composeEventHandlers(props.onKeyDown, (event) => {\n          if (disabled) return;\n          if ([\"Enter\", \" \"].includes(event.key)) context.onOpenToggle();\n          if (event.key === \"ArrowDown\") context.onOpenChange(true);\n          if ([\"Enter\", \" \", \"ArrowDown\"].includes(event.key)) event.preventDefault();\n        })\n      }\n    ) });\n  }\n);\nDropdownMenuTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DropdownMenuPortal\";\nvar DropdownMenuPortal = (props) => {\n  const { __scopeDropdownMenu, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.Portal, { ...menuScope, ...portalProps });\n};\nDropdownMenuPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"DropdownMenuContent\";\nvar DropdownMenuContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDropdownMenu, ...contentProps } = props;\n    const context = useDropdownMenuContext(CONTENT_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const hasInteractedOutsideRef = React.useRef(false);\n    return /* @__PURE__ */ jsx(\n      MenuPrimitive.Content,\n      {\n        id: context.contentId,\n        \"aria-labelledby\": context.triggerId,\n        ...menuScope,\n        ...contentProps,\n        ref: forwardedRef,\n        onCloseAutoFocus: composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n          hasInteractedOutsideRef.current = false;\n          event.preventDefault();\n        }),\n        onInteractOutside: composeEventHandlers(props.onInteractOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n          if (!context.modal || isRightClick) hasInteractedOutsideRef.current = true;\n        }),\n        style: {\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            \"--radix-dropdown-menu-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n            \"--radix-dropdown-menu-content-available-width\": \"var(--radix-popper-available-width)\",\n            \"--radix-dropdown-menu-content-available-height\": \"var(--radix-popper-available-height)\",\n            \"--radix-dropdown-menu-trigger-width\": \"var(--radix-popper-anchor-width)\",\n            \"--radix-dropdown-menu-trigger-height\": \"var(--radix-popper-anchor-height)\"\n          }\n        }\n      }\n    );\n  }\n);\nDropdownMenuContent.displayName = CONTENT_NAME;\nvar GROUP_NAME = \"DropdownMenuGroup\";\nvar DropdownMenuGroup = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDropdownMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Group, { ...menuScope, ...groupProps, ref: forwardedRef });\n  }\n);\nDropdownMenuGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"DropdownMenuLabel\";\nvar DropdownMenuLabel = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDropdownMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Label, { ...menuScope, ...labelProps, ref: forwardedRef });\n  }\n);\nDropdownMenuLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"DropdownMenuItem\";\nvar DropdownMenuItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDropdownMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Item, { ...menuScope, ...itemProps, ref: forwardedRef });\n  }\n);\nDropdownMenuItem.displayName = ITEM_NAME;\nvar CHECKBOX_ITEM_NAME = \"DropdownMenuCheckboxItem\";\nvar DropdownMenuCheckboxItem = React.forwardRef((props, forwardedRef) => {\n  const { __scopeDropdownMenu, ...checkboxItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.CheckboxItem, { ...menuScope, ...checkboxItemProps, ref: forwardedRef });\n});\nDropdownMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\nvar RADIO_GROUP_NAME = \"DropdownMenuRadioGroup\";\nvar DropdownMenuRadioGroup = React.forwardRef((props, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioGroupProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.RadioGroup, { ...menuScope, ...radioGroupProps, ref: forwardedRef });\n});\nDropdownMenuRadioGroup.displayName = RADIO_GROUP_NAME;\nvar RADIO_ITEM_NAME = \"DropdownMenuRadioItem\";\nvar DropdownMenuRadioItem = React.forwardRef((props, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.RadioItem, { ...menuScope, ...radioItemProps, ref: forwardedRef });\n});\nDropdownMenuRadioItem.displayName = RADIO_ITEM_NAME;\nvar INDICATOR_NAME = \"DropdownMenuItemIndicator\";\nvar DropdownMenuItemIndicator = React.forwardRef((props, forwardedRef) => {\n  const { __scopeDropdownMenu, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.ItemIndicator, { ...menuScope, ...itemIndicatorProps, ref: forwardedRef });\n});\nDropdownMenuItemIndicator.displayName = INDICATOR_NAME;\nvar SEPARATOR_NAME = \"DropdownMenuSeparator\";\nvar DropdownMenuSeparator = React.forwardRef((props, forwardedRef) => {\n  const { __scopeDropdownMenu, ...separatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.Separator, { ...menuScope, ...separatorProps, ref: forwardedRef });\n});\nDropdownMenuSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"DropdownMenuArrow\";\nvar DropdownMenuArrow = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeDropdownMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ jsx(MenuPrimitive.Arrow, { ...menuScope, ...arrowProps, ref: forwardedRef });\n  }\n);\nDropdownMenuArrow.displayName = ARROW_NAME;\nvar DropdownMenuSub = (props) => {\n  const { __scopeDropdownMenu, children, open: openProp, onOpenChange, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: \"DropdownMenuSub\"\n  });\n  return /* @__PURE__ */ jsx(MenuPrimitive.Sub, { ...menuScope, open, onOpenChange: setOpen, children });\n};\nvar SUB_TRIGGER_NAME = \"DropdownMenuSubTrigger\";\nvar DropdownMenuSubTrigger = React.forwardRef((props, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subTriggerProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(MenuPrimitive.SubTrigger, { ...menuScope, ...subTriggerProps, ref: forwardedRef });\n});\nDropdownMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\nvar SUB_CONTENT_NAME = \"DropdownMenuSubContent\";\nvar DropdownMenuSubContent = React.forwardRef((props, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subContentProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return /* @__PURE__ */ jsx(\n    MenuPrimitive.SubContent,\n    {\n      ...menuScope,\n      ...subContentProps,\n      ref: forwardedRef,\n      style: {\n        ...props.style,\n        // re-namespace exposed content custom properties\n        ...{\n          \"--radix-dropdown-menu-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n          \"--radix-dropdown-menu-content-available-width\": \"var(--radix-popper-available-width)\",\n          \"--radix-dropdown-menu-content-available-height\": \"var(--radix-popper-available-height)\",\n          \"--radix-dropdown-menu-trigger-width\": \"var(--radix-popper-anchor-width)\",\n          \"--radix-dropdown-menu-trigger-height\": \"var(--radix-popper-anchor-height)\"\n        }\n      }\n    }\n  );\n});\nDropdownMenuSubContent.displayName = SUB_CONTENT_NAME;\nvar Root2 = DropdownMenu;\nvar Trigger = DropdownMenuTrigger;\nvar Portal2 = DropdownMenuPortal;\nvar Content2 = DropdownMenuContent;\nvar Group2 = DropdownMenuGroup;\nvar Label2 = DropdownMenuLabel;\nvar Item2 = DropdownMenuItem;\nvar CheckboxItem2 = DropdownMenuCheckboxItem;\nvar RadioGroup2 = DropdownMenuRadioGroup;\nvar RadioItem2 = DropdownMenuRadioItem;\nvar ItemIndicator2 = DropdownMenuItemIndicator;\nvar Separator2 = DropdownMenuSeparator;\nvar Arrow2 = DropdownMenuArrow;\nvar Sub2 = DropdownMenuSub;\nvar SubTrigger2 = DropdownMenuSubTrigger;\nvar SubContent2 = DropdownMenuSubContent;\nexport {\n  Arrow2 as Arrow,\n  CheckboxItem2 as CheckboxItem,\n  Content2 as Content,\n  DropdownMenu,\n  DropdownMenuArrow,\n  DropdownMenuCheckboxItem,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuItem,\n  DropdownMenuItemIndicator,\n  DropdownMenuLabel,\n  DropdownMenuPortal,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuTrigger,\n  Group2 as Group,\n  Item2 as Item,\n  ItemIndicator2 as ItemIndicator,\n  Label2 as Label,\n  Portal2 as Portal,\n  RadioGroup2 as RadioGroup,\n  RadioItem2 as RadioItem,\n  Root2 as Root,\n  Separator2 as Separator,\n  Sub2 as Sub,\n  SubContent2 as SubContent,\n  SubTrigger2 as SubTrigger,\n  Trigger,\n  createDropdownMenuScope\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }]];\n\n/**\n * @component @name Circle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Circle = createLucideIcon('circle', __iconNode);\n\nexport default Circle;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm9 18 6-6-6-6', key: 'mthhwq' }]];\n\n/**\n * @component @name ChevronRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSAxOCA2LTYtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRight = createLucideIcon('chevron-right', __iconNode);\n\nexport default ChevronRight;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '1', key: '41hilf' }],\n  ['circle', { cx: '19', cy: '12', r: '1', key: '1wjl8i' }],\n  ['circle', { cx: '5', cy: '12', r: '1', key: '1pcz8c' }],\n];\n\n/**\n * @component @name Ellipsis\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxIiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iMTIiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iNSIgY3k9IjEyIiByPSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/ellipsis\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Ellipsis = createLucideIcon('ellipsis', __iconNode);\n\nexport default Ellipsis;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7', key: '1m0v6g' }],\n  [\n    'path',\n    {\n      d: 'M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z',\n      key: 'ohrbg2',\n    },\n  ],\n];\n\n/**\n * @component @name SquarePen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM0g1YTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxNGEyIDIgMCAwIDAgMi0ydi03IiAvPgogIDxwYXRoIGQ9Ik0xOC4zNzUgMi42MjVhMSAxIDAgMCAxIDMgM2wtOS4wMTMgOS4wMTRhMiAyIDAgMCAxLS44NTMuNTA1bC0yLjg3My44NGEuNS41IDAgMCAxLS42Mi0uNjJsLjg0LTIuODczYTIgMiAwIDAgMSAuNTA2LS44NTJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square-pen\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SquarePen = createLucideIcon('square-pen', __iconNode);\n\nexport default SquarePen;\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as MenuPrimitive from '@radix-ui/react-menu';\nimport { createMenuScope } from '@radix-ui/react-menu';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst DROPDOWN_MENU_NAME = 'DropdownMenu';\n\ntype ScopedProps<P> = P & { __scopeDropdownMenu?: Scope };\nconst [createDropdownMenuContext, createDropdownMenuScope] = createContextScope(\n  DROPDOWN_MENU_NAME,\n  [createMenuScope]\n);\nconst useMenuScope = createMenuScope();\n\ntype DropdownMenuContextValue = {\n  triggerId: string;\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DropdownMenuProvider, useDropdownMenuContext] =\n  createDropdownMenuContext<DropdownMenuContextValue>(DROPDOWN_MENU_NAME);\n\ninterface DropdownMenuProps {\n  children?: React.ReactNode;\n  dir?: Direction;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst DropdownMenu: React.FC<DropdownMenuProps> = (props: ScopedProps<DropdownMenuProps>) => {\n  const {\n    __scopeDropdownMenu,\n    children,\n    dir,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DROPDOWN_MENU_NAME,\n  });\n\n  return (\n    <DropdownMenuProvider\n      scope={__scopeDropdownMenu}\n      triggerId={useId()}\n      triggerRef={triggerRef}\n      contentId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      <MenuPrimitive.Root {...menuScope} open={open} onOpenChange={setOpen} dir={dir} modal={modal}>\n        {children}\n      </MenuPrimitive.Root>\n    </DropdownMenuProvider>\n  );\n};\n\nDropdownMenu.displayName = DROPDOWN_MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DropdownMenuTrigger';\n\ntype DropdownMenuTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DropdownMenuTriggerProps extends PrimitiveButtonProps {}\n\nconst DropdownMenuTrigger = React.forwardRef<DropdownMenuTriggerElement, DropdownMenuTriggerProps>(\n  (props: ScopedProps<DropdownMenuTriggerProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, disabled = false, ...triggerProps } = props;\n    const context = useDropdownMenuContext(TRIGGER_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return (\n      <MenuPrimitive.Anchor asChild {...menuScope}>\n        <Primitive.button\n          type=\"button\"\n          id={context.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={context.open ? context.contentId : undefined}\n          data-state={context.open ? 'open' : 'closed'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          {...triggerProps}\n          ref={composeRefs(forwardedRef, context.triggerRef)}\n          onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onOpenToggle();\n              // prevent trigger focusing when opening\n              // this allows the content to be given focus without competition\n              if (!context.open) event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (disabled) return;\n            if (['Enter', ' '].includes(event.key)) context.onOpenToggle();\n            if (event.key === 'ArrowDown') context.onOpenChange(true);\n            // prevent keydown from scrolling window / first focused item to execute\n            // that keydown (inadvertently closing the menu)\n            if (['Enter', ' ', 'ArrowDown'].includes(event.key)) event.preventDefault();\n          })}\n        />\n      </MenuPrimitive.Anchor>\n    );\n  }\n);\n\nDropdownMenuTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DropdownMenuPortal';\n\ntype MenuPortalProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Portal>;\ninterface DropdownMenuPortalProps extends MenuPortalProps {}\n\nconst DropdownMenuPortal: React.FC<DropdownMenuPortalProps> = (\n  props: ScopedProps<DropdownMenuPortalProps>\n) => {\n  const { __scopeDropdownMenu, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Portal {...menuScope} {...portalProps} />;\n};\n\nDropdownMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DropdownMenuContent';\n\ntype DropdownMenuContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Content>;\ninterface DropdownMenuContentProps extends Omit<MenuContentProps, 'onEntryFocus'> {}\n\nconst DropdownMenuContent = React.forwardRef<DropdownMenuContentElement, DropdownMenuContentProps>(\n  (props: ScopedProps<DropdownMenuContentProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...contentProps } = props;\n    const context = useDropdownMenuContext(CONTENT_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const hasInteractedOutsideRef = React.useRef(false);\n\n    return (\n      <MenuPrimitive.Content\n        id={context.contentId}\n        aria-labelledby={context.triggerId}\n        {...menuScope}\n        {...contentProps}\n        ref={forwardedRef}\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n          hasInteractedOutsideRef.current = false;\n          // Always prevent auto focus because we either focus manually or want user agent focus\n          event.preventDefault();\n        })}\n        onInteractOutside={composeEventHandlers(props.onInteractOutside, (event) => {\n          const originalEvent = event.detail.originalEvent as PointerEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n          if (!context.modal || isRightClick) hasInteractedOutsideRef.current = true;\n        })}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-dropdown-menu-content-transform-origin':\n              'var(--radix-popper-transform-origin)',\n            '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-dropdown-menu-content-available-height':\n              'var(--radix-popper-available-height)',\n            '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nDropdownMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'DropdownMenuGroup';\n\ntype DropdownMenuGroupElement = React.ComponentRef<typeof MenuPrimitive.Group>;\ntype MenuGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Group>;\ninterface DropdownMenuGroupProps extends MenuGroupProps {}\n\nconst DropdownMenuGroup = React.forwardRef<DropdownMenuGroupElement, DropdownMenuGroupProps>(\n  (props: ScopedProps<DropdownMenuGroupProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Group {...menuScope} {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'DropdownMenuLabel';\n\ntype DropdownMenuLabelElement = React.ComponentRef<typeof MenuPrimitive.Label>;\ntype MenuLabelProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Label>;\ninterface DropdownMenuLabelProps extends MenuLabelProps {}\n\nconst DropdownMenuLabel = React.forwardRef<DropdownMenuLabelElement, DropdownMenuLabelProps>(\n  (props: ScopedProps<DropdownMenuLabelProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Label {...menuScope} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'DropdownMenuItem';\n\ntype DropdownMenuItemElement = React.ComponentRef<typeof MenuPrimitive.Item>;\ntype MenuItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Item>;\ninterface DropdownMenuItemProps extends MenuItemProps {}\n\nconst DropdownMenuItem = React.forwardRef<DropdownMenuItemElement, DropdownMenuItemProps>(\n  (props: ScopedProps<DropdownMenuItemProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Item {...menuScope} {...itemProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'DropdownMenuCheckboxItem';\n\ntype DropdownMenuCheckboxItemElement = React.ComponentRef<typeof MenuPrimitive.CheckboxItem>;\ntype MenuCheckboxItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.CheckboxItem>;\ninterface DropdownMenuCheckboxItemProps extends MenuCheckboxItemProps {}\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  DropdownMenuCheckboxItemElement,\n  DropdownMenuCheckboxItemProps\n>((props: ScopedProps<DropdownMenuCheckboxItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...checkboxItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.CheckboxItem {...menuScope} {...checkboxItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'DropdownMenuRadioGroup';\n\ntype DropdownMenuRadioGroupElement = React.ComponentRef<typeof MenuPrimitive.RadioGroup>;\ntype MenuRadioGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioGroup>;\ninterface DropdownMenuRadioGroupProps extends MenuRadioGroupProps {}\n\nconst DropdownMenuRadioGroup = React.forwardRef<\n  DropdownMenuRadioGroupElement,\n  DropdownMenuRadioGroupProps\n>((props: ScopedProps<DropdownMenuRadioGroupProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioGroupProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioGroup {...menuScope} {...radioGroupProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'DropdownMenuRadioItem';\n\ntype DropdownMenuRadioItemElement = React.ComponentRef<typeof MenuPrimitive.RadioItem>;\ntype MenuRadioItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioItem>;\ninterface DropdownMenuRadioItemProps extends MenuRadioItemProps {}\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  DropdownMenuRadioItemElement,\n  DropdownMenuRadioItemProps\n>((props: ScopedProps<DropdownMenuRadioItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioItem {...menuScope} {...radioItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'DropdownMenuItemIndicator';\n\ntype DropdownMenuItemIndicatorElement = React.ComponentRef<typeof MenuPrimitive.ItemIndicator>;\ntype MenuItemIndicatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.ItemIndicator>;\ninterface DropdownMenuItemIndicatorProps extends MenuItemIndicatorProps {}\n\nconst DropdownMenuItemIndicator = React.forwardRef<\n  DropdownMenuItemIndicatorElement,\n  DropdownMenuItemIndicatorProps\n>((props: ScopedProps<DropdownMenuItemIndicatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.ItemIndicator {...menuScope} {...itemIndicatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuItemIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'DropdownMenuSeparator';\n\ntype DropdownMenuSeparatorElement = React.ComponentRef<typeof MenuPrimitive.Separator>;\ntype MenuSeparatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Separator>;\ninterface DropdownMenuSeparatorProps extends MenuSeparatorProps {}\n\nconst DropdownMenuSeparator = React.forwardRef<\n  DropdownMenuSeparatorElement,\n  DropdownMenuSeparatorProps\n>((props: ScopedProps<DropdownMenuSeparatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...separatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Separator {...menuScope} {...separatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'DropdownMenuArrow';\n\ntype DropdownMenuArrowElement = React.ComponentRef<typeof MenuPrimitive.Arrow>;\ntype MenuArrowProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Arrow>;\ninterface DropdownMenuArrowProps extends MenuArrowProps {}\n\nconst DropdownMenuArrow = React.forwardRef<DropdownMenuArrowElement, DropdownMenuArrowProps>(\n  (props: ScopedProps<DropdownMenuArrowProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Arrow {...menuScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSub\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DropdownMenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst DropdownMenuSub: React.FC<DropdownMenuSubProps> = (\n  props: ScopedProps<DropdownMenuSubProps>\n) => {\n  const { __scopeDropdownMenu, children, open: openProp, onOpenChange, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: 'DropdownMenuSub',\n  });\n\n  return (\n    <MenuPrimitive.Sub {...menuScope} open={open} onOpenChange={setOpen}>\n      {children}\n    </MenuPrimitive.Sub>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'DropdownMenuSubTrigger';\n\ntype DropdownMenuSubTriggerElement = React.ComponentRef<typeof MenuPrimitive.SubTrigger>;\ntype MenuSubTriggerProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubTrigger>;\ninterface DropdownMenuSubTriggerProps extends MenuSubTriggerProps {}\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  DropdownMenuSubTriggerElement,\n  DropdownMenuSubTriggerProps\n>((props: ScopedProps<DropdownMenuSubTriggerProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subTriggerProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.SubTrigger {...menuScope} {...subTriggerProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'DropdownMenuSubContent';\n\ntype DropdownMenuSubContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuSubContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubContent>;\ninterface DropdownMenuSubContentProps extends MenuSubContentProps {}\n\nconst DropdownMenuSubContent = React.forwardRef<\n  DropdownMenuSubContentElement,\n  DropdownMenuSubContentProps\n>((props: ScopedProps<DropdownMenuSubContentProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subContentProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n\n  return (\n    <MenuPrimitive.SubContent\n      {...menuScope}\n      {...subContentProps}\n      ref={forwardedRef}\n      style={{\n        ...props.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-dropdown-menu-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-dropdown-menu-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nDropdownMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = DropdownMenu;\nconst Trigger = DropdownMenuTrigger;\nconst Portal = DropdownMenuPortal;\nconst Content = DropdownMenuContent;\nconst Group = DropdownMenuGroup;\nconst Label = DropdownMenuLabel;\nconst Item = DropdownMenuItem;\nconst CheckboxItem = DropdownMenuCheckboxItem;\nconst RadioGroup = DropdownMenuRadioGroup;\nconst RadioItem = DropdownMenuRadioItem;\nconst ItemIndicator = DropdownMenuItemIndicator;\nconst Separator = DropdownMenuSeparator;\nconst Arrow = DropdownMenuArrow;\nconst Sub = DropdownMenuSub;\nconst SubTrigger = DropdownMenuSubTrigger;\nconst SubContent = DropdownMenuSubContent;\n\nexport {\n  createDropdownMenuScope,\n  //\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuPortal,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuItemIndicator,\n  DropdownMenuSeparator,\n  DropdownMenuArrow,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  DropdownMenuProps,\n  DropdownMenuTriggerProps,\n  DropdownMenuPortalProps,\n  DropdownMenuContentProps,\n  DropdownMenuGroupProps,\n  DropdownMenuLabelProps,\n  DropdownMenuItemProps,\n  DropdownMenuCheckboxItemProps,\n  DropdownMenuRadioGroupProps,\n  DropdownMenuRadioItemProps,\n  DropdownMenuItemIndicatorProps,\n  DropdownMenuSeparatorProps,\n  DropdownMenuArrowProps,\n  DropdownMenuSubProps,\n  DropdownMenuSubTriggerProps,\n  DropdownMenuSubContentProps,\n};\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n  preventScrollOnEntryFocus?: boolean;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes, preventScrollOnEntryFocus);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends Omit<PrimitiveSpanProps, 'children'> {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n  children?:\n    | React.ReactNode\n    | ((props: { hasTabStop: boolean; isCurrentTabStop: boolean }) => React.ReactNode);\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        >\n          {typeof children === 'function'\n            ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null })\n            : children}\n        </Primitive.span>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[], preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs, composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst SELECTION_KEYS = ['Enter', ' '];\nconst FIRST_KEYS = ['ArrowDown', 'PageUp', 'Home'];\nconst LAST_KEYS = ['ArrowUp', 'PageDown', 'End'];\nconst FIRST_LAST_KEYS = [...FIRST_KEYS, ...LAST_KEYS];\nconst SUB_OPEN_KEYS: Record<Direction, string[]> = {\n  ltr: [...SELECTION_KEYS, 'ArrowRight'],\n  rtl: [...SELECTION_KEYS, 'ArrowLeft'],\n};\nconst SUB_CLOSE_KEYS: Record<Direction, string[]> = {\n  ltr: ['ArrowLeft'],\n  rtl: ['ArrowRight'],\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Menu\n * -----------------------------------------------------------------------------------------------*/\n\nconst MENU_NAME = 'Menu';\n\ntype ItemData = { disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  MenuItemElement,\n  ItemData\n>(MENU_NAME);\n\ntype ScopedProps<P> = P & { __scopeMenu?: Scope };\nconst [createMenuContext, createMenuScope] = createContextScope(MENU_NAME, [\n  createCollectionScope,\n  createPopperScope,\n  createRovingFocusGroupScope,\n]);\nconst usePopperScope = createPopperScope();\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype MenuContextValue = {\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  content: MenuContentElement | null;\n  onContentChange(content: MenuContentElement | null): void;\n};\n\nconst [MenuProvider, useMenuContext] = createMenuContext<MenuContextValue>(MENU_NAME);\n\ntype MenuRootContextValue = {\n  onClose(): void;\n  isUsingKeyboardRef: React.RefObject<boolean>;\n  dir: Direction;\n  modal: boolean;\n};\n\nconst [MenuRootProvider, useMenuRootContext] = createMenuContext<MenuRootContextValue>(MENU_NAME);\n\ninterface MenuProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  modal?: boolean;\n}\n\nconst Menu: React.FC<MenuProps> = (props: ScopedProps<MenuProps>) => {\n  const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n  const popperScope = usePopperScope(__scopeMenu);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const isUsingKeyboardRef = React.useRef(false);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n  const direction = useDirection(dir);\n\n  React.useEffect(() => {\n    // Capture phase ensures we set the boolean before any side effects execute\n    // in response to the key or pointer event as they might depend on this value.\n    const handleKeyDown = () => {\n      isUsingKeyboardRef.current = true;\n      document.addEventListener('pointerdown', handlePointer, { capture: true, once: true });\n      document.addEventListener('pointermove', handlePointer, { capture: true, once: true });\n    };\n    const handlePointer = () => (isUsingKeyboardRef.current = false);\n    document.addEventListener('keydown', handleKeyDown, { capture: true });\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown, { capture: true });\n      document.removeEventListener('pointerdown', handlePointer, { capture: true });\n      document.removeEventListener('pointermove', handlePointer, { capture: true });\n    };\n  }, []);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuRootProvider\n          scope={__scopeMenu}\n          onClose={React.useCallback(() => handleOpenChange(false), [handleOpenChange])}\n          isUsingKeyboardRef={isUsingKeyboardRef}\n          dir={direction}\n          modal={modal}\n        >\n          {children}\n        </MenuRootProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenu.displayName = MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'MenuAnchor';\n\ntype MenuAnchorElement = React.ComponentRef<typeof PopperPrimitive.Anchor>;\ntype PopperAnchorProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Anchor>;\ninterface MenuAnchorProps extends PopperAnchorProps {}\n\nconst MenuAnchor = React.forwardRef<MenuAnchorElement, MenuAnchorProps>(\n  (props: ScopedProps<MenuAnchorProps>, forwardedRef) => {\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Anchor {...popperScope} {...anchorProps} ref={forwardedRef} />;\n  }\n);\n\nMenuAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'MenuPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createMenuContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface MenuPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuPortal: React.FC<MenuPortalProps> = (props: ScopedProps<MenuPortalProps>) => {\n  const { __scopeMenu, forceMount, children, container } = props;\n  const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n  return (\n    <PortalProvider scope={__scopeMenu} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'MenuContent';\n\ntype MenuContentContextValue = {\n  onItemEnter(event: React.PointerEvent): void;\n  onItemLeave(event: React.PointerEvent): void;\n  onTriggerLeave(event: React.PointerEvent): void;\n  searchRef: React.RefObject<string>;\n  pointerGraceTimerRef: React.MutableRefObject<number>;\n  onPointerGraceIntentChange(intent: GraceIntent | null): void;\n};\nconst [MenuContentProvider, useMenuContentContext] =\n  createMenuContext<MenuContentContextValue>(CONTENT_NAME);\n\ntype MenuContentElement = MenuRootContentTypeElement;\n/**\n * We purposefully don't union MenuRootContent and MenuSubContent props here because\n * they have conflicting prop types. We agreed that we would allow MenuSubContent to\n * accept props that it would just ignore.\n */\ninterface MenuContentProps extends MenuRootContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuContent = React.forwardRef<MenuContentElement, MenuContentProps>(\n  (props: ScopedProps<MenuContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            {rootContext.modal ? (\n              <MenuRootContentModal {...contentProps} ref={forwardedRef} />\n            ) : (\n              <MenuRootContentNonModal {...contentProps} ref={forwardedRef} />\n            )}\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuRootContentTypeElement = MenuContentImplElement;\ninterface MenuRootContentTypeProps\n  extends Omit<MenuContentImplProps, keyof MenuContentImplPrivateProps> {}\n\nconst MenuRootContentModal = React.forwardRef<MenuRootContentTypeElement, MenuRootContentTypeProps>(\n  (props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuRootContentTypeElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    // Hide everything from ARIA except the `MenuContent`\n    React.useEffect(() => {\n      const content = ref.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <MenuContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure we're not trapping once it's been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        // make sure to only disable pointer events when open\n        // this avoids blocking interactions while animating out\n        disableOutsidePointerEvents={context.open}\n        disableOutsideScroll\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault(),\n          { checkForDefaultPrevented: false }\n        )}\n        onDismiss={() => context.onOpenChange(false)}\n      />\n    );\n  }\n);\n\nconst MenuRootContentNonModal = React.forwardRef<\n  MenuRootContentTypeElement,\n  MenuRootContentTypeProps\n>((props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n  const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n  return (\n    <MenuContentImpl\n      {...props}\n      ref={forwardedRef}\n      trapFocus={false}\n      disableOutsidePointerEvents={false}\n      disableOutsideScroll={false}\n      onDismiss={() => context.onOpenChange(false)}\n    />\n  );\n});\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuContentImplElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ntype MenuContentImplPrivateProps = {\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n  onDismiss?: DismissableLayerProps['onDismiss'];\n  disableOutsidePointerEvents?: DismissableLayerProps['disableOutsidePointerEvents'];\n\n  /**\n   * Whether scrolling outside the `MenuContent` should be prevented\n   * (default: `false`)\n   */\n  disableOutsideScroll?: boolean;\n\n  /**\n   * Whether focus should be trapped within the `MenuContent`\n   * (default: false)\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n};\ninterface MenuContentImplProps\n  extends MenuContentImplPrivateProps,\n    Omit<PopperContentProps, 'dir' | 'onPlaced'> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: RovingFocusGroupProps['loop'];\n\n  onEntryFocus?: RovingFocusGroupProps['onEntryFocus'];\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n  onFocusOutside?: DismissableLayerProps['onFocusOutside'];\n  onInteractOutside?: DismissableLayerProps['onInteractOutside'];\n}\n\nconst Slot = createSlot('MenuContent.ScrollLock');\n\nconst MenuContentImpl = React.forwardRef<MenuContentImplElement, MenuContentImplProps>(\n  (props: ScopedProps<MenuContentImplProps>, forwardedRef) => {\n    const {\n      __scopeMenu,\n      loop = false,\n      trapFocus,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      disableOutsidePointerEvents,\n      onEntryFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      disableOutsideScroll,\n      ...contentProps\n    } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = React.useState<string | null>(null);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = React.useRef(0);\n    const searchRef = React.useRef('');\n    const pointerGraceTimerRef = React.useRef(0);\n    const pointerGraceIntentRef = React.useRef<GraceIntent | null>(null);\n    const pointerDirRef = React.useRef<Side>('right');\n    const lastPointerXRef = React.useRef(0);\n\n    const ScrollLockWrapper = disableOutsideScroll ? RemoveScroll : React.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll\n      ? { as: Slot, allowPinchZoom: true }\n      : undefined;\n\n    const handleTypeaheadSearch = (key: string) => {\n      const search = searchRef.current + key;\n      const items = getItems().filter((item) => !item.disabled);\n      const currentItem = document.activeElement;\n      const currentMatch = items.find((item) => item.ref.current === currentItem)?.textValue;\n      const values = items.map((item) => item.textValue);\n      const nextMatch = getNextMatch(values, search, currentMatch);\n      const newItem = items.find((item) => item.textValue === nextMatch)?.ref.current;\n\n      // Reset `searchRef` 1 second after it was last updated\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n\n      if (newItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (newItem as HTMLElement).focus());\n      }\n    };\n\n    React.useEffect(() => {\n      return () => window.clearTimeout(timerRef.current);\n    }, []);\n\n    // Make sure the whole tree has focus guards as our `MenuContent` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const isPointerMovingToSubmenu = React.useCallback((event: React.PointerEvent) => {\n      const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n      return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n    }, []);\n\n    return (\n      <MenuContentProvider\n        scope={__scopeMenu}\n        searchRef={searchRef}\n        onItemEnter={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onItemLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) return;\n            contentRef.current?.focus();\n            setCurrentItemId(null);\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onTriggerLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        pointerGraceTimerRef={pointerGraceTimerRef}\n        onPointerGraceIntentChange={React.useCallback((intent) => {\n          pointerGraceIntentRef.current = intent;\n        }, [])}\n      >\n        <ScrollLockWrapper {...scrollLockWrapperProps}>\n          <FocusScope\n            asChild\n            trapped={trapFocus}\n            onMountAutoFocus={composeEventHandlers(onOpenAutoFocus, (event) => {\n              // when opening, explicitly focus the content area only and leave\n              // `onEntryFocus` in  control of focusing first item\n              event.preventDefault();\n              contentRef.current?.focus({ preventScroll: true });\n            })}\n            onUnmountAutoFocus={onCloseAutoFocus}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents={disableOutsidePointerEvents}\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              onFocusOutside={onFocusOutside}\n              onInteractOutside={onInteractOutside}\n              onDismiss={onDismiss}\n            >\n              <RovingFocusGroup.Root\n                asChild\n                {...rovingFocusGroupScope}\n                dir={rootContext.dir}\n                orientation=\"vertical\"\n                loop={loop}\n                currentTabStopId={currentItemId}\n                onCurrentTabStopIdChange={setCurrentItemId}\n                onEntryFocus={composeEventHandlers(onEntryFocus, (event) => {\n                  // only focus first item when using keyboard\n                  if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                })}\n                preventScrollOnEntryFocus\n              >\n                <PopperPrimitive.Content\n                  role=\"menu\"\n                  aria-orientation=\"vertical\"\n                  data-state={getOpenState(context.open)}\n                  data-radix-menu-content=\"\"\n                  dir={rootContext.dir}\n                  {...popperScope}\n                  {...contentProps}\n                  ref={composedRefs}\n                  style={{ outline: 'none', ...contentProps.style }}\n                  onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                    // submenu key events bubble through portals. We only care about keys in this menu.\n                    const target = event.target as HTMLElement;\n                    const isKeyDownInside =\n                      target.closest('[data-radix-menu-content]') === event.currentTarget;\n                    const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                    const isCharacterKey = event.key.length === 1;\n                    if (isKeyDownInside) {\n                      // menus should not be navigated using tab key so we prevent it\n                      if (event.key === 'Tab') event.preventDefault();\n                      if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                    }\n                    // focus first/last item based on key pressed\n                    const content = contentRef.current;\n                    if (event.target !== content) return;\n                    if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item) => !item.disabled);\n                    const candidateNodes = items.map((item) => item.ref.current!);\n                    if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                    focusFirst(candidateNodes);\n                  })}\n                  onBlur={composeEventHandlers(props.onBlur, (event) => {\n                    // clear search buffer when leaving the menu\n                    if (!event.currentTarget.contains(event.target)) {\n                      window.clearTimeout(timerRef.current);\n                      searchRef.current = '';\n                    }\n                  })}\n                  onPointerMove={composeEventHandlers(\n                    props.onPointerMove,\n                    whenMouse((event) => {\n                      const target = event.target as HTMLElement;\n                      const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n\n                      // We don't use `event.movementX` for this check because Safari will\n                      // always return `0` on a pointer event.\n                      if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                        const newDir = event.clientX > lastPointerXRef.current ? 'right' : 'left';\n                        pointerDirRef.current = newDir;\n                        lastPointerXRef.current = event.clientX;\n                      }\n                    })\n                  )}\n                />\n              </RovingFocusGroup.Root>\n            </DismissableLayer>\n          </FocusScope>\n        </ScrollLockWrapper>\n      </MenuContentProvider>\n    );\n  }\n);\n\nMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'MenuGroup';\n\ntype MenuGroupElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface MenuGroupProps extends PrimitiveDivProps {}\n\nconst MenuGroup = React.forwardRef<MenuGroupElement, MenuGroupProps>(\n  (props: ScopedProps<MenuGroupProps>, forwardedRef) => {\n    const { __scopeMenu, ...groupProps } = props;\n    return <Primitive.div role=\"group\" {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'MenuLabel';\n\ntype MenuLabelElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuLabelProps extends PrimitiveDivProps {}\n\nconst MenuLabel = React.forwardRef<MenuLabelElement, MenuLabelProps>(\n  (props: ScopedProps<MenuLabelProps>, forwardedRef) => {\n    const { __scopeMenu, ...labelProps } = props;\n    return <Primitive.div {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'MenuItem';\nconst ITEM_SELECT = 'menu.itemSelect';\n\ntype MenuItemElement = MenuItemImplElement;\ninterface MenuItemProps extends Omit<MenuItemImplProps, 'onSelect'> {\n  onSelect?: (event: Event) => void;\n}\n\nconst MenuItem = React.forwardRef<MenuItemElement, MenuItemProps>(\n  (props: ScopedProps<MenuItemProps>, forwardedRef) => {\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = React.useRef<HTMLDivElement>(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const isPointerDownRef = React.useRef(false);\n\n    const handleSelect = () => {\n      const menuItem = ref.current;\n      if (!disabled && menuItem) {\n        const itemSelectEvent = new CustomEvent(ITEM_SELECT, { bubbles: true, cancelable: true });\n        menuItem.addEventListener(ITEM_SELECT, (event) => onSelect?.(event), { once: true });\n        dispatchDiscreteCustomEvent(menuItem, itemSelectEvent);\n        if (itemSelectEvent.defaultPrevented) {\n          isPointerDownRef.current = false;\n        } else {\n          rootContext.onClose();\n        }\n      }\n    };\n\n    return (\n      <MenuItemImpl\n        {...itemProps}\n        ref={composedRefs}\n        disabled={disabled}\n        onClick={composeEventHandlers(props.onClick, handleSelect)}\n        onPointerDown={(event) => {\n          props.onPointerDown?.(event);\n          isPointerDownRef.current = true;\n        }}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          // Pointer down can move to a different menu item which should activate it on pointer up.\n          // We dispatch a click for selection to allow composition with click based triggers and to\n          // prevent Firefox from getting stuck in text selection mode when the menu closes.\n          if (!isPointerDownRef.current) event.currentTarget?.click();\n        })}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          const isTypingAhead = contentContext.searchRef.current !== '';\n          if (disabled || (isTypingAhead && event.key === ' ')) return;\n          if (SELECTION_KEYS.includes(event.key)) {\n            event.currentTarget.click();\n            /**\n             * We prevent default browser behaviour for selection keys as they should trigger\n             * a selection only:\n             * - prevents space from scrolling the page.\n             * - if keydown causes focus to move, prevents keydown from firing on the new target.\n             */\n            event.preventDefault();\n          }\n        })}\n      />\n    );\n  }\n);\n\nMenuItem.displayName = ITEM_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuItemImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuItemImplProps extends PrimitiveDivProps {\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst MenuItemImpl = React.forwardRef<MenuItemImplElement, MenuItemImplProps>(\n  (props: ScopedProps<MenuItemImplProps>, forwardedRef) => {\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const [isFocused, setIsFocused] = React.useState(false);\n\n    // get the item's `.textContent` as default strategy for typeahead `textValue`\n    const [textContent, setTextContent] = React.useState('');\n    React.useEffect(() => {\n      const menuItem = ref.current;\n      if (menuItem) {\n        setTextContent((menuItem.textContent ?? '').trim());\n      }\n    }, [itemProps.children]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeMenu}\n        disabled={disabled}\n        textValue={textValue ?? textContent}\n      >\n        <RovingFocusGroup.Item asChild {...rovingFocusGroupScope} focusable={!disabled}>\n          <Primitive.div\n            role=\"menuitem\"\n            data-highlighted={isFocused ? '' : undefined}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            {...itemProps}\n            ref={composedRefs}\n            /**\n             * We focus items on `pointerMove` to achieve the following:\n             *\n             * - Mouse over an item (it focuses)\n             * - Leave mouse where it is and use keyboard to focus a different item\n             * - Wiggle mouse without it leaving previously focused item\n             * - Previously focused item should re-focus\n             *\n             * If we used `mouseOver`/`mouseEnter` it would not re-focus when the mouse\n             * wiggles. This is to match native menu implementation.\n             */\n            onPointerMove={composeEventHandlers(\n              props.onPointerMove,\n              whenMouse((event) => {\n                if (disabled) {\n                  contentContext.onItemLeave(event);\n                } else {\n                  contentContext.onItemEnter(event);\n                  if (!event.defaultPrevented) {\n                    const item = event.currentTarget;\n                    item.focus({ preventScroll: true });\n                  }\n                }\n              })\n            )}\n            onPointerLeave={composeEventHandlers(\n              props.onPointerLeave,\n              whenMouse((event) => contentContext.onItemLeave(event))\n            )}\n            onFocus={composeEventHandlers(props.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(props.onBlur, () => setIsFocused(false))}\n          />\n        </RovingFocusGroup.Item>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * MenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'MenuCheckboxItem';\n\ntype MenuCheckboxItemElement = MenuItemElement;\n\ntype CheckedState = boolean | 'indeterminate';\n\ninterface MenuCheckboxItemProps extends MenuItemProps {\n  checked?: CheckedState;\n  // `onCheckedChange` can never be called with `\"indeterminate\"` from the inside\n  onCheckedChange?: (checked: boolean) => void;\n}\n\nconst MenuCheckboxItem = React.forwardRef<MenuCheckboxItemElement, MenuCheckboxItemProps>(\n  (props: ScopedProps<MenuCheckboxItemProps>, forwardedRef) => {\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemcheckbox\"\n          aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n          {...checkboxItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            checkboxItemProps.onSelect,\n            () => onCheckedChange?.(isIndeterminate(checked) ? true : !checked),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'MenuRadioGroup';\n\nconst [RadioGroupProvider, useRadioGroupContext] = createMenuContext<MenuRadioGroupProps>(\n  RADIO_GROUP_NAME,\n  { value: undefined, onValueChange: () => {} }\n);\n\ntype MenuRadioGroupElement = React.ComponentRef<typeof MenuGroup>;\ninterface MenuRadioGroupProps extends MenuGroupProps {\n  value?: string;\n  onValueChange?: (value: string) => void;\n}\n\nconst MenuRadioGroup = React.forwardRef<MenuRadioGroupElement, MenuRadioGroupProps>(\n  (props: ScopedProps<MenuRadioGroupProps>, forwardedRef) => {\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = useCallbackRef(onValueChange);\n    return (\n      <RadioGroupProvider scope={props.__scopeMenu} value={value} onValueChange={handleValueChange}>\n        <MenuGroup {...groupProps} ref={forwardedRef} />\n      </RadioGroupProvider>\n    );\n  }\n);\n\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'MenuRadioItem';\n\ntype MenuRadioItemElement = React.ComponentRef<typeof MenuItem>;\ninterface MenuRadioItemProps extends MenuItemProps {\n  value: string;\n}\n\nconst MenuRadioItem = React.forwardRef<MenuRadioItemElement, MenuRadioItemProps>(\n  (props: ScopedProps<MenuRadioItemProps>, forwardedRef) => {\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemradio\"\n          aria-checked={checked}\n          {...radioItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            radioItemProps.onSelect,\n            () => context.onValueChange?.(value),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'MenuItemIndicator';\n\ntype CheckboxContextValue = { checked: CheckedState };\n\nconst [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext<CheckboxContextValue>(\n  ITEM_INDICATOR_NAME,\n  { checked: false }\n);\n\ntype MenuItemIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface MenuItemIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuItemIndicator = React.forwardRef<MenuItemIndicatorElement, MenuItemIndicatorProps>(\n  (props: ScopedProps<MenuItemIndicatorProps>, forwardedRef) => {\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return (\n      <Presence\n        present={\n          forceMount ||\n          isIndeterminate(indicatorContext.checked) ||\n          indicatorContext.checked === true\n        }\n      >\n        <Primitive.span\n          {...itemIndicatorProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(indicatorContext.checked)}\n        />\n      </Presence>\n    );\n  }\n);\n\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'MenuSeparator';\n\ntype MenuSeparatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuSeparatorProps extends PrimitiveDivProps {}\n\nconst MenuSeparator = React.forwardRef<MenuSeparatorElement, MenuSeparatorProps>(\n  (props: ScopedProps<MenuSeparatorProps>, forwardedRef) => {\n    const { __scopeMenu, ...separatorProps } = props;\n    return (\n      <Primitive.div\n        role=\"separator\"\n        aria-orientation=\"horizontal\"\n        {...separatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'MenuArrow';\n\ntype MenuArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface MenuArrowProps extends PopperArrowProps {}\n\nconst MenuArrow = React.forwardRef<MenuArrowElement, MenuArrowProps>(\n  (props: ScopedProps<MenuArrowProps>, forwardedRef) => {\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'MenuSub';\n\ntype MenuSubContextValue = {\n  contentId: string;\n  triggerId: string;\n  trigger: MenuSubTriggerElement | null;\n  onTriggerChange(trigger: MenuSubTriggerElement | null): void;\n};\n\nconst [MenuSubProvider, useMenuSubContext] = createMenuContext<MenuSubContextValue>(SUB_NAME);\n\ninterface MenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst MenuSub: React.FC<MenuSubProps> = (props: ScopedProps<MenuSubProps>) => {\n  const { __scopeMenu, children, open = false, onOpenChange } = props;\n  const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n  const popperScope = usePopperScope(__scopeMenu);\n  const [trigger, setTrigger] = React.useState<MenuSubTriggerElement | null>(null);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n\n  // Prevent the parent menu from reopening with open submenus.\n  React.useEffect(() => {\n    if (parentMenuContext.open === false) handleOpenChange(false);\n    return () => handleOpenChange(false);\n  }, [parentMenuContext.open, handleOpenChange]);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuSubProvider\n          scope={__scopeMenu}\n          contentId={useId()}\n          triggerId={useId()}\n          trigger={trigger}\n          onTriggerChange={setTrigger}\n        >\n          {children}\n        </MenuSubProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenuSub.displayName = SUB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'MenuSubTrigger';\n\ntype MenuSubTriggerElement = MenuItemImplElement;\ninterface MenuSubTriggerProps extends MenuItemImplProps {}\n\nconst MenuSubTrigger = React.forwardRef<MenuSubTriggerElement, MenuSubTriggerProps>(\n  (props: ScopedProps<MenuSubTriggerProps>, forwardedRef) => {\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = React.useRef<number | null>(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = { __scopeMenu: props.__scopeMenu };\n\n    const clearOpenTimer = React.useCallback(() => {\n      if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n      openTimerRef.current = null;\n    }, []);\n\n    React.useEffect(() => clearOpenTimer, [clearOpenTimer]);\n\n    React.useEffect(() => {\n      const pointerGraceTimer = pointerGraceTimerRef.current;\n      return () => {\n        window.clearTimeout(pointerGraceTimer);\n        onPointerGraceIntentChange(null);\n      };\n    }, [pointerGraceTimerRef, onPointerGraceIntentChange]);\n\n    return (\n      <MenuAnchor asChild {...scope}>\n        <MenuItemImpl\n          id={subContext.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={subContext.contentId}\n          data-state={getOpenState(context.open)}\n          {...props}\n          ref={composeRefs(forwardedRef, subContext.onTriggerChange)}\n          // This is redundant for mouse users but we cannot determine pointer type from\n          // click event and we cannot use pointerup event (see git history for reasons why)\n          onClick={(event) => {\n            props.onClick?.(event);\n            if (props.disabled || event.defaultPrevented) return;\n            /**\n             * We manually focus because iOS Safari doesn't always focus on click (e.g. buttons)\n             * and we rely heavily on `onFocusOutside` for submenus to close when switching\n             * between separate submenus.\n             */\n            event.currentTarget.focus();\n            if (!context.open) context.onOpenChange(true);\n          }}\n          onPointerMove={composeEventHandlers(\n            props.onPointerMove,\n            whenMouse((event) => {\n              contentContext.onItemEnter(event);\n              if (event.defaultPrevented) return;\n              if (!props.disabled && !context.open && !openTimerRef.current) {\n                contentContext.onPointerGraceIntentChange(null);\n                openTimerRef.current = window.setTimeout(() => {\n                  context.onOpenChange(true);\n                  clearOpenTimer();\n                }, 100);\n              }\n            })\n          )}\n          onPointerLeave={composeEventHandlers(\n            props.onPointerLeave,\n            whenMouse((event) => {\n              clearOpenTimer();\n\n              const contentRect = context.content?.getBoundingClientRect();\n              if (contentRect) {\n                // TODO: make sure to update this when we change positioning logic\n                const side = context.content?.dataset.side as Side;\n                const rightSide = side === 'right';\n                const bleed = rightSide ? -5 : +5;\n                const contentNearEdge = contentRect[rightSide ? 'left' : 'right'];\n                const contentFarEdge = contentRect[rightSide ? 'right' : 'left'];\n\n                contentContext.onPointerGraceIntentChange({\n                  area: [\n                    // Apply a bleed on clientX to ensure that our exit point is\n                    // consistently within polygon bounds\n                    { x: event.clientX + bleed, y: event.clientY },\n                    { x: contentNearEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.bottom },\n                    { x: contentNearEdge, y: contentRect.bottom },\n                  ],\n                  side,\n                });\n\n                window.clearTimeout(pointerGraceTimerRef.current);\n                pointerGraceTimerRef.current = window.setTimeout(\n                  () => contentContext.onPointerGraceIntentChange(null),\n                  300\n                );\n              } else {\n                contentContext.onTriggerLeave(event);\n                if (event.defaultPrevented) return;\n\n                // There's 100ms where the user may leave an item before the submenu was opened.\n                contentContext.onPointerGraceIntentChange(null);\n              }\n            })\n          )}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            const isTypingAhead = contentContext.searchRef.current !== '';\n            if (props.disabled || (isTypingAhead && event.key === ' ')) return;\n            if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n              context.onOpenChange(true);\n              // The trigger may hold focus if opened via pointer interaction\n              // so we ensure content is given focus again when switching to keyboard.\n              context.content?.focus();\n              // prevent window from scrolling\n              event.preventDefault();\n            }\n          })}\n        />\n      </MenuAnchor>\n    );\n  }\n);\n\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'MenuSubContent';\n\ntype MenuSubContentElement = MenuContentImplElement;\ninterface MenuSubContentProps\n  extends Omit<\n    MenuContentImplProps,\n    keyof MenuContentImplPrivateProps | 'onCloseAutoFocus' | 'onEntryFocus' | 'side' | 'align'\n  > {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuSubContent = React.forwardRef<MenuSubContentElement, MenuSubContentProps>(\n  (props: ScopedProps<MenuSubContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuSubContentElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            <MenuContentImpl\n              id={subContext.contentId}\n              aria-labelledby={subContext.triggerId}\n              {...subContentProps}\n              ref={composedRefs}\n              align=\"start\"\n              side={rootContext.dir === 'rtl' ? 'left' : 'right'}\n              disableOutsidePointerEvents={false}\n              disableOutsideScroll={false}\n              trapFocus={false}\n              onOpenAutoFocus={(event) => {\n                // when opening a submenu, focus content for keyboard users only\n                if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                event.preventDefault();\n              }}\n              // The menu might close because of focusing another menu item in the parent menu. We\n              // don't want it to refocus the trigger in that case so we handle trigger focus ourselves.\n              onCloseAutoFocus={(event) => event.preventDefault()}\n              onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) => {\n                // We prevent closing when the trigger is focused to avoid triggering a re-open animation\n                // on pointer interaction.\n                if (event.target !== subContext.trigger) context.onOpenChange(false);\n              })}\n              onEscapeKeyDown={composeEventHandlers(props.onEscapeKeyDown, (event) => {\n                rootContext.onClose();\n                // ensure pressing escape in submenu doesn't escape full screen mode\n                event.preventDefault();\n              })}\n              onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                // Submenu key events bubble through portals. We only care about keys in this menu.\n                const isKeyDownInside = event.currentTarget.contains(event.target as HTMLElement);\n                const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                if (isKeyDownInside && isCloseKey) {\n                  context.onOpenChange(false);\n                  // We focus manually because we prevented it in `onCloseAutoFocus`\n                  subContext.trigger?.focus();\n                  // prevent window from scrolling\n                  event.preventDefault();\n                }\n              })}\n            />\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\nMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getOpenState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getCheckedState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus();\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in all the values,\n * the search and the current match, and returns the next match (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through options starting with that character)\n *\n * We also reorder the values by wrapping the array around the current match.\n * This is so we always look forward from the current match, and picking the first\n * match will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current match from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current match still matches.\n */\nfunction getNextMatch(values: string[], search: string, currentMatch?: string) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n  let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n  const excludeCurrentMatch = normalizedSearch.length === 1;\n  if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v) => v !== currentMatch);\n  const nextMatch = wrappedValues.find((value) =>\n    value.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextMatch !== currentMatch ? nextMatch : undefined;\n}\n\ntype Point = { x: number; y: number };\ntype Polygon = Point[];\ntype Side = 'left' | 'right';\ntype GraceIntent = { area: Polygon; side: Side };\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const ii = polygon[i]!;\n    const jj = polygon[j]!;\n    const xi = ii.x;\n    const yi = ii.y;\n    const xj = jj.x;\n    const yj = jj.y;\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n    if (intersect) inside = !inside;\n  }\n\n  return inside;\n}\n\nfunction isPointerInGraceArea(event: React.PointerEvent, area?: Polygon) {\n  if (!area) return false;\n  const cursorPos = { x: event.clientX, y: event.clientY };\n  return isPointInPolygon(cursorPos, area);\n}\n\nfunction whenMouse<E>(handler: React.PointerEventHandler<E>): React.PointerEventHandler<E> {\n  return (event) => (event.pointerType === 'mouse' ? handler(event) : undefined);\n}\n\nconst Root = Menu;\nconst Anchor = MenuAnchor;\nconst Portal = MenuPortal;\nconst Content = MenuContent;\nconst Group = MenuGroup;\nconst Label = MenuLabel;\nconst Item = MenuItem;\nconst CheckboxItem = MenuCheckboxItem;\nconst RadioGroup = MenuRadioGroup;\nconst RadioItem = MenuRadioItem;\nconst ItemIndicator = MenuItemIndicator;\nconst Separator = MenuSeparator;\nconst Arrow = MenuArrow;\nconst Sub = MenuSub;\nconst SubTrigger = MenuSubTrigger;\nconst SubContent = MenuSubContent;\n\nexport {\n  createMenuScope,\n  //\n  Menu,\n  MenuAnchor,\n  MenuPortal,\n  MenuContent,\n  MenuGroup,\n  MenuLabel,\n  MenuItem,\n  MenuCheckboxItem,\n  MenuRadioGroup,\n  MenuRadioItem,\n  MenuItemIndicator,\n  MenuSeparator,\n  MenuArrow,\n  MenuSub,\n  MenuSubTrigger,\n  MenuSubContent,\n  //\n  Root,\n  Anchor,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  MenuProps,\n  MenuAnchorProps,\n  MenuPortalProps,\n  MenuContentProps,\n  MenuGroupProps,\n  MenuLabelProps,\n  MenuItemProps,\n  MenuCheckboxItemProps,\n  MenuRadioGroupProps,\n  MenuRadioItemProps,\n  MenuItemIndicatorProps,\n  MenuSeparatorProps,\n  MenuArrowProps,\n  MenuSubProps,\n  MenuSubTriggerProps,\n  MenuSubContentProps,\n};\n"], "names": [], "mappings": "uLMKG,SAAU,CAAE,AAAF,EAAE,CAAI,AAAJ,CAAI,CAAA,CAAA,AEGG,CFHH,CAAM,CAAA,CIGE,AJHF,CAAA,AAAI,IAAA,CAAA,AAAM,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,AAAK,GAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,WAC7C,CAAE,CAAA,CAAA,CAAA,AAAI,GAAA,AIGG,CJHH,AAAK,CAAA,CAAA,CAAI,CAAA,AGGC,CHHD,CAAA,CAAA,CAAM,AAAN,CAAM,CAAA,AAAG,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,AAAK,QAAA,CAAU,CAAA,iCCmBnD,EAAA,CGTG,AHSH,EAAY,CAAA,CAAA,CAAA,CAAZ,AAAY,CAAZ,AAAY,CAAZ,AAAY,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AGTS,CHST,CAAA,CAAA,UArBxB,CDAR,ACAQ,AHYL,+DGZwE,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,EAEzF,CAAA,ADAD,CAAA,ACAC,CAAA,ADAD,CCAC,ADAD,CGGM,AHHN,AIGM,AHHL,CAAA,ADAD,CAAA,CAAA,ACEG,CAAA,CAAA,0HACA,GAAA,AGGG,CHHH,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CGGQ,EHFf,CAEJ,oMLTA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,ODHA,EAAA,EAAA,CAAA,CAAA,OAGA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,ODNA,EAAA,EAAA,CAAA,CAAA,MAII,EAAc,gCACd,EAAgB,CAAE,QAAS,GAAO,YAAY,CAAK,EACnD,EAAa,mBACb,CAAC,EAAY,EAAe,EAAsB,CAAG,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACtE,CAAC,EAA+B,EAA4B,CAAG,CAAA,EAAA,EAAA,kBAAA,AAAkB,EACnF,EACA,CAAC,EAAsB,EAErB,CAAC,EAAqB,EAAsB,CAAG,EAA8B,GAC7E,EAAmB,EAAA,UAAgB,CACrC,CAAC,EAAO,IACiB,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EAAW,QAAQ,CAAE,CAAE,MAAO,EAAM,uBAAuB,CAAE,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAW,EAAlB,EAAsB,CAAE,CAAE,MAAO,EAAM,uBAAuB,CAAE,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAsB,CAAE,CAA/B,EAAkC,CAAK,CAAE,IAAK,CAAa,EAAG,EAAG,IAG3Q,EAAiB,WAAW,CAAG,EAC/B,IAAI,EAAuB,EAAA,UAAgB,CAAC,CAAC,EAAO,KAClD,GAAM,CACJ,yBAAuB,aACvB,CAAW,MACX,GAAO,CAAK,KACZ,CAAG,CACH,iBAAkB,CAAoB,yBACtC,CAAuB,0BACvB,CAAwB,cACxB,CAAY,2BACZ,GAA4B,CAAK,CACjC,GAAG,EACJ,CAAG,EACE,EAAM,EAAA,MAAY,CAAC,MACnB,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,GAC7C,EAAY,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,GACzB,CAAC,EAAkB,EAAoB,CAAG,CAAA,EAAA,EAAA,oBAAoB,AAApB,EAAqB,CACnE,KAAM,EACN,YAAa,GAA2B,KACxC,SAAU,EACV,OAAQ,CACV,GACM,CAAC,EAAkB,EAAoB,CAAG,EAAA,QAAc,EAAC,GACzD,EAAmB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,GAClC,EAAW,EAAc,GACzB,EAAkB,EAAA,MAAY,CAAC,IAC/B,CAAC,EAAqB,EAAuB,CAAG,EAAA,QAAc,CAAC,GAQrE,OAAO,AAPP,EAAA,SAAe,CAAC,CAOI,IANlB,IAAM,EAAO,EAAI,OAAO,CACxB,GAAI,EAEF,IAFQ,GACR,EAAK,gBAAgB,CAAC,EAAa,GAC5B,IAAM,EAAK,mBAAmB,CAAC,EAAa,EAEvD,EAAG,CAAC,EAAiB,EACE,CAAA,EAAA,EAAA,GAAA,AAAG,EACxB,EACA,CACE,MAAO,EACP,cACA,IAAK,OACL,mBACA,EACA,YAAa,EAAA,WAAiB,CAC5B,AAAC,GAAc,EAAoB,GACnC,CAAC,EAAoB,EAEvB,eAAgB,EAAA,WAAiB,CAAC,IAAM,GAAoB,GAAO,EAAE,EACrE,mBAAoB,EAAA,WAAiB,CACnC,IAAM,EAAuB,AAAC,GAAc,EAAY,GACxD,EAAE,EAEJ,sBAAuB,EAAA,WAAiB,CACtC,IAAM,EAAuB,AAAC,GAAc,EAAY,GACxD,EAAE,EAEJ,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAC3B,EAAA,EADqB,OACZ,CAAC,GAAG,CACb,CACE,SAAU,GAA4C,IAAxB,EAA4B,CAAC,EAAI,EAC/D,mBAAoB,EACpB,GAAG,CAAU,CACb,IAAK,EACL,MAAO,CAAE,QAAS,OAAQ,GAAG,EAAM,KAAK,AAAC,EACzC,YAAa,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,WAAW,CAAE,KACnD,EAAgB,OAAO,EAAG,CAC5B,GACA,QAAS,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,OAAO,CAAE,AAAC,IAC5C,IAAM,EAAkB,CAAC,EAAgB,OAAO,CAChD,GAAI,EAAM,MAAM,GAAK,EAAM,aAAa,EAAI,GAAmB,CAAC,EAAkB,CAChF,IAAM,EAAkB,IAAI,YAAY,EAAa,GAErD,GADA,EAAM,aAAa,CAAC,aAAa,CAAC,GAC9B,CAAC,EAAgB,gBAAgB,CAAE,CACrC,IAAM,EAAQ,IAAW,MAAM,CAAC,AAAC,GAAS,EAAK,SAAS,EAOxD,EAJuB,AAGA,CALJ,EAAM,IAAI,CAAE,AAAD,CAMnB,EAN6B,EAAK,MAAM,EAC/B,EAAM,IAAI,CAAE,AAAD,GAAU,EAAK,EAAE,GAAK,MACD,EAAM,CAAC,MAAM,CAC/D,SAEoC,GAAG,CAAC,AAAC,GAAS,EAAK,GAAG,CAAC,OAAO,EACzC,EAC7B,CACF,CACA,EAAgB,OAAO,EAAG,CAC5B,GACA,OAAQ,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,MAAM,CAAE,IAAM,GAAoB,GACvE,EAEJ,EAEJ,GACI,EAAY,uBACZ,EAAuB,EAAA,UAAgB,CACzC,CAAC,EAAO,KACN,GAAM,yBACJ,CAAuB,WACvB,GAAY,CAAI,QAChB,GAAS,CAAK,WACd,CAAS,UACT,CAAQ,CACR,GAAG,EACJ,CAAG,EACE,EAAS,CAAA,EAAA,EAAA,KAAA,AAAK,IACd,EAAK,GAAa,EAClB,EAAU,EAAsB,EAAW,GAC3C,EAAmB,EAAQ,gBAAgB,GAAK,EAChD,EAAW,EAAc,GACzB,CAAE,oBAAkB,uBAAE,CAAqB,kBAAE,CAAgB,CAAE,CAAG,EAOxE,OAAO,AANP,EAAA,SAAe,CAAC,CAMI,IALlB,GAAI,EAEF,OADA,EADa,EAEN,IAAM,GAEjB,EAAG,CAAC,EAAW,EAAoB,EAAsB,EAClC,CAAA,EAAA,EAAA,GAAA,AAAG,EACxB,EAAW,QAAQ,CACnB,CACE,MAAO,EACP,eACA,SACA,EACA,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAG,AAAH,EACxB,EAAA,EADqB,OACZ,CAAC,IAAI,CACd,CACE,SAAU,EAAmB,EAAI,CAAC,EAClC,mBAAoB,EAAQ,WAAW,CACvC,GAAG,CAAS,CACZ,IAAK,EACL,YAAa,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,WAAW,CAAE,AAAC,IAC/C,EACA,EAAQ,WAAW,CAAC,GADT,EAAM,cAAc,EAEtC,GACA,QAAS,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,OAAO,CAAE,IAAM,EAAQ,WAAW,CAAC,IACvE,UAAW,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,SAAS,CAAG,AAAD,IAC/C,GAAkB,QAAd,EAAM,GAAG,EAAc,EAAM,QAAQ,CAAE,YACzC,EAAQ,cAAc,GAGxB,GAAI,EAAM,MAAM,GAAK,EAAM,aAAa,CAAE,OAC1C,IAAM,EAqCpB,AArCkC,SAqCzB,AAAe,CAAK,CAAE,CAAW,CAAE,CAAG,QAC7C,IAAM,GALsB,EAKK,CALF,AAKnB,CAA2B,CALN,EAKS,CALN,AACpC,AAAI,AAAQ,OAAO,CAIyB,EAJlB,EACX,cAAR,EAAsB,aAAuB,eAAR,EAAuB,YAAc,GAIjF,KAAoB,aAAhB,GAA8B,CAAC,YAAa,aAAa,CAAC,QAAQ,CAAC,EAAA,GAAM,EACzD,KADgE,KAAK,KACrF,GAAgC,CAAC,UAAW,YAAY,CAAC,QAAQ,CAAC,EAAA,EACtE,CAD4E,MACrE,CAD4E,AACrD,CAAC,EAAI,AACrC,EA1CiD,AAwCyC,EAxClC,EAAQ,WAAW,CAAE,EAAQ,GAAG,EAC1E,GAAoB,KAAK,IAArB,EAAwB,CAC1B,GAAI,EAAM,OAAO,EAAI,EAAM,OAAO,EAAI,EAAM,MAAM,EAAI,EAAM,QAAQ,CAAE,OACtE,EAAM,cAAc,GAEpB,IAAI,EADU,AACO,IADI,MAAM,CAAC,AAAC,GAAS,EAAK,SAAS,EAC7B,GAAG,CAAC,AAAC,GAAS,EAAK,GAAG,CAAC,OAAO,EACzD,GAAI,AAAgB,WAAQ,EAAe,OAAO,QAC7C,GAAI,AAAgB,YAA0B,SAAhB,EAAwB,CACrC,SAAhB,GAAwB,EAAe,OAAO,GAClD,IAAM,EAAe,EAAe,OAAO,CAAC,EAAM,aAAa,EAC/D,EAAiB,EAAQ,IAAI,CAAG,AAyClD,SAAS,AAAU,CAAK,CAAE,CAAU,EAClC,OAAO,EAAM,GAAG,CAAC,CAAC,EAAG,IAAU,CAAK,CAAE,AAAD,GAAc,CAAA,CAAK,CAAI,EAAM,MAAM,CAAC,CAC3E,EA3C4D,EAAgB,EAAe,GAAK,EAAe,KAAK,CAAC,EAAe,EACpH,CACA,WAAW,IAAM,EAAW,GAC9B,CACF,GACA,SAA8B,YAApB,OAAO,EAA0B,EAAS,kBAAE,EAAkB,WAAY,AAAoB,OAAK,GAAK,CACpH,EAEJ,EAEJ,GAEF,EAAqB,WAAW,CAAG,EACnC,IAAI,EAA0B,CAC5B,UAAW,OACX,QAAS,OACT,WAAY,OACZ,UAAW,OACX,OAAQ,QACR,KAAM,QACN,SAAU,OACV,IAAK,MACP,EAWA,SAAS,EAAW,CAAU,CAAE,GAAgB,CAAK,EACnD,IAAM,EAA6B,SAAS,aAAa,CACzD,IAAK,IAAM,KAAa,EACtB,GAAI,IAAc,EADgB,EAElC,EAAU,KAAK,CAAC,CAAE,eAAc,GAC5B,SAAS,aAAa,GAAK,GAFe,MAIlD,CCrMA,IAAA,EAAA,EAAA,CAAA,CAAA,OAEA,CDiM+D,CCjM/D,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEI,EAAiB,CAAC,QAAS,IAAI,CAE/B,EAAY,CAAC,UAAW,WAAY,MAAM,CAC1C,EAAkB,CAFJ,YAAa,SAAU,UAEA,EAAU,CAC/C,EAAgB,CAClB,IAAK,IAAI,EAAgB,aAAa,CACtC,IAAK,IAAI,EAAgB,YAAY,AACvC,EACI,EAAiB,CACnB,IAAK,CAAC,YAAY,CAClB,IAAK,CAAC,aAAa,AACrB,EACI,EAAY,OACZ,CAAC,EAAY,EAAe,EAAsB,CAAG,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACtE,CAAC,EAAmB,EAAgB,CAAG,CAAA,EAAA,EAAA,kBAAA,AAAkB,EAAC,EAAW,CACvE,EACA,EAAA,iBAAiB,CACjB,EACD,EACG,EAAiB,CAAA,EAAA,EAAA,iBAAA,AAAiB,IAClC,EAA2B,IAC3B,CAAC,EAAc,GAAe,CAAG,EAAkB,GACnD,CAAC,GAAkB,GAAmB,CAAG,EAAkB,GAC3D,GAAO,AAAC,IACV,GAAM,aAAE,CAAW,MAAE,GAAO,CAAK,CAAE,UAAQ,CAAE,KAAG,cAAE,CAAY,OAAE,GAAQ,CAAI,CAAE,CAAG,EAC3E,EAAc,EAAe,GAC7B,CAAC,EAAS,EAAW,CAAG,EAAA,QAAc,CAAC,MACvC,EAAqB,EAAA,MAAY,CAAC,IAClC,EAAmB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,GAClC,EAAY,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,GAe/B,OAdA,AAcO,EAdP,SAAe,CAAC,CAcI,IAblB,IAAM,EAAgB,KACpB,EAAmB,OAAO,EAAG,EAC7B,SAAS,gBAAgB,CAAC,cAAe,EAAe,CAAE,SAAS,EAAM,MAAM,CAAK,GACpF,SAAS,gBAAgB,CAAC,cAAe,EAAe,CAAE,SAAS,EAAM,MAAM,CAAK,EACtF,EACM,EAAgB,IAAM,EAAmB,OAAO,EAAG,EAEzD,OADA,SAAS,gBAAgB,CAAC,UAAW,EAAe,CAAE,SAAS,CAAK,GAC7D,KACL,SAAS,mBAAmB,CAAC,UAAW,EAAe,CAAE,SAAS,CAAK,GACvE,SAAS,mBAAmB,CAAC,cAAe,EAAe,CAAE,SAAS,CAAK,GAC3E,SAAS,mBAAmB,CAAC,cAAe,EAAe,CAAE,SAAS,CAAK,EAC7E,CACF,EAAG,EAAE,EACkB,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,IAAoB,CAAE,CAAE,GAAG,CAAW,CAAE,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAC9F,EACA,CACE,CAHsF,KAG/E,EACP,OACA,aAAc,UACd,EACA,gBAAiB,EACjB,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAC3B,GACA,CACE,AAHmB,MAGZ,EACP,QAAS,EAAA,WAAiB,CAAC,IAAM,GAAiB,GAAQ,CAAC,EAAiB,qBAC5E,EACA,IAAK,QACL,WACA,CACF,EAEJ,EACA,EACJ,EACA,GAAK,WAAW,CAAG,EAEnB,IAAI,GAAa,EAAA,UAAgB,CAC/B,CAAC,EAAO,KACN,GAAM,aAAE,CAAW,CAAE,GAAG,EAAa,CAAG,EAClC,EAAc,EAAe,GACnC,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,IAA6B,CAAE,CAAE,GAAG,CAAW,CAAE,GAAG,CAAW,CAAE,IAAK,CAAa,EACzG,GAEF,GAAW,WAAW,CARJ,EAQO,WACzB,IAAI,GAAc,aACd,CAAC,GAAgB,GAAiB,CAAG,EAAkB,GAAa,CACtE,WAAY,KAAK,CACnB,GACI,GAAa,AAAC,IAChB,GAAM,aAAE,CAAW,YAAE,CAAU,UAAE,CAAQ,WAAE,CAAS,CAAE,CAAG,EACnD,EAAU,GAAe,GAAa,GAC5C,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,GAAgB,CAAvB,AAAyB,MAAO,aAAa,EAAY,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,MAAe,CAAE,CAAE,QAAS,GAAc,EAAQ,IAAI,CAAE,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,IAAsB,CAAE,CAAE,SAAS,YAAM,WAAW,CAAS,EAAG,EAAG,EACxP,EACA,GAAW,WAAW,CAAG,GACzB,IAAI,GAAe,cACf,CAAC,GAAqB,GAAsB,CAAG,EAAkB,IACjE,GAAc,EAAA,UAAgB,CAChC,CAAC,EAAO,KACN,IAAM,EAAgB,GAAiB,GAAc,EAAM,WAAW,EAChE,CAAE,aAAa,EAAc,UAAU,CAAE,GAAG,EAAc,CAAG,EAC7D,EAAU,GAAe,GAAc,EAAM,WAAW,EACxD,EAAc,GAAmB,GAAc,EAAM,WAAW,EACtE,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAG,AAAH,EAAI,EAAW,EAAlB,MAA0B,CAAE,CAAE,MAAO,EAAM,WAAW,CAAE,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,MAAe,CAAE,CAAE,QAAS,GAAc,EAAQ,IAAI,CAAE,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAAC,EAAW,EAAlB,EAAsB,CAAE,CAAE,MAAO,EAAM,WAAW,CAAE,SAAU,EAAY,KAAK,CAAmB,CAAA,CAAhB,CAAgB,EAAA,GAAG,AAAH,EAAI,GAAsB,CAAE,CAA/B,EAAkC,CAAY,CAAE,IAAK,CAAa,GAAqB,CAAA,CAAhB,CAAgB,EAAA,GAAA,AAAG,EAAC,GAAyB,CAAE,CAAlC,EAAqC,CAAY,CAAE,IAAK,CAAa,EAAG,EAAG,EAAG,EACrb,GAEE,GAAuB,EAAA,UAAgB,CACzC,CAAC,EAAO,KACN,IAAM,EAAU,GAAe,GAAc,EAAM,WAAW,EACxD,EAAM,EAAA,MAAY,CAAC,MACnB,EAAe,CAAA,EAAA,EAAA,eAAe,AAAf,EAAgB,EAAc,GAKnD,OAJA,AAIO,EAJP,SAAe,CAAC,CAII,IAHlB,IAAM,EAAU,EAAI,OAAO,CAC3B,GAAI,EAAS,MAAO,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,EACjC,EAAG,EAAE,EACkB,CAAA,EAAA,EAAA,GAAA,AAAG,EACxB,GACA,CACE,GAAG,CAAK,CACR,IAAK,EACL,UAAW,EAAQ,IAAI,CACvB,4BAA6B,EAAQ,IAAI,CACzC,sBAAsB,EACtB,eAAgB,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAClC,EAAM,cAAc,CACpB,AAAC,GAAU,EAAM,cAAc,GAC/B,CAAE,0BAA0B,CAAM,GAEpC,UAAW,IAAM,EAAQ,YAAY,EAAC,EACxC,EAEJ,GAEE,GAA0B,EAAA,UAAgB,CAAC,CAAC,EAAO,KACrD,IAAM,EAAU,GAAe,GAAc,EAAM,WAAW,EAC9D,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EACxB,GACA,CACE,AAHgB,GAGb,CAAK,CACR,IAAK,EACL,WAAW,EACX,6BAA6B,EAC7B,sBAAsB,EACtB,UAAW,IAAM,EAAQ,YAAY,EAAC,EACxC,EAEJ,GACI,GAAO,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,0BAClB,GAAkB,EAAA,UAAgB,CACpC,CAAC,EAAO,KACN,GAAM,aACJ,CAAW,MACX,GAAO,CAAK,WACZ,CAAS,iBACT,CAAe,CACf,kBAAgB,6BAChB,CAA2B,cAC3B,CAAY,iBACZ,CAAe,sBACf,CAAoB,CACpB,gBAAc,mBACd,CAAiB,WACjB,CAAS,sBACT,CAAoB,CACpB,GAAG,EACJ,CAAG,EACE,EAAU,GAAe,GAAc,GACvC,EAAc,GAAmB,GAAc,GAC/C,EAAc,EAAe,GAC7B,EAAwB,EAAyB,GACjD,EAAW,EAAc,GACzB,CAAC,EAAe,EAAiB,CAAG,EAAA,QAAc,CAAC,MACnD,EAAa,EAAA,MAAY,CAAC,MAC1B,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,EAAY,EAAQ,eAAe,EAChF,EAAW,EAAA,MAAY,CAAC,GACxB,EAAY,EAAA,MAAY,CAAC,IACzB,EAAuB,EAAA,MAAY,CAAC,GACpC,EAAwB,EAAA,MAAY,CAAC,MACrC,EAAgB,EAAA,MAAY,CAAC,SAC7B,EAAkB,EAAA,MAAY,CAAC,GAC/B,EAAoB,EAAuB,EAAA,YAAY,CAAG,EAAA,QAAc,CAmB9E,EAAA,SAAe,CAAC,IACP,IAAM,OAAO,YAAY,CAAC,EAAS,OAAO,EAChD,EAAE,EACL,CAAA,EAAA,EAAA,cAAA,AAAc,IACd,IAAM,EAA2B,EAAA,WAAiB,CAAC,AAAC,GAC1B,AACjB,EAD+B,OAAO,GAAK,EAAsB,OAAO,EAAE,MACvD,AA+kBhC,SAAS,AAAqB,CAAK,CAAE,CAAI,QACvC,CAAI,CAAC,GAhBP,AAkBS,GAFI,MAhBJ,AAAiB,CAAK,AAgBX,CAhBa,CAAO,EACtC,GAiBwB,AAjBlB,GAAE,CAAC,GAAE,CAAC,CAAE,CAAG,EACb,GAAS,EACb,IAAK,IAAI,EAAI,EAAG,EAAI,EAAQ,MAAM,CAAG,EAAG,EAAI,EAAQ,MAAM,CAAE,EAAI,IAAK,CACnE,IAAM,EAAK,CAAO,CAAC,EAAE,CACf,EAAK,CAAO,CAAC,EAAE,CACf,EAAK,EAAG,CAAC,CACT,EAAK,EAAG,CAAC,CACT,EAAK,EAAG,CAAC,CACT,EAAK,EAAG,CAAC,AAEX,CADc,EAAK,GAAM,EAAK,GAAK,EAAI,CAAC,EAAK,CAAA,CAAE,EAAK,EAAD,AAAK,CAAA,CAAE,CAAK,GAAD,AAAM,CAAA,CAAE,CAAI,IAC/D,EAAS,CAAC,CAAA,CAC3B,CACA,OAAO,CACT,EAGoB,CAAE,EAAG,EAAM,OAAO,CAAE,EAAG,EAAM,OAAO,AAAC,EACpB,EACrC,EAnlBqD,EAAO,EAAsB,OAAO,EAAE,MACpF,EAAE,EACL,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EACxB,GACA,CAFkB,AAGhB,MAAO,YACP,EACA,YAAa,EAAA,WAAiB,CAC5B,AAAC,IACK,EAAyB,IAAQ,EAAM,cAAc,EAC3D,EACA,CAAC,EAAyB,EAE5B,YAAa,EAAA,WAAiB,CAC5B,AAAC,IACK,EAAyB,KAC7B,EAAW,CAD0B,MACnB,EAAE,QACpB,EAAiB,MACnB,EACA,CAAC,EAAyB,EAE5B,eAAgB,EAAA,WAAiB,CAC/B,AAAC,IACK,EAAyB,IAAQ,EAAM,cAAc,EAC3D,EACA,CAAC,EAAyB,uBAE5B,EACA,2BAA4B,EAAA,WAAiB,CAAC,AAAC,IAC7C,EAAsB,OAAO,CAAG,CAClC,EAAG,EAAE,EACL,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAmB,CAvD2C,CAuDrE,EAvDI,EAAuB,CAAE,GAAI,GAAM,eAAgB,EAAK,EAAI,KAAK,CAuDzC,CAA2B,EAAxB,OAAkD,CAAhB,AAAgB,EAAA,EAAA,GAAG,AAAH,EACtG,EAAA,EADmG,CAAzB,OAChE,CACV,CACE,SAAS,EACT,QAAS,EACT,iBAAkB,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAiB,AAAC,IACvD,EAAM,cAAc,GACpB,EAAW,OAAO,EAAE,MAAM,CAAE,eAAe,CAAK,EAClD,GACA,mBAAoB,EACpB,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAG,AAAH,EACxB,EAAA,EADqB,cACL,CAChB,CACE,SAAS,8BACT,kBACA,EACA,uBACA,mCACA,YACA,EACA,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAC3B,ADpDP,ECqDO,CACE,CAHmB,OAGV,GACT,GAAG,CAAqB,CACxB,IAAK,EAAY,GAAG,CACpB,YAAa,gBACb,EACA,iBAAkB,EAClB,yBAA0B,EAC1B,aAAc,CAAA,EAAA,EAAA,oBAAoB,AAApB,EAAqB,EAAc,AAAC,IAC5C,AAAC,EAAY,kBAAkB,CAAC,OAAO,EAAE,EAAM,cAAc,EACnE,GACA,2BAA2B,EAC3B,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAC3B,EAAA,EADqB,KACE,CACvB,CACE,KAAM,OACN,mBAAoB,WACpB,aAAc,GAAa,EAAQ,IAAI,EACvC,0BAA2B,GAC3B,IAAK,EAAY,GAAG,CACpB,GAAG,CAAW,CACd,GAAG,CAAY,CACf,IAAK,EACL,MAAO,CAAE,QAAS,OAAQ,GAAG,EAAa,KAAK,AAAC,EAChD,UAAW,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAa,SAAS,CAAE,AAAC,IAEvD,IAAM,EADS,AACS,EADH,MAAM,CACI,OAAO,CAAC,+BAAiC,EAAM,aAAa,CACrF,EAAgB,EAAM,OAAO,EAAI,EAAM,MAAM,EAAI,EAAM,OAAO,CAC9D,EAAsC,IAArB,EAAM,GAAG,CAAC,MAAM,CACnC,IACE,AAAc,UAAR,GADS,AACN,EAAY,EAAM,cAAc,GACzC,CAAC,GAAiB,GA3GhB,AA2GgC,CA3G/B,IAC7B,IAAM,EAAS,EAAU,OAAO,CAAG,EAC7B,EAAQ,IAAW,MAAM,CAAC,AAAC,GAAS,CAAC,EAAK,QAAQ,EAClD,EAAc,SAAS,aAAa,CACpC,EAAe,EAAM,IAAI,CAAE,AAAD,GAAU,EAAK,GAAG,CAAC,OAAO,GAAK,IAAc,UAEvE,EAqkBZ,AArkBwB,SAqkBf,AAAa,CAAM,CAAE,CAAM,CAAE,AArkBD,CAqkBa,MAHxB,EAKxB,IAAM,EAAmB,AADN,EAJe,AAIR,MAAM,CAAG,GAAK,MAAM,IAAI,CAAC,GAAQ,KAAK,CAAE,AAAD,GAAU,IAAS,CAAM,CAAC,EAAE,EACvD,CAAM,CAAC,EAAE,CAAG,EAC5C,EAAoB,EAAe,EAAO,OAAO,CAAC,GAAgB,CAAC,EACrE,KAAkC,KAAK,GAAG,CAAC,EAAmB,AAA9C,GANb,EAAM,GAAG,CAAC,CAAC,EAAG,IAAU,CAAK,CAAC,CAAC,EAAa,CAAA,CAAK,CAAI,AAM9B,EANoC,MAAM,CAAC,EAQrE,CADoD,IAA5B,EAAiB,MAAM,GAC1B,EAAgB,EAAc,MAAM,CAAC,AAAC,GAAM,IAAM,EAAA,EAC3E,IAAM,EAAY,EAAc,IAAI,CAClC,AAAC,GAAU,EAAM,WAAW,GAAG,UAAU,CAAC,EAAiB,WAAW,KAExE,OAAO,IAAc,EAAe,EAAY,KAAK,CACvD,EAjlBqB,EAAM,GAAG,CAAC,AAAC,GAAS,EAAK,SAAS,EACV,EAAQ,GACzC,EAAU,EAAM,IAAI,CAAC,AAAC,GAAS,EAAK,SAAS,GAAK,IAAY,IAAI,SACxE,AAAC,SAAS,EAAa,CAAK,EAC1B,EAAU,OAAO,CAAG,EACpB,OAAO,YAAY,CAAC,EAAS,OAAO,EACtB,KAAV,IAAc,EAAS,OAAO,CAAG,OAAO,UAAU,CAAC,IAAM,EAAa,IAAK,IAAA,CACjF,CAAC,CAAE,GACC,GACF,MADW,KACA,IAAM,EAAQ,KAAK,IAElC,EA2FoF,EAAM,GAAG,GAEvE,IAAM,EAAU,EAAW,OAAO,CAClC,GAAI,EAAM,MAAM,GAAK,GACjB,CAAC,EAAgB,QAAQ,CAAC,EAAM,GAAG,EADT,CACY,MAC1C,EAAM,cAAc,GAEpB,IAAM,EAAiB,AADT,IAAW,MAAM,CAAC,AAAC,GAAS,CAAC,EAAK,QAAQ,EAC3B,GAAG,CAAC,AAAC,GAAS,EAAK,GAAG,CAAC,OAAO,EACvD,EAAU,QAAQ,CAAC,EAAM,GAAG,GAAG,EAAe,OAAO,GA6cnF,AA5c0B,SA4cjB,AAAW,CAAU,EAC5B,IAAM,EAA6B,SAAS,aAAa,CACzD,IAAK,IAAM,KAAa,EACtB,GAAI,IAAc,EADgB,EAElC,EAAU,KAAK,GACX,SAAS,aAAa,GAAK,GAFe,MAIlD,EAndqC,EACb,GACA,OAAQ,CAAA,EAAA,EAAA,AA+c+B,oBA/c/B,AAAoB,EAAC,EAAM,MAAM,CAAG,AAAD,IACpC,EAAM,aAAa,CAAC,QAAQ,CAAC,EAAM,MAAM,GAAG,CAC/C,OAAO,YAAY,CAAC,EAAS,OAAO,EACpC,EAAU,OAAO,CAAG,GAExB,GACA,cAAe,CAAA,EAAA,EAAA,oBAAA,AAAoB,EACjC,EAAM,aAAa,CACnB,GAAU,AAAC,IACT,IAAM,EAAS,EAAM,MAAM,CACrB,EAAqB,EAAgB,OAAO,GAAK,EAAM,OAAO,CAChE,EAAM,aAAa,CAAC,QAAQ,CAAC,IAAW,IAE1C,EAAc,OAAO,CADN,EAAM,AACG,IAFsC,GAClC,CAAG,EAAgB,OAAO,CAAG,QAAU,OAEnE,EAAgB,OAAO,CAAG,EAAM,OAAO,CAE3C,GAEJ,EAEJ,EAEJ,EAEJ,EACA,EACJ,EAEJ,GAEF,GAAY,WAAW,CAAG,GAE1B,IAAI,GAAY,EAAA,UAAgB,CAC9B,CAAC,EAAO,KACN,GAAM,aAAE,CAAW,CAAE,GAAG,EAAY,CAAG,EACvC,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,OAAgB,CAAC,GAAG,CAAE,CAAE,KAAM,QAAS,GAAG,CAAU,CAAE,IAAK,CAAa,EAC9F,GAEF,GAAU,WAAW,CAPJ,EAOO,UAExB,IAAI,GAAY,EAAA,UAAgB,CAC9B,CAAC,EAAO,KACN,GAAM,aAAE,CAAW,CAAE,GAAG,EAAY,CAAG,EACvC,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,OAAgB,CAAC,GAAG,CAAE,CAAE,GAAG,CAAU,CAAE,IAAK,CAAa,EAC/E,GAEF,GAAU,WAAW,CAPJ,EAOO,UACxB,IAAI,GAAY,WACZ,GAAc,kBACd,GAAW,EAAA,UAAgB,CAC7B,CAAC,EAAO,KACN,GAAM,UAAE,GAAW,CAAK,UAAE,CAAQ,CAAE,GAAG,EAAW,CAAG,EAC/C,EAAM,EAAA,MAAY,CAAC,MACnB,EAAc,GAAmB,GAAW,EAAM,WAAW,EAC7D,EAAiB,GAAsB,GAAW,EAAM,WAAW,EACnE,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,GAC7C,EAAmB,EAAA,MAAY,EAAC,GActC,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EACxB,GACA,CAFkB,AAGhB,GAAG,CAAS,CACZ,IAAK,EACL,WACA,QAAS,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,OAAO,CAnB1B,CAmB4B,IAlB/C,IAAM,EAAW,EAAI,OAAO,CAC5B,GAAI,CAAC,GAAY,EAAU,CACzB,IAAM,EAAkB,IAAI,YAAY,GAAa,CAAE,QAAS,GAAM,YAAY,CAAK,GACvF,EAAS,gBAAgB,CAAC,GAAa,AAAC,GAAU,IAAW,GAAQ,CAAE,MAAM,CAAK,GAClF,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAU,GAClC,EAAgB,gBAAgB,CAClC,CADoC,CACnB,OAAO,EAAG,EAE3B,EAAY,OAAO,EAEvB,CACF,GAQI,cAAgB,AAAD,IACb,EAAM,aAAa,GAAG,GACtB,EAAiB,OAAO,EAAG,CAC7B,EACA,YAAa,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,WAAW,CAAE,AAAC,IAChD,AAAC,EAAiB,OAAO,EAAE,EAAM,aAAa,EAAE,OACtD,GACA,UAAW,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,SAAS,CAAE,AAAC,IAChD,IAAM,EAAgB,AAAqC,OAAtB,SAAS,CAAC,OAAO,CAClD,GAAY,GAA+B,KAAK,CAAnB,EAAM,GAAG,EACtC,EAAe,QAAQ,CAAC,EAAM,GAAG,GAAG,CACtC,EAAM,aAAa,CAAC,KAAK,GACzB,EAAM,cAAc,GAExB,EACF,EAEJ,GAEF,GAAS,WAAW,CAAG,GACvB,IAAI,GAAe,EAAA,UAAgB,CACjC,CAAC,EAAO,KACN,GAAM,CAAE,aAAW,UAAE,GAAW,CAAK,WAAE,CAAS,CAAE,GAAG,EAAW,CAAG,EAC7D,EAAiB,GAAsB,GAAW,GAClD,EAAwB,EAAyB,GACjD,EAAM,EAAA,MAAY,CAAC,MACnB,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,GAC7C,CAAC,EAAW,EAAa,CAAG,EAAA,QAAc,CAAC,IAC3C,CAAC,EAAa,EAAe,CAAG,EAAA,QAAc,CAAC,IAOrD,OANA,AAMO,EANP,SAAe,CAAC,CAMI,IALlB,IAAM,EAAW,EAAI,OAAO,AACxB,IACF,EAAe,CAAC,EAAS,CADb,UACwB,EAAI,EAAA,CAAE,CAAE,IAAI,GAEpD,EAAG,CAAC,EAAU,QAAQ,CAAC,EACA,CAAA,EAAA,EAAA,GAAA,AAAG,EACxB,EAAW,QAAQ,CACnB,CACE,MAAO,WACP,EACA,UAAW,GAAa,EACxB,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,ADpN3B,ECoNkD,CAAE,CAAhC,QAAyC,EAAM,GAAG,CAAqB,CAAE,UAAW,CAAC,EAAU,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EACjJ,EAAA,EAD2I,OAClI,CAAC,GAAG,CACb,CACE,KAAM,WACN,mBAAoB,EAAY,GAAK,KAAK,EAC1C,gBAAiB,GAAY,KAAK,EAClC,gBAAiB,EAAW,GAAK,KAAK,EACtC,GAAG,CAAS,CACZ,IAAK,EACL,cAAe,CAAA,EAAA,EAAA,oBAAA,AAAoB,EACjC,EAAM,aAAa,CACnB,GAAU,AAAC,IACL,EACF,EAAe,MADH,KACc,CAAC,IAE3B,EAAe,WAAW,CAAC,GACtB,EAAM,gBAAgB,EACZ,AADc,AAE3B,EADmB,aAAa,CAC3B,KAAK,CAAC,CAAE,cAAe,EAAK,GAGvC,IAEF,eAAgB,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAClC,EAAM,cAAc,CACpB,GAAU,AAAC,GAAU,EAAe,WAAW,CAAC,KAElD,QAAS,CAAA,EAAA,EAAA,oBAAoB,AAApB,EAAqB,EAAM,OAAO,CAAE,IAAM,GAAa,IAChE,OAAQ,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,MAAM,CAAE,IAAM,GAAa,GAChE,EACA,EACJ,EAEJ,GAGE,GAAmB,EAAA,UAAgB,CACrC,CAAC,EAAO,KACN,GAAM,SAAE,EAAU,EAAK,iBAAE,CAAe,CAAE,GAAG,EAAmB,CAAG,EACnE,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAG,AAAH,EAAI,GAAuB,CAA9B,AAAgC,MAAO,EAAM,WAAW,CAAE,UAAS,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAClH,GACA,CAF4G,AAG1G,KAAM,mBACN,eAAgB,GAAgB,GAAW,QAAU,EACrD,GAAG,CAAiB,CACpB,IAAK,EACL,aAAc,GAAgB,GAC9B,SAAU,CAAA,EAAA,EAAA,oBAAoB,AAApB,EACR,EAAkB,QAAQ,CAC1B,IAAM,MAAkB,GAAgB,IAAkB,CAAC,GAC3D,CAAE,EADiD,wBACvB,CAAM,EAEtC,EACA,EACJ,GAEF,GAAiB,WAAW,CArBH,EAqBM,iBAC/B,IAAI,GAAmB,iBACnB,CAAC,GAAoB,GAAqB,CAAG,EAC/C,GACA,CAAE,MAAO,KAAK,EAAG,cAAe,KAChC,CAAE,GAEA,GAAiB,EAAA,UAAgB,CACnC,CAAC,EAAO,KACN,GAAM,OAAE,CAAK,eAAE,CAAa,CAAE,GAAG,EAAY,CAAG,EAC1C,EAAoB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,GACzC,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAAC,GAAoB,CAAE,AAA7B,MAAoC,EAAM,WAAW,CAAE,QAAO,cAAe,EAAmB,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,GAAW,CAAE,AAApB,GAAuB,CAAU,CAAE,IAAK,CAAa,EAAG,EACrM,GAEF,GAAe,WAAW,CAAG,GAC7B,IAAI,GAAkB,gBAClB,GAAgB,EAAA,UAAgB,CAClC,CAAC,EAAO,KACN,GAAM,OAAE,CAAK,CAAE,GAAG,EAAgB,CAAG,EAC/B,EAAU,GAAqB,GAAiB,EAAM,WAAW,EACjE,EAAU,IAAU,EAAQ,KAAK,CACvC,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAAC,GAAuB,CAAE,AAAhC,MAAuC,EAAM,WAAW,SAAE,EAAS,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAClH,GACA,CAF4G,AAG1G,KAAM,gBACN,eAAgB,EAChB,GAAG,CAAc,CACjB,IAAK,EACL,aAAc,GAAgB,GAC9B,SAAU,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAC5B,EAAe,QAAQ,CACvB,IAAM,EAAQ,aAAa,GAAG,GAC9B,CAAE,0BAA0B,CAAM,EAEtC,EACA,EACJ,EAEF,IAAc,WAAW,CAAG,GAC5B,IAAI,GAAsB,oBACtB,CAAC,GAAuB,GAAwB,CAAG,EACrD,GACA,CAAE,QAAS,EAAM,GAEf,GAAoB,EAAA,UAAgB,CACtC,CAAC,EAAO,KACN,GAAM,aAAE,CAAW,YAAE,CAAU,CAAE,GAAG,EAAoB,CAAG,EACrD,EAAmB,GAAwB,GAAqB,GACtE,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EACxB,EAAA,EADkB,MACV,CACR,CACE,QAAS,GAAc,GAAgB,EAAiB,OAAO,IAAkC,IAA7B,EAAiB,OAAO,CAC5F,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAC3B,EAAA,EADqB,OACZ,CAAC,IAAI,CACd,CACE,GAAG,CAAkB,CACrB,IAAK,EACL,aAAc,GAAgB,EAAiB,OAAO,CACxD,EAEJ,EAEJ,GAEF,GAAkB,WAAW,CAAG,GAEhC,IAAI,GAAgB,EAAA,UAAgB,CAClC,CAAC,EAAO,KACN,GAAM,CAAE,aAAW,CAAE,GAAG,EAAgB,CAAG,EAC3C,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAG,AAAH,EACrB,EAAA,EADkB,OACT,CAAC,GAAG,CACb,CACE,KAAM,YACN,mBAAoB,aACpB,GAAG,CAAc,CACjB,IAAK,CACP,EAEJ,GAEF,GAAc,WAAW,CAfJ,EAeO,cAE5B,IAAI,GAAY,EAAA,UAAgB,CAC9B,CAAC,EAAO,KACN,GAAM,aAAE,CAAW,CAAE,GAAG,EAAY,CAAG,EACjC,EAAc,EAAe,GACnC,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAG,AAAH,EAAI,EAAA,EAAP,GAA4B,CAAE,CAAE,GAAG,CAAW,CAAE,GAAG,CAAU,CAAE,IAAK,CAAa,EACvG,GAEF,GAAU,WAAW,CARJ,EAQO,UACxB,GACI,CAAC,AADD,GACkB,GAAkB,CAAG,EAD5B,WAoCX,GAAmB,EAnCsC,eAoCzD,GAAiB,EAAA,UAAgB,CACnC,CAAC,EAAO,KACN,IAAM,EAAU,GAAe,GAAkB,EAAM,WAAW,EAC5D,EAAc,GAAmB,GAAkB,EAAM,WAAW,EACpE,EAAa,GAAkB,GAAkB,EAAM,WAAW,EAClE,EAAiB,GAAsB,GAAkB,EAAM,WAAW,EAC1E,EAAe,EAAA,MAAY,CAAC,MAC5B,sBAAE,CAAoB,4BAAE,CAA0B,CAAE,CAAG,EACvD,EAAQ,CAAE,YAAa,EAAM,WAAW,AAAC,EACzC,EAAiB,EAAA,WAAiB,CAAC,KACnC,EAAa,OAAO,EAAE,OAAO,YAAY,CAAC,EAAa,OAAO,EAClE,EAAa,OAAO,CAAG,IACzB,EAAG,EAAE,EASL,OARA,AAQO,EARP,SAAe,CAAC,CAQI,GARE,EAAgB,CAAC,EAAe,EACtD,EAAA,SAAe,CAAC,KACd,IAAM,EAAoB,EAAqB,OAAO,CACtD,MAAO,KACL,OAAO,YAAY,CAAC,GACpB,EAA2B,KAC7B,CACF,EAAG,CAAC,EAAsB,EAA2B,EAC9B,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,GAAY,CAAE,SAAS,EAAM,GAAG,CAAK,CAAE,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAC7F,GACA,CAFuF,AAGrF,GAAI,EAAW,SAAS,CACxB,gBAAiB,OACjB,gBAAiB,EAAQ,IAAI,CAC7B,gBAAiB,EAAW,SAAS,CACrC,aAAc,GAAa,EAAQ,IAAI,EACvC,GAAG,CAAK,CACR,IAAK,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,EAAc,EAAW,eAAe,EACzD,QAAS,AAAC,IACR,EAAM,OAAO,GAAG,GACZ,EAAM,QAAQ,EAAI,EAAM,gBAAgB,EAAE,CAC9C,EAAM,aAAa,CAAC,KAAK,GACrB,AAAC,EAAQ,IAAI,EAAE,EAAQ,YAAY,EAAC,GAC1C,EACA,cAAe,CAAA,EAAA,EAAA,oBAAoB,AAApB,EACb,EAAM,aAAa,CACnB,GAAU,AAAC,IACT,EAAe,WAAW,CAAC,IACvB,EAAM,gBAAgB,EAAE,CACvB,EAAM,QAAQ,EAAK,EAAD,AAAS,IAAI,EAAK,EAAD,AAAc,OAAO,EAAE,CAC7D,EAAe,0BAA0B,CAAC,MAC1C,EAAa,OAAO,CAAG,OAAO,UAAU,CAAC,KACvC,EAAQ,YAAY,EAAC,GACrB,GACF,EAAG,MAEP,IAEF,eAAgB,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAClC,EAAM,cAAc,CACpB,GAAU,AAAC,IACT,IACA,IAAM,EAAc,EAAQ,OAAO,EAAE,wBACrC,GAAI,EAAa,CACf,IAAM,EAAO,EAAQ,OAAO,EAAE,QAAQ,KAChC,EAAqB,UAAT,EAEZ,EAAkB,CAAW,CAAC,EAAY,OAAS,QAAQ,CAC3D,EAAiB,CAAW,CAAC,EAAY,QAAU,OAAO,CAChE,EAAe,0BAA0B,CAAC,CACxC,KAAM,CAGJ,CAAE,EAAG,EAAM,OAAO,EAPR,CAOW,CAPC,CAAC,GAAI,EAOC,EAAG,EAAM,OAAO,AAAC,EAC7C,CAAE,EAAG,EAAiB,EAAG,EAAY,GAAG,AAAC,EACzC,CAAE,EAAG,EAAgB,EAAG,EAAY,GAAG,AAAC,EACxC,CAAE,EAAG,EAAgB,EAAG,EAAY,MAAO,AAAD,EAC1C,CAAE,EAAG,EAAiB,EAAG,EAAY,MAAO,AAAD,EAC5C,MACD,CACF,GACA,OAAO,YAAY,CAAC,EAAqB,OAAO,EAChD,EAAqB,OAAO,CAAG,OAAO,UAAU,CAC9C,IAAM,EAAe,0BAA0B,CAAC,MAChD,IAEJ,KAAO,CAEL,GADA,EAAe,cAAc,CAAC,GAC1B,EAAM,gBAAgB,CAAE,OAC5B,EAAe,0BAA0B,CAAC,KAC5C,CACF,IAEF,UAAW,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,SAAS,CAAE,AAAC,IAChD,IAAM,EAAqD,KAArC,EAAe,SAAS,CAAC,OAAO,CAClD,EAAM,QAAQ,EAAI,GAA+B,KAAK,CAAnB,EAAM,GAAG,EAC5C,CAAa,CAAC,EAAY,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAM,GAAG,GAAG,CACtD,EAAQ,YAAY,EAAC,GACrB,EAAQ,OAAO,EAAE,QACjB,EAAM,cAAc,GAExB,EACF,EACA,EACJ,GAEF,GAAe,WAAW,CAAG,GAC7B,IAAI,GAAmB,iBACnB,GAAiB,EAAA,UAAgB,CACnC,CAAC,EAAO,KACN,IAAM,EAAgB,GAAiB,GAAc,EAAM,WAAW,EAChE,YAAE,EAAa,EAAc,UAAU,CAAE,GAAG,EAAiB,CAAG,EAChE,EAAU,GAAe,GAAc,EAAM,WAAW,EACxD,EAAc,GAAmB,GAAc,EAAM,WAAW,EAChE,EAAa,GAAkB,GAAkB,EAAM,WAAW,EAClE,EAAM,EAAA,MAAY,CAAC,MACnB,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,GACnD,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAW,EAAlB,MAA0B,CAAE,CAAE,MAAO,EAAM,WAAW,CAAE,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,MAAe,CAAE,CAAE,QAAS,GAAc,EAAQ,IAAI,CAAE,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAW,EAAlB,EAAsB,CAAE,CAAE,MAAO,EAAM,WAAW,CAAE,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EACjQ,GACA,CAF2P,AAGzP,GAAI,EAAW,SAAS,CACxB,kBAAmB,EAAW,SAAS,CACvC,GAAG,CAAe,CAClB,IAAK,EACL,MAAO,QACP,KAA0B,QAApB,EAAY,GAAG,CAAa,OAAS,QAC3C,6BAA6B,EAC7B,sBAAsB,EACtB,WAAW,EACX,gBAAiB,AAAC,IACZ,EAAY,kBAAkB,CAAC,OAAO,EAAE,EAAI,OAAO,EAAE,QACzD,EAAM,cAAc,EACtB,EACA,iBAAkB,AAAC,GAAU,EAAM,cAAc,GACjD,eAAgB,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,cAAc,CAAE,AAAC,IACtD,EAAM,MAAM,GAAK,EAAW,OAAO,EAAE,EAAQ,YAAY,EAAC,EAChE,GACA,gBAAiB,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,eAAe,CAAE,AAAC,IAC5D,EAAY,OAAO,GACnB,EAAM,cAAc,EACtB,GACA,UAAW,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,SAAS,CAAE,AAAC,IAChD,IAAM,EAAkB,EAAM,aAAa,CAAC,QAAQ,CAAC,EAAM,MAAM,EAC3D,EAAa,CAAc,CAAC,EAAY,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAM,GAAG,EACjE,GAAmB,IACrB,EAAQ,MADyB,MACb,EAAC,GACrB,EAAW,OAAO,EAAE,QACpB,EAAM,cAAc,GAExB,EACF,EACA,EAAG,EAAG,EACV,GAGF,SAAS,GAAa,CAAI,EACxB,OAAO,EAAO,OAAS,QACzB,CACA,SAAS,GAAgB,CAAO,EAC9B,MAAmB,kBAAZ,CACT,CACA,SAAS,GAAgB,CAAO,EAC9B,OAAO,GAAgB,GAAW,gBAAkB,EAAU,UAAY,WAC5E,CA4CA,SAAS,GAAU,CAAO,EACxB,OAAO,AAAC,GAAgC,UAAtB,EAAM,WAAW,CAAe,EAAQ,GAAS,KAAK,CAC1E,CAvDA,GAAe,WAAW,CAAG,GC/uB7B,IAAI,GAAqB,eACrB,CAAC,GAA2B,GAAwB,CAAG,CAAA,EAAA,EAAA,kBAAA,AAAkB,EAC3E,GACA,CAAC,EAAgB,EAEf,GAAe,IACf,CAAC,GAAsB,GAAuB,CAAG,GAA0B,IAC3E,GAAe,AAAC,IAClB,GAAM,qBACJ,CAAmB,UACnB,CAAQ,KACR,CAAG,CACH,KAAM,CAAQ,CACd,aAAW,cACX,CAAY,OACZ,GAAQ,CAAI,CACb,CAAG,EACE,EAAY,GAAa,GACzB,EAAa,EAAA,MAAY,CAAC,MAC1B,CAAC,EAAM,EAAQ,CAAG,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,CAC3C,KAAM,EACN,YAAa,IAAe,EAC5B,SAAU,EACV,OAAQ,EACV,GACA,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EACxB,GACA,CACE,AAHgB,MAGT,EACP,UAAW,CAAA,EAAA,EAAA,KAAA,AAAK,IAChB,aACA,UAAW,CAAA,EAAA,EAAA,KAAA,AAAK,SAChB,EACA,aAAc,EACd,aAAc,EAAA,WAAiB,CAAC,IAAM,EAAQ,AAAC,GAAa,CAAC,GAAW,CAAC,EAAQ,QACjF,EACA,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,ADmwBxB,GCnwB4C,CAAE,AAA7B,GAAgC,CAAS,MAAE,EAAM,aAAc,MAAS,QAAK,WAAO,CAAS,EACtH,EAEJ,EACA,GAAa,WAAW,CAAG,GAC3B,IAAI,GAAe,sBACf,GAAsB,EAAA,UAAgB,CACxC,CAAC,EAAO,KACN,GAAM,qBAAE,CAAmB,CAAE,YAAW,CAAK,CAAE,GAAG,EAAc,CAAG,EAC7D,EAAU,GAAuB,GAAc,GAC/C,EAAY,GAAa,GAC/B,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAAC,ADyvBjB,GCzvBuC,CAAE,AAA/B,SAAwC,EAAM,GAAG,CAAS,CAAE,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAC3G,EAAA,EADqG,OAC5F,CAAC,MAAM,CAChB,CACE,KAAM,SACN,GAAI,EAAQ,SAAS,CACrB,gBAAiB,OACjB,gBAAiB,EAAQ,IAAI,CAC7B,gBAAiB,EAAQ,IAAI,CAAG,EAAQ,SAAS,CAAG,KAAK,EACzD,aAAc,EAAQ,IAAI,CAAG,OAAS,SACtC,gBAAiB,EAAW,GAAK,KAAK,WACtC,EACA,GAAG,CAAY,CACf,IAAK,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,EAAc,EAAQ,UAAU,EACjD,cAAe,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,aAAa,CAAG,AAAD,IACnD,CAAC,GAA6B,IAAjB,EAAM,MAAM,GAA4B,IAAlB,EAAM,CAAmB,MAAZ,GAClD,EAAQ,YAAY,GAChB,AAAC,EAAQ,IAAI,EAAE,EAAM,cAAc,GAE3C,GACA,UAAW,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,SAAS,CAAE,AAAC,KAC5C,IACA,CAAC,KADS,GACA,IAAI,CAAC,QAAQ,CAAC,EAAM,GAAG,GAAG,EAAQ,YAAY,GAC1C,cAAd,EAAM,GAAG,EAAkB,EAAQ,YAAY,EAAC,GAChD,CAAC,QAAS,IAAK,YAAY,CAAC,QAAQ,CAAC,EAAM,GAAG,GAAG,EAAM,cAAc,GAC3E,EACF,EACA,EACJ,GAEF,GAAoB,WAAW,CAAG,GAElC,IAAI,GAAqB,AAAC,IACxB,GAAM,qBAAE,CAAmB,CAAE,GAAG,EAAa,CAAG,EAC1C,EAAY,GAAa,GAC/B,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,ADwtBhB,GCxtBsC,CAA7B,AAA+B,GAAG,CAAS,CAAE,GAAG,CAAW,AAAC,EAClF,CACA,IAAmB,WAAW,CANZ,EAMe,mBACjC,IAAI,GAAe,sBACf,GAAsB,EAAA,UAAgB,CACxC,CAAC,EAAO,KACN,GAAM,qBAAE,CAAmB,CAAE,GAAG,EAAc,CAAG,EAC3C,EAAU,GAAuB,GAAc,GAC/C,EAAY,GAAa,GACzB,EAA0B,EAAA,MAAY,EAAC,GAC7C,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EACxB,AD8sBS,GC7sBT,CAFkB,AAGhB,GAAI,EAAQ,SAAS,CACrB,kBAAmB,EAAQ,SAAS,CACpC,GAAG,CAAS,CACZ,GAAG,CAAY,CACf,IAAK,EACL,iBAAkB,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAM,gBAAgB,CAAE,AAAC,IAC1D,AAAC,EAAwB,OAAO,EAAE,EAAQ,UAAU,CAAC,OAAO,EAAE,QAClE,EAAwB,OAAO,EAAG,EAClC,EAAM,cAAc,EACtB,GACA,kBAAmB,CAAA,EAAA,EAAA,oBAAoB,AAApB,EAAqB,EAAM,iBAAiB,CAAE,AAAC,IAChE,IAAM,EAAgB,EAAM,MAAM,CAAC,aAAa,CAC1C,EAAyC,IAAzB,EAAc,MAAM,EAAU,CAA0B,MAAZ,OAAO,CACnE,EAAwC,IAAzB,EAAc,MAAM,EAAU,GAC/C,CAAC,EAAQ,KAAK,EAAI,CAAA,IAAc,EAAwB,OAAO,CAAG,EAAA,CACxE,GACA,MAAO,CACL,GAAG,EAAM,KAAK,CAGZ,iDAAkD,uCAClD,gDAAiD,sCACjD,iDAAkD,uCAClD,sCAAuC,mCACvC,uCAAwC,mCAE5C,CACF,EAEJ,GAEF,GAAoB,WAAW,CAAG,GAEV,AAOxB,EAPwB,UAAgB,CACtC,CAAC,EAAO,KACN,GAAM,qBAAE,CAAmB,CAAE,GAAG,EAAY,CAAG,EACzC,EAAY,GAAa,GAC/B,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,ADwqBnB,GCxqBwC,CAA5B,AAA8B,GAAG,CAAS,CAAE,GAAG,CAAU,CAAE,IAAK,CAAa,EACnG,GAEgB,WAAW,CARZ,EAQe,kBAEhC,IAAI,GAAoB,EAAA,UAAgB,CACtC,CAAC,EAAO,KACN,GAAM,qBAAE,CAAmB,CAAE,GAAG,EAAY,CAAG,EACzC,EAAY,GAAa,GAC/B,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EDgqBlB,AChqBmB,GAAqB,CAA5B,AAA8B,GAAG,CAAS,CAAE,GAAG,CAAU,CAAE,IAAK,CAAa,EACnG,GAEF,GAAkB,WAAW,CARZ,EAQe,kBAEhC,IAAI,GAAmB,EAAA,UAAgB,CACrC,CAAC,EAAO,KACN,GAAM,qBAAE,CAAmB,CAAE,GAAG,EAAW,CAAG,EACxC,EAAY,GAAa,GAC/B,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EDwpBlB,ACxpBmB,GAAoB,CAA3B,AAA6B,GAAG,CAAS,CAAE,GAAG,CAAS,CAAE,IAAK,CAAa,EACjG,GAEF,GAAiB,WAAW,CARZ,EAQe,iBAO/B,AAL+B,EAAA,UAAgB,CAAC,CAAC,EAAO,KACtD,GAAM,qBAAE,CAAmB,CAAE,GAAG,EAAmB,CAAG,EAChD,EAAY,GAAa,GAC/B,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EDipBT,ACjpBU,GAA4B,CAAnC,AAAqC,GAAG,CAAS,CAAE,GAAG,CAAiB,CAAE,IAAK,CAAa,EACjH,GACyB,WAAW,CANX,EAMc,yBAEV,AAK7B,EAL6B,UAAgB,CAAC,CAAC,EAAO,KACpD,GAAM,qBAAE,CAAmB,CAAE,GAAG,EAAiB,CAAG,EAC9C,EAAY,GAAa,GAC/B,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,ED2oBX,AC3oBY,GAA0B,CAAjC,AAAmC,GAAG,CAAS,CAAE,GAAG,CAAe,CAAE,IAAK,CAAa,EAC7G,GACuB,WAAW,CANX,EAMc,uBAET,AAK5B,EAL4B,UAAgB,CAAC,CAAC,EAAO,KACnD,GAAM,qBAAE,CAAmB,CAAE,GAAG,EAAgB,CAAG,EAC7C,EAAY,GAAa,GAC/B,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAAC,ADqoBb,GCroBsC,CAAE,AAAlC,GAAqC,CAAS,CAAE,GAAG,CAAc,CAAE,IAAK,CAAa,EAC3G,GACsB,WAAW,CANX,EAMc,sBAEJ,AAKhC,EALgC,UAAgB,CAAC,CAAC,EAAO,KACvD,GAAM,qBAAE,CAAmB,CAAE,GAAG,EAAoB,CAAG,EACjD,EAAY,GAAa,GAC/B,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,AD+nBT,GC/nBsC,CAApC,AAAsC,GAAG,CAAS,CAAE,GAAG,CAAkB,CAAE,IAAK,CAAa,EACnH,GAC0B,WAAW,CANhB,EAMmB,0BAExC,IAAI,GAAwB,EAAA,UAAgB,CAAC,CAAC,EAAO,KACnD,GAAM,qBAAE,CAAmB,CAAE,GAAG,EAAgB,CAAG,EAC7C,EAAY,GAAa,GAC/B,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,ADynBb,GCznBsC,CAAE,AAAlC,GAAqC,CAAS,CAAE,GAAG,CAAc,CAAE,IAAK,CAAa,EAC3G,GACA,GAAsB,WAAW,CANZ,EAMe,sBAEZ,AAOxB,EAPwB,UAAgB,CACtC,CAAC,EAAO,KACN,GAAM,qBAAE,CAAmB,CAAE,GAAG,EAAY,CAAG,EACzC,EAAY,GAAa,GAC/B,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAAC,ADknBlB,GClnBuC,CAAE,AAA9B,GAAiC,CAAS,CAAE,GAAG,CAAU,CAAE,IAAK,CAAa,EACnG,GAEgB,WAAW,CARZ,EAQe,kBAaH,AAK7B,EAL6B,UAAgB,CAAC,CAAC,EAAO,KACpD,GAAM,qBAAE,CAAmB,CAAE,GAAG,EAAiB,CAAG,EAC9C,EAAY,GAAa,GAC/B,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,ADimBZ,GCjmBsC,CAAjC,AAAmC,GAAG,CAAS,CAAE,GAAG,CAAe,CAAE,IAAK,CAAa,EAC7G,GACuB,WAAW,CANX,EAMc,uBAER,AAuB7B,EAvB6B,UAAgB,CAAC,CAAC,EAAO,KACpD,GAAM,qBAAE,CAAmB,CAAE,GAAG,EAAiB,CAAG,EAC9C,EAAY,GAAa,GAC/B,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EACxB,AD0lBa,GCzlBb,CACE,AAHgB,GAGb,CAAS,CACZ,GAAG,CAAe,CAClB,IAAK,EACL,MAAO,CACL,GAAG,EAAM,KAAK,CAGZ,iDAAkD,uCAClD,gDAAiD,sCACjD,iDAAkD,uCAClD,sCAAuC,mCACvC,uCAAwC,mCAE5C,CACF,EAEJ,GACuB,WAAW,CAxBX,EAwBc,uBCxPrC,EAAA,CAAA,CAAA,kFIAG,CAAA,EAAA,EAAA,OAAA,AHYY,EAAiB,CAAA,AGZhB,AFYK,ACZQ,CAAA,ADYR,AEZL,AHYgB,QAbK,AAaK,UAbM,CAAA,GAAM,CAAA,CAAA,CAAA,CAAA,CAAA,AAAM,EAAA,CAAA,AAAI,CAAA,CAAA,ACAb,CAAA,ADAa,EAAM,CCAF,ADAE,CAAA,ACAF,ADAK,CCAA,ADAA,CAAA,ACAA,CAAA,ADAA,CAAA,ACAA,CAAA,ADAM,CCAN,ADAM,CCAN,ADAM,EAAK,QAAA,CAAU,CAAC,CAAA,EDG/F,IAAA,GAAA,EAAA,CAAA,CAAA,OAEA,SAAS,GAAa,CACpB,GAAG,EACqD,EACxD,MAAO,CAAA,EAAA,EAAA,GAAA,EDkPG,AClPF,GAAA,CAA2B,YAAU,gBAAiB,GAAG,CAAK,EACxE,CAUA,SAAS,GAAoB,CAC3B,GAAG,EACwD,EAC3D,MACE,CAAA,EAAA,EAAA,GAAA,EDoOU,ACpOT,GAAA,CACC,YAAU,wBACT,GAAG,CAAK,EAGf,CAEA,SAAS,GAAoB,WAC3B,CAAS,CACT,aAAa,CAAC,CACd,GAAG,EACwD,EAC3D,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,ADwNS,GCxNT,UACC,CAAA,EAAA,EAAA,GAAA,EAAC,ADwNQ,GCxNR,CACC,YAAU,wBACV,WAAY,EACZ,UAAW,CAAA,EAAA,GAAA,EAAA,AAAE,EACX,yjBACA,GAED,GAAG,CAAK,IAIjB,CAUA,SAAS,GAAiB,WACxB,CAAS,OACT,CAAK,SACL,EAAU,SAAS,CACnB,GAAG,EAIJ,EACC,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,AD4LO,GC5LP,CACC,YAAU,qBACV,aAAY,EACZ,eAAc,EACd,UAAW,CAAA,EAAA,GAAA,EAAA,AAAE,EACX,8mBACA,GAED,GAAG,CAAK,EAGf,CA+DA,SAAS,GAAkB,WACzB,CAAS,CACT,OAAK,CACL,GAAG,EAGJ,EACC,MACE,CAAA,EAAA,EAAA,GAAA,EDyGS,ACzGR,GAAA,CACC,YAAU,sBACV,aAAY,EACZ,UAAW,CAAA,EAAA,GAAA,EAAA,AAAE,EACX,oDACA,GAED,GAAG,CAAK,EAGf,CAEA,SAAS,GAAsB,WAC7B,CAAS,CACT,GAAG,EAC0D,EAC7D,MACE,CAAA,EAAA,EAAA,GAAA,ED8Fa,AC9FZ,GAAA,CACC,YAAU,0BACV,UAAW,CAAA,EAAA,GAAA,EAAA,AAAE,EAAC,4BAA6B,GAC1C,GAAG,CAAK,EAGf", "ignoreList": [0, 1, 2, 4, 5, 6, 7, 8, 9, 10]}