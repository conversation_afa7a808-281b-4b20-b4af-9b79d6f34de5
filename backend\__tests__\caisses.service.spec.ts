import 'reflect-metadata';
import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { CaissesService } from '../src/caisses/caisses.service';
import { PaymentsService } from '../src/payments/payments.service';
import { SessionsService } from '../src/sessions/sessions.service';
import { Model, Types } from 'mongoose';
import { Caisse } from '../src/caisses/schemas/caisse.schema';
import { ExitOrder } from '../src/caisses/schemas/exit-order.schema';
import { Payment } from '../src/payments/schemas/payment.schema';
import { SessionMember } from '../src/sessions/schemas/session-member.schema';
import { Reunion } from '../src/reunions/schemas/reunion.schema';
import { Session } from '../src/sessions/schemas/session.schema';
import { jest } from '@jest/globals';

const mockModel = () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  findById: jest.fn(),
  findByIdAndUpdate: jest.fn(),
  deleteMany: jest.fn(),
  create: jest.fn(),
  lean: jest.fn(),
  sort: jest.fn(),
});

const paymentsServiceMock = () => ({
  create: jest.fn(),
});

const sessionsServiceMock = {
  updateNextMeet: jest.fn(),
  findOne: jest.fn(),
};

describe('CaissesService', () => {
  let service: CaissesService;
  let caisseModel: any;
  let exitOrderModel: any;
  let paymentModel: any;
  let sessionModel: any;
  let sessionMemberModel: any;
  let reunionModel: any;
  let paymentsService: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CaissesService,
        { provide: PaymentsService, useFactory: paymentsServiceMock },
        { provide: SessionsService, useValue: sessionsServiceMock },
        { provide: getModelToken(Caisse.name), useFactory: mockModel },
        { provide: getModelToken(ExitOrder.name), useFactory: mockModel },
        { provide: getModelToken(Payment.name), useFactory: mockModel },
        { provide: getModelToken(Session.name), useFactory: mockModel },
        { provide: getModelToken(SessionMember.name), useFactory: mockModel },
        { provide: getModelToken(Reunion.name), useFactory: mockModel },
      ],
    }).compile();

    service = module.get<CaissesService>(CaissesService);
    paymentsService = module.get(PaymentsService);
    caisseModel = module.get(getModelToken(Caisse.name));
    exitOrderModel = module.get(getModelToken(ExitOrder.name));
    paymentModel = module.get(getModelToken(Payment.name));
    sessionModel = module.get(getModelToken(Session.name));
    sessionMemberModel = module.get(getModelToken(SessionMember.name));
    reunionModel = module.get(getModelToken(Reunion.name));
  });

  it('myDebrief refuse un user non assigné', async () => {
    const caisseId = new Types.ObjectId();
    const userId = new Types.ObjectId().toString();
    caisseModel.findById.mockResolvedValue({ _id: caisseId, cashierId: new Types.ObjectId() });
    await expect(service.myDebrief(caisseId.toString(), userId, {} as any)).rejects.toThrow();
  });
});