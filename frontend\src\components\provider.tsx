"use client";

import { SessionProvider } from "next-auth/react";
import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { type PropsWithChildren, useState } from "react";
import { getQueryClient } from "@/lib/query-client";

function Providers({ children }: PropsWithChildren) {
	// NOTE: Avoid useState when initializing the query client if you don't
	// have a suspense boundary between this and the code that may
	// suspend because <PERSON><PERSON> will throw away the client on the initial
	// render if it suspends and there is no boundary
	const [queryClient] = useState(() => getQueryClient());

	return (
		<QueryClientProvider client={queryClient}>
			<SessionProvider>
				{children}
				{/* React Query DevTools - only shows in development */}
				<ReactQueryDevtools
					initialIsOpen={false}
					buttonPosition="bottom-right"
				/>
			</SessionProvider>
		</QueryClientProvider>
	);
}

export default Providers;
