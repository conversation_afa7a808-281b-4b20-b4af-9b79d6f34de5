import { Modu<PERSON> } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { MockJwtAuthGuard, MockRolesGuard } from './utils/mock-guards';
import { JwtAuthGuard } from '../src/auth/jwt-auth.guard';
import { RolesGuard } from '../src/common/guards/role.guard';

@Module({
  providers: [
    Reflector,
    // Override guards used by @UseGuards(JwtAuthGuard, RolesGuard)
    { provide: JwtAuthGuard, useClass: MockJwtAuthGuard },
    { provide: RolesGuard, useClass: MockRolesGuard },
  ],
  exports: [JwtAuthGuard, RolesGuard],
})
export class AppTestingModule {}