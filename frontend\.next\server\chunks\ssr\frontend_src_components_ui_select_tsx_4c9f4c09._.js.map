{"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-select@2.2._87908ea416406e1c58c240362be0f00b/node_modules/@radix-ui/react-select/dist/index.mjs", "turbopack:///[project]/frontend/src/components/ui/select.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+number@1.1.1/node_modules/@radix-ui/number/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-use-previou_913befb2c5ba91b732d33e96587c3761/node_modules/@radix-ui/react-use-previous/dist/index.mjs", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/chevron-up.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/chevron-down.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-select@2.2._87908ea416406e1c58c240362be0f00b/node_modules/@radix-ui/react-select/src/select.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/@radix-ui+react-use-previou_913befb2c5ba91b732d33e96587c3761/node_modules/@radix-ui/react-use-previous/src/use-previous.tsx"], "sourcesContent": ["\"use client\";\n\n// src/select.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { clamp } from \"@radix-ui/number\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createCollection } from \"@radix-ui/react-collection\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { DismissableLayer } from \"@radix-ui/react-dismissable-layer\";\nimport { useFocusGuards } from \"@radix-ui/react-focus-guards\";\nimport { FocusScope } from \"@radix-ui/react-focus-scope\";\nimport { useId } from \"@radix-ui/react-id\";\nimport * as PopperPrimitive from \"@radix-ui/react-popper\";\nimport { createPopperScope } from \"@radix-ui/react-popper\";\nimport { Portal as PortalPrimitive } from \"@radix-ui/react-portal\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { usePrevious } from \"@radix-ui/react-use-previous\";\nimport { VISUALLY_HIDDEN_STYLES } from \"@radix-ui/react-visually-hidden\";\nimport { hideOthers } from \"aria-hidden\";\nimport { RemoveScroll } from \"react-remove-scroll\";\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar OPEN_KEYS = [\" \", \"Enter\", \"ArrowUp\", \"ArrowDown\"];\nvar SELECTION_KEYS = [\" \", \"Enter\"];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = createCollection(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope\n]);\nvar usePopperScope = createPopperScope();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState(null);\n  const [valueNode, setValueNode] = React.useState(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SELECT_NAME\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange,\n    caller: SELECT_NAME\n  });\n  const triggerPointerDownPosRef = React.useRef(null);\n  const isFormControl = trigger ? form || !!trigger.closest(\"form\") : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(/* @__PURE__ */ new Set());\n  const nativeSelectKey = Array.from(nativeOptionsSet).map((option) => option.props.value).join(\";\");\n  return /* @__PURE__ */ jsx(PopperPrimitive.Root, { ...popperScope, children: /* @__PURE__ */ jsxs(\n    SelectProvider,\n    {\n      required,\n      scope: __scopeSelect,\n      trigger,\n      onTriggerChange: setTrigger,\n      valueNode,\n      onValueNodeChange: setValueNode,\n      valueNodeHasChildren,\n      onValueNodeHasChildrenChange: setValueNodeHasChildren,\n      contentId: useId(),\n      value,\n      onValueChange: setValue,\n      open,\n      onOpenChange: setOpen,\n      dir: direction,\n      triggerPointerDownPosRef,\n      disabled,\n      children: [\n        /* @__PURE__ */ jsx(Collection.Provider, { scope: __scopeSelect, children: /* @__PURE__ */ jsx(\n          SelectNativeOptionsProvider,\n          {\n            scope: props.__scopeSelect,\n            onNativeOptionAdd: React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, []),\n            onNativeOptionRemove: React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, []),\n            children\n          }\n        ) }),\n        isFormControl ? /* @__PURE__ */ jsxs(\n          SelectBubbleInput,\n          {\n            \"aria-hidden\": true,\n            required,\n            tabIndex: -1,\n            name,\n            autoComplete,\n            value,\n            onChange: (event) => setValue(event.target.value),\n            disabled,\n            form,\n            children: [\n              value === void 0 ? /* @__PURE__ */ jsx(\"option\", { value: \"\" }) : null,\n              Array.from(nativeOptionsSet)\n            ]\n          },\n          nativeSelectKey\n        ) : null\n      ]\n    }\n  ) });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = React.useRef(\"touch\");\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== void 0) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n    const handleOpen = (pointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        resetTypeahead();\n      }\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY)\n        };\n      }\n    };\n    return /* @__PURE__ */ jsx(PopperPrimitive.Anchor, { asChild: true, ...popperScope, children: /* @__PURE__ */ jsx(\n      Primitive.button,\n      {\n        type: \"button\",\n        role: \"combobox\",\n        \"aria-controls\": context.contentId,\n        \"aria-expanded\": context.open,\n        \"aria-required\": context.required,\n        \"aria-autocomplete\": \"none\",\n        dir: context.dir,\n        \"data-state\": context.open ? \"open\" : \"closed\",\n        disabled: isDisabled,\n        \"data-disabled\": isDisabled ? \"\" : void 0,\n        \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n        ...triggerProps,\n        ref: composedRefs,\n        onClick: composeEventHandlers(triggerProps.onClick, (event) => {\n          event.currentTarget.focus();\n          if (pointerTypeRef.current !== \"mouse\") {\n            handleOpen(event);\n          }\n        }),\n        onPointerDown: composeEventHandlers(triggerProps.onPointerDown, (event) => {\n          pointerTypeRef.current = event.pointerType;\n          const target = event.target;\n          if (target.hasPointerCapture(event.pointerId)) {\n            target.releasePointerCapture(event.pointerId);\n          }\n          if (event.button === 0 && event.ctrlKey === false && event.pointerType === \"mouse\") {\n            handleOpen(event);\n            event.preventDefault();\n          }\n        }),\n        onKeyDown: composeEventHandlers(triggerProps.onKeyDown, (event) => {\n          const isTypingAhead = searchRef.current !== \"\";\n          const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n          if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n          if (isTypingAhead && event.key === \" \") return;\n          if (OPEN_KEYS.includes(event.key)) {\n            handleOpen();\n            event.preventDefault();\n          }\n        })\n      }\n    ) });\n  }\n);\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n    return /* @__PURE__ */ jsx(\n      Primitive.span,\n      {\n        ...valueProps,\n        ref: composedRefs,\n        style: { pointerEvents: \"none\" },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ jsx(Fragment, { children: placeholder }) : children\n      }\n    );\n  }\n);\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.span, { \"aria-hidden\": true, ...iconProps, ref: forwardedRef, children: children || \"\\u25BC\" });\n  }\n);\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props) => {\n  return /* @__PURE__ */ jsx(PortalPrimitive, { asChild: true, ...props });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState();\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n    if (!context.open) {\n      const frag = fragment;\n      return frag ? ReactDOM.createPortal(\n        /* @__PURE__ */ jsx(SelectContentProvider, { scope: props.__scopeSelect, children: /* @__PURE__ */ jsx(Collection.Slot, { scope: props.__scopeSelect, children: /* @__PURE__ */ jsx(\"div\", { children: props.children }) }) }),\n        frag\n      ) : null;\n    }\n    return /* @__PURE__ */ jsx(SelectContentImpl, { ...props, ref: forwardedRef });\n  }\n);\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar Slot = createSlot(\"SelectContent.RemoveScroll\");\nvar SelectContentImpl = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = \"item-aligned\",\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState(null);\n    const [viewport, setViewport] = React.useState(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState(null);\n    const [selectedItemText, setSelectedItemText] = React.useState(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n    useFocusGuards();\n    const focusFirst = React.useCallback(\n      (candidates) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: \"nearest\" });\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n        const handlePointerMove = (event) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n          };\n        };\n        const handlePointerUp = (event) => {\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            if (!content.contains(event.target)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener(\"pointermove\", handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener(\"pointermove\", handlePointerMove);\n          document.addEventListener(\"pointerup\", handlePointerUp, { capture: true, once: true });\n        }\n        return () => {\n          document.removeEventListener(\"pointermove\", handlePointerMove);\n          document.removeEventListener(\"pointerup\", handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener(\"blur\", close);\n      window.addEventListener(\"resize\", close);\n      return () => {\n        window.removeEventListener(\"blur\", close);\n        window.removeEventListener(\"resize\", close);\n      };\n    }, [onOpenChange]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        setTimeout(() => nextItem.ref.current.focus());\n      }\n    });\n    const itemRefCallback = React.useCallback(\n      (node, value, disabled) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node, value, disabled) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions\n    } : {};\n    return /* @__PURE__ */ jsx(\n      SelectContentProvider,\n      {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ jsx(RemoveScroll, { as: Slot, allowPinchZoom: true, children: /* @__PURE__ */ jsx(\n          FocusScope,\n          {\n            asChild: true,\n            trapped: context.open,\n            onMountAutoFocus: (event) => {\n              event.preventDefault();\n            },\n            onUnmountAutoFocus: composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            }),\n            children: /* @__PURE__ */ jsx(\n              DismissableLayer,\n              {\n                asChild: true,\n                disableOutsidePointerEvents: true,\n                onEscapeKeyDown,\n                onPointerDownOutside,\n                onFocusOutside: (event) => event.preventDefault(),\n                onDismiss: () => context.onOpenChange(false),\n                children: /* @__PURE__ */ jsx(\n                  SelectPosition,\n                  {\n                    role: \"listbox\",\n                    id: context.contentId,\n                    \"data-state\": context.open ? \"open\" : \"closed\",\n                    dir: context.dir,\n                    onContextMenu: (event) => event.preventDefault(),\n                    ...contentProps,\n                    ...popperContentProps,\n                    onPlaced: () => setIsPositioned(true),\n                    ref: composedRefs,\n                    style: {\n                      // flex layout so we can place the scroll buttons properly\n                      display: \"flex\",\n                      flexDirection: \"column\",\n                      // reset the outline by default as the content MAY get focused\n                      outline: \"none\",\n                      ...contentProps.style\n                    },\n                    onKeyDown: composeEventHandlers(contentProps.onKeyDown, (event) => {\n                      const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                      if (event.key === \"Tab\") event.preventDefault();\n                      if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                      if ([\"ArrowUp\", \"ArrowDown\", \"Home\", \"End\"].includes(event.key)) {\n                        const items = getItems().filter((item) => !item.disabled);\n                        let candidateNodes = items.map((item) => item.ref.current);\n                        if ([\"ArrowUp\", \"End\"].includes(event.key)) {\n                          candidateNodes = candidateNodes.slice().reverse();\n                        }\n                        if ([\"ArrowUp\", \"ArrowDown\"].includes(event.key)) {\n                          const currentElement = event.target;\n                          const currentIndex = candidateNodes.indexOf(currentElement);\n                          candidateNodes = candidateNodes.slice(currentIndex + 1);\n                        }\n                        setTimeout(() => focusFirst(candidateNodes));\n                        event.preventDefault();\n                      }\n                    })\n                  }\n                )\n              }\n            )\n          }\n        ) })\n      }\n    );\n  }\n);\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = React.forwardRef((props, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState(null);\n  const [content, setContent] = React.useState(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n      if (context.dir !== \"rtl\") {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth)\n        ]);\n        contentWrapper.style.minWidth = minContentWidth + \"px\";\n        contentWrapper.style.left = clampedLeft + \"px\";\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth)\n        ]);\n        contentWrapper.style.minWidth = minContentWidth + \"px\";\n        contentWrapper.style.right = clampedRight + \"px\";\n      }\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n        contentWrapper.style.bottom = \"0px\";\n        const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n          (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + \"px\";\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n        contentWrapper.style.top = \"0px\";\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n          (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + \"px\";\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + \"px\";\n      contentWrapper.style.maxHeight = availableHeight + \"px\";\n      onPlaced?.();\n      requestAnimationFrame(() => shouldExpandOnScrollRef.current = true);\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced\n  ]);\n  useLayoutEffect(() => position(), [position]);\n  const [contentZIndex, setContentZIndex] = React.useState();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n  const handleScrollButtonChange = React.useCallback(\n    (node) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n  return /* @__PURE__ */ jsx(\n    SelectViewportProvider,\n    {\n      scope: __scopeSelect,\n      contentWrapper,\n      shouldExpandOnScrollRef,\n      onScrollButtonChange: handleScrollButtonChange,\n      children: /* @__PURE__ */ jsx(\n        \"div\",\n        {\n          ref: setContentWrapper,\n          style: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"fixed\",\n            zIndex: contentZIndex\n          },\n          children: /* @__PURE__ */ jsx(\n            Primitive.div,\n            {\n              ...popperProps,\n              ref: composedRefs,\n              style: {\n                // When we get the height of the content, it includes borders. If we were to set\n                // the height without having `boxSizing: 'border-box'` it would be too big.\n                boxSizing: \"border-box\",\n                // We need to ensure the content doesn't get taller than the wrapper\n                maxHeight: \"100%\",\n                ...popperProps.style\n              }\n            }\n          )\n        }\n      )\n    }\n  );\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = React.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = \"start\",\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  return /* @__PURE__ */ jsx(\n    PopperPrimitive.Content,\n    {\n      ...popperScope,\n      ...popperProps,\n      ref: forwardedRef,\n      align,\n      collisionPadding,\n      style: {\n        // Ensure border-box for floating-ui calculations\n        boxSizing: \"border-box\",\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n          \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n          \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n          \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n          \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n        }\n      }\n    }\n  );\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(\n        \"style\",\n        {\n          dangerouslySetInnerHTML: {\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n          },\n          nonce\n        }\n      ),\n      /* @__PURE__ */ jsx(Collection.Slot, { scope: __scopeSelect, children: /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          \"data-radix-select-viewport\": \"\",\n          role: \"presentation\",\n          ...viewportProps,\n          ref: composedRefs,\n          style: {\n            // we use position: 'relative' here on the `viewport` so that when we call\n            // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n            // (independent of the scrollUpButton).\n            position: \"relative\",\n            flex: 1,\n            // Viewport should only be scrollable in the vertical direction.\n            // This won't work in vertical writing modes, so we'll need to\n            // revisit this if/when that is supported\n            // https://developer.chrome.com/blog/vertical-form-controls\n            overflow: \"hidden auto\",\n            ...viewportProps.style\n          },\n          onScroll: composeEventHandlers(viewportProps.onScroll, (event) => {\n            const viewport = event.currentTarget;\n            const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n            if (shouldExpandOnScrollRef?.current && contentWrapper) {\n              const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n              if (scrolledBy > 0) {\n                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                const cssHeight = parseFloat(contentWrapper.style.height);\n                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                if (prevHeight < availableHeight) {\n                  const nextHeight = prevHeight + scrolledBy;\n                  const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                  const heightDiff = nextHeight - clampedNextHeight;\n                  contentWrapper.style.height = clampedNextHeight + \"px\";\n                  if (contentWrapper.style.bottom === \"0px\") {\n                    viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                    contentWrapper.style.justifyContent = \"flex-end\";\n                  }\n                }\n              }\n            }\n            prevScrollTopRef.current = viewport.scrollTop;\n          })\n        }\n      ) })\n    ] });\n  }\n);\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return /* @__PURE__ */ jsx(SelectGroupContextProvider, { scope: __scopeSelect, id: groupId, children: /* @__PURE__ */ jsx(Primitive.div, { role: \"group\", \"aria-labelledby\": groupId, ...groupProps, ref: forwardedRef }) });\n  }\n);\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ jsx(Primitive.div, { id: groupContext.id, ...labelProps, ref: forwardedRef });\n  }\n);\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = React.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n    const pointerTypeRef = React.useRef(\"touch\");\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n    if (value === \"\") {\n      throw new Error(\n        \"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\"\n      );\n    }\n    return /* @__PURE__ */ jsx(\n      SelectItemContextProvider,\n      {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? \"\").trim());\n        }, []),\n        children: /* @__PURE__ */ jsx(\n          Collection.ItemSlot,\n          {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ jsx(\n              Primitive.div,\n              {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: composeEventHandlers(itemProps.onFocus, () => setIsFocused(true)),\n                onBlur: composeEventHandlers(itemProps.onBlur, () => setIsFocused(false)),\n                onClick: composeEventHandlers(itemProps.onClick, () => {\n                  if (pointerTypeRef.current !== \"mouse\") handleSelect();\n                }),\n                onPointerUp: composeEventHandlers(itemProps.onPointerUp, () => {\n                  if (pointerTypeRef.current === \"mouse\") handleSelect();\n                }),\n                onPointerDown: composeEventHandlers(itemProps.onPointerDown, (event) => {\n                  pointerTypeRef.current = event.pointerType;\n                }),\n                onPointerMove: composeEventHandlers(itemProps.onPointerMove, (event) => {\n                  pointerTypeRef.current = event.pointerType;\n                  if (disabled) {\n                    contentContext.onItemLeave?.();\n                  } else if (pointerTypeRef.current === \"mouse\") {\n                    event.currentTarget.focus({ preventScroll: true });\n                  }\n                }),\n                onPointerLeave: composeEventHandlers(itemProps.onPointerLeave, (event) => {\n                  if (event.currentTarget === document.activeElement) {\n                    contentContext.onItemLeave?.();\n                  }\n                }),\n                onKeyDown: composeEventHandlers(itemProps.onKeyDown, (event) => {\n                  const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                  if (isTypingAhead && event.key === \" \") return;\n                  if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                  if (event.key === \" \") event.preventDefault();\n                })\n              }\n            )\n          }\n        )\n      }\n    );\n  }\n);\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => /* @__PURE__ */ jsx(\"option\", { value: itemContext.value, disabled: itemContext.disabled, children: textContent }, itemContext.value),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(Primitive.span, { id: itemContext.textId, ...itemTextProps, ref: composedRefs }),\n      itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? ReactDOM.createPortal(itemTextProps.children, context.valueNode) : null\n    ] });\n  }\n);\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ jsx(Primitive.span, { \"aria-hidden\": true, ...itemIndicatorProps, ref: forwardedRef }) : null;\n  }\n);\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = React.forwardRef((props, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      let handleScroll2 = function() {\n        const canScrollUp2 = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp2);\n      };\n      var handleScroll = handleScroll2;\n      const viewport = contentContext.viewport;\n      handleScroll2();\n      viewport.addEventListener(\"scroll\", handleScroll2);\n      return () => viewport.removeEventListener(\"scroll\", handleScroll2);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n  return canScrollUp ? /* @__PURE__ */ jsx(\n    SelectScrollButtonImpl,\n    {\n      ...props,\n      ref: composedRefs,\n      onAutoScroll: () => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }\n    }\n  ) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = React.forwardRef((props, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      let handleScroll2 = function() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown2);\n      };\n      var handleScroll = handleScroll2;\n      const viewport = contentContext.viewport;\n      handleScroll2();\n      viewport.addEventListener(\"scroll\", handleScroll2);\n      return () => viewport.removeEventListener(\"scroll\", handleScroll2);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n  return canScrollDown ? /* @__PURE__ */ jsx(\n    SelectScrollButtonImpl,\n    {\n      ...props,\n      ref: composedRefs,\n      onAutoScroll: () => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }\n    }\n  ) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = React.forwardRef((props, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n  const autoScrollTimerRef = React.useRef(null);\n  const getItems = useCollection(__scopeSelect);\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: \"nearest\" });\n  }, [getItems]);\n  return /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      \"aria-hidden\": true,\n      ...scrollIndicatorProps,\n      ref: forwardedRef,\n      style: { flexShrink: 0, ...scrollIndicatorProps.style },\n      onPointerDown: composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      }),\n      onPointerMove: composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      }),\n      onPointerLeave: composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })\n    }\n  );\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ jsx(Primitive.div, { \"aria-hidden\": true, ...separatorProps, ref: forwardedRef });\n  }\n);\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ jsx(PopperPrimitive.Arrow, { ...popperScope, ...arrowProps, ref: forwardedRef }) : null;\n  }\n);\nSelectArrow.displayName = ARROW_NAME;\nvar BUBBLE_INPUT_NAME = \"SelectBubbleInput\";\nvar SelectBubbleInput = React.forwardRef(\n  ({ __scopeSelect, value, ...props }, forwardedRef) => {\n    const ref = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n    React.useEffect(() => {\n      const select = ref.current;\n      if (!select) return;\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        \"value\"\n      );\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event(\"change\", { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n    return /* @__PURE__ */ jsx(\n      Primitive.select,\n      {\n        ...props,\n        style: { ...VISUALLY_HIDDEN_STYLES, ...props.style },\n        ref: composedRefs,\n        defaultValue: value\n      }\n    );\n  }\n);\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction shouldShowPlaceholder(value) {\n  return value === \"\" || value === void 0;\n}\nfunction useTypeaheadSearch(onSearchChange) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef(\"\");\n  const timerRef = React.useRef(0);\n  const handleTypeaheadSearch = React.useCallback(\n    (key) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n      (function updateSearch(value) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== \"\") timerRef.current = window.setTimeout(() => updateSearch(\"\"), 1e3);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = \"\";\n    window.clearTimeout(timerRef.current);\n  }, []);\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n  return [searchRef, handleTypeaheadSearch, resetTypeahead];\n}\nfunction findNextItem(items, search, currentItem) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0] : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find(\n    (item) => item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\nexport {\n  Arrow2 as Arrow,\n  Content2 as Content,\n  Group,\n  Icon,\n  Item,\n  ItemIndicator,\n  ItemText,\n  Label,\n  Portal,\n  Root2 as Root,\n  ScrollDownButton,\n  ScrollUpButton,\n  Select,\n  SelectArrow,\n  SelectContent,\n  SelectGroup,\n  SelectIcon,\n  SelectItem,\n  SelectItemIndicator,\n  SelectItemText,\n  SelectLabel,\n  SelectPortal,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n  SelectViewport,\n  Separator,\n  Trigger,\n  Value,\n  Viewport,\n  createSelectScope\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n", "// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\nexport {\n  clamp\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-previous/src/use-previous.tsx\nimport * as React from \"react\";\nfunction usePrevious(value) {\n  const ref = React.useRef({ value, previous: value });\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport {\n  usePrevious\n};\n//# sourceMappingURL=index.mjs.map\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm18 15-6-6-6 6', key: '153udz' }]];\n\n/**\n * @component @name ChevronUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTggMTUtNi02LTYgNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronUp = createLucideIcon('chevron-up', __iconNode);\n\nexport default ChevronUp;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }]];\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('chevron-down', __iconNode);\n\nexport default ChevronDown;\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { VISUALLY_HIDDEN_STYLES } from '@radix-ui/react-visually-hidden';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst OPEN_KEYS = [' ', 'Enter', 'ArrowUp', 'ArrowDown'];\nconst SELECTION_KEYS = [' ', 'Enter'];\n\n/* -------------------------------------------------------------------------------------------------\n * Select\n * -----------------------------------------------------------------------------------------------*/\n\nconst SELECT_NAME = 'Select';\n\ntype ItemData = { value: string; disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  SelectItemElement,\n  ItemData\n>(SELECT_NAME);\n\ntype ScopedProps<P> = P & { __scopeSelect?: Scope };\nconst [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype SelectContextValue = {\n  trigger: SelectTriggerElement | null;\n  onTriggerChange(node: SelectTriggerElement | null): void;\n  valueNode: SelectValueElement | null;\n  onValueNodeChange(node: SelectValueElement): void;\n  valueNodeHasChildren: boolean;\n  onValueNodeHasChildrenChange(hasChildren: boolean): void;\n  contentId: string;\n  value: string | undefined;\n  onValueChange(value: string): void;\n  open: boolean;\n  required?: boolean;\n  onOpenChange(open: boolean): void;\n  dir: SelectProps['dir'];\n  triggerPointerDownPosRef: React.MutableRefObject<{ x: number; y: number } | null>;\n  disabled?: boolean;\n};\n\nconst [SelectProvider, useSelectContext] = createSelectContext<SelectContextValue>(SELECT_NAME);\n\ntype NativeOption = React.ReactElement<React.ComponentProps<'option'>>;\n\ntype SelectNativeOptionsContextValue = {\n  onNativeOptionAdd(option: NativeOption): void;\n  onNativeOptionRemove(option: NativeOption): void;\n};\nconst [SelectNativeOptionsProvider, useSelectNativeOptionsContext] =\n  createSelectContext<SelectNativeOptionsContextValue>(SELECT_NAME);\n\ninterface ControlledClearableSelectProps {\n  value: string | undefined;\n  defaultValue?: never;\n  onValueChange: (value: string | undefined) => void;\n}\n\ninterface ControlledUnclearableSelectProps {\n  value: string;\n  defaultValue?: never;\n  onValueChange: (value: string) => void;\n}\n\ninterface UncontrolledSelectProps {\n  value?: never;\n  defaultValue?: string;\n  onValueChange?: {\n    (value: string): void;\n    (value: string | undefined): void;\n  };\n}\n\ntype SelectControlProps =\n  | ControlledClearableSelectProps\n  | ControlledUnclearableSelectProps\n  | UncontrolledSelectProps;\n\ninterface SelectSharedProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  name?: string;\n  autoComplete?: string;\n  disabled?: boolean;\n  required?: boolean;\n  form?: string;\n}\n\n// TODO: Should improve typing somewhat, but this would be a breaking change.\n// Consider using in the next major version (along with some testing to be sure\n// it works as expected and doesn't cause problems)\ntype _FutureSelectProps = SelectSharedProps & SelectControlProps;\n\ntype SelectProps = SelectSharedProps & {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?(value: string): void;\n};\n\nconst Select: React.FC<SelectProps> = (props: ScopedProps<SelectProps>) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form,\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState<SelectTriggerElement | null>(null);\n  const [valueNode, setValueNode] = React.useState<SelectValueElement | null>(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SELECT_NAME,\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange as any,\n    caller: SELECT_NAME,\n  });\n  const triggerPointerDownPosRef = React.useRef<{ x: number; y: number } | null>(null);\n\n  // We set this to true by default so that events bubble to forms without JS (SSR)\n  const isFormControl = trigger ? form || !!trigger.closest('form') : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(new Set<NativeOption>());\n\n  // The native `select` only associates the correct default value if the corresponding\n  // `option` is rendered as a child **at the same time** as itself.\n  // Because it might take a few renders for our items to gather the information to build\n  // the native `option`(s), we generate a key on the `select` to make sure React re-builds it\n  // each time the options change.\n  const nativeSelectKey = Array.from(nativeOptionsSet)\n    .map((option) => option.props.value)\n    .join(';');\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <SelectProvider\n        required={required}\n        scope={__scopeSelect}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        valueNode={valueNode}\n        onValueNodeChange={setValueNode}\n        valueNodeHasChildren={valueNodeHasChildren}\n        onValueNodeHasChildrenChange={setValueNodeHasChildren}\n        contentId={useId()}\n        value={value}\n        onValueChange={setValue}\n        open={open}\n        onOpenChange={setOpen}\n        dir={direction}\n        triggerPointerDownPosRef={triggerPointerDownPosRef}\n        disabled={disabled}\n      >\n        <Collection.Provider scope={__scopeSelect}>\n          <SelectNativeOptionsProvider\n            scope={props.__scopeSelect}\n            onNativeOptionAdd={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, [])}\n            onNativeOptionRemove={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, [])}\n          >\n            {children}\n          </SelectNativeOptionsProvider>\n        </Collection.Provider>\n\n        {isFormControl ? (\n          <SelectBubbleInput\n            key={nativeSelectKey}\n            aria-hidden\n            required={required}\n            tabIndex={-1}\n            name={name}\n            autoComplete={autoComplete}\n            value={value}\n            // enable form autofill\n            onChange={(event) => setValue(event.target.value)}\n            disabled={disabled}\n            form={form}\n          >\n            {value === undefined ? <option value=\"\" /> : null}\n            {Array.from(nativeOptionsSet)}\n          </SelectBubbleInput>\n        ) : null}\n      </SelectProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nSelect.displayName = SELECT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'SelectTrigger';\n\ntype SelectTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SelectTriggerProps extends PrimitiveButtonProps {}\n\nconst SelectTrigger = React.forwardRef<SelectTriggerElement, SelectTriggerProps>(\n  (props: ScopedProps<SelectTriggerProps>, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== undefined) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n\n    const handleOpen = (pointerEvent?: React.MouseEvent | React.PointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        // reset typeahead when we open\n        resetTypeahead();\n      }\n\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY),\n        };\n      }\n    };\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          type=\"button\"\n          role=\"combobox\"\n          aria-controls={context.contentId}\n          aria-expanded={context.open}\n          aria-required={context.required}\n          aria-autocomplete=\"none\"\n          dir={context.dir}\n          data-state={context.open ? 'open' : 'closed'}\n          disabled={isDisabled}\n          data-disabled={isDisabled ? '' : undefined}\n          data-placeholder={shouldShowPlaceholder(context.value) ? '' : undefined}\n          {...triggerProps}\n          ref={composedRefs}\n          // Enable compatibility with native label or custom `Label` \"click\" for Safari:\n          onClick={composeEventHandlers(triggerProps.onClick, (event) => {\n            // Whilst browsers generally have no issue focusing the trigger when clicking\n            // on a label, Safari seems to struggle with the fact that there's no `onClick`.\n            // We force `focus` in this case. Note: this doesn't create any other side-effect\n            // because we are preventing default in `onPointerDown` so effectively\n            // this only runs for a label \"click\"\n            event.currentTarget.focus();\n\n            // Open on click when using a touch or pen device\n            if (pointerTypeRef.current !== 'mouse') {\n              handleOpen(event);\n            }\n          })}\n          onPointerDown={composeEventHandlers(triggerProps.onPointerDown, (event) => {\n            pointerTypeRef.current = event.pointerType;\n\n            // prevent implicit pointer capture\n            // https://www.w3.org/TR/pointerevents3/#implicit-pointer-capture\n            const target = event.target as HTMLElement;\n            if (target.hasPointerCapture(event.pointerId)) {\n              target.releasePointerCapture(event.pointerId);\n            }\n\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click); also not for touch\n            // devices because that would open the menu on scroll. (pen devices behave as touch on iOS).\n            if (event.button === 0 && event.ctrlKey === false && event.pointerType === 'mouse') {\n              handleOpen(event);\n              // prevent trigger from stealing focus from the active item after opening.\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(triggerProps.onKeyDown, (event) => {\n            const isTypingAhead = searchRef.current !== '';\n            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n            if (isTypingAhead && event.key === ' ') return;\n            if (OPEN_KEYS.includes(event.key)) {\n              handleOpen();\n              event.preventDefault();\n            }\n          })}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nSelectTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectValue\n * -----------------------------------------------------------------------------------------------*/\n\nconst VALUE_NAME = 'SelectValue';\n\ntype SelectValueElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SelectValueProps extends Omit<PrimitiveSpanProps, 'placeholder'> {\n  placeholder?: React.ReactNode;\n}\n\nconst SelectValue = React.forwardRef<SelectValueElement, SelectValueProps>(\n  (props: ScopedProps<SelectValueProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, children, placeholder = '', ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== undefined;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n\n    return (\n      <Primitive.span\n        {...valueProps}\n        ref={composedRefs}\n        // we don't want events from the portalled `SelectValue` children to bubble\n        // through the item they came from\n        style={{ pointerEvents: 'none' }}\n      >\n        {shouldShowPlaceholder(context.value) ? <>{placeholder}</> : children}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectValue.displayName = VALUE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectIcon\n * -----------------------------------------------------------------------------------------------*/\n\nconst ICON_NAME = 'SelectIcon';\n\ntype SelectIconElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectIconProps extends PrimitiveSpanProps {}\n\nconst SelectIcon = React.forwardRef<SelectIconElement, SelectIconProps>(\n  (props: ScopedProps<SelectIconProps>, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return (\n      <Primitive.span aria-hidden {...iconProps} ref={forwardedRef}>\n        {children || '▼'}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectIcon.displayName = ICON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'SelectPortal';\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface SelectPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n}\n\nconst SelectPortal: React.FC<SelectPortalProps> = (props: ScopedProps<SelectPortalProps>) => {\n  return <PortalPrimitive asChild {...props} />;\n};\n\nSelectPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'SelectContent';\n\ntype SelectContentElement = SelectContentImplElement;\ninterface SelectContentProps extends SelectContentImplProps {}\n\nconst SelectContent = React.forwardRef<SelectContentElement, SelectContentProps>(\n  (props: ScopedProps<SelectContentProps>, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState<DocumentFragment>();\n\n    // setting the fragment in `useLayoutEffect` as `DocumentFragment` doesn't exist on the server\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n\n    if (!context.open) {\n      const frag = fragment as Element | undefined;\n      return frag\n        ? ReactDOM.createPortal(\n            <SelectContentProvider scope={props.__scopeSelect}>\n              <Collection.Slot scope={props.__scopeSelect}>\n                <div>{props.children}</div>\n              </Collection.Slot>\n            </SelectContentProvider>,\n            frag\n          )\n        : null;\n    }\n\n    return <SelectContentImpl {...props} ref={forwardedRef} />;\n  }\n);\n\nSelectContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContentImpl\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_MARGIN = 10;\n\ntype SelectContentContextValue = {\n  content?: SelectContentElement | null;\n  viewport?: SelectViewportElement | null;\n  onViewportChange?: (node: SelectViewportElement | null) => void;\n  itemRefCallback?: (node: SelectItemElement | null, value: string, disabled: boolean) => void;\n  selectedItem?: SelectItemElement | null;\n  onItemLeave?: () => void;\n  itemTextRefCallback?: (\n    node: SelectItemTextElement | null,\n    value: string,\n    disabled: boolean\n  ) => void;\n  focusSelectedItem?: () => void;\n  selectedItemText?: SelectItemTextElement | null;\n  position?: SelectContentProps['position'];\n  isPositioned?: boolean;\n  searchRef?: React.RefObject<string>;\n};\n\nconst [SelectContentProvider, useSelectContentContext] =\n  createSelectContext<SelectContentContextValue>(CONTENT_NAME);\n\nconst CONTENT_IMPL_NAME = 'SelectContentImpl';\n\ntype SelectContentImplElement = SelectPopperPositionElement | SelectItemAlignedPositionElement;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\n\ntype SelectPopperPrivateProps = { onPlaced?: PopperContentProps['onPlaced'] };\n\ninterface SelectContentImplProps\n  extends Omit<SelectPopperPositionProps, keyof SelectPopperPrivateProps>,\n    Omit<SelectItemAlignedPositionProps, keyof SelectPopperPrivateProps> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n\n  position?: 'item-aligned' | 'popper';\n}\n\nconst Slot = createSlot('SelectContent.RemoveScroll');\n\nconst SelectContentImpl = React.forwardRef<SelectContentImplElement, SelectContentImplProps>(\n  (props: ScopedProps<SelectContentImplProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = 'item-aligned',\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState<SelectContentImplElement | null>(null);\n    const [viewport, setViewport] = React.useState<SelectViewportElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState<SelectItemElement | null>(null);\n    const [selectedItemText, setSelectedItemText] = React.useState<SelectItemTextElement | null>(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n\n    // Make sure the whole tree has focus guards as our `Select` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const focusFirst = React.useCallback(\n      (candidates: Array<HTMLElement | null>) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          // if focus is already where we want to go, we don't want to keep going through the candidates\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: 'nearest' });\n          // viewport might have padding so scroll to its edges when focusing first/last items.\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n\n    // Since this is not dependent on layout, we want to ensure this runs at the same time as\n    // other effects across components. Hence why we don't call `focusSelectedItem` inside `position`.\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n\n    // prevent selecting items on `pointerup` in some cases after opening from `pointerdown`\n    // and close on `pointerup` outside.\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n\n        const handlePointerMove = (event: PointerEvent) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0)),\n          };\n        };\n        const handlePointerUp = (event: PointerEvent) => {\n          // If the pointer hasn't moved by a certain threshold then we prevent selecting item on `pointerup`.\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            // otherwise, if the event was outside the content, close.\n            if (!content.contains(event.target as HTMLElement)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener('pointermove', handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener('pointermove', handlePointerMove);\n          document.addEventListener('pointerup', handlePointerUp, { capture: true, once: true });\n        }\n\n        return () => {\n          document.removeEventListener('pointermove', handlePointerMove);\n          document.removeEventListener('pointerup', handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener('blur', close);\n      window.addEventListener('resize', close);\n      return () => {\n        window.removeEventListener('blur', close);\n        window.removeEventListener('resize', close);\n      };\n    }, [onOpenChange]);\n\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (nextItem.ref.current as HTMLElement).focus());\n      }\n    });\n\n    const itemRefCallback = React.useCallback(\n      (node: SelectItemElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node: SelectItemTextElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n\n    const SelectPosition = position === 'popper' ? SelectPopperPosition : SelectItemAlignedPosition;\n\n    // Silently ignore props that are not supported by `SelectItemAlignedPosition`\n    const popperContentProps =\n      SelectPosition === SelectPopperPosition\n        ? {\n            side,\n            sideOffset,\n            align,\n            alignOffset,\n            arrowPadding,\n            collisionBoundary,\n            collisionPadding,\n            sticky,\n            hideWhenDetached,\n            avoidCollisions,\n          }\n        : {};\n\n    return (\n      <SelectContentProvider\n        scope={__scopeSelect}\n        content={content}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        itemRefCallback={itemRefCallback}\n        selectedItem={selectedItem}\n        onItemLeave={handleItemLeave}\n        itemTextRefCallback={itemTextRefCallback}\n        focusSelectedItem={focusSelectedItem}\n        selectedItemText={selectedItemText}\n        position={position}\n        isPositioned={isPositioned}\n        searchRef={searchRef}\n      >\n        <RemoveScroll as={Slot} allowPinchZoom>\n          <FocusScope\n            asChild\n            // we make sure we're not trapping once it's been closed\n            // (closed !== unmounted when animating out)\n            trapped={context.open}\n            onMountAutoFocus={(event) => {\n              // we prevent open autofocus because we manually focus the selected item\n              event.preventDefault();\n            }}\n            onUnmountAutoFocus={composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            })}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              // When focus is trapped, a focusout event may still happen.\n              // We make sure we don't trigger our `onDismiss` in such case.\n              onFocusOutside={(event) => event.preventDefault()}\n              onDismiss={() => context.onOpenChange(false)}\n            >\n              <SelectPosition\n                role=\"listbox\"\n                id={context.contentId}\n                data-state={context.open ? 'open' : 'closed'}\n                dir={context.dir}\n                onContextMenu={(event) => event.preventDefault()}\n                {...contentProps}\n                {...popperContentProps}\n                onPlaced={() => setIsPositioned(true)}\n                ref={composedRefs}\n                style={{\n                  // flex layout so we can place the scroll buttons properly\n                  display: 'flex',\n                  flexDirection: 'column',\n                  // reset the outline by default as the content MAY get focused\n                  outline: 'none',\n                  ...contentProps.style,\n                }}\n                onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                  const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n\n                  // select should not be navigated using tab key so we prevent it\n                  if (event.key === 'Tab') event.preventDefault();\n\n                  if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n\n                  if (['ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {\n                    const items = getItems().filter((item) => !item.disabled);\n                    let candidateNodes = items.map((item) => item.ref.current!);\n\n                    if (['ArrowUp', 'End'].includes(event.key)) {\n                      candidateNodes = candidateNodes.slice().reverse();\n                    }\n                    if (['ArrowUp', 'ArrowDown'].includes(event.key)) {\n                      const currentElement = event.target as SelectItemElement;\n                      const currentIndex = candidateNodes.indexOf(currentElement);\n                      candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n\n                    /**\n                     * Imperative focus during keydown is risky so we prevent React's batching updates\n                     * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n                     */\n                    setTimeout(() => focusFirst(candidateNodes));\n\n                    event.preventDefault();\n                  }\n                })}\n              />\n            </DismissableLayer>\n          </FocusScope>\n        </RemoveScroll>\n      </SelectContentProvider>\n    );\n  }\n);\n\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemAlignedPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_ALIGNED_POSITION_NAME = 'SelectItemAlignedPosition';\n\ntype SelectItemAlignedPositionElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectItemAlignedPositionProps extends PrimitiveDivProps, SelectPopperPrivateProps {}\n\nconst SelectItemAlignedPosition = React.forwardRef<\n  SelectItemAlignedPositionElement,\n  SelectItemAlignedPositionProps\n>((props: ScopedProps<SelectItemAlignedPositionProps>, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState<HTMLDivElement | null>(null);\n  const [content, setContent] = React.useState<SelectItemAlignedPositionElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (\n      context.trigger &&\n      context.valueNode &&\n      contentWrapper &&\n      content &&\n      viewport &&\n      selectedItem &&\n      selectedItemText\n    ) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n\n      // -----------------------------------------------------------------------------------------\n      //  Horizontal positioning\n      // -----------------------------------------------------------------------------------------\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n\n      if (context.dir !== 'rtl') {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.left = clampedLeft + 'px';\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.right = clampedRight + 'px';\n      }\n\n      // -----------------------------------------------------------------------------------------\n      // Vertical positioning\n      // -----------------------------------------------------------------------------------------\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth; // prettier-ignore\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem =\n          items.length > 0 && selectedItem === items[items.length - 1]!.ref.current;\n        contentWrapper.style.bottom = 0 + 'px';\n        const viewportOffsetBottom =\n          content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight +\n            // viewport might have padding bottom, include it to avoid a scrollable viewport\n            (isLastItem ? viewportPaddingBottom : 0) +\n            viewportOffsetBottom +\n            contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + 'px';\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0]!.ref.current;\n        contentWrapper.style.top = 0 + 'px';\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth +\n            viewport.offsetTop +\n            // viewport might have padding top, include it to avoid a scrollable viewport\n            (isFirstItem ? viewportPaddingTop : 0) +\n            selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + 'px';\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + 'px';\n      contentWrapper.style.maxHeight = availableHeight + 'px';\n      // -----------------------------------------------------------------------------------------\n\n      onPlaced?.();\n\n      // we don't want the initial scroll position adjustment to trigger \"expand on scroll\"\n      // so we explicitly turn it on only after they've registered.\n      requestAnimationFrame(() => (shouldExpandOnScrollRef.current = true));\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced,\n  ]);\n\n  useLayoutEffect(() => position(), [position]);\n\n  // copy z-index from content to wrapper\n  const [contentZIndex, setContentZIndex] = React.useState<string>();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n\n  // When the viewport becomes scrollable at the top, the scroll up button will mount.\n  // Because it is part of the normal flow, it will push down the viewport, thus throwing our\n  // trigger => selectedItem alignment off by the amount the viewport was pushed down.\n  // We wait for this to happen and then re-run the positining logic one more time to account for it.\n  const handleScrollButtonChange = React.useCallback(\n    (node: SelectScrollButtonImplElement | null) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n\n  return (\n    <SelectViewportProvider\n      scope={__scopeSelect}\n      contentWrapper={contentWrapper}\n      shouldExpandOnScrollRef={shouldExpandOnScrollRef}\n      onScrollButtonChange={handleScrollButtonChange}\n    >\n      <div\n        ref={setContentWrapper}\n        style={{\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'fixed',\n          zIndex: contentZIndex,\n        }}\n      >\n        <Primitive.div\n          {...popperProps}\n          ref={composedRefs}\n          style={{\n            // When we get the height of the content, it includes borders. If we were to set\n            // the height without having `boxSizing: 'border-box'` it would be too big.\n            boxSizing: 'border-box',\n            // We need to ensure the content doesn't get taller than the wrapper\n            maxHeight: '100%',\n            ...popperProps.style,\n          }}\n        />\n      </div>\n    </SelectViewportProvider>\n  );\n});\n\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPopperPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_POSITION_NAME = 'SelectPopperPosition';\n\ntype SelectPopperPositionElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface SelectPopperPositionProps extends PopperContentProps, SelectPopperPrivateProps {}\n\nconst SelectPopperPosition = React.forwardRef<\n  SelectPopperPositionElement,\n  SelectPopperPositionProps\n>((props: ScopedProps<SelectPopperPositionProps>, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = 'start',\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n\n  return (\n    <PopperPrimitive.Content\n      {...popperScope}\n      {...popperProps}\n      ref={forwardedRef}\n      align={align}\n      collisionPadding={collisionPadding}\n      style={{\n        // Ensure border-box for floating-ui calculations\n        boxSizing: 'border-box',\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-select-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-select-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-select-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-select-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-select-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectViewport\n * -----------------------------------------------------------------------------------------------*/\n\ntype SelectViewportContextValue = {\n  contentWrapper?: HTMLDivElement | null;\n  shouldExpandOnScrollRef?: React.RefObject<boolean>;\n  onScrollButtonChange?: (node: SelectScrollButtonImplElement | null) => void;\n};\n\nconst [SelectViewportProvider, useSelectViewportContext] =\n  createSelectContext<SelectViewportContextValue>(CONTENT_NAME, {});\n\nconst VIEWPORT_NAME = 'SelectViewport';\n\ntype SelectViewportElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SelectViewportProps extends PrimitiveDivProps {\n  nonce?: string;\n}\n\nconst SelectViewport = React.forwardRef<SelectViewportElement, SelectViewportProps>(\n  (props: ScopedProps<SelectViewportProps>, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n          nonce={nonce}\n        />\n        <Collection.Slot scope={__scopeSelect}>\n          <Primitive.div\n            data-radix-select-viewport=\"\"\n            role=\"presentation\"\n            {...viewportProps}\n            ref={composedRefs}\n            style={{\n              // we use position: 'relative' here on the `viewport` so that when we call\n              // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n              // (independent of the scrollUpButton).\n              position: 'relative',\n              flex: 1,\n              // Viewport should only be scrollable in the vertical direction.\n              // This won't work in vertical writing modes, so we'll need to\n              // revisit this if/when that is supported\n              // https://developer.chrome.com/blog/vertical-form-controls\n              overflow: 'hidden auto',\n              ...viewportProps.style,\n            }}\n            onScroll={composeEventHandlers(viewportProps.onScroll, (event) => {\n              const viewport = event.currentTarget;\n              const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n              if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                if (scrolledBy > 0) {\n                  const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                  const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                  const cssHeight = parseFloat(contentWrapper.style.height);\n                  const prevHeight = Math.max(cssMinHeight, cssHeight);\n\n                  if (prevHeight < availableHeight) {\n                    const nextHeight = prevHeight + scrolledBy;\n                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                    const heightDiff = nextHeight - clampedNextHeight;\n\n                    contentWrapper.style.height = clampedNextHeight + 'px';\n                    if (contentWrapper.style.bottom === '0px') {\n                      viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                      // ensure the content stays pinned to the bottom\n                      contentWrapper.style.justifyContent = 'flex-end';\n                    }\n                  }\n                }\n              }\n              prevScrollTopRef.current = viewport.scrollTop;\n            })}\n          />\n        </Collection.Slot>\n      </>\n    );\n  }\n);\n\nSelectViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'SelectGroup';\n\ntype SelectGroupContextValue = { id: string };\n\nconst [SelectGroupContextProvider, useSelectGroupContext] =\n  createSelectContext<SelectGroupContextValue>(GROUP_NAME);\n\ntype SelectGroupElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectGroupProps extends PrimitiveDivProps {}\n\nconst SelectGroup = React.forwardRef<SelectGroupElement, SelectGroupProps>(\n  (props: ScopedProps<SelectGroupProps>, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return (\n      <SelectGroupContextProvider scope={__scopeSelect} id={groupId}>\n        <Primitive.div role=\"group\" aria-labelledby={groupId} {...groupProps} ref={forwardedRef} />\n      </SelectGroupContextProvider>\n    );\n  }\n);\n\nSelectGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'SelectLabel';\n\ntype SelectLabelElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectLabelProps extends PrimitiveDivProps {}\n\nconst SelectLabel = React.forwardRef<SelectLabelElement, SelectLabelProps>(\n  (props: ScopedProps<SelectLabelProps>, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return <Primitive.div id={groupContext.id} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nSelectLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'SelectItem';\n\ntype SelectItemContextValue = {\n  value: string;\n  disabled: boolean;\n  textId: string;\n  isSelected: boolean;\n  onItemTextChange(node: SelectItemTextElement | null): void;\n};\n\nconst [SelectItemContextProvider, useSelectItemContext] =\n  createSelectContext<SelectItemContextValue>(ITEM_NAME);\n\ntype SelectItemElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectItemProps extends PrimitiveDivProps {\n  value: string;\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst SelectItem = React.forwardRef<SelectItemElement, SelectItemProps>(\n  (props: ScopedProps<SelectItemProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? '');\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) =>\n      contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n\n    if (value === '') {\n      throw new Error(\n        'A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.'\n      );\n    }\n\n    return (\n      <SelectItemContextProvider\n        scope={__scopeSelect}\n        value={value}\n        disabled={disabled}\n        textId={textId}\n        isSelected={isSelected}\n        onItemTextChange={React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? '').trim());\n        }, [])}\n      >\n        <Collection.ItemSlot\n          scope={__scopeSelect}\n          value={value}\n          disabled={disabled}\n          textValue={textValue}\n        >\n          <Primitive.div\n            role=\"option\"\n            aria-labelledby={textId}\n            data-highlighted={isFocused ? '' : undefined}\n            // `isFocused` caveat fixes stuttering in VoiceOver\n            aria-selected={isSelected && isFocused}\n            data-state={isSelected ? 'checked' : 'unchecked'}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            tabIndex={disabled ? undefined : -1}\n            {...itemProps}\n            ref={composedRefs}\n            onFocus={composeEventHandlers(itemProps.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(itemProps.onBlur, () => setIsFocused(false))}\n            onClick={composeEventHandlers(itemProps.onClick, () => {\n              // Open on click when using a touch or pen device\n              if (pointerTypeRef.current !== 'mouse') handleSelect();\n            })}\n            onPointerUp={composeEventHandlers(itemProps.onPointerUp, () => {\n              // Using a mouse you should be able to do pointer down, move through\n              // the list, and release the pointer over the item to select it.\n              if (pointerTypeRef.current === 'mouse') handleSelect();\n            })}\n            onPointerDown={composeEventHandlers(itemProps.onPointerDown, (event) => {\n              pointerTypeRef.current = event.pointerType;\n            })}\n            onPointerMove={composeEventHandlers(itemProps.onPointerMove, (event) => {\n              // Remember pointer type when sliding over to this item from another one\n              pointerTypeRef.current = event.pointerType;\n              if (disabled) {\n                contentContext.onItemLeave?.();\n              } else if (pointerTypeRef.current === 'mouse') {\n                // even though safari doesn't support this option, it's acceptable\n                // as it only means it might scroll a few pixels when using the pointer.\n                event.currentTarget.focus({ preventScroll: true });\n              }\n            })}\n            onPointerLeave={composeEventHandlers(itemProps.onPointerLeave, (event) => {\n              if (event.currentTarget === document.activeElement) {\n                contentContext.onItemLeave?.();\n              }\n            })}\n            onKeyDown={composeEventHandlers(itemProps.onKeyDown, (event) => {\n              const isTypingAhead = contentContext.searchRef?.current !== '';\n              if (isTypingAhead && event.key === ' ') return;\n              if (SELECTION_KEYS.includes(event.key)) handleSelect();\n              // prevent page scroll if using the space key to select an item\n              if (event.key === ' ') event.preventDefault();\n            })}\n          />\n        </Collection.ItemSlot>\n      </SelectItemContextProvider>\n    );\n  }\n);\n\nSelectItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemText\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_TEXT_NAME = 'SelectItemText';\n\ntype SelectItemTextElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectItemTextProps extends PrimitiveSpanProps {}\n\nconst SelectItemText = React.forwardRef<SelectItemTextElement, SelectItemTextProps>(\n  (props: ScopedProps<SelectItemTextProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState<SelectItemTextElement | null>(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => (\n        <option key={itemContext.value} value={itemContext.value} disabled={itemContext.disabled}>\n          {textContent}\n        </option>\n      ),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n\n    return (\n      <>\n        <Primitive.span id={itemContext.textId} {...itemTextProps} ref={composedRefs} />\n\n        {/* Portal the select item text into the trigger value node */}\n        {itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren\n          ? ReactDOM.createPortal(itemTextProps.children, context.valueNode)\n          : null}\n      </>\n    );\n  }\n);\n\nSelectItemText.displayName = ITEM_TEXT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'SelectItemIndicator';\n\ntype SelectItemIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectItemIndicatorProps extends PrimitiveSpanProps {}\n\nconst SelectItemIndicator = React.forwardRef<SelectItemIndicatorElement, SelectItemIndicatorProps>(\n  (props: ScopedProps<SelectItemIndicatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? (\n      <Primitive.span aria-hidden {...itemIndicatorProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollUpButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_UP_BUTTON_NAME = 'SelectScrollUpButton';\n\ntype SelectScrollUpButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollUpButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollUpButton = React.forwardRef<\n  SelectScrollUpButtonElement,\n  SelectScrollUpButtonProps\n>((props: ScopedProps<SelectScrollUpButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const canScrollUp = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollUp ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollDownButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_DOWN_BUTTON_NAME = 'SelectScrollDownButton';\n\ntype SelectScrollDownButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollDownButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollDownButton = React.forwardRef<\n  SelectScrollDownButtonElement,\n  SelectScrollDownButtonProps\n>((props: ScopedProps<SelectScrollDownButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        // we use Math.ceil here because if the UI is zoomed-in\n        // `scrollTop` is not always reported as an integer\n        const canScrollDown = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollDown ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\n\ntype SelectScrollButtonImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectScrollButtonImplProps extends PrimitiveDivProps {\n  onAutoScroll(): void;\n}\n\nconst SelectScrollButtonImpl = React.forwardRef<\n  SelectScrollButtonImplElement,\n  SelectScrollButtonImplProps\n>((props: ScopedProps<SelectScrollButtonImplProps>, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext('SelectScrollButton', __scopeSelect);\n  const autoScrollTimerRef = React.useRef<number | null>(null);\n  const getItems = useCollection(__scopeSelect);\n\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n\n  // When the viewport becomes scrollable on either side, the relevant scroll button will mount.\n  // Because it is part of the normal flow, it will push down (top button) or shrink (bottom button)\n  // the viewport, potentially causing the active item to now be partially out of view.\n  // We re-run the `scrollIntoView` logic to make sure it stays within the viewport.\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: 'nearest' });\n  }, [getItems]);\n\n  return (\n    <Primitive.div\n      aria-hidden\n      {...scrollIndicatorProps}\n      ref={forwardedRef}\n      style={{ flexShrink: 0, ...scrollIndicatorProps.style }}\n      onPointerDown={composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerMove={composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerLeave={composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })}\n    />\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * SelectSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'SelectSeparator';\n\ntype SelectSeparatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectSeparatorProps extends PrimitiveDivProps {}\n\nconst SelectSeparator = React.forwardRef<SelectSeparatorElement, SelectSeparatorProps>(\n  (props: ScopedProps<SelectSeparatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return <Primitive.div aria-hidden {...separatorProps} ref={forwardedRef} />;\n  }\n);\n\nSelectSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'SelectArrow';\n\ntype SelectArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface SelectArrowProps extends PopperArrowProps {}\n\nconst SelectArrow = React.forwardRef<SelectArrowElement, SelectArrowProps>(\n  (props: ScopedProps<SelectArrowProps>, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === 'popper' ? (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'SelectBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.select>;\ninterface SwitchBubbleInputProps extends InputProps {}\n\nconst SelectBubbleInput = React.forwardRef<HTMLSelectElement, SwitchBubbleInputProps>(\n  ({ __scopeSelect, value, ...props }: ScopedProps<SwitchBubbleInputProps>, forwardedRef) => {\n    const ref = React.useRef<HTMLSelectElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n\n    // Bubble value change to parents (e.g form change event)\n    React.useEffect(() => {\n      const select = ref.current;\n      if (!select) return;\n\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        'value'\n      ) as PropertyDescriptor;\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event('change', { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n\n    /**\n     * We purposefully use a `select` here to support form autofill as much as\n     * possible.\n     *\n     * We purposefully do not add the `value` attribute here to allow the value\n     * to be set programmatically and bubble to any parent form `onChange`\n     * event. Adding the `value` will cause React to consider the programmatic\n     * dispatch a duplicate and it will get swallowed.\n     *\n     * We use visually hidden styles rather than `display: \"none\"` because\n     * Safari autofill won't work otherwise.\n     */\n    return (\n      <Primitive.select\n        {...props}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n        ref={composedRefs}\n        defaultValue={value}\n      />\n    );\n  }\n);\n\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction shouldShowPlaceholder(value?: string) {\n  return value === '' || value === undefined;\n}\n\nfunction useTypeaheadSearch(onSearchChange: (search: string) => void) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef('');\n  const timerRef = React.useRef(0);\n\n  const handleTypeaheadSearch = React.useCallback(\n    (key: string) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        // Reset `searchRef` 1 second after it was last updated\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = '';\n    window.clearTimeout(timerRef.current);\n  }, []);\n\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n\n  return [searchRef, handleTypeaheadSearch, resetTypeahead] as const;\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in a list of items,\n * the search and the current item, and returns the next item (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through items starting with that character)\n *\n * We also reorder the items by wrapping the array around the current item.\n * This is so we always look forward from the current item, and picking the first\n * item will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current item from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current item still matches.\n */\nfunction findNextItem<T extends { textValue: string }>(\n  items: T[],\n  search: string,\n  currentItem?: T\n) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find((item) =>\n    item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : undefined;\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = Select;\nconst Trigger = SelectTrigger;\nconst Value = SelectValue;\nconst Icon = SelectIcon;\nconst Portal = SelectPortal;\nconst Content = SelectContent;\nconst Viewport = SelectViewport;\nconst Group = SelectGroup;\nconst Label = SelectLabel;\nconst Item = SelectItem;\nconst ItemText = SelectItemText;\nconst ItemIndicator = SelectItemIndicator;\nconst ScrollUpButton = SelectScrollUpButton;\nconst ScrollDownButton = SelectScrollDownButton;\nconst Separator = SelectSeparator;\nconst Arrow = SelectArrow;\n\nexport {\n  createSelectScope,\n  //\n  Select,\n  SelectTrigger,\n  SelectValue,\n  SelectIcon,\n  SelectPortal,\n  SelectContent,\n  SelectViewport,\n  SelectGroup,\n  SelectLabel,\n  SelectItem,\n  SelectItemText,\n  SelectItemIndicator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n  SelectSeparator,\n  SelectArrow,\n  //\n  Root,\n  Trigger,\n  Value,\n  Icon,\n  Portal,\n  Content,\n  Viewport,\n  Group,\n  Label,\n  Item,\n  ItemText,\n  ItemIndicator,\n  ScrollUpButton,\n  ScrollDownButton,\n  Separator,\n  Arrow,\n};\nexport type {\n  SelectProps,\n  SelectTriggerProps,\n  SelectValueProps,\n  SelectIconProps,\n  SelectPortalProps,\n  SelectContentProps,\n  SelectViewportProps,\n  SelectGroupProps,\n  SelectLabelProps,\n  SelectItemProps,\n  SelectItemTextProps,\n  SelectItemIndicatorProps,\n  SelectScrollUpButtonProps,\n  SelectScrollDownButtonProps,\n  SelectSeparatorProps,\n  SelectArrowProps,\n};\n", "import * as React from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = React.useRef({ value, previous: value });\n\n  // We compare values before making an update to ensure that\n  // a change has been made. This ensures the previous value is\n  // persisted correctly between renders.\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\nexport { usePrevious };\n"], "names": [], "mappings": "4KAGA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OEHA,SAAS,EAAM,CAAK,CAAE,CAAC,EAAK,EAAI,EAC9B,OAAO,KAAK,GAAG,CAAC,EAAK,KAAK,GAAG,CAAC,EAAK,GACrC,CFGA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEI,EAAY,CAAC,IAAK,QAAS,UAAW,YAAY,CAClD,EAAiB,CAAC,IAAK,QAAQ,CAC/B,EAAc,SACd,CAAC,EAAY,EAAe,EAAsB,CAAG,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACtE,CAAC,EAAqB,EAAkB,CAAG,CAAA,EAAA,EAAA,kBAAA,AAAkB,EAAC,EAAa,CAC7E,EACA,EAAA,iBAAiB,CAClB,EACG,EAAiB,CAAA,EAAA,EAAA,iBAAA,AAAiB,IAClC,CAAC,EAAgB,EAAiB,CAAG,EAAoB,GACzD,CAAC,EAA6B,EAA8B,CAAG,EAAoB,GACnF,EAAU,AAAD,IACX,GAAM,eACJ,CAAa,UACb,CAAQ,CACR,KAAM,CAAQ,aACd,CAAW,cACX,CAAY,CACZ,MAAO,CAAS,cAChB,CAAY,eACZ,CAAa,KACb,CAAG,MACH,CAAI,cACJ,CAAY,UACZ,CAAQ,CACR,UAAQ,MACR,CAAI,CACL,CAAG,EACE,EAAc,EAAe,GAC7B,CAAC,EAAS,EAAW,CAAG,EAAA,QAAc,CAAC,MACvC,CAAC,EAAW,EAAa,CAAG,EAAA,QAAc,CAAC,MAC3C,CAAC,EAAsB,EAAwB,CAAG,EAAA,QAAc,CAAC,IACjE,EAAY,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,GACzB,CAAC,EAAM,EAAQ,CAAG,CAAA,EAAA,EAAA,oBAAoB,AAApB,EAAqB,CAC3C,KAAM,EACN,YAAa,GAAe,GAC5B,SAAU,EACV,OAAQ,CACV,GACM,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,CAC7C,KAAM,EACN,YAAa,EACb,SAAU,EACV,OAAQ,CACV,GACM,EAA2B,EAAA,MAAY,CAAC,MACxC,GAAgB,GAAU,GAAQ,CAAC,CAAC,EAAQ,OAAO,CAAC,QACpD,CAAC,CAD6D,CAC3C,EAAoB,CAAG,EAAA,QAAc,CAAiB,AAAhB,IAAoB,KAC7E,EAAkB,EADoD,IAC9C,IAAI,CAAC,GAAkB,GAAG,CAAC,AAAC,GAAW,EAAO,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAC9F,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,EAA2B,CAAE,CAAE,GAAG,CAAW,CAAE,SAA0B,CAAhB,AAAgB,EAAA,EAAA,IAAA,AAAI,EAC/F,EACA,CAFwF,SAGtF,EACA,MAAO,UACP,EACA,gBAAiB,EACjB,YACA,kBAAmB,EACnB,uBACA,6BAA8B,EAC9B,UAAW,CAAA,EAAA,EAAA,KAAA,AAAK,UAChB,EACA,cAAe,OACf,EACA,aAAc,EACd,IAAK,EACL,2BACA,WACA,SAAU,CACQ,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EAAW,QAAQ,CAAE,CAAE,MAAO,EAAe,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAC5F,EACA,CACE,CAHoF,KAG7E,EAAM,aAAa,CAC1B,kBAAmB,EAAA,WAAiB,CAAC,AAAC,IACpC,EAAoB,AAAC,GAAS,IAAI,IAAI,GAAM,GAAG,CAAC,GAClD,EAAG,EAAE,EACL,qBAAsB,EAAA,WAAiB,CAAC,AAAC,IACvC,EAAqB,AAAD,IAClB,IAAM,EAAa,IAAI,IAAI,GAE3B,OADA,EAAW,MAAM,CAAC,GACX,CACT,EACF,EAAG,EAAE,WACL,CACF,EACA,GACF,EAAgC,CAAA,EAAA,EAAA,IAAI,AAAJ,EAC9B,GADc,AAEd,CACE,YAHyB,GAGV,WACf,EACA,SAAU,CAAC,OACX,EACA,qBACA,EACA,SAAU,AAAC,GAAU,EAAS,EAAM,MAAM,CAAC,KAAK,WAChD,OACA,EACA,SAAU,CACE,KAAK,IAAI,AAAnB,EAAmC,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,CAAP,QAAiB,CAAE,MAAO,EAAG,GAAK,KAClE,MAAM,IAAI,CAAC,GAEf,AADG,EAEH,GACE,KAER,AADG,EAEH,EACJ,EACA,EAAO,WAAW,CAAG,EACrB,IAAI,EAAe,gBACf,EAAgB,EAAA,UAAgB,CAClC,CAAC,EAAO,KACN,GAAM,eAAE,CAAa,CAAE,YAAW,CAAK,CAAE,GAAG,EAAc,CAAG,EACvD,EAAc,EAAe,GAC7B,EAAU,EAAiB,EAAc,GACzC,EAAa,EAAQ,QAAQ,EAAI,EACjC,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,EAAQ,eAAe,EACpE,EAAW,EAAc,GACzB,EAAiB,EAAA,MAAY,CAAC,SAC9B,CAAC,EAAW,EAAuB,EAAe,CAAG,GAAmB,AAAC,IAC7E,IAAM,EAAe,IAAW,MAAM,CAAC,AAAC,GAAS,CAAC,EAAK,QAAQ,EACzD,EAAc,EAAa,IAAI,CAAC,AAAC,GAAS,EAAK,KAAK,GAAK,EAAQ,KAAK,EACtE,EAAW,GAAa,EAAc,EAAQ,EACnC,MAAK,GAAG,CAArB,GACF,EAAQ,aAAa,CAAC,EAAS,KAAK,CAExC,GACM,EAAa,AAAC,IACb,IACH,EAAQ,MADO,MACK,EAAC,GACrB,KAEE,IACF,EAAQ,QADQ,gBACgB,CAAC,OAAO,CAAG,CACzC,EAAG,KAAK,KAAK,CAAC,EAAa,KAAK,EAChC,EAAG,KAAK,KAAK,CAAC,EAAa,KAAK,EAClC,CAEJ,EACA,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,IAA6B,CAAE,CAAE,SAAS,EAAM,GAAG,CAAW,CAAE,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAC/G,EAAA,EADyG,OAChG,CAAC,MAAM,CAChB,CACE,KAAM,SACN,KAAM,WACN,gBAAiB,EAAQ,SAAS,CAClC,gBAAiB,EAAQ,IAAI,CAC7B,gBAAiB,EAAQ,QAAQ,CACjC,oBAAqB,OACrB,IAAK,EAAQ,GAAG,CAChB,aAAc,EAAQ,IAAI,CAAG,OAAS,SACtC,SAAU,EACV,gBAAiB,EAAa,GAAK,KAAK,EACxC,mBAAoB,GAAsB,EAAQ,KAAK,EAAI,GAAK,KAAK,EACrE,GAAG,CAAY,CACf,IAAK,EACL,QAAS,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAa,OAAO,CAAE,AAAC,IACnD,EAAM,aAAa,CAAC,KAAK,GACM,SAAS,CAApC,EAAe,OAAO,EACxB,EAAW,EAEf,GACA,cAAe,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAa,aAAa,CAAG,AAAD,IAC9D,EAAe,OAAO,CAAG,EAAM,WAAW,CAC1C,IAAM,EAAS,EAAM,MAAM,AACvB,GAAO,iBAAiB,CAAC,EAAM,SAAS,GAAG,AAC7C,EAAO,qBAAqB,CAAC,EAAM,SAAS,EAEzB,IAAjB,EAAM,MAAM,GAA4B,IAAlB,EAAM,OAAO,EAAoC,SAAS,CAA/B,EAAM,WAAW,GACpE,EAAW,GACX,EAAM,cAAc,GAExB,GACA,UAAW,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAa,SAAS,CAAG,AAAD,IACtD,IAAM,EAAsC,KAAtB,EAAU,OAAO,AAEnC,CADkB,AACjB,EADuB,OAAO,EAAI,EAAM,MAAM,EAAI,EAAM,OAAO,EACzB,IAArB,EAAM,GAAG,CAAC,MAAM,EAAQ,EAAsB,EAAM,GAAG,IACzE,GAA+B,MAAd,EAAM,GAAG,AAAK,GAAK,AACpC,EAAU,QAAQ,CAAC,EAAM,GAAG,GAAG,CACjC,IACA,EAAM,cAAc,GAExB,EACF,EACA,EACJ,GAEF,EAAc,WAAW,CAAG,EAC5B,IAAI,EAAa,cACb,EAAc,EAAA,UAAgB,CAChC,CAAC,EAAO,KACN,GAAM,eAAE,CAAa,WAAE,CAAS,OAAE,CAAK,UAAE,CAAQ,aAAE,EAAc,EAAE,CAAE,GAAG,EAAY,CAAG,EACjF,EAAU,EAAiB,EAAY,GACvC,8BAAE,CAA4B,CAAE,CAAG,EACnC,EAA2B,KAAK,IAAlB,EACd,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,EAAQ,iBAAiB,EAI5E,MAHA,CAAA,AAGO,EAHP,EAAA,SAGoB,MAHpB,AAAe,EAAC,KACd,EAA6B,EAC/B,EAAG,CAAC,EAA8B,EAAY,EACvB,CAAA,EAAA,EAAA,GAAA,AAAG,EACxB,EAAA,SAAS,CAAC,IAAI,CACd,CACE,GAAG,CAAU,CACb,IAAK,EACL,MAAO,CAAE,cAAe,MAAO,EAC/B,SAAU,GAAsB,EAAQ,KAAK,EAAoB,CAAA,CAAhB,CAAgB,EAAA,GAAA,AAAG,EAAC,EAAA,GAAP,KAAe,CAAE,CAAE,SAAU,CAAY,GAAK,CAC9G,EAEJ,GAEF,EAAY,WAAW,CAAG,EAE1B,IAAI,EAAa,EAAA,UAAgB,CAC/B,CAAC,EAAO,KACN,GAAM,eAAE,CAAa,UAAE,CAAQ,CAAE,GAAG,EAAW,CAAG,EAClD,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,OAAgB,CAAC,IAAI,CAAE,CAAE,eAAe,EAAM,GAAG,CAAS,CAAE,IAAK,EAAc,SAAU,GAAY,GAAS,EACpI,EAEF,GAAW,WAAW,CAPN,EAOS,WAEzB,IAAI,EAAe,AAAC,GACK,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,MAAe,CAAE,CAAE,SAAS,EAAM,GAAG,CAAM,AAAD,GAEvE,EAAa,WAAW,CAJN,EAIS,aAC3B,IAAI,EAAe,gBACf,EAAgB,EAAA,UAAgB,CAClC,CAAC,EAAO,KACN,IAAM,EAAU,EAAiB,EAAc,EAAM,aAAa,EAC5D,CAAC,EAAU,EAAY,CAAG,EAAA,QAAc,SAI9C,CAHA,CAAA,EAGI,AAHJ,EAAA,eAAA,AAAe,EAAC,KACd,EAAY,IAAI,iBAClB,EAAG,EAAE,EACA,EAAQ,IAAI,EAOM,AAPJ,AAOI,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EAAmB,CAAE,GAAG,CAAK,CAAE,IAAK,CAAa,GALnE,EAAO,EAAA,YAAqB,CACjC,AAAgB,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EAAuB,CAA9B,AAAgC,MAAO,EAAM,aAAa,CAAE,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAW,EAAlB,EAAsB,CAAE,CAAE,MAAO,EAAM,aAAa,CAAE,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAAC,IAAP,EAAc,CAAE,SAAU,EAAM,QAAS,AAAD,EAAI,EAAG,GAFjN,CAGX,EACE,IAGR,GAEF,EAAc,WAAW,CAAG,EAE5B,GAAI,CAAC,EAAuB,EAAwB,CAAG,EAAoB,GAEvE,EAAO,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,8BAClB,EAAoB,EAAA,UAAgB,CACtC,CAAC,EAAO,KACN,GAAM,CACJ,eAAa,UACb,EAAW,cAAc,kBACzB,CAAgB,iBAChB,CAAe,sBACf,CAAoB,EACpB,EAAE,EAEF,CAAI,YACJ,CAAU,CACV,OAAK,aACL,CAAW,cACX,CAAY,CACZ,mBAAiB,kBACjB,CAAgB,QAChB,CAAM,kBACN,CAAgB,iBAChB,CAAe,CAEf,CADA,EAAE,AACC,EACJ,CAAG,EACE,EAAU,EAAiB,EAAc,GACzC,CAAC,EAAS,EAAW,CAAG,EAAA,QAAc,CAAC,MACvC,CAAC,EAAU,EAAY,CAAG,EAAA,QAAc,CAAC,MACzC,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,AAAC,GAAS,EAAW,IAClE,CAAC,EAAc,EAAgB,CAAG,EAAA,QAAc,CAAC,MACjD,CAAC,EAAkB,EAAoB,CAAG,EAAA,QAAc,CAC5D,MAEI,EAAW,EAAc,GACzB,CAAC,EAAc,EAAgB,CAAG,EAAA,QAAc,EAAC,GACjD,EAAyB,EAAA,MAAY,CAAC,IAC5C,EAAA,SAAe,CAAC,KACd,GAAI,EAAS,MAAO,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,EACjC,EAAG,CAAC,EAAQ,EACZ,CAAA,EAAA,EAAA,cAAA,AAAc,IACd,IAAM,EAAa,EAAA,WAAiB,CAClC,AAAC,IACC,GAAM,CAAC,EAAW,GAAG,EAAU,CAAG,IAAW,GAAG,CAAE,AAAD,GAAU,EAAK,GAAG,CAAC,OAAO,EACrE,CAAC,EAAS,CAAG,EAAU,KAAK,CAAC,CAAC,GAC9B,EAA6B,SAAS,aAAa,CACzD,IAAK,IAAM,KAAa,EACtB,GAAI,IAAc,EADgB,EAElC,GAAW,eAAe,CAAE,MAAO,SAAU,GACzC,IAAc,GAAa,IAAU,EAAS,SAAS,EAAG,EAC1D,IAAc,GAAY,GAAU,GAAS,SAAS,CAAG,EAAS,YAAY,AAAZ,EACtE,GAAW,QACP,SAAS,aAAa,GAAK,GALe,MAOlD,EACA,CAAC,EAAU,EAAS,EAEhB,EAAoB,EAAA,MALuC,KAKtB,CACzC,IAAM,EAAW,CAAC,EAAc,EAAQ,EACxC,CAAC,EAAY,EAAc,EAAQ,EAErC,EAAA,SAAe,CAAC,KACV,GACF,GAEJ,EAAG,CAAC,EAAc,EAAkB,CAHhB,CAIpB,GAAM,cAAE,CAAY,CAAE,0BAAwB,CAAE,CAAG,EACnD,EAAA,SAAe,CAAC,KACd,GAAI,EAAS,CACX,IAAI,EAAmB,CAAE,EAAG,EAAG,EAAG,CAAE,EAC9B,EAAoB,AAAC,IACzB,EAAmB,CACjB,EAAG,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,EAAM,KAAK,GAAK,CAAD,CAA0B,OAAO,EAAE,IAAK,CAAC,EAC/E,EAAG,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,EAAM,KAAK,GAAK,CAAD,CAA0B,OAAO,EAAE,IAAK,CAAC,CACjF,CACF,EACM,EAAkB,AAAC,IACnB,EAAiB,CAAC,EAAI,IAAM,EAAiB,CAAC,EAAI,GACpD,CADwD,CAClD,cAAc,GAEhB,AAAC,EAAQ,QAAQ,CAAC,EAAM,MAAM,GAAG,AACnC,GAAa,GAGjB,SAAS,mBAAmB,CAAC,cAAe,GAC5C,EAAyB,OAAO,CAAG,IACrC,EAKA,OAJyC,MAAM,CAA3C,EAAyB,OAAO,GAClC,SAAS,gBAAgB,CAAC,cAAe,GACzC,SAAS,gBAAgB,CAAC,YAAa,EAAiB,CAAE,SAAS,EAAM,KAAM,EAAK,IAE/E,KACL,SAAS,mBAAmB,CAAC,cAAe,GAC5C,SAAS,mBAAmB,CAAC,YAAa,EAAiB,CAAE,QAAS,EAAK,EAC7E,CACF,CACF,EAAG,CAAC,EAAS,EAAc,EAAyB,EACpD,EAAA,SAAe,CAAC,KACd,IAAM,EAAQ,IAAM,GAAa,GAGjC,OAFA,OAAO,gBAAgB,CAAC,OAAQ,GAChC,OAAO,gBAAgB,CAAC,SAAU,GAC3B,KACL,OAAO,mBAAmB,CAAC,OAAQ,GACnC,OAAO,mBAAmB,CAAC,SAAU,EACvC,CACF,EAAG,CAAC,EAAa,EACjB,GAAM,CAAC,EAAW,EAAsB,CAAG,GAAmB,AAAC,IAC7D,IAAM,EAAe,IAAW,MAAM,CAAC,AAAC,GAAS,CAAC,EAAK,QAAQ,EACzD,EAAc,EAAa,IAAI,CAAC,AAAC,GAAS,EAAK,GAAG,CAAC,OAAO,GAAK,SAAS,aAAa,EACrF,EAAW,GAAa,EAAc,EAAQ,GAChD,GACF,OADY,IACD,IAAM,EAAS,GAAG,CAAC,OAAO,CAAC,KAAK,GAE/C,GACM,GAAkB,EAAA,WAAiB,CACvC,CAAC,EAAM,EAAO,KACZ,IAAM,EAAmB,CAAC,EAAuB,OAAO,EAAI,CAAC,GACpB,AACrC,KAD0C,IAAvB,EAAQ,KAAK,EAAe,EAAQ,KAAK,GAAK,GAC/C,CAAA,GAAkB,CACtC,EAAgB,GACZ,IAAkB,EAAuB,OAAO,EAAG,CAAA,EAE3D,EACA,CAAC,EAAQ,KAAK,CAAC,EAEX,GAAkB,EAAA,WAAiB,CAAC,IAAM,GAAS,QAAS,CAAC,EAAQ,EACrE,GAAsB,EAAA,WAAiB,CAC3C,CAAC,EAAM,EAAO,KACZ,IAAM,EAAmB,CAAC,EAAuB,OAAO,EAAI,CAAC,GAEzD,AADqC,KAAK,IAAvB,EAAQ,KAAK,EAAe,EAAQ,KAAK,GAAK,GAC/C,CAAA,GAAkB,AACtC,EAAoB,EAExB,EACA,CAAC,EAAQ,KAAK,CAAC,EAEX,GAA8B,WAAb,EAAwB,EAAuB,EAChE,GAAqB,KAAmB,EAAuB,MACnE,aACA,QACA,cACA,eACA,oBACA,mBACA,SACA,mBACA,kBACA,CACF,EAAI,CAAC,EACL,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EACxB,EACA,CACE,CAHgB,KAGT,EACP,mBACA,EACA,iBAAkB,kBAClB,gBACA,EACA,YAAa,GACb,uBACA,oBACA,mBACA,wBACA,YACA,EACA,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,UAAmB,CAAE,CAAE,GAAI,EAAM,gBAAgB,EAAM,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EACzG,EAAA,EADmG,QACzF,CACV,CACE,SAAS,EACT,QAAS,EAAQ,IAAI,CACrB,iBAAkB,AAAC,IACjB,EAAM,cAAc,EACtB,EACA,mBAAoB,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAkB,AAAC,IAC1D,EAAQ,OAAO,EAAE,MAAM,CAAE,eAAe,CAAK,GAC7C,EAAM,cAAc,EACtB,GACA,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAC3B,EAAA,EADqB,cACL,CAChB,CACE,SAAS,EACT,6BAA6B,EAC7B,uCACA,EACA,eAAgB,AAAC,GAAU,EAAM,cAAc,GAC/C,UAAW,IAAM,EAAQ,YAAY,EAAC,GACtC,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAC3B,GACA,CAFqB,AAGnB,KAAM,UACN,GAAI,EAAQ,SAAS,CACrB,aAAc,EAAQ,IAAI,CAAG,OAAS,SACtC,IAAK,EAAQ,GAAG,CAChB,cAAe,AAAC,GAAU,EAAM,cAAc,GAC9C,GAAG,CAAY,CACf,GAAG,EAAkB,CACrB,SAAU,IAAM,GAAgB,GAChC,IAAK,EACL,MAAO,CAEL,QAAS,OACT,cAAe,SAEf,QAAS,OACT,GAAG,EAAa,KAAK,AACvB,EACA,UAAW,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAa,SAAS,CAAE,AAAC,IACvD,IAAM,EAAgB,EAAM,OAAO,EAAI,EAAM,MAAM,EAAI,EAAM,OAAO,CAGpE,GAFkB,QAAd,EAAM,GAAG,EAAY,EAAM,cAAc,GACzC,AAAC,GAAsC,IAArB,EAAM,GAAG,CAAC,MAAM,EAAQ,EAAsB,EAAM,GAAG,EACzE,CAAC,UAAW,YAAa,OAAQ,MAAM,CAAC,QAAQ,CAAC,EAAM,GAAG,EAAG,CAE/D,IAAI,EADU,AACO,IADI,MAAM,CAAE,AAAD,GAAU,CAAC,EAAK,QAAQ,EAC7B,GAAG,CAAC,AAAC,GAAS,EAAK,GAAG,CAAC,OAAO,EAIzD,GAHI,CAAC,UAAW,MAAM,CAAC,QAAQ,CAAC,EAAM,GAAG,GAAG,CAC1C,EAAiB,EAAe,KAAK,GAAG,OAAO,EAAA,EAE7C,CAAC,UAAW,YAAY,CAAC,QAAQ,CAAC,EAAM,GAAG,EAAG,CAChD,IAAM,EAAiB,EAAM,MAAM,CAC7B,EAAe,EAAe,OAAO,CAAC,GAC5C,EAAiB,EAAe,KAAK,CAAC,EAAe,EACvD,CACA,WAAW,IAAM,EAAW,IAC5B,EAAM,cAAc,EACtB,CACF,EACF,EAEJ,EAEJ,EACA,EACJ,EAEJ,EAEF,GAAkB,WAAW,CA1OL,EA0OQ,kBAEhC,IAAI,EAA4B,EAAA,UAAgB,CAAC,CAAC,EAAO,KACvD,GAAM,eAAE,CAAa,UAAE,CAAQ,CAAE,GAAG,EAAa,CAAG,EAC9C,EAAU,EAAiB,EAAc,GACzC,EAAiB,EAAwB,EAAc,GACvD,CAAC,EAAgB,EAAkB,CAAG,EAAA,QAAc,CAAC,MACrD,CAAC,EAAS,EAAW,CAAG,EAAA,QAAc,CAAC,MACvC,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,AAAC,GAAS,EAAW,IAClE,EAAW,EAAc,GACzB,EAA0B,EAAA,MAAY,EAAC,GACvC,EAAsB,EAAA,MAAY,EAAC,GACnC,UAAE,CAAQ,cAAE,CAAY,CAAE,kBAAgB,mBAAE,CAAiB,CAAE,CAAG,EAClE,EAAW,EAAA,WAAiB,CAAC,KACjC,GAAI,EAAQ,OAAO,EAAI,EAAQ,SAAS,EAAI,GAAkB,GAAW,GAAY,GAAgB,EAAkB,CACrH,IAAM,EAAc,EAAQ,OAAO,CAAC,qBAAqB,GACnD,EAAc,EAAQ,qBAAqB,GAC3C,EAAgB,EAAQ,SAAS,CAAC,qBAAqB,GACvD,EAAe,EAAiB,qBAAqB,GAC3D,GAAoB,QAAhB,EAAQ,GAAG,CAAY,CACzB,IAAM,EAAiB,EAAa,IAAI,CAAG,EAAY,IAAI,CACrD,EAAO,EAAc,IAAI,CAAG,EAC5B,EAAY,EAAY,IAAI,CAAG,EAC/B,EAAkB,EAAY,KAAK,CAAG,EACtC,EAAe,KAAK,GAAG,CAAC,EAAiB,EAAY,KAAK,EAE1D,EAAc,EAAM,EAAM,IAO9B,KAAK,GAAG,CAAC,GARO,AAQS,OARF,UAAU,GAAG,CAQC,GACtC,EACD,EAAe,KAAK,CAAC,QAAQ,CAAG,EAAkB,KAClD,EAAe,KAAK,CAAC,IAAI,CAAG,EAAc,IAC5C,KAAO,CACL,IAAM,EAAiB,EAAY,KAAK,CAAG,EAAa,KAAK,CACvD,EAAQ,OAAO,UAAU,CAAG,EAAc,KAAK,CAAG,EAClD,EAAa,OAAO,UAAU,CAAG,EAAY,KAAK,CAAG,EACrD,EAAkB,EAAY,KAAK,CAAG,EACtC,EAAe,KAAK,GAAG,CAAC,EAAiB,EAAY,KAAK,EAE1D,EAAe,EAAM,EAAO,IAEhC,KAAK,GAAG,CAAC,GAAgB,AAHV,OAAO,UAAU,GAAG,CAGC,GACrC,EACD,EAAe,KAAK,CAAC,QAAQ,CAAG,EAAkB,KAClD,EAAe,KAAK,CAAC,KAAK,CAAG,EAAe,IAC9C,CACA,IAAM,EAAQ,IACR,EAAkB,OAAO,WAAW,CAAG,GACvC,EAAc,EAAS,UADiC,EACrB,CACnC,EAAgB,OAAO,gBAAgB,CAAC,GACxC,EAAwB,SAAS,EAAc,cAAc,CAAE,IAC/D,EAAoB,SAAS,EAAc,UAAU,CAAE,IACvD,EAA2B,SAAS,EAAc,iBAAiB,CAAE,IAErE,EAAoB,EAAwB,EAAoB,EADzC,SAAS,EAAc,CACgC,YADnB,CAAE,IACwC,EACrG,EAAmB,KAAK,GAAG,CAA6B,EAA5B,EAAa,YAAY,CAAM,GAC3D,EAAiB,OAAO,gBAAgB,CAAC,GACzC,EAAqB,SAAS,EAAe,UAAU,CAAE,IACzD,EAAwB,SAAS,EAAe,aAAa,CAAE,IAC/D,EAAyB,EAAY,GAAG,CAAG,EAAY,MAAM,CAAG,IAAI,CAEpE,EAAyB,EAAa,YAAY,CAAG,EAErD,EAAyB,EAAwB,GAD9B,EAAa,SAAS,CAAG,CAAA,EAIlD,EAH2E,CAEvC,CAChC,EAD0D,EAC7B,CAC/B,IAAM,EAAa,EAAM,MAAM,CAAG,GAAK,IAAiB,CAAK,CAAC,EAAM,MAAM,CAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAC3F,EAAe,KAAK,CAAC,MAAM,CAAG,MAE9B,IAAM,EAAmC,KAAK,GAAG,CAVjB,AAW9B,EAXgD,EAYhD,GACC,EAAa,GAAwB,CAAC,CAJZ,GAAQ,AAIQ,YADlB,AAHsB,CAAG,EAAS,SAAS,CAAG,EAAS,YAAA,AAAY,EAI1B,GAGpE,EAAe,KAAK,CAAC,MAAM,CADZ,AACe,EADU,EACD,IACzC,KAAO,CACL,IAAM,EAAc,EAAM,MAAM,CAAG,GAAK,CANmE,GAMlD,CAAK,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAC7E,EAAe,KAAK,CAAC,GAAG,CAAG,MAC3B,IAAM,EAAgC,KAAK,GAAG,CAC5C,EACA,EAAwB,EAAS,SAAS,CACzC,EAD4C,CAC9B,GAAqB,CAAC,CAAI,GAG3C,EAAe,KAAK,CAAC,MAAM,CADZ,AACe,EAtBE,GAAoB,CAAA,EAsBb,KACvC,EAAS,SAAS,CAAG,EAAyB,EAAyB,EAAS,CAFjC,QAE0C,AAC3F,CACA,EAAe,KAAK,CAAC,IAPyG,EAOnG,CAAG,GAAG,MACjC,EAAe,KAAK,CAAC,CAD2B,IAAI,CAAC,GACvB,CAAG,EAAmB,KACpD,EAAe,KAAK,CAAC,SAAS,CAAG,EAAkB,KACnD,MACA,sBAAsB,IAAM,EAAwB,OAAO,EAAG,EAChE,CACF,EAAG,CACD,EACA,EAAQ,OAAO,CACf,EAAQ,SAAS,CACjB,EACA,EACA,EACA,EACA,EACA,EAAQ,GAAG,CACX,EACD,EACD,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,IAAM,IAAY,CAAC,EAAS,EAC5C,GAAM,CAAC,EAAe,EAAiB,CAAG,EAAA,QAAc,GACxD,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,KACV,GAAS,EAAiB,OAAO,gBAAgB,CAAC,GAAS,MAAM,CACvE,EAAG,CAAC,EAAQ,EACZ,IAAM,EAA2B,EAAA,WAAiB,CAChD,AAAC,IACK,GAAQ,AAAgC,MAAM,CAAlB,OAAO,GACrC,IACA,MACA,EAAoB,OAAO,EAAG,EAElC,EACA,CAAC,EAAU,EAAkB,EAE/B,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EACxB,EACA,CACE,CAHgB,KAGT,iBACP,0BACA,EACA,qBAAsB,EACtB,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAC3B,IADqB,EAErB,CACE,IAAK,EACL,MAAO,CACL,QAAS,OACT,cAAe,SACf,SAAU,QACV,OAAQ,CACV,EACA,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAC3B,EAAA,EADqB,OACZ,CAAC,GAAG,CACb,CACE,GAAG,CAAW,CACd,IAAK,EACL,MAAO,CAGL,UAAW,aAEX,UAAW,OACX,GAAG,EAAY,KAAK,AACtB,CACF,EAEJ,EAEJ,EAEJ,GACA,EAA0B,WAAW,CAnKJ,EAmKO,0BAExC,IAAI,EAAuB,EAAA,UAAgB,CAAC,CAAC,EAAO,KAClD,GAAM,eACJ,CAAa,OACb,EAAQ,OAAO,kBACf,EAtZiB,EAsZgB,CACjC,GAAG,EACJ,CAAG,EACE,EAAc,EAAe,EAHd,CAIrB,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EACxB,EAAA,EADkB,KACK,CACvB,CACE,GAAG,CAAW,CACd,GAAG,CAAW,CACd,IAAK,QACL,mBACA,EACA,MAAO,CAEL,UAAW,aACX,GAAG,EAAY,KAAK,CAGlB,0CAA2C,uCAC3C,yCAA0C,sCAC1C,0CAA2C,uCAC3C,+BAAgC,mCAChC,gCAAiC,mCAErC,CACF,EAEJ,GACA,EAAqB,WAAW,CAjCL,EAiCQ,qBACnC,GAAI,CAAC,EAAwB,EAAyB,CAAG,EAAoB,EAAc,CAAC,GACxF,GAAgB,iBAChB,GAAiB,EAAA,UAAgB,CACnC,CAAC,EAAO,KACN,GAAM,eAAE,CAAa,OAAE,CAAK,CAAE,GAAG,EAAe,CAAG,EAC7C,EAAiB,EAAwB,GAAe,GACxD,EAAkB,EAAyB,GAAe,GAC1D,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,EAAe,gBAAgB,EAC5E,EAAmB,EAAA,MAAY,CAAC,GACtC,MAAuB,CAAhB,AAAgB,EAAA,EAAA,IAAA,AAAI,EAAC,EAAA,CAAR,OAAgB,CAAE,CAAE,SAAU,CAChC,CAAA,EAAA,EAAA,GAAA,AAAG,EACjB,QACA,CACE,wBAAyB,CACvB,OAAQ,CAAC,yKAAyK,CAAC,AACrL,QACA,CACF,GAEc,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EAAW,IAAI,CAAE,CAAE,MAAO,EAAe,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EACxF,EAAA,EADkF,OACzE,CAAC,GAAG,CACb,CACE,6BAA8B,GAC9B,KAAM,eACN,GAAG,CAAa,CAChB,IAAK,EACL,MAAO,CAIL,SAAU,WACV,KAAM,EAKN,SAAU,cACV,GAAG,EAAc,KAAK,AACxB,EACA,SAAU,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAc,QAAQ,CAAE,AAAC,IACtD,IAAM,EAAW,EAAM,aAAa,CAC9B,gBAAE,CAAc,yBAAE,CAAuB,CAAE,CAAG,EACpD,GAAI,GAAyB,SAAW,EAAgB,CACtD,IAAM,EAAa,KAAK,GAAG,CAAC,EAAiB,OAAO,CAAG,EAAS,SAAS,EACzE,GAAI,EAAa,EAAG,CAClB,IAAM,EAAkB,OAAO,WAAW,CAAG,GAGvC,EAAa,KAAK,GAAG,CAAC,AAFP,GADyC,QAC9B,EAAe,CAEL,IAFU,CAAC,SAAS,EAC5C,WAAW,EAAe,KAAK,CAAC,MAAM,GAExD,GAAI,EAAa,EAAiB,CAChC,IAAM,EAAa,EAAa,EAC1B,EAAoB,KAAK,GAAG,CAAC,EAAiB,GAC9C,EAAa,EAAa,EAChC,EAAe,KAAK,CAAC,MAAM,CAAG,EAAoB,KACd,OAAO,CAAvC,EAAe,KAAK,CAAC,MAAM,GAC7B,EAAS,SAAS,CAAG,EAAa,EAAI,EAAa,EACnD,EAAe,KAAK,CAAC,cAAc,CAAG,WAE1C,CACF,CACF,CACA,EAAiB,OAAO,CAAG,EAAS,SACtC,AAD+C,EAEjD,EACA,GACH,AAAC,EACJ,EAEF,IAAe,WAAW,CAAG,GAC7B,IAAI,GAAa,cACb,CAAC,GAA4B,GAAsB,CAAG,EAAoB,GAQ9E,CAPkB,EAAA,UAAgB,CAChC,CAAC,EAAO,KACN,GAAM,eAAE,CAAa,CAAE,GAAG,EAAY,CAAG,EACnC,EAAU,CAAA,EAAA,EAAA,KAAA,AAAK,IACrB,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,GAA4B,CAAnC,AAAqC,MAAO,EAAe,GAAI,EAAS,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,OAAgB,CAAC,GAAG,CAAE,CAAE,KAAM,QAAS,kBAAmB,EAAS,GAAG,CAAU,CAAE,IAAK,CAAa,EAAG,EAC5N,GAEU,WAAW,CAAG,GAC1B,IAAI,GAAa,aAQjB,CAPkB,EAAA,UAAgB,CAChC,CAAC,EAAO,KACN,GAAM,eAAE,CAAa,CAAE,GAAG,EAAY,CAAG,EACnC,EAAe,GAAsB,GAAY,GACvD,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,OAAgB,CAAC,GAAG,CAAE,CAAE,GAAI,EAAa,EAAE,CAAE,GAAG,CAAU,CAAE,IAAK,CAAa,EACpG,GAEU,WAAW,CAAG,GAC1B,IAAI,GAAY,aACZ,CAAC,GAA2B,GAAqB,CAAG,EAAoB,IACxE,GAAa,EAAA,UAAgB,CAC/B,CAAC,EAAO,KACN,GAAM,eACJ,CAAa,OACb,CAAK,UACL,GAAW,CAAK,CAChB,UAAW,CAAa,CACxB,GAAG,EACJ,CAAG,EACE,EAAU,EAAiB,GAAW,GACtC,EAAiB,EAAwB,GAAW,GACpD,EAAa,EAAQ,KAAK,GAAK,EAC/B,CAAC,EAAW,EAAa,CAAG,EAAA,QAAc,CAAC,GAAiB,IAC5D,CAAC,EAAW,EAAa,CAAG,EAAA,QAAc,EAAC,GAC3C,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAClC,EACA,AAAC,GAAS,EAAe,eAAe,GAAG,EAAM,EAAO,IAEpD,EAAS,CAAA,EAAA,EAAA,KAAA,AAAK,IACd,EAAiB,EAAA,MAAY,CAAC,SAC9B,EAAe,KACd,IACH,EAAQ,IADK,SACQ,CAAC,GACtB,EAAQ,YAAY,EAAC,GAEzB,EACA,GAAI,AAAU,IAAI,GAChB,MAAM,AAAI,MACR,yLAGJ,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EACxB,GACA,CAFkB,AAGhB,MAAO,EACP,iBACA,SACA,aACA,EACA,iBAAkB,EAAA,WAAiB,CAAE,AAAD,IAClC,EAAa,AAAC,GAAkB,GAAiB,CAAC,GAAM,aAAe,EAAA,CAAE,CAAE,IAAI,GACjF,EAAG,EAAE,EACL,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAC3B,EAAW,EADU,MACF,CACnB,CACE,MAAO,QACP,WACA,YACA,EACA,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAC3B,EAAA,EADqB,OACZ,CAAC,GAAG,CACb,CACE,KAAM,SACN,kBAAmB,EACnB,mBAAoB,EAAY,GAAK,KAAK,EAC1C,gBAAiB,GAAc,EAC/B,aAAc,EAAa,UAAY,YACvC,gBAAiB,GAAY,KAAK,EAClC,gBAAiB,EAAW,GAAK,KAAK,EACtC,SAAU,EAAW,KAAK,EAAI,CAAC,EAC/B,GAAG,CAAS,CACZ,IAAK,EACL,QAAS,CAAA,EAAA,EAAA,oBAAoB,AAApB,EAAqB,EAAU,OAAO,CAAE,IAAM,GAAa,IACpE,OAAQ,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAU,MAAM,CAAE,IAAM,GAAa,IAClE,QAAS,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAU,OAAO,CAAE,KAC3C,AAA2B,YAAZ,OAAO,EAAc,GAC1C,GACA,YAAa,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAU,WAAW,CAAE,KACnD,AAA2B,YAAZ,OAAO,EAAc,GAC1C,GACA,cAAe,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAU,aAAa,CAAE,AAAC,IAC5D,EAAe,OAAO,CAAG,EAAM,WAAW,AAC5C,GACA,cAAe,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAU,aAAa,CAAG,AAAD,IAC3D,EAAe,OAAO,CAAG,EAAM,WAAW,CACtC,EACF,EAAe,MADH,KACc,KACU,SAAS,CAApC,EAAe,OAAO,EAC/B,EAAM,aAAa,CAAC,KAAK,CAAC,CAAE,eAAe,CAAK,EAEpD,GACA,eAAgB,CAAA,EAAA,EAAA,oBAAoB,AAApB,EAAqB,EAAU,cAAc,CAAE,AAAC,IAC1D,EAAM,aAAa,GAAK,SAAS,aAAa,EAAE,AAClD,EAAe,WAAW,IAE9B,GACA,UAAW,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAU,SAAS,CAAE,AAAC,KAC9B,AAClB,EADiC,SAAS,EAAE,UAAY,IACzB,MAAd,EAAM,GAAG,AAAK,GAAK,CACpC,EAAe,QAAQ,CAAC,EAAM,GAAG,GAAG,IACtB,MAAd,EAAM,GAAG,EAAU,EAAM,cAAc,GAC7C,EACF,EAEJ,EAEJ,EAEJ,GAEF,GAAW,WAAW,CAAG,GACzB,IAAI,GAAiB,iBACjB,GAAiB,EAAA,UAAgB,CACnC,CAAC,EAAO,KACN,GAAM,eAAE,CAAa,WAAE,CAAS,CAAE,OAAK,CAAE,GAAG,EAAe,CAAG,EACxD,EAAU,EAAiB,GAAgB,GAC3C,EAAiB,EAAwB,GAAgB,GACzD,EAAc,GAAqB,GAAgB,GACnD,EAAuB,EAA8B,GAAgB,GACrE,CAAC,EAAc,EAAgB,CAAG,EAAA,QAAc,CAAC,MACjD,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAClC,EACA,AAAC,GAAS,EAAgB,GAC1B,EAAY,gBAAgB,CAC5B,AAAC,GAAS,EAAe,mBAAmB,GAAG,EAAM,EAAY,KAAK,CAAE,EAAY,QAAQ,GAExF,EAAc,GAAc,YAC5B,EAAe,EAAA,OAAa,CAChC,IAAsB,AAAhB,CAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,GAAP,MAAiB,CAAE,MAAO,EAAY,KAAK,CAAE,SAAU,EAAY,QAAQ,CAAE,SAAU,CAAY,EAAG,EAAY,KAAK,EAC1I,CAAC,EAAY,QAAQ,CAAE,EAAY,KAAK,CAAE,EAAY,EAElD,CAAE,mBAAiB,sBAAE,CAAoB,CAAE,CAAG,EAKpD,MAJA,CAAA,AAIO,EAJP,EAAA,SAIoB,MAJpB,AAAe,EAAC,KACd,EAAkB,GACX,IAAM,EAAqB,IACjC,CAAC,EAAmB,EAAsB,EAAa,EACnC,CAAA,EAAA,EAAA,IAAA,AAAI,EAAC,EAAA,QAAQ,CAAE,CAAE,SAAU,CAChC,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,SAAS,CAAC,IAAI,CAAE,CAAE,GAAI,EAAY,MAAM,CAAE,GAAG,CAAa,CAAE,IAAK,CAAa,GAClG,EAAY,UAAU,EAAI,EAAQ,SAAS,EAAI,CAAC,EAAQ,oBAAoB,CAAG,EAAA,YAAqB,CAAC,EAAc,QAAQ,CAAE,EAAQ,SAAS,EAAI,KAClJ,AAAD,EACH,GAEF,GAAe,WAAW,CAAG,GAC7B,IAAI,GAAsB,sBACtB,GAAsB,EAAA,UAAgB,CACxC,CAAC,EAAO,KACN,GAAM,CAAE,eAAa,CAAE,GAAG,EAAoB,CAAG,EAEjD,OADoB,AACb,GADkC,GAAqB,GAC3C,UAAU,CAAmB,CAAA,CAAhB,CAAgB,EAAA,GAAA,AAAG,EAAC,EAAA,GAAP,MAAgB,CAAC,IAAI,CAAE,CAAE,cAAe,GAAM,GAAG,CAAkB,CAAE,IAAK,CAAa,GAAK,IAC3I,GAEF,GAAoB,WAAW,CAAG,GAClC,IAAI,GAAwB,uBACxB,GAAuB,EAAA,UAAgB,CAAC,CAAC,EAAO,KAClD,IAAM,EAAiB,EAAwB,GAAuB,EAAM,aAAa,EACnF,EAAkB,EAAyB,GAAuB,EAAM,aAAa,EACrF,CAAC,EAAa,EAAe,CAAG,EAAA,QAAc,EAAC,GAC/C,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,EAAgB,oBAAoB,EAcvF,MAbA,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,KACd,GAAI,EAAe,QAAQ,EAAI,EAAe,YAAY,CAAE,CAC1D,IAAI,EAAgB,WAElB,EADqB,EAAS,SAAS,CAAG,CAC3B,CACjB,EAEM,EAAW,EAAe,QAAQ,CAGxC,OAFA,IACA,EAAS,gBAAgB,CAAC,SAAU,GAC7B,IAAM,EAAS,mBAAmB,CAAC,SAAU,EACtD,CACF,EAAG,CAAC,EAAe,QAAQ,CAAE,EAAe,YAAY,CAAC,EAClD,EAA8B,CAAA,EAAA,EAAA,GAAA,AAAG,EACtC,EADmB,CAEnB,CACE,GAAG,CAAK,CACR,IAAK,EACL,AAL8B,aAKhB,KACZ,GAAM,UAAE,CAAQ,cAAE,CAAY,CAAE,CAAG,CAC/B,IAAY,IACd,EAAS,QADmB,CACV,CAAG,EAAS,SAAS,CAAG,EAAa,YAAA,AAAY,CAEvE,CACF,GACE,IACN,GACA,GAAqB,WAAW,CAAG,GACnC,IAAI,GAA0B,yBAC1B,GAAyB,EAAA,UAAgB,CAAC,CAAC,EAAO,KACpD,IAAM,EAAiB,EAAwB,GAAyB,EAAM,aAAa,EACrF,EAAkB,EAAyB,GAAyB,EAAM,aAAa,EACvF,CAAC,EAAe,EAAiB,CAAG,EAAA,QAAc,EAAC,GACnD,EAAe,CAAA,EAAA,EAAA,eAAe,AAAf,EAAgB,EAAc,EAAgB,oBAAoB,EAevF,MAdA,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,KACd,GAAI,EAAe,QAAQ,EAAI,EAAe,YAAY,CAAE,CAC1D,IAAI,EAAgB,WAClB,IAAM,EAAY,EAAS,YAAY,CAAG,EAAS,YAAY,CAE/D,EADuB,KAAK,IAAI,CAAC,EAAS,GACzB,MADkC,EAAI,EAEzD,EAEM,EAAW,EAAe,QAAQ,CAGxC,OAFA,IACA,EAAS,gBAAgB,CAAC,SAAU,GAC7B,IAAM,EAAS,mBAAmB,CAAC,SAAU,EACtD,CACF,EAAG,CAAC,EAAe,QAAQ,CAAE,EAAe,YAAY,CAAC,EAClD,EAAgC,CAAA,EAAA,EAAA,GAAA,AAAG,EACxC,GACA,CAFqB,AAGnB,GAAG,CAAK,CACR,IAAK,EACL,EALgC,WAKlB,KACZ,GAAM,CAAE,UAAQ,cAAE,CAAY,CAAE,CAAG,EAC/B,GAAY,IACd,EAAS,QADmB,CACV,CAAG,EAAS,SAAS,CAAG,EAAa,YAAA,AAAY,CAEvE,CACF,GACE,IACN,GACA,GAAuB,WAAW,CAAG,GACrC,IAAI,GAAyB,EAAA,UAAgB,CAAC,CAAC,EAAO,KACpD,GAAM,CAAE,eAAa,cAAE,CAAY,CAAE,GAAG,EAAsB,CAAG,EAC3D,EAAiB,EAAwB,qBAAsB,GAC/D,EAAqB,EAAA,MAAY,CAAC,MAClC,EAAW,EAAc,GACzB,EAAuB,EAAA,WAAiB,CAAC,KACV,MAAM,CAArC,EAAmB,OAAO,GAC5B,OAAO,aAAa,CAAC,EAAmB,OAAO,EAC/C,EAAmB,OAAO,CAAG,KAEjC,EAAG,EAAE,EAQL,OAPA,AAOO,EAPP,SAAe,CAAC,CAOI,GANX,IAAM,IACZ,CAAC,EAAqB,EACzB,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,KACd,IAAM,EAAa,IAAW,IAAI,CAAC,AAAC,GAAS,EAAK,GAAG,CAAC,OAAO,GAAK,SAAS,aAAa,EACxF,GAAY,IAAI,SAAS,eAAe,CAAE,MAAO,SAAU,EAC7D,EAAG,CAAC,EAAS,EACU,CAAA,EAAA,EAAA,GAAA,AAAG,EACxB,EAAA,SAAS,CAAC,GAAG,CACb,CACE,eAAe,EACf,GAAG,CAAoB,CACvB,IAAK,EACL,MAAO,CAAE,WAAY,EAAG,GAAG,EAAqB,KAAK,AAAC,EACtD,cAAe,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAqB,aAAa,CAAE,KACnC,MAAM,CAArC,EAAmB,OAAO,GAC5B,EAAmB,OAAO,CAAG,OAAO,WAAW,CAAC,EAAc,GAAA,CAElE,GACA,cAAe,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAqB,aAAa,CAAE,KACtE,EAAe,WAAW,KACS,MAAM,CAArC,EAAmB,OAAO,GAC5B,EAAmB,OAAO,CAAG,OAAO,WAAW,CAAC,EAAc,GAAA,CAElE,GACA,eAAgB,CAAA,EAAA,EAAA,oBAAA,AAAoB,EAAC,EAAqB,cAAc,CAAE,KACxE,GACF,EACF,EAEJ,EAQA,CANsB,EAAA,UAAgB,CACpC,CAAC,EAAO,KACN,GAAM,eAAE,CAAa,CAAE,GAAG,EAAgB,CAAG,EAC7C,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,OAAgB,CAAC,GAAG,CAAE,CAAE,cAAe,GAAM,GAAG,CAAc,CAAE,IAAK,CAAa,EACxG,GAEc,WAAW,CAPN,EAOS,gBAC9B,IAAI,GAAa,aAUjB,CATkB,EAAA,UAAgB,CAChC,CAAC,EAAO,KACN,GAAM,eAAE,CAAa,CAAE,GAAG,EAAY,CAAG,EACnC,EAAc,EAAe,GAC7B,EAAU,EAAiB,GAAY,GACvC,EAAiB,EAAwB,GAAY,GAC3D,OAAO,EAAQ,IAAI,EAAgC,WAA5B,AAAuC,EAAxB,QAAQ,CAAgC,CAAA,CAAH,CAAG,EAAA,GAAA,AAAG,EAAC,EAAA,KAAqB,CAAE,CAAE,GAAG,CAAW,CAAE,GAAG,CAAU,CAAE,IAAK,CAAa,GAAK,IACnK,GAEU,WAAW,CAAG,GAE1B,IAAI,GAAoB,EAAA,UAAgB,CACtC,CAAC,eAAE,CAAa,OAAE,CAAK,CAAE,GAAG,EAAO,CAAE,KACnC,IAAM,EAAM,EAAA,MAAY,CAAC,MACnB,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,GAC7C,EG/iCV,AH+iCsB,SG/iCb,AAAY,CAAK,EACxB,IAAM,EAAM,EAAA,MAAY,CAAC,OAAE,EAAO,SAAU,CAAM,GAClD,OAAO,EAAA,OAAa,CAAC,KACf,EAAI,OAAO,CAAC,KAAK,GAAK,IACxB,EAAI,CAD2B,MACpB,CAAC,QAAQ,CAAG,EAAI,OAAO,CAAC,KAAK,CACxC,EAAI,OAAO,CAAC,KAAK,CAAG,GAEf,EAAI,OAAO,CAAC,QAAQ,EAC1B,CAAC,EAAM,CACZ,EHsiCkC,GAgB9B,OAfA,AAeO,EAfP,SAAe,CAAC,CAeI,IAdlB,IAAM,EAAS,EAAI,OAAO,CAC1B,GAAI,CAAC,EAAQ,OAMb,IAAM,EAJa,AAIF,OAJS,wBAAwB,CAD9B,AAElB,OAFyB,iBAAiB,CAAC,SAAS,CAGpD,SAE0B,GAAG,CAC/B,GAAI,IAAc,GAAS,EAAU,CACnC,IAAM,EAAQ,IAAI,MAAM,SAAU,CAAE,SAAS,CAAK,GAClD,EAAS,IAAI,CAAC,EAAQ,GACtB,EAAO,aAAa,CAAC,EACvB,CACF,EAAG,CAAC,EAAW,EAAM,EACE,CAAA,EAAA,EAAA,GAAA,AAAG,EACxB,EAAA,SAAS,CAAC,MAAM,CAChB,CACE,GAAG,CAAK,CACR,MAAO,CAAE,GAAG,EAAA,sBAAsB,CAAE,GAAG,EAAM,KAAK,AAAC,EACnD,IAAK,EACL,aAAc,CAChB,EAEJ,GAGF,SAAS,GAAsB,CAAK,EAClC,MAAiB,KAAV,GAAgB,AAAU,KAAK,KACxC,CACA,SAAS,GAAmB,CAAc,EACxC,IAAM,EAAqB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,GACpC,EAAY,EAAA,MAAY,CAAC,IACzB,EAAW,EAAA,MAAY,CAAC,GACxB,EAAwB,EAAA,WAAiB,CAC7C,AAAC,IACC,IAAM,EAAS,EAAU,OAAO,CAAG,EACnC,EAAmB,GACnB,AAAC,SAAS,EAAa,CAAK,EAC1B,EAAU,OAAO,CAAG,EACpB,OAAO,YAAY,CAAC,EAAS,OAAO,EACtB,KAAV,IAAc,EAAS,OAAO,CAAG,OAAO,UAAU,CAAC,IAAM,EAAa,IAAK,IAAA,CACjF,CAAC,CAAE,EACL,EACA,CAAC,EAAmB,EAEhB,EAAiB,EAAA,WAAiB,CAAC,KACvC,EAAU,OAAO,CAAG,GACpB,OAAO,YAAY,CAAC,EAAS,OAAO,CACtC,EAAG,EAAE,EAIL,OAHA,EAAA,SAAe,CAAC,IACP,IAAM,OAAO,YAAY,CAAC,EAAS,OAAO,EAChD,EAAE,EACE,CAAC,EAAW,EAAuB,EAAe,AAC3D,CACA,SAAS,GAAa,CAAK,CAAE,CAAM,CAAE,CAAW,MAY7B,IAVjB,CAUsB,GAVhB,EADa,AACM,EADC,MAAM,CAAG,GAAK,MAAM,IAAI,CAAC,GAAQ,KAAK,CAAC,AAAC,GAAS,IAAS,CAAM,CAAC,EAAE,EACvD,CAAM,CAAC,EAAE,CAAG,EAC5C,EAAmB,EAAc,EAAM,OAAO,CAAC,GAAe,CAAC,EACjE,KAAyB,EAQL,EARY,KAAK,CAAtB,EAAyB,AAQV,CARW,EAAkB,GASxD,EAAM,GAAG,CAAC,CAAC,EAAG,IAAU,CAAK,CAAC,CAAC,EAAa,CAAA,CAAK,CAAI,EAAM,MAAM,CAAC,EAPrE,CADmD,IAA5B,EAAiB,MAAM,GAC1B,EAAe,EAAa,MAAM,CAAE,AAAD,GAAO,IAAM,EAAA,EACxE,IAAM,EAAW,EAAa,IAAI,CAChC,AAAC,GAAS,EAAK,SAAS,CAAC,WAAW,GAAG,UAAU,CAAC,EAAiB,WAAW,KAEhF,OAAO,IAAa,EAAc,EAAW,KAAK,CACpD,CAxCA,GAAkB,WAAW,CAhCL,EAgCQ,kBCxkChC,IAAA,GAAA,EAAA,CAAA,CAAA,wDIDoC,CAaiB,CAAA,ADAJ,CAAA,ACAI,CAAA,ADAJ,CCAI,ADAJ,CAAA,ACAI,CAAA,ADAM,CCAN,ADAM,CCAN,CAAA,CAAU,CAAA,gCDAzD,GETG,ACOE,CAAA,CAAA,CHEO,CCAZ,ADAY,EAAA,OAAA,EAAiB,aCbtB,CDaoC,CAAA,ACAZ,CAAA,ADAY,0BAboB,CCAF,ADAE,CCAF,ADAE,EAAK,CCAF,ADAE,CAAA,ACAF,CAAA,ADAE,CCAF,ADAE,CCAF,ADAE,CAAA,ACAF,IDAa,CAAA,EHGrF,IAAA,GAAA,EAAA,CAAA,CAAA,OAEA,SAAS,GAAO,CACd,GAAG,EAC+C,EAClD,MAAO,CAAA,EAAA,EAAA,GAAA,EAAC,AD6mCE,EC7mCF,CAAqB,YAAU,SAAU,GAAG,CAAK,EAC3D,CAQA,SAAS,GAAY,CACnB,GAAG,EACgD,EACnD,MAAO,CAAA,EAAA,EAAA,GAAA,EAAC,ADmmCE,ECnmCF,CAAsB,YAAU,eAAgB,GAAG,CAAK,EAClE,CAEA,SAAS,GAAc,WACrB,CAAS,CACT,OAAO,SAAS,UAChB,CAAQ,CACR,GAAG,EAGJ,EACC,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,ADslCS,ECtlCT,CACC,YAAU,iBACV,YAAW,EACX,UAAW,CAAA,EAAA,GAAA,EAAA,AAAE,EACX,+yBACA,GAED,GAAG,CAAK,WAER,EACD,CAAA,EAAA,EAAA,GAAA,EAAC,AD8kCI,EC9kCJ,CAAqB,OAAO,CAAA,CAAA,WAC3B,CAAA,EAAA,EAAA,GAAA,EAAC,GAAA,CAAgB,UAAU,0BAInC,CAEA,SAAS,GAAc,WACrB,CAAS,UACT,CAAQ,UACR,EAAW,QAAQ,CACnB,GAAG,EACkD,EACrD,MACE,CAAA,EAAA,EAAA,GAAA,EDikCS,ACjkCR,EAAA,UACC,CAAA,EAAA,EAAA,IAAA,EDikCS,ACjkCR,EAAA,CACC,YAAU,iBACV,UAAW,CAAA,EAAA,GAAA,EAAA,AAAE,EACX,gjBACa,WAAb,GACE,kIACF,GAEF,SAAU,EACT,GAAG,CAAK,WAET,CAAA,EAAA,EAAA,GAAA,EAAC,GAAA,CAAA,GACD,CAAA,EAAA,EAAA,GAAA,EAAC,ADsjCM,GCtjCN,CACC,UAAW,CAAA,EAAA,GAAA,EAAA,AAAE,EACX,MACa,WAAb,GACE,gHAGH,IAEH,CAAA,EAAA,EAAA,GAAA,EAAC,GAAA,CAAA,OAIT,CAeA,SAAS,GAAW,WAClB,CAAS,UACT,CAAQ,CACR,GAAG,EAC+C,EAClD,MACE,CAAA,EAAA,EAAA,IAAA,EDuhCO,ACvhCN,GAAA,CACC,YAAU,cACV,UAAW,CAAA,EAAA,GAAA,EAAA,AAAE,EACX,4aACA,GAED,GAAG,CAAK,WAET,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,sEACd,CAAA,EAAA,EAAA,GAAA,EDghCY,AChhCX,GAAA,UACC,CAAA,EAAA,EAAA,GAAA,EAAC,GAAA,SAAS,CAAA,CAAC,UAAU,eAGzB,CAAA,EAAA,EAAA,GAAA,ED2gCS,AC3gCR,GAAA,UAA0B,MAGjC,CAeA,SAAS,GAAqB,WAC5B,CAAS,CACT,GAAG,EACyD,EAC5D,MACE,CAAA,EAAA,EAAA,GAAA,EDs/BiB,ACt/BhB,GAAA,CACC,YAAU,0BACV,UAAW,CAAA,EAAA,GAAA,EAAA,AAAE,EACX,uDACA,GAED,GAAG,CAAK,UAET,CAAA,EAAA,EAAA,GAAA,EAAC,GAAA,CAAc,UAAU,YAG/B,CAEA,SAAS,GAAuB,WAC9B,CAAS,CACT,GAAG,EAC2D,EAC9D,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,ADq+BkB,GCr+BlB,CACC,YAAU,4BACV,UAAW,CAAA,EAAA,GAAA,EAAA,AAAE,EACX,uDACA,GAED,GAAG,CAAK,UAET,CAAA,EAAA,EAAA,GAAA,EAAC,GAAA,CAAgB,UAAU,YAGjC", "ignoreList": [0, 2, 3, 4, 5, 6, 7]}