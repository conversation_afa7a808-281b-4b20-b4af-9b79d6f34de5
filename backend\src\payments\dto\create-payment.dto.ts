import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsPositive, IsString } from 'class-validator';
import { PaymentDirection, PaymentFunction } from '../schemas/payment.schema';

export class CreatePaymentDto {
  @ApiProperty({ enum: PaymentDirection })
  @IsEnum(PaymentDirection)
  direction!: PaymentDirection;

  @ApiProperty({ enum: PaymentFunction })
  @IsEnum(PaymentFunction)
  func!: PaymentFunction;

  @ApiProperty({ example: 1000 })
  @IsNumber()
  @IsPositive()
  amount!: number;

  @ApiProperty({ description: 'Linked caisse (mandatory)' })
  @IsMongoId()
  caisseId!: string;

  @ApiPropertyOptional({ description: 'Receiving caisse for transfer (optional: only for inter-caisse transfert)' })
  @IsOptional()
  @IsMongoId()
  receivingCaisseId?: string;

  @ApiPropertyOptional({ description: 'Linked session if applicable(optional because it is only for "caisse" type REUNION)' })
  @IsOptional()
  @IsMongoId()
  sessionId?: string;

  @ApiPropertyOptional({ description: 'Linked reunion if applicable(optional because it is only for meeting cotisation tontine)' })
  @IsOptional()
  @IsMongoId()
  reunionId?: string;

  @ApiPropertyOptional({ description: 'Linked member for contributions (optional)' })
  @IsOptional()
  @IsMongoId()
  memberId?: string;

  @ApiPropertyOptional({ description: 'Reason for external OUT payments (optional)' })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  reason?: string;

  @ApiPropertyOptional({ description: 'ExitOrder to use (optional, required for reunion OUT by cashier)' })
  @IsOptional()
  @IsMongoId()
  exitOrderId?: string;
}