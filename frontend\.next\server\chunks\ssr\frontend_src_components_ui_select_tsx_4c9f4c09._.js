module.exports=[44932,a=>{"use strict";a.s(["Select",()=>aD,"SelectContent",()=>aG,"SelectItem",()=>aH,"SelectTrigger",()=>aF,"SelectValue",()=>aE],44932);var b=a.i(68116),c=a.i(128),d=a.i(60443);function e(a,[b,c]){return Math.min(c,Math.max(b,a))}var f=a.i(57565),g=a.i(48099),h=a.i(9403),i=a.i(54130),j=a.i(85955),k=a.i(44177),l=a.i(95686),m=a.i(45105),n=a.i(90368),o=a.i(60091),p=a.i(24321),q=a.i(48206),r=a.i(85689),s=a.i(1510),t=a.i(65662),u=a.i(58714),v=a.i(75936),w=a.i(22999),x=a.i(39491),y=[" ","Enter","ArrowUp","ArrowDown"],z=[" ","Enter"],A="Select",[B,C,D]=(0,g.createCollection)(A),[E,F]=(0,i.createContextScope)(A,[D,o.createPopperScope]),G=(0,o.createPopperScope)(),[H,I]=E(A),[J,K]=E(A),L=a=>{let{__scopeSelect:d,children:e,open:f,defaultOpen:g,onOpenChange:h,value:i,defaultValue:k,onValueChange:l,dir:m,name:p,autoComplete:q,disabled:r,required:s,form:u}=a,v=G(d),[w,x]=c.useState(null),[y,z]=c.useState(null),[C,D]=c.useState(!1),E=(0,j.useDirection)(m),[F,I]=(0,t.useControllableState)({prop:f,defaultProp:g??!1,onChange:h,caller:A}),[K,L]=(0,t.useControllableState)({prop:i,defaultProp:k,onChange:l,caller:A}),M=c.useRef(null),N=!w||u||!!w.closest("form"),[O,P]=c.useState(new Set),Q=Array.from(O).map(a=>a.props.value).join(";");return(0,b.jsx)(o.Root,{...v,children:(0,b.jsxs)(H,{required:s,scope:d,trigger:w,onTriggerChange:x,valueNode:y,onValueNodeChange:z,valueNodeHasChildren:C,onValueNodeHasChildrenChange:D,contentId:(0,n.useId)(),value:K,onValueChange:L,open:F,onOpenChange:I,dir:E,triggerPointerDownPosRef:M,disabled:r,children:[(0,b.jsx)(B.Provider,{scope:d,children:(0,b.jsx)(J,{scope:a.__scopeSelect,onNativeOptionAdd:c.useCallback(a=>{P(b=>new Set(b).add(a))},[]),onNativeOptionRemove:c.useCallback(a=>{P(b=>{let c=new Set(b);return c.delete(a),c})},[]),children:e})}),N?(0,b.jsxs)(au,{"aria-hidden":!0,required:s,tabIndex:-1,name:p,autoComplete:q,value:K,onChange:a=>L(a.target.value),disabled:r,form:u,children:[void 0===K?(0,b.jsx)("option",{value:""}):null,Array.from(O)]},Q):null]})})};L.displayName=A;var M="SelectTrigger",N=c.forwardRef((a,d)=>{let{__scopeSelect:e,disabled:g=!1,...i}=a,j=G(e),k=I(M,e),l=k.disabled||g,m=(0,h.useComposedRefs)(d,k.onTriggerChange),n=C(e),p=c.useRef("touch"),[r,s,t]=aw(a=>{let b=n().filter(a=>!a.disabled),c=b.find(a=>a.value===k.value),d=ax(b,a,c);void 0!==d&&k.onValueChange(d.value)}),u=a=>{l||(k.onOpenChange(!0),t()),a&&(k.triggerPointerDownPosRef.current={x:Math.round(a.pageX),y:Math.round(a.pageY)})};return(0,b.jsx)(o.Anchor,{asChild:!0,...j,children:(0,b.jsx)(q.Primitive.button,{type:"button",role:"combobox","aria-controls":k.contentId,"aria-expanded":k.open,"aria-required":k.required,"aria-autocomplete":"none",dir:k.dir,"data-state":k.open?"open":"closed",disabled:l,"data-disabled":l?"":void 0,"data-placeholder":av(k.value)?"":void 0,...i,ref:m,onClick:(0,f.composeEventHandlers)(i.onClick,a=>{a.currentTarget.focus(),"mouse"!==p.current&&u(a)}),onPointerDown:(0,f.composeEventHandlers)(i.onPointerDown,a=>{p.current=a.pointerType;let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),0===a.button&&!1===a.ctrlKey&&"mouse"===a.pointerType&&(u(a),a.preventDefault())}),onKeyDown:(0,f.composeEventHandlers)(i.onKeyDown,a=>{let b=""!==r.current;a.ctrlKey||a.altKey||a.metaKey||1!==a.key.length||s(a.key),(!b||" "!==a.key)&&y.includes(a.key)&&(u(),a.preventDefault())})})})});N.displayName=M;var O="SelectValue",P=c.forwardRef((a,c)=>{let{__scopeSelect:d,className:e,style:f,children:g,placeholder:i="",...j}=a,k=I(O,d),{onValueNodeHasChildrenChange:l}=k,m=void 0!==g,n=(0,h.useComposedRefs)(c,k.onValueNodeChange);return(0,u.useLayoutEffect)(()=>{l(m)},[l,m]),(0,b.jsx)(q.Primitive.span,{...j,ref:n,style:{pointerEvents:"none"},children:av(k.value)?(0,b.jsx)(b.Fragment,{children:i}):g})});P.displayName=O;var Q=c.forwardRef((a,c)=>{let{__scopeSelect:d,children:e,...f}=a;return(0,b.jsx)(q.Primitive.span,{"aria-hidden":!0,...f,ref:c,children:e||"▼"})});Q.displayName="SelectIcon";var R=a=>(0,b.jsx)(p.Portal,{asChild:!0,...a});R.displayName="SelectPortal";var S="SelectContent",T=c.forwardRef((a,e)=>{let f=I(S,a.__scopeSelect),[g,h]=c.useState();return((0,u.useLayoutEffect)(()=>{h(new DocumentFragment)},[]),f.open)?(0,b.jsx)(X,{...a,ref:e}):g?d.createPortal((0,b.jsx)(U,{scope:a.__scopeSelect,children:(0,b.jsx)(B.Slot,{scope:a.__scopeSelect,children:(0,b.jsx)("div",{children:a.children})})}),g):null});T.displayName=S;var[U,V]=E(S),W=(0,r.createSlot)("SelectContent.RemoveScroll"),X=c.forwardRef((a,d)=>{let{__scopeSelect:e,position:g="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:j,onPointerDownOutside:n,side:o,sideOffset:p,align:q,alignOffset:r,arrowPadding:s,collisionBoundary:t,collisionPadding:u,sticky:v,hideWhenDetached:y,avoidCollisions:z,...A}=a,B=I(S,e),[D,E]=c.useState(null),[F,G]=c.useState(null),H=(0,h.useComposedRefs)(d,a=>E(a)),[J,K]=c.useState(null),[L,M]=c.useState(null),N=C(e),[O,P]=c.useState(!1),Q=c.useRef(!1);c.useEffect(()=>{if(D)return(0,w.hideOthers)(D)},[D]),(0,l.useFocusGuards)();let R=c.useCallback(a=>{let[b,...c]=N().map(a=>a.ref.current),[d]=c.slice(-1),e=document.activeElement;for(let c of a)if(c===e||(c?.scrollIntoView({block:"nearest"}),c===b&&F&&(F.scrollTop=0),c===d&&F&&(F.scrollTop=F.scrollHeight),c?.focus(),document.activeElement!==e))return},[N,F]),T=c.useCallback(()=>R([J,D]),[R,J,D]);c.useEffect(()=>{O&&T()},[O,T]);let{onOpenChange:V,triggerPointerDownPosRef:X}=B;c.useEffect(()=>{if(D){let a={x:0,y:0},b=b=>{a={x:Math.abs(Math.round(b.pageX)-(X.current?.x??0)),y:Math.abs(Math.round(b.pageY)-(X.current?.y??0))}},c=c=>{a.x<=10&&a.y<=10?c.preventDefault():D.contains(c.target)||V(!1),document.removeEventListener("pointermove",b),X.current=null};return null!==X.current&&(document.addEventListener("pointermove",b),document.addEventListener("pointerup",c,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",b),document.removeEventListener("pointerup",c,{capture:!0})}}},[D,V,X]),c.useEffect(()=>{let a=()=>V(!1);return window.addEventListener("blur",a),window.addEventListener("resize",a),()=>{window.removeEventListener("blur",a),window.removeEventListener("resize",a)}},[V]);let[$,_]=aw(a=>{let b=N().filter(a=>!a.disabled),c=b.find(a=>a.ref.current===document.activeElement),d=ax(b,a,c);d&&setTimeout(()=>d.ref.current.focus())}),aa=c.useCallback((a,b,c)=>{let d=!Q.current&&!c;(void 0!==B.value&&B.value===b||d)&&(K(a),d&&(Q.current=!0))},[B.value]),ab=c.useCallback(()=>D?.focus(),[D]),ac=c.useCallback((a,b,c)=>{let d=!Q.current&&!c;(void 0!==B.value&&B.value===b||d)&&M(a)},[B.value]),ad="popper"===g?Z:Y,ae=ad===Z?{side:o,sideOffset:p,align:q,alignOffset:r,arrowPadding:s,collisionBoundary:t,collisionPadding:u,sticky:v,hideWhenDetached:y,avoidCollisions:z}:{};return(0,b.jsx)(U,{scope:e,content:D,viewport:F,onViewportChange:G,itemRefCallback:aa,selectedItem:J,onItemLeave:ab,itemTextRefCallback:ac,focusSelectedItem:T,selectedItemText:L,position:g,isPositioned:O,searchRef:$,children:(0,b.jsx)(x.RemoveScroll,{as:W,allowPinchZoom:!0,children:(0,b.jsx)(m.FocusScope,{asChild:!0,trapped:B.open,onMountAutoFocus:a=>{a.preventDefault()},onUnmountAutoFocus:(0,f.composeEventHandlers)(i,a=>{B.trigger?.focus({preventScroll:!0}),a.preventDefault()}),children:(0,b.jsx)(k.DismissableLayer,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:j,onPointerDownOutside:n,onFocusOutside:a=>a.preventDefault(),onDismiss:()=>B.onOpenChange(!1),children:(0,b.jsx)(ad,{role:"listbox",id:B.contentId,"data-state":B.open?"open":"closed",dir:B.dir,onContextMenu:a=>a.preventDefault(),...A,...ae,onPlaced:()=>P(!0),ref:H,style:{display:"flex",flexDirection:"column",outline:"none",...A.style},onKeyDown:(0,f.composeEventHandlers)(A.onKeyDown,a=>{let b=a.ctrlKey||a.altKey||a.metaKey;if("Tab"===a.key&&a.preventDefault(),b||1!==a.key.length||_(a.key),["ArrowUp","ArrowDown","Home","End"].includes(a.key)){let b=N().filter(a=>!a.disabled).map(a=>a.ref.current);if(["ArrowUp","End"].includes(a.key)&&(b=b.slice().reverse()),["ArrowUp","ArrowDown"].includes(a.key)){let c=a.target,d=b.indexOf(c);b=b.slice(d+1)}setTimeout(()=>R(b)),a.preventDefault()}})})})})})})});X.displayName="SelectContentImpl";var Y=c.forwardRef((a,d)=>{let{__scopeSelect:f,onPlaced:g,...i}=a,j=I(S,f),k=V(S,f),[l,m]=c.useState(null),[n,o]=c.useState(null),p=(0,h.useComposedRefs)(d,a=>o(a)),r=C(f),s=c.useRef(!1),t=c.useRef(!0),{viewport:v,selectedItem:w,selectedItemText:x,focusSelectedItem:y}=k,z=c.useCallback(()=>{if(j.trigger&&j.valueNode&&l&&n&&v&&w&&x){let a=j.trigger.getBoundingClientRect(),b=n.getBoundingClientRect(),c=j.valueNode.getBoundingClientRect(),d=x.getBoundingClientRect();if("rtl"!==j.dir){let f=d.left-b.left,g=c.left-f,h=a.left-g,i=a.width+h,j=Math.max(i,b.width),k=e(g,[10,Math.max(10,window.innerWidth-10-j)]);l.style.minWidth=i+"px",l.style.left=k+"px"}else{let f=b.right-d.right,g=window.innerWidth-c.right-f,h=window.innerWidth-a.right-g,i=a.width+h,j=Math.max(i,b.width),k=e(g,[10,Math.max(10,window.innerWidth-10-j)]);l.style.minWidth=i+"px",l.style.right=k+"px"}let f=r(),h=window.innerHeight-20,i=v.scrollHeight,k=window.getComputedStyle(n),m=parseInt(k.borderTopWidth,10),o=parseInt(k.paddingTop,10),p=parseInt(k.borderBottomWidth,10),q=m+o+i+parseInt(k.paddingBottom,10)+p,t=Math.min(5*w.offsetHeight,q),u=window.getComputedStyle(v),y=parseInt(u.paddingTop,10),z=parseInt(u.paddingBottom,10),A=a.top+a.height/2-10,B=w.offsetHeight/2,C=m+o+(w.offsetTop+B);if(C<=A){let a=f.length>0&&w===f[f.length-1].ref.current;l.style.bottom="0px";let b=Math.max(h-A,B+(a?z:0)+(n.clientHeight-v.offsetTop-v.offsetHeight)+p);l.style.height=C+b+"px"}else{let a=f.length>0&&w===f[0].ref.current;l.style.top="0px";let b=Math.max(A,m+v.offsetTop+(a?y:0)+B);l.style.height=b+(q-C)+"px",v.scrollTop=C-A+v.offsetTop}l.style.margin="10px 0",l.style.minHeight=t+"px",l.style.maxHeight=h+"px",g?.(),requestAnimationFrame(()=>s.current=!0)}},[r,j.trigger,j.valueNode,l,n,v,w,x,j.dir,g]);(0,u.useLayoutEffect)(()=>z(),[z]);let[A,B]=c.useState();(0,u.useLayoutEffect)(()=>{n&&B(window.getComputedStyle(n).zIndex)},[n]);let D=c.useCallback(a=>{a&&!0===t.current&&(z(),y?.(),t.current=!1)},[z,y]);return(0,b.jsx)($,{scope:f,contentWrapper:l,shouldExpandOnScrollRef:s,onScrollButtonChange:D,children:(0,b.jsx)("div",{ref:m,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:A},children:(0,b.jsx)(q.Primitive.div,{...i,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});Y.displayName="SelectItemAlignedPosition";var Z=c.forwardRef((a,c)=>{let{__scopeSelect:d,align:e="start",collisionPadding:f=10,...g}=a,h=G(d);return(0,b.jsx)(o.Content,{...h,...g,ref:c,align:e,collisionPadding:f,style:{boxSizing:"border-box",...g.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Z.displayName="SelectPopperPosition";var[$,_]=E(S,{}),aa="SelectViewport",ab=c.forwardRef((a,d)=>{let{__scopeSelect:e,nonce:g,...i}=a,j=V(aa,e),k=_(aa,e),l=(0,h.useComposedRefs)(d,j.onViewportChange),m=c.useRef(0);return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:g}),(0,b.jsx)(B.Slot,{scope:e,children:(0,b.jsx)(q.Primitive.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:l,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,f.composeEventHandlers)(i.onScroll,a=>{let b=a.currentTarget,{contentWrapper:c,shouldExpandOnScrollRef:d}=k;if(d?.current&&c){let a=Math.abs(m.current-b.scrollTop);if(a>0){let d=window.innerHeight-20,e=Math.max(parseFloat(c.style.minHeight),parseFloat(c.style.height));if(e<d){let f=e+a,g=Math.min(d,f),h=f-g;c.style.height=g+"px","0px"===c.style.bottom&&(b.scrollTop=h>0?h:0,c.style.justifyContent="flex-end")}}}m.current=b.scrollTop})})})]})});ab.displayName=aa;var ac="SelectGroup",[ad,ae]=E(ac);c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a,f=(0,n.useId)();return(0,b.jsx)(ad,{scope:d,id:f,children:(0,b.jsx)(q.Primitive.div,{role:"group","aria-labelledby":f,...e,ref:c})})}).displayName=ac;var af="SelectLabel";c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a,f=ae(af,d);return(0,b.jsx)(q.Primitive.div,{id:f.id,...e,ref:c})}).displayName=af;var ag="SelectItem",[ah,ai]=E(ag),aj=c.forwardRef((a,d)=>{let{__scopeSelect:e,value:g,disabled:i=!1,textValue:j,...k}=a,l=I(ag,e),m=V(ag,e),o=l.value===g,[p,r]=c.useState(j??""),[s,t]=c.useState(!1),u=(0,h.useComposedRefs)(d,a=>m.itemRefCallback?.(a,g,i)),v=(0,n.useId)(),w=c.useRef("touch"),x=()=>{i||(l.onValueChange(g),l.onOpenChange(!1))};if(""===g)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,b.jsx)(ah,{scope:e,value:g,disabled:i,textId:v,isSelected:o,onItemTextChange:c.useCallback(a=>{r(b=>b||(a?.textContent??"").trim())},[]),children:(0,b.jsx)(B.ItemSlot,{scope:e,value:g,disabled:i,textValue:p,children:(0,b.jsx)(q.Primitive.div,{role:"option","aria-labelledby":v,"data-highlighted":s?"":void 0,"aria-selected":o&&s,"data-state":o?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...k,ref:u,onFocus:(0,f.composeEventHandlers)(k.onFocus,()=>t(!0)),onBlur:(0,f.composeEventHandlers)(k.onBlur,()=>t(!1)),onClick:(0,f.composeEventHandlers)(k.onClick,()=>{"mouse"!==w.current&&x()}),onPointerUp:(0,f.composeEventHandlers)(k.onPointerUp,()=>{"mouse"===w.current&&x()}),onPointerDown:(0,f.composeEventHandlers)(k.onPointerDown,a=>{w.current=a.pointerType}),onPointerMove:(0,f.composeEventHandlers)(k.onPointerMove,a=>{w.current=a.pointerType,i?m.onItemLeave?.():"mouse"===w.current&&a.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,f.composeEventHandlers)(k.onPointerLeave,a=>{a.currentTarget===document.activeElement&&m.onItemLeave?.()}),onKeyDown:(0,f.composeEventHandlers)(k.onKeyDown,a=>{(m.searchRef?.current===""||" "!==a.key)&&(z.includes(a.key)&&x()," "===a.key&&a.preventDefault())})})})})});aj.displayName=ag;var ak="SelectItemText",al=c.forwardRef((a,e)=>{let{__scopeSelect:f,className:g,style:i,...j}=a,k=I(ak,f),l=V(ak,f),m=ai(ak,f),n=K(ak,f),[o,p]=c.useState(null),r=(0,h.useComposedRefs)(e,a=>p(a),m.onItemTextChange,a=>l.itemTextRefCallback?.(a,m.value,m.disabled)),s=o?.textContent,t=c.useMemo(()=>(0,b.jsx)("option",{value:m.value,disabled:m.disabled,children:s},m.value),[m.disabled,m.value,s]),{onNativeOptionAdd:v,onNativeOptionRemove:w}=n;return(0,u.useLayoutEffect)(()=>(v(t),()=>w(t)),[v,w,t]),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(q.Primitive.span,{id:m.textId,...j,ref:r}),m.isSelected&&k.valueNode&&!k.valueNodeHasChildren?d.createPortal(j.children,k.valueNode):null]})});al.displayName=ak;var am="SelectItemIndicator",an=c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a;return ai(am,d).isSelected?(0,b.jsx)(q.Primitive.span,{"aria-hidden":!0,...e,ref:c}):null});an.displayName=am;var ao="SelectScrollUpButton",ap=c.forwardRef((a,d)=>{let e=V(ao,a.__scopeSelect),f=_(ao,a.__scopeSelect),[g,i]=c.useState(!1),j=(0,h.useComposedRefs)(d,f.onScrollButtonChange);return(0,u.useLayoutEffect)(()=>{if(e.viewport&&e.isPositioned){let a=function(){i(b.scrollTop>0)},b=e.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[e.viewport,e.isPositioned]),g?(0,b.jsx)(as,{...a,ref:j,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=e;a&&b&&(a.scrollTop=a.scrollTop-b.offsetHeight)}}):null});ap.displayName=ao;var aq="SelectScrollDownButton",ar=c.forwardRef((a,d)=>{let e=V(aq,a.__scopeSelect),f=_(aq,a.__scopeSelect),[g,i]=c.useState(!1),j=(0,h.useComposedRefs)(d,f.onScrollButtonChange);return(0,u.useLayoutEffect)(()=>{if(e.viewport&&e.isPositioned){let a=function(){let a=b.scrollHeight-b.clientHeight;i(Math.ceil(b.scrollTop)<a)},b=e.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[e.viewport,e.isPositioned]),g?(0,b.jsx)(as,{...a,ref:j,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=e;a&&b&&(a.scrollTop=a.scrollTop+b.offsetHeight)}}):null});ar.displayName=aq;var as=c.forwardRef((a,d)=>{let{__scopeSelect:e,onAutoScroll:g,...h}=a,i=V("SelectScrollButton",e),j=c.useRef(null),k=C(e),l=c.useCallback(()=>{null!==j.current&&(window.clearInterval(j.current),j.current=null)},[]);return c.useEffect(()=>()=>l(),[l]),(0,u.useLayoutEffect)(()=>{let a=k().find(a=>a.ref.current===document.activeElement);a?.ref.current?.scrollIntoView({block:"nearest"})},[k]),(0,b.jsx)(q.Primitive.div,{"aria-hidden":!0,...h,ref:d,style:{flexShrink:0,...h.style},onPointerDown:(0,f.composeEventHandlers)(h.onPointerDown,()=>{null===j.current&&(j.current=window.setInterval(g,50))}),onPointerMove:(0,f.composeEventHandlers)(h.onPointerMove,()=>{i.onItemLeave?.(),null===j.current&&(j.current=window.setInterval(g,50))}),onPointerLeave:(0,f.composeEventHandlers)(h.onPointerLeave,()=>{l()})})});c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a;return(0,b.jsx)(q.Primitive.div,{"aria-hidden":!0,...e,ref:c})}).displayName="SelectSeparator";var at="SelectArrow";c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a,f=G(d),g=I(at,d),h=V(at,d);return g.open&&"popper"===h.position?(0,b.jsx)(o.Arrow,{...f,...e,ref:c}):null}).displayName=at;var au=c.forwardRef(({__scopeSelect:a,value:d,...e},f)=>{let g=c.useRef(null),i=(0,h.useComposedRefs)(f,g),j=function(a){let b=c.useRef({value:a,previous:a});return c.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}(d);return c.useEffect(()=>{let a=g.current;if(!a)return;let b=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(j!==d&&b){let c=new Event("change",{bubbles:!0});b.call(a,d),a.dispatchEvent(c)}},[j,d]),(0,b.jsx)(q.Primitive.select,{...e,style:{...v.VISUALLY_HIDDEN_STYLES,...e.style},ref:i,defaultValue:d})});function av(a){return""===a||void 0===a}function aw(a){let b=(0,s.useCallbackRef)(a),d=c.useRef(""),e=c.useRef(0),f=c.useCallback(a=>{let c=d.current+a;b(c),function a(b){d.current=b,window.clearTimeout(e.current),""!==b&&(e.current=window.setTimeout(()=>a(""),1e3))}(c)},[b]),g=c.useCallback(()=>{d.current="",window.clearTimeout(e.current)},[]);return c.useEffect(()=>()=>window.clearTimeout(e.current),[]),[d,f,g]}function ax(a,b,c){var d,e;let f=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,g=c?a.indexOf(c):-1,h=(d=a,e=Math.max(g,0),d.map((a,b)=>d[(e+b)%d.length]));1===f.length&&(h=h.filter(a=>a!==c));let i=h.find(a=>a.textValue.toLowerCase().startsWith(f.toLowerCase()));return i!==c?i:void 0}au.displayName="SelectBubbleInput";var ay=a.i(54839),az=a.i(621);let aA=(0,az.default)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),aB=(0,az.default)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var aC=a.i(22171);function aD({...a}){return(0,b.jsx)(L,{"data-slot":"select",...a})}function aE({...a}){return(0,b.jsx)(P,{"data-slot":"select-value",...a})}function aF({className:a,size:c="default",children:d,...e}){return(0,b.jsxs)(N,{"data-slot":"select-trigger","data-size":c,className:(0,aC.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...e,children:[d,(0,b.jsx)(Q,{asChild:!0,children:(0,b.jsx)(aA,{className:"size-4 opacity-50"})})]})}function aG({className:a,children:c,position:d="popper",...e}){return(0,b.jsx)(R,{children:(0,b.jsxs)(T,{"data-slot":"select-content",className:(0,aC.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===d&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:d,...e,children:[(0,b.jsx)(aI,{}),(0,b.jsx)(ab,{className:(0,aC.cn)("p-1","popper"===d&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:c}),(0,b.jsx)(aJ,{})]})})}function aH({className:a,children:c,...d}){return(0,b.jsxs)(aj,{"data-slot":"select-item",className:(0,aC.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...d,children:[(0,b.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,b.jsx)(an,{children:(0,b.jsx)(ay.CheckIcon,{className:"size-4"})})}),(0,b.jsx)(al,{children:c})]})}function aI({className:a,...c}){return(0,b.jsx)(ap,{"data-slot":"select-scroll-up-button",className:(0,aC.cn)("flex cursor-default items-center justify-center py-1",a),...c,children:(0,b.jsx)(aB,{className:"size-4"})})}function aJ({className:a,...c}){return(0,b.jsx)(ar,{"data-slot":"select-scroll-down-button",className:(0,aC.cn)("flex cursor-default items-center justify-center py-1",a),...c,children:(0,b.jsx)(aA,{className:"size-4"})})}}];

//# sourceMappingURL=frontend_src_components_ui_select_tsx_4c9f4c09._.js.map