{"version": 3, "sources": ["turbopack:///[project]/frontend/src/hooks/use-api.ts", "turbopack:///[project]/frontend/src/types/index.ts", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/plus.ts", "turbopack:///[project]/frontend/src/components/ui/badge.tsx", "turbopack:///[project]/frontend/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/trash-2.ts", "turbopack:///[project]/frontend/src/components/ui/table.tsx", "turbopack:///[project]/frontend/src/app/dashboard/users/page.tsx"], "sourcesContent": ["import { useSession } from \"next-auth/react\";\nimport { apiService } from \"@/lib/api\";\nimport {\n\tSession,\n\tCaisse,\n\tReunion,\n\tMember,\n\tPayment,\n\tSessionMember,\n\tCreateSessionDto,\n\tUpdateSessionDto,\n\tCreateCaisseDto,\n\tUpdateCaisseDto,\n\tUpdateReunionDto,\n\tCreateMemberDto,\n\tUpdateMemberDto,\n\tCreatePaymentDto,\n\tCreateSessionMemberDto,\n\tMemberDebrief,\n\tPaymentFilters,\n} from \"@/types\";\n\nexport function useApi() {\n\tconst { data: session } = useSession();\n\n\tconst authenticatedRequest = async <T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> => {\n\t\tif (!session?.accessToken) {\n\t\t\tthrow new Error(\"Non authentifié\");\n\t\t}\n\n\t\treturn apiService.authenticatedRequest<T>(\n\t\t\tendpoint,\n\t\t\tsession.accessToken,\n\t\t\toptions,\n\t\t);\n\t};\n\n\treturn {\n\t\t// Méthodes d'authentification (pas besoin de token)\n\t\tlogin: apiService.login.bind(apiService),\n\t\tregister: apiService.register.bind(apiService),\n\n\t\t// Méthodes authentifiées\n\t\tauthenticatedRequest,\n\n\t\t// Raccourcis pour les endpoints courants\n\t\tgetUsers: () => authenticatedRequest<any[]>(\"/users\"),\n\t\tgetUser: (id: string) => authenticatedRequest<any>(`/users/${id}`),\n\t\tcreateUser: (userData: any) =>\n\t\t\tauthenticatedRequest<any>(\"/users\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tupdateUser: (id: string, userData: any) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tdeleteUser: (id: string) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Sessions\n\t\tgetSessions: () => authenticatedRequest<Session[]>(\"/sessions\"),\n\t\tgetSession: (id: string) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`),\n\t\tcreateSession: (sessionData: CreateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(\"/sessions\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tupdateSession: (id: string, sessionData: UpdateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tdeleteSession: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Caisses\n\t\tgetCaisses: () => authenticatedRequest<Caisse[]>(\"/caisses\"),\n\t\tgetCaisse: (id: string) => authenticatedRequest<Caisse>(`/caisses/${id}`),\n\t\tcreateCaisse: (caisseData: CreateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(\"/caisses\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tupdateCaisse: (id: string, caisseData: UpdateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tdeleteCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\temargerCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}/emarger`, {\n\t\t\t\tmethod: \"POST\",\n\t\t\t}),\n\n\t\t// Réunions\n\t\tgetReunions: () => authenticatedRequest<Reunion[]>(\"/reunions\"),\n\t\tgetReunion: (id: string) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`),\n\t\tupdateReunion: (id: string, reunionData: UpdateReunionDto) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(reunionData),\n\t\t\t}),\n\n\t\t// Members\n\t\tgetMembers: () => authenticatedRequest<Member[]>(\"/members\"),\n\t\tgetMember: (id: string) => authenticatedRequest<Member>(`/members/${id}`),\n\t\tcreateMember: (memberData: CreateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(\"/members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tupdateMember: (id: string, memberData: UpdateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(`/members/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tdeleteMember: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/members/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\tgetMemberDebrief: (id: string, filters?: PaymentFilters) => {\n\t\t\tconst params = new URLSearchParams();\n\t\t\tif (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);\n\t\t\tif (filters?.dateTo) params.append('dateTo', filters.dateTo);\n\t\t\tif (filters?.sessionId) params.append('sessionId', filters.sessionId);\n\t\t\tconst query = params.toString() ? `?${params.toString()}` : '';\n\t\t\treturn authenticatedRequest<MemberDebrief>(`/members/${id}/debrief${query}`);\n\t\t},\n\n\t\t// Payments\n\t\tcreatePayment: (paymentData: CreatePaymentDto) =>\n\t\t\tauthenticatedRequest<Payment>(\"/payments\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(paymentData),\n\t\t\t}),\n\n\t\t// Session Members (inscription des membres aux sessions)\n\t\tgetSessionMembers: (sessionId: string) =>\n\t\t\tauthenticatedRequest<SessionMember[]>(`/sessions/${sessionId}/members`),\n\t\taddSessionMember: (sessionMemberData: CreateSessionMemberDto) =>\n\t\t\tauthenticatedRequest<SessionMember>(\"/session-members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionMemberData),\n\t\t\t}),\n\t\tremoveSessionMember: (sessionId: string, memberId: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${sessionId}/members/${memberId}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t};\n}\n", "// Types pour l'application Tontine\n\n// Enums\nexport enum CaisseType {\n  PRINCIPALE = 'PRINCIPALE',\n  REUNION = 'REUNION',\n}\n\nexport enum UserRole {\n  SECRETARY_GENERAL = 'secretary_general',\n  CONTROLLER = 'controller',\n  CASHIER = 'cashier',\n}\n\nexport enum UserStatus {\n  ACTIF = 'actif',\n  EN_ATTENTE = 'en_attente',\n  SUSPENDU = 'suspendu',\n}\n\nexport enum PaymentDirection {\n  IN = 'IN',\n  OUT = 'OUT',\n}\n\nexport enum PaymentFunction {\n  CONTRIBUTION = 'cotisation',\n  TRANSFER = 'transfert',\n  EXTERNAL = 'exterieur',\n}\n\n// Interfaces principales\nexport interface User {\n  _id: string;\n  username: string;\n  role: UserRole;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Session {\n  _id: string;\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  dateProchaineReunion?: string;\n  nextReunionId?: string;\n  partFixe: number;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Reunion {\n  _id: string;\n  dateReunion: string;\n  lieu?: string;\n  caissePrincipale?: string;\n  sessionId: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Caisse {\n  _id: string;\n  nom: string;\n  type: CaisseType;\n  soldeActuel: number;\n  sessionId?: string;\n  createdBy: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: Member (différent de User)\nexport interface Member {\n  _id: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: SessionMember (inscription d'un membre à une session)\nexport interface SessionMember {\n  _id: string;\n  sessionId: string;\n  memberId: string;\n  parts: number;\n  totalDue: number;\n  paidSoFar: number;\n  expectedToDate: number;\n  overdueAmount: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: Payment\nexport interface Payment {\n  _id: string;\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  date: string;\n  sessionId?: string;\n  reunionId?: string;\n  caisseId: string;\n  receivingCaisseId?: string;\n  memberId?: string;\n  reason?: string;\n  exitOrderId?: string;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// DTOs pour les formulaires\nexport interface CreateSessionDto {\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  partFixe: number;\n}\n\nexport interface UpdateSessionDto {\n  annee?: number;\n  dateDebut?: string;\n  dateFin?: string;\n  partFixe?: number;\n}\n\nexport interface CreateCaisseDto {\n  nom: string;\n  type: CaisseType;\n  soldeActuel?: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface UpdateCaisseDto {\n  nom?: string;\n  type?: CaisseType;\n  soldeActuel?: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface UpdateReunionDto {\n  dateReunion?: string;\n  lieu?: string;\n  caissePrincipale?: string;\n}\n\n// Nouveaux DTOs\nexport interface CreateMemberDto {\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface UpdateMemberDto {\n  firstName?: string;\n  lastName?: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface CreatePaymentDto {\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  caisseId: string;\n  receivingCaisseId?: string;\n  sessionId?: string;\n  reunionId?: string;\n  memberId?: string;\n  reason?: string;\n  exitOrderId?: string;\n}\n\nexport interface CreateSessionMemberDto {\n  sessionId: string;\n  memberId: string;\n  parts: number;\n}\n\n// Types pour les statistiques\nexport interface SessionStats {\n  total: number;\n  active: number;\n  completed: number;\n  totalPartFixe: number;\n}\n\nexport interface CaisseStats {\n  total: number;\n  principales: number;\n  reunions: number;\n  soldeTotal: number;\n  soldePrincipales: number;\n  soldeReunions: number;\n}\n\nexport interface MemberStats {\n  total: number;\n  withEmail: number;\n  withPhone: number;\n  withAddress: number;\n}\n\nexport interface PaymentStats {\n  totalIn: number;\n  totalOut: number;\n  netAmount: number;\n  contributionsTotal: number;\n  transfersTotal: number;\n  externalTotal: number;\n}\n\n// Types pour les formulaires\nexport interface SessionForm {\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  partFixe: number;\n}\n\nexport interface CaisseForm {\n  nom: string;\n  type: CaisseType;\n  soldeActuel: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface MemberForm {\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface PaymentForm {\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  caisseId: string;\n  receivingCaisseId?: string;\n  sessionId?: string;\n  reunionId?: string;\n  memberId?: string;\n  reason?: string;\n}\n\n// Types pour les réponses API\nexport interface ApiResponse<T> {\n  data: T;\n  message?: string;\n  success: boolean;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n// Types pour les filtres\nexport interface SessionFilters {\n  annee?: number;\n  status?: 'active' | 'completed' | 'all';\n}\n\nexport interface CaisseFilters {\n  type?: CaisseType | 'all';\n  sessionId?: string;\n}\n\nexport interface MemberFilters {\n  search?: string;\n  hasEmail?: boolean;\n  hasPhone?: boolean;\n}\n\nexport interface PaymentFilters {\n  direction?: PaymentDirection | 'all';\n  func?: PaymentFunction | 'all';\n  caisseId?: string;\n  sessionId?: string;\n  memberId?: string;\n  dateFrom?: string;\n  dateTo?: string;\n}\n\n// Types pour les réponses spéciales\nexport interface MemberDebrief {\n  member: Member;\n  totalIn: number;\n  totalOut: number;\n  netAmount: number;\n  contributionsTotal: number;\n  transfersTotal: number;\n  externalTotal: number;\n  payments: Payment[];\n}\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 11v6', key: 'nco0om' }],\n  ['path', { d: 'M14 11v6', key: 'outv1u' }],\n  ['path', { d: 'M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6', key: 'miytrc' }],\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2', key: 'e791ji' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTF2NiIgLz4KICA8cGF0aCBkPSJNMTQgMTF2NiIgLz4KICA8cGF0aCBkPSJNMTkgNnYxNGEyIDIgMCAwIDEtMiAySDdhMiAyIDAgMCAxLTItMlY2IiAvPgogIDxwYXRoIGQ9Ik0zIDZoMTgiIC8+CiAgPHBhdGggZD0iTTggNlY0YTIgMiAwIDAgMSAyLTJoNGEyIDIgMCAwIDEgMiAydjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n", "\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n", "\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSession } from \"next-auth/react\";\nimport { Plus, Search, Edit, Trash2 } from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport {\n\tTable,\n\tTableBody,\n\tTableCell,\n\tTableHead,\n\tTableHeader,\n\tTableRow,\n} from \"@/components/ui/table\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { useApi } from \"@/hooks/use-api\";\nimport { User, UserRole } from \"@/types\";\n\nexport default function UsersPage() {\n\tconst { data: session } = useSession();\n\tconst api = useApi();\n\n\tconst [users, setUsers] = useState<User[]>([]);\n\tconst [loading, setLoading] = useState(true);\n\tconst [searchTerm, setSearchTerm] = useState(\"\");\n\n\t// Vérifier les permissions - Se<PERSON> le Secrétaire Général peut gérer les utilisateurs\n\tconst canManageUsers =\n\t\tsession?.user && (session.user as any).role === UserRole.SECRETARY_GENERAL;\n\n\tuseEffect(() => {\n\t\tif (session?.accessToken) {\n\t\t\tloadUsers();\n\t\t}\n\t}, [session]);\n\n\tconst loadUsers = async () => {\n\t\ttry {\n\t\t\tsetLoading(true);\n\t\t\tconst usersData = await api.getUsers();\n\t\t\tsetUsers(usersData);\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors du chargement des utilisateurs:\", error);\n\t\t} finally {\n\t\t\tsetLoading(false);\n\t\t}\n\t};\n\n\tconst handleDeleteUser = async (userId: string) => {\n\t\tif (!confirm(\"Êtes-vous sûr de vouloir supprimer cet utilisateur ?\")) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tawait api.deleteUser(userId);\n\t\t\tawait loadUsers();\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors de la suppression:\", error);\n\t\t}\n\t};\n\n\tconst filteredUsers = users.filter((user) =>\n\t\tuser.username.toLowerCase().includes(searchTerm.toLowerCase())\n\t);\n\n\tconst getRoleBadgeColor = (role: UserRole) => {\n\t\tswitch (role) {\n\t\t\tcase UserRole.SECRETARY_GENERAL:\n\t\t\t\treturn \"bg-purple-100 text-purple-800\";\n\t\t\tcase UserRole.CONTROLLER:\n\t\t\t\treturn \"bg-blue-100 text-blue-800\";\n\t\t\tcase UserRole.CASHIER:\n\t\t\t\treturn \"bg-green-100 text-green-800\";\n\t\t\tdefault:\n\t\t\t\treturn \"bg-gray-100 text-gray-800\";\n\t\t}\n\t};\n\n\tconst getRoleLabel = (role: UserRole) => {\n\t\tswitch (role) {\n\t\t\tcase UserRole.SECRETARY_GENERAL:\n\t\t\t\treturn \"Secrétaire Général\";\n\t\t\tcase UserRole.CONTROLLER:\n\t\t\t\treturn \"Contrôleur\";\n\t\t\tcase UserRole.CASHIER:\n\t\t\t\treturn \"Caissier\";\n\t\t\tdefault:\n\t\t\t\treturn role;\n\t\t}\n\t};\n\n\tconst formatDate = (dateString: string) => {\n\t\treturn new Date(dateString).toLocaleDateString('fr-FR', {\n\t\t\tyear: 'numeric',\n\t\t\tmonth: 'short',\n\t\t\tday: 'numeric',\n\t\t});\n\t};\n\n\tif (!canManageUsers) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<h2 className=\"text-lg font-semibold text-gray-900\">Accès refusé</h2>\n\t\t\t\t\t\t<p className=\"text-gray-600\">\n\t\t\t\t\t\t\tSeul le Secrétaire Général peut accéder à cette page.\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tif (loading) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex justify-center py-8\">\n\t\t\t\t\t<div className=\"text-gray-500\">Chargement...</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* En-tête */}\n\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t<div>\n\t\t\t\t\t<h1 className=\"text-2xl font-bold text-gray-900\">Utilisateurs</h1>\n\t\t\t\t\t<p className=\"text-gray-600\">\n\t\t\t\t\t\tGestion des comptes d'accès à l'application\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t\t<Link href=\"/auth/register\">\n\t\t\t\t\t<Button>\n\t\t\t\t\t\t<Plus className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\tNouvel utilisateur\n\t\t\t\t\t</Button>\n\t\t\t\t</Link>\n\t\t\t</div>\n\n\t\t\t{/* Statistiques */}\n\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n\t\t\t\t<Card>\n\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\tTotal utilisateurs\n\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t</CardHeader>\n\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t<div className=\"text-2xl font-bold\">{users.length}</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t\t<Card>\n\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\tSecrétaires Généraux\n\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t</CardHeader>\n\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t<div className=\"text-2xl font-bold text-purple-600\">\n\t\t\t\t\t\t\t{users.filter(u => u.role === UserRole.SECRETARY_GENERAL).length}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t\t<Card>\n\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\tContrôleurs\n\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t</CardHeader>\n\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t<div className=\"text-2xl font-bold text-blue-600\">\n\t\t\t\t\t\t\t{users.filter(u => u.role === UserRole.CONTROLLER).length}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t\t<Card>\n\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\tCaissiers\n\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t</CardHeader>\n\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t<div className=\"text-2xl font-bold text-green-600\">\n\t\t\t\t\t\t\t{users.filter(u => u.role === UserRole.CASHIER).length}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t</div>\n\n\t\t\t{/* Recherche et liste */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<CardTitle>Liste des utilisateurs</CardTitle>\n\t\t\t\t\t\t\t<CardDescription>\n\t\t\t\t\t\t\t\t{filteredUsers.length} utilisateur(s)\n\t\t\t\t\t\t\t</CardDescription>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t\t\t<div className=\"relative\">\n\t\t\t\t\t\t\t\t<Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\tplaceholder=\"Rechercher un utilisateur...\"\n\t\t\t\t\t\t\t\t\tvalue={searchTerm}\n\t\t\t\t\t\t\t\t\tonChange={(e) => setSearchTerm(e.target.value)}\n\t\t\t\t\t\t\t\t\tclassName=\"pl-10 w-64\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t{filteredUsers.length > 0 ? (\n\t\t\t\t\t\t<Table>\n\t\t\t\t\t\t\t<TableHeader>\n\t\t\t\t\t\t\t\t<TableRow>\n\t\t\t\t\t\t\t\t\t<TableHead>Nom d'utilisateur</TableHead>\n\t\t\t\t\t\t\t\t\t<TableHead>Rôle</TableHead>\n\t\t\t\t\t\t\t\t\t<TableHead>Créé le</TableHead>\n\t\t\t\t\t\t\t\t\t<TableHead>Actions</TableHead>\n\t\t\t\t\t\t\t\t</TableRow>\n\t\t\t\t\t\t\t</TableHeader>\n\t\t\t\t\t\t\t<TableBody>\n\t\t\t\t\t\t\t\t{filteredUsers.map((user) => (\n\t\t\t\t\t\t\t\t\t<TableRow key={user._id}>\n\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"font-medium\">{user.username}</div>\n\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t<Badge className={getRoleBadgeColor(user.role)}>\n\t\t\t\t\t\t\t\t\t\t\t\t{getRoleLabel(user.role)}\n\t\t\t\t\t\t\t\t\t\t\t</Badge>\n\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t<TableCell className=\"text-gray-600\">\n\t\t\t\t\t\t\t\t\t\t\t{formatDate(user.createdAt)}\n\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"ghost\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"sm\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleDeleteUser(user._id)}\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"text-red-600 hover:text-red-700\"\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Trash2 className=\"h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t</TableRow>\n\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t</TableBody>\n\t\t\t\t\t\t</Table>\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<div className=\"text-center py-8 text-gray-500\">\n\t\t\t\t\t\t\t{searchTerm ? \"Aucun utilisateur trouvé\" : \"Aucun utilisateur\"}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t)}\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": "gPAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAqBO,SAAS,IACf,GAAM,CAAE,KAAM,CAAO,CAAE,CAAG,CAAA,EAAA,EAAA,UAAA,AAAU,IAE9B,EAAuB,MAC5B,EACA,EAAuB,CAAC,CAAC,IAEzB,GAAI,CAAC,GAAS,YACb,CAD0B,KACpB,AAAI,MAAM,mBAGjB,OAAO,EAAA,UAAU,CAAC,oBAAoB,CACrC,EACA,EAAQ,WAAW,CACnB,EAEF,EAEA,MAAO,CAEN,MAAO,EAAA,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAA,UAAU,EACvC,SAAU,EAAA,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAA,UAAU,EAG7C,uBAGA,SAAU,IAAM,EAA4B,UAC5C,QAAS,AAAC,GAAe,EAA0B,CAAC,OAAO,EAAE,EAAA,CAAI,EACjE,WAAY,AAAC,GACZ,EAA0B,SAAU,CACnC,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,WAAY,CAAC,EAAY,IACxB,EAA0B,CAAC,OAAO,EAAE,EAAA,CAAI,CAAE,CACzC,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,WAAa,AAAD,GACX,EAA0B,CAAC,OAAO,EAAE,EAAA,CAAI,CAAE,CACzC,OAAQ,QACT,GAGD,YAAa,IAAM,EAAgC,aACnD,WAAY,AAAC,GACZ,EAA8B,CAAC,UAAU,EAAE,EAAA,CAAI,EAChD,cAAgB,AAAD,GACd,EAA8B,YAAa,CAC1C,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,cAAe,CAAC,EAAY,IAC3B,EAA8B,CAAC,UAAU,EAAE,EAAA,CAAI,CAAE,CAChD,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,cAAe,AAAC,GACf,EAA2B,CAAC,UAAU,EAAE,EAAA,CAAI,CAAE,CAC7C,OAAQ,QACT,GAGD,WAAY,IAAM,EAA+B,YACjD,UAAW,AAAC,GAAe,EAA6B,CAAC,SAAS,EAAE,EAAA,CAAI,EACxE,aAAc,AAAC,GACd,EAA6B,WAAY,CACxC,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,aAAc,CAAC,EAAY,IAC1B,EAA6B,CAAC,SAAS,EAAE,EAAA,CAAI,CAAE,CAC9C,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,aAAc,AAAC,GACd,EAA2B,CAAC,SAAS,EAAE,EAAA,CAAI,CAAE,CAC5C,OAAQ,QACT,GACD,cAAe,AAAC,GACf,EAA6B,CAAC,SAAS,EAAE,EAAG,QAAQ,CAAC,CAAE,CACtD,OAAQ,MACT,GAGD,YAAa,IAAM,EAAgC,aACnD,WAAY,AAAC,GACZ,EAA8B,CAAC,UAAU,EAAE,EAAA,CAAI,EAChD,cAAe,CAAC,EAAY,IAC3B,EAA8B,CAAC,UAAU,EAAE,EAAA,CAAI,CAAE,CAChD,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GAGD,WAAY,IAAM,EAA+B,YACjD,UAAW,AAAC,GAAe,EAA6B,CAAC,SAAS,EAAE,EAAA,CAAI,EACxE,aAAc,AAAC,GACd,EAA6B,WAAY,CACxC,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,aAAc,CAAC,EAAY,IAC1B,EAA6B,CAAC,SAAS,EAAE,EAAA,CAAI,CAAE,CAC9C,OAAQ,QACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,aAAc,AAAC,GACd,EAA2B,CAAC,SAAS,EAAE,EAAA,CAAI,CAAE,CAC5C,OAAQ,QACT,GACD,iBAAkB,CAAC,EAAY,KAC9B,IAAM,EAAS,IAAI,eACf,IAAS,UAAU,EAAO,MAAM,CAAC,WAAY,EAAQ,QAAQ,EAC7D,GAAS,QAAQ,EAAO,MAAM,CAAC,SAAU,EAAQ,MAAM,EACvD,GAAS,WAAW,EAAO,MAAM,CAAC,YAAa,EAAQ,SAAS,EACpE,IAAM,EAAQ,EAAO,QAAQ,GAAK,CAAC,CAAC,EAAE,EAAO,QAAQ,GAAA,CAAI,CAAG,GAC5D,OAAO,EAAoC,CAAC,SAAS,EAAE,EAAG,QAAQ,EAAE,EAAA,CAAO,CAC5E,EAGA,cAAe,AAAC,GACf,EAA8B,YAAa,CAC1C,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GAGD,kBAAmB,AAAC,GACnB,EAAsC,CAAC,UAAU,EAAE,EAAU,QAAQ,CAAC,EACvE,iBAAkB,AAAC,GAClB,EAAoC,mBAAoB,CACvD,OAAQ,OACR,KAAM,KAAK,SAAS,CAAC,EACtB,GACD,oBAAqB,CAAC,EAAmB,IACxC,EAA2B,CAAC,UAAU,EAAE,EAAU,SAAS,EAAE,EAAA,CAAU,CAAE,CACxE,OAAQ,QACT,EACF,CACD,sHChKO,IAAK,EAAA,SAAA,CAAA,uDAAA,OAKA,EAAA,SAAA,CAAA,+FAAA,OAYA,EAAA,SAAA,CAAA,+BAAA,OAKA,EAAA,SAAA,CAAA,mFAAA,yDCNZ,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAAM,CAAA,CAAA,CAAA,AAAO,CAAP,AAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAhBM,CAClC,AAeoC,CAfnC,AAemC,CAAA,AAfnC,CAAA,AAemC,CAfnC,AAemC,CAfnC,AAemC,CAfnC,AAemC,CAfnC,AAemC,CAfnC,AAemC,AAf3B,CAe2B,AAf3B,AAAE,CAemC,CAfhC,AAegC,CAfhC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAY,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACzC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAY,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAC3C,gECLA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OAEA,IAAM,EAAgB,CAAA,EAAA,EAAA,GAAA,AAAG,EACvB,iZACA,CACE,SAAU,CACR,QAAS,CACP,QACE,iFACF,UACE,uFACF,YACE,4KACF,QACE,wEACJ,CACF,EACA,gBAAiB,CACf,QAAS,SACX,CACF,GAGF,SAAS,EAAM,WACb,CAAS,SACT,CAAO,SACP,GAAU,CAAK,CACf,GAAG,EAEuD,EAC1D,IAAM,EAAO,EAAU,EAAA,IAAI,CAAG,OAE9B,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,YAAU,QACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,EAAc,SAAE,CAAQ,GAAI,GACzC,GAAG,CAAK,EAGf,oDCrBA,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAAM,CAAA,EAAS,CAAT,AAAS,CAAT,AAAS,CAAT,AAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAnBI,CAClC,AAkByC,CAlBxC,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAkBwC,CAlBxC,AAAQ,AAkBgC,CAlBhC,AAAE,AAkB8B,CAAU,CAAA,AAlBrC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAY,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACzC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAY,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACzC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAA4C,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACzE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAW,CAAA,CAAA,CAAA,CAAK,AAAL,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACxC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,AAAR,CAAU,AAAF,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAA0C,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CACzE,2JCLA,EAAA,EAAA,CAAA,CAAA,OAEA,SAAS,EAAM,WAAE,CAAS,CAAE,GAAG,EAAsC,EACnE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,YAAU,kBACV,UAAU,2CAEV,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,YAAU,QACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,gCAAiC,GAC9C,GAAG,CAAK,IAIjB,CAEA,SAAS,EAAY,WAAE,CAAS,CAAE,GAAG,EAAsC,EACzE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,YAAU,eACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,kBAAmB,GAChC,GAAG,CAAK,EAGf,CAEA,SAAS,EAAU,WAAE,CAAS,CAAE,GAAG,EAAsC,EACvE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,YAAU,aACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,6BAA8B,GAC3C,GAAG,CAAK,EAGf,CAeA,SAAS,EAAS,WAAE,CAAS,CAAE,GAAG,EAAmC,EACnE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CACC,YAAU,YACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,8EACA,GAED,GAAG,CAAK,EAGf,CAEA,SAAS,EAAU,WAAE,CAAS,CAAE,GAAG,EAAmC,EACpE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CACC,YAAU,aACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,qJACA,GAED,GAAG,CAAK,EAGf,CAEA,SAAS,EAAU,WAAE,CAAS,CAAE,GAAG,EAAmC,EACpE,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CACC,YAAU,aACV,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,yGACA,GAED,GAAG,CAAK,EAGf,kECzFA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAOA,EAAA,EAAA,CAAA,CAAA,OAQA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACvB,GAAM,CAAE,KAAM,CAAO,CAAE,CAAG,CAAA,EAAA,EAAA,UAAA,AAAU,IAC9B,EAAM,CAAA,EAAA,EAAA,MAAM,AAAN,IAEN,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAS,EAAE,EACvC,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAGvC,EACL,GAAS,MAAS,EAAQ,IAAI,CAAS,IAAI,GAAK,EAAA,QAAQ,CAAC,iBAAiB,CAE3E,CAAA,EAAA,EAAA,SAAS,AAAT,EAAU,KACL,GAAS,aAAa,AACzB,GAEF,EAAG,CAAC,EAAQ,EAEZ,IAAM,EAAY,UACjB,GAAI,CACH,GAAW,GACX,IAAM,EAAY,MAAM,EAAI,QAAQ,GACpC,EAAS,EACV,CAAE,MAAO,EAAO,CACf,QAAQ,KAAK,CAAC,8CAA+C,EAC9D,QAAU,CACT,GAAW,EACZ,CACD,EAEM,EAAmB,MAAO,IAC/B,GAAK,CAAD,OAAS,wDAIb,CAJsE,EAIlE,CACH,MAAM,EAAI,UAAU,CAAC,GACrB,MAAM,GACP,CAAE,MAAO,EAAO,CACf,QAAQ,KAAK,CAAC,iCAAkC,EACjD,CACD,EAEM,EAAgB,EAAM,MAAM,CAAC,AAAC,GACnC,EAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAW,WAAW,YAqC5D,AAAK,EAeD,EAfA,AAiBF,CAAA,EAAA,EAAA,EAFW,CAEX,EAAC,EAjBkB,IAiBlB,CAAI,UAAU,qBACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oCACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,sBAOlC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBAEd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4CAAmC,iBACjD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,mDAI9B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,0BACV,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,WACN,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,iBAAiB,6BAOpC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACd,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,gBACrB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,6CAAoC,yBAI1D,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BAAsB,EAAM,MAAM,QAGnD,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,gBACrB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,6CAAoC,2BAI1D,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8CACb,EAAM,MAAM,CAAC,GAAK,EAAE,IAAI,GAAK,EAAA,QAAQ,CAAC,iBAAiB,EAAE,MAAM,QAInE,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,gBACrB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,6CAAoC,kBAI1D,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CACb,EAAM,MAAM,CAAC,GAAK,EAAE,IAAI,GAAK,EAAA,QAAQ,CAAC,UAAU,EAAE,MAAM,QAI5D,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,gBACrB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,6CAAoC,gBAI1D,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CACb,EAAM,MAAM,CAAC,GAAK,EAAE,IAAI,GAAK,EAAA,QAAQ,CAAC,OAAO,EAAE,MAAM,WAO1D,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACJ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,UACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACA,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,2BACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,eAAe,CAAA,WACd,EAAc,MAAM,CAAC,wBAGxB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mCACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qBACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,6EAClB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CACL,YAAY,+BACZ,MAAO,EACP,SAAU,AAAC,GAAM,EAAc,EAAE,MAAM,CAAC,KAAK,EAC7C,UAAU,yBAMf,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,EAAc,MAAM,CAAG,EACvB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,KAAK,CAAA,WACL,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,sBACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,SACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,YACX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UAAC,iBAGb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UACR,EAAc,GAAG,CAAC,AAAC,GACnB,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,QAAQ,CAAA,WACR,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UACT,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uBAAe,EAAK,QAAQ,KAE5C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UACT,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAW,CAxKF,AAAC,IAC1B,OAAQ,GACP,KAAK,EAAA,QAAQ,CAAC,iBAAiB,CAC9B,MAAO,+BACR,MAAK,EAAA,QAAQ,CAAC,UAAU,CACvB,MAAO,2BACR,MAAK,EAAA,QAAQ,CAAC,OAAO,CACpB,MAAO,6BACR,SACC,MAAO,2BACT,EACD,EA6J8C,EAAK,IAAI,WAC3C,CA5JU,AAAD,IACpB,OAAQ,GACP,KAAK,EAAA,QAAQ,CAAC,iBAAiB,CAC9B,MAAO,oBACR,MAAK,EAAA,QAAQ,CAAC,UAAU,CACvB,MAAO,YACR,MAAK,EAAA,QAAQ,CAAC,OAAO,CACpB,MAAO,UACR,SACC,OAAO,CACT,CACD,GAiJyB,EAAK,IAAI,MAGzB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,yBAjJtB,CAkJG,GAlJC,KAkJU,AAlJL,EAkJU,SAAS,EAlJP,kBAAkB,CAAC,QAAS,CACvD,KAAM,UACN,MAAO,QACP,IAAK,SACN,KAgJQ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,UACT,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mCACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CACN,QAAQ,QACR,KAAK,KACL,QAAS,IAAM,EAAiB,EAAK,GAAG,EACxC,UAAU,2CAEV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,oBApBP,EAAK,GAAG,QA6B1B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0CACb,EAAa,2BAA6B,8BA7J/C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACd,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACd,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,+CAAsC,iBACpD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,gEAgKnC", "ignoreList": [2, 4]}