module.exports=[64743,a=>{"use strict";a.s(["Sidebar",()=>ao,"SidebarContent",()=>aw,"SidebarFooter",()=>au,"SidebarGroup",()=>ax,"SidebarGroupAction",()=>az,"SidebarGroupContent",()=>aA,"SidebarGroupLabel",()=>ay,"SidebarHeader",()=>at,"SidebarInput",()=>as,"SidebarInset",()=>ar,"SidebarMenu",()=>aB,"SidebarMenuAction",()=>aF,"SidebarMenuBadge",()=>aG,"SidebarMenuButton",()=>aE,"SidebarMenuItem",()=>aC,"SidebarMenuSkeleton",()=>aH,"SidebarMenuSub",()=>aI,"SidebarMenuSubButton",()=>aK,"SidebarMenuSubItem",()=>aJ,"SidebarProvider",()=>an,"SidebarRail",()=>aq,"SidebarSeparator",()=>av,"SidebarTrigger",()=>ap,"useSidebar",()=>am],64743);var b=a.i(68116),c=a.i(128),d=a.i(85689),e=a.i(57167);let f=(0,a.i(621).default)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]);var g=a.i(22171),h=a.i(2979),i=a.i(78184),j=a.i(48206),k="horizontal",l=["horizontal","vertical"],m=c.forwardRef((a,c)=>{var d;let{decorative:e,orientation:f=k,...g}=a,h=(d=f,l.includes(d))?f:k;return(0,b.jsx)(j.Primitive.div,{"data-orientation":h,...e?{role:"none"}:{"aria-orientation":"vertical"===h?h:void 0,role:"separator"},...g,ref:c})});function n({className:a,orientation:c="horizontal",decorative:d=!0,...e}){return(0,b.jsx)(m,{"data-slot":"separator-root",decorative:d,orientation:c,className:(0,g.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...e})}m.displayName="Separator";var o=a.i(66423),p=a.i(55667),p=p;function q({...a}){return(0,b.jsx)(o.Root,{"data-slot":"sheet",...a})}function r({...a}){return(0,b.jsx)(o.Portal,{"data-slot":"sheet-portal",...a})}function s({className:a,...c}){return(0,b.jsx)(o.Overlay,{"data-slot":"sheet-overlay",className:(0,g.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...c})}function t({className:a,children:c,side:d="right",...e}){return(0,b.jsxs)(r,{children:[(0,b.jsx)(s,{}),(0,b.jsxs)(o.Content,{"data-slot":"sheet-content",className:(0,g.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===d&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===d&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===d&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===d&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",a),...e,children:[c,(0,b.jsxs)(o.Close,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,b.jsx)(p.default,{className:"size-4"}),(0,b.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:a,...c}){return(0,b.jsx)("div",{"data-slot":"sheet-header",className:(0,g.cn)("flex flex-col gap-1.5 p-4",a),...c})}function v({className:a,...c}){return(0,b.jsx)(o.Title,{"data-slot":"sheet-title",className:(0,g.cn)("text-foreground font-semibold",a),...c})}function w({className:a,...c}){return(0,b.jsx)(o.Description,{"data-slot":"sheet-description",className:(0,g.cn)("text-muted-foreground text-sm",a),...c})}function x({className:a,...c}){return(0,b.jsx)("div",{"data-slot":"skeleton",className:(0,g.cn)("bg-accent animate-pulse rounded-md",a),...c})}var y=a.i(57565),z=a.i(9403),A=a.i(54130),B=a.i(44177),C=a.i(90368),D=a.i(60091),E=a.i(24321),F=a.i(96780),G=a.i(65662),H=a.i(75936),[I,J]=(0,A.createContextScope)("Tooltip",[D.createPopperScope]),K=(0,D.createPopperScope)(),L="TooltipProvider",M="tooltip.open",[N,O]=I(L),P=a=>{let{__scopeTooltip:d,delayDuration:e=700,skipDelayDuration:f=300,disableHoverableContent:g=!1,children:h}=a,i=c.useRef(!0),j=c.useRef(!1),k=c.useRef(0);return c.useEffect(()=>{let a=k.current;return()=>window.clearTimeout(a)},[]),(0,b.jsx)(N,{scope:d,isOpenDelayedRef:i,delayDuration:e,onOpen:c.useCallback(()=>{window.clearTimeout(k.current),i.current=!1},[]),onClose:c.useCallback(()=>{window.clearTimeout(k.current),k.current=window.setTimeout(()=>i.current=!0,f)},[f]),isPointerInTransitRef:j,onPointerInTransitChange:c.useCallback(a=>{j.current=a},[]),disableHoverableContent:g,children:h})};P.displayName=L;var Q="Tooltip",[R,S]=I(Q),T=a=>{let{__scopeTooltip:d,children:e,open:f,defaultOpen:g,onOpenChange:h,disableHoverableContent:i,delayDuration:j}=a,k=O(Q,a.__scopeTooltip),l=K(d),[m,n]=c.useState(null),o=(0,C.useId)(),p=c.useRef(0),q=i??k.disableHoverableContent,r=j??k.delayDuration,s=c.useRef(!1),[t,u]=(0,G.useControllableState)({prop:f,defaultProp:g??!1,onChange:a=>{a?(k.onOpen(),document.dispatchEvent(new CustomEvent(M))):k.onClose(),h?.(a)},caller:Q}),v=c.useMemo(()=>t?s.current?"delayed-open":"instant-open":"closed",[t]),w=c.useCallback(()=>{window.clearTimeout(p.current),p.current=0,s.current=!1,u(!0)},[u]),x=c.useCallback(()=>{window.clearTimeout(p.current),p.current=0,u(!1)},[u]),y=c.useCallback(()=>{window.clearTimeout(p.current),p.current=window.setTimeout(()=>{s.current=!0,u(!0),p.current=0},r)},[r,u]);return c.useEffect(()=>()=>{p.current&&(window.clearTimeout(p.current),p.current=0)},[]),(0,b.jsx)(D.Root,{...l,children:(0,b.jsx)(R,{scope:d,contentId:o,open:t,stateAttribute:v,trigger:m,onTriggerChange:n,onTriggerEnter:c.useCallback(()=>{k.isOpenDelayedRef.current?y():w()},[k.isOpenDelayedRef,y,w]),onTriggerLeave:c.useCallback(()=>{q?x():(window.clearTimeout(p.current),p.current=0)},[x,q]),onOpen:w,onClose:x,disableHoverableContent:q,children:e})})};T.displayName=Q;var U="TooltipTrigger",V=c.forwardRef((a,d)=>{let{__scopeTooltip:e,...f}=a,g=S(U,e),h=O(U,e),i=K(e),k=c.useRef(null),l=(0,z.useComposedRefs)(d,k,g.onTriggerChange),m=c.useRef(!1),n=c.useRef(!1),o=c.useCallback(()=>m.current=!1,[]);return c.useEffect(()=>()=>document.removeEventListener("pointerup",o),[o]),(0,b.jsx)(D.Anchor,{asChild:!0,...i,children:(0,b.jsx)(j.Primitive.button,{"aria-describedby":g.open?g.contentId:void 0,"data-state":g.stateAttribute,...f,ref:l,onPointerMove:(0,y.composeEventHandlers)(a.onPointerMove,a=>{"touch"!==a.pointerType&&(n.current||h.isPointerInTransitRef.current||(g.onTriggerEnter(),n.current=!0))}),onPointerLeave:(0,y.composeEventHandlers)(a.onPointerLeave,()=>{g.onTriggerLeave(),n.current=!1}),onPointerDown:(0,y.composeEventHandlers)(a.onPointerDown,()=>{g.open&&g.onClose(),m.current=!0,document.addEventListener("pointerup",o,{once:!0})}),onFocus:(0,y.composeEventHandlers)(a.onFocus,()=>{m.current||g.onOpen()}),onBlur:(0,y.composeEventHandlers)(a.onBlur,g.onClose),onClick:(0,y.composeEventHandlers)(a.onClick,g.onClose)})})});V.displayName=U;var W="TooltipPortal",[X,Y]=I(W,{forceMount:void 0}),Z=a=>{let{__scopeTooltip:c,forceMount:d,children:e,container:f}=a,g=S(W,c);return(0,b.jsx)(X,{scope:c,forceMount:d,children:(0,b.jsx)(F.Presence,{present:d||g.open,children:(0,b.jsx)(E.Portal,{asChild:!0,container:f,children:e})})})};Z.displayName=W;var $="TooltipContent",_=c.forwardRef((a,c)=>{let d=Y($,a.__scopeTooltip),{forceMount:e=d.forceMount,side:f="top",...g}=a,h=S($,a.__scopeTooltip);return(0,b.jsx)(F.Presence,{present:e||h.open,children:h.disableHoverableContent?(0,b.jsx)(ae,{side:f,...g,ref:c}):(0,b.jsx)(aa,{side:f,...g,ref:c})})}),aa=c.forwardRef((a,d)=>{let e=S($,a.__scopeTooltip),f=O($,a.__scopeTooltip),g=c.useRef(null),h=(0,z.useComposedRefs)(d,g),[i,j]=c.useState(null),{trigger:k,onClose:l}=e,m=g.current,{onPointerInTransitChange:n}=f,o=c.useCallback(()=>{j(null),n(!1)},[n]),p=c.useCallback((a,b)=>{let c=a.currentTarget,d={x:a.clientX,y:a.clientY},e=function(a,b){let c=Math.abs(b.top-a.y),d=Math.abs(b.bottom-a.y),e=Math.abs(b.right-a.x),f=Math.abs(b.left-a.x);switch(Math.min(c,d,e,f)){case f:return"left";case e:return"right";case c:return"top";case d:return"bottom";default:throw Error("unreachable")}}(d,c.getBoundingClientRect());j(function(a){let b=a.slice();return b.sort((a,b)=>a.x<b.x?-1:a.x>b.x?1:a.y<b.y?-1:1*!!(a.y>b.y)),function(a){if(a.length<=1)return a.slice();let b=[];for(let c=0;c<a.length;c++){let d=a[c];for(;b.length>=2;){let a=b[b.length-1],c=b[b.length-2];if((a.x-c.x)*(d.y-c.y)>=(a.y-c.y)*(d.x-c.x))b.pop();else break}b.push(d)}b.pop();let c=[];for(let b=a.length-1;b>=0;b--){let d=a[b];for(;c.length>=2;){let a=c[c.length-1],b=c[c.length-2];if((a.x-b.x)*(d.y-b.y)>=(a.y-b.y)*(d.x-b.x))c.pop();else break}c.push(d)}return(c.pop(),1===b.length&&1===c.length&&b[0].x===c[0].x&&b[0].y===c[0].y)?b:b.concat(c)}(b)}([...function(a,b,c=5){let d=[];switch(b){case"top":d.push({x:a.x-c,y:a.y+c},{x:a.x+c,y:a.y+c});break;case"bottom":d.push({x:a.x-c,y:a.y-c},{x:a.x+c,y:a.y-c});break;case"left":d.push({x:a.x+c,y:a.y-c},{x:a.x+c,y:a.y+c});break;case"right":d.push({x:a.x-c,y:a.y-c},{x:a.x-c,y:a.y+c})}return d}(d,e),...function(a){let{top:b,right:c,bottom:d,left:e}=a;return[{x:e,y:b},{x:c,y:b},{x:c,y:d},{x:e,y:d}]}(b.getBoundingClientRect())])),n(!0)},[n]);return c.useEffect(()=>()=>o(),[o]),c.useEffect(()=>{if(k&&m){let a=a=>p(a,m),b=a=>p(a,k);return k.addEventListener("pointerleave",a),m.addEventListener("pointerleave",b),()=>{k.removeEventListener("pointerleave",a),m.removeEventListener("pointerleave",b)}}},[k,m,p,o]),c.useEffect(()=>{if(i){let a=a=>{let b=a.target,c={x:a.clientX,y:a.clientY},d=k?.contains(b)||m?.contains(b),e=!function(a,b){let{x:c,y:d}=a,e=!1;for(let a=0,f=b.length-1;a<b.length;f=a++){let g=b[a],h=b[f],i=g.x,j=g.y,k=h.x,l=h.y;j>d!=l>d&&c<(k-i)*(d-j)/(l-j)+i&&(e=!e)}return e}(c,i);d?o():e&&(o(),l())};return document.addEventListener("pointermove",a),()=>document.removeEventListener("pointermove",a)}},[k,m,i,l,o]),(0,b.jsx)(ae,{...a,ref:h})}),[ab,ac]=I(Q,{isInside:!1}),ad=(0,d.createSlottable)("TooltipContent"),ae=c.forwardRef((a,d)=>{let{__scopeTooltip:e,children:f,"aria-label":g,onEscapeKeyDown:h,onPointerDownOutside:i,...j}=a,k=S($,e),l=K(e),{onClose:m}=k;return c.useEffect(()=>(document.addEventListener(M,m),()=>document.removeEventListener(M,m)),[m]),c.useEffect(()=>{if(k.trigger){let a=a=>{let b=a.target;b?.contains(k.trigger)&&m()};return window.addEventListener("scroll",a,{capture:!0}),()=>window.removeEventListener("scroll",a,{capture:!0})}},[k.trigger,m]),(0,b.jsx)(B.DismissableLayer,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:h,onPointerDownOutside:i,onFocusOutside:a=>a.preventDefault(),onDismiss:m,children:(0,b.jsxs)(D.Content,{"data-state":k.stateAttribute,...l,...j,ref:d,style:{...j.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,b.jsx)(ad,{children:f}),(0,b.jsx)(ab,{scope:e,isInside:!0,children:(0,b.jsx)(H.Root,{id:k.contentId,role:"tooltip",children:g||f})})]})})});_.displayName=$;var af="TooltipArrow",ag=c.forwardRef((a,c)=>{let{__scopeTooltip:d,...e}=a,f=K(d);return ac(af,d).isInside?null:(0,b.jsx)(D.Arrow,{...f,...e,ref:c})});function ah({delayDuration:a=0,...c}){return(0,b.jsx)(P,{"data-slot":"tooltip-provider",delayDuration:a,...c})}function ai({...a}){return(0,b.jsx)(ah,{children:(0,b.jsx)(T,{"data-slot":"tooltip",...a})})}function aj({...a}){return(0,b.jsx)(V,{"data-slot":"tooltip-trigger",...a})}function ak({className:a,sideOffset:c=0,children:d,...e}){return(0,b.jsx)(Z,{children:(0,b.jsxs)(_,{"data-slot":"tooltip-content",sideOffset:c,className:(0,g.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",a),...e,children:[d,(0,b.jsx)(ag,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}ag.displayName=af;let al=c.createContext(null);function am(){let a=c.useContext(al);if(!a)throw Error("useSidebar must be used within a SidebarProvider.");return a}function an({defaultOpen:a=!0,open:d,onOpenChange:e,className:f,style:h,children:i,...j}){let k=function(){let[a,b]=(0,c.useState)(!1);return(0,c.useEffect)(()=>{let a=()=>{b(window.innerWidth<768)};return a(),window.addEventListener("resize",a),()=>{window.removeEventListener("resize",a)}},[]),a}(),[l,m]=c.useState(!1),[n,o]=c.useState(a),p=d??n,q=c.useCallback(a=>{let b="function"==typeof a?a(p):a;e?e(b):o(b),document.cookie=`sidebar_state=${b}; path=/; max-age=604800`},[e,p]),r=c.useCallback(()=>k?m(a=>!a):q(a=>!a),[k,q,m]);c.useEffect(()=>{let a=a=>{"b"===a.key&&(a.metaKey||a.ctrlKey)&&(a.preventDefault(),r())};return window.addEventListener("keydown",a),()=>window.removeEventListener("keydown",a)},[r]);let s=p?"expanded":"collapsed",t=c.useMemo(()=>({state:s,open:p,setOpen:q,isMobile:k,openMobile:l,setOpenMobile:m,toggleSidebar:r}),[s,p,q,k,l,m,r]);return(0,b.jsx)(al.Provider,{value:t,children:(0,b.jsx)(ah,{delayDuration:0,children:(0,b.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...h},className:(0,g.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",f),...j,children:i})})})}function ao({side:a="left",variant:c="sidebar",collapsible:d="offcanvas",className:e,children:f,...h}){let{isMobile:i,state:j,openMobile:k,setOpenMobile:l}=am();return"none"===d?(0,b.jsx)("div",{"data-slot":"sidebar",className:(0,g.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",e),...h,children:f}):i?(0,b.jsx)(q,{open:k,onOpenChange:l,...h,children:(0,b.jsxs)(t,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:a,children:[(0,b.jsxs)(u,{className:"sr-only",children:[(0,b.jsx)(v,{children:"Sidebar"}),(0,b.jsx)(w,{children:"Displays the mobile sidebar."})]}),(0,b.jsx)("div",{className:"flex h-full w-full flex-col",children:f})]})}):(0,b.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":j,"data-collapsible":"collapsed"===j?d:"","data-variant":c,"data-side":a,"data-slot":"sidebar",children:[(0,b.jsx)("div",{"data-slot":"sidebar-gap",className:(0,g.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===c||"inset"===c?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,b.jsx)("div",{"data-slot":"sidebar-container",className:(0,g.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===a?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===c||"inset"===c?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",e),...h,children:(0,b.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:f})})]})}function ap({className:a,onClick:c,...d}){let{toggleSidebar:e}=am();return(0,b.jsxs)(h.Button,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,g.cn)("size-7",a),onClick:a=>{c?.(a),e()},...d,children:[(0,b.jsx)(f,{}),(0,b.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function aq({className:a,...c}){let{toggleSidebar:d}=am();return(0,b.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:d,title:"Toggle Sidebar",className:(0,g.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",a),...c})}function ar({className:a,...c}){return(0,b.jsx)("main",{"data-slot":"sidebar-inset",className:(0,g.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",a),...c})}function as({className:a,...c}){return(0,b.jsx)(i.Input,{"data-slot":"sidebar-input","data-sidebar":"input",className:(0,g.cn)("bg-background h-8 w-full shadow-none",a),...c})}function at({className:a,...c}){return(0,b.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,g.cn)("flex flex-col gap-2 p-2",a),...c})}function au({className:a,...c}){return(0,b.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,g.cn)("flex flex-col gap-2 p-2",a),...c})}function av({className:a,...c}){return(0,b.jsx)(n,{"data-slot":"sidebar-separator","data-sidebar":"separator",className:(0,g.cn)("bg-sidebar-border mx-2 w-auto",a),...c})}function aw({className:a,...c}){return(0,b.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,g.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",a),...c})}function ax({className:a,...c}){return(0,b.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,g.cn)("relative flex w-full min-w-0 flex-col p-2",a),...c})}function ay({className:a,asChild:c=!1,...e}){let f=c?d.Slot:"div";return(0,b.jsx)(f,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,g.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",a),...e})}function az({className:a,asChild:c=!1,...e}){let f=c?d.Slot:"button";return(0,b.jsx)(f,{"data-slot":"sidebar-group-action","data-sidebar":"group-action",className:(0,g.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 md:after:hidden","group-data-[collapsible=icon]:hidden",a),...e})}function aA({className:a,...c}){return(0,b.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,g.cn)("w-full text-sm",a),...c})}function aB({className:a,...c}){return(0,b.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,g.cn)("flex w-full min-w-0 flex-col gap-1",a),...c})}function aC({className:a,...c}){return(0,b.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,g.cn)("group/menu-item relative",a),...c})}let aD=(0,e.cva)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function aE({asChild:a=!1,isActive:c=!1,variant:e="default",size:f="default",tooltip:h,className:i,...j}){let k=a?d.Slot:"button",{isMobile:l,state:m}=am(),n=(0,b.jsx)(k,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":f,"data-active":c,className:(0,g.cn)(aD({variant:e,size:f}),i),...j});return h?("string"==typeof h&&(h={children:h}),(0,b.jsxs)(ai,{children:[(0,b.jsx)(aj,{asChild:!0,children:n}),(0,b.jsx)(ak,{side:"right",align:"center",hidden:"collapsed"!==m||l,...h})]})):n}function aF({className:a,asChild:c=!1,showOnHover:e=!1,...f}){let h=c?d.Slot:"button";return(0,b.jsx)(h,{"data-slot":"sidebar-menu-action","data-sidebar":"menu-action",className:(0,g.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 md:after:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e&&"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0",a),...f})}function aG({className:a,...c}){return(0,b.jsx)("div",{"data-slot":"sidebar-menu-badge","data-sidebar":"menu-badge",className:(0,g.cn)("text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",a),...c})}function aH({className:a,showIcon:d=!1,...e}){let f=c.useMemo(()=>`${Math.floor(40*Math.random())+50}%`,[]);return(0,b.jsxs)("div",{"data-slot":"sidebar-menu-skeleton","data-sidebar":"menu-skeleton",className:(0,g.cn)("flex h-8 items-center gap-2 rounded-md px-2",a),...e,children:[d&&(0,b.jsx)(x,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,b.jsx)(x,{className:"h-4 max-w-(--skeleton-width) flex-1","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":f}})]})}function aI({className:a,...c}){return(0,b.jsx)("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:(0,g.cn)("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",a),...c})}function aJ({className:a,...c}){return(0,b.jsx)("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:(0,g.cn)("group/menu-sub-item relative",a),...c})}function aK({asChild:a=!1,size:c="md",isActive:e=!1,className:f,...h}){let i=a?d.Slot:"a";return(0,b.jsx)(i,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":c,"data-active":e,className:(0,g.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===c&&"text-xs","md"===c&&"text-sm","group-data-[collapsible=icon]:hidden",f),...h})}},59611,a=>{"use strict";a.s(["Header",()=>i],59611);var b=a.i(68116),c=a.i(81223);let d=(0,a.i(621).default)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);var e=a.i(23814),f=a.i(2979),g=a.i(78184),h=a.i(64743);function i(){let{data:a}=(0,c.useSession)();return(0,b.jsx)("header",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,b.jsx)(h.SidebarTrigger,{}),(0,b.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Dashboard"})]}),(0,b.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,b.jsxs)("div",{className:"relative",children:[(0,b.jsx)(e.Search,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,b.jsx)(g.Input,{placeholder:"Search...",className:"pl-10 w-64"})]}),(0,b.jsx)(f.Button,{variant:"ghost",size:"icon",children:(0,b.jsx)(d,{className:"h-5 w-5"})}),a?.user&&(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)("div",{className:"h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center",children:(0,b.jsx)("span",{className:"text-white text-sm font-medium",children:a.user.username?.charAt(0)||"U"})}),(0,b.jsx)("span",{className:"text-sm font-medium text-gray-700",children:a.user.username})]})]})]})})}},12626,a=>{"use strict";a.s(["AppSidebar",()=>s],12626);var b=a.i(68116),c=a.i(33055),d=a.i(50395),e=a.i(81223),f=a.i(621);let g=(0,f.default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var h=a.i(3666);let i=(0,f.default)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),j=(0,f.default)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);var k=a.i(20762),l=a.i(72376);let m=(0,f.default)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),n=(0,f.default)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),o=(0,f.default)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var p=a.i(2979),q=a.i(64743);let r=[{name:"Dashboard",href:"/dashboard",icon:g},{name:"Membres",href:"/dashboard/members",icon:o,roles:["SECRETARY_GENERAL","CONTROLLER"]},{name:"Sessions",href:"/dashboard/sessions",icon:k.Calendar,roles:["SECRETARY_GENERAL","CONTROLLER","CASHIER"]},{name:"Caisses",href:"/dashboard/caisses",icon:l.Wallet,roles:["SECRETARY_GENERAL","CONTROLLER"]},{name:"Paiements",href:"/dashboard/payments",icon:n,roles:["SECRETARY_GENERAL","CONTROLLER","CASHIER"]},{name:"Utilisateurs",href:"/dashboard/users",icon:h.Users,roles:["SECRETARY_GENERAL"]},{name:"Rapports",href:"/dashboard/reports",icon:m,roles:["SECRETARY_GENERAL","CONTROLLER"]},{name:"Paramètres",href:"/dashboard/settings",icon:i,roles:["SECRETARY_GENERAL"]}];function s(){let{state:a}=(0,q.useSidebar)(),f=(0,d.usePathname)(),{data:g}=(0,e.useSession)(),h=r.filter(a=>{if(!a.roles)return!0;let b=g?.user?.role?.toUpperCase();return b&&a.roles.includes(b)});return(0,b.jsxs)(q.Sidebar,{collapsible:"icon",children:[(0,b.jsx)(q.SidebarHeader,{children:(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)("div",{className:"flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground",children:(0,b.jsx)(l.Wallet,{className:"size-4"})}),(0,b.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,b.jsx)("span",{className:"truncate font-semibold",children:"Tontine"}),(0,b.jsx)("span",{className:"truncate text-xs",children:"Gestion de tontines"})]})]})}),(0,b.jsx)(q.SidebarContent,{children:(0,b.jsxs)(q.SidebarGroup,{children:[(0,b.jsx)(q.SidebarGroupLabel,{children:"Navigation"}),(0,b.jsx)(q.SidebarGroupContent,{children:(0,b.jsx)(q.SidebarMenu,{children:h.map(a=>{let d=a.icon,e=f===a.href||f.startsWith(`${a.href}/`);return(0,b.jsx)(q.SidebarMenuItem,{children:(0,b.jsx)(q.SidebarMenuButton,{asChild:!0,isActive:e,children:(0,b.jsxs)(c.default,{href:a.href,children:[(0,b.jsx)(d,{}),(0,b.jsx)("span",{children:a.name})]})})},a.name)})})})]})}),(0,b.jsx)(q.SidebarFooter,{children:g?.user&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)("div",{className:"flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-accent text-sidebar-accent-foreground",children:(0,b.jsx)("span",{className:"text-xs font-medium",children:g.user.username?.charAt(0).toUpperCase()})}),(0,b.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,b.jsx)("span",{className:"truncate font-semibold",children:g.user.username}),(0,b.jsx)("span",{className:"truncate text-xs",children:g.user.role})]})]}),(0,b.jsxs)(p.Button,{variant:"destructive",onClick:()=>{(0,e.signOut)({callbackUrl:"/auth/signin"})},className:"w-full justify-start",children:[(0,b.jsx)(j,{className:"mr-2 h-4 w-4"}),"expanded"===a?"Sign out":null]})]})})]})}}];

//# sourceMappingURL=frontend_src_components_95ee152e._.js.map