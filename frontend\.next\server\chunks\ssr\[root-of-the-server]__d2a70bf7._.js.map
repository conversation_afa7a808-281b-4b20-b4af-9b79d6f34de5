{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,yXAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,yXAAC;YACC,aAAU;YACV,WAAW,IAAA,qIAAE,EAAC,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,yXAAC,uSAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,yXAAC,ySAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,yXAAC,0SAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,yXAAC,ySAA4B;kBAC3B,cAAA,yXAAC,0SAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,IAAA,qIAAE,EACX,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,yXAAC,wSAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,yXAAC,uSAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,IAAA,qIAAE,EACX,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,yXAAC,+SAAkC;QACjC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,yXAAC;gBAAK,WAAU;0BACd,cAAA,yXAAC,gTAAmC;8BAClC,cAAA,yXAAC,qTAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,yXAAC,6SAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,yXAAC,4SAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,gTACA;QAED,GAAG,KAAK;;0BAET,yXAAC;gBAAK,WAAU;0BACd,cAAA,yXAAC,gTAAmC;8BAClC,cAAA,yXAAC,wTAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,yXAAC,wSAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,IAAA,qIAAE,EACX,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,yXAAC,4SAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,yXAAC,sSAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,yXAAC,6SAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,IAAA,qIAAE,EACX,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,yXAAC,8UAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,yXAAC,6SAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,yXAAC,mSAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,yXAAC,oSAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,yXAAC,oSAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,yXAAC,sSAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,qIAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,yXAAC,mSAAoB;gBAAC,OAAO;0BAC3B,cAAA,yXAAC,2UAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,yXAAC,qSAAsB;kBACrB,cAAA,yXAAC,sSAAuB;YACtB,aAAU;YACV,WAAW,IAAA,qIAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,yXAAC;;;;;8BACD,yXAAC,uSAAwB;oBACvB,WAAW,IAAA,qIAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,yXAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,yXAAC,oSAAqB;QACpB,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,yXAAC,mSAAoB;QACnB,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,yXAAC;gBAAK,WAAU;0BACd,cAAA,yXAAC,4SAA6B;8BAC5B,cAAA,yXAAC,qTAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,yXAAC,uSAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,yXAAC,wSAAyB;QACxB,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,yXAAC,6SAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,yXAAC,qUAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,yXAAC,+SAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,yXAAC,2UAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,IAAA,8PAAG,EACvB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,yTAAI,GAAG;IAE9B,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/auth.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from \"next-auth/providers/credentials\";\nimport { z } from \"zod\";\nimport { apiService } from \"./api\";\nimport { decodeJwt } from \"./utils\";\n\nconst loginSchema = z.object({\n\tusername: z.string().min(1, \"Username is required\"),\n\tpassword: z.string().min(1, \"Password is required\"),\n});\n\nexport const { handlers, signIn, signOut, auth } = NextAuth({\n\tproviders: [\n\t\tCredentialsProvider({\n\t\t\tname: \"credentials\",\n\t\t\tcredentials: {\n\t\t\t\tusername: { label: \"Username\", type: \"text\" },\n\t\t\t\tpassword: { label: \"Password\", type: \"password\" },\n\t\t\t},\n\t\t\tasync authorize(credentials) {\n\t\t\t\ttry {\n\t\t\t\t\tconst { username, password } = loginSchema.parse(credentials);\n\n\t\t\t\t\t// Authentification avec l'API backend\n\t\t\t\t\tconst response = await apiService.login({ username, password });\n\t\t\t\t\tconst jwt = decodeJwt(response.access_token);\n\t\t\t\t\tif(!jwt) return null;\n\t\t\t\t\tconst jwtPayload = jwt.payload as unknown as { sub: string; username: string; role:  'secretary_general' | 'controller' | 'cashier' };\n\t\t\t\t\t\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tid: jwtPayload.sub,\n\t\t\t\t\t\t\tusername: jwtPayload.username,\n\t\t\t\t\t\t\trole: jwtPayload.role,\n\t\t\t\t\t\t\taccessToken: response.access_token,\n\t\t\t\t\t}\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"Erreur d'authentification:\", error);\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\t\t}),\n\t],\n\tpages: {\n\t\tsignIn: \"/auth/signin\",\n\t},\n\tcallbacks: {\n\t\tauthorized: ({ auth }) => !!auth,\n\t\tasync jwt({ token, user }) {\n\t\t\tif (user) {\n\t\t\t\ttoken.username = user.username;\n\t\t\t\ttoken.role = user.role;\n\t\t\t\ttoken.accessToken = user.accessToken;\n\t\t\t}\n\t\t\treturn token;\n\t\t},\n\t\tasync session({ session, token }) {\n\t\t\tif (token) {\n\t\t\t\tsession.user.id = token.sub || \"\";\n\t\t\t\tsession.user.username = token.username as string;\n\t\t\t\tsession.user.role = token.role as  'secretary_general' | 'controller' | 'cashier';\n\t\t\t\tsession.accessToken = token.accessToken as string;\n\t\t\t}\n\t\t\treturn session;\n\t\t},\n\t},\n\tsession: {\n\t\tstrategy: \"jwt\",\n\t},\n});\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,cAAc,8OAAC,CAAC,MAAM,CAAC;IAC5B,UAAU,8OAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,UAAU,8OAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC7B;AAEO,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAA,wRAAQ,EAAC;IAC3D,WAAW;QACV,IAAA,6OAAmB,EAAC;YACnB,MAAM;YACN,aAAa;gBACZ,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAO;gBAC5C,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YACjD;YACA,MAAM,WAAU,WAAW;gBAC1B,IAAI;oBACH,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,YAAY,KAAK,CAAC;oBAEjD,sCAAsC;oBACtC,MAAM,WAAW,MAAM,2IAAU,CAAC,KAAK,CAAC;wBAAE;wBAAU;oBAAS;oBAC7D,MAAM,MAAM,IAAA,4IAAS,EAAC,SAAS,YAAY;oBAC3C,IAAG,CAAC,KAAK,OAAO;oBAChB,MAAM,aAAa,IAAI,OAAO;oBAE7B,OAAO;wBACN,IAAI,WAAW,GAAG;wBAClB,UAAU,WAAW,QAAQ;wBAC7B,MAAM,WAAW,IAAI;wBACrB,aAAa,SAAS,YAAY;oBACpC;gBAED,EAAE,OAAO,OAAO;oBACf,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,OAAO;gBACR;YACD;QACD;KACA;IACD,OAAO;QACN,QAAQ;IACT;IACA,WAAW;QACV,YAAY,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC,CAAC;QAC5B,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACxB,IAAI,MAAM;gBACT,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,WAAW,GAAG,KAAK,WAAW;YACrC;YACA,OAAO;QACR;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC/B,IAAI,OAAO;gBACV,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI;gBAC/B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,WAAW,GAAG,MAAM,WAAW;YACxC;YACA,OAAO;QACR;IACD;IACA,SAAS;QACR,UAAU;IACX;AACD", "debugId": null}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/api.ts"], "sourcesContent": ["import { signOut } from \"next-auth/react\";\nimport { signOut as ServerSignOut } from \"./auth\";\nimport { Caisse, Session } from \"@/types\";\n\n// Configuration de l'API\nexport const API_BASE_URL =\n\tprocess.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000\";\n\n// Types pour l'authentification\nexport interface LoginRequest {\n\tusername: string;\n\tpassword: string;\n}\n\nexport interface LoginResponse {\n\taccess_token: string;\n\tuser: {\n\t\tid: string;\n\t\tusername: string;\n\t\tnom: string;\n\t\tprenom: string;\n\t\temail: string;\n\t\trole: \"admin\" | \"tresorier\" | \"membre\";\n\t\tstatut: \"actif\" | \"en_attente\" | \"suspendu\";\n\t};\n}\n\nexport interface RegisterRequest {\n\tusername: string;\n\tpassword: string;\n\trole: string;\n\t// statut?: \"actif\" | \"en_attente\" | \"suspendu\";\n}\n\nexport interface ApiError {\n\tmessage: string;\n\tstatusCode: number;\n\terror?: string;\n}\n\n// Service API\nexport class ApiService {\n\tprivate baseUrl: string;\n\n\tconstructor(baseUrl: string = API_BASE_URL) {\n\t\tthis.baseUrl = baseUrl;\n\t}\n\n\tprivate async request<T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\tconst url = `${this.baseUrl}${endpoint}`;\n\n\t\tconst config: RequestInit = {\n\t\t\theaders: {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t...options.headers,\n\t\t\t},\n\t\t\t...options,\n\t\t};\n\n\t\ttry {\n\t\t\tconst response = await fetch(url, config);\n\n\t\t\tif (!response.ok) {\n\t\t\t\tconst errorData: ApiError = await response.json().catch(() => ({\n\t\t\t\t\tmessage: \"Une erreur est survenue\",\n\t\t\t\t\tstatusCode: response.status,\n\t\t\t\t}));\n\n\t\t\t\t// 🚨 Gestion du cas 401\n\t\t\t\tif (response.status === 401) {\n\t\t\t\t\t// Si côté client → on déconnecte\n\t\t\t\t\tif (typeof window !== \"undefined\") {\n\t\t\t\t\t\tawait signOut({ callbackUrl: \"/auth/signin\" });\n\t\t\t\t\t} else {\n\t\t\t\t\t\tawait ServerSignOut({ redirectTo: \"/auth/signin\" });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthrow new Error(errorData.message || `HTTP ${response.status}`);\n\t\t\t}\n\n\t\t\treturn await response.json();\n\t\t} catch (error) {\n\t\t\tconsole.log(error);\n\n\t\t\tif (error instanceof Error) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t\tthrow new Error(\"Erreur de connexion au serveur\");\n\t\t}\n\t}\n\n\t// Authentification\n\tasync login(credentials: LoginRequest): Promise<LoginResponse> {\n\t\treturn this.request<LoginResponse>(\"/auth/login\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(credentials),\n\t\t});\n\t}\n\n\tasync register(\n\t\tuserData: RegisterRequest,\n\t): Promise<{ message: string; user: any }> {\n\t\treturn this.request<{ message: string; user: any }>(\"/auth/register\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(userData),\n\t\t});\n\t}\n\n\t// Méthodes avec authentification\n\tasync authenticatedRequest<T>(\n\t\tendpoint: string,\n\t\ttoken: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\treturn this.request<T>(endpoint, {\n\t\t\t...options,\n\t\t\theaders: {\n\t\t\t\t...options.headers,\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\tAuthorization: `Bearer ${token}`,\n\t\t\t},\n\t\t});\n\t}\n\n\t// Utilisateurs\n\tasync getUsers(token: string): Promise<any[]> {\n\t\treturn this.authenticatedRequest<any[]>(\"/users\", token);\n\t}\n\n\tasync getUser(id: string, token: string): Promise<any> {\n\t\treturn this.authenticatedRequest<any>(`/users/${id}`, token);\n\t}\n\n\tasync getSessions(token: string): Promise<Session[]> {\n\t\treturn this.authenticatedRequest<Session[]>(\"/sessions\", token);\n\t}\n\n\tasync getCaisses(token: string): Promise<Caisse[]> {\n\t\treturn this.authenticatedRequest<Caisse[]>(\"/caisses\", token);\n\t}\n}\n\n// Instance par défaut\nexport const apiService = new ApiService();\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAIO,MAAM,eACZ,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAmC7B,MAAM;IACJ,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC3C,IAAI,CAAC,OAAO,GAAG;IAChB;IAEA,MAAc,QACb,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACZ;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC3B,SAAS;gBACR,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACnB;YACA,GAAG,OAAO;QACX;QAEA,IAAI;YACH,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACjB,MAAM,YAAsB,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;wBAC9D,SAAS;wBACT,YAAY,SAAS,MAAM;oBAC5B,CAAC;gBAED,wBAAwB;gBACxB,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC5B,iCAAiC;oBACjC;;yBAEO;wBACN,MAAM,IAAA,yIAAa,EAAC;4BAAE,YAAY;wBAAe;oBAClD;gBACD;gBACA,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;YAC/D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC3B,EAAE,OAAO,OAAO;YACf,QAAQ,GAAG,CAAC;YAEZ,IAAI,iBAAiB,OAAO;gBAC3B,MAAM;YACP;YACA,MAAM,IAAI,MAAM;QACjB;IACD;IAEA,mBAAmB;IACnB,MAAM,MAAM,WAAyB,EAA0B;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAgB,eAAe;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,MAAM,SACL,QAAyB,EACiB;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAiC,kBAAkB;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,iCAAiC;IACjC,MAAM,qBACL,QAAgB,EAChB,KAAa,EACb,UAAuB,CAAC,CAAC,EACZ;QACb,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAChC,GAAG,OAAO;YACV,SAAS;gBACR,GAAG,QAAQ,OAAO;gBAClB,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;YACjC;QACD;IACD;IAEA,eAAe;IACf,MAAM,SAAS,KAAa,EAAkB;QAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAQ,UAAU;IACnD;IAEA,MAAM,QAAQ,EAAU,EAAE,KAAa,EAAgB;QACtD,OAAO,IAAI,CAAC,oBAAoB,CAAM,CAAC,OAAO,EAAE,IAAI,EAAE;IACvD;IAEA,MAAM,YAAY,KAAa,EAAsB;QACpD,OAAO,IAAI,CAAC,oBAAoB,CAAY,aAAa;IAC1D;IAEA,MAAM,WAAW,KAAa,EAAqB;QAClD,OAAO,IAAI,CAAC,oBAAoB,CAAW,YAAY;IACxD;AACD;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/use-api.ts"], "sourcesContent": ["import { useSession } from \"next-auth/react\";\nimport { apiService } from \"@/lib/api\";\nimport {\n\tSession,\n\tCaisse,\n\tReunion,\n\tMember,\n\tPayment,\n\tSessionMember,\n\tCreateSessionDto,\n\tUpdateSessionDto,\n\tCreateCaisseDto,\n\tUpdateCaisseDto,\n\tUpdateReunionDto,\n\tCreateMemberDto,\n\tUpdateMemberDto,\n\tCreatePaymentDto,\n\tCreateSessionMemberDto,\n\tMemberDebrief,\n\tPaymentFilters,\n} from \"@/types\";\n\nexport function useApi() {\n\tconst { data: session } = useSession();\n\n\tconst authenticatedRequest = async <T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> => {\n\t\tif (!session?.accessToken) {\n\t\t\tthrow new Error(\"Non authentifié\");\n\t\t}\n\n\t\treturn apiService.authenticatedRequest<T>(\n\t\t\tendpoint,\n\t\t\tsession.accessToken,\n\t\t\toptions,\n\t\t);\n\t};\n\n\treturn {\n\t\t// Méthodes d'authentification (pas besoin de token)\n\t\tlogin: apiService.login.bind(apiService),\n\t\tregister: apiService.register.bind(apiService),\n\n\t\t// Méthodes authentifiées\n\t\tauthenticatedRequest,\n\n\t\t// Raccourcis pour les endpoints courants\n\t\tgetUsers: () => authenticatedRequest<any[]>(\"/users\"),\n\t\tgetUser: (id: string) => authenticatedRequest<any>(`/users/${id}`),\n\t\tcreateUser: (userData: any) =>\n\t\t\tauthenticatedRequest<any>(\"/users\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tupdateUser: (id: string, userData: any) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tdeleteUser: (id: string) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Sessions\n\t\tgetSessions: () => authenticatedRequest<Session[]>(\"/sessions\"),\n\t\tgetSession: (id: string) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`),\n\t\tcreateSession: (sessionData: CreateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(\"/sessions\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tupdateSession: (id: string, sessionData: UpdateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tdeleteSession: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Caisses\n\t\tgetCaisses: () => authenticatedRequest<Caisse[]>(\"/caisses\"),\n\t\tgetCaisse: (id: string) => authenticatedRequest<Caisse>(`/caisses/${id}`),\n\t\tcreateCaisse: (caisseData: CreateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(\"/caisses\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tupdateCaisse: (id: string, caisseData: UpdateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tdeleteCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\temargerCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}/emarger`, {\n\t\t\t\tmethod: \"POST\",\n\t\t\t}),\n\n\t\t// Réunions\n\t\tgetReunions: () => authenticatedRequest<Reunion[]>(\"/reunions\"),\n\t\tgetReunion: (id: string) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`),\n\t\tupdateReunion: (id: string, reunionData: UpdateReunionDto) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(reunionData),\n\t\t\t}),\n\n\t\t// Members\n\t\tgetMembers: () => authenticatedRequest<Member[]>(\"/members\"),\n\t\tgetMember: (id: string) => authenticatedRequest<Member>(`/members/${id}`),\n\t\tcreateMember: (memberData: CreateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(\"/members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tupdateMember: (id: string, memberData: UpdateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(`/members/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tdeleteMember: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/members/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\tgetMemberDebrief: (id: string, filters?: PaymentFilters) => {\n\t\t\tconst params = new URLSearchParams();\n\t\t\tif (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);\n\t\t\tif (filters?.dateTo) params.append('dateTo', filters.dateTo);\n\t\t\tif (filters?.sessionId) params.append('sessionId', filters.sessionId);\n\t\t\tconst query = params.toString() ? `?${params.toString()}` : '';\n\t\t\treturn authenticatedRequest<MemberDebrief>(`/members/${id}/debrief${query}`);\n\t\t},\n\n\t\t// Payments\n\t\tcreatePayment: (paymentData: CreatePaymentDto) =>\n\t\t\tauthenticatedRequest<Payment>(\"/payments\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(paymentData),\n\t\t\t}),\n\n\t\t// Session Members (inscription des membres aux sessions)\n\t\tgetSessionMembers: (sessionId: string) =>\n\t\t\tauthenticatedRequest<SessionMember[]>(`/sessions/${sessionId}/members`),\n\t\taddSessionMember: (sessionMemberData: CreateSessionMemberDto) =>\n\t\t\tauthenticatedRequest<SessionMember>(\"/session-members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionMemberData),\n\t\t\t}),\n\t\tremoveSessionMember: (sessionId: string, memberId: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${sessionId}/members/${memberId}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t};\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAqBO,SAAS;IACf,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IAEpC,MAAM,uBAAuB,OAC5B,UACA,UAAuB,CAAC,CAAC;QAEzB,IAAI,CAAC,SAAS,aAAa;YAC1B,MAAM,IAAI,MAAM;QACjB;QAEA,OAAO,2IAAU,CAAC,oBAAoB,CACrC,UACA,QAAQ,WAAW,EACnB;IAEF;IAEA,OAAO;QACN,oDAAoD;QACpD,OAAO,2IAAU,CAAC,KAAK,CAAC,IAAI,CAAC,2IAAU;QACvC,UAAU,2IAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,2IAAU;QAE7C,yBAAyB;QACzB;QAEA,yCAAyC;QACzC,UAAU,IAAM,qBAA4B;QAC5C,SAAS,CAAC,KAAe,qBAA0B,CAAC,OAAO,EAAE,IAAI;QACjE,YAAY,CAAC,WACZ,qBAA0B,UAAU;gBACnC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,YAAY,CAAC,IAAY,WACxB,qBAA0B,CAAC,OAAO,EAAE,IAAI,EAAE;gBACzC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,YAAY,CAAC,KACZ,qBAA0B,CAAC,OAAO,EAAE,IAAI,EAAE;gBACzC,QAAQ;YACT;QAED,WAAW;QACX,aAAa,IAAM,qBAAgC;QACnD,YAAY,CAAC,KACZ,qBAA8B,CAAC,UAAU,EAAE,IAAI;QAChD,eAAe,CAAC,cACf,qBAA8B,aAAa;gBAC1C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,eAAe,CAAC,IAAY,cAC3B,qBAA8B,CAAC,UAAU,EAAE,IAAI,EAAE;gBAChD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,eAAe,CAAC,KACf,qBAA2B,CAAC,UAAU,EAAE,IAAI,EAAE;gBAC7C,QAAQ;YACT;QAED,UAAU;QACV,YAAY,IAAM,qBAA+B;QACjD,WAAW,CAAC,KAAe,qBAA6B,CAAC,SAAS,EAAE,IAAI;QACxE,cAAc,CAAC,aACd,qBAA6B,YAAY;gBACxC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,IAAY,aAC1B,qBAA6B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC9C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,KACd,qBAA2B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC5C,QAAQ;YACT;QACD,eAAe,CAAC,KACf,qBAA6B,CAAC,SAAS,EAAE,GAAG,QAAQ,CAAC,EAAE;gBACtD,QAAQ;YACT;QAED,WAAW;QACX,aAAa,IAAM,qBAAgC;QACnD,YAAY,CAAC,KACZ,qBAA8B,CAAC,UAAU,EAAE,IAAI;QAChD,eAAe,CAAC,IAAY,cAC3B,qBAA8B,CAAC,UAAU,EAAE,IAAI,EAAE;gBAChD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QAED,UAAU;QACV,YAAY,IAAM,qBAA+B;QACjD,WAAW,CAAC,KAAe,qBAA6B,CAAC,SAAS,EAAE,IAAI;QACxE,cAAc,CAAC,aACd,qBAA6B,YAAY;gBACxC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,IAAY,aAC1B,qBAA6B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC9C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,KACd,qBAA2B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC5C,QAAQ;YACT;QACD,kBAAkB,CAAC,IAAY;YAC9B,MAAM,SAAS,IAAI;YACnB,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YACjE,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YAC3D,IAAI,SAAS,WAAW,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;YACpE,MAAM,QAAQ,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG;YAC5D,OAAO,qBAAoC,CAAC,SAAS,EAAE,GAAG,QAAQ,EAAE,OAAO;QAC5E;QAEA,WAAW;QACX,eAAe,CAAC,cACf,qBAA8B,aAAa;gBAC1C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QAED,yDAAyD;QACzD,mBAAmB,CAAC,YACnB,qBAAsC,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC;QACvE,kBAAkB,CAAC,oBAClB,qBAAoC,oBAAoB;gBACvD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,qBAAqB,CAAC,WAAmB,WACxC,qBAA2B,CAAC,UAAU,EAAE,UAAU,SAAS,EAAE,UAAU,EAAE;gBACxE,QAAQ;YACT;IACF;AACD", "debugId": null}}, {"offset": {"line": 997, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/queries/use-sessions.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\nimport { useSession } from \"next-auth/react\";\nimport { useApi } from \"@/hooks/use-api\";\nimport type { Session, CreateSessionDto, UpdateSessionDto } from \"@/types\";\n\n// Query Keys\nexport const sessionKeys = {\n\tall: [\"sessions\"] as const,\n\tlists: () => [...sessionKeys.all, \"list\"] as const,\n\tlist: (filters: Record<string, any>) =>\n\t\t[...sessionKeys.lists(), { filters }] as const,\n\tdetails: () => [...sessionKeys.all, \"detail\"] as const,\n\tdetail: (id: string) => [...sessionKeys.details(), id] as const,\n\tmembers: (id: string) => [...sessionKeys.detail(id), \"members\"] as const,\n};\n\n// Hooks\nexport function useSessions() {\n\tconst { data: session } = useSession();\n\tconst api = useApi();\n\n\treturn useQuery({\n\t\tqueryKey: sessionKeys.lists(),\n\t\tqueryFn: () => api.getSessions(),\n\t\tenabled: !!session?.accessToken,\n\t\tstaleTime: 5 * 60 * 1000, // 5 minutes\n\t});\n}\n\nexport function useSessionDetail(sessionId: string) {\n\tconst { data: session } = useSession();\n\tconst api = useApi();\n\n\treturn useQuery({\n\t\tqueryKey: sessionKeys.detail(sessionId),\n\t\tqueryFn: () => api.getSession(sessionId),\n\t\tenabled: !!session?.accessToken && !!sessionId,\n\t\tstaleTime: 5 * 60 * 1000, // 5 minutes\n\t});\n}\n\nexport function useSessionMembers(sessionId: string) {\n\tconst { data: session } = useSession();\n\tconst api = useApi();\n\n\treturn useQuery({\n\t\tqueryKey: sessionKeys.members(sessionId),\n\t\tqueryFn: () => api.getSessionMembers(sessionId),\n\t\tenabled: !!session?.accessToken && !!sessionId,\n\t\tstaleTime: 2 * 60 * 1000, // 2 minutes\n\t});\n}\n\n// Mutations\nexport function useCreateSession() {\n\tconst queryClient = useQueryClient();\n\tconst api = useApi();\n\n\treturn useMutation({\n\t\tmutationFn: (data: CreateSessionDto) => api.createSession(data),\n\t\tonSuccess: () => {\n\t\t\t// Invalidate and refetch sessions list\n\t\t\tqueryClient.invalidateQueries({ queryKey: sessionKeys.lists() });\n\t\t},\n\t\tonError: (error) => {\n\t\t\tconsole.error(\"Error creating session:\", error);\n\t\t},\n\t});\n}\n\nexport function useUpdateSession() {\n\tconst queryClient = useQueryClient();\n\tconst api = useApi();\n\n\treturn useMutation({\n\t\tmutationFn: ({ id, data }: { id: string; data: UpdateSessionDto }) =>\n\t\t\tapi.updateSession(id, data),\n\t\tonSuccess: (_, { id }) => {\n\t\t\t// Invalidate specific session and sessions list\n\t\t\tqueryClient.invalidateQueries({ queryKey: sessionKeys.detail(id) });\n\t\t\tqueryClient.invalidateQueries({ queryKey: sessionKeys.lists() });\n\t\t},\n\t\tonError: (error) => {\n\t\t\tconsole.error(\"Error updating session:\", error);\n\t\t},\n\t});\n}\n\nexport function useDeleteSession() {\n\tconst queryClient = useQueryClient();\n\tconst api = useApi();\n\n\treturn useMutation({\n\t\tmutationFn: (sessionId: string) => api.deleteSession(sessionId),\n\t\tonSuccess: (_, sessionId) => {\n\t\t\t// Remove from cache and invalidate lists\n\t\t\tqueryClient.removeQueries({ queryKey: sessionKeys.detail(sessionId) });\n\t\t\tqueryClient.invalidateQueries({ queryKey: sessionKeys.lists() });\n\t\t},\n\t\tonError: (error) => {\n\t\t\tconsole.error(\"Error deleting session:\", error);\n\t\t},\n\t});\n}\n\nexport function useAddSessionMember() {\n\tconst queryClient = useQueryClient();\n\tconst api = useApi();\n\n\treturn useMutation({\n\t\tmutationFn: (data: {\n\t\t\tsessionId: string;\n\t\t\tmemberId: string;\n\t\t\tpartFixe: number;\n\t\t}) =>\n\t\t\tapi.addSessionMember({\n\t\t\t\tsessionId: data.sessionId,\n\t\t\t\tmemberId: data.memberId,\n\t\t\t\tparts: data.partFixe,\n\t\t\t}),\n\t\tonSuccess: (_, { sessionId }) => {\n\t\t\t// Invalidate session members\n\t\t\tqueryClient.invalidateQueries({\n\t\t\t\tqueryKey: sessionKeys.members(sessionId),\n\t\t\t});\n\t\t},\n\t\tonError: (error) => {\n\t\t\tconsole.error(\"Error adding session member:\", error);\n\t\t},\n\t});\n}\n\nexport function useRemoveSessionMember() {\n\tconst queryClient = useQueryClient();\n\tconst api = useApi();\n\n\treturn useMutation({\n\t\tmutationFn: ({\n\t\t\tsessionId,\n\t\t\tmemberId,\n\t\t}: {\n\t\t\tsessionId: string;\n\t\t\tmemberId: string;\n\t\t}) => api.removeSessionMember(sessionId, memberId),\n\t\tonSuccess: (_, { sessionId }) => {\n\t\t\t// Invalidate session members\n\t\t\tqueryClient.invalidateQueries({\n\t\t\t\tqueryKey: sessionKeys.members(sessionId),\n\t\t\t});\n\t\t},\n\t\tonError: (error) => {\n\t\t\tconsole.error(\"Error removing session member:\", error);\n\t\t},\n\t});\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;;;;AAIO,MAAM,cAAc;IAC1B,KAAK;QAAC;KAAW;IACjB,OAAO,IAAM;eAAI,YAAY,GAAG;YAAE;SAAO;IACzC,MAAM,CAAC,UACN;eAAI,YAAY,KAAK;YAAI;gBAAE;YAAQ;SAAE;IACtC,SAAS,IAAM;eAAI,YAAY,GAAG;YAAE;SAAS;IAC7C,QAAQ,CAAC,KAAe;eAAI,YAAY,OAAO;YAAI;SAAG;IACtD,SAAS,CAAC,KAAe;eAAI,YAAY,MAAM,CAAC;YAAK;SAAU;AAChE;AAGO,SAAS;IACf,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,mSAAQ,EAAC;QACf,UAAU,YAAY,KAAK;QAC3B,SAAS,IAAM,IAAI,WAAW;QAC9B,SAAS,CAAC,CAAC,SAAS;QACpB,WAAW,IAAI,KAAK;IACrB;AACD;AAEO,SAAS,iBAAiB,SAAiB;IACjD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,mSAAQ,EAAC;QACf,UAAU,YAAY,MAAM,CAAC;QAC7B,SAAS,IAAM,IAAI,UAAU,CAAC;QAC9B,SAAS,CAAC,CAAC,SAAS,eAAe,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACrB;AACD;AAEO,SAAS,kBAAkB,SAAiB;IAClD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,mSAAQ,EAAC;QACf,UAAU,YAAY,OAAO,CAAC;QAC9B,SAAS,IAAM,IAAI,iBAAiB,CAAC;QACrC,SAAS,CAAC,CAAC,SAAS,eAAe,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACrB;AACD;AAGO,SAAS;IACf,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QAClB,YAAY,CAAC,OAA2B,IAAI,aAAa,CAAC;QAC1D,WAAW;YACV,uCAAuC;YACvC,YAAY,iBAAiB,CAAC;gBAAE,UAAU,YAAY,KAAK;YAAG;QAC/D;QACA,SAAS,CAAC;YACT,QAAQ,KAAK,CAAC,2BAA2B;QAC1C;IACD;AACD;AAEO,SAAS;IACf,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QAClB,YAAY,CAAC,EAAE,EAAE,EAAE,IAAI,EAA0C,GAChE,IAAI,aAAa,CAAC,IAAI;QACvB,WAAW,CAAC,GAAG,EAAE,EAAE,EAAE;YACpB,gDAAgD;YAChD,YAAY,iBAAiB,CAAC;gBAAE,UAAU,YAAY,MAAM,CAAC;YAAI;YACjE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,YAAY,KAAK;YAAG;QAC/D;QACA,SAAS,CAAC;YACT,QAAQ,KAAK,CAAC,2BAA2B;QAC1C;IACD;AACD;AAEO,SAAS;IACf,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QAClB,YAAY,CAAC,YAAsB,IAAI,aAAa,CAAC;QACrD,WAAW,CAAC,GAAG;YACd,yCAAyC;YACzC,YAAY,aAAa,CAAC;gBAAE,UAAU,YAAY,MAAM,CAAC;YAAW;YACpE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,YAAY,KAAK;YAAG;QAC/D;QACA,SAAS,CAAC;YACT,QAAQ,KAAK,CAAC,2BAA2B;QAC1C;IACD;AACD;AAEO,SAAS;IACf,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QAClB,YAAY,CAAC,OAKZ,IAAI,gBAAgB,CAAC;gBACpB,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,QAAQ;YACrB;QACD,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE;YAC3B,6BAA6B;YAC7B,YAAY,iBAAiB,CAAC;gBAC7B,UAAU,YAAY,OAAO,CAAC;YAC/B;QACD;QACA,SAAS,CAAC;YACT,QAAQ,KAAK,CAAC,gCAAgC;QAC/C;IACD;AACD;AAEO,SAAS;IACf,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QAClB,YAAY,CAAC,EACZ,SAAS,EACT,QAAQ,EAIR,GAAK,IAAI,mBAAmB,CAAC,WAAW;QACzC,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE;YAC3B,6BAA6B;YAC7B,YAAY,iBAAiB,CAAC;gBAC7B,UAAU,YAAY,OAAO,CAAC;YAC/B;QACD;QACA,SAAS,CAAC;YACT,QAAQ,KAAK,CAAC,kCAAkC;QACjD;IACD;AACD", "debugId": null}}, {"offset": {"line": 1176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/queries/use-members.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { useSession } from 'next-auth/react';\nimport { useApi } from '@/hooks/use-api';\nimport type { Member, CreateMemberDto, UpdateMemberDto, MemberDebrief, PaymentFilters } from '@/types';\n\n// Query Keys\nexport const memberKeys = {\n  all: ['members'] as const,\n  lists: () => [...memberKeys.all, 'list'] as const,\n  list: (filters: Record<string, any>) => [...memberKeys.lists(), { filters }] as const,\n  details: () => [...memberKeys.all, 'detail'] as const,\n  detail: (id: string) => [...memberKeys.details(), id] as const,\n  debrief: (id: string, filters?: PaymentFilters) => [...memberKeys.detail(id), 'debrief', { filters }] as const,\n};\n\n// Hooks\nexport function useMembers(searchTerm?: string) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: memberKeys.list({ searchTerm }),\n    queryFn: () => api.getMembers(),\n    enabled: !!session?.accessToken,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n    select: (data) => {\n      // Client-side filtering for search\n      if (!searchTerm) return data;\n      const search = searchTerm.toLowerCase();\n      return data.filter(\n        (member) =>\n          member.firstName.toLowerCase().includes(search) ||\n          member.lastName.toLowerCase().includes(search) ||\n          member.email?.toLowerCase().includes(search) ||\n          member.phone?.includes(search)\n      );\n    },\n  });\n}\n\nexport function useMember(memberId: string) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: memberKeys.detail(memberId),\n    queryFn: () => api.getMember(memberId),\n    enabled: !!session?.accessToken && !!memberId,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\nexport function useMemberDebrief(memberId: string, filters?: PaymentFilters) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: memberKeys.debrief(memberId, filters),\n    queryFn: () => api.getMemberDebrief(memberId, filters),\n    enabled: !!session?.accessToken && !!memberId,\n    staleTime: 2 * 60 * 1000, // 2 minutes\n  });\n}\n\n// Mutations\nexport function useCreateMember() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (data: CreateMemberDto) => api.createMember(data),\n    onSuccess: () => {\n      // Invalidate and refetch members list\n      queryClient.invalidateQueries({ queryKey: memberKeys.lists() });\n    },\n    onError: (error) => {\n      console.error('Error creating member:', error);\n    },\n  });\n}\n\nexport function useUpdateMember() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: UpdateMemberDto }) => \n      api.updateMember(id, data),\n    onSuccess: (_, { id }) => {\n      // Invalidate specific member and members list\n      queryClient.invalidateQueries({ queryKey: memberKeys.detail(id) });\n      queryClient.invalidateQueries({ queryKey: memberKeys.lists() });\n      // Also invalidate debrief data\n      queryClient.invalidateQueries({ queryKey: memberKeys.debrief(id) });\n    },\n    onError: (error) => {\n      console.error('Error updating member:', error);\n    },\n  });\n}\n\nexport function useDeleteMember() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (memberId: string) => api.deleteMember(memberId),\n    onSuccess: (_, memberId) => {\n      // Remove from cache and invalidate lists\n      queryClient.removeQueries({ queryKey: memberKeys.detail(memberId) });\n      queryClient.removeQueries({ queryKey: memberKeys.debrief(memberId) });\n      queryClient.invalidateQueries({ queryKey: memberKeys.lists() });\n    },\n    onError: (error) => {\n      console.error('Error deleting member:', error);\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;;;;AAIO,MAAM,aAAa;IACxB,KAAK;QAAC;KAAU;IAChB,OAAO,IAAM;eAAI,WAAW,GAAG;YAAE;SAAO;IACxC,MAAM,CAAC,UAAiC;eAAI,WAAW,KAAK;YAAI;gBAAE;YAAQ;SAAE;IAC5E,SAAS,IAAM;eAAI,WAAW,GAAG;YAAE;SAAS;IAC5C,QAAQ,CAAC,KAAe;eAAI,WAAW,OAAO;YAAI;SAAG;IACrD,SAAS,CAAC,IAAY,UAA6B;eAAI,WAAW,MAAM,CAAC;YAAK;YAAW;gBAAE;YAAQ;SAAE;AACvG;AAGO,SAAS,WAAW,UAAmB;IAC5C,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,mSAAQ,EAAC;QACd,UAAU,WAAW,IAAI,CAAC;YAAE;QAAW;QACvC,SAAS,IAAM,IAAI,UAAU;QAC7B,SAAS,CAAC,CAAC,SAAS;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ,CAAC;YACP,mCAAmC;YACnC,IAAI,CAAC,YAAY,OAAO;YACxB,MAAM,SAAS,WAAW,WAAW;YACrC,OAAO,KAAK,MAAM,CAChB,CAAC,SACC,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WACxC,OAAO,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WACvC,OAAO,KAAK,EAAE,cAAc,SAAS,WACrC,OAAO,KAAK,EAAE,SAAS;QAE7B;IACF;AACF;AAEO,SAAS,UAAU,QAAgB;IACxC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,mSAAQ,EAAC;QACd,UAAU,WAAW,MAAM,CAAC;QAC5B,SAAS,IAAM,IAAI,SAAS,CAAC;QAC7B,SAAS,CAAC,CAAC,SAAS,eAAe,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,SAAS,iBAAiB,QAAgB,EAAE,OAAwB;IACzE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,mSAAQ,EAAC;QACd,UAAU,WAAW,OAAO,CAAC,UAAU;QACvC,SAAS,IAAM,IAAI,gBAAgB,CAAC,UAAU;QAC9C,SAAS,CAAC,CAAC,SAAS,eAAe,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS;IACd,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QACjB,YAAY,CAAC,OAA0B,IAAI,YAAY,CAAC;QACxD,WAAW;YACT,sCAAsC;YACtC,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,KAAK;YAAG;QAC/D;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QACjB,YAAY,CAAC,EAAE,EAAE,EAAE,IAAI,EAAyC,GAC9D,IAAI,YAAY,CAAC,IAAI;QACvB,WAAW,CAAC,GAAG,EAAE,EAAE,EAAE;YACnB,8CAA8C;YAC9C,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,MAAM,CAAC;YAAI;YAChE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,KAAK;YAAG;YAC7D,+BAA+B;YAC/B,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,OAAO,CAAC;YAAI;QACnE;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QACjB,YAAY,CAAC,WAAqB,IAAI,YAAY,CAAC;QACnD,WAAW,CAAC,GAAG;YACb,yCAAyC;YACzC,YAAY,aAAa,CAAC;gBAAE,UAAU,WAAW,MAAM,CAAC;YAAU;YAClE,YAAY,aAAa,CAAC;gBAAE,UAAU,WAAW,OAAO,CAAC;YAAU;YACnE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,KAAK;YAAG;QAC/D;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;AACF", "debugId": null}}, {"offset": {"line": 1333, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/queries/use-caisses.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { useSession } from 'next-auth/react';\nimport { useApi } from '@/hooks/use-api';\nimport type { Caisse, CreateCaisseDto, UpdateCaisseDto } from '@/types';\n\n// Query Keys\nexport const caisseKeys = {\n  all: ['caisses'] as const,\n  lists: () => [...caisseKeys.all, 'list'] as const,\n  list: (filters: Record<string, any>) => [...caisseKeys.lists(), { filters }] as const,\n  details: () => [...caisseKeys.all, 'detail'] as const,\n  detail: (id: string) => [...caisseKeys.details(), id] as const,\n};\n\n// Hooks\nexport function useCaisses() {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: caisseKeys.lists(),\n    queryFn: () => api.getCaisses(),\n    enabled: !!session?.accessToken,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\nexport function useCaisse(caisseId: string) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: caisseKeys.detail(caisseId),\n    queryFn: () => api.getCaisse(caisseId),\n    enabled: !!session?.accessToken && !!caisseId,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n// Mutations\nexport function useCreateCaisse() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (data: CreateCaisseDto) => api.createCaisse(data),\n    onSuccess: () => {\n      // Invalidate and refetch caisses list\n      queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });\n    },\n    onError: (error) => {\n      console.error('Error creating caisse:', error);\n    },\n  });\n}\n\nexport function useUpdateCaisse() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: UpdateCaisseDto }) => \n      api.updateCaisse(id, data),\n    onSuccess: (_, { id }) => {\n      // Invalidate specific caisse and caisses list\n      queryClient.invalidateQueries({ queryKey: caisseKeys.detail(id) });\n      queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });\n    },\n    onError: (error) => {\n      console.error('Error updating caisse:', error);\n    },\n  });\n}\n\nexport function useDeleteCaisse() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (caisseId: string) => api.deleteCaisse(caisseId),\n    onSuccess: (_, caisseId) => {\n      // Remove from cache and invalidate lists\n      queryClient.removeQueries({ queryKey: caisseKeys.detail(caisseId) });\n      queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });\n    },\n    onError: (error) => {\n      console.error('Error deleting caisse:', error);\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;;;;AAIO,MAAM,aAAa;IACxB,KAAK;QAAC;KAAU;IAChB,OAAO,IAAM;eAAI,WAAW,GAAG;YAAE;SAAO;IACxC,MAAM,CAAC,UAAiC;eAAI,WAAW,KAAK;YAAI;gBAAE;YAAQ;SAAE;IAC5E,SAAS,IAAM;eAAI,WAAW,GAAG;YAAE;SAAS;IAC5C,QAAQ,CAAC,KAAe;eAAI,WAAW,OAAO;YAAI;SAAG;AACvD;AAGO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,mSAAQ,EAAC;QACd,UAAU,WAAW,KAAK;QAC1B,SAAS,IAAM,IAAI,UAAU;QAC7B,SAAS,CAAC,CAAC,SAAS;QACpB,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,SAAS,UAAU,QAAgB;IACxC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,mSAAQ,EAAC;QACd,UAAU,WAAW,MAAM,CAAC;QAC5B,SAAS,IAAM,IAAI,SAAS,CAAC;QAC7B,SAAS,CAAC,CAAC,SAAS,eAAe,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS;IACd,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QACjB,YAAY,CAAC,OAA0B,IAAI,YAAY,CAAC;QACxD,WAAW;YACT,sCAAsC;YACtC,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,KAAK;YAAG;QAC/D;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QACjB,YAAY,CAAC,EAAE,EAAE,EAAE,IAAI,EAAyC,GAC9D,IAAI,YAAY,CAAC,IAAI;QACvB,WAAW,CAAC,GAAG,EAAE,EAAE,EAAE;YACnB,8CAA8C;YAC9C,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,MAAM,CAAC;YAAI;YAChE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,KAAK;YAAG;QAC/D;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QACjB,YAAY,CAAC,WAAqB,IAAI,YAAY,CAAC;QACnD,WAAW,CAAC,GAAG;YACb,yCAAyC;YACzC,YAAY,aAAa,CAAC;gBAAE,UAAU,WAAW,MAAM,CAAC;YAAU;YAClE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,KAAK;YAAG;QAC/D;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;AACF", "debugId": null}}, {"offset": {"line": 1456, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/queries/use-users.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { useSession } from 'next-auth/react';\nimport { useApi } from '@/hooks/use-api';\n\n// Query Keys\nexport const userKeys = {\n  all: ['users'] as const,\n  lists: () => [...userKeys.all, 'list'] as const,\n  list: (filters: Record<string, any>) => [...userKeys.lists(), { filters }] as const,\n  details: () => [...userKeys.all, 'detail'] as const,\n  detail: (id: string) => [...userKeys.details(), id] as const,\n  stats: () => [...userKeys.all, 'stats'] as const,\n  byRole: (role: string) => [...userKeys.all, 'by-role', role] as const,\n};\n\n// Hooks\nexport function useUsers() {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: userKeys.lists(),\n    queryFn: () => api.getUsers(),\n    enabled: !!session?.accessToken,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\nexport function useUser(userId: string) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: userKeys.detail(userId),\n    queryFn: () => api.getUser(userId),\n    enabled: !!session?.accessToken && !!userId,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\nexport function useUserStats() {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: userKeys.stats(),\n    queryFn: () => api.authenticatedRequest('/users/stats'),\n    enabled: !!session?.accessToken,\n    staleTime: 10 * 60 * 1000, // 10 minutes - stats don't change frequently\n  });\n}\n\nexport function useUsersByRole(role: string) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: userKeys.byRole(role),\n    queryFn: () => api.authenticatedRequest(`/users/by-role/${role}`),\n    enabled: !!session?.accessToken && !!role,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n// Mutations\nexport function useCreateUser() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (userData: any) => api.createUser(userData),\n    onSuccess: () => {\n      // Invalidate and refetch users list and stats\n      queryClient.invalidateQueries({ queryKey: userKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userKeys.stats() });\n    },\n    onError: (error) => {\n      console.error('Error creating user:', error);\n    },\n  });\n}\n\nexport function useUpdateUser() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: ({ id, userData }: { id: string; userData: any }) => \n      api.updateUser(id, userData),\n    onSuccess: (_, { id }) => {\n      // Invalidate specific user, users list, and stats\n      queryClient.invalidateQueries({ queryKey: userKeys.detail(id) });\n      queryClient.invalidateQueries({ queryKey: userKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userKeys.stats() });\n      // Invalidate all by-role queries since role might have changed\n      queryClient.invalidateQueries({ queryKey: [...userKeys.all, 'by-role'] });\n    },\n    onError: (error) => {\n      console.error('Error updating user:', error);\n    },\n  });\n}\n\nexport function useDeleteUser() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (userId: string) => api.deleteUser(userId),\n    onSuccess: (_, userId) => {\n      // Remove from cache and invalidate lists\n      queryClient.removeQueries({ queryKey: userKeys.detail(userId) });\n      queryClient.invalidateQueries({ queryKey: userKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: userKeys.stats() });\n      // Invalidate all by-role queries\n      queryClient.invalidateQueries({ queryKey: [...userKeys.all, 'by-role'] });\n    },\n    onError: (error) => {\n      console.error('Error deleting user:', error);\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;;;;AAGO,MAAM,WAAW;IACtB,KAAK;QAAC;KAAQ;IACd,OAAO,IAAM;eAAI,SAAS,GAAG;YAAE;SAAO;IACtC,MAAM,CAAC,UAAiC;eAAI,SAAS,KAAK;YAAI;gBAAE;YAAQ;SAAE;IAC1E,SAAS,IAAM;eAAI,SAAS,GAAG;YAAE;SAAS;IAC1C,QAAQ,CAAC,KAAe;eAAI,SAAS,OAAO;YAAI;SAAG;IACnD,OAAO,IAAM;eAAI,SAAS,GAAG;YAAE;SAAQ;IACvC,QAAQ,CAAC,OAAiB;eAAI,SAAS,GAAG;YAAE;YAAW;SAAK;AAC9D;AAGO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,mSAAQ,EAAC;QACd,UAAU,SAAS,KAAK;QACxB,SAAS,IAAM,IAAI,QAAQ;QAC3B,SAAS,CAAC,CAAC,SAAS;QACpB,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,SAAS,QAAQ,MAAc;IACpC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,mSAAQ,EAAC;QACd,UAAU,SAAS,MAAM,CAAC;QAC1B,SAAS,IAAM,IAAI,OAAO,CAAC;QAC3B,SAAS,CAAC,CAAC,SAAS,eAAe,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,mSAAQ,EAAC;QACd,UAAU,SAAS,KAAK;QACxB,SAAS,IAAM,IAAI,oBAAoB,CAAC;QACxC,SAAS,CAAC,CAAC,SAAS;QACpB,WAAW,KAAK,KAAK;IACvB;AACF;AAEO,SAAS,eAAe,IAAY;IACzC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,mSAAQ,EAAC;QACd,UAAU,SAAS,MAAM,CAAC;QAC1B,SAAS,IAAM,IAAI,oBAAoB,CAAC,CAAC,eAAe,EAAE,MAAM;QAChE,SAAS,CAAC,CAAC,SAAS,eAAe,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS;IACd,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QACjB,YAAY,CAAC,WAAkB,IAAI,UAAU,CAAC;QAC9C,WAAW;YACT,8CAA8C;YAC9C,YAAY,iBAAiB,CAAC;gBAAE,UAAU,SAAS,KAAK;YAAG;YAC3D,YAAY,iBAAiB,CAAC;gBAAE,UAAU,SAAS,KAAK;YAAG;QAC7D;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QACjB,YAAY,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAiC,GAC1D,IAAI,UAAU,CAAC,IAAI;QACrB,WAAW,CAAC,GAAG,EAAE,EAAE,EAAE;YACnB,kDAAkD;YAClD,YAAY,iBAAiB,CAAC;gBAAE,UAAU,SAAS,MAAM,CAAC;YAAI;YAC9D,YAAY,iBAAiB,CAAC;gBAAE,UAAU,SAAS,KAAK;YAAG;YAC3D,YAAY,iBAAiB,CAAC;gBAAE,UAAU,SAAS,KAAK;YAAG;YAC3D,+DAA+D;YAC/D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;uBAAI,SAAS,GAAG;oBAAE;iBAAU;YAAC;QACzE;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QACjB,YAAY,CAAC,SAAmB,IAAI,UAAU,CAAC;QAC/C,WAAW,CAAC,GAAG;YACb,yCAAyC;YACzC,YAAY,aAAa,CAAC;gBAAE,UAAU,SAAS,MAAM,CAAC;YAAQ;YAC9D,YAAY,iBAAiB,CAAC;gBAAE,UAAU,SAAS,KAAK;YAAG;YAC3D,YAAY,iBAAiB,CAAC;gBAAE,UAAU,SAAS,KAAK;YAAG;YAC3D,iCAAiC;YACjC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;uBAAI,SAAS,GAAG;oBAAE;iBAAU;YAAC;QACzE;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 1635, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/queries/use-payments.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { useSession } from 'next-auth/react';\nimport { useApi } from '@/hooks/use-api';\nimport type { Payment, CreatePaymentDto, PaymentFilters } from '@/types';\nimport { memberKeys } from './use-members';\nimport { caisseKeys } from './use-caisses';\n\n// Query Keys\nexport const paymentKeys = {\n  all: ['payments'] as const,\n  lists: () => [...paymentKeys.all, 'list'] as const,\n  list: (filters: PaymentFilters) => [...paymentKeys.lists(), { filters }] as const,\n  details: () => [...paymentKeys.all, 'detail'] as const,\n  detail: (id: string) => [...paymentKeys.details(), id] as const,\n};\n\n// Hooks\nexport function usePayments(filters?: PaymentFilters) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: paymentKeys.list(filters || {}),\n    queryFn: () => api.getPayments(filters),\n    enabled: !!session?.accessToken,\n    staleTime: 2 * 60 * 1000, // 2 minutes - payments change more frequently\n  });\n}\n\nexport function usePayment(paymentId: string) {\n  const { data: session } = useSession();\n  const api = useApi();\n\n  return useQuery({\n    queryKey: paymentKeys.detail(paymentId),\n    queryFn: () => api.getPayment(paymentId),\n    enabled: !!session?.accessToken && !!paymentId,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n}\n\n// Mutations\nexport function useCreatePayment() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (data: CreatePaymentDto) => api.createPayment(data),\n    onSuccess: (newPayment) => {\n      // Invalidate payments lists\n      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });\n      \n      // Invalidate related member debrief if memberId is available\n      if (newPayment.memberId) {\n        queryClient.invalidateQueries({ \n          queryKey: memberKeys.debrief(newPayment.memberId) \n        });\n      }\n      \n      // Invalidate related caisse if caisseId is available\n      if (newPayment.caisseId) {\n        queryClient.invalidateQueries({ \n          queryKey: caisseKeys.detail(newPayment.caisseId) \n        });\n        queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });\n      }\n    },\n    onError: (error) => {\n      console.error('Error creating payment:', error);\n    },\n  });\n}\n\nexport function useUpdatePayment() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: ({ id, data }: { id: string; data: Partial<CreatePaymentDto> }) => \n      api.updatePayment(id, data),\n    onSuccess: (updatedPayment, { id }) => {\n      // Invalidate specific payment and payments lists\n      queryClient.invalidateQueries({ queryKey: paymentKeys.detail(id) });\n      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });\n      \n      // Invalidate related member debrief\n      if (updatedPayment.memberId) {\n        queryClient.invalidateQueries({ \n          queryKey: memberKeys.debrief(updatedPayment.memberId) \n        });\n      }\n      \n      // Invalidate related caisse\n      if (updatedPayment.caisseId) {\n        queryClient.invalidateQueries({ \n          queryKey: caisseKeys.detail(updatedPayment.caisseId) \n        });\n        queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });\n      }\n    },\n    onError: (error) => {\n      console.error('Error updating payment:', error);\n    },\n  });\n}\n\nexport function useDeletePayment() {\n  const queryClient = useQueryClient();\n  const api = useApi();\n\n  return useMutation({\n    mutationFn: (paymentId: string) => api.deletePayment(paymentId),\n    onSuccess: (_, paymentId) => {\n      // Remove from cache and invalidate lists\n      queryClient.removeQueries({ queryKey: paymentKeys.detail(paymentId) });\n      queryClient.invalidateQueries({ queryKey: paymentKeys.lists() });\n      \n      // Invalidate all member debriefs and caisses since we don't know which ones were affected\n      queryClient.invalidateQueries({ queryKey: memberKeys.all });\n      queryClient.invalidateQueries({ queryKey: caisseKeys.lists() });\n    },\n    onError: (error) => {\n      console.error('Error deleting payment:', error);\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;;;;;;AAGO,MAAM,cAAc;IACzB,KAAK;QAAC;KAAW;IACjB,OAAO,IAAM;eAAI,YAAY,GAAG;YAAE;SAAO;IACzC,MAAM,CAAC,UAA4B;eAAI,YAAY,KAAK;YAAI;gBAAE;YAAQ;SAAE;IACxE,SAAS,IAAM;eAAI,YAAY,GAAG;YAAE;SAAS;IAC7C,QAAQ,CAAC,KAAe;eAAI,YAAY,OAAO;YAAI;SAAG;AACxD;AAGO,SAAS,YAAY,OAAwB;IAClD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,mSAAQ,EAAC;QACd,UAAU,YAAY,IAAI,CAAC,WAAW,CAAC;QACvC,SAAS,IAAM,IAAI,WAAW,CAAC;QAC/B,SAAS,CAAC,CAAC,SAAS;QACpB,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,SAAS,WAAW,SAAiB;IAC1C,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,mSAAQ,EAAC;QACd,UAAU,YAAY,MAAM,CAAC;QAC7B,SAAS,IAAM,IAAI,UAAU,CAAC;QAC9B,SAAS,CAAC,CAAC,SAAS,eAAe,CAAC,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,SAAS;IACd,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QACjB,YAAY,CAAC,OAA2B,IAAI,aAAa,CAAC;QAC1D,WAAW,CAAC;YACV,4BAA4B;YAC5B,YAAY,iBAAiB,CAAC;gBAAE,UAAU,YAAY,KAAK;YAAG;YAE9D,6DAA6D;YAC7D,IAAI,WAAW,QAAQ,EAAE;gBACvB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,mKAAU,CAAC,OAAO,CAAC,WAAW,QAAQ;gBAClD;YACF;YAEA,qDAAqD;YACrD,IAAI,WAAW,QAAQ,EAAE;gBACvB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,mKAAU,CAAC,MAAM,CAAC,WAAW,QAAQ;gBACjD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,mKAAU,CAAC,KAAK;gBAAG;YAC/D;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QACjB,YAAY,CAAC,EAAE,EAAE,EAAE,IAAI,EAAmD,GACxE,IAAI,aAAa,CAAC,IAAI;QACxB,WAAW,CAAC,gBAAgB,EAAE,EAAE,EAAE;YAChC,iDAAiD;YACjD,YAAY,iBAAiB,CAAC;gBAAE,UAAU,YAAY,MAAM,CAAC;YAAI;YACjE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,YAAY,KAAK;YAAG;YAE9D,oCAAoC;YACpC,IAAI,eAAe,QAAQ,EAAE;gBAC3B,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,mKAAU,CAAC,OAAO,CAAC,eAAe,QAAQ;gBACtD;YACF;YAEA,4BAA4B;YAC5B,IAAI,eAAe,QAAQ,EAAE;gBAC3B,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,mKAAU,CAAC,MAAM,CAAC,eAAe,QAAQ;gBACrD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,mKAAU,CAAC,KAAK;gBAAG;YAC/D;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,IAAA,oTAAc;IAClC,MAAM,MAAM,IAAA,gJAAM;IAElB,OAAO,IAAA,ySAAW,EAAC;QACjB,YAAY,CAAC,YAAsB,IAAI,aAAa,CAAC;QACrD,WAAW,CAAC,GAAG;YACb,yCAAyC;YACzC,YAAY,aAAa,CAAC;gBAAE,UAAU,YAAY,MAAM,CAAC;YAAW;YACpE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,YAAY,KAAK;YAAG;YAE9D,0FAA0F;YAC1F,YAAY,iBAAiB,CAAC;gBAAE,UAAU,mKAAU,CAAC,GAAG;YAAC;YACzD,YAAY,iBAAiB,CAAC;gBAAE,UAAU,mKAAU,CAAC,KAAK;YAAG;QAC/D;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;AACF", "debugId": null}}, {"offset": {"line": 1799, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/queries/index.ts"], "sourcesContent": ["// Export all query hooks for easy importing\nexport * from './use-sessions';\nexport * from './use-members';\nexport * from './use-caisses';\nexport * from './use-users';\nexport * from './use-payments';\n\n// Export query keys for external use if needed\nexport { sessionKeys } from './use-sessions';\nexport { memberKeys } from './use-members';\nexport { caisseKeys } from './use-caisses';\nexport { userKeys } from './use-users';\nexport { paymentKeys } from './use-payments';\n"], "names": [], "mappings": "AAAA,4CAA4C;;AAC5C;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1820, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/types/index.ts"], "sourcesContent": ["// Types pour l'application Tontine\n\n// Enums\nexport enum CaisseType {\n  PRINCIPALE = 'PRINCIPALE',\n  REUNION = 'REUNION',\n}\n\nexport enum UserRole {\n  SECRETARY_GENERAL = 'secretary_general',\n  CONTROLLER = 'controller',\n  CASHIER = 'cashier',\n}\n\nexport enum UserStatus {\n  ACTIF = 'actif',\n  EN_ATTENTE = 'en_attente',\n  SUSPENDU = 'suspendu',\n}\n\nexport enum PaymentDirection {\n  IN = 'IN',\n  OUT = 'OUT',\n}\n\nexport enum PaymentFunction {\n  CONTRIBUTION = 'cotisation',\n  TRANSFER = 'transfert',\n  EXTERNAL = 'exterieur',\n}\n\n// Interfaces principales\nexport interface User {\n  _id: string;\n  username: string;\n  role: UserRole;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Session {\n  _id: string;\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  dateProchaineReunion?: string;\n  nextReunionId?: string;\n  partFixe: number;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Reunion {\n  _id: string;\n  dateReunion: string;\n  lieu?: string;\n  caissePrincipale?: string;\n  sessionId: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Caisse {\n  _id: string;\n  nom: string;\n  type: CaisseType;\n  soldeActuel: number;\n  sessionId?: string;\n  createdBy: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: Member (différent de User)\nexport interface Member {\n  _id: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: SessionMember (inscription d'un membre à une session)\nexport interface SessionMember {\n  _id: string;\n  sessionId: string;\n  memberId: string;\n  parts: number;\n  totalDue: number;\n  paidSoFar: number;\n  expectedToDate: number;\n  overdueAmount: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: Payment\nexport interface Payment {\n  _id: string;\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  date: string;\n  sessionId?: string;\n  reunionId?: string;\n  caisseId: string;\n  receivingCaisseId?: string;\n  memberId?: string;\n  reason?: string;\n  exitOrderId?: string;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// DTOs pour les formulaires\nexport interface CreateSessionDto {\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  partFixe: number;\n}\n\nexport interface UpdateSessionDto {\n  annee?: number;\n  dateDebut?: string;\n  dateFin?: string;\n  partFixe?: number;\n}\n\nexport interface CreateCaisseDto {\n  nom: string;\n  type: CaisseType;\n  soldeActuel?: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface UpdateCaisseDto {\n  nom?: string;\n  type?: CaisseType;\n  soldeActuel?: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface UpdateReunionDto {\n  dateReunion?: string;\n  lieu?: string;\n  caissePrincipale?: string;\n}\n\n// Nouveaux DTOs\nexport interface CreateMemberDto {\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface UpdateMemberDto {\n  firstName?: string;\n  lastName?: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface CreatePaymentDto {\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  caisseId: string;\n  receivingCaisseId?: string;\n  sessionId?: string;\n  reunionId?: string;\n  memberId?: string;\n  reason?: string;\n  exitOrderId?: string;\n}\n\nexport interface CreateSessionMemberDto {\n  sessionId: string;\n  memberId: string;\n  parts: number;\n}\n\n// Types pour les statistiques\nexport interface SessionStats {\n  total: number;\n  active: number;\n  completed: number;\n  totalPartFixe: number;\n}\n\nexport interface CaisseStats {\n  total: number;\n  principales: number;\n  reunions: number;\n  soldeTotal: number;\n  soldePrincipales: number;\n  soldeReunions: number;\n}\n\nexport interface MemberStats {\n  total: number;\n  withEmail: number;\n  withPhone: number;\n  withAddress: number;\n}\n\nexport interface PaymentStats {\n  totalIn: number;\n  totalOut: number;\n  netAmount: number;\n  contributionsTotal: number;\n  transfersTotal: number;\n  externalTotal: number;\n}\n\n// Types pour les formulaires\nexport interface SessionForm {\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  partFixe: number;\n}\n\nexport interface CaisseForm {\n  nom: string;\n  type: CaisseType;\n  soldeActuel: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface MemberForm {\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface PaymentForm {\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  caisseId: string;\n  receivingCaisseId?: string;\n  sessionId?: string;\n  reunionId?: string;\n  memberId?: string;\n  reason?: string;\n}\n\n// Types pour les réponses API\nexport interface ApiResponse<T> {\n  data: T;\n  message?: string;\n  success: boolean;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n// Types pour les filtres\nexport interface SessionFilters {\n  annee?: number;\n  status?: 'active' | 'completed' | 'all';\n}\n\nexport interface CaisseFilters {\n  type?: CaisseType | 'all';\n  sessionId?: string;\n}\n\nexport interface MemberFilters {\n  search?: string;\n  hasEmail?: boolean;\n  hasPhone?: boolean;\n}\n\nexport interface PaymentFilters {\n  direction?: PaymentDirection | 'all';\n  func?: PaymentFunction | 'all';\n  caisseId?: string;\n  sessionId?: string;\n  memberId?: string;\n  dateFrom?: string;\n  dateTo?: string;\n}\n\n// Types pour les réponses spéciales\nexport interface MemberDebrief {\n  member: Member;\n  totalIn: number;\n  totalOut: number;\n  netAmount: number;\n  contributionsTotal: number;\n  transfersTotal: number;\n  externalTotal: number;\n  payments: Payment[];\n}\n"], "names": [], "mappings": "AAAA,mCAAmC;AAEnC,QAAQ;;;;;;;;;;;;;AACD,IAAA,AAAK,oCAAA;;;WAAA;;AAKL,IAAA,AAAK,kCAAA;;;;WAAA;;AAML,IAAA,AAAK,oCAAA;;;;WAAA;;AAML,IAAA,AAAK,0CAAA;;;WAAA;;AAKL,IAAA,AAAK,yCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 1866, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/app/dashboard/caisses/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useMemo, useEffect } from \"react\";\nimport { useSession } from \"next-auth/react\";\nimport {\n\tPlus,\n\tSearch,\n\tFilter,\n\tMoreHorizontal,\n\tEdit,\n\tTrash2,\n\tWallet,\n\tDollarSign,\n\tArrowUpRight,\n\tBuilding,\n\tUsers,\n} from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport {\n\tTable,\n\tTableBody,\n\tTableCell,\n\tTableHead,\n\tTableHeader,\n\tTableRow,\n} from \"@/components/ui/table\";\nimport {\n\tDropdownMenu,\n\tDropdownMenuContent,\n\tDropdownMenuItem,\n\tDropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport {\n\tSelect,\n\tSelectContent,\n\tSelectItem,\n\tSelectTrigger,\n\tSelectValue,\n} from \"@/components/ui/select\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { useCaisses, useDeleteCaisse } from \"@/hooks/queries\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON>seStats, CaisseType } from \"@/types\";\n\nexport default function CaissesPage() {\n\tconst { data: session } = useSession();\n\tconst [searchTerm, setSearchTerm] = useState(\"\");\n\tconst [typeFilter, setTypeFilter] = useState<string>(\"all\");\n\n\t// React Query hooks\n\tconst caissesQuery = useCaisses();\n\tconst deleteCaisseMutation = useDeleteCaisse();\n\n\t// Vérifier les permissions\n\tconst permissions = useMemo(() => {\n\t\tconst role = (session?.user as any)?.role;\n\t\treturn {\n\t\t\tcanManageCaisses: [\"secretary_general\", \"controller\", \"cashier\"].includes(\n\t\t\t\trole,\n\t\t\t),\n\t\t\tcanCreateCaisses: role === \"secretary_general\",\n\t\t\tcanEmarger: role === \"cashier\",\n\t\t};\n\t}, [session?.user]);\n\n\t// Extract data and loading state\n\tconst allCaisses = caissesQuery.data || [];\n\tconst loading = caissesQuery.isLoading;\n\n\tconst loadData = async () => {\n\t\ttry {\n\t\t\tsetLoading(true);\n\t\t\tconst caissesData = await api.getCaisses();\n\t\t\tsetCaisses(caissesData);\n\n\t\t\t// Calculer les statistiques\n\t\t\tconst principales = caissesData.filter(\n\t\t\t\t(c) => c.type === CaisseType.PRINCIPALE,\n\t\t\t);\n\t\t\tconst reunions = caissesData.filter((c) => c.type === CaisseType.REUNION);\n\t\t\tconst soldeTotal = caissesData.reduce((sum, c) => sum + c.soldeActuel, 0);\n\t\t\tconst soldePrincipales = principales.reduce(\n\t\t\t\t(sum, c) => sum + c.soldeActuel,\n\t\t\t\t0,\n\t\t\t);\n\t\t\tconst soldeReunions = reunions.reduce((sum, c) => sum + c.soldeActuel, 0);\n\n\t\t\tsetStats({\n\t\t\t\ttotal: caissesData.length,\n\t\t\t\tprincipales: principales.length,\n\t\t\t\treunions: reunions.length,\n\t\t\t\tsoldeTotal,\n\t\t\t\tsoldePrincipales,\n\t\t\t\tsoldeReunions,\n\t\t\t});\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors du chargement des caisses:\", error);\n\t\t} finally {\n\t\t\tsetLoading(false);\n\t\t}\n\t};\n\n\tuseEffect(() => {\n\t\tif (session?.accessToken) {\n\t\t\tloadData();\n\t\t}\n\t}, [session]);\n\n\tconst handleDelete = async (caisseId: string) => {\n\t\tif (!confirm(\"Êtes-vous sûr de vouloir supprimer cette caisse ?\")) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tawait api.deleteCaisse(caisseId);\n\t\t\tloadData();\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors de la suppression:\", error);\n\t\t}\n\t};\n\n\tconst handleEmarger = async (caisseId: string) => {\n\t\tif (\n\t\t\t!confirm(\n\t\t\t\t\"Êtes-vous sûr de vouloir émarger cette caisse ? Le solde sera transféré vers la caisse principale.\",\n\t\t\t)\n\t\t) {\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tawait api.emargerCaisse(caisseId);\n\t\t\tloadData();\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors de l'émargement:\", error);\n\t\t}\n\t};\n\n\t// Filtrer les caisses\n\tconst filteredCaisses = caisses.filter((caisse) => {\n\t\tconst matchesSearch =\n\t\t\tcaisse.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||\n\t\t\tcaisse.soldeActuel.toString().includes(searchTerm);\n\n\t\tconst matchesType = typeFilter === \"all\" || caisse.type === typeFilter;\n\n\t\treturn matchesSearch && matchesType;\n\t});\n\n\tconst getTypeVariant = (type: CaisseType) => {\n\t\tswitch (type) {\n\t\t\tcase CaisseType.PRINCIPALE:\n\t\t\t\treturn \"default\" as const;\n\t\t\tcase CaisseType.REUNION:\n\t\t\t\treturn \"secondary\" as const;\n\t\t\tdefault:\n\t\t\t\treturn \"outline\" as const;\n\t\t}\n\t};\n\n\tconst getTypeLabel = (type: CaisseType) => {\n\t\tswitch (type) {\n\t\t\tcase CaisseType.PRINCIPALE:\n\t\t\t\treturn \"Principale\";\n\t\t\tcase CaisseType.REUNION:\n\t\t\t\treturn \"Réunion\";\n\t\t\tdefault:\n\t\t\t\treturn type;\n\t\t}\n\t};\n\n\tif (loading) {\n\t\treturn (\n\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t<div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"></div>\n\t\t\t\t\t<p className=\"mt-2 text-sm text-gray-600\">\n\t\t\t\t\t\tChargement des caisses...\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* En-tête */}\n\t\t\t<div className=\"flex justify-between items-center\">\n\t\t\t\t<div>\n\t\t\t\t\t<h1 className=\"text-3xl font-bold tracking-tight\">Caisses</h1>\n\t\t\t\t\t<p className=\"text-muted-foreground\">\n\t\t\t\t\t\tGérez les caisses principales et de réunion\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t\t{canCreateCaisses && (\n\t\t\t\t\t<Button asChild>\n\t\t\t\t\t\t<Link href=\"/dashboard/caisses/new\">\n\t\t\t\t\t\t\t<Plus className=\"mr-2 h-4 w-4\" />\n\t\t\t\t\t\t\tNouvelle caisse\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</Button>\n\t\t\t\t)}\n\t\t\t</div>\n\n\t\t\t{/* Statistiques */}\n\t\t\t{stats && (\n\t\t\t\t<div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium\">\n\t\t\t\t\t\t\t\tTotal Caisses\n\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t<Wallet className=\"h-4 w-4 text-muted-foreground\" />\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold\">{stats.total}</div>\n\t\t\t\t\t\t\t<p className=\"text-xs text-muted-foreground\">\n\t\t\t\t\t\t\t\t{stats.principales} principales, {stats.reunions} réunions\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium\">Solde Total</CardTitle>\n\t\t\t\t\t\t\t<DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold\">\n\t\t\t\t\t\t\t\t{stats.soldeTotal.toLocaleString()} FCFA\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium\">\n\t\t\t\t\t\t\t\tCaisses Principales\n\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t<Building className=\"h-4 w-4 text-muted-foreground\" />\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold\">\n\t\t\t\t\t\t\t\t{stats.soldePrincipales.toLocaleString()} FCFA\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p className=\"text-xs text-muted-foreground\">\n\t\t\t\t\t\t\t\t{stats.principales} caisses\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium\">\n\t\t\t\t\t\t\t\tCaisses Réunions\n\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t<Users className=\"h-4 w-4 text-muted-foreground\" />\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold\">\n\t\t\t\t\t\t\t\t{stats.soldeReunions.toLocaleString()} FCFA\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p className=\"text-xs text-muted-foreground\">\n\t\t\t\t\t\t\t\t{stats.reunions} caisses\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t</div>\n\t\t\t)}\n\n\t\t\t{/* Filtres */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle>Filtres</CardTitle>\n\t\t\t\t\t<CardDescription>Recherchez et filtrez les caisses</CardDescription>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<div className=\"flex gap-4\">\n\t\t\t\t\t\t<div className=\"flex-1\">\n\t\t\t\t\t\t\t<div className=\"relative\">\n\t\t\t\t\t\t\t\t<Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\n\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\tplaceholder=\"Rechercher par nom ou solde...\"\n\t\t\t\t\t\t\t\t\tvalue={searchTerm}\n\t\t\t\t\t\t\t\t\tonChange={(e) => setSearchTerm(e.target.value)}\n\t\t\t\t\t\t\t\t\tclassName=\"pl-8\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<Select value={typeFilter} onValueChange={setTypeFilter}>\n\t\t\t\t\t\t\t<SelectTrigger className=\"w-[180px]\">\n\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Filtrer par type\" />\n\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t<SelectItem value=\"all\">Tous les types</SelectItem>\n\t\t\t\t\t\t\t\t<SelectItem value={CaisseType.PRINCIPALE}>\n\t\t\t\t\t\t\t\t\tPrincipales\n\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t<SelectItem value={CaisseType.REUNION}>Réunions</SelectItem>\n\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t</Select>\n\t\t\t\t\t</div>\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\n\t\t\t{/* Tableau des caisses */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle>Caisses ({filteredCaisses.length})</CardTitle>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<Table>\n\t\t\t\t\t\t<TableHeader>\n\t\t\t\t\t\t\t<TableRow>\n\t\t\t\t\t\t\t\t<TableHead>Nom</TableHead>\n\t\t\t\t\t\t\t\t<TableHead>Type</TableHead>\n\t\t\t\t\t\t\t\t<TableHead>Solde Actuel</TableHead>\n\t\t\t\t\t\t\t\t<TableHead>Créée le</TableHead>\n\t\t\t\t\t\t\t\t{canManageCaisses && <TableHead>Actions</TableHead>}\n\t\t\t\t\t\t\t</TableRow>\n\t\t\t\t\t\t</TableHeader>\n\t\t\t\t\t\t<TableBody>\n\t\t\t\t\t\t\t{filteredCaisses.map((caisse) => (\n\t\t\t\t\t\t\t\t<TableRow key={caisse._id}>\n\t\t\t\t\t\t\t\t\t<TableCell className=\"font-medium\">{caisse.nom}</TableCell>\n\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t<Badge variant={getTypeVariant(caisse.type)}>\n\t\t\t\t\t\t\t\t\t\t\t{getTypeLabel(caisse.type)}\n\t\t\t\t\t\t\t\t\t\t</Badge>\n\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\t\tclassName={\n\t\t\t\t\t\t\t\t\t\t\t\tcaisse.soldeActuel > 0\n\t\t\t\t\t\t\t\t\t\t\t\t\t? \"text-green-600 font-medium\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t: \"text-gray-500\"\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t{caisse.soldeActuel.toLocaleString()} FCFA\n\t\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t{new Date(caisse.createdAt).toLocaleDateString()}\n\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t{canManageCaisses && (\n\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t<DropdownMenu>\n\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuTrigger asChild>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MoreHorizontal className=\"h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenuTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuContent align=\"end\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuItem asChild>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thref={`/dashboard/caisses/${caisse._id}/edit`}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Edit className=\"mr-2 h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tModifier\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenuItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{canEmarger &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcaisse.type === CaisseType.REUNION &&\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcaisse.soldeActuel > 0 && (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuItem\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleEmarger(caisse._id)}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ArrowUpRight className=\"mr-2 h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tÉmarger\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenuItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\t\t\t\t{canCreateCaisses && (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<DropdownMenuItem\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleDelete(caisse._id)}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"text-red-600\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Trash2 className=\"mr-2 h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tSupprimer\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenuItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\t\t\t</DropdownMenuContent>\n\t\t\t\t\t\t\t\t\t\t\t</DropdownMenu>\n\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t</TableRow>\n\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t</TableBody>\n\t\t\t\t\t</Table>\n\t\t\t\t\t{filteredCaisses.length === 0 && (\n\t\t\t\t\t\t<div className=\"text-center py-8\">\n\t\t\t\t\t\t\t<p className=\"text-muted-foreground\">Aucune caisse trouvée</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t)}\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAEA;AACA;AACA;AAOA;AAQA;AAMA;AAOA;AACA;AAAA;AACA;AAnDA;;;;;;;;;;;;;;;AAqDe,SAAS;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IACpC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,4VAAQ,EAAC;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,4VAAQ,EAAS;IAErD,oBAAoB;IACpB,MAAM,eAAe,IAAA,mKAAU;IAC/B,MAAM,uBAAuB,IAAA,wKAAe;IAE5C,2BAA2B;IAC3B,MAAM,cAAc,IAAA,2VAAO,EAAC;QAC3B,MAAM,OAAQ,SAAS,MAAc;QACrC,OAAO;YACN,kBAAkB;gBAAC;gBAAqB;gBAAc;aAAU,CAAC,QAAQ,CACxE;YAED,kBAAkB,SAAS;YAC3B,YAAY,SAAS;QACtB;IACD,GAAG;QAAC,SAAS;KAAK;IAElB,iCAAiC;IACjC,MAAM,aAAa,aAAa,IAAI,IAAI,EAAE;IAC1C,MAAM,UAAU,aAAa,SAAS;IAEtC,MAAM,WAAW;QAChB,IAAI;YACH,WAAW;YACX,MAAM,cAAc,MAAM,IAAI,UAAU;YACxC,WAAW;YAEX,4BAA4B;YAC5B,MAAM,cAAc,YAAY,MAAM,CACrC,CAAC,IAAM,EAAE,IAAI,KAAK,+IAAU,CAAC,UAAU;YAExC,MAAM,WAAW,YAAY,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,+IAAU,CAAC,OAAO;YACxE,MAAM,aAAa,YAAY,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,WAAW,EAAE;YACvE,MAAM,mBAAmB,YAAY,MAAM,CAC1C,CAAC,KAAK,IAAM,MAAM,EAAE,WAAW,EAC/B;YAED,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,WAAW,EAAE;YAEvE,SAAS;gBACR,OAAO,YAAY,MAAM;gBACzB,aAAa,YAAY,MAAM;gBAC/B,UAAU,SAAS,MAAM;gBACzB;gBACA;gBACA;YACD;QACD,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,0CAA0C;QACzD,SAAU;YACT,WAAW;QACZ;IACD;IAEA,IAAA,6VAAS,EAAC;QACT,IAAI,SAAS,aAAa;YACzB;QACD;IACD,GAAG;QAAC;KAAQ;IAEZ,MAAM,eAAe,OAAO;QAC3B,IAAI,CAAC,QAAQ,sDAAsD;YAClE;QACD;QAEA,IAAI;YACH,MAAM,IAAI,YAAY,CAAC;YACvB;QACD,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,kCAAkC;QACjD;IACD;IAEA,MAAM,gBAAgB,OAAO;QAC5B,IACC,CAAC,QACA,uGAEA;YACD;QACD;QAEA,IAAI;YACH,MAAM,IAAI,aAAa,CAAC;YACxB;QACD,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,gCAAgC;QAC/C;IACD;IAEA,sBAAsB;IACtB,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAC;QACvC,MAAM,gBACL,OAAO,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,OAAO,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAExC,MAAM,cAAc,eAAe,SAAS,OAAO,IAAI,KAAK;QAE5D,OAAO,iBAAiB;IACzB;IAEA,MAAM,iBAAiB,CAAC;QACvB,OAAQ;YACP,KAAK,+IAAU,CAAC,UAAU;gBACzB,OAAO;YACR,KAAK,+IAAU,CAAC,OAAO;gBACtB,OAAO;YACR;gBACC,OAAO;QACT;IACD;IAEA,MAAM,eAAe,CAAC;QACrB,OAAQ;YACP,KAAK,+IAAU,CAAC,UAAU;gBACzB,OAAO;YACR,KAAK,+IAAU,CAAC,OAAO;gBACtB,OAAO;YACR;gBACC,OAAO;QACT;IACD;IAEA,IAAI,SAAS;QACZ,qBACC,yXAAC;YAAI,WAAU;sBACd,cAAA,yXAAC;gBAAI,WAAU;;kCACd,yXAAC;wBAAI,WAAU;;;;;;kCACf,yXAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAM9C;IAEA,qBACC,yXAAC;QAAI,WAAU;;0BAEd,yXAAC;gBAAI,WAAU;;kCACd,yXAAC;;0CACA,yXAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,yXAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;oBAIrC,kCACA,yXAAC,wJAAM;wBAAC,OAAO;kCACd,cAAA,yXAAC,kTAAI;4BAAC,MAAK;;8CACV,yXAAC,0SAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;YAQpC,uBACA,yXAAC;gBAAI,WAAU;;kCACd,yXAAC,oJAAI;;0CACJ,yXAAC,0JAAU;gCAAC,WAAU;;kDACrB,yXAAC,yJAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAG3C,yXAAC,gTAAM;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,yXAAC,2JAAW;;kDACX,yXAAC;wCAAI,WAAU;kDAAsB,MAAM,KAAK;;;;;;kDAChD,yXAAC;wCAAE,WAAU;;4CACX,MAAM,WAAW;4CAAC;4CAAe,MAAM,QAAQ;4CAAC;;;;;;;;;;;;;;;;;;;kCAIpD,yXAAC,oJAAI;;0CACJ,yXAAC,0JAAU;gCAAC,WAAU;;kDACrB,yXAAC,yJAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,yXAAC,gUAAU;wCAAC,WAAU;;;;;;;;;;;;0CAEvB,yXAAC,2JAAW;0CACX,cAAA,yXAAC;oCAAI,WAAU;;wCACb,MAAM,UAAU,CAAC,cAAc;wCAAG;;;;;;;;;;;;;;;;;;kCAItC,yXAAC,oJAAI;;0CACJ,yXAAC,0JAAU;gCAAC,WAAU;;kDACrB,yXAAC,yJAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAG3C,yXAAC,sTAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAErB,yXAAC,2JAAW;;kDACX,yXAAC;wCAAI,WAAU;;4CACb,MAAM,gBAAgB,CAAC,cAAc;4CAAG;;;;;;;kDAE1C,yXAAC;wCAAE,WAAU;;4CACX,MAAM,WAAW;4CAAC;;;;;;;;;;;;;;;;;;;kCAItB,yXAAC,oJAAI;;0CACJ,yXAAC,0JAAU;gCAAC,WAAU;;kDACrB,yXAAC,yJAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAG3C,yXAAC,6SAAK;wCAAC,WAAU;;;;;;;;;;;;0CAElB,yXAAC,2JAAW;;kDACX,yXAAC;wCAAI,WAAU;;4CACb,MAAM,aAAa,CAAC,cAAc;4CAAG;;;;;;;kDAEvC,yXAAC;wCAAE,WAAU;;4CACX,MAAM,QAAQ;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrB,yXAAC,oJAAI;;kCACJ,yXAAC,0JAAU;;0CACV,yXAAC,yJAAS;0CAAC;;;;;;0CACX,yXAAC,+JAAe;0CAAC;;;;;;;;;;;;kCAElB,yXAAC,2JAAW;kCACX,cAAA,yXAAC;4BAAI,WAAU;;8CACd,yXAAC;oCAAI,WAAU;8CACd,cAAA,yXAAC;wCAAI,WAAU;;0DACd,yXAAC,gTAAM;gDAAC,WAAU;;;;;;0DAClB,yXAAC,sJAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;;;;;;8CAIb,yXAAC,wJAAM;oCAAC,OAAO;oCAAY,eAAe;;sDACzC,yXAAC,+JAAa;4CAAC,WAAU;sDACxB,cAAA,yXAAC,6JAAW;gDAAC,aAAY;;;;;;;;;;;sDAE1B,yXAAC,+JAAa;;8DACb,yXAAC,4JAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,yXAAC,4JAAU;oDAAC,OAAO,+IAAU,CAAC,UAAU;8DAAE;;;;;;8DAG1C,yXAAC,4JAAU;oDAAC,OAAO,+IAAU,CAAC,OAAO;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5C,yXAAC,oJAAI;;kCACJ,yXAAC,0JAAU;kCACV,cAAA,yXAAC,yJAAS;;gCAAC;gCAAU,gBAAgB,MAAM;gCAAC;;;;;;;;;;;;kCAE7C,yXAAC,2JAAW;;0CACX,yXAAC,sJAAK;;kDACL,yXAAC,4JAAW;kDACX,cAAA,yXAAC,yJAAQ;;8DACR,yXAAC,0JAAS;8DAAC;;;;;;8DACX,yXAAC,0JAAS;8DAAC;;;;;;8DACX,yXAAC,0JAAS;8DAAC;;;;;;8DACX,yXAAC,0JAAS;8DAAC;;;;;;gDACV,kCAAoB,yXAAC,0JAAS;8DAAC;;;;;;;;;;;;;;;;;kDAGlC,yXAAC,0JAAS;kDACR,gBAAgB,GAAG,CAAC,CAAC,uBACrB,yXAAC,yJAAQ;;kEACR,yXAAC,0JAAS;wDAAC,WAAU;kEAAe,OAAO,GAAG;;;;;;kEAC9C,yXAAC,0JAAS;kEACT,cAAA,yXAAC,sJAAK;4DAAC,SAAS,eAAe,OAAO,IAAI;sEACxC,aAAa,OAAO,IAAI;;;;;;;;;;;kEAG3B,yXAAC,0JAAS;kEACT,cAAA,yXAAC;4DACA,WACC,OAAO,WAAW,GAAG,IAClB,+BACA;;gEAGH,OAAO,WAAW,CAAC,cAAc;gEAAG;;;;;;;;;;;;kEAGvC,yXAAC,0JAAS;kEACR,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB;;;;;;oDAE9C,kCACA,yXAAC,0JAAS;kEACT,cAAA,yXAAC,wKAAY;;8EACZ,yXAAC,+KAAmB;oEAAC,OAAO;8EAC3B,cAAA,yXAAC,wJAAM;wEAAC,SAAQ;wEAAQ,WAAU;kFACjC,cAAA,yXAAC,kUAAc;4EAAC,WAAU;;;;;;;;;;;;;;;;8EAG5B,yXAAC,+KAAmB;oEAAC,OAAM;;sFAC1B,yXAAC,4KAAgB;4EAAC,OAAO;sFACxB,cAAA,yXAAC,kTAAI;gFACJ,MAAM,CAAC,mBAAmB,EAAE,OAAO,GAAG,CAAC,KAAK,CAAC;;kGAE7C,yXAAC,mTAAI;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;wEAIlC,cACA,OAAO,IAAI,KAAK,+IAAU,CAAC,OAAO,IAClC,OAAO,WAAW,GAAG,mBACpB,yXAAC,4KAAgB;4EAChB,SAAS,IAAM,cAAc,OAAO,GAAG;;8FAEvC,yXAAC,0UAAY;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;wEAI3C,kCACA,yXAAC,4KAAgB;4EAChB,SAAS,IAAM,aAAa,OAAO,GAAG;4EACtC,WAAU;;8FAEV,yXAAC,oTAAM;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;+CArD3B,OAAO,GAAG;;;;;;;;;;;;;;;;4BAiE3B,gBAAgB,MAAM,KAAK,mBAC3B,yXAAC;gCAAI,WAAU;0CACd,cAAA,yXAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}]}