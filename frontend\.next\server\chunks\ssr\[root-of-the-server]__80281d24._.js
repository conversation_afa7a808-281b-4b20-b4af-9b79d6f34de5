module.exports=[24725,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},62303,a=>{"use strict";a.s(["DollarSign",()=>b],62303);let b=(0,a.i(621).default)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},41405,a=>{"use strict";a.s(["Label",()=>g],41405);var b=a.i(68116),c=a.i(128),d=a.i(48206),e=c.forwardRef((a,c)=>(0,b.jsx)(d.Primitive.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));e.displayName="Label";var f=a.i(22171);function g({className:a,...c}){return(0,b.jsx)(e,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...c})}},12594,a=>{"use strict";a.s(["useApi",()=>d]);var b=a.i(81223),c=a.i(34075);function d(){let{data:a}=(0,b.useSession)(),d=async(b,d={})=>{if(!a?.accessToken)throw Error("Non authentifié");return c.apiService.authenticatedRequest(b,a.accessToken,d)};return{login:c.apiService.login.bind(c.apiService),register:c.apiService.register.bind(c.apiService),authenticatedRequest:d,getUsers:()=>d("/users"),getUser:a=>d(`/users/${a}`),createUser:a=>d("/users",{method:"POST",body:JSON.stringify(a)}),updateUser:(a,b)=>d(`/users/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteUser:a=>d(`/users/${a}`,{method:"DELETE"}),getSessions:()=>d("/sessions"),getSession:a=>d(`/sessions/${a}`),createSession:a=>d("/sessions",{method:"POST",body:JSON.stringify(a)}),updateSession:(a,b)=>d(`/sessions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteSession:a=>d(`/sessions/${a}`,{method:"DELETE"}),getCaisses:()=>d("/caisses"),getCaisse:a=>d(`/caisses/${a}`),createCaisse:a=>d("/caisses",{method:"POST",body:JSON.stringify(a)}),updateCaisse:(a,b)=>d(`/caisses/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteCaisse:a=>d(`/caisses/${a}`,{method:"DELETE"}),emargerCaisse:a=>d(`/caisses/${a}/emarger`,{method:"POST"}),getReunions:()=>d("/reunions"),getReunion:a=>d(`/reunions/${a}`),updateReunion:(a,b)=>d(`/reunions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),getMembers:()=>d("/members"),getMember:a=>d(`/members/${a}`),createMember:a=>d("/members",{method:"POST",body:JSON.stringify(a)}),updateMember:(a,b)=>d(`/members/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteMember:a=>d(`/members/${a}`,{method:"DELETE"}),getMemberDebrief:(a,b)=>{let c=new URLSearchParams;b?.dateFrom&&c.append("dateFrom",b.dateFrom),b?.dateTo&&c.append("dateTo",b.dateTo),b?.sessionId&&c.append("sessionId",b.sessionId);let e=c.toString()?`?${c.toString()}`:"";return d(`/members/${a}/debrief${e}`)},createPayment:a=>d("/payments",{method:"POST",body:JSON.stringify(a)}),getSessionMembers:a=>d(`/sessions/${a}/members`),addSessionMember:a=>d("/session-members",{method:"POST",body:JSON.stringify(a)}),removeSessionMember:(a,b)=>d(`/sessions/${a}/members/${b}`,{method:"DELETE"})}}},6821,a=>{"use strict";a.s(["ArrowLeft",()=>b],6821);let b=(0,a.i(621).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},42989,a=>{"use strict";a.s(["default",()=>s]);var b=a.i(68116),c=a.i(128),d=a.i(81223),e=a.i(50395),f=a.i(64853),g=a.i(54636),h=a.i(66446),i=a.i(6821),j=a.i(20762),k=a.i(62303),l=a.i(33055),m=a.i(2979),n=a.i(78184),o=a.i(75780),p=a.i(60563),q=a.i(12594);let r=h.z.object({annee:h.z.number().min(2020,"L'année doit être supérieure à 2020").max(2050,"L'année doit être inférieure à 2050"),dateDebut:h.z.string().min(1,"La date de début est requise"),dateFin:h.z.string().min(1,"La date de fin est requise"),partFixe:h.z.number().min(1,"La part fixe doit être supérieure à 0").max(1e6,"La part fixe ne peut pas dépasser 1,000,000 FCFA")}).refine(a=>{let b=new Date(a.dateDebut);return new Date(a.dateFin)>b},{message:"La date de fin doit être postérieure à la date de début",path:["dateFin"]});function s(){let{data:a}=(0,d.useSession)(),h=(0,e.useRouter)(),s=(0,q.useApi)(),[t,u]=(0,c.useState)(!1),[v,w]=(0,c.useState)(null),x=a?.user&&"secretary_general"===a.user.role,y=(0,f.useForm)({resolver:(0,g.zodResolver)(r),defaultValues:{annee:new Date().getFullYear(),dateDebut:"",dateFin:"",partFixe:0}}),z=async a=>{if(!x)return void w("Vous n'avez pas les permissions pour créer une session");try{u(!0),w(null),await s.createSession(a),h.push("/dashboard/sessions")}catch(a){console.error("Erreur lors de la création:",a),w(a.message||"Une erreur est survenue lors de la création")}finally{u(!1)}};return x?(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(m.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,b.jsx)(l.default,{href:"/dashboard/sessions",children:(0,b.jsx)(i.ArrowLeft,{className:"h-4 w-4"})})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Nouvelle Session"}),(0,b.jsx)("p",{className:"text-muted-foreground",children:"Créer une nouvelle session de tontine"})]})]}),(0,b.jsxs)(o.Card,{children:[(0,b.jsxs)(o.CardHeader,{children:[(0,b.jsxs)(o.CardTitle,{className:"flex items-center gap-2",children:[(0,b.jsx)(j.Calendar,{className:"h-5 w-5"}),"Informations de la session"]}),(0,b.jsx)(o.CardDescription,{children:"Définissez les paramètres de la nouvelle session. Les réunions seront automatiquement générées pour chaque dimanche de la période."})]}),(0,b.jsx)(o.CardContent,{children:(0,b.jsx)(p.Form,{...y,children:(0,b.jsxs)("form",{onSubmit:y.handleSubmit(z),className:"space-y-6",children:[v&&(0,b.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,b.jsx)("p",{className:"text-sm text-red-600",children:v})}),(0,b.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,b.jsx)(p.FormField,{control:y.control,name:"annee",render:({field:a})=>(0,b.jsxs)(p.FormItem,{children:[(0,b.jsx)(p.FormLabel,{children:"Année"}),(0,b.jsx)(p.FormControl,{children:(0,b.jsx)(n.Input,{type:"number",...a,onChange:b=>a.onChange(parseInt(b.target.value)||0)})}),(0,b.jsx)(p.FormDescription,{children:"L'année de la session de tontine"}),(0,b.jsx)(p.FormMessage,{})]})}),(0,b.jsx)(p.FormField,{control:y.control,name:"partFixe",render:({field:a})=>(0,b.jsxs)(p.FormItem,{children:[(0,b.jsxs)(p.FormLabel,{className:"flex items-center gap-2",children:[(0,b.jsx)(k.DollarSign,{className:"h-4 w-4"}),"Part Fixe (FCFA)"]}),(0,b.jsx)(p.FormControl,{children:(0,b.jsx)(n.Input,{type:"number",...a,onChange:b=>a.onChange(parseInt(b.target.value)||0)})}),(0,b.jsx)(p.FormDescription,{children:"Montant de la cotisation fixe par réunion"}),(0,b.jsx)(p.FormMessage,{})]})})]}),(0,b.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,b.jsx)(p.FormField,{control:y.control,name:"dateDebut",render:({field:a})=>(0,b.jsxs)(p.FormItem,{children:[(0,b.jsx)(p.FormLabel,{children:"Date de début"}),(0,b.jsx)(p.FormControl,{children:(0,b.jsx)(n.Input,{type:"date",...a})}),(0,b.jsx)(p.FormDescription,{children:"Date de début de la session"}),(0,b.jsx)(p.FormMessage,{})]})}),(0,b.jsx)(p.FormField,{control:y.control,name:"dateFin",render:({field:a})=>(0,b.jsxs)(p.FormItem,{children:[(0,b.jsx)(p.FormLabel,{children:"Date de fin"}),(0,b.jsx)(p.FormControl,{children:(0,b.jsx)(n.Input,{type:"date",...a})}),(0,b.jsx)(p.FormDescription,{children:"Date de fin de la session"}),(0,b.jsx)(p.FormMessage,{})]})})]}),(0,b.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,b.jsx)(m.Button,{variant:"outline",asChild:!0,children:(0,b.jsx)(l.default,{href:"/dashboard/sessions",children:"Annuler"})}),(0,b.jsx)(m.Button,{type:"submit",disabled:t,children:t?"Création...":"Créer la session"})]})]})})})]}),(0,b.jsxs)(o.Card,{children:[(0,b.jsx)(o.CardHeader,{children:(0,b.jsx)(o.CardTitle,{children:"À propos des sessions"})}),(0,b.jsx)(o.CardContent,{children:(0,b.jsxs)("div",{className:"space-y-2 text-sm text-muted-foreground",children:[(0,b.jsx)("p",{children:"• Les réunions seront automatiquement créées pour chaque dimanche dans la période définie"}),(0,b.jsx)("p",{children:"• La part fixe représente le montant que chaque membre doit cotiser à chaque réunion"}),(0,b.jsx)("p",{children:"• Une fois créée, la session ne peut être modifiée que par un administrateur"}),(0,b.jsx)("p",{children:"• La suppression d'une session supprimera également toutes les réunions associées"})]})})]})]}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(m.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,b.jsx)(l.default,{href:"/dashboard/sessions",children:(0,b.jsx)(i.ArrowLeft,{className:"h-4 w-4"})})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Nouvelle Session"}),(0,b.jsx)("p",{className:"text-muted-foreground",children:"Créer une nouvelle session de tontine"})]})]}),(0,b.jsx)(o.Card,{children:(0,b.jsx)(o.CardContent,{className:"pt-6",children:(0,b.jsxs)("div",{className:"text-center py-8",children:[(0,b.jsx)("p",{className:"text-muted-foreground",children:"Vous n'avez pas les permissions pour créer une session."}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Seuls les administrateurs peuvent créer des sessions."})]})})})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__80281d24._.js.map