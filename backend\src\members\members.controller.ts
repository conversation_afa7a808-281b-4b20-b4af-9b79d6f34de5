import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../common/guards/role.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../common/enums/user-role.enum';
import { MembersService } from './members.service';
import { CreateMemberDto } from './dto/create-member.dto';
import { UpdateMemberDto } from './dto/update-member.dto';
import { PaymentFiltersDto } from '../common/dto/payment-filters.dto';

@ApiTags('members')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('members')
export class MembersController {
  constructor(private readonly membersService: MembersService) {}

  @Post()
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Créer un membre', description: 'Crée un membre avec ses informations de base (nom, contact, adresse).' })
  create(@Body() dto: CreateMemberDto) {
    return this.membersService.create(dto);
  }

  @Get()
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER)
  @ApiOperation({ summary: 'Lister les membres' })
  findAll() {
    return this.membersService.findAll();
  }

  @Get(':id')
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER)
  @ApiOperation({ summary: 'Détail d\'un membre' })
  findOne(@Param('id') id: string) {
    return this.membersService.findOne(id);
  }

  @Patch(':id')
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Mettre à jour un membre' })
  update(@Param('id') id: string, @Body() dto: UpdateMemberDto) {
    return this.membersService.update(id, dto);
  }

  @Delete(':id')
  @Roles(UserRole.SECRETARY_GENERAL)
  @ApiOperation({ summary: 'Supprimer un membre' })
  remove(@Param('id') id: string) {
    return this.membersService.remove(id);
  }

  // Debrief financier d'un membre
  @Get(':id/debrief')
  @Roles(UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER)
  @ApiOperation({ summary: 'Debrief financier d\'un membre', description: 'Totaux IN/OUT, net et agrégations pour le membre (filtrable).' })
  debrief(@Param('id') id: string, @Query() filters: PaymentFiltersDto) {
    return this.membersService.paymentsDebrief(id, filters);
  }
}