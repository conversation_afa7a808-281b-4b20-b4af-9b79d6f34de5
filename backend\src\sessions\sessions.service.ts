import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model, Types } from 'mongoose';
import { Session, SessionDocument } from './schemas/session.schema';
import { SessionMember, SessionMemberDocument } from './schemas/session-member.schema';
import { CreateSessionDto } from './dto/create-session.dto';
import { UpdateSessionDto } from './dto/update-session.dto';
import { Reunion, ReunionDocument } from '../reunions/schemas/reunion.schema';
import { AddMemberToSessionDto } from './dto/add-member.dto';
import { Payment, PaymentDocument, PaymentDirection, PaymentFunction } from '../payments/schemas/payment.schema';
import { PaymentFiltersDto } from '../common/dto/payment-filters.dto';

@Injectable()
export class SessionsService {
  constructor(
    @InjectModel(Session.name) private sessionModel: Model<SessionDocument>,
    @InjectModel(Reunion.name) private reunionModel: Model<ReunionDocument>,
    @InjectModel(SessionMember.name) private sessionMemberModel: Model<SessionMemberDocument>,
    @InjectModel(Payment.name) private paymentModel: Model<PaymentDocument>,
  ) {}

  private generateSundays(start: Date, end: Date): Date[] {
    if (end < start) throw new BadRequestException('dateFin doit être >= dateDebut');
    // Find first Sunday on/after start
    const result: Date[] = [];
    const date = new Date(start);
    const day = date.getDay(); // 0=Sunday
    const diff = (7 - day) % 7; // days until Sunday
    date.setDate(date.getDate() + diff);
    while (date <= end) {
      result.push(new Date(date));
      date.setDate(date.getDate() + 7);
    }
    return result;
  }

  async create(createDto: CreateSessionDto, createdBy: string): Promise<Session> {
    const { annee, dateDebut, dateFin, partFixe } = createDto;
    const start = new Date(dateDebut);
    const end = new Date(dateFin);

    const session = await this.sessionModel.create({
      annee,
      dateDebut: start,
      dateFin: end,
      partFixe,
      createdBy: new Types.ObjectId(createdBy),
    });

    const sundays = this.generateSundays(start, end);
    if (sundays.length === 0) {
      await session.deleteOne();
      throw new BadRequestException('Aucune réunion générée (vérifiez la période)');
    }

    // Create reunions for each Sunday
    const reunionsToCreate = sundays.map((d) => ({
      dateReunion: d,
      sessionId: session._id,
    }));
    await this.reunionModel.insertMany(reunionsToCreate);

    // Set next upcoming reunion for this session (today or future)
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const nextDate = sundays.find(d => d >= todayStart) || sundays[0];
    const nextReunionDoc = await this.reunionModel.findOne({ sessionId: session._id, dateReunion: nextDate });
    session.dateProchaineReunion = nextDate;
    (session as any).nextReunionId = nextReunionDoc?._id; // typed field exists in schema
    await session.save();


    return session.toObject();
  }

  async updateNextMeet(sessionId: string) {
    if (!Types.ObjectId.isValid(sessionId)) throw new NotFoundException('Session introuvable');
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const reunion = await this.reunionModel
      .findOne({ sessionId: new Types.ObjectId(sessionId), dateReunion: { $gte: todayStart } })
      .sort({ dateReunion: 1 });

    if (reunion) {
      await this.sessionModel.findByIdAndUpdate(sessionId, {
        $set: { dateProchaineReunion: reunion.dateReunion, nextReunionId: reunion._id },
      });
    } else {
      await this.sessionModel.findByIdAndUpdate(sessionId, {
        $unset: { dateProchaineReunion: '', nextReunionId: '' },
      });
    }
  }
  async findAll(): Promise<Session[]> {
    return this.sessionModel.find().sort({ dateDebut: 1 }).lean();
  }

  async findOne(id: string): Promise<Session | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Session introuvable');
    return this.sessionModel.findById(id).lean();
  }

  async update(id: string, dto: UpdateSessionDto): Promise<Session | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Session introuvable');
    const updated = await this.sessionModel.findByIdAndUpdate(
      id,
      {
        $set: {
          ...('annee' in dto ? { annee: dto.annee } : {}),
          ...('dateDebut' in dto ? { dateDebut: new Date(dto.dateDebut!) } : {}),
          ...('dateFin' in dto ? { dateFin: new Date(dto.dateFin!) } : {}),
          ...('partFixe' in dto ? { partFixe: dto.partFixe } : {}),
        },
      },
      { new: true },
    );
    return updated?.toObject() ?? null;
  }

  async updateAffiliation(affiliationId:string): Promise<SessionMember | null> {
    if (!Types.ObjectId.isValid(affiliationId)) throw new NotFoundException('Affiliation introuvable');
    const affiliation = await this.sessionMemberModel.findById(affiliationId);
    if(!affiliation) throw new NotFoundException('Affiliation introuvable');
    //on met à jour 
    const s = await this.sessionModel.findById(affiliation.sessionId);
    const perMeetingDue = s ? s.partFixe * affiliation.parts : 0; // montant par réunion pour ce membre
    const reunionsPassed = await this.reunionModel.countDocuments({ sessionId: affiliation.sessionId, dateReunion: { $lte: new Date() } });
    const expected = perMeetingDue * reunionsPassed;
    affiliation.expectedToDate = expected;
    affiliation.overdueAmount = Math.max(0, expected - (affiliation.paidSoFar || 0));
    await affiliation.save();
    return affiliation.toObject();
  }
  async addMember(id:string, dto: AddMemberToSessionDto): Promise<SessionMember | null> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Session introuvable');
    if (!Types.ObjectId.isValid(dto.memberId)) throw new NotFoundException('Membre introuvable');
    if (dto.parts < 1) throw new BadRequestException('parts doit être >= 1');
    const session = await this.sessionModel.findById(id);
    if (!session) throw new NotFoundException('Session introuvable');
    const reunionsCount = await this.reunionModel.countDocuments({ sessionId: new Types.ObjectId(id) });
    const totalDue = (session.partFixe || 0) * dto.parts * reunionsCount;
    const affiliation = await this.sessionMemberModel.create({
      sessionId: new Types.ObjectId(id),
      memberId: new Types.ObjectId(dto.memberId),
      parts: dto.parts,
      totalDue,
    });
    //on avant de retourner on met à jour les montants dus car le membre a rejoint la session
    return this.updateAffiliation(affiliation._id.toString());
  }

  // Debrief financier lié aux paiements d'une session (filtrable)
  async paymentsDebrief(sessionId: string, filters: PaymentFiltersDto) {
    if (!Types.ObjectId.isValid(sessionId)) throw new NotFoundException('Session introuvable');
    const q: any = { sessionId: new Types.ObjectId(sessionId) };
    if (filters.memberId) q.memberId = new Types.ObjectId(filters.memberId);
    if (filters.reunionId) q.reunionId = new Types.ObjectId(filters.reunionId);
    if (filters.direction) q.direction = filters.direction;
    if (filters.func) q.func = filters.func;

    if (filters.startDate || filters.endDate) {
      q.date = {} as any;
      if (filters.startDate) q.date.$gte = new Date(filters.startDate);
      if (filters.endDate) q.date.$lte = new Date(filters.endDate);
    }

    const payments = await this.paymentModel.find(q).lean();
    const totalIn = payments.filter(p => p.direction === PaymentDirection.IN).reduce((s,p)=>s+p.amount, 0);
    const totalOut = payments.filter(p => p.direction === PaymentDirection.OUT).reduce((s,p)=>s+p.amount, 0);

    // agrégations simples
    const byFunc = payments.reduce((acc:any, p:any)=>{
      acc[p.func] = (acc[p.func] || 0) + p.amount; return acc;
    }, {});

    return { sessionId, filters, totalIn, totalOut, net: totalIn - totalOut, byFunc, count: payments.length };
  }

  async remove(id: string): Promise<void> {
    if (!Types.ObjectId.isValid(id)) throw new NotFoundException('Session introuvable');
    // Delete reunions of session, then session
    await this.reunionModel.deleteMany({ sessionId: new Types.ObjectId(id) });
    await this.sessionModel.findByIdAndDelete(id);
  }
}