(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,44640,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return s}});class s{static get(e,r,t){let s=Reflect.get(e,r,t);return"function"==typeof s?s.bind(e):s}static set(e,r,t,s){return Reflect.set(e,r,t,s)}static has(e,r){return Reflect.has(e,r)}static deleteProperty(e,r){return Reflect.deleteProperty(e,r)}}},8356,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"afterTaskAsyncStorageInstance",{enumerable:!0,get:function(){return s}});let s=(0,e.r(97918).createAsyncLocalStorage)()},62355,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"afterTaskAsyncStorage",{enumerable:!0,get:function(){return s.afterTaskAsyncStorageInstance}});let s=e.r(8356)},17939,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(t,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return o},throwWithStaticGenerationBailoutError:function(){return a},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return i}});let s=e.r(85115),n=e.r(62355);function a(e,r){throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(e," couldn't be rendered statically because it used ").concat(r,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function i(e,r){throw Object.defineProperty(new s.StaticGenBailoutError("Route ".concat(e,' with `dynamic = "error"` couldn\'t be rendered statically because it used ').concat(r,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function o(e,r){let t=Object.defineProperty(Error("Route ".concat(e.route,' used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(t,r),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=t),t}function c(){let e=n.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},93168,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(t,{describeHasCheckingStringProperty:function(){return a},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return i}});let s=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,r){return s.test(r)?"`"+e+"."+r+"`":"`"+e+"["+JSON.stringify(r)+"]`"}function a(e,r){let t=JSON.stringify(r);return"`Reflect.has("+e+", "+t+")`, `"+t+" in "+e+"`, or similar"}let i=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},63470,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let s=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(r);if(t&&t.has(e))return t.get(e);var s={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var o=a?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(s,i,o):s[i]=e[i]}return s.default=e,t&&t.set(e,s),s}(e.r(38477));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}let a={current:null},i="function"==typeof s.cache?s.cache:e=>e,o=console.warn;function c(e){return function(){for(var r=arguments.length,t=Array(r),s=0;s<r;s++)t[s]=arguments[s];o(e(...t))}}i(e=>{try{o(a.current)}finally{a.current=null}})},4467,e=>{"use strict";e.s(["useApi",()=>s]);var r=e.i(1269),t=e.i(1831);function s(){let{data:e}=(0,r.useSession)(),s=async function(r){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(null==e?void 0:e.accessToken))throw Error("Non authentifié");return t.apiService.authenticatedRequest(r,e.accessToken,s)};return{login:t.apiService.login.bind(t.apiService),register:t.apiService.register.bind(t.apiService),authenticatedRequest:s,getUsers:()=>s("/users"),getUser:e=>s("/users/".concat(e)),createUser:e=>s("/users",{method:"POST",body:JSON.stringify(e)}),updateUser:(e,r)=>s("/users/".concat(e),{method:"PATCH",body:JSON.stringify(r)}),deleteUser:e=>s("/users/".concat(e),{method:"DELETE"}),getSessions:()=>s("/sessions"),getSession:e=>s("/sessions/".concat(e)),createSession:e=>s("/sessions",{method:"POST",body:JSON.stringify(e)}),updateSession:(e,r)=>s("/sessions/".concat(e),{method:"PATCH",body:JSON.stringify(r)}),deleteSession:e=>s("/sessions/".concat(e),{method:"DELETE"}),getCaisses:()=>s("/caisses"),getCaisse:e=>s("/caisses/".concat(e)),createCaisse:e=>s("/caisses",{method:"POST",body:JSON.stringify(e)}),updateCaisse:(e,r)=>s("/caisses/".concat(e),{method:"PATCH",body:JSON.stringify(r)}),deleteCaisse:e=>s("/caisses/".concat(e),{method:"DELETE"}),emargerCaisse:e=>s("/caisses/".concat(e,"/emarger"),{method:"POST"}),getReunions:()=>s("/reunions"),getReunion:e=>s("/reunions/".concat(e)),updateReunion:(e,r)=>s("/reunions/".concat(e),{method:"PATCH",body:JSON.stringify(r)}),getMembers:()=>s("/members"),getMember:e=>s("/members/".concat(e)),createMember:e=>s("/members",{method:"POST",body:JSON.stringify(e)}),updateMember:(e,r)=>s("/members/".concat(e),{method:"PATCH",body:JSON.stringify(r)}),deleteMember:e=>s("/members/".concat(e),{method:"DELETE"}),getMemberDebrief:(e,r)=>{let t=new URLSearchParams;(null==r?void 0:r.dateFrom)&&t.append("dateFrom",r.dateFrom),(null==r?void 0:r.dateTo)&&t.append("dateTo",r.dateTo),(null==r?void 0:r.sessionId)&&t.append("sessionId",r.sessionId);let n=t.toString()?"?".concat(t.toString()):"";return s("/members/".concat(e,"/debrief").concat(n))},createPayment:e=>s("/payments",{method:"POST",body:JSON.stringify(e)}),getSessionMembers:e=>s("/sessions/".concat(e,"/members")),addSessionMember:e=>s("/session-members",{method:"POST",body:JSON.stringify(e)}),removeSessionMember:(e,r)=>s("/sessions/".concat(e,"/members/").concat(r),{method:"DELETE"})}}},12058,e=>{"use strict";e.s(["CaisseType",()=>r,"PaymentDirection",()=>s,"PaymentFunction",()=>n,"UserRole",()=>t]);var r=function(e){return e.PRINCIPALE="PRINCIPALE",e.REUNION="REUNION",e}({}),t=function(e){return e.SECRETARY_GENERAL="secretary_general",e.CONTROLLER="controller",e.CASHIER="cashier",e}({}),s=function(e){return e.IN="IN",e.OUT="OUT",e}({}),n=function(e){return e.CONTRIBUTION="cotisation",e.TRANSFER="transfert",e.EXTERNAL="exterieur",e}({})},65429,e=>{"use strict";e.s(["Label",()=>i],65429);var r=e.i(4051),t=e.i(38477),s=e.i(38909),n=t.forwardRef((e,t)=>(0,r.jsx)(s.Primitive.label,{...e,ref:t,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var a=e.i(41428);function i(e){let{className:t,...s}=e;return(0,r.jsx)(n,{"data-slot":"label",className:(0,a.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}},14545,e=>{"use strict";e.s(["ArrowLeft",()=>r],14545);let r=(0,e.i(44571).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},547,e=>{"use strict";e.s(["Textarea",()=>s]);var r=e.i(4051),t=e.i(41428);function s(e){let{className:s,...n}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,t.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...n})}},84828,e=>{"use strict";e.s(["default",()=>y]);var r=e.i(4051),t=e.i(38477),s=e.i(57691),n=e.i(1269),a=e.i(67967),i=e.i(78381),o=e.i(45086),c=e.i(14545),l=e.i(5085),d=e.i(85205),u=e.i(96134),m=e.i(547),f=e.i(75680),p=e.i(5647),h=e.i(4467),b=e.i(12058);let g=o.z.object({firstName:o.z.string().min(2,"Le prénom doit contenir au moins 2 caractères"),lastName:o.z.string().min(2,"Le nom doit contenir au moins 2 caractères"),email:o.z.string().email("Adresse email invalide").optional().or(o.z.literal("")),phone:o.z.string().optional(),address:o.z.string().optional()});function y(){let{data:e}=(0,n.useSession)(),o=(0,s.useRouter)(),y=(0,h.useApi)(),[x,j]=(0,t.useState)(!1),[v,S]=(0,t.useState)(null),N=(null==e?void 0:e.user)&&e.user.role===b.UserRole.SECRETARY_GENERAL,O=(0,a.useForm)({resolver:(0,i.zodResolver)(g),defaultValues:{firstName:"",lastName:"",email:"",phone:"",address:""}}),E=async e=>{j(!0),S(null);try{let r={firstName:e.firstName,lastName:e.lastName,...e.email&&{email:e.email},...e.phone&&{phone:e.phone},...e.address&&{address:e.address}};await y.createMember(r),o.push("/dashboard/members")}catch(e){console.error("Erreur lors de la création du membre:",e),e instanceof Error?S(e.message):S("Erreur lors de la création du membre. Veuillez réessayer.")}finally{j(!1)}};return N?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(l.default,{href:"/dashboard/members",children:(0,r.jsxs)(d.Button,{variant:"ghost",size:"sm",children:[(0,r.jsx)(c.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Nouveau Membre"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Créer un nouveau membre de la tontine"})]})]}),(0,r.jsxs)(f.Card,{className:"max-w-2xl",children:[(0,r.jsxs)(f.CardHeader,{children:[(0,r.jsx)(f.CardTitle,{children:"Informations du membre"}),(0,r.jsx)(f.CardDescription,{children:"Remplissez tous les champs pour créer un nouveau membre"})]}),(0,r.jsx)(f.CardContent,{children:(0,r.jsx)(p.Form,{...O,children:(0,r.jsxs)("form",{onSubmit:O.handleSubmit(E),className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(p.FormField,{control:O.control,name:"firstName",render:e=>{let{field:t}=e;return(0,r.jsxs)(p.FormItem,{children:[(0,r.jsx)(p.FormLabel,{children:"Prénom *"}),(0,r.jsx)(p.FormControl,{children:(0,r.jsx)(u.Input,{placeholder:"Prénom",...t,disabled:x})}),(0,r.jsx)(p.FormMessage,{})]})}}),(0,r.jsx)(p.FormField,{control:O.control,name:"lastName",render:e=>{let{field:t}=e;return(0,r.jsxs)(p.FormItem,{children:[(0,r.jsx)(p.FormLabel,{children:"Nom *"}),(0,r.jsx)(p.FormControl,{children:(0,r.jsx)(u.Input,{placeholder:"Nom de famille",...t,disabled:x})}),(0,r.jsx)(p.FormMessage,{})]})}})]}),(0,r.jsx)(p.FormField,{control:O.control,name:"email",render:e=>{let{field:t}=e;return(0,r.jsxs)(p.FormItem,{children:[(0,r.jsx)(p.FormLabel,{children:"Email"}),(0,r.jsx)(p.FormControl,{children:(0,r.jsx)(u.Input,{type:"email",placeholder:"<EMAIL>",...t,disabled:x})}),(0,r.jsx)(p.FormMessage,{})]})}}),(0,r.jsx)(p.FormField,{control:O.control,name:"phone",render:e=>{let{field:t}=e;return(0,r.jsxs)(p.FormItem,{children:[(0,r.jsx)(p.FormLabel,{children:"Téléphone"}),(0,r.jsx)(p.FormControl,{children:(0,r.jsx)(u.Input,{placeholder:"+237123456789",...t,disabled:x})}),(0,r.jsx)(p.FormMessage,{})]})}}),(0,r.jsx)(p.FormField,{control:O.control,name:"address",render:e=>{let{field:t}=e;return(0,r.jsxs)(p.FormItem,{children:[(0,r.jsx)(p.FormLabel,{children:"Adresse"}),(0,r.jsx)(p.FormControl,{children:(0,r.jsx)(m.Textarea,{placeholder:"Adresse complète",...t,disabled:x,rows:3})}),(0,r.jsx)(p.FormMessage,{})]})}}),v&&(0,r.jsx)("div",{className:"text-red-600 text-sm bg-red-50 p-3 rounded",children:v}),(0,r.jsxs)("div",{className:"flex justify-end gap-4 pt-6",children:[(0,r.jsx)(l.default,{href:"/dashboard/members",children:(0,r.jsx)(d.Button,{variant:"outline",disabled:x,children:"Annuler"})}),(0,r.jsx)(d.Button,{type:"submit",disabled:x,children:x?"Création...":"Créer le membre"})]})]})})})]})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center gap-4",children:(0,r.jsx)(l.default,{href:"/dashboard/members",children:(0,r.jsxs)(d.Button,{variant:"ghost",size:"sm",children:[(0,r.jsx)(c.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Accès refusé"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Seuls les administrateurs peuvent créer des membres."})]})})]})}}]);