import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';

export enum HistoryAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  OPEN_CAISSE = 'OPEN_CAISSE',
  CLOSE_CAISSE = 'CLOSE_CAISSE',
  PAYMENT = 'PAYMENT',
}

export type HistoryDocument = HydratedDocument<History>;

@Schema({ timestamps: true })
export class History {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId!: Types.ObjectId;

  // target references (optional, only one or several can be set)
  @Prop({ type: Types.ObjectId, ref: 'Session', required: false })
  sessionId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Reunion', required: false })
  reunionId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Caisse', required: false })
  caisseId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Member', required: false })
  memberId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'SessionMember', required: false })
  sessionMemberId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Payment', required: false })
  paymentId?: Types.ObjectId;

  // action type
  @Prop({ required: true, enum: Object.values(HistoryAction) })
  action!: HistoryAction;

  // free JSON context to store details
  @Prop({ type: Object, required: false })
  context?: Record<string, any>;
}

export const HistorySchema = SchemaFactory.createForClass(History);