import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { UserRole } from '../../common/enums/user-role.enum';

export type UserDocument = User & Document;
@Schema({ timestamps: true })
export class User {
  @Prop({ required: true, unique: true })
  username!: string;

  @Prop({ required: true })
  password!: string; // stored hashed

  // Keep enum values in English to align with DTO validation
  @Prop({ enum: [UserRole.CASHIER, UserRole.CONTROLLER, UserRole.SECRETARY_GENERAL], required: true })
  role!: UserRole;
}

export const UserSchema = SchemaFactory.createForClass(User);
