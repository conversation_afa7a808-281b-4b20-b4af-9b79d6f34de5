// Types pour l'application Tontine

// Enums
export enum CaisseType {
  PRINCIPALE = 'PRINCIPALE',
  REUNION = 'REUNION',
}

export enum UserRole {
  SECRETARY_GENERAL = 'secretary_general',
  CONTROLLER = 'controller',
  CASHIER = 'cashier',
}

export enum UserStatus {
  ACTIF = 'actif',
  EN_ATTENTE = 'en_attente',
  SUSPENDU = 'suspendu',
}

export enum PaymentDirection {
  IN = 'IN',
  OUT = 'OUT',
}

export enum PaymentFunction {
  CONTRIBUTION = 'cotisation',
  TRANSFER = 'transfert',
  EXTERNAL = 'exterieur',
}

// Interfaces principales
export interface User {
  _id: string;
  username: string;
  role: UserRole;
  createdAt: string;
  updatedAt: string;
}

export interface Session {
  _id: string;
  annee: number;
  dateDebut: string;
  dateFin: string;
  dateProchaineReunion?: string;
  nextReunionId?: string;
  partFixe: number;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface Reunion {
  _id: string;
  dateReunion: string;
  lieu?: string;
  caissePrincipale?: string;
  sessionId: string;
  createdAt: string;
  updatedAt: string;
}

export interface Caisse {
  _id: string;
  nom: string;
  type: CaisseType;
  soldeActuel: number;
  sessionId?: string;
  createdBy: string;
  cashierId?: string;
  caissePrincipaleId?: string;
  createdAt: string;
  updatedAt: string;
}

// Nouveau: Member (différent de User)
export interface Member {
  _id: string;
  firstName: string;
  lastName: string;
  phone?: string;
  email?: string;
  address?: string;
  createdAt: string;
  updatedAt: string;
}

// Nouveau: SessionMember (inscription d'un membre à une session)
export interface SessionMember {
  _id: string;
  sessionId: string;
  memberId: string;
  parts: number;
  totalDue: number;
  paidSoFar: number;
  expectedToDate: number;
  overdueAmount: number;
  createdAt: string;
  updatedAt: string;
}

// Nouveau: Payment
export interface Payment {
  _id: string;
  direction: PaymentDirection;
  func: PaymentFunction;
  amount: number;
  date: string;
  sessionId?: string;
  reunionId?: string;
  caisseId: string;
  receivingCaisseId?: string;
  memberId?: string;
  reason?: string;
  exitOrderId?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// DTOs pour les formulaires
export interface CreateSessionDto {
  annee: number;
  dateDebut: string;
  dateFin: string;
  partFixe: number;
}

export interface UpdateSessionDto {
  annee?: number;
  dateDebut?: string;
  dateFin?: string;
  partFixe?: number;
}

export interface CreateCaisseDto {
  nom: string;
  type: CaisseType;
  soldeActuel?: number;
  sessionId?: string;
  cashierId?: string;
  caissePrincipaleId?: string;
}

export interface UpdateCaisseDto {
  nom?: string;
  type?: CaisseType;
  soldeActuel?: number;
  sessionId?: string;
  cashierId?: string;
  caissePrincipaleId?: string;
}

export interface UpdateReunionDto {
  dateReunion?: string;
  lieu?: string;
  caissePrincipale?: string;
}

// Nouveaux DTOs
export interface CreateMemberDto {
  firstName: string;
  lastName: string;
  phone?: string;
  email?: string;
  address?: string;
}

export interface UpdateMemberDto {
  firstName?: string;
  lastName?: string;
  phone?: string;
  email?: string;
  address?: string;
}

export interface CreatePaymentDto {
  direction: PaymentDirection;
  func: PaymentFunction;
  amount: number;
  caisseId: string;
  receivingCaisseId?: string;
  sessionId?: string;
  reunionId?: string;
  memberId?: string;
  reason?: string;
  exitOrderId?: string;
}

export interface CreateSessionMemberDto {
  sessionId: string;
  memberId: string;
  parts: number;
}

// Types pour les statistiques
export interface SessionStats {
  total: number;
  active: number;
  completed: number;
  totalPartFixe: number;
}

export interface CaisseStats {
  total: number;
  principales: number;
  reunions: number;
  soldeTotal: number;
  soldePrincipales: number;
  soldeReunions: number;
}

export interface MemberStats {
  total: number;
  withEmail: number;
  withPhone: number;
  withAddress: number;
}

export interface PaymentStats {
  totalIn: number;
  totalOut: number;
  netAmount: number;
  contributionsTotal: number;
  transfersTotal: number;
  externalTotal: number;
}

// Types pour les formulaires
export interface SessionForm {
  annee: number;
  dateDebut: string;
  dateFin: string;
  partFixe: number;
}

export interface CaisseForm {
  nom: string;
  type: CaisseType;
  soldeActuel: number;
  sessionId?: string;
  cashierId?: string;
  caissePrincipaleId?: string;
}

export interface MemberForm {
  firstName: string;
  lastName: string;
  phone?: string;
  email?: string;
  address?: string;
}

export interface PaymentForm {
  direction: PaymentDirection;
  func: PaymentFunction;
  amount: number;
  caisseId: string;
  receivingCaisseId?: string;
  sessionId?: string;
  reunionId?: string;
  memberId?: string;
  reason?: string;
}

// Types pour les réponses API
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Types pour les filtres
export interface SessionFilters {
  annee?: number;
  status?: 'active' | 'completed' | 'all';
}

export interface CaisseFilters {
  type?: CaisseType | 'all';
  sessionId?: string;
}

export interface MemberFilters {
  search?: string;
  hasEmail?: boolean;
  hasPhone?: boolean;
}

export interface PaymentFilters {
  direction?: PaymentDirection | 'all';
  func?: PaymentFunction | 'all';
  caisseId?: string;
  sessionId?: string;
  memberId?: string;
  dateFrom?: string;
  dateTo?: string;
}

// Types pour les réponses spéciales
export interface MemberDebrief {
  member: Member;
  totalIn: number;
  totalOut: number;
  netAmount: number;
  contributionsTotal: number;
  transfersTotal: number;
  externalTotal: number;
  payments: Payment[];
}
