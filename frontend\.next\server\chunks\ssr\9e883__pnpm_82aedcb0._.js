module.exports=[70208,a=>{"use strict";a.s(["QueryClientProvider",()=>f,"useQueryClient",()=>e]);var b=a.i(128),c=a.i(68116),d=b.createContext(void 0),e=a=>{let c=b.useContext(d);if(a)return a;if(!c)throw Error("No QueryClient set, use QueryClientProvider to set one");return c},f=({client:a,children:e})=>(b.useEffect(()=>(a.mount(),()=>{a.unmount()}),[a]),(0,c.jsx)(d.Provider,{value:a,children:e}))},84536,18777,a=>{"use strict";a.s(["addToEnd",()=>x,"addToStart",()=>y,"ensureQueryFn",()=>A,"functionalUpdate",()=>g,"hashKey",()=>o,"hashQueryKeyByOptions",()=>n,"isServer",()=>e,"isValidTimeout",()=>h,"matchMutation",()=>m,"matchQuery",()=>l,"noop",()=>f,"partialMatchKey",()=>p,"replaceData",()=>w,"resolveEnabled",()=>k,"resolveStaleTime",()=>j,"shallowEqualObjects",()=>r,"shouldThrowError",()=>B,"skipToken",()=>z,"sleep",()=>v,"timeUntilStale",()=>i],84536),a.s(["systemSetTimeoutZero",()=>d,"timeoutManager",()=>c],18777);var b={setTimeout:(a,b)=>setTimeout(a,b),clearTimeout:a=>clearTimeout(a),setInterval:(a,b)=>setInterval(a,b),clearInterval:a=>clearInterval(a)},c=new class{#a=b;#b=!1;setTimeoutProvider(a){this.#a=a}setTimeout(a,b){return this.#a.setTimeout(a,b)}clearTimeout(a){this.#a.clearTimeout(a)}setInterval(a,b){return this.#a.setInterval(a,b)}clearInterval(a){this.#a.clearInterval(a)}};function d(a){setTimeout(a,0)}var e=!0;function f(){}function g(a,b){return"function"==typeof a?a(b):a}function h(a){return"number"==typeof a&&a>=0&&a!==1/0}function i(a,b){return Math.max(a+(b||0)-Date.now(),0)}function j(a,b){return"function"==typeof a?a(b):a}function k(a,b){return"function"==typeof a?a(b):a}function l(a,b){let{type:c="all",exact:d,fetchStatus:e,predicate:f,queryKey:g,stale:h}=a;if(g){if(d){if(b.queryHash!==n(g,b.options))return!1}else if(!p(b.queryKey,g))return!1}if("all"!==c){let a=b.isActive();if("active"===c&&!a||"inactive"===c&&a)return!1}return("boolean"!=typeof h||b.isStale()===h)&&(!e||e===b.state.fetchStatus)&&(!f||!!f(b))}function m(a,b){let{exact:c,status:d,predicate:e,mutationKey:f}=a;if(f){if(!b.options.mutationKey)return!1;if(c){if(o(b.options.mutationKey)!==o(f))return!1}else if(!p(b.options.mutationKey,f))return!1}return(!d||b.state.status===d)&&(!e||!!e(b))}function n(a,b){return(b?.queryKeyHashFn||o)(a)}function o(a){return JSON.stringify(a,(a,b)=>t(b)?Object.keys(b).sort().reduce((a,c)=>(a[c]=b[c],a),{}):b)}function p(a,b){return a===b||typeof a==typeof b&&!!a&&!!b&&"object"==typeof a&&"object"==typeof b&&Object.keys(b).every(c=>p(a[c],b[c]))}var q=Object.prototype.hasOwnProperty;function r(a,b){if(!b||Object.keys(a).length!==Object.keys(b).length)return!1;for(let c in a)if(a[c]!==b[c])return!1;return!0}function s(a){return Array.isArray(a)&&a.length===Object.keys(a).length}function t(a){if(!u(a))return!1;let b=a.constructor;if(void 0===b)return!0;let c=b.prototype;return!!u(c)&&!!c.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(a)===Object.prototype}function u(a){return"[object Object]"===Object.prototype.toString.call(a)}function v(a){return new Promise(b=>{c.setTimeout(b,a)})}function w(a,b,c){return"function"==typeof c.structuralSharing?c.structuralSharing(a,b):!1!==c.structuralSharing?function a(b,c){if(b===c)return b;let d=s(b)&&s(c);if(!d&&!(t(b)&&t(c)))return c;let e=(d?b:Object.keys(b)).length,f=d?c:Object.keys(c),g=f.length,h=d?Array(g):{},i=0;for(let j=0;j<g;j++){let g=d?j:f[j],k=b[g],l=c[g];if(k===l){h[g]=k,(d?j<e:q.call(b,g))&&i++;continue}if(null===k||null===l||"object"!=typeof k||"object"!=typeof l){h[g]=l;continue}let m=a(k,l);h[g]=m,m===k&&i++}return e===g&&i===e?b:h}(a,b):b}function x(a,b,c=0){let d=[...a,b];return c&&d.length>c?d.slice(1):d}function y(a,b,c=0){let d=[b,...a];return c&&d.length>c?d.slice(0,-1):d}var z=Symbol();function A(a,b){return!a.queryFn&&b?.initialPromise?()=>b.initialPromise:a.queryFn&&a.queryFn!==z?a.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${a.queryHash}'`))}function B(a,b){return"function"==typeof a?a(...b):!!a}},99731,a=>{"use strict";a.s(["notifyManager",()=>c]);var b=a.i(18777).systemSetTimeoutZero,c=function(){let a=[],c=0,d=a=>{a()},e=a=>{a()},f=b,g=b=>{c?a.push(b):f(()=>{d(b)})};return{batch:b=>{let g;c++;try{g=b()}finally{--c||(()=>{let b=a;a=[],b.length&&f(()=>{e(()=>{b.forEach(a=>{d(a)})})})})()}return g},batchCalls:a=>(...b)=>{g(()=>{a(...b)})},schedule:g,setNotifyFunction:a=>{d=a},setBatchNotifyFunction:a=>{e=a},setScheduler:a=>{f=a}}}()},20475,a=>{"use strict";a.s(["Subscribable",()=>b]);var b=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(a){return this.listeners.add(a),this.onSubscribe(),()=>{this.listeners.delete(a),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},15285,a=>{"use strict";a.s(["focusManager",()=>d]);var b=a.i(20475),c=a.i(84536),d=new class extends b.Subscribable{#c;#d;#e;constructor(){super(),this.#e=a=>{if(!c.isServer&&window.addEventListener){let b=()=>a();return window.addEventListener("visibilitychange",b,!1),()=>{window.removeEventListener("visibilitychange",b)}}}}onSubscribe(){this.#d||this.setEventListener(this.#e)}onUnsubscribe(){this.hasListeners()||(this.#d?.(),this.#d=void 0)}setEventListener(a){this.#e=a,this.#d?.(),this.#d=a(a=>{"boolean"==typeof a?this.setFocused(a):this.onFocus()})}setFocused(a){this.#c!==a&&(this.#c=a,this.onFocus())}onFocus(){let a=this.isFocused();this.listeners.forEach(b=>{b(a)})}isFocused(){return"boolean"==typeof this.#c?this.#c:globalThis.document?.visibilityState!=="hidden"}}},25405,92467,11257,5268,99785,a=>{"use strict";a.s(["Query",()=>n,"fetchState",()=>o],25405);var b=a.i(84536),c=a.i(99731);a.s(["CancelledError",()=>j,"canFetch",()=>i,"createRetryer",()=>k],5268);var d=a.i(15285);a.s(["onlineManager",()=>f],92467);var e=a.i(20475),f=new class extends e.Subscribable{#f=!0;#d;#e;constructor(){super(),this.#e=a=>{if(!b.isServer&&window.addEventListener){let b=()=>a(!0),c=()=>a(!1);return window.addEventListener("online",b,!1),window.addEventListener("offline",c,!1),()=>{window.removeEventListener("online",b),window.removeEventListener("offline",c)}}}}onSubscribe(){this.#d||this.setEventListener(this.#e)}onUnsubscribe(){this.hasListeners()||(this.#d?.(),this.#d=void 0)}setEventListener(a){this.#e=a,this.#d?.(),this.#d=a(this.setOnline.bind(this))}setOnline(a){this.#f!==a&&(this.#f=a,this.listeners.forEach(b=>{b(a)}))}isOnline(){return this.#f}};function g(){let a,b,c=new Promise((c,d)=>{a=c,b=d});function d(a){Object.assign(c,a),delete c.resolve,delete c.reject}return c.status="pending",c.catch(()=>{}),c.resolve=b=>{d({status:"fulfilled",value:b}),a(b)},c.reject=a=>{d({status:"rejected",reason:a}),b(a)},c}function h(a){return Math.min(1e3*2**a,3e4)}function i(a){return(a??"online")!=="online"||f.isOnline()}a.s(["pendingThenable",()=>g],11257);var j=class extends Error{constructor(a){super("CancelledError"),this.revert=a?.revert,this.silent=a?.silent}};function k(a){let c,e=!1,k=0,l=g(),m=()=>d.focusManager.isFocused()&&("always"===a.networkMode||f.isOnline())&&a.canRun(),n=()=>i(a.networkMode)&&a.canRun(),o=a=>{"pending"===l.status&&(c?.(),l.resolve(a))},p=a=>{"pending"===l.status&&(c?.(),l.reject(a))},q=()=>new Promise(b=>{c=a=>{("pending"!==l.status||m())&&b(a)},a.onPause?.()}).then(()=>{c=void 0,"pending"===l.status&&a.onContinue?.()}),r=()=>{let c;if("pending"!==l.status)return;let d=0===k?a.initialPromise:void 0;try{c=d??a.fn()}catch(a){c=Promise.reject(a)}Promise.resolve(c).then(o).catch(c=>{if("pending"!==l.status)return;let d=a.retry??3*!b.isServer,f=a.retryDelay??h,g="function"==typeof f?f(k,c):f,i=!0===d||"number"==typeof d&&k<d||"function"==typeof d&&d(k,c);if(e||!i)return void p(c);k++,a.onFail?.(k,c),(0,b.sleep)(g).then(()=>m()?void 0:q()).then(()=>{e?p(c):r()})})};return{promise:l,status:()=>l.status,cancel:b=>{if("pending"===l.status){let c=new j(b);p(c),a.onCancel?.(c)}},continue:()=>(c?.(),l),cancelRetry:()=>{e=!0},continueRetry:()=>{e=!1},canStart:n,start:()=>(n()?r():q().then(r),l)}}a.s(["Removable",()=>m],99785);var l=a.i(18777),m=class{#g;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,b.isValidTimeout)(this.gcTime)&&(this.#g=l.timeoutManager.setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(a){this.gcTime=Math.max(this.gcTime||0,a??(b.isServer?1/0:3e5))}clearGcTimeout(){this.#g&&(l.timeoutManager.clearTimeout(this.#g),this.#g=void 0)}},n=class extends m{#h;#i;#j;#k;#l;#m;#n;constructor(a){super(),this.#n=!1,this.#m=a.defaultOptions,this.setOptions(a.options),this.observers=[],this.#k=a.client,this.#j=this.#k.getQueryCache(),this.queryKey=a.queryKey,this.queryHash=a.queryHash,this.#h=p(this.options),this.state=a.state??this.#h,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#l?.promise}setOptions(a){if(this.options={...this.#m,...a},this.updateGcTime(this.options.gcTime),this.state&&void 0===this.state.data){let a=p(this.options);void 0!==a.data&&(this.setData(a.data,{updatedAt:a.dataUpdatedAt,manual:!0}),this.#h=a)}}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#j.remove(this)}setData(a,c){let d=(0,b.replaceData)(this.state.data,a,this.options);return this.#o({data:d,type:"success",dataUpdatedAt:c?.updatedAt,manual:c?.manual}),d}setState(a,b){this.#o({type:"setState",state:a,setStateOptions:b})}cancel(a){let c=this.#l?.promise;return this.#l?.cancel(a),c?c.then(b.noop).catch(b.noop):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#h)}isActive(){return this.observers.some(a=>!1!==(0,b.resolveEnabled)(a.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===b.skipToken||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(a=>"static"===(0,b.resolveStaleTime)(a.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(a=>a.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(a=0){return void 0===this.state.data||"static"!==a&&(!!this.state.isInvalidated||!(0,b.timeUntilStale)(this.state.dataUpdatedAt,a))}onFocus(){let a=this.observers.find(a=>a.shouldFetchOnWindowFocus());a?.refetch({cancelRefetch:!1}),this.#l?.continue()}onOnline(){let a=this.observers.find(a=>a.shouldFetchOnReconnect());a?.refetch({cancelRefetch:!1}),this.#l?.continue()}addObserver(a){this.observers.includes(a)||(this.observers.push(a),this.clearGcTimeout(),this.#j.notify({type:"observerAdded",query:this,observer:a}))}removeObserver(a){this.observers.includes(a)&&(this.observers=this.observers.filter(b=>b!==a),this.observers.length||(this.#l&&(this.#n?this.#l.cancel({revert:!0}):this.#l.cancelRetry()),this.scheduleGc()),this.#j.notify({type:"observerRemoved",query:this,observer:a}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#o({type:"invalidate"})}async fetch(a,c){if("idle"!==this.state.fetchStatus&&this.#l?.status()!=="rejected"){if(void 0!==this.state.data&&c?.cancelRefetch)this.cancel({silent:!0});else if(this.#l)return this.#l.continueRetry(),this.#l.promise}if(a&&this.setOptions(a),!this.options.queryFn){let a=this.observers.find(a=>a.options.queryFn);a&&this.setOptions(a.options)}let d=new AbortController,e=a=>{Object.defineProperty(a,"signal",{enumerable:!0,get:()=>(this.#n=!0,d.signal)})},f=()=>{let a=(0,b.ensureQueryFn)(this.options,c),d=(()=>{let a={client:this.#k,queryKey:this.queryKey,meta:this.meta};return e(a),a})();return(this.#n=!1,this.options.persister)?this.options.persister(a,d,this):a(d)},g=(()=>{let a={fetchOptions:c,options:this.options,queryKey:this.queryKey,client:this.#k,state:this.state,fetchFn:f};return e(a),a})();this.options.behavior?.onFetch(g,this),this.#i=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==g.fetchOptions?.meta)&&this.#o({type:"fetch",meta:g.fetchOptions?.meta}),this.#l=k({initialPromise:c?.initialPromise,fn:g.fetchFn,onCancel:a=>{a instanceof j&&a.revert&&this.setState({...this.#i,fetchStatus:"idle"}),d.abort()},onFail:(a,b)=>{this.#o({type:"failed",failureCount:a,error:b})},onPause:()=>{this.#o({type:"pause"})},onContinue:()=>{this.#o({type:"continue"})},retry:g.options.retry,retryDelay:g.options.retryDelay,networkMode:g.options.networkMode,canRun:()=>!0});try{let a=await this.#l.start();if(void 0===a)throw Error(`${this.queryHash} data is undefined`);return this.setData(a),this.#j.config.onSuccess?.(a,this),this.#j.config.onSettled?.(a,this.state.error,this),a}catch(a){if(a instanceof j){if(a.silent)return this.#l.promise;else if(a.revert){if(void 0===this.state.data)throw a;return this.state.data}}throw this.#o({type:"error",error:a}),this.#j.config.onError?.(a,this),this.#j.config.onSettled?.(this.state.data,a,this),a}finally{this.scheduleGc()}}#o(a){let b=b=>{switch(a.type){case"failed":return{...b,fetchFailureCount:a.failureCount,fetchFailureReason:a.error};case"pause":return{...b,fetchStatus:"paused"};case"continue":return{...b,fetchStatus:"fetching"};case"fetch":return{...b,...o(b.data,this.options),fetchMeta:a.meta??null};case"success":let c={...b,data:a.data,dataUpdateCount:b.dataUpdateCount+1,dataUpdatedAt:a.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!a.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};return this.#i=a.manual?c:void 0,c;case"error":let d=a.error;return{...b,error:d,errorUpdateCount:b.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:b.fetchFailureCount+1,fetchFailureReason:d,fetchStatus:"idle",status:"error"};case"invalidate":return{...b,isInvalidated:!0};case"setState":return{...b,...a.state}}};this.state=b(this.state),c.notifyManager.batch(()=>{this.observers.forEach(a=>{a.onQueryUpdate()}),this.#j.notify({query:this,type:"updated",action:a})})}};function o(a,b){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:i(b.networkMode)?"fetching":"paused",...void 0===a&&{error:null,status:"pending"}}}function p(a){let b="function"==typeof a.initialData?a.initialData():a.initialData,c=void 0!==b,d=c?"function"==typeof a.initialDataUpdatedAt?a.initialDataUpdatedAt():a.initialDataUpdatedAt:0;return{data:b,dataUpdateCount:0,dataUpdatedAt:c?d??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:c?"success":"pending",fetchStatus:"idle"}}},61289,a=>{"use strict";a.s(["Mutation",()=>e,"getDefaultState",()=>f]);var b=a.i(99731),c=a.i(99785),d=a.i(5268),e=class extends c.Removable{#p;#q;#l;constructor(a){super(),this.mutationId=a.mutationId,this.#q=a.mutationCache,this.#p=[],this.state=a.state||f(),this.setOptions(a.options),this.scheduleGc()}setOptions(a){this.options=a,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(a){this.#p.includes(a)||(this.#p.push(a),this.clearGcTimeout(),this.#q.notify({type:"observerAdded",mutation:this,observer:a}))}removeObserver(a){this.#p=this.#p.filter(b=>b!==a),this.scheduleGc(),this.#q.notify({type:"observerRemoved",mutation:this,observer:a})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#q.remove(this))}continue(){return this.#l?.continue()??this.execute(this.state.variables)}async execute(a){let b=()=>{this.#o({type:"continue"})};this.#l=(0,d.createRetryer)({fn:()=>this.options.mutationFn?this.options.mutationFn(a):Promise.reject(Error("No mutationFn found")),onFail:(a,b)=>{this.#o({type:"failed",failureCount:a,error:b})},onPause:()=>{this.#o({type:"pause"})},onContinue:b,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#q.canRun(this)});let c="pending"===this.state.status,e=!this.#l.canStart();try{if(c)b();else{this.#o({type:"pending",variables:a,isPaused:e}),await this.#q.config.onMutate?.(a,this);let b=await this.options.onMutate?.(a);b!==this.state.context&&this.#o({type:"pending",context:b,variables:a,isPaused:e})}let d=await this.#l.start();return await this.#q.config.onSuccess?.(d,a,this.state.context,this),await this.options.onSuccess?.(d,a,this.state.context),await this.#q.config.onSettled?.(d,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(d,null,a,this.state.context),this.#o({type:"success",data:d}),d}catch(b){try{throw await this.#q.config.onError?.(b,a,this.state.context,this),await this.options.onError?.(b,a,this.state.context),await this.#q.config.onSettled?.(void 0,b,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,b,a,this.state.context),b}finally{this.#o({type:"error",error:b})}}finally{this.#q.runNext(this)}}#o(a){this.state=(b=>{switch(a.type){case"failed":return{...b,failureCount:a.failureCount,failureReason:a.error};case"pause":return{...b,isPaused:!0};case"continue":return{...b,isPaused:!1};case"pending":return{...b,context:a.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:a.isPaused,status:"pending",variables:a.variables,submittedAt:Date.now()};case"success":return{...b,data:a.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...b,data:void 0,error:a.error,failureCount:b.failureCount+1,failureReason:a.error,isPaused:!1,status:"error"}}})(this.state),b.notifyManager.batch(()=>{this.#p.forEach(b=>{b.onMutationUpdate(a)}),this.#q.notify({mutation:this,type:"updated",action:a})})}};function f(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}}];

//# sourceMappingURL=9e883__pnpm_82aedcb0._.js.map