import 'reflect-metadata';
import { Test } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { ReunionsController } from '../src/reunions/reunions.controller';
import { ReunionsService } from '../src/reunions/reunions.service';
import { AppTestingModule } from './app-testing.module';

const reunionsServiceMock = {
  findAll: jest.fn().mockResolvedValue([{ _id: 'r1' }]),
  findOne: jest.fn().mockResolvedValue({ _id: 'r1' }),
  expectedPayments: jest.fn().mockResolvedValue([]),
  debrief: jest.fn().mockResolvedValue({ totalIn: 100, totalOut: 40, state: { membersExpected: 10, expectedTotal: 200, receivedContrib: 100, gap: 100 } }),
  update: jest.fn().mockResolvedValue({ _id: 'r1', lieu: 'A' }),
};

describe('ReunionsController', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      controllers: [ReunionsController],
      providers: [{ provide: ReunionsService, useValue: reunionsServiceMock }],
    })
      .overrideGuard(require('../src/auth/jwt-auth.guard').JwtAuthGuard)
      .useClass(require('./utils/mock-guards').MockJwtAuthGuard)
      .overrideGuard(require('../src/common/guards/role.guard').RolesGuard)
      .useClass(require('./utils/mock-guards').MockRolesGuard)
      .compile();

    app = moduleRef.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('GET /reunions', async () => {
    await request(app.getHttpServer()).get('/reunions').set('x-user-role','cashier').expect(200);
  });

  it('GET /reunions/:id', async () => {
    await request(app.getHttpServer()).get('/reunions/r1').set('x-user-role','controller').expect(200);
  });

  it('GET /reunions/:id/expected-payments', async () => {
    await request(app.getHttpServer()).get('/reunions/r1/expected-payments').set('x-user-role','secretary_general').expect(200);
  });

  it('GET /reunions/:id/debrief', async () => {
    await request(app.getHttpServer()).get('/reunions/r1/debrief').set('x-user-role','controller').expect(200);
  });

  it('GET /reunions/:id/state', async () => {
    const res = await request(app.getHttpServer()).get('/reunions/r1/state').set('x-user-role','cashier').expect(200);
    expect(res.body.state).toBeDefined();
  });

  it('PATCH /reunions/:id', async () => {
    await request(app.getHttpServer()).patch('/reunions/r1').set('x-user-role','secretary_general').send({ lieu: 'A' }).expect(200);
  });
});