{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\n\nimport { cn } from '@/lib/utils';\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot='label'\n      className={cn(\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Label };\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,yXAAC,kSAAmB;QAClB,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/form.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { Slot } from '@radix-ui/react-slot';\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues\n} from 'react-hook-form';\n\nimport { cn } from '@/lib/utils';\nimport { Label } from '@/components/ui/label';\n\nconst Form = FormProvider;\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n> = {\n  name: TName;\n};\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n);\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  );\n};\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext);\n  const itemContext = React.useContext(FormItemContext);\n  const { getFieldState } = useFormContext();\n  const formState = useFormState({ name: fieldContext.name });\n  const fieldState = getFieldState(fieldContext.name, formState);\n\n  if (!fieldContext) {\n    throw new Error('useFormField should be used within <FormField>');\n  }\n\n  const { id } = itemContext;\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState\n  };\n};\n\ntype FormItemContextValue = {\n  id: string;\n};\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n);\n\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\n  const id = React.useId();\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot='form-item'\n        className={cn('grid gap-2', className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  );\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField();\n\n  return (\n    <Label\n      data-slot='form-label'\n      data-error={!!error}\n      className={cn('data-[error=true]:text-destructive', className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  );\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } =\n    useFormField();\n\n  return (\n    <Slot\n      data-slot='form-control'\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  );\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\n  const { formDescriptionId } = useFormField();\n\n  return (\n    <p\n      data-slot='form-description'\n      id={formDescriptionId}\n      className={cn('text-muted-foreground text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\n  const { error, formMessageId } = useFormField();\n  const body = error ? String(error?.message ?? '') : props.children;\n\n  if (!body) {\n    return null;\n  }\n\n  return (\n    <p\n      data-slot='form-message'\n      id={formMessageId}\n      className={cn('text-destructive text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,mRAAY;AASzB,MAAM,iCAAmB,iWAAmB,CAC1C,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,yXAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,yXAAC,iRAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,8VAAgB,CAAC;IACtC,MAAM,cAAc,8VAAgB,CAAC;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,IAAA,qRAAc;IACxC,MAAM,YAAY,IAAA,mRAAY,EAAC;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,iWAAmB,CACzC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,yVAAW;IAEtB,qBACE,yXAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,yXAAC;YACC,aAAU;YACV,WAAW,IAAA,qIAAE,EAAC,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,yXAAC,sJAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,IAAA,qIAAE,EAAC,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAC3D;IAEF,qBACE,yXAAC,yTAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,yXAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,IAAA,qIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,yXAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,IAAA,qIAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,yXAAC,mSAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,yXAAC,oSAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,yXAAC,oSAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,yXAAC,sSAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,qIAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,yXAAC,mSAAoB;gBAAC,OAAO;0BAC3B,cAAA,yXAAC,2UAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,yXAAC,qSAAsB;kBACrB,cAAA,yXAAC,sSAAuB;YACtB,aAAU;YACV,WAAW,IAAA,qIAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,yXAAC;;;;;8BACD,yXAAC,uSAAwB;oBACvB,WAAW,IAAA,qIAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,yXAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,yXAAC,oSAAqB;QACpB,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,yXAAC,mSAAoB;QACnB,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,yXAAC;gBAAK,WAAU;0BACd,cAAA,yXAAC,4SAA6B;8BAC5B,cAAA,yXAAC,qTAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,yXAAC,uSAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,yXAAC,wSAAyB;QACxB,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,yXAAC,6SAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,yXAAC,qUAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,yXAAC,+SAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,yXAAC,2UAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/auth.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from \"next-auth/providers/credentials\";\nimport { z } from \"zod\";\nimport { apiService } from \"./api\";\nimport { decodeJwt } from \"./utils\";\n\nconst loginSchema = z.object({\n\tusername: z.string().min(1, \"Username is required\"),\n\tpassword: z.string().min(1, \"Password is required\"),\n});\n\nexport const { handlers, signIn, signOut, auth } = NextAuth({\n\tproviders: [\n\t\tCredentialsProvider({\n\t\t\tname: \"credentials\",\n\t\t\tcredentials: {\n\t\t\t\tusername: { label: \"Username\", type: \"text\" },\n\t\t\t\tpassword: { label: \"Password\", type: \"password\" },\n\t\t\t},\n\t\t\tasync authorize(credentials) {\n\t\t\t\ttry {\n\t\t\t\t\tconst { username, password } = loginSchema.parse(credentials);\n\n\t\t\t\t\t// Authentification avec l'API backend\n\t\t\t\t\tconst response = await apiService.login({ username, password });\n\t\t\t\t\tconst jwt = decodeJwt(response.access_token);\n\t\t\t\t\tif(!jwt) return null;\n\t\t\t\t\tconst jwtPayload = jwt.payload as unknown as { sub: string; username: string; role:  'secretary_general' | 'controller' | 'cashier' };\n\t\t\t\t\t\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tid: jwtPayload.sub,\n\t\t\t\t\t\t\tusername: jwtPayload.username,\n\t\t\t\t\t\t\trole: jwtPayload.role,\n\t\t\t\t\t\t\taccessToken: response.access_token,\n\t\t\t\t\t}\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"Erreur d'authentification:\", error);\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\t\t}),\n\t],\n\tpages: {\n\t\tsignIn: \"/auth/signin\",\n\t},\n\tcallbacks: {\n\t\tauthorized: ({ auth }) => !!auth,\n\t\tasync jwt({ token, user }) {\n\t\t\tif (user) {\n\t\t\t\ttoken.username = user.username;\n\t\t\t\ttoken.role = user.role;\n\t\t\t\ttoken.accessToken = user.accessToken;\n\t\t\t}\n\t\t\treturn token;\n\t\t},\n\t\tasync session({ session, token }) {\n\t\t\tif (token) {\n\t\t\t\tsession.user.id = token.sub || \"\";\n\t\t\t\tsession.user.username = token.username as string;\n\t\t\t\tsession.user.role = token.role as  'secretary_general' | 'controller' | 'cashier';\n\t\t\t\tsession.accessToken = token.accessToken as string;\n\t\t\t}\n\t\t\treturn session;\n\t\t},\n\t},\n\tsession: {\n\t\tstrategy: \"jwt\",\n\t},\n});\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,cAAc,8OAAC,CAAC,MAAM,CAAC;IAC5B,UAAU,8OAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,UAAU,8OAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC7B;AAEO,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAA,wRAAQ,EAAC;IAC3D,WAAW;QACV,IAAA,6OAAmB,EAAC;YACnB,MAAM;YACN,aAAa;gBACZ,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAO;gBAC5C,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YACjD;YACA,MAAM,WAAU,WAAW;gBAC1B,IAAI;oBACH,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,YAAY,KAAK,CAAC;oBAEjD,sCAAsC;oBACtC,MAAM,WAAW,MAAM,2IAAU,CAAC,KAAK,CAAC;wBAAE;wBAAU;oBAAS;oBAC7D,MAAM,MAAM,IAAA,4IAAS,EAAC,SAAS,YAAY;oBAC3C,IAAG,CAAC,KAAK,OAAO;oBAChB,MAAM,aAAa,IAAI,OAAO;oBAE7B,OAAO;wBACN,IAAI,WAAW,GAAG;wBAClB,UAAU,WAAW,QAAQ;wBAC7B,MAAM,WAAW,IAAI;wBACrB,aAAa,SAAS,YAAY;oBACpC;gBAED,EAAE,OAAO,OAAO;oBACf,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,OAAO;gBACR;YACD;QACD;KACA;IACD,OAAO;QACN,QAAQ;IACT;IACA,WAAW;QACV,YAAY,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC,CAAC;QAC5B,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACxB,IAAI,MAAM;gBACT,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,WAAW,GAAG,KAAK,WAAW;YACrC;YACA,OAAO;QACR;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC/B,IAAI,OAAO;gBACV,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI;gBAC/B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,WAAW,GAAG,MAAM,WAAW;YACxC;YACA,OAAO;QACR;IACD;IACA,SAAS;QACR,UAAU;IACX;AACD", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/api.ts"], "sourcesContent": ["import { signOut } from \"next-auth/react\";\nimport { signOut as ServerSignOut } from \"./auth\";\nimport { Caisse, Session } from \"@/types\";\n\n// Configuration de l'API\nexport const API_BASE_URL =\n\tprocess.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000\";\n\n// Types pour l'authentification\nexport interface LoginRequest {\n\tusername: string;\n\tpassword: string;\n}\n\nexport interface LoginResponse {\n\taccess_token: string;\n\tuser: {\n\t\tid: string;\n\t\tusername: string;\n\t\tnom: string;\n\t\tprenom: string;\n\t\temail: string;\n\t\trole: \"admin\" | \"tresorier\" | \"membre\";\n\t\tstatut: \"actif\" | \"en_attente\" | \"suspendu\";\n\t};\n}\n\nexport interface RegisterRequest {\n\tusername: string;\n\tpassword: string;\n\trole: string;\n\t// statut?: \"actif\" | \"en_attente\" | \"suspendu\";\n}\n\nexport interface ApiError {\n\tmessage: string;\n\tstatusCode: number;\n\terror?: string;\n}\n\n// Service API\nexport class ApiService {\n\tprivate baseUrl: string;\n\n\tconstructor(baseUrl: string = API_BASE_URL) {\n\t\tthis.baseUrl = baseUrl;\n\t}\n\n\tprivate async request<T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\tconst url = `${this.baseUrl}${endpoint}`;\n\n\t\tconst config: RequestInit = {\n\t\t\theaders: {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t...options.headers,\n\t\t\t},\n\t\t\t...options,\n\t\t};\n\n\t\ttry {\n\t\t\tconst response = await fetch(url, config);\n\n\t\t\tif (!response.ok) {\n\t\t\t\tconst errorData: ApiError = await response.json().catch(() => ({\n\t\t\t\t\tmessage: \"Une erreur est survenue\",\n\t\t\t\t\tstatusCode: response.status,\n\t\t\t\t}));\n\n\t\t\t\t// 🚨 Gestion du cas 401\n\t\t\t\tif (response.status === 401) {\n\t\t\t\t\t// Si côté client → on déconnecte\n\t\t\t\t\tif (typeof window !== \"undefined\") {\n\t\t\t\t\t\tawait signOut({ callbackUrl: \"/auth/signin\" });\n\t\t\t\t\t} else {\n\t\t\t\t\t\tawait ServerSignOut({ redirectTo: \"/auth/signin\" });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthrow new Error(errorData.message || `HTTP ${response.status}`);\n\t\t\t}\n\n\t\t\treturn await response.json();\n\t\t} catch (error) {\n\t\t\tconsole.log(error);\n\n\t\t\tif (error instanceof Error) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t\tthrow new Error(\"Erreur de connexion au serveur\");\n\t\t}\n\t}\n\n\t// Authentification\n\tasync login(credentials: LoginRequest): Promise<LoginResponse> {\n\t\treturn this.request<LoginResponse>(\"/auth/login\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(credentials),\n\t\t});\n\t}\n\n\tasync register(\n\t\tuserData: RegisterRequest,\n\t): Promise<{ message: string; user: any }> {\n\t\treturn this.request<{ message: string; user: any }>(\"/auth/register\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(userData),\n\t\t});\n\t}\n\n\t// Méthodes avec authentification\n\tasync authenticatedRequest<T>(\n\t\tendpoint: string,\n\t\ttoken: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\treturn this.request<T>(endpoint, {\n\t\t\t...options,\n\t\t\theaders: {\n\t\t\t\t...options.headers,\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\tAuthorization: `Bearer ${token}`,\n\t\t\t},\n\t\t});\n\t}\n\n\t// Utilisateurs\n\tasync getUsers(token: string): Promise<any[]> {\n\t\treturn this.authenticatedRequest<any[]>(\"/users\", token);\n\t}\n\n\tasync getUser(id: string, token: string): Promise<any> {\n\t\treturn this.authenticatedRequest<any>(`/users/${id}`, token);\n\t}\n\n\tasync getSessions(token: string): Promise<Session[]> {\n\t\treturn this.authenticatedRequest<Session[]>(\"/sessions\", token);\n\t}\n\n\tasync getCaisses(token: string): Promise<Caisse[]> {\n\t\treturn this.authenticatedRequest<Caisse[]>(\"/caisses\", token);\n\t}\n}\n\n// Instance par défaut\nexport const apiService = new ApiService();\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAIO,MAAM,eACZ,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAmC7B,MAAM;IACJ,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC3C,IAAI,CAAC,OAAO,GAAG;IAChB;IAEA,MAAc,QACb,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACZ;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC3B,SAAS;gBACR,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACnB;YACA,GAAG,OAAO;QACX;QAEA,IAAI;YACH,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACjB,MAAM,YAAsB,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;wBAC9D,SAAS;wBACT,YAAY,SAAS,MAAM;oBAC5B,CAAC;gBAED,wBAAwB;gBACxB,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC5B,iCAAiC;oBACjC;;yBAEO;wBACN,MAAM,IAAA,yIAAa,EAAC;4BAAE,YAAY;wBAAe;oBAClD;gBACD;gBACA,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;YAC/D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC3B,EAAE,OAAO,OAAO;YACf,QAAQ,GAAG,CAAC;YAEZ,IAAI,iBAAiB,OAAO;gBAC3B,MAAM;YACP;YACA,MAAM,IAAI,MAAM;QACjB;IACD;IAEA,mBAAmB;IACnB,MAAM,MAAM,WAAyB,EAA0B;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAgB,eAAe;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,MAAM,SACL,QAAyB,EACiB;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAiC,kBAAkB;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,iCAAiC;IACjC,MAAM,qBACL,QAAgB,EAChB,KAAa,EACb,UAAuB,CAAC,CAAC,EACZ;QACb,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAChC,GAAG,OAAO;YACV,SAAS;gBACR,GAAG,QAAQ,OAAO;gBAClB,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;YACjC;QACD;IACD;IAEA,eAAe;IACf,MAAM,SAAS,KAAa,EAAkB;QAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAQ,UAAU;IACnD;IAEA,MAAM,QAAQ,EAAU,EAAE,KAAa,EAAgB;QACtD,OAAO,IAAI,CAAC,oBAAoB,CAAM,CAAC,OAAO,EAAE,IAAI,EAAE;IACvD;IAEA,MAAM,YAAY,KAAa,EAAsB;QACpD,OAAO,IAAI,CAAC,oBAAoB,CAAY,aAAa;IAC1D;IAEA,MAAM,WAAW,KAAa,EAAqB;QAClD,OAAO,IAAI,CAAC,oBAAoB,CAAW,YAAY;IACxD;AACD;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/use-api.ts"], "sourcesContent": ["import { useSession } from \"next-auth/react\";\nimport { apiService } from \"@/lib/api\";\nimport {\n\tSession,\n\tCaisse,\n\tReunion,\n\tMember,\n\tPayment,\n\tSessionMember,\n\tCreateSessionDto,\n\tUpdateSessionDto,\n\tCreateCaisseDto,\n\tUpdateCaisseDto,\n\tUpdateReunionDto,\n\tCreateMemberDto,\n\tUpdateMemberDto,\n\tCreatePaymentDto,\n\tCreateSessionMemberDto,\n\tMemberDebrief,\n\tPaymentFilters,\n} from \"@/types\";\n\nexport function useApi() {\n\tconst { data: session } = useSession();\n\n\tconst authenticatedRequest = async <T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> => {\n\t\tif (!session?.accessToken) {\n\t\t\tthrow new Error(\"Non authentifié\");\n\t\t}\n\n\t\treturn apiService.authenticatedRequest<T>(\n\t\t\tendpoint,\n\t\t\tsession.accessToken,\n\t\t\toptions,\n\t\t);\n\t};\n\n\treturn {\n\t\t// Méthodes d'authentification (pas besoin de token)\n\t\tlogin: apiService.login.bind(apiService),\n\t\tregister: apiService.register.bind(apiService),\n\n\t\t// Méthodes authentifiées\n\t\tauthenticatedRequest,\n\n\t\t// Raccourcis pour les endpoints courants\n\t\tgetUsers: () => authenticatedRequest<any[]>(\"/users\"),\n\t\tgetUser: (id: string) => authenticatedRequest<any>(`/users/${id}`),\n\t\tcreateUser: (userData: any) =>\n\t\t\tauthenticatedRequest<any>(\"/users\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tupdateUser: (id: string, userData: any) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tdeleteUser: (id: string) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Sessions\n\t\tgetSessions: () => authenticatedRequest<Session[]>(\"/sessions\"),\n\t\tgetSession: (id: string) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`),\n\t\tcreateSession: (sessionData: CreateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(\"/sessions\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tupdateSession: (id: string, sessionData: UpdateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tdeleteSession: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Caisses\n\t\tgetCaisses: () => authenticatedRequest<Caisse[]>(\"/caisses\"),\n\t\tgetCaisse: (id: string) => authenticatedRequest<Caisse>(`/caisses/${id}`),\n\t\tcreateCaisse: (caisseData: CreateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(\"/caisses\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tupdateCaisse: (id: string, caisseData: UpdateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tdeleteCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\temargerCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}/emarger`, {\n\t\t\t\tmethod: \"POST\",\n\t\t\t}),\n\n\t\t// Réunions\n\t\tgetReunions: () => authenticatedRequest<Reunion[]>(\"/reunions\"),\n\t\tgetReunion: (id: string) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`),\n\t\tupdateReunion: (id: string, reunionData: UpdateReunionDto) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(reunionData),\n\t\t\t}),\n\n\t\t// Members\n\t\tgetMembers: () => authenticatedRequest<Member[]>(\"/members\"),\n\t\tgetMember: (id: string) => authenticatedRequest<Member>(`/members/${id}`),\n\t\tcreateMember: (memberData: CreateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(\"/members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tupdateMember: (id: string, memberData: UpdateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(`/members/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tdeleteMember: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/members/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\tgetMemberDebrief: (id: string, filters?: PaymentFilters) => {\n\t\t\tconst params = new URLSearchParams();\n\t\t\tif (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);\n\t\t\tif (filters?.dateTo) params.append('dateTo', filters.dateTo);\n\t\t\tif (filters?.sessionId) params.append('sessionId', filters.sessionId);\n\t\t\tconst query = params.toString() ? `?${params.toString()}` : '';\n\t\t\treturn authenticatedRequest<MemberDebrief>(`/members/${id}/debrief${query}`);\n\t\t},\n\n\t\t// Payments\n\t\tcreatePayment: (paymentData: CreatePaymentDto) =>\n\t\t\tauthenticatedRequest<Payment>(\"/payments\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(paymentData),\n\t\t\t}),\n\n\t\t// Session Members (inscription des membres aux sessions)\n\t\tgetSessionMembers: (sessionId: string) =>\n\t\t\tauthenticatedRequest<SessionMember[]>(`/sessions/${sessionId}/members`),\n\t\taddSessionMember: (sessionMemberData: CreateSessionMemberDto) =>\n\t\t\tauthenticatedRequest<SessionMember>(\"/session-members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionMemberData),\n\t\t\t}),\n\t\tremoveSessionMember: (sessionId: string, memberId: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${sessionId}/members/${memberId}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t};\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAqBO,SAAS;IACf,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IAEpC,MAAM,uBAAuB,OAC5B,UACA,UAAuB,CAAC,CAAC;QAEzB,IAAI,CAAC,SAAS,aAAa;YAC1B,MAAM,IAAI,MAAM;QACjB;QAEA,OAAO,2IAAU,CAAC,oBAAoB,CACrC,UACA,QAAQ,WAAW,EACnB;IAEF;IAEA,OAAO;QACN,oDAAoD;QACpD,OAAO,2IAAU,CAAC,KAAK,CAAC,IAAI,CAAC,2IAAU;QACvC,UAAU,2IAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,2IAAU;QAE7C,yBAAyB;QACzB;QAEA,yCAAyC;QACzC,UAAU,IAAM,qBAA4B;QAC5C,SAAS,CAAC,KAAe,qBAA0B,CAAC,OAAO,EAAE,IAAI;QACjE,YAAY,CAAC,WACZ,qBAA0B,UAAU;gBACnC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,YAAY,CAAC,IAAY,WACxB,qBAA0B,CAAC,OAAO,EAAE,IAAI,EAAE;gBACzC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,YAAY,CAAC,KACZ,qBAA0B,CAAC,OAAO,EAAE,IAAI,EAAE;gBACzC,QAAQ;YACT;QAED,WAAW;QACX,aAAa,IAAM,qBAAgC;QACnD,YAAY,CAAC,KACZ,qBAA8B,CAAC,UAAU,EAAE,IAAI;QAChD,eAAe,CAAC,cACf,qBAA8B,aAAa;gBAC1C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,eAAe,CAAC,IAAY,cAC3B,qBAA8B,CAAC,UAAU,EAAE,IAAI,EAAE;gBAChD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,eAAe,CAAC,KACf,qBAA2B,CAAC,UAAU,EAAE,IAAI,EAAE;gBAC7C,QAAQ;YACT;QAED,UAAU;QACV,YAAY,IAAM,qBAA+B;QACjD,WAAW,CAAC,KAAe,qBAA6B,CAAC,SAAS,EAAE,IAAI;QACxE,cAAc,CAAC,aACd,qBAA6B,YAAY;gBACxC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,IAAY,aAC1B,qBAA6B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC9C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,KACd,qBAA2B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC5C,QAAQ;YACT;QACD,eAAe,CAAC,KACf,qBAA6B,CAAC,SAAS,EAAE,GAAG,QAAQ,CAAC,EAAE;gBACtD,QAAQ;YACT;QAED,WAAW;QACX,aAAa,IAAM,qBAAgC;QACnD,YAAY,CAAC,KACZ,qBAA8B,CAAC,UAAU,EAAE,IAAI;QAChD,eAAe,CAAC,IAAY,cAC3B,qBAA8B,CAAC,UAAU,EAAE,IAAI,EAAE;gBAChD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QAED,UAAU;QACV,YAAY,IAAM,qBAA+B;QACjD,WAAW,CAAC,KAAe,qBAA6B,CAAC,SAAS,EAAE,IAAI;QACxE,cAAc,CAAC,aACd,qBAA6B,YAAY;gBACxC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,IAAY,aAC1B,qBAA6B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC9C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,KACd,qBAA2B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC5C,QAAQ;YACT;QACD,kBAAkB,CAAC,IAAY;YAC9B,MAAM,SAAS,IAAI;YACnB,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YACjE,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YAC3D,IAAI,SAAS,WAAW,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;YACpE,MAAM,QAAQ,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG;YAC5D,OAAO,qBAAoC,CAAC,SAAS,EAAE,GAAG,QAAQ,EAAE,OAAO;QAC5E;QAEA,WAAW;QACX,eAAe,CAAC,cACf,qBAA8B,aAAa;gBAC1C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QAED,yDAAyD;QACzD,mBAAmB,CAAC,YACnB,qBAAsC,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC;QACvE,kBAAkB,CAAC,oBAClB,qBAAoC,oBAAoB;gBACvD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,qBAAqB,CAAC,WAAmB,WACxC,qBAA2B,CAAC,UAAU,EAAE,UAAU,SAAS,EAAE,UAAU,EAAE;gBACxE,QAAQ;YACT;IACF;AACD", "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/types/index.ts"], "sourcesContent": ["// Types pour l'application Tontine\n\n// Enums\nexport enum CaisseType {\n  PRINCIPALE = 'PRINCIPALE',\n  REUNION = 'REUNION',\n}\n\nexport enum UserRole {\n  SECRETARY_GENERAL = 'secretary_general',\n  CONTROLLER = 'controller',\n  CASHIER = 'cashier',\n}\n\nexport enum UserStatus {\n  ACTIF = 'actif',\n  EN_ATTENTE = 'en_attente',\n  SUSPENDU = 'suspendu',\n}\n\nexport enum PaymentDirection {\n  IN = 'IN',\n  OUT = 'OUT',\n}\n\nexport enum PaymentFunction {\n  CONTRIBUTION = 'cotisation',\n  TRANSFER = 'transfert',\n  EXTERNAL = 'exterieur',\n}\n\n// Interfaces principales\nexport interface User {\n  _id: string;\n  username: string;\n  role: UserRole;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Session {\n  _id: string;\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  dateProchaineReunion?: string;\n  nextReunionId?: string;\n  partFixe: number;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Reunion {\n  _id: string;\n  dateReunion: string;\n  lieu?: string;\n  caissePrincipale?: string;\n  sessionId: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Caisse {\n  _id: string;\n  nom: string;\n  type: CaisseType;\n  soldeActuel: number;\n  sessionId?: string;\n  createdBy: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: Member (différent de User)\nexport interface Member {\n  _id: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: SessionMember (inscription d'un membre à une session)\nexport interface SessionMember {\n  _id: string;\n  sessionId: string;\n  memberId: string;\n  parts: number;\n  totalDue: number;\n  paidSoFar: number;\n  expectedToDate: number;\n  overdueAmount: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: Payment\nexport interface Payment {\n  _id: string;\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  date: string;\n  sessionId?: string;\n  reunionId?: string;\n  caisseId: string;\n  receivingCaisseId?: string;\n  memberId?: string;\n  reason?: string;\n  exitOrderId?: string;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// DTOs pour les formulaires\nexport interface CreateSessionDto {\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  partFixe: number;\n}\n\nexport interface UpdateSessionDto {\n  annee?: number;\n  dateDebut?: string;\n  dateFin?: string;\n  partFixe?: number;\n}\n\nexport interface CreateCaisseDto {\n  nom: string;\n  type: CaisseType;\n  soldeActuel?: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface UpdateCaisseDto {\n  nom?: string;\n  type?: CaisseType;\n  soldeActuel?: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface UpdateReunionDto {\n  dateReunion?: string;\n  lieu?: string;\n  caissePrincipale?: string;\n}\n\n// Nouveaux DTOs\nexport interface CreateMemberDto {\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface UpdateMemberDto {\n  firstName?: string;\n  lastName?: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface CreatePaymentDto {\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  caisseId: string;\n  receivingCaisseId?: string;\n  sessionId?: string;\n  reunionId?: string;\n  memberId?: string;\n  reason?: string;\n  exitOrderId?: string;\n}\n\nexport interface CreateSessionMemberDto {\n  sessionId: string;\n  memberId: string;\n  parts: number;\n}\n\n// Types pour les statistiques\nexport interface SessionStats {\n  total: number;\n  active: number;\n  completed: number;\n  totalPartFixe: number;\n}\n\nexport interface CaisseStats {\n  total: number;\n  principales: number;\n  reunions: number;\n  soldeTotal: number;\n  soldePrincipales: number;\n  soldeReunions: number;\n}\n\nexport interface MemberStats {\n  total: number;\n  withEmail: number;\n  withPhone: number;\n  withAddress: number;\n}\n\nexport interface PaymentStats {\n  totalIn: number;\n  totalOut: number;\n  netAmount: number;\n  contributionsTotal: number;\n  transfersTotal: number;\n  externalTotal: number;\n}\n\n// Types pour les formulaires\nexport interface SessionForm {\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  partFixe: number;\n}\n\nexport interface CaisseForm {\n  nom: string;\n  type: CaisseType;\n  soldeActuel: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface MemberForm {\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface PaymentForm {\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  caisseId: string;\n  receivingCaisseId?: string;\n  sessionId?: string;\n  reunionId?: string;\n  memberId?: string;\n  reason?: string;\n}\n\n// Types pour les réponses API\nexport interface ApiResponse<T> {\n  data: T;\n  message?: string;\n  success: boolean;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n// Types pour les filtres\nexport interface SessionFilters {\n  annee?: number;\n  status?: 'active' | 'completed' | 'all';\n}\n\nexport interface CaisseFilters {\n  type?: CaisseType | 'all';\n  sessionId?: string;\n}\n\nexport interface MemberFilters {\n  search?: string;\n  hasEmail?: boolean;\n  hasPhone?: boolean;\n}\n\nexport interface PaymentFilters {\n  direction?: PaymentDirection | 'all';\n  func?: PaymentFunction | 'all';\n  caisseId?: string;\n  sessionId?: string;\n  memberId?: string;\n  dateFrom?: string;\n  dateTo?: string;\n}\n\n// Types pour les réponses spéciales\nexport interface MemberDebrief {\n  member: Member;\n  totalIn: number;\n  totalOut: number;\n  netAmount: number;\n  contributionsTotal: number;\n  transfersTotal: number;\n  externalTotal: number;\n  payments: Payment[];\n}\n"], "names": [], "mappings": "AAAA,mCAAmC;AAEnC,QAAQ;;;;;;;;;;;;;AACD,IAAA,AAAK,oCAAA;;;WAAA;;AAKL,IAAA,AAAK,kCAAA;;;;WAAA;;AAML,IAAA,AAAK,oCAAA;;;;WAAA;;AAML,IAAA,AAAK,0CAAA;;;WAAA;;AAKL,IAAA,AAAK,yCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/app/dashboard/caisses/new/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSession } from \"next-auth/react\";\nimport { useRouter } from \"next/navigation\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport { ArrowLeft, Wallet, DollarSign } from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport {\n\tForm,\n\tFormControl,\n\tFormDescription,\n\tFormField,\n\tFormItem,\n\tFormLabel,\n\tFormMessage,\n} from \"@/components/ui/form\";\nimport {\n\tSelect,\n\tSelectContent,\n\tSelectItem,\n\tSelectTrigger,\n\tSelectValue,\n} from \"@/components/ui/select\";\nimport { useApi } from \"@/hooks/use-api\";\nimport { CaisseForm, CaisseType, Session, Caisse } from \"@/types\";\n\nconst caisseSchema = z\n\t.object({\n\t\tnom: z\n\t\t\t.string()\n\t\t\t.min(1, \"Le nom est requis\")\n\t\t\t.max(100, \"Le nom est trop long\"),\n\t\ttype: z.nativeEnum(CaisseType, {\n\t\t\trequired_error: \"Le type est requis\",\n\t\t}),\n\t\tsoldeActuel: z\n\t\t\t.number()\n\t\t\t.gte(0, \"Le solde ne peut pas être négatif\")\n\t\t\t.max(10000000, \"Le solde ne peut pas dépasser 10,000,000 FCFA\"),\n\t\tsessionId: z.string().optional(),\n\t\tcaissePrincipaleId: z.string().optional(),\n\t})\n\t.refine(\n\t\t(data) => {\n\t\t\tif (data.type === CaisseType.REUNION) {\n\t\t\t\treturn data.sessionId && data.caissePrincipaleId;\n\t\t\t}\n\t\t\treturn true;\n\t\t},\n\t\t{\n\t\t\tmessage:\n\t\t\t\t\"Pour une caisse de réunion, la session et la caisse principale sont requises\",\n\t\t\tpath: [\"sessionId\"],\n\t\t},\n\t);\n\nexport default function NewCaissePage() {\n\tconst { data: session, status } = useSession();\n\tconst router = useRouter();\n\tconst api = useApi();\n\n\tconst [isLoading, setIsLoading] = useState(false);\n\tconst [error, setError] = useState<string | null>(null);\n\tconst [sessions, setSessions] = useState<Session[]>([]);\n\tconst [caissesPrincipales, setCaissesPrincipales] = useState<Caisse[]>([]);\n\tconst [loadingData, setLoadingData] = useState(true);\n\n\t// Vérifier les permissions\n\tconst canCreateCaisses =\n\t\tsession?.user && (session.user as any).role === \"secretary_general\";\n\n\tconst form = useForm<CaisseForm>({\n\t\tresolver: zodResolver(caisseSchema),\n\t\tdefaultValues: {\n\t\t\tnom: \"\",\n\t\t\ttype: CaisseType.PRINCIPALE,\n\t\t\tsoldeActuel: 0,\n\t\t\tsessionId: \"\",\n\t\t\tcaissePrincipaleId: \"\",\n\t\t},\n\t});\n\n\tconst watchType = form.watch(\"type\");\n\n\t// Charger les données nécessaires\n\tuseEffect(() => {\n\t\tconst loadData = async () => {\n\t\t\ttry {\n\t\t\t\tsetLoadingData(true);\n\t\t\t\tconst [sessionsData, caissesData] = await Promise.all([\n\t\t\t\t\tapi.getSessions(),\n\t\t\t\t\tapi.getCaisses(),\n\t\t\t\t]);\n\n\t\t\t\tsetSessions(sessionsData);\n\t\t\t\tsetCaissesPrincipales(\n\t\t\t\t\tcaissesData.filter((c) => c.type === CaisseType.PRINCIPALE),\n\t\t\t\t);\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(\"Erreur lors du chargement des données:\", error);\n\t\t\t} finally {\n\t\t\t\tsetLoadingData(false);\n\t\t\t}\n\t\t};\n\n\t\tif (session?.accessToken) {\n\t\t\tloadData();\n\t\t}\n\t}, [status]);\n\n\tconst onSubmit = async (data: CaisseForm) => {\n\t\tif (!canCreateCaisses) {\n\t\t\tsetError(\"Vous n'avez pas les permissions pour créer une caisse\");\n\t\t\treturn;\n\t\t}\n\n\t\ttry {\n\t\t\tsetIsLoading(true);\n\t\t\tsetError(null);\n\n\t\t\t// Préparer les données\n\t\t\tconst caisseData = {\n\t\t\t\tnom: data.nom,\n\t\t\t\ttype: data.type,\n\t\t\t\tsoldeActuel: data.soldeActuel,\n\t\t\t\t...(data.type === CaisseType.REUNION && {\n\t\t\t\t\tsessionId: data.sessionId,\n\t\t\t\t\tcaissePrincipaleId: data.caissePrincipaleId,\n\t\t\t\t}),\n\t\t\t};\n\n\t\t\tawait api.createCaisse(caisseData);\n\t\t\trouter.push(\"/dashboard/caisses\");\n\t\t} catch (error: any) {\n\t\t\tconsole.error(\"Erreur lors de la création:\", error);\n\t\t\tsetError(error.message || \"Une erreur est survenue lors de la création\");\n\t\t} finally {\n\t\t\tsetIsLoading(false);\n\t\t}\n\t};\n\n\tif (!canCreateCaisses) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Button variant=\"outline\" size=\"icon\" asChild>\n\t\t\t\t\t\t<Link href=\"/dashboard/caisses\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4\" />\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</Button>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<h1 className=\"text-3xl font-bold tracking-tight\">\n\t\t\t\t\t\t\tNouvelle Caisse\n\t\t\t\t\t\t</h1>\n\t\t\t\t\t\t<p className=\"text-muted-foreground\">Créer une nouvelle caisse</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<Card>\n\t\t\t\t\t<CardContent className=\"pt-6\">\n\t\t\t\t\t\t<div className=\"text-center py-8\">\n\t\t\t\t\t\t\t<p className=\"text-muted-foreground\">\n\t\t\t\t\t\t\t\tVous n'avez pas les permissions pour créer une caisse.\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t<p className=\"text-sm text-muted-foreground mt-2\">\n\t\t\t\t\t\t\t\tSeuls les administrateurs et trésoriers peuvent créer des\n\t\t\t\t\t\t\t\tcaisses.\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</CardContent>\n\t\t\t\t</Card>\n\t\t\t</div>\n\t\t);\n\t}\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* En-tête */}\n\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t<Button variant=\"outline\" size=\"icon\" asChild>\n\t\t\t\t\t<Link href=\"/dashboard/caisses\">\n\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4\" />\n\t\t\t\t\t</Link>\n\t\t\t\t</Button>\n\t\t\t\t<div>\n\t\t\t\t\t<h1 className=\"text-3xl font-bold tracking-tight\">Nouvelle Caisse</h1>\n\t\t\t\t\t<p className=\"text-muted-foreground\">\n\t\t\t\t\t\tCréer une nouvelle caisse principale ou de réunion\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t{/* Formulaire */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle className=\"flex items-center gap-2\">\n\t\t\t\t\t\t<Wallet className=\"h-5 w-5\" />\n\t\t\t\t\t\tInformations de la caisse\n\t\t\t\t\t</CardTitle>\n\t\t\t\t\t<CardDescription>\n\t\t\t\t\t\tDéfinissez les paramètres de la nouvelle caisse\n\t\t\t\t\t</CardDescription>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t{loadingData ? (\n\t\t\t\t\t\t<div className=\"flex items-center justify-center py-8\">\n\t\t\t\t\t\t\t<div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900\"></div>\n\t\t\t\t\t\t\t<span className=\"ml-2\">Chargement des données...</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t) : (\n\t\t\t\t\t\t<Form {...form}>\n\t\t\t\t\t\t\t<form\n\t\t\t\t\t\t\t\tonSubmit={form.handleSubmit(onSubmit)}\n\t\t\t\t\t\t\t\tclassName=\"space-y-6\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{error && (\n\t\t\t\t\t\t\t\t\t<div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-sm text-red-600\">{error}</p>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t\t\t<div className=\"grid gap-6 md:grid-cols-2\">\n\t\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\t\tname=\"nom\"\n\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Nom de la caisse</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tplaceholder=\"Ex: Caisse Principale 2025\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t\tNom descriptif de la caisse\n\t\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\t\tname=\"type\"\n\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Type de caisse</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\t\t\tonValueChange={field.onChange}\n\t\t\t\t\t\t\t\t\t\t\t\t\tdefaultValue={field.value}\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Sélectionnez le type\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value={CaisseType.PRINCIPALE}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tPrincipale\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem value={CaisseType.REUNION}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tRéunion\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{watchType === CaisseType.PRINCIPALE\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"Caisse pour les fonds consolidés\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"Caisse liée à une session spécifique\"}\n\t\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\tname=\"soldeActuel\"\n\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t<FormLabel className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t\t\t\t<DollarSign className=\"h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t\tSolde initial (FCFA)\n\t\t\t\t\t\t\t\t\t\t\t</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t<Input\n\t\t\t\t\t\t\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t{...field}\n\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={(e) =>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfield.onChange(parseInt(e.target.value) || 0)\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\tMontant initial dans la caisse\n\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t{watchType === CaisseType.REUNION && (\n\t\t\t\t\t\t\t\t\t<div className=\"grid gap-6 md:grid-cols-2\">\n\t\t\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\t\t\tname=\"sessionId\"\n\t\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Session associée</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonValueChange={field.onChange}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdefaultValue={field.value}\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Sélectionnez une session\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{sessions.map((session) => (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={session._id}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={session._id}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{session.annee} (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{new Date(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsession.dateDebut,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t).toLocaleDateString()}{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t-{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{new Date(\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsession.dateFin,\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t).toLocaleDateString()}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tSession à laquelle cette caisse est liée\n\t\t\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\t/>\n\n\t\t\t\t\t\t\t\t\t\t<FormField\n\t\t\t\t\t\t\t\t\t\t\tcontrol={form.control}\n\t\t\t\t\t\t\t\t\t\t\tname=\"caissePrincipaleId\"\n\t\t\t\t\t\t\t\t\t\t\trender={({ field }) => (\n\t\t\t\t\t\t\t\t\t\t\t\t<FormItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormLabel>Caisse principale</FormLabel>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Select\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tonValueChange={field.onChange}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdefaultValue={field.value}\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectValue placeholder=\"Sélectionnez une caisse principale\" />\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectTrigger>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</FormControl>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{caissesPrincipales.map((caisse) => (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<SelectItem key={caisse._id} value={caisse._id}>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{caisse.nom} (\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{caisse.soldeActuel.toLocaleString()} FCFA)\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectItem>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</SelectContent>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Select>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tCaisse principale pour l'émargement\n\t\t\t\t\t\t\t\t\t\t\t\t\t</FormDescription>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<FormMessage />\n\t\t\t\t\t\t\t\t\t\t\t\t</FormItem>\n\t\t\t\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t)}\n\n\t\t\t\t\t\t\t\t<div className=\"flex justify-end gap-4\">\n\t\t\t\t\t\t\t\t\t<Button variant=\"outline\" asChild>\n\t\t\t\t\t\t\t\t\t\t<Link href=\"/dashboard/caisses\">Annuler</Link>\n\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t<Button type=\"submit\" disabled={isLoading}>\n\t\t\t\t\t\t\t\t\t\t{isLoading ? \"Création...\" : \"Créer la caisse\"}\n\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</form>\n\t\t\t\t\t\t</Form>\n\t\t\t\t\t)}\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\n\t\t\t{/* Informations supplémentaires */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle>Types de caisses</CardTitle>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<div className=\"space-y-4\">\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<h4 className=\"font-medium\">Caisse Principale</h4>\n\t\t\t\t\t\t\t<p className=\"text-sm text-muted-foreground\">\n\t\t\t\t\t\t\t\tCaisse pour consolider les fonds de toutes les réunions. Les\n\t\t\t\t\t\t\t\tfonds des caisses de réunion peuvent être émargés vers cette\n\t\t\t\t\t\t\t\tcaisse.\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<h4 className=\"font-medium\">Caisse de Réunion</h4>\n\t\t\t\t\t\t\t<p className=\"text-sm text-muted-foreground\">\n\t\t\t\t\t\t\t\tCaisse liée à une session spécifique. Doit être associée à une\n\t\t\t\t\t\t\t\tcaisse principale pour permettre l'émargement des fonds.\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAOA;AASA;AAOA;AACA;AArCA;;;;;;;;;;;;;;;;;AAuCA,MAAM,eAAe,8OAAC,CACpB,MAAM,CAAC;IACP,KAAK,8OAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG,qBACP,GAAG,CAAC,KAAK;IACX,MAAM,8OAAC,CAAC,UAAU,CAAC,+IAAU,EAAE;QAC9B,gBAAgB;IACjB;IACA,aAAa,8OAAC,CACZ,MAAM,GACN,GAAG,CAAC,GAAG,qCACP,GAAG,CAAC,UAAU;IAChB,WAAW,8OAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,oBAAoB,8OAAC,CAAC,MAAM,GAAG,QAAQ;AACxC,GACC,MAAM,CACN,CAAC;IACA,IAAI,KAAK,IAAI,KAAK,+IAAU,CAAC,OAAO,EAAE;QACrC,OAAO,KAAK,SAAS,IAAI,KAAK,kBAAkB;IACjD;IACA,OAAO;AACR,GACA;IACC,SACC;IACD,MAAM;QAAC;KAAY;AACpB;AAGa,SAAS;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,IAAA,2QAAU;IAC5C,MAAM,SAAS,IAAA,0RAAS;IACxB,MAAM,MAAM,IAAA,gJAAM;IAElB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,4VAAQ,EAAC;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,4VAAQ,EAAgB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,4VAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,4VAAQ,EAAW,EAAE;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,4VAAQ,EAAC;IAE/C,2BAA2B;IAC3B,MAAM,mBACL,SAAS,QAAQ,AAAC,QAAQ,IAAI,CAAS,IAAI,KAAK;IAEjD,MAAM,OAAO,IAAA,8QAAO,EAAa;QAChC,UAAU,IAAA,gSAAW,EAAC;QACtB,eAAe;YACd,KAAK;YACL,MAAM,+IAAU,CAAC,UAAU;YAC3B,aAAa;YACb,WAAW;YACX,oBAAoB;QACrB;IACD;IAEA,MAAM,YAAY,KAAK,KAAK,CAAC;IAE7B,kCAAkC;IAClC,IAAA,6VAAS,EAAC;QACT,MAAM,WAAW;YAChB,IAAI;gBACH,eAAe;gBACf,MAAM,CAAC,cAAc,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;oBACrD,IAAI,WAAW;oBACf,IAAI,UAAU;iBACd;gBAED,YAAY;gBACZ,sBACC,YAAY,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,+IAAU,CAAC,UAAU;YAE5D,EAAE,OAAO,OAAO;gBACf,QAAQ,KAAK,CAAC,0CAA0C;YACzD,SAAU;gBACT,eAAe;YAChB;QACD;QAEA,IAAI,SAAS,aAAa;YACzB;QACD;IACD,GAAG;QAAC;KAAO;IAEX,MAAM,WAAW,OAAO;QACvB,IAAI,CAAC,kBAAkB;YACtB,SAAS;YACT;QACD;QAEA,IAAI;YACH,aAAa;YACb,SAAS;YAET,uBAAuB;YACvB,MAAM,aAAa;gBAClB,KAAK,KAAK,GAAG;gBACb,MAAM,KAAK,IAAI;gBACf,aAAa,KAAK,WAAW;gBAC7B,GAAI,KAAK,IAAI,KAAK,+IAAU,CAAC,OAAO,IAAI;oBACvC,WAAW,KAAK,SAAS;oBACzB,oBAAoB,KAAK,kBAAkB;gBAC5C,CAAC;YACF;YAEA,MAAM,IAAI,YAAY,CAAC;YACvB,OAAO,IAAI,CAAC;QACb,EAAE,OAAO,OAAY;YACpB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS,MAAM,OAAO,IAAI;QAC3B,SAAU;YACT,aAAa;QACd;IACD;IAEA,IAAI,CAAC,kBAAkB;QACtB,qBACC,yXAAC;YAAI,WAAU;;8BACd,yXAAC;oBAAI,WAAU;;sCACd,yXAAC,wJAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAO,OAAO;sCAC5C,cAAA,yXAAC,kTAAI;gCAAC,MAAK;0CACV,cAAA,yXAAC,6TAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAGvB,yXAAC;;8CACA,yXAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAGlD,yXAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAIvC,yXAAC,oJAAI;8BACJ,cAAA,yXAAC,2JAAW;wBAAC,WAAU;kCACtB,cAAA,yXAAC;4BAAI,WAAU;;8CACd,yXAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,yXAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASxD;IAEA,qBACC,yXAAC;QAAI,WAAU;;0BAEd,yXAAC;gBAAI,WAAU;;kCACd,yXAAC,wJAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAO,OAAO;kCAC5C,cAAA,yXAAC,kTAAI;4BAAC,MAAK;sCACV,cAAA,yXAAC,6TAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAGvB,yXAAC;;0CACA,yXAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,yXAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAOvC,yXAAC,oJAAI;;kCACJ,yXAAC,0JAAU;;0CACV,yXAAC,yJAAS;gCAAC,WAAU;;kDACpB,yXAAC,gTAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,yXAAC,+JAAe;0CAAC;;;;;;;;;;;;kCAIlB,yXAAC,2JAAW;kCACV,4BACA,yXAAC;4BAAI,WAAU;;8CACd,yXAAC;oCAAI,WAAU;;;;;;8CACf,yXAAC;oCAAK,WAAU;8CAAO;;;;;;;;;;;iDAGxB,yXAAC,oJAAI;4BAAE,GAAG,IAAI;sCACb,cAAA,yXAAC;gCACA,UAAU,KAAK,YAAY,CAAC;gCAC5B,WAAU;;oCAET,uBACA,yXAAC;wCAAI,WAAU;kDACd,cAAA,yXAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;kDAIvC,yXAAC;wCAAI,WAAU;;0DACd,yXAAC,yJAAS;gDACT,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACjB,yXAAC,wJAAQ;;0EACR,yXAAC,yJAAS;0EAAC;;;;;;0EACX,yXAAC,2JAAW;0EACX,cAAA,yXAAC,sJAAK;oEACL,aAAY;oEACX,GAAG,KAAK;;;;;;;;;;;0EAGX,yXAAC,+JAAe;0EAAC;;;;;;0EAGjB,yXAAC,2JAAW;;;;;;;;;;;;;;;;0DAKf,yXAAC,yJAAS;gDACT,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACjB,yXAAC,wJAAQ;;0EACR,yXAAC,yJAAS;0EAAC;;;;;;0EACX,yXAAC,wJAAM;gEACN,eAAe,MAAM,QAAQ;gEAC7B,cAAc,MAAM,KAAK;;kFAEzB,yXAAC,2JAAW;kFACX,cAAA,yXAAC,+JAAa;sFACb,cAAA,yXAAC,6JAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG3B,yXAAC,+JAAa;;0FACb,yXAAC,4JAAU;gFAAC,OAAO,+IAAU,CAAC,UAAU;0FAAE;;;;;;0FAG1C,yXAAC,4JAAU;gFAAC,OAAO,+IAAU,CAAC,OAAO;0FAAE;;;;;;;;;;;;;;;;;;0EAKzC,yXAAC,+JAAe;0EACd,cAAc,+IAAU,CAAC,UAAU,GACjC,qCACA;;;;;;0EAEJ,yXAAC,2JAAW;;;;;;;;;;;;;;;;;;;;;;kDAMhB,yXAAC,yJAAS;wCACT,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACjB,yXAAC,wJAAQ;;kEACR,yXAAC,yJAAS;wDAAC,WAAU;;0EACpB,yXAAC,gUAAU;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGnC,yXAAC,2JAAW;kEACX,cAAA,yXAAC,sJAAK;4DACL,MAAK;4DACJ,GAAG,KAAK;4DACT,UAAU,CAAC,IACV,MAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;kEAI9C,yXAAC,+JAAe;kEAAC;;;;;;kEAGjB,yXAAC,2JAAW;;;;;;;;;;;;;;;;oCAKd,cAAc,+IAAU,CAAC,OAAO,kBAChC,yXAAC;wCAAI,WAAU;;0DACd,yXAAC,yJAAS;gDACT,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACjB,yXAAC,wJAAQ;;0EACR,yXAAC,yJAAS;0EAAC;;;;;;0EACX,yXAAC,wJAAM;gEACN,eAAe,MAAM,QAAQ;gEAC7B,cAAc,MAAM,KAAK;;kFAEzB,yXAAC,2JAAW;kFACX,cAAA,yXAAC,+JAAa;sFACb,cAAA,yXAAC,6JAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG3B,yXAAC,+JAAa;kFACZ,SAAS,GAAG,CAAC,CAAC,wBACd,yXAAC,4JAAU;gFAEV,OAAO,QAAQ,GAAG;;oFAEjB,QAAQ,KAAK;oFAAC;oFACd,IAAI,KACJ,QAAQ,SAAS,EAChB,kBAAkB;oFAAI;oFAAI;oFAC1B;oFACD,IAAI,KACJ,QAAQ,OAAO,EACd,kBAAkB;oFAAG;;+EAVlB,QAAQ,GAAG;;;;;;;;;;;;;;;;0EAgBpB,yXAAC,+JAAe;0EAAC;;;;;;0EAGjB,yXAAC,2JAAW;;;;;;;;;;;;;;;;0DAKf,yXAAC,yJAAS;gDACT,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACjB,yXAAC,wJAAQ;;0EACR,yXAAC,yJAAS;0EAAC;;;;;;0EACX,yXAAC,wJAAM;gEACN,eAAe,MAAM,QAAQ;gEAC7B,cAAc,MAAM,KAAK;;kFAEzB,yXAAC,2JAAW;kFACX,cAAA,yXAAC,+JAAa;sFACb,cAAA,yXAAC,6JAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG3B,yXAAC,+JAAa;kFACZ,mBAAmB,GAAG,CAAC,CAAC,uBACxB,yXAAC,4JAAU;gFAAkB,OAAO,OAAO,GAAG;;oFAC5C,OAAO,GAAG;oFAAC;oFACX,OAAO,WAAW,CAAC,cAAc;oFAAG;;+EAFrB,OAAO,GAAG;;;;;;;;;;;;;;;;0EAO9B,yXAAC,+JAAe;0EAAC;;;;;;0EAGjB,yXAAC,2JAAW;;;;;;;;;;;;;;;;;;;;;;kDAOjB,yXAAC;wCAAI,WAAU;;0DACd,yXAAC,wJAAM;gDAAC,SAAQ;gDAAU,OAAO;0DAChC,cAAA,yXAAC,kTAAI;oDAAC,MAAK;8DAAqB;;;;;;;;;;;0DAEjC,yXAAC,wJAAM;gDAAC,MAAK;gDAAS,UAAU;0DAC9B,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpC,yXAAC,oJAAI;;kCACJ,yXAAC,0JAAU;kCACV,cAAA,yXAAC,yJAAS;sCAAC;;;;;;;;;;;kCAEZ,yXAAC,2JAAW;kCACX,cAAA,yXAAC;4BAAI,WAAU;;8CACd,yXAAC;;sDACA,yXAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,yXAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAM9C,yXAAC;;sDACA,yXAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,yXAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpD", "debugId": null}}]}