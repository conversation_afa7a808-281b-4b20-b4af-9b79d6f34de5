(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,97667,e=>{"use strict";e.s(["default",()=>eg],97667);var t,s,i,a,r,l,n,o,h,c,d,u,p,f,m,x,_,v,b,g,w,y,j,C,S,N,R,k=e.i(4051),T=e.i(38477),O=e.i(1269),E=e.i(5085),M=e.i(75680),W=e.i(85205),D=e.i(57223),F=e.i(67435),A=e.i(42633),q=e.i(81787);let U=(0,e.i(44571).default)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var I=e.i(61816),L=e.i(40003),P=e.i(76233),V=e.i(25839),Q=e.i(22e3),H=e.i(53779),B=e.i(27399),z=e.i(75335),K=e.i(26797),G=e.i(99905),J=e.i(76252),X=e.i(20800),Y=(t=new WeakMap,s=new WeakMap,i=new WeakMap,a=new WeakMap,r=new WeakMap,l=new WeakMap,n=new WeakMap,o=new WeakMap,h=new WeakMap,c=new WeakMap,d=new WeakMap,u=new WeakMap,p=new WeakMap,f=new WeakMap,m=new WeakMap,x=new WeakSet,_=new WeakSet,v=new WeakSet,b=new WeakSet,g=new WeakSet,w=new WeakSet,y=new WeakSet,j=new WeakSet,C=new WeakSet,class extends K.Subscribable{bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&((0,I._)(this,s).addObserver(this),Z((0,I._)(this,s),this.options)?(0,V._)(this,x,es).call(this):this.updateResult(),(0,V._)(this,g,el).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return $((0,I._)(this,s),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return $((0,I._)(this,s),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,(0,V._)(this,w,en).call(this),(0,V._)(this,y,eo).call(this),(0,I._)(this,s).removeObserver(this)}setOptions(e){let i=this.options,a=(0,I._)(this,s);if(this.options=(0,I._)(this,t).defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,J.resolveEnabled)(this.options.enabled,(0,I._)(this,s)))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");(0,V._)(this,j,eh).call(this),(0,I._)(this,s).setOptions(this.options),i._defaulted&&!(0,J.shallowEqualObjects)(this.options,i)&&(0,I._)(this,t).getQueryCache().notify({type:"observerOptionsUpdated",query:(0,I._)(this,s),observer:this});let r=this.hasListeners();r&&ee((0,I._)(this,s),a,this.options,i)&&(0,V._)(this,x,es).call(this),this.updateResult(),r&&((0,I._)(this,s)!==a||(0,J.resolveEnabled)(this.options.enabled,(0,I._)(this,s))!==(0,J.resolveEnabled)(i.enabled,(0,I._)(this,s))||(0,J.resolveStaleTime)(this.options.staleTime,(0,I._)(this,s))!==(0,J.resolveStaleTime)(i.staleTime,(0,I._)(this,s)))&&(0,V._)(this,_,ei).call(this);let l=(0,V._)(this,v,ea).call(this);r&&((0,I._)(this,s)!==a||(0,J.resolveEnabled)(this.options.enabled,(0,I._)(this,s))!==(0,J.resolveEnabled)(i.enabled,(0,I._)(this,s))||l!==(0,I._)(this,f))&&(0,V._)(this,b,er).call(this,l)}getOptimisticResult(e){var i,n;let o=(0,I._)(this,t).getQueryCache().build((0,I._)(this,t),e),h=this.createResult(o,e);return i=this,n=h,(0,J.shallowEqualObjects)(i.getCurrentResult(),n)||((0,P._)(this,a,h),(0,P._)(this,l,this.options),(0,P._)(this,r,(0,I._)(this,s).state)),h}getCurrentResult(){return(0,I._)(this,a)}trackResult(e,t){return new Proxy(e,{get:(e,s)=>(this.trackProp(s),null==t||t(s),"promise"!==s||this.options.experimental_prefetchInRender||"pending"!==(0,I._)(this,n).status||(0,I._)(this,n).reject(Error("experimental_prefetchInRender feature flag is not enabled")),Reflect.get(e,s))})}trackProp(e){(0,I._)(this,m).add(e)}getCurrentQuery(){return(0,I._)(this,s)}refetch(){let{...e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.fetch({...e})}fetchOptimistic(e){let s=(0,I._)(this,t).defaultQueryOptions(e),i=(0,I._)(this,t).getQueryCache().build((0,I._)(this,t),s);return i.fetch().then(()=>this.createResult(i,s))}fetch(e){var t;return(0,V._)(this,x,es).call(this,{...e,cancelRefetch:null==(t=e.cancelRefetch)||t}).then(()=>(this.updateResult(),(0,I._)(this,a)))}createResult(e,t){let u,p=(0,I._)(this,s),f=this.options,m=(0,I._)(this,a),x=(0,I._)(this,r),_=(0,I._)(this,l),v=e!==p?e.state:(0,I._)(this,i),{state:b}=e,g={...b},w=!1;if(t._optimisticResults){let s=this.hasListeners(),i=!s&&Z(e,t),a=s&&ee(e,p,t,f);(i||a)&&(g={...g,...(0,z.fetchState)(b.data,e.options)}),"isRestoring"===t._optimisticResults&&(g.fetchStatus="idle")}let{error:y,errorUpdatedAt:j,status:C}=g;u=g.data;let S=!1;if(void 0!==t.placeholderData&&void 0===u&&"pending"===C){let e;if((null==m?void 0:m.isPlaceholderData)&&t.placeholderData===(null==_?void 0:_.placeholderData))e=m.data,S=!0;else{var N;e="function"==typeof t.placeholderData?t.placeholderData(null==(N=(0,I._)(this,d))?void 0:N.state.data,(0,I._)(this,d)):t.placeholderData}void 0!==e&&(C="success",u=(0,J.replaceData)(null==m?void 0:m.data,e,t),w=!0)}if(t.select&&void 0!==u&&!S)if(m&&u===(null==x?void 0:x.data)&&t.select===(0,I._)(this,h))u=(0,I._)(this,c);else try{(0,P._)(this,h,t.select),u=t.select(u),u=(0,J.replaceData)(null==m?void 0:m.data,u,t),(0,P._)(this,c,u),(0,P._)(this,o,null)}catch(e){(0,P._)(this,o,e)}(0,I._)(this,o)&&(y=(0,I._)(this,o),u=(0,I._)(this,c),j=Date.now(),C="error");let R="fetching"===g.fetchStatus,k="pending"===C,T="error"===C,O=k&&R,E=void 0!==u,M={status:C,fetchStatus:g.fetchStatus,isPending:k,isSuccess:"success"===C,isError:T,isInitialLoading:O,isLoading:O,data:u,dataUpdatedAt:g.dataUpdatedAt,error:y,errorUpdatedAt:j,failureCount:g.fetchFailureCount,failureReason:g.fetchFailureReason,errorUpdateCount:g.errorUpdateCount,isFetched:g.dataUpdateCount>0||g.errorUpdateCount>0,isFetchedAfterMount:g.dataUpdateCount>v.dataUpdateCount||g.errorUpdateCount>v.errorUpdateCount,isFetching:R,isRefetching:R&&!k,isLoadingError:T&&!E,isPaused:"paused"===g.fetchStatus,isPlaceholderData:w,isRefetchError:T&&E,isStale:et(e,t),refetch:this.refetch,promise:(0,I._)(this,n),isEnabled:!1!==(0,J.resolveEnabled)(t.enabled,e)};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===M.status?e.reject(M.error):void 0!==M.data&&e.resolve(M.data)},s=()=>{t((0,P._)(this,n,M.promise=(0,G.pendingThenable)()))},i=(0,I._)(this,n);switch(i.status){case"pending":e.queryHash===p.queryHash&&t(i);break;case"fulfilled":("error"===M.status||M.data!==i.value)&&s();break;case"rejected":("error"!==M.status||M.error!==i.reason)&&s()}}return M}updateResult(){let e=(0,I._)(this,a),t=this.createResult((0,I._)(this,s),this.options);if((0,P._)(this,r,(0,I._)(this,s).state),(0,P._)(this,l,this.options),void 0!==(0,I._)(this,r).data&&(0,P._)(this,d,(0,I._)(this,s)),(0,J.shallowEqualObjects)(t,e))return;(0,P._)(this,a,t);let i=()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,s="function"==typeof t?t():t;if("all"===s||!s&&!(0,I._)(this,m).size)return!0;let i=new Set(null!=s?s:(0,I._)(this,m));return this.options.throwOnError&&i.add("error"),Object.keys((0,I._)(this,a)).some(t=>(0,I._)(this,a)[t]!==e[t]&&i.has(t))};(0,V._)(this,C,ec).call(this,{listeners:i()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&(0,V._)(this,g,el).call(this)}constructor(e,S){super(),(0,Q._)(this,x),(0,Q._)(this,_),(0,Q._)(this,v),(0,Q._)(this,b),(0,Q._)(this,g),(0,Q._)(this,w),(0,Q._)(this,y),(0,Q._)(this,j),(0,Q._)(this,C),(0,L._)(this,t,{writable:!0,value:void 0}),(0,L._)(this,s,{writable:!0,value:void 0}),(0,L._)(this,i,{writable:!0,value:void 0}),(0,L._)(this,a,{writable:!0,value:void 0}),(0,L._)(this,r,{writable:!0,value:void 0}),(0,L._)(this,l,{writable:!0,value:void 0}),(0,L._)(this,n,{writable:!0,value:void 0}),(0,L._)(this,o,{writable:!0,value:void 0}),(0,L._)(this,h,{writable:!0,value:void 0}),(0,L._)(this,c,{writable:!0,value:void 0}),(0,L._)(this,d,{writable:!0,value:void 0}),(0,L._)(this,u,{writable:!0,value:void 0}),(0,L._)(this,p,{writable:!0,value:void 0}),(0,L._)(this,f,{writable:!0,value:void 0}),(0,L._)(this,m,{writable:!0,value:new Set}),this.options=S,(0,P._)(this,t,e),(0,P._)(this,o,null),(0,P._)(this,n,(0,G.pendingThenable)()),this.bindMethods(),this.setOptions(S)}});function Z(e,t){return!1!==(0,J.resolveEnabled)(t.enabled,e)&&void 0===e.state.data&&("error"!==e.state.status||!1!==t.retryOnMount)||void 0!==e.state.data&&$(e,t,t.refetchOnMount)}function $(e,t,s){if(!1!==(0,J.resolveEnabled)(t.enabled,e)&&"static"!==(0,J.resolveStaleTime)(t.staleTime,e)){let i="function"==typeof s?s(e):s;return"always"===i||!1!==i&&et(e,t)}return!1}function ee(e,t,s,i){return(e!==t||!1===(0,J.resolveEnabled)(i.enabled,e))&&(!s.suspense||"error"!==e.state.status)&&et(e,s)}function et(e,t){return!1!==(0,J.resolveEnabled)(t.enabled,e)&&e.isStaleByTime((0,J.resolveStaleTime)(t.staleTime,e))}function es(e){(0,V._)(this,j,eh).call(this);let t=(0,I._)(this,s).fetch(this.options,e);return(null==e?void 0:e.throwOnError)||(t=t.catch(J.noop)),t}function ei(){(0,V._)(this,w,en).call(this);let e=(0,J.resolveStaleTime)(this.options.staleTime,(0,I._)(this,s));if(J.isServer||(0,I._)(this,a).isStale||!(0,J.isValidTimeout)(e))return;let t=(0,J.timeUntilStale)((0,I._)(this,a).dataUpdatedAt,e);(0,P._)(this,u,X.timeoutManager.setTimeout(()=>{(0,I._)(this,a).isStale||this.updateResult()},t+1))}function ea(){var e;return null!=(e="function"==typeof this.options.refetchInterval?this.options.refetchInterval((0,I._)(this,s)):this.options.refetchInterval)&&e}function er(e){(0,V._)(this,y,eo).call(this),(0,P._)(this,f,e),!J.isServer&&!1!==(0,J.resolveEnabled)(this.options.enabled,(0,I._)(this,s))&&(0,J.isValidTimeout)((0,I._)(this,f))&&0!==(0,I._)(this,f)&&(0,P._)(this,p,X.timeoutManager.setInterval(()=>{(this.options.refetchIntervalInBackground||H.focusManager.isFocused())&&(0,V._)(this,x,es).call(this)},(0,I._)(this,f)))}function el(){(0,V._)(this,_,ei).call(this),(0,V._)(this,b,er).call(this,(0,V._)(this,v,ea).call(this))}function en(){(0,I._)(this,u)&&(X.timeoutManager.clearTimeout((0,I._)(this,u)),(0,P._)(this,u,void 0))}function eo(){(0,I._)(this,p)&&(X.timeoutManager.clearInterval((0,I._)(this,p)),(0,P._)(this,p,void 0))}function eh(){let e=(0,I._)(this,t).getQueryCache().build((0,I._)(this,t),this.options);if(e===(0,I._)(this,s))return;let a=(0,I._)(this,s);(0,P._)(this,s,e),(0,P._)(this,i,e.state),this.hasListeners()&&(null==a||a.removeObserver(this),e.addObserver(this))}function ec(e){B.notifyManager.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e((0,I._)(this,a))}),(0,I._)(this,t).getQueryCache().notify({query:(0,I._)(this,s),type:"observerResultsUpdated"})})}e.i(50460);var ed=e.i(1660),eu=T.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),ep=T.createContext(!1);ep.Provider;var ef=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function em(e,t){return function(e,t,s){var i,a,r,l,n;let o=T.useContext(ep),h=T.useContext(eu),c=(0,ed.useQueryClient)(s),d=c.defaultQueryOptions(e);if(null==(a=c.getDefaultOptions().queries)||null==(i=a._experimental_beforeQuery)||i.call(a,d),d._optimisticResults=o?"isRestoring":"optimistic",d.suspense){let e=e=>"static"===e?e:Math.max(null!=e?e:1e3,1e3),t=d.staleTime;d.staleTime="function"==typeof t?function(){for(var s=arguments.length,i=Array(s),a=0;a<s;a++)i[a]=arguments[a];return e(t(...i))}:e(t),"number"==typeof d.gcTime&&(d.gcTime=Math.max(d.gcTime,1e3))}(d.suspense||d.throwOnError||d.experimental_prefetchInRender)&&!h.isReset()&&(d.retryOnMount=!1),T.useEffect(()=>{h.clearReset()},[h]);let u=!c.getQueryCache().get(d.queryHash),[p]=T.useState(()=>new t(c,d)),f=p.getOptimisticResult(d),m=!o&&!1!==e.subscribed;if(T.useSyncExternalStore(T.useCallback(e=>{let t=m?p.subscribe(B.notifyManager.batchCalls(e)):J.noop;return p.updateResult(),t},[p,m]),()=>p.getCurrentResult(),()=>p.getCurrentResult()),T.useEffect(()=>{p.setOptions(d)},[d,p]),(null==d?void 0:d.suspense)&&f.isPending)throw ef(d,p,h);if((e=>{let{result:t,errorResetBoundary:s,throwOnError:i,query:a,suspense:r}=e;return t.isError&&!s.isReset()&&!t.isFetching&&a&&(r&&void 0===t.data||(0,J.shouldThrowError)(i,[t.error,a]))})({result:f,errorResetBoundary:h,throwOnError:d.throwOnError,query:c.getQueryCache().get(d.queryHash),suspense:d.suspense}))throw f.error;if(null==(l=c.getDefaultOptions().queries)||null==(r=l._experimental_afterQuery)||r.call(l,d,f),d.experimental_prefetchInRender&&!J.isServer&&f.isLoading&&f.isFetching&&!o){let e=u?ef(d,p,h):null==(n=c.getQueryCache().get(d.queryHash))?void 0:n.promise;null==e||e.catch(J.noop).finally(()=>{p.updateResult()})}return d.notifyOnChangeProps?f:p.trackResult(f)}(e,Y,t)}e.i(86784);new WeakMap,S=new WeakMap,N=new WeakMap,R=new WeakMap,new WeakSet,new WeakSet,K.Subscribable;var ex=e.i(4467);let e_={all:["sessions"],lists:()=>[...e_.all,"list"],list:e=>[...e_.lists(),{filters:e}],details:()=>[...e_.all,"detail"],detail:e=>[...e_.details(),e],members:e=>[...e_.detail(e),"members"]},ev={all:["caisses"],lists:()=>[...ev.all,"list"],list:e=>[...ev.lists(),{filters:e}],details:()=>[...ev.all,"detail"],detail:e=>[...ev.details(),e]};var eb=e.i(12058);function eg(){var e;let{data:t}=(0,O.useSession)(),s=(0,T.useMemo)(()=>{var e;let s=null==(e=null==t?void 0:t.user)?void 0:e.role;return{canViewSessions:["secretary_general","controller","cashier"].includes(s),canViewCaisses:["secretary_general","controller","cashier"].includes(s),canCreateSessions:"secretary_general"===s,canCreateCaisses:"secretary_general"===s}},[null==t?void 0:t.user]),i=function(){let{data:e}=(0,O.useSession)(),t=(0,ex.useApi)();return em({queryKey:e_.lists(),queryFn:()=>t.getSessions(),enabled:!!(null==e?void 0:e.accessToken),staleTime:3e5})}(),a=function(){let{data:e}=(0,O.useSession)(),t=(0,ex.useApi)();return em({queryKey:ev.lists(),queryFn:()=>t.getCaisses(),enabled:!!(null==e?void 0:e.accessToken),staleTime:3e5})}(),r=s.canViewSessions&&i.data||[],l=s.canViewCaisses&&a.data||[],n=s.canViewSessions&&i.isLoading||s.canViewCaisses&&a.isLoading,o=new Date,h=r.filter(e=>new Date(e.dateDebut)<=o&&new Date(e.dateFin)>=o),c=l.reduce((e,t)=>e+t.soldeActuel,0),d=l.filter(e=>e.type===eb.CaisseType.PRINCIPALE),u=l.filter(e=>e.type===eb.CaisseType.REUNION);return n?(0,k.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,k.jsxs)("div",{className:"text-center",children:[(0,k.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"}),(0,k.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Chargement du tableau de bord..."})]})}):(0,k.jsxs)("div",{className:"space-y-6",children:[(0,k.jsxs)("div",{children:[(0,k.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["Bienvenue, ",null==(e=null==t?void 0:t.user)?void 0:e.username,"!"]}),(0,k.jsx)("p",{className:"text-gray-600 mt-1",children:"Voici un aperçu de votre tontine aujourd'hui."}),(null==t?void 0:t.user)&&(0,k.jsxs)("div",{className:"mt-2 text-sm text-gray-500",children:["Connecté en tant que"," ",(0,k.jsx)("span",{className:"font-medium",children:t.user.username})," (",t.user.role,")"]})]}),(0,k.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[s.canViewSessions&&(0,k.jsxs)(M.Card,{children:[(0,k.jsxs)(M.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,k.jsx)(M.CardTitle,{className:"text-sm font-medium",children:"Sessions Totales"}),(0,k.jsx)(F.Calendar,{className:"h-4 w-4 text-muted-foreground"})]}),(0,k.jsxs)(M.CardContent,{children:[(0,k.jsx)("div",{className:"text-2xl font-bold",children:r.length}),(0,k.jsxs)("p",{className:"text-xs text-muted-foreground",children:[h.length," actives"]})]})]}),s.canViewSessions&&(0,k.jsxs)(M.Card,{children:[(0,k.jsxs)(M.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,k.jsx)(M.CardTitle,{className:"text-sm font-medium",children:"Sessions Actives"}),(0,k.jsx)(F.Calendar,{className:"h-4 w-4 text-green-600"})]}),(0,k.jsxs)(M.CardContent,{children:[(0,k.jsx)("div",{className:"text-2xl font-bold text-green-600",children:h.length}),(0,k.jsx)("p",{className:"text-xs text-muted-foreground",children:"En cours actuellement"})]})]}),s.canViewCaisses&&(0,k.jsxs)(M.Card,{children:[(0,k.jsxs)(M.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,k.jsx)(M.CardTitle,{className:"text-sm font-medium",children:"Total Caisses"}),(0,k.jsx)(A.Wallet,{className:"h-4 w-4 text-muted-foreground"})]}),(0,k.jsxs)(M.CardContent,{children:[(0,k.jsx)("div",{className:"text-2xl font-bold",children:l.length}),(0,k.jsxs)("p",{className:"text-xs text-muted-foreground",children:[d.length," principales,"," ",u.length," réunions"]})]})]}),s.canViewCaisses&&(0,k.jsxs)(M.Card,{children:[(0,k.jsxs)(M.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,k.jsx)(M.CardTitle,{className:"text-sm font-medium",children:"Solde Total"}),(0,k.jsx)(D.DollarSign,{className:"h-4 w-4 text-green-600"})]}),(0,k.jsxs)(M.CardContent,{children:[(0,k.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[c.toLocaleString()," FCFA"]}),(0,k.jsx)("p",{className:"text-xs text-muted-foreground",children:"Toutes caisses confondues"})]})]})]}),(0,k.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[s.canViewSessions&&(0,k.jsxs)(M.Card,{children:[(0,k.jsxs)(M.CardHeader,{className:"flex flex-row items-center justify-between",children:[(0,k.jsxs)("div",{children:[(0,k.jsx)(M.CardTitle,{children:"Sessions Récentes"}),(0,k.jsx)(M.CardDescription,{children:"Dernières sessions créées"})]}),s.canCreateSessions&&(0,k.jsx)(W.Button,{asChild:!0,size:"sm",children:(0,k.jsxs)(E.default,{href:"/dashboard/sessions/new",children:[(0,k.jsx)(q.Plus,{className:"mr-2 h-4 w-4"}),"Nouvelle"]})})]}),(0,k.jsx)(M.CardContent,{children:(0,k.jsxs)("div",{className:"space-y-4",children:[r.slice(0,4).map(e=>(0,k.jsxs)("div",{className:"flex items-center justify-between",children:[(0,k.jsxs)("div",{children:[(0,k.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["Session ",e.annee]}),(0,k.jsxs)("p",{className:"text-xs text-gray-500",children:[new Date(e.dateDebut).toLocaleDateString()," -"," ",new Date(e.dateFin).toLocaleDateString()]})]}),(0,k.jsxs)("div",{className:"text-sm font-medium text-blue-600",children:[e.partFixe.toLocaleString()," FCFA"]})]},e._id)),0===r.length&&(0,k.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"Aucune session trouvée"}),r.length>0&&(0,k.jsx)("div",{className:"pt-2",children:(0,k.jsx)(W.Button,{variant:"outline",size:"sm",asChild:!0,className:"w-full",children:(0,k.jsxs)(E.default,{href:"/dashboard/sessions",children:["Voir toutes les sessions",(0,k.jsx)(U,{className:"ml-2 h-4 w-4"})]})})})]})})]}),s.canViewCaisses&&(0,k.jsxs)(M.Card,{children:[(0,k.jsxs)(M.CardHeader,{className:"flex flex-row items-center justify-between",children:[(0,k.jsxs)("div",{children:[(0,k.jsx)(M.CardTitle,{children:"Caisses Récentes"}),(0,k.jsx)(M.CardDescription,{children:"Dernières caisses créées"})]}),s.canCreateCaisses&&(0,k.jsx)(W.Button,{asChild:!0,size:"sm",children:(0,k.jsxs)(E.default,{href:"/dashboard/caisses/new",children:[(0,k.jsx)(q.Plus,{className:"mr-2 h-4 w-4"}),"Nouvelle"]})})]}),(0,k.jsx)(M.CardContent,{children:(0,k.jsxs)("div",{className:"space-y-4",children:[l.slice(0,4).map(e=>(0,k.jsxs)("div",{className:"flex items-center justify-between",children:[(0,k.jsxs)("div",{children:[(0,k.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.nom}),(0,k.jsx)("p",{className:"text-xs text-gray-500",children:e.type===eb.CaisseType.PRINCIPALE?"Principale":"Réunion"})]}),(0,k.jsxs)("div",{className:"text-sm font-medium ".concat(e.soldeActuel>0?"text-green-600":"text-gray-500"),children:[e.soldeActuel.toLocaleString()," FCFA"]})]},e._id)),0===l.length&&(0,k.jsx)("p",{className:"text-sm text-muted-foreground text-center py-4",children:"Aucune caisse trouvée"}),l.length>0&&(0,k.jsx)("div",{className:"pt-2",children:(0,k.jsx)(W.Button,{variant:"outline",size:"sm",asChild:!0,className:"w-full",children:(0,k.jsxs)(E.default,{href:"/dashboard/caisses",children:["Voir toutes les caisses",(0,k.jsx)(U,{className:"ml-2 h-4 w-4"})]})})})]})})]})]})]})}}]);