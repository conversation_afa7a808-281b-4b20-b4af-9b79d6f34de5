"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, ArrowRight } from "lucide-react";

interface DashboardActionsProps {
  userRole: string;
}

export function DashboardActions({ userRole }: DashboardActionsProps) {
  // Permission checks
  const canCreateSessions = userRole === "secretary_general";
  const canCreateCaisses = userRole === "secretary_general";
  const canManageMembers = ["secretary_general", "controller"].includes(userRole);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {canCreateSessions && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Nouvelle Session</CardTitle>
            <CardDescription>
              Créer une nouvelle session de tontine
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Link href="/dashboard/sessions/new">
              <Button className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Créer une session
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}

      {canCreateCaisses && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Nouvelle Caisse</CardTitle>
            <CardDescription>
              Créer une nouvelle caisse pour la tontine
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Link href="/dashboard/caisses/new">
              <Button className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Créer une caisse
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}

      {canManageMembers && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Gestion des Membres</CardTitle>
            <CardDescription>
              Ajouter et gérer les membres de la tontine
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Link href="/dashboard/members/new">
                <Button className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Nouveau membre
                </Button>
              </Link>
              <Link href="/dashboard/members">
                <Button variant="outline" className="w-full">
                  <ArrowRight className="h-4 w-4 mr-2" />
                  Voir tous les membres
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
