{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["/* istanbul ignore file */\n\nexport { focusManager } from './focusManager'\nexport {\n  defaultShouldDehydrateMutation,\n  defaultShouldDehydrateQuery,\n  dehydrate,\n  hydrate,\n} from './hydration'\nexport { InfiniteQueryObserver } from './infiniteQueryObserver'\nexport { MutationCache } from './mutationCache'\nexport type { MutationCacheNotifyEvent } from './mutationCache'\nexport { MutationObserver } from './mutationObserver'\nexport { defaultScheduler, notifyManager } from './notifyManager'\nexport { onlineManager } from './onlineManager'\nexport { QueriesObserver } from './queriesObserver'\nexport { QueryCache } from './queryCache'\nexport type { QueryCacheNotifyEvent } from './queryCache'\nexport { QueryClient } from './queryClient'\nexport { QueryObserver } from './queryObserver'\nexport { CancelledError, isCancelledError } from './retryer'\nexport {\n  timeoutManager,\n  type ManagedTimerId,\n  type TimeoutCallback,\n  type TimeoutProvider,\n} from './timeoutManager'\nexport {\n  hashKey,\n  isServer,\n  keepPreviousData,\n  matchMutation,\n  matchQuery,\n  noop,\n  partialMatchKey,\n  replaceEqualDeep,\n  shouldThrowError,\n  skipToken,\n} from './utils'\nexport type { MutationFilters, QueryFilters, SkipToken, Updater } from './utils'\n\nexport { streamedQuery as experimental_streamedQuery } from './streamedQuery'\n\n// Types\nexport type {\n  DehydratedState,\n  DehydrateOptions,\n  HydrateOptions,\n} from './hydration'\nexport { Mutation } from './mutation'\nexport type { MutationState } from './mutation'\nexport type { QueriesObserverOptions } from './queriesObserver'\nexport { Query } from './query'\nexport type { QueryState } from './query'\nexport * from './types'\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,0BAA6B;AAC7B,uBAKO;AACP,mCAAsC;AACtC,2BAA8B;AAE9B,8BAAiC;AACjC,2BAAgD;AAChD,2BAA8B;AAC9B,6BAAgC;AAChC,wBAA2B;AAE3B,yBAA4B;AAC5B,2BAA8B;AAC9B,qBAAiD;AACjD,4BAKO;AACP,mBAWO;AAGP,2BAA4D;AAQ5D,sBAAyB;AAGzB,mBAAsB;AAEtB,0BAAc,wBAtDd;", "names": []}