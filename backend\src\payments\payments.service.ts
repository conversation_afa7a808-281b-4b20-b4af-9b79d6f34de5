import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Payment, PaymentDirection, PaymentDocument, PaymentFunction } from './schemas/payment.schema';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { Caisse, CaisseDocument, CaisseStatus, CaisseType } from '../caisses/schemas/caisse.schema';
import { ExitOrder, ExitOrderDocument } from '../caisses/schemas/exit-order.schema';
import { SessionMember, SessionMemberDocument } from '../sessions/schemas/session-member.schema';

@Injectable()
export class PaymentsService {
  constructor(
    @InjectModel(Payment.name) private paymentModel: Model<PaymentDocument>,
    @InjectModel(Caisse.name) private caisseModel: Model<CaisseDocument>,
    @InjectModel(ExitOrder.name) private exitOrderModel: Model<ExitOrderDocument>,
    @InjectModel(SessionMember.name) private sessionMemberModel: Model<SessionMemberDocument>,
  ) {}

  // crée un paiement, met à jour les soldes, applique la cotisation sur le membre
  async create(dto: CreatePaymentDto, userId: string): Promise<Payment> {
    const session = await this.paymentModel.db.startSession();
    let created: Payment | null = null;
    await session.withTransaction(async () => {
      const caisse = await this.caisseModel.findById(dto.caisseId).session(session);
      if (!caisse) throw new NotFoundException('Caisse introuvable');

      // règle: Cashier ne peut payer que sur sa caisse ouverte si REUNION (sera renforcée côté contrôleur/guard si besoin)
      if (caisse.type === CaisseType.REUNION && caisse.status !== CaisseStatus.OPEN && dto.func !== PaymentFunction.TRANSFER) {
        throw new ForbiddenException('La caisse de réunion doit être ouverte pour enregistrer un paiement');
      }

      // règle: si caisse REUNION et paiement OUT -> exitOrderId requis
      if (caisse.type === CaisseType.REUNION && dto.direction === PaymentDirection.OUT) {
        if (!dto.exitOrderId) throw new BadRequestException('exitOrderId requis pour sortie de caisse REUNION');
        const eo = await this.exitOrderModel.findById(dto.exitOrderId).session(session);
        if (!eo || !eo.open || eo.caisseId.toString() !== caisse._id.toString()) {
          throw new BadRequestException('ExitOrder invalide');
        }
        if (eo.amount < dto.amount) throw new BadRequestException('Montant > ExitOrder');
      }

      // règle: si paiement est contribution -> direction doit être IN
      if (dto.func === PaymentFunction.CONTRIBUTION && dto.direction !== PaymentDirection.IN) {
        throw new BadRequestException('Contribution doit être en entrée (IN)');
      }

      const sessionId = dto.sessionId ? new Types.ObjectId(dto.sessionId) : undefined;
      const reunionId = dto.reunionId ? new Types.ObjectId(dto.reunionId) : undefined;

      // mouvements de solde
      if (dto.direction === PaymentDirection.IN) {
        caisse.soldeActuel = (caisse.soldeActuel || 0) + dto.amount;
      } else {
        if ((caisse.soldeActuel || 0) < dto.amount) throw new BadRequestException('Solde insuffisant');
        caisse.soldeActuel = (caisse.soldeActuel || 0) - dto.amount;
      }

      // transfert (double mouvement)
      if (dto.func === PaymentFunction.TRANSFER) {
        if (!dto.receivingCaisseId) throw new BadRequestException('receivingCaisseId requis pour un transfert');
        const receiving = await this.caisseModel.findById(dto.receivingCaisseId).session(session);
        if (!receiving) throw new NotFoundException('Caisse receptrice introuvable');
        receiving.soldeActuel = (receiving.soldeActuel || 0) + dto.amount;
        await receiving.save({ session });
      }

      // contribution: appliquer sur le membre (paidSoFar et recalcul overdueAmount)
      if (dto.func === PaymentFunction.CONTRIBUTION) {
        if (!dto.memberId || !sessionId) throw new BadRequestException('memberId et sessionId requis pour cotisation');
        const sm = await this.sessionMemberModel.findOne({ memberId: new Types.ObjectId(dto.memberId), sessionId }).session(session);
        if (!sm) throw new NotFoundException('Membre non inscrit à la session');
        sm.paidSoFar = (sm.paidSoFar || 0) + dto.amount;
        // overdue = max(0, expectedToDate - paidSoFar)
        sm.overdueAmount = Math.max(0, (sm.expectedToDate || 0) - (sm.paidSoFar || 0));
        await sm.save({ session });
      }

      await caisse.save({ session });

      const docs = await this.paymentModel.create([
        {
          direction: dto.direction,
          func: dto.func,
          amount: dto.amount,
          date: new Date(),
          sessionId,
          reunionId,
          caisseId: new Types.ObjectId(dto.caisseId),
          receivingCaisseId: dto.receivingCaisseId ? new Types.ObjectId(dto.receivingCaisseId) : undefined,
          memberId: dto.memberId ? new Types.ObjectId(dto.memberId) : undefined,
          reason: dto.reason,
          createdBy: new Types.ObjectId(userId),
          exitOrderId: dto.exitOrderId ? new Types.ObjectId(dto.exitOrderId) : undefined,
        },
      ], { session });

      created = (docs[0] as any).toObject();
    });

    return created as any;
  }
}