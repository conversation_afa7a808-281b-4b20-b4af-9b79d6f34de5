import { <PERSON>du<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";

import { AuthModule } from "./auth/auth.module";
import { UsersModule } from "./users/users.module";
import { SessionsModule } from "./sessions/sessions.module";
import { ReunionsModule } from "./reunions/reunions.module";
import { CaissesModule } from "./caisses/caisses.module";
import { MembersModule } from "./members/members.module";
import { PaymentsModule } from "./payments/payments.module";
import { MongooseModule as M } from "@nestjs/mongoose";
import { History, HistorySchema } from "./history/schemas/history.schema";

@Module({
	imports: [
		MongooseModule.forRoot(
			"mongodb+srv://cabrelelvis187_db_user:<EMAIL>/tontineMvpDB?retryWrites=true&w=majority&appName=testing",
		),
		<PERSON><PERSON>forFeature([{ name: History.name, schema: HistorySchema }]),
		UsersModule,
		AuthModule,
		SessionsModule,
		ReunionsModule,
		CaissesModule,
		MembersModule,
		PaymentsModule,
	],
})
export class AppModule {}


			