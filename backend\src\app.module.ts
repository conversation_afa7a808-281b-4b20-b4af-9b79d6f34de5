import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { SessionsModule } from './sessions/sessions.module';
import { ReunionsModule } from './reunions/reunions.module';
import { CaissesModule } from './caisses/caisses.module';
import { MembersModule } from './members/members.module';
import { PaymentsModule } from './payments/payments.module';
import { MongooseModule as M } from '@nestjs/mongoose';
import { History, HistorySchema } from './history/schemas/history.schema';

@Module({
  imports: [
    MongooseModule.forRoot('mongodb://localhost:27017/tontine-mvp'),
    <PERSON>.forFeature([{ name: History.name, schema: HistorySchema }]),
    UsersModule,
    AuthModule,
    SessionsModule,
    ReunionsModule,
    CaissesModule,
    MembersModule,
    PaymentsModule,
  ],
})
export class AppModule {}