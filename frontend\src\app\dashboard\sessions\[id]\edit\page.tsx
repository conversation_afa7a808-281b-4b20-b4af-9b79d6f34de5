"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ArrowLeft, Calendar, DollarSign, Trash2 } from "lucide-react";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { useApi } from "@/hooks/use-api";
import { Session, SessionForm } from "@/types";

const sessionSchema = z
	.object({
		annee: z
			.number()
			.min(2020, "L'année doit être supérieure à 2020")
			.max(2050, "L'année doit être inférieure à 2050"),
		dateDebut: z.string().min(1, "La date de début est requise"),
		dateFin: z.string().min(1, "La date de fin est requise"),
		partFixe: z
			.number()
			.min(1, "La part fixe doit être supérieure à 0")
			.max(1000000, "La part fixe ne peut pas dépasser 1,000,000 FCFA"),
	})
	.refine(
		(data) => {
			const debut = new Date(data.dateDebut);
			const fin = new Date(data.dateFin);
			return fin > debut;
		},
		{
			message: "La date de fin doit être postérieure à la date de début",
			path: ["dateFin"],
		},
	);

export default function EditSessionPage() {
	const { id: sessionId } = useParams();
	const { data: session, status } = useSession();
	const router = useRouter();
	const api = useApi();

	const [sessionData, setSessionData] = useState<Session | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [loading, setLoading] = useState(true);

	// Vérifier les permissions
	const canEditSessions =
		session?.user && session.user.role === "secretary_general";

	const form = useForm<SessionForm>({
		resolver: zodResolver(sessionSchema),
		defaultValues: {
			annee: new Date().getFullYear(),
			dateDebut: "",
			dateFin: "",
			partFixe: 0,
		},
	});

	// Charger les données de la session
	useEffect(() => {
		const loadSession = async () => {
			if (!sessionId || typeof sessionId !== "string") return;

			try {
				setLoading(true);
				const data = await api.getSession(sessionId);
				setSessionData(data);

				// Mettre à jour le formulaire
				form.reset({
					annee: data.annee,
					dateDebut: data.dateDebut.split("T")[0], // Format YYYY-MM-DD
					dateFin: data.dateFin.split("T")[0],
					partFixe: data.partFixe,
				});
			} catch (error) {
				console.error("Erreur lors du chargement:", error);
				setError("Session introuvable");
			} finally {
				setLoading(false);
			}
		};

		if (session?.accessToken) {
			loadSession();
		}
	}, [sessionId, status]);

	const onSubmit = async (data: SessionForm) => {
		if (!canEditSessions || !sessionId || typeof sessionId !== "string") {
			setError("Vous n'avez pas les permissions pour modifier cette session");
			return;
		}

		try {
			setIsLoading(true);
			setError(null);

			await api.updateSession(sessionId, data);
			router.push("/dashboard/sessions");
		} catch (error: any) {
			console.error("Erreur lors de la modification:", error);
			setError(
				error.message || "Une erreur est survenue lors de la modification",
			);
		} finally {
			setIsLoading(false);
		}
	};

	const handleDelete = async () => {
		if (
			!canEditSessions ||
			!sessionId ||
			typeof sessionId !== "string" ||
			!sessionData
		) {
			return;
		}

		if (
			!confirm(
				`Êtes-vous sûr de vouloir supprimer la session ${sessionData.annee} ? Cette action supprimera également toutes les réunions associées.`,
			)
		) {
			return;
		}

		try {
			setIsDeleting(true);
			await api.deleteSession(sessionId);
			router.push("/dashboard/sessions");
		} catch (error: any) {
			console.error("Erreur lors de la suppression:", error);
			setError(
				error.message || "Une erreur est survenue lors de la suppression",
			);
		} finally {
			setIsDeleting(false);
		}
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
					<p className="mt-2 text-sm text-gray-600">
						Chargement de la session...
					</p>
				</div>
			</div>
		);
	}

	if (!canEditSessions) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Button variant="outline" size="icon" asChild>
						<Link href="/dashboard/sessions">
							<ArrowLeft className="h-4 w-4" />
						</Link>
					</Button>
					<div>
						<h1 className="text-3xl font-bold tracking-tight">
							Modifier Session
						</h1>
						<p className="text-muted-foreground">
							Modifier les paramètres de la session
						</p>
					</div>
				</div>

				<Card>
					<CardContent className="pt-6">
						<div className="text-center py-8">
							<p className="text-muted-foreground">
								Vous n'avez pas les permissions pour modifier cette session.
							</p>
							<p className="text-sm text-muted-foreground mt-2">
								Seuls les administrateurs peuvent modifier les sessions.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	if (!sessionData) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Button variant="outline" size="icon" asChild>
						<Link href="/dashboard/sessions">
							<ArrowLeft className="h-4 w-4" />
						</Link>
					</Button>
					<div>
						<h1 className="text-3xl font-bold tracking-tight">
							Session introuvable
						</h1>
					</div>
				</div>

				<Card>
					<CardContent className="pt-6">
						<div className="text-center py-8">
							<p className="text-muted-foreground">
								La session demandée n'a pas été trouvée.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-4">
					<Button variant="outline" size="icon" asChild>
						<Link href="/dashboard/sessions">
							<ArrowLeft className="h-4 w-4" />
						</Link>
					</Button>
					<div>
						<h1 className="text-3xl font-bold tracking-tight">
							Modifier Session {sessionData.annee}
						</h1>
						<p className="text-muted-foreground">
							Modifier les paramètres de la session
						</p>
					</div>
				</div>

				<Button
					variant="destructive"
					onClick={handleDelete}
					disabled={isDeleting}
				>
					<Trash2 className="mr-2 h-4 w-4" />
					{isDeleting ? "Suppression..." : "Supprimer"}
				</Button>
			</div>

			{/* Formulaire */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Calendar className="h-5 w-5" />
						Informations de la session
					</CardTitle>
					<CardDescription>
						Modifiez les paramètres de la session. Attention : les modifications
						peuvent affecter les réunions existantes.
					</CardDescription>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							{error && (
								<div className="bg-red-50 border border-red-200 rounded-md p-4">
									<p className="text-sm text-red-600">{error}</p>
								</div>
							)}

							<div className="grid gap-6 md:grid-cols-2">
								<FormField
									control={form.control}
									name="annee"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Année</FormLabel>
											<FormControl>
												<Input
													type="number"
													{...field}
													onChange={(e) =>
														field.onChange(parseInt(e.target.value) || 0)
													}
												/>
											</FormControl>
											<FormDescription>
												L'année de la session de tontine
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="partFixe"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="flex items-center gap-2">
												<DollarSign className="h-4 w-4" />
												Part Fixe (FCFA)
											</FormLabel>
											<FormControl>
												<Input
													type="number"
													{...field}
													onChange={(e) =>
														field.onChange(parseInt(e.target.value) || 0)
													}
												/>
											</FormControl>
											<FormDescription>
												Montant de la cotisation fixe par réunion
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<div className="grid gap-6 md:grid-cols-2">
								<FormField
									control={form.control}
									name="dateDebut"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Date de début</FormLabel>
											<FormControl>
												<Input type="date" {...field} />
											</FormControl>
											<FormDescription>
												Date de début de la session
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="dateFin"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Date de fin</FormLabel>
											<FormControl>
												<Input type="date" {...field} />
											</FormControl>
											<FormDescription>
												Date de fin de la session
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<div className="flex justify-end gap-4">
								<Button variant="outline" asChild>
									<Link href="/dashboard/sessions">Annuler</Link>
								</Button>
								<Button type="submit" disabled={isLoading}>
									{isLoading ? "Modification..." : "Modifier la session"}
								</Button>
							</div>
						</form>
					</Form>
				</CardContent>
			</Card>
		</div>
	);
}
