import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CaissesController } from './caisses.controller';
import { CaissesService } from './caisses.service';
import { <PERSON><PERSON><PERSON>, CaisseSchema } from './schemas/caisse.schema';
import { ExitOrder, ExitOrderSchema } from './schemas/exit-order.schema';
import { PaymentsModule } from '../payments/payments.module';
import { SessionsModule } from '../sessions/sessions.module';
import { SessionMember, SessionMemberSchema } from '../sessions/schemas/session-member.schema';
import { Reunion, ReunionSchema } from '../reunions/schemas/reunion.schema';
import { Session, SessionSchema } from '../sessions/schemas/session.schema';
import { Payment, PaymentSchema } from '../payments/schemas/payment.schema';

@Module({
  imports: [
    PaymentsModule,
    SessionsModule,
    MongooseModule.forFeature([
      { name: Caisse.name, schema: CaisseSchema },
      { name: ExitOrder.name, schema: ExitOrderSchema },
      { name: SessionMember.name, schema: SessionMemberSchema },
      { name: Reunion.name, schema: ReunionSchema },
      { name: Session.name, schema: SessionSchema },
      { name: Payment.name, schema: PaymentSchema },
    ]),
  ],
  controllers: [CaissesController],
  providers: [CaissesService],
  exports: [CaissesService],
})
export class CaissesModule {}