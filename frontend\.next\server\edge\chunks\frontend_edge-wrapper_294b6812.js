(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["chunks/frontend_edge-wrapper_294b6812.js",71055,(e,t,n)=>{self._ENTRIES||={};let h=Promise.resolve().then(()=>e.i(36087));h.catch(()=>{}),self._ENTRIES.middleware_middleware=new Proxy(h,{get(e,t){if("then"===t)return(t,n)=>e.then(t,n);let n=(...n)=>e.then(e=>(0,e[t])(...n));return n.then=(n,h)=>e.then(e=>e[t]).then(n,h),n}})}]);

//# sourceMappingURL=frontend_edge-wrapper_294b6812.js.map