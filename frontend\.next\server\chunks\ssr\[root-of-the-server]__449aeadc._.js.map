{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card'\n      className={cn(\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-header'\n      className={cn(\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-title'\n      className={cn('leading-none font-semibold', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-description'\n      className={cn('text-muted-foreground text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-action'\n      className={cn(\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-content'\n      className={cn('px-6', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-footer'\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,yXAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,yXAAC;YACC,aAAU;YACV,WAAW,IAAA,qIAAE,EAAC,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,IAAA,8PAAG,EACvB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,yTAAI,GAAG;IAE9B,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/auth.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from \"next-auth/providers/credentials\";\nimport { z } from \"zod\";\nimport { apiService } from \"./api\";\nimport { decodeJwt } from \"./utils\";\n\nconst loginSchema = z.object({\n\tusername: z.string().min(1, \"Username is required\"),\n\tpassword: z.string().min(1, \"Password is required\"),\n});\n\nexport const { handlers, signIn, signOut, auth } = NextAuth({\n\tproviders: [\n\t\tCredentialsProvider({\n\t\t\tname: \"credentials\",\n\t\t\tcredentials: {\n\t\t\t\tusername: { label: \"Username\", type: \"text\" },\n\t\t\t\tpassword: { label: \"Password\", type: \"password\" },\n\t\t\t},\n\t\t\tasync authorize(credentials) {\n\t\t\t\ttry {\n\t\t\t\t\tconst { username, password } = loginSchema.parse(credentials);\n\n\t\t\t\t\t// Authentification avec l'API backend\n\t\t\t\t\tconst response = await apiService.login({ username, password });\n\t\t\t\t\tconst jwt = decodeJwt(response.access_token);\n\t\t\t\t\tif(!jwt) return null;\n\t\t\t\t\tconst jwtPayload = jwt.payload as unknown as { sub: string; username: string; role:  'secretary_general' | 'controller' | 'cashier' };\n\t\t\t\t\t\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tid: jwtPayload.sub,\n\t\t\t\t\t\t\tusername: jwtPayload.username,\n\t\t\t\t\t\t\trole: jwtPayload.role,\n\t\t\t\t\t\t\taccessToken: response.access_token,\n\t\t\t\t\t}\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"Erreur d'authentification:\", error);\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\t\t}),\n\t],\n\tpages: {\n\t\tsignIn: \"/auth/signin\",\n\t},\n\tcallbacks: {\n\t\tauthorized: ({ auth }) => !!auth,\n\t\tasync jwt({ token, user }) {\n\t\t\tif (user) {\n\t\t\t\ttoken.username = user.username;\n\t\t\t\ttoken.role = user.role;\n\t\t\t\ttoken.accessToken = user.accessToken;\n\t\t\t}\n\t\t\treturn token;\n\t\t},\n\t\tasync session({ session, token }) {\n\t\t\tif (token) {\n\t\t\t\tsession.user.id = token.sub || \"\";\n\t\t\t\tsession.user.username = token.username as string;\n\t\t\t\tsession.user.role = token.role as  'secretary_general' | 'controller' | 'cashier';\n\t\t\t\tsession.accessToken = token.accessToken as string;\n\t\t\t}\n\t\t\treturn session;\n\t\t},\n\t},\n\tsession: {\n\t\tstrategy: \"jwt\",\n\t},\n});\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,cAAc,8OAAC,CAAC,MAAM,CAAC;IAC5B,UAAU,8OAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,UAAU,8OAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC7B;AAEO,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAA,wRAAQ,EAAC;IAC3D,WAAW;QACV,IAAA,6OAAmB,EAAC;YACnB,MAAM;YACN,aAAa;gBACZ,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAO;gBAC5C,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YACjD;YACA,MAAM,WAAU,WAAW;gBAC1B,IAAI;oBACH,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,YAAY,KAAK,CAAC;oBAEjD,sCAAsC;oBACtC,MAAM,WAAW,MAAM,2IAAU,CAAC,KAAK,CAAC;wBAAE;wBAAU;oBAAS;oBAC7D,MAAM,MAAM,IAAA,4IAAS,EAAC,SAAS,YAAY;oBAC3C,IAAG,CAAC,KAAK,OAAO;oBAChB,MAAM,aAAa,IAAI,OAAO;oBAE7B,OAAO;wBACN,IAAI,WAAW,GAAG;wBAClB,UAAU,WAAW,QAAQ;wBAC7B,MAAM,WAAW,IAAI;wBACrB,aAAa,SAAS,YAAY;oBACpC;gBAED,EAAE,OAAO,OAAO;oBACf,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,OAAO;gBACR;YACD;QACD;KACA;IACD,OAAO;QACN,QAAQ;IACT;IACA,WAAW;QACV,YAAY,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC,CAAC;QAC5B,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACxB,IAAI,MAAM;gBACT,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,WAAW,GAAG,KAAK,WAAW;YACrC;YACA,OAAO;QACR;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC/B,IAAI,OAAO;gBACV,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI;gBAC/B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,WAAW,GAAG,MAAM,WAAW;YACxC;YACA,OAAO;QACR;IACD;IACA,SAAS;QACR,UAAU;IACX;AACD", "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/api.ts"], "sourcesContent": ["import { signOut } from \"next-auth/react\";\nimport { signOut as ServerSignOut } from \"./auth\";\n\n// Configuration de l'API\nexport const API_BASE_URL =\n\tprocess.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000\";\n\n// Types pour l'authentification\nexport interface LoginRequest {\n\tusername: string;\n\tpassword: string;\n}\n\nexport interface LoginResponse {\n\taccess_token: string;\n\tuser: {\n\t\tid: string;\n\t\tusername: string;\n\t\tnom: string;\n\t\tprenom: string;\n\t\temail: string;\n\t\trole: \"admin\" | \"tresorier\" | \"membre\";\n\t\tstatut: \"actif\" | \"en_attente\" | \"suspendu\";\n\t};\n}\n\nexport interface RegisterRequest {\n\tusername: string;\n\tpassword: string;\n\trole: string;\n\t// statut?: \"actif\" | \"en_attente\" | \"suspendu\";\n}\n\nexport interface ApiError {\n\tmessage: string;\n\tstatusCode: number;\n\terror?: string;\n}\n\n// Service API\nexport class ApiService {\n\tprivate baseUrl: string;\n\n\tconstructor(baseUrl: string = API_BASE_URL) {\n\t\tthis.baseUrl = baseUrl;\n\t}\n\n\tprivate async request<T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\tconst url = `${this.baseUrl}${endpoint}`;\n\n\t\tconst config: RequestInit = {\n\t\t\theaders: {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t...options.headers,\n\t\t\t},\n\t\t\t...options,\n\t\t};\n\n\t\ttry {\n\t\t\tconst response = await fetch(url, config);\n\n\t\t\tif (!response.ok) {\n\t\t\t\tconst errorData: ApiError = await response.json().catch(() => ({\n\t\t\t\t\tmessage: \"Une erreur est survenue\",\n\t\t\t\t\tstatusCode: response.status,\n\t\t\t\t}));\n\n\t\t\t\t// 🚨 Gestion du cas 401\n\t\t\t\tif (response.status === 401) {\n\t\t\t\t\t// Si côté client → on déconnecte\n\t\t\t\t\tif (typeof window !== \"undefined\") {\n\t\t\t\t\t\tawait signOut({ callbackUrl: \"/auth/signin\" });\n\t\t\t\t\t} else {\n\t\t\t\t\t\tawait ServerSignOut({ redirectTo: \"/auth/signin\" });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthrow new Error(errorData.message || `HTTP ${response.status}`);\n\t\t\t}\n\n\t\t\treturn await response.json();\n\t\t} catch (error) {\n\t\t\tconsole.log(error);\n\n\t\t\tif (error instanceof Error) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t\tthrow new Error(\"Erreur de connexion au serveur\");\n\t\t}\n\t}\n\n\t// Authentification\n\tasync login(credentials: LoginRequest): Promise<LoginResponse> {\n\t\treturn this.request<LoginResponse>(\"/auth/login\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(credentials),\n\t\t});\n\t}\n\n\tasync register(\n\t\tuserData: RegisterRequest,\n\t): Promise<{ message: string; user: any }> {\n\t\treturn this.request<{ message: string; user: any }>(\"/auth/register\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(userData),\n\t\t});\n\t}\n\n\t// Méthodes avec authentification\n\tasync authenticatedRequest<T>(\n\t\tendpoint: string,\n\t\ttoken: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\treturn this.request<T>(endpoint, {\n\t\t\t...options,\n\t\t\theaders: {\n\t\t\t\t...options.headers,\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\tAuthorization: `Bearer ${token}`,\n\t\t\t},\n\t\t});\n\t}\n\n\t// Utilisateurs\n\tasync getUsers(token: string): Promise<any[]> {\n\t\treturn this.authenticatedRequest<any[]>(\"/users\", token);\n\t}\n\n\tasync getUser(id: string, token: string): Promise<any> {\n\t\treturn this.authenticatedRequest<any>(`/users/${id}`, token);\n\t}\n}\n\n// Instance par défaut\nexport const apiService = new ApiService();\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAGO,MAAM,eACZ,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAmC7B,MAAM;IACJ,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC3C,IAAI,CAAC,OAAO,GAAG;IAChB;IAEA,MAAc,QACb,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACZ;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC3B,SAAS;gBACR,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACnB;YACA,GAAG,OAAO;QACX;QAEA,IAAI;YACH,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACjB,MAAM,YAAsB,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;wBAC9D,SAAS;wBACT,YAAY,SAAS,MAAM;oBAC5B,CAAC;gBAED,wBAAwB;gBACxB,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC5B,iCAAiC;oBACjC;;yBAEO;wBACN,MAAM,IAAA,yIAAa,EAAC;4BAAE,YAAY;wBAAe;oBAClD;gBACD;gBACA,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;YAC/D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC3B,EAAE,OAAO,OAAO;YACf,QAAQ,GAAG,CAAC;YAEZ,IAAI,iBAAiB,OAAO;gBAC3B,MAAM;YACP;YACA,MAAM,IAAI,MAAM;QACjB;IACD;IAEA,mBAAmB;IACnB,MAAM,MAAM,WAAyB,EAA0B;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAgB,eAAe;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,MAAM,SACL,QAAyB,EACiB;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAiC,kBAAkB;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,iCAAiC;IACjC,MAAM,qBACL,QAAgB,EAChB,KAAa,EACb,UAAuB,CAAC,CAAC,EACZ;QACb,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAChC,GAAG,OAAO;YACV,SAAS;gBACR,GAAG,QAAQ,OAAO;gBAClB,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;YACjC;QACD;IACD;IAEA,eAAe;IACf,MAAM,SAAS,KAAa,EAAkB;QAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAQ,UAAU;IACnD;IAEA,MAAM,QAAQ,EAAU,EAAE,KAAa,EAAgB;QACtD,OAAO,IAAI,CAAC,oBAAoB,CAAM,CAAC,OAAO,EAAE,IAAI,EAAE;IACvD;AACD;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/use-api.ts"], "sourcesContent": ["import { useSession } from \"next-auth/react\";\nimport { apiService } from \"@/lib/api\";\nimport {\n\tSession,\n\tCaisse,\n\tReunion,\n\tMember,\n\tPayment,\n\tSessionMember,\n\tCreateSessionDto,\n\tUpdateSessionDto,\n\tCreateCaisseDto,\n\tUpdateCaisseDto,\n\tUpdateReunionDto,\n\tCreateMemberDto,\n\tUpdateMemberDto,\n\tCreatePaymentDto,\n\tCreateSessionMemberDto,\n\tMemberDebrief,\n\tPaymentFilters,\n} from \"@/types\";\n\nexport function useApi() {\n\tconst { data: session } = useSession();\n\n\tconst authenticatedRequest = async <T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> => {\n\t\tif (!session?.accessToken) {\n\t\t\tthrow new Error(\"Non authentifié\");\n\t\t}\n\n\t\treturn apiService.authenticatedRequest<T>(\n\t\t\tendpoint,\n\t\t\tsession.accessToken,\n\t\t\toptions,\n\t\t);\n\t};\n\n\treturn {\n\t\t// Méthodes d'authentification (pas besoin de token)\n\t\tlogin: apiService.login.bind(apiService),\n\t\tregister: apiService.register.bind(apiService),\n\n\t\t// Méthodes authentifiées\n\t\tauthenticatedRequest,\n\n\t\t// Raccourcis pour les endpoints courants\n\t\tgetUsers: () => authenticatedRequest<any[]>(\"/users\"),\n\t\tgetUser: (id: string) => authenticatedRequest<any>(`/users/${id}`),\n\t\tcreateUser: (userData: any) =>\n\t\t\tauthenticatedRequest<any>(\"/users\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tupdateUser: (id: string, userData: any) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tdeleteUser: (id: string) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Sessions\n\t\tgetSessions: () => authenticatedRequest<Session[]>(\"/sessions\"),\n\t\tgetSession: (id: string) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`),\n\t\tcreateSession: (sessionData: CreateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(\"/sessions\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tupdateSession: (id: string, sessionData: UpdateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tdeleteSession: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Caisses\n\t\tgetCaisses: () => authenticatedRequest<Caisse[]>(\"/caisses\"),\n\t\tgetCaisse: (id: string) => authenticatedRequest<Caisse>(`/caisses/${id}`),\n\t\tcreateCaisse: (caisseData: CreateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(\"/caisses\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tupdateCaisse: (id: string, caisseData: UpdateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tdeleteCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\temargerCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}/emarger`, {\n\t\t\t\tmethod: \"POST\",\n\t\t\t}),\n\n\t\t// Réunions\n\t\tgetReunions: () => authenticatedRequest<Reunion[]>(\"/reunions\"),\n\t\tgetReunion: (id: string) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`),\n\t\tupdateReunion: (id: string, reunionData: UpdateReunionDto) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(reunionData),\n\t\t\t}),\n\n\t\t// Members\n\t\tgetMembers: () => authenticatedRequest<Member[]>(\"/members\"),\n\t\tgetMember: (id: string) => authenticatedRequest<Member>(`/members/${id}`),\n\t\tcreateMember: (memberData: CreateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(\"/members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tupdateMember: (id: string, memberData: UpdateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(`/members/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tdeleteMember: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/members/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\tgetMemberDebrief: (id: string, filters?: PaymentFilters) => {\n\t\t\tconst params = new URLSearchParams();\n\t\t\tif (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);\n\t\t\tif (filters?.dateTo) params.append('dateTo', filters.dateTo);\n\t\t\tif (filters?.sessionId) params.append('sessionId', filters.sessionId);\n\t\t\tconst query = params.toString() ? `?${params.toString()}` : '';\n\t\t\treturn authenticatedRequest<MemberDebrief>(`/members/${id}/debrief${query}`);\n\t\t},\n\n\t\t// Payments\n\t\tcreatePayment: (paymentData: CreatePaymentDto) =>\n\t\t\tauthenticatedRequest<Payment>(\"/payments\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(paymentData),\n\t\t\t}),\n\n\t\t// Session Members (inscription des membres aux sessions)\n\t\tgetSessionMembers: (sessionId: string) =>\n\t\t\tauthenticatedRequest<SessionMember[]>(`/sessions/${sessionId}/members`),\n\t\taddSessionMember: (sessionMemberData: CreateSessionMemberDto) =>\n\t\t\tauthenticatedRequest<SessionMember>(\"/session-members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionMemberData),\n\t\t\t}),\n\t\tremoveSessionMember: (sessionId: string, memberId: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${sessionId}/members/${memberId}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t};\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAqBO,SAAS;IACf,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IAEpC,MAAM,uBAAuB,OAC5B,UACA,UAAuB,CAAC,CAAC;QAEzB,IAAI,CAAC,SAAS,aAAa;YAC1B,MAAM,IAAI,MAAM;QACjB;QAEA,OAAO,2IAAU,CAAC,oBAAoB,CACrC,UACA,QAAQ,WAAW,EACnB;IAEF;IAEA,OAAO;QACN,oDAAoD;QACpD,OAAO,2IAAU,CAAC,KAAK,CAAC,IAAI,CAAC,2IAAU;QACvC,UAAU,2IAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,2IAAU;QAE7C,yBAAyB;QACzB;QAEA,yCAAyC;QACzC,UAAU,IAAM,qBAA4B;QAC5C,SAAS,CAAC,KAAe,qBAA0B,CAAC,OAAO,EAAE,IAAI;QACjE,YAAY,CAAC,WACZ,qBAA0B,UAAU;gBACnC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,YAAY,CAAC,IAAY,WACxB,qBAA0B,CAAC,OAAO,EAAE,IAAI,EAAE;gBACzC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,YAAY,CAAC,KACZ,qBAA0B,CAAC,OAAO,EAAE,IAAI,EAAE;gBACzC,QAAQ;YACT;QAED,WAAW;QACX,aAAa,IAAM,qBAAgC;QACnD,YAAY,CAAC,KACZ,qBAA8B,CAAC,UAAU,EAAE,IAAI;QAChD,eAAe,CAAC,cACf,qBAA8B,aAAa;gBAC1C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,eAAe,CAAC,IAAY,cAC3B,qBAA8B,CAAC,UAAU,EAAE,IAAI,EAAE;gBAChD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,eAAe,CAAC,KACf,qBAA2B,CAAC,UAAU,EAAE,IAAI,EAAE;gBAC7C,QAAQ;YACT;QAED,UAAU;QACV,YAAY,IAAM,qBAA+B;QACjD,WAAW,CAAC,KAAe,qBAA6B,CAAC,SAAS,EAAE,IAAI;QACxE,cAAc,CAAC,aACd,qBAA6B,YAAY;gBACxC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,IAAY,aAC1B,qBAA6B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC9C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,KACd,qBAA2B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC5C,QAAQ;YACT;QACD,eAAe,CAAC,KACf,qBAA6B,CAAC,SAAS,EAAE,GAAG,QAAQ,CAAC,EAAE;gBACtD,QAAQ;YACT;QAED,WAAW;QACX,aAAa,IAAM,qBAAgC;QACnD,YAAY,CAAC,KACZ,qBAA8B,CAAC,UAAU,EAAE,IAAI;QAChD,eAAe,CAAC,IAAY,cAC3B,qBAA8B,CAAC,UAAU,EAAE,IAAI,EAAE;gBAChD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QAED,UAAU;QACV,YAAY,IAAM,qBAA+B;QACjD,WAAW,CAAC,KAAe,qBAA6B,CAAC,SAAS,EAAE,IAAI;QACxE,cAAc,CAAC,aACd,qBAA6B,YAAY;gBACxC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,IAAY,aAC1B,qBAA6B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC9C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,KACd,qBAA2B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC5C,QAAQ;YACT;QACD,kBAAkB,CAAC,IAAY;YAC9B,MAAM,SAAS,IAAI;YACnB,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YACjE,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YAC3D,IAAI,SAAS,WAAW,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;YACpE,MAAM,QAAQ,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG;YAC5D,OAAO,qBAAoC,CAAC,SAAS,EAAE,GAAG,QAAQ,EAAE,OAAO;QAC5E;QAEA,WAAW;QACX,eAAe,CAAC,cACf,qBAA8B,aAAa;gBAC1C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QAED,yDAAyD;QACzD,mBAAmB,CAAC,YACnB,qBAAsC,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC;QACvE,kBAAkB,CAAC,oBAClB,qBAAoC,oBAAoB;gBACvD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,qBAAqB,CAAC,WAAmB,WACxC,qBAA2B,CAAC,UAAU,EAAE,UAAU,SAAS,EAAE,UAAU,EAAE;gBACxE,QAAQ;YACT;IACF;AACD", "debugId": null}}, {"offset": {"line": 585, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/types/index.ts"], "sourcesContent": ["// Types pour l'application Tontine\n\n// Enums\nexport enum CaisseType {\n  PRINCIPALE = 'PRINCIPALE',\n  REUNION = 'REUNION',\n}\n\nexport enum UserRole {\n  SECRETARY_GENERAL = 'secretary_general',\n  CONTROLLER = 'controller',\n  CASHIER = 'cashier',\n}\n\nexport enum UserStatus {\n  ACTIF = 'actif',\n  EN_ATTENTE = 'en_attente',\n  SUSPENDU = 'suspendu',\n}\n\nexport enum PaymentDirection {\n  IN = 'IN',\n  OUT = 'OUT',\n}\n\nexport enum PaymentFunction {\n  CONTRIBUTION = 'cotisation',\n  TRANSFER = 'transfert',\n  EXTERNAL = 'exterieur',\n}\n\n// Interfaces principales\nexport interface User {\n  _id: string;\n  username: string;\n  role: UserRole;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Session {\n  _id: string;\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  dateProchaineReunion?: string;\n  nextReunionId?: string;\n  partFixe: number;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Reunion {\n  _id: string;\n  dateReunion: string;\n  lieu?: string;\n  caissePrincipale?: string;\n  sessionId: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Caisse {\n  _id: string;\n  nom: string;\n  type: CaisseType;\n  soldeActuel: number;\n  sessionId?: string;\n  createdBy: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: Member (différent de User)\nexport interface Member {\n  _id: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: SessionMember (inscription d'un membre à une session)\nexport interface SessionMember {\n  _id: string;\n  sessionId: string;\n  memberId: string;\n  parts: number;\n  totalDue: number;\n  paidSoFar: number;\n  expectedToDate: number;\n  overdueAmount: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: Payment\nexport interface Payment {\n  _id: string;\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  date: string;\n  sessionId?: string;\n  reunionId?: string;\n  caisseId: string;\n  receivingCaisseId?: string;\n  memberId?: string;\n  reason?: string;\n  exitOrderId?: string;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// DTOs pour les formulaires\nexport interface CreateSessionDto {\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  partFixe: number;\n}\n\nexport interface UpdateSessionDto {\n  annee?: number;\n  dateDebut?: string;\n  dateFin?: string;\n  partFixe?: number;\n}\n\nexport interface CreateCaisseDto {\n  nom: string;\n  type: CaisseType;\n  soldeActuel?: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface UpdateCaisseDto {\n  nom?: string;\n  type?: CaisseType;\n  soldeActuel?: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface UpdateReunionDto {\n  dateReunion?: string;\n  lieu?: string;\n  caissePrincipale?: string;\n}\n\n// Nouveaux DTOs\nexport interface CreateMemberDto {\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface UpdateMemberDto {\n  firstName?: string;\n  lastName?: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface CreatePaymentDto {\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  caisseId: string;\n  receivingCaisseId?: string;\n  sessionId?: string;\n  reunionId?: string;\n  memberId?: string;\n  reason?: string;\n  exitOrderId?: string;\n}\n\nexport interface CreateSessionMemberDto {\n  sessionId: string;\n  memberId: string;\n  parts: number;\n}\n\n// Types pour les statistiques\nexport interface SessionStats {\n  total: number;\n  active: number;\n  completed: number;\n  totalPartFixe: number;\n}\n\nexport interface CaisseStats {\n  total: number;\n  principales: number;\n  reunions: number;\n  soldeTotal: number;\n  soldePrincipales: number;\n  soldeReunions: number;\n}\n\nexport interface MemberStats {\n  total: number;\n  withEmail: number;\n  withPhone: number;\n  withAddress: number;\n}\n\nexport interface PaymentStats {\n  totalIn: number;\n  totalOut: number;\n  netAmount: number;\n  contributionsTotal: number;\n  transfersTotal: number;\n  externalTotal: number;\n}\n\n// Types pour les formulaires\nexport interface SessionForm {\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  partFixe: number;\n}\n\nexport interface CaisseForm {\n  nom: string;\n  type: CaisseType;\n  soldeActuel: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface MemberForm {\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface PaymentForm {\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  caisseId: string;\n  receivingCaisseId?: string;\n  sessionId?: string;\n  reunionId?: string;\n  memberId?: string;\n  reason?: string;\n}\n\n// Types pour les réponses API\nexport interface ApiResponse<T> {\n  data: T;\n  message?: string;\n  success: boolean;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n// Types pour les filtres\nexport interface SessionFilters {\n  annee?: number;\n  status?: 'active' | 'completed' | 'all';\n}\n\nexport interface CaisseFilters {\n  type?: CaisseType | 'all';\n  sessionId?: string;\n}\n\nexport interface MemberFilters {\n  search?: string;\n  hasEmail?: boolean;\n  hasPhone?: boolean;\n}\n\nexport interface PaymentFilters {\n  direction?: PaymentDirection | 'all';\n  func?: PaymentFunction | 'all';\n  caisseId?: string;\n  sessionId?: string;\n  memberId?: string;\n  dateFrom?: string;\n  dateTo?: string;\n}\n\n// Types pour les réponses spéciales\nexport interface MemberDebrief {\n  member: Member;\n  totalIn: number;\n  totalOut: number;\n  netAmount: number;\n  contributionsTotal: number;\n  transfersTotal: number;\n  externalTotal: number;\n  payments: Payment[];\n}\n"], "names": [], "mappings": "AAAA,mCAAmC;AAEnC,QAAQ;;;;;;;;;;;;;AACD,IAAA,AAAK,oCAAA;;;WAAA;;AAKL,IAAA,AAAK,kCAAA;;;;WAAA;;AAML,IAAA,AAAK,oCAAA;;;;WAAA;;AAML,IAAA,AAAK,0CAAA;;;WAAA;;AAKL,IAAA,AAAK,yCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 631, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/app/dashboard/members/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { usePara<PERSON>, useRouter } from \"next/navigation\";\nimport { useSession } from \"next-auth/react\";\nimport {\n\tArrowLeft,\n\tMail,\n\tPhone,\n\tMapPin,\n\tDollarSign,\n\tTrendingUp,\n\tTrendingDown,\n} from \"lucide-react\";\nimport Link from \"next/link\";\n\nimport { Button } from \"@/components/ui/button\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport {\n\tTable,\n\tTableBody,\n\tTableCell,\n\tTableHead,\n\tTableHeader,\n\tTableRow,\n} from \"@/components/ui/table\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { useApi } from \"@/hooks/use-api\";\nimport {\n\tMember,\n\tMemberDebrief,\n\tUserRole,\n\tPaymentDirection,\n\tPaymentFunction,\n} from \"@/types\";\n\nexport default function MemberDetailPage() {\n\tconst params = useParams();\n\tconst router = useRouter();\n\tconst { data: session, status } = useSession();\n\tconst api = useApi();\n\n\tconst [member, setMember] = useState<Member | null>(null);\n\tconst [debrief, setDebrief] = useState<MemberDebrief | null>(null);\n\tconst [loading, setLoading] = useState(true);\n\tconst [error, setError] = useState<string | null>(null);\n\n\tconst memberId = params.id as string;\n\n\t// Vérifier les permissions\n\tconst canViewMembers =\n\t\tsession?.user &&\n\t\t((session.user as any).role === UserRole.SECRETARY_GENERAL ||\n\t\t\t(session.user as any).role === UserRole.CONTROLLER);\n\n\tuseEffect(() => {\n\t\tif (session?.accessToken && memberId) {\n\t\t\tloadData();\n\t\t}\n\t}, [status, memberId]);\n\n\tconst loadData = async () => {\n\t\ttry {\n\t\t\tsetLoading(true);\n\t\t\tsetError(null);\n\n\t\t\tconst [memberData, debriefData] = await Promise.all([\n\t\t\t\tapi.getMember(memberId),\n\t\t\t\tapi.getMemberDebrief(memberId),\n\t\t\t]);\n\n\t\t\tsetMember(memberData);\n\t\t\tsetDebrief(debriefData);\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Erreur lors du chargement:\", error);\n\t\t\tsetError(\"Erreur lors du chargement des données\");\n\t\t} finally {\n\t\t\tsetLoading(false);\n\t\t}\n\t};\n\n\tconst formatCurrency = (amount: number) => {\n\t\treturn new Intl.NumberFormat(\"fr-FR\", {\n\t\t\tstyle: \"currency\",\n\t\t\tcurrency: \"XAF\",\n\t\t}).format(amount);\n\t};\n\n\tconst formatDate = (dateString: string) => {\n\t\treturn new Date(dateString).toLocaleDateString(\"fr-FR\", {\n\t\t\tyear: \"numeric\",\n\t\t\tmonth: \"long\",\n\t\t\tday: \"numeric\",\n\t\t});\n\t};\n\n\tconst getPaymentFunctionBadge = (func: PaymentFunction) => {\n\t\tconst variants = {\n\t\t\t[PaymentFunction.CONTRIBUTION]: \"default\",\n\t\t\t[PaymentFunction.TRANSFER]: \"secondary\",\n\t\t\t[PaymentFunction.EXTERNAL]: \"outline\",\n\t\t} as const;\n\n\t\tconst labels = {\n\t\t\t[PaymentFunction.CONTRIBUTION]: \"Cotisation\",\n\t\t\t[PaymentFunction.TRANSFER]: \"Transfert\",\n\t\t\t[PaymentFunction.EXTERNAL]: \"Extérieur\",\n\t\t};\n\n\t\treturn <Badge variant={variants[func]}>{labels[func]}</Badge>;\n\t};\n\n\tconst getDirectionIcon = (direction: PaymentDirection) => {\n\t\treturn direction === PaymentDirection.IN ? (\n\t\t\t<TrendingUp className=\"h-4 w-4 text-green-600\" />\n\t\t) : (\n\t\t\t<TrendingDown className=\"h-4 w-4 text-red-600\" />\n\t\t);\n\t};\n\n\tif (!canViewMembers) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Link href=\"/dashboard/members\">\n\t\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tRetour\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\t\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<h2 className=\"text-lg font-semibold text-gray-900\">\n\t\t\t\t\t\t\tAccès refusé\n\t\t\t\t\t\t</h2>\n\t\t\t\t\t\t<p className=\"text-gray-600\">\n\t\t\t\t\t\t\tVous n'avez pas les permissions pour accéder à cette page.\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tif (loading) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Link href=\"/dashboard/members\">\n\t\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tRetour\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\t\t\t\t<div className=\"flex justify-center py-8\">\n\t\t\t\t\t<div className=\"text-gray-500\">Chargement...</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\tif (error || !member) {\n\t\treturn (\n\t\t\t<div className=\"space-y-6\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Link href=\"/dashboard/members\">\n\t\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tRetour\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\t\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<h2 className=\"text-lg font-semibold text-gray-900\">Erreur</h2>\n\t\t\t\t\t\t<p className=\"text-gray-600\">{error || \"Membre introuvable\"}</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* En-tête */}\n\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t<Link href=\"/dashboard/members\">\n\t\t\t\t\t\t<Button variant=\"ghost\" size=\"sm\">\n\t\t\t\t\t\t\t<ArrowLeft className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\tRetour\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</Link>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<h1 className=\"text-2xl font-bold text-gray-900\">\n\t\t\t\t\t\t\t{member.firstName} {member.lastName}\n\t\t\t\t\t\t</h1>\n\t\t\t\t\t\t<p className=\"text-gray-600\">Détails du membre</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<Link href={`/dashboard/members/${member._id}/edit`}>\n\t\t\t\t\t<Button>Modifier</Button>\n\t\t\t\t</Link>\n\t\t\t</div>\n\n\t\t\t{/* Informations du membre */}\n\t\t\t<Card>\n\t\t\t\t<CardHeader>\n\t\t\t\t\t<CardTitle>Informations personnelles</CardTitle>\n\t\t\t\t</CardHeader>\n\t\t\t\t<CardContent>\n\t\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<h3 className=\"font-medium text-gray-900 mb-2\">Nom complet</h3>\n\t\t\t\t\t\t\t<p className=\"text-gray-600\">\n\t\t\t\t\t\t\t\t{member.firstName} {member.lastName}\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{member.email && (\n\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t<h3 className=\"font-medium text-gray-900 mb-2\">Email</h3>\n\t\t\t\t\t\t\t\t<div className=\"flex items-center text-gray-600\">\n\t\t\t\t\t\t\t\t\t<Mail className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\t\t\t{member.email}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t)}\n\t\t\t\t\t\t{member.phone && (\n\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t<h3 className=\"font-medium text-gray-900 mb-2\">Téléphone</h3>\n\t\t\t\t\t\t\t\t<div className=\"flex items-center text-gray-600\">\n\t\t\t\t\t\t\t\t\t<Phone className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\t\t\t{member.phone}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t)}\n\t\t\t\t\t\t{member.address && (\n\t\t\t\t\t\t\t<div className=\"md:col-span-3\">\n\t\t\t\t\t\t\t\t<h3 className=\"font-medium text-gray-900 mb-2\">Adresse</h3>\n\t\t\t\t\t\t\t\t<div className=\"flex items-center text-gray-600\">\n\t\t\t\t\t\t\t\t\t<MapPin className=\"h-4 w-4 mr-2\" />\n\t\t\t\t\t\t\t\t\t{member.address}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t)}\n\t\t\t\t\t</div>\n\t\t\t\t</CardContent>\n\t\t\t</Card>\n\n\t\t\t{/* Debrief financier */}\n\t\t\t{debrief && (\n\t\t\t\t<>\n\t\t\t\t\t{/* Statistiques financières */}\n\t\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n\t\t\t\t\t\t<Card>\n\t\t\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\t\t\tTotal Entrées\n\t\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t\t<div className=\"text-2xl font-bold text-green-600\">\n\t\t\t\t\t\t\t\t\t{formatCurrency(debrief.totalIn)}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t\t</Card>\n\t\t\t\t\t\t<Card>\n\t\t\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\t\t\tTotal Sorties\n\t\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t\t<div className=\"text-2xl font-bold text-red-600\">\n\t\t\t\t\t\t\t\t\t{formatCurrency(debrief.totalOut)}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t\t</Card>\n\t\t\t\t\t\t<Card>\n\t\t\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\t\t\tSolde Net\n\t\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclassName={`text-2xl font-bold ${debrief.netAmount >= 0 ? \"text-green-600\" : \"text-red-600\"}`}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{formatCurrency(debrief.netAmount ?? 0)}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t\t</Card>\n\t\t\t\t\t\t<Card>\n\t\t\t\t\t\t\t<CardHeader className=\"pb-2\">\n\t\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium text-gray-600\">\n\t\t\t\t\t\t\t\t\tCotisations\n\t\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t\t<div className=\"text-2xl font-bold text-blue-600\">\n\t\t\t\t\t\t\t\t\t{formatCurrency(debrief.contributionsTotal ?? 0)}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t\t</Card>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t{/* Historique des paiements */}\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader>\n\t\t\t\t\t\t\t<CardTitle>Historique des paiements</CardTitle>\n\t\t\t\t\t\t\t<CardDescription>\n\t\t\t\t\t\t\t\t{debrief.payments?.length} paiement(s) enregistré(s)\n\t\t\t\t\t\t\t</CardDescription>\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t{debrief.payments?.length > 0 ? (\n\t\t\t\t\t\t\t\t<Table>\n\t\t\t\t\t\t\t\t\t<TableHeader>\n\t\t\t\t\t\t\t\t\t\t<TableRow>\n\t\t\t\t\t\t\t\t\t\t\t<TableHead>Date</TableHead>\n\t\t\t\t\t\t\t\t\t\t\t<TableHead>Type</TableHead>\n\t\t\t\t\t\t\t\t\t\t\t<TableHead>Fonction</TableHead>\n\t\t\t\t\t\t\t\t\t\t\t<TableHead>Montant</TableHead>\n\t\t\t\t\t\t\t\t\t\t</TableRow>\n\t\t\t\t\t\t\t\t\t</TableHeader>\n\t\t\t\t\t\t\t\t\t<TableBody>\n\t\t\t\t\t\t\t\t\t\t{debrief.payments.map((payment) => (\n\t\t\t\t\t\t\t\t\t\t\t<TableRow key={payment._id}>\n\t\t\t\t\t\t\t\t\t\t\t\t<TableCell>{formatDate(payment.date)}</TableCell>\n\t\t\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"flex items-center\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{getDirectionIcon(payment.direction)}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span className=\"ml-2\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{payment.direction === PaymentDirection.IN\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"Entrée\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"Sortie\"}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{getPaymentFunctionBadge(payment.func)}\n\t\t\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t\t\t<TableCell>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName={\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpayment.direction === PaymentDirection.IN\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"text-green-600\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"text-red-600\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{formatCurrency(payment.amount)}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t\t\t\t</TableCell>\n\t\t\t\t\t\t\t\t\t\t\t</TableRow>\n\t\t\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t\t</TableBody>\n\t\t\t\t\t\t\t\t</Table>\n\t\t\t\t\t\t\t) : (\n\t\t\t\t\t\t\t\t<div className=\"text-center py-8 text-gray-500\">\n\t\t\t\t\t\t\t\t\tAucun paiement enregistré\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t</>\n\t\t\t)}\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAEA;AACA;AAOA;AAQA;AACA;AACA;AAlCA;;;;;;;;;;;;;AA0Ce,SAAS;IACvB,MAAM,SAAS,IAAA,0RAAS;IACxB,MAAM,SAAS,IAAA,0RAAS;IACxB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,IAAA,2QAAU;IAC5C,MAAM,MAAM,IAAA,gJAAM;IAElB,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,4VAAQ,EAAgB;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,4VAAQ,EAAuB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,4VAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,4VAAQ,EAAgB;IAElD,MAAM,WAAW,OAAO,EAAE;IAE1B,2BAA2B;IAC3B,MAAM,iBACL,SAAS,QACT,CAAC,AAAC,QAAQ,IAAI,CAAS,IAAI,KAAK,6IAAQ,CAAC,iBAAiB,IACzD,AAAC,QAAQ,IAAI,CAAS,IAAI,KAAK,6IAAQ,CAAC,UAAU;IAEpD,IAAA,6VAAS,EAAC;QACT,IAAI,SAAS,eAAe,UAAU;YACrC;QACD;IACD,GAAG;QAAC;QAAQ;KAAS;IAErB,MAAM,WAAW;QAChB,IAAI;YACH,WAAW;YACX,SAAS;YAET,MAAM,CAAC,YAAY,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACnD,IAAI,SAAS,CAAC;gBACd,IAAI,gBAAgB,CAAC;aACrB;YAED,UAAU;YACV,WAAW;QACZ,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS;QACV,SAAU;YACT,WAAW;QACZ;IACD;IAEA,MAAM,iBAAiB,CAAC;QACvB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACrC,OAAO;YACP,UAAU;QACX,GAAG,MAAM,CAAC;IACX;IAEA,MAAM,aAAa,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACvD,MAAM;YACN,OAAO;YACP,KAAK;QACN;IACD;IAEA,MAAM,0BAA0B,CAAC;QAChC,MAAM,WAAW;YAChB,CAAC,oJAAe,CAAC,YAAY,CAAC,EAAE;YAChC,CAAC,oJAAe,CAAC,QAAQ,CAAC,EAAE;YAC5B,CAAC,oJAAe,CAAC,QAAQ,CAAC,EAAE;QAC7B;QAEA,MAAM,SAAS;YACd,CAAC,oJAAe,CAAC,YAAY,CAAC,EAAE;YAChC,CAAC,oJAAe,CAAC,QAAQ,CAAC,EAAE;YAC5B,CAAC,oJAAe,CAAC,QAAQ,CAAC,EAAE;QAC7B;QAEA,qBAAO,yXAAC,sJAAK;YAAC,SAAS,QAAQ,CAAC,KAAK;sBAAG,MAAM,CAAC,KAAK;;;;;;IACrD;IAEA,MAAM,mBAAmB,CAAC;QACzB,OAAO,cAAc,qJAAgB,CAAC,EAAE,iBACvC,yXAAC,gUAAU;YAAC,WAAU;;;;;iCAEtB,yXAAC,sUAAY;YAAC,WAAU;;;;;;IAE1B;IAEA,IAAI,CAAC,gBAAgB;QACpB,qBACC,yXAAC;YAAI,WAAU;;8BACd,yXAAC;oBAAI,WAAU;8BACd,cAAA,yXAAC,kTAAI;wBAAC,MAAK;kCACV,cAAA,yXAAC,wJAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC5B,yXAAC,6TAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;8BAKzC,yXAAC;oBAAI,WAAU;8BACd,cAAA,yXAAC;wBAAI,WAAU;;0CACd,yXAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,yXAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;IAOlC;IAEA,IAAI,SAAS;QACZ,qBACC,yXAAC;YAAI,WAAU;;8BACd,yXAAC;oBAAI,WAAU;8BACd,cAAA,yXAAC,kTAAI;wBAAC,MAAK;kCACV,cAAA,yXAAC,wJAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC5B,yXAAC,6TAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;8BAKzC,yXAAC;oBAAI,WAAU;8BACd,cAAA,yXAAC;wBAAI,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAInC;IAEA,IAAI,SAAS,CAAC,QAAQ;QACrB,qBACC,yXAAC;YAAI,WAAU;;8BACd,yXAAC;oBAAI,WAAU;8BACd,cAAA,yXAAC,kTAAI;wBAAC,MAAK;kCACV,cAAA,yXAAC,wJAAM;4BAAC,SAAQ;4BAAQ,MAAK;;8CAC5B,yXAAC,6TAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;8BAKzC,yXAAC;oBAAI,WAAU;8BACd,cAAA,yXAAC;wBAAI,WAAU;;0CACd,yXAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,yXAAC;gCAAE,WAAU;0CAAiB,SAAS;;;;;;;;;;;;;;;;;;;;;;;IAK5C;IAEA,qBACC,yXAAC;QAAI,WAAU;;0BAEd,yXAAC;gBAAI,WAAU;;kCACd,yXAAC;wBAAI,WAAU;;0CACd,yXAAC,kTAAI;gCAAC,MAAK;0CACV,cAAA,yXAAC,wJAAM;oCAAC,SAAQ;oCAAQ,MAAK;;sDAC5B,yXAAC,6TAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIxC,yXAAC;;kDACA,yXAAC;wCAAG,WAAU;;4CACZ,OAAO,SAAS;4CAAC;4CAAE,OAAO,QAAQ;;;;;;;kDAEpC,yXAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAG/B,yXAAC,kTAAI;wBAAC,MAAM,CAAC,mBAAmB,EAAE,OAAO,GAAG,CAAC,KAAK,CAAC;kCAClD,cAAA,yXAAC,wJAAM;sCAAC;;;;;;;;;;;;;;;;;0BAKV,yXAAC,oJAAI;;kCACJ,yXAAC,0JAAU;kCACV,cAAA,yXAAC,yJAAS;sCAAC;;;;;;;;;;;kCAEZ,yXAAC,2JAAW;kCACX,cAAA,yXAAC;4BAAI,WAAU;;8CACd,yXAAC;;sDACA,yXAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,yXAAC;4CAAE,WAAU;;gDACX,OAAO,SAAS;gDAAC;gDAAE,OAAO,QAAQ;;;;;;;;;;;;;gCAGpC,OAAO,KAAK,kBACZ,yXAAC;;sDACA,yXAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,yXAAC;4CAAI,WAAU;;8DACd,yXAAC,0SAAI;oDAAC,WAAU;;;;;;gDACf,OAAO,KAAK;;;;;;;;;;;;;gCAIf,OAAO,KAAK,kBACZ,yXAAC;;sDACA,yXAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,yXAAC;4CAAI,WAAU;;8DACd,yXAAC,6SAAK;oDAAC,WAAU;;;;;;gDAChB,OAAO,KAAK;;;;;;;;;;;;;gCAIf,OAAO,OAAO,kBACd,yXAAC;oCAAI,WAAU;;sDACd,yXAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,yXAAC;4CAAI,WAAU;;8DACd,yXAAC,oTAAM;oDAAC,WAAU;;;;;;gDACjB,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASpB,yBACA;;kCAEC,yXAAC;wBAAI,WAAU;;0CACd,yXAAC,oJAAI;;kDACJ,yXAAC,0JAAU;wCAAC,WAAU;kDACrB,cAAA,yXAAC,yJAAS;4CAAC,WAAU;sDAAoC;;;;;;;;;;;kDAI1D,yXAAC,2JAAW;kDACX,cAAA,yXAAC;4CAAI,WAAU;sDACb,eAAe,QAAQ,OAAO;;;;;;;;;;;;;;;;;0CAIlC,yXAAC,oJAAI;;kDACJ,yXAAC,0JAAU;wCAAC,WAAU;kDACrB,cAAA,yXAAC,yJAAS;4CAAC,WAAU;sDAAoC;;;;;;;;;;;kDAI1D,yXAAC,2JAAW;kDACX,cAAA,yXAAC;4CAAI,WAAU;sDACb,eAAe,QAAQ,QAAQ;;;;;;;;;;;;;;;;;0CAInC,yXAAC,oJAAI;;kDACJ,yXAAC,0JAAU;wCAAC,WAAU;kDACrB,cAAA,yXAAC,yJAAS;4CAAC,WAAU;sDAAoC;;;;;;;;;;;kDAI1D,yXAAC,2JAAW;kDACX,cAAA,yXAAC;4CACA,WAAW,CAAC,mBAAmB,EAAE,QAAQ,SAAS,IAAI,IAAI,mBAAmB,gBAAgB;sDAE5F,eAAe,QAAQ,SAAS,IAAI;;;;;;;;;;;;;;;;;0CAIxC,yXAAC,oJAAI;;kDACJ,yXAAC,0JAAU;wCAAC,WAAU;kDACrB,cAAA,yXAAC,yJAAS;4CAAC,WAAU;sDAAoC;;;;;;;;;;;kDAI1D,yXAAC,2JAAW;kDACX,cAAA,yXAAC;4CAAI,WAAU;sDACb,eAAe,QAAQ,kBAAkB,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAOlD,yXAAC,oJAAI;;0CACJ,yXAAC,0JAAU;;kDACV,yXAAC,yJAAS;kDAAC;;;;;;kDACX,yXAAC,+JAAe;;4CACd,QAAQ,QAAQ,EAAE;4CAAO;;;;;;;;;;;;;0CAG5B,yXAAC,2JAAW;0CACV,QAAQ,QAAQ,EAAE,SAAS,kBAC3B,yXAAC,sJAAK;;sDACL,yXAAC,4JAAW;sDACX,cAAA,yXAAC,yJAAQ;;kEACR,yXAAC,0JAAS;kEAAC;;;;;;kEACX,yXAAC,0JAAS;kEAAC;;;;;;kEACX,yXAAC,0JAAS;kEAAC;;;;;;kEACX,yXAAC,0JAAS;kEAAC;;;;;;;;;;;;;;;;;sDAGb,yXAAC,0JAAS;sDACR,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACtB,yXAAC,yJAAQ;;sEACR,yXAAC,0JAAS;sEAAE,WAAW,QAAQ,IAAI;;;;;;sEACnC,yXAAC,0JAAS;sEACT,cAAA,yXAAC;gEAAI,WAAU;;oEACb,iBAAiB,QAAQ,SAAS;kFACnC,yXAAC;wEAAK,WAAU;kFACd,QAAQ,SAAS,KAAK,qJAAgB,CAAC,EAAE,GACvC,WACA;;;;;;;;;;;;;;;;;sEAIN,yXAAC,0JAAS;sEACR,wBAAwB,QAAQ,IAAI;;;;;;sEAEtC,yXAAC,0JAAS;sEACT,cAAA,yXAAC;gEACA,WACC,QAAQ,SAAS,KAAK,qJAAgB,CAAC,EAAE,GACtC,mBACA;0EAGH,eAAe,QAAQ,MAAM;;;;;;;;;;;;mDAvBlB,QAAQ,GAAG;;;;;;;;;;;;;;;yDA+B7B,yXAAC;oCAAI,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;;;;;;;;AAUxD", "debugId": null}}]}