module.exports=[24725,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},62303,a=>{"use strict";a.s(["DollarSign",()=>b],62303);let b=(0,a.i(621).default)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},41405,a=>{"use strict";a.s(["Label",()=>g],41405);var b=a.i(68116),c=a.i(128),d=a.i(48206),e=c.forwardRef((a,c)=>(0,b.jsx)(d.Primitive.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));e.displayName="Label";var f=a.i(22171);function g({className:a,...c}){return(0,b.jsx)(e,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...c})}},6821,a=>{"use strict";a.s(["ArrowLeft",()=>b],6821);let b=(0,a.i(621).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},56619,a=>{"use strict";a.s(["default",()=>u]);var b=a.i(68116),c=a.i(128),d=a.i(81223),e=a.i(50395),f=a.i(64853),g=a.i(54636),h=a.i(66446),i=a.i(6821),j=a.i(72376),k=a.i(62303),l=a.i(33055),m=a.i(2979),n=a.i(78184),o=a.i(75780),p=a.i(60563),q=a.i(44932),r=a.i(12594),s=a.i(78655);let t=h.z.object({nom:h.z.string().min(1,"Le nom est requis").max(100,"Le nom est trop long"),type:h.z.nativeEnum(s.CaisseType,{required_error:"Le type est requis"}),soldeActuel:h.z.number().gte(0,"Le solde ne peut pas être négatif").max(1e7,"Le solde ne peut pas dépasser 10,000,000 FCFA"),sessionId:h.z.string().optional(),caissePrincipaleId:h.z.string().optional()}).refine(a=>a.type!==s.CaisseType.REUNION||a.sessionId&&a.caissePrincipaleId,{message:"Pour une caisse de réunion, la session et la caisse principale sont requises",path:["sessionId"]});function u(){let{data:a,status:h}=(0,d.useSession)(),u=(0,e.useRouter)(),v=(0,r.useApi)(),[w,x]=(0,c.useState)(!1),[y,z]=(0,c.useState)(null),[A,B]=(0,c.useState)([]),[C,D]=(0,c.useState)([]),[E,F]=(0,c.useState)(!0),G=a?.user&&"secretary_general"===a.user.role,H=(0,f.useForm)({resolver:(0,g.zodResolver)(t),defaultValues:{nom:"",type:s.CaisseType.PRINCIPALE,soldeActuel:0,sessionId:"",caissePrincipaleId:""}}),I=H.watch("type");(0,c.useEffect)(()=>{let b=async()=>{try{F(!0);let[a,b]=await Promise.all([v.getSessions(),v.getCaisses()]);B(a),D(b.filter(a=>a.type===s.CaisseType.PRINCIPALE))}catch(a){console.error("Erreur lors du chargement des données:",a)}finally{F(!1)}};a?.accessToken&&b()},[h]);let J=async a=>{if(!G)return void z("Vous n'avez pas les permissions pour créer une caisse");try{x(!0),z(null);let b={nom:a.nom,type:a.type,soldeActuel:a.soldeActuel,...a.type===s.CaisseType.REUNION&&{sessionId:a.sessionId,caissePrincipaleId:a.caissePrincipaleId}};await v.createCaisse(b),u.push("/dashboard/caisses")}catch(a){console.error("Erreur lors de la création:",a),z(a.message||"Une erreur est survenue lors de la création")}finally{x(!1)}};return G?(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(m.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,b.jsx)(l.default,{href:"/dashboard/caisses",children:(0,b.jsx)(i.ArrowLeft,{className:"h-4 w-4"})})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Nouvelle Caisse"}),(0,b.jsx)("p",{className:"text-muted-foreground",children:"Créer une nouvelle caisse principale ou de réunion"})]})]}),(0,b.jsxs)(o.Card,{children:[(0,b.jsxs)(o.CardHeader,{children:[(0,b.jsxs)(o.CardTitle,{className:"flex items-center gap-2",children:[(0,b.jsx)(j.Wallet,{className:"h-5 w-5"}),"Informations de la caisse"]}),(0,b.jsx)(o.CardDescription,{children:"Définissez les paramètres de la nouvelle caisse"})]}),(0,b.jsx)(o.CardContent,{children:E?(0,b.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"}),(0,b.jsx)("span",{className:"ml-2",children:"Chargement des données..."})]}):(0,b.jsx)(p.Form,{...H,children:(0,b.jsxs)("form",{onSubmit:H.handleSubmit(J),className:"space-y-6",children:[y&&(0,b.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,b.jsx)("p",{className:"text-sm text-red-600",children:y})}),(0,b.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,b.jsx)(p.FormField,{control:H.control,name:"nom",render:({field:a})=>(0,b.jsxs)(p.FormItem,{children:[(0,b.jsx)(p.FormLabel,{children:"Nom de la caisse"}),(0,b.jsx)(p.FormControl,{children:(0,b.jsx)(n.Input,{placeholder:"Ex: Caisse Principale 2025",...a})}),(0,b.jsx)(p.FormDescription,{children:"Nom descriptif de la caisse"}),(0,b.jsx)(p.FormMessage,{})]})}),(0,b.jsx)(p.FormField,{control:H.control,name:"type",render:({field:a})=>(0,b.jsxs)(p.FormItem,{children:[(0,b.jsx)(p.FormLabel,{children:"Type de caisse"}),(0,b.jsxs)(q.Select,{onValueChange:a.onChange,defaultValue:a.value,children:[(0,b.jsx)(p.FormControl,{children:(0,b.jsx)(q.SelectTrigger,{children:(0,b.jsx)(q.SelectValue,{placeholder:"Sélectionnez le type"})})}),(0,b.jsxs)(q.SelectContent,{children:[(0,b.jsx)(q.SelectItem,{value:s.CaisseType.PRINCIPALE,children:"Principale"}),(0,b.jsx)(q.SelectItem,{value:s.CaisseType.REUNION,children:"Réunion"})]})]}),(0,b.jsx)(p.FormDescription,{children:I===s.CaisseType.PRINCIPALE?"Caisse pour les fonds consolidés":"Caisse liée à une session spécifique"}),(0,b.jsx)(p.FormMessage,{})]})})]}),(0,b.jsx)(p.FormField,{control:H.control,name:"soldeActuel",render:({field:a})=>(0,b.jsxs)(p.FormItem,{children:[(0,b.jsxs)(p.FormLabel,{className:"flex items-center gap-2",children:[(0,b.jsx)(k.DollarSign,{className:"h-4 w-4"}),"Solde initial (FCFA)"]}),(0,b.jsx)(p.FormControl,{children:(0,b.jsx)(n.Input,{type:"number",...a,onChange:b=>a.onChange(parseInt(b.target.value)||0)})}),(0,b.jsx)(p.FormDescription,{children:"Montant initial dans la caisse"}),(0,b.jsx)(p.FormMessage,{})]})}),I===s.CaisseType.REUNION&&(0,b.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,b.jsx)(p.FormField,{control:H.control,name:"sessionId",render:({field:a})=>(0,b.jsxs)(p.FormItem,{children:[(0,b.jsx)(p.FormLabel,{children:"Session associée"}),(0,b.jsxs)(q.Select,{onValueChange:a.onChange,defaultValue:a.value,children:[(0,b.jsx)(p.FormControl,{children:(0,b.jsx)(q.SelectTrigger,{children:(0,b.jsx)(q.SelectValue,{placeholder:"Sélectionnez une session"})})}),(0,b.jsx)(q.SelectContent,{children:A.map(a=>(0,b.jsxs)(q.SelectItem,{value:a._id,children:[a.annee," (",new Date(a.dateDebut).toLocaleDateString()," ","-"," ",new Date(a.dateFin).toLocaleDateString(),")"]},a._id))})]}),(0,b.jsx)(p.FormDescription,{children:"Session à laquelle cette caisse est liée"}),(0,b.jsx)(p.FormMessage,{})]})}),(0,b.jsx)(p.FormField,{control:H.control,name:"caissePrincipaleId",render:({field:a})=>(0,b.jsxs)(p.FormItem,{children:[(0,b.jsx)(p.FormLabel,{children:"Caisse principale"}),(0,b.jsxs)(q.Select,{onValueChange:a.onChange,defaultValue:a.value,children:[(0,b.jsx)(p.FormControl,{children:(0,b.jsx)(q.SelectTrigger,{children:(0,b.jsx)(q.SelectValue,{placeholder:"Sélectionnez une caisse principale"})})}),(0,b.jsx)(q.SelectContent,{children:C.map(a=>(0,b.jsxs)(q.SelectItem,{value:a._id,children:[a.nom," (",a.soldeActuel.toLocaleString()," FCFA)"]},a._id))})]}),(0,b.jsx)(p.FormDescription,{children:"Caisse principale pour l'émargement"}),(0,b.jsx)(p.FormMessage,{})]})})]}),(0,b.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,b.jsx)(m.Button,{variant:"outline",asChild:!0,children:(0,b.jsx)(l.default,{href:"/dashboard/caisses",children:"Annuler"})}),(0,b.jsx)(m.Button,{type:"submit",disabled:w,children:w?"Création...":"Créer la caisse"})]})]})})})]}),(0,b.jsxs)(o.Card,{children:[(0,b.jsx)(o.CardHeader,{children:(0,b.jsx)(o.CardTitle,{children:"Types de caisses"})}),(0,b.jsx)(o.CardContent,{children:(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium",children:"Caisse Principale"}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground",children:"Caisse pour consolider les fonds de toutes les réunions. Les fonds des caisses de réunion peuvent être émargés vers cette caisse."})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"font-medium",children:"Caisse de Réunion"}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground",children:"Caisse liée à une session spécifique. Doit être associée à une caisse principale pour permettre l'émargement des fonds."})]})]})})]})]}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(m.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,b.jsx)(l.default,{href:"/dashboard/caisses",children:(0,b.jsx)(i.ArrowLeft,{className:"h-4 w-4"})})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Nouvelle Caisse"}),(0,b.jsx)("p",{className:"text-muted-foreground",children:"Créer une nouvelle caisse"})]})]}),(0,b.jsx)(o.Card,{children:(0,b.jsx)(o.CardContent,{className:"pt-6",children:(0,b.jsxs)("div",{className:"text-center py-8",children:[(0,b.jsx)("p",{className:"text-muted-foreground",children:"Vous n'avez pas les permissions pour créer une caisse."}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Seuls les administrateurs et trésoriers peuvent créer des caisses."})]})})})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__38add67f._.js.map