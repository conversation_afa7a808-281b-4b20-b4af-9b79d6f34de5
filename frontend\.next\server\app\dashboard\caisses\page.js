var R=require("../../../chunks/ssr/[turbopack]_runtime.js")("server/app/dashboard/caisses/page.js")
R.c("server/chunks/ssr/9e883__pnpm_63af8711._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/frontend_src_app_8a61bd4d._.js")
R.c("server/chunks/ssr/[root-of-the-server]__2a207f5e._.js")
R.c("server/chunks/ssr/94728_next_dist_client_components_30d6cb2e._.js")
R.c("server/chunks/ssr/94728_next_dist_client_components_builtin_forbidden_924fc06b.js")
R.c("server/chunks/ssr/94728_next_dist_client_components_builtin_unauthorized_938777fe.js")
R.c("server/chunks/ssr/94728_next_dist_client_components_builtin_global-error_d43bf2c7.js")
R.c("server/chunks/ssr/frontend_src_2e8922a6._.js")
R.c("server/chunks/ssr/94728_next_dist_22ab2df2._.js")
R.c("server/chunks/ssr/[root-of-the-server]__35775917._.js")
R.m("[project]/frontend/.next-internal/server/app/dashboard/caisses/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/dashboard/caisses/page { GLOBAL_ERROR_MODULE => \"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/frontend/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/frontend/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/frontend/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/frontend/src/app/dashboard/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_7 => \"[project]/frontend/src/app/dashboard/caisses/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/dashboard/caisses/page { GLOBAL_ERROR_MODULE => \"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/frontend/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/frontend/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/frontend/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/frontend/src/app/dashboard/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_7 => \"[project]/frontend/src/app/dashboard/caisses/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
