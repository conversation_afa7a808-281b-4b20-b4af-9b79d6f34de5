module.exports=[24725,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},62303,a=>{"use strict";a.s(["DollarSign",()=>b],62303);let b=(0,a.i(621).default)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6601,a=>{"use strict";a.s(["Trash2",()=>b],6601);let b=(0,a.i(621).default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},41405,a=>{"use strict";a.s(["Label",()=>g],41405);var b=a.i(68116),c=a.i(128),d=a.i(48206),e=c.forwardRef((a,c)=>(0,b.jsx)(d.Primitive.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));e.displayName="Label";var f=a.i(22171);function g({className:a,...c}){return(0,b.jsx)(e,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...c})}},6821,a=>{"use strict";a.s(["ArrowLeft",()=>b],6821);let b=(0,a.i(621).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},98455,a=>{"use strict";a.s(["default",()=>v]);var b=a.i(68116),c=a.i(128),d=a.i(81223),e=a.i(50395),f=a.i(64853),g=a.i(54636),h=a.i(66446),i=a.i(6821),j=a.i(72376),k=a.i(62303),l=a.i(6601),m=a.i(33055),n=a.i(2979),o=a.i(78184),p=a.i(75780),q=a.i(60563),r=a.i(44932),s=a.i(12594),t=a.i(78655);let u=h.z.object({nom:h.z.string().min(1,"Le nom est requis").max(100,"Le nom est trop long"),type:h.z.nativeEnum(t.CaisseType,{required_error:"Le type est requis"}),soldeActuel:h.z.number().min(0,"Le solde ne peut pas être négatif").max(1e7,"Le solde ne peut pas dépasser 10,000,000 FCFA"),sessionId:h.z.string().optional(),caissePrincipaleId:h.z.string().optional()}).refine(a=>a.type!==t.CaisseType.REUNION||a.sessionId&&a.caissePrincipaleId,{message:"Pour une caisse de réunion, la session et la caisse principale sont requises",path:["sessionId"]});function v(){let{id:a}=(0,e.useParams)(),{data:h,status:v}=(0,d.useSession)(),w=(0,e.useRouter)(),x=(0,s.useApi)(),[y,z]=(0,c.useState)(null),[A,B]=(0,c.useState)(!1),[C,D]=(0,c.useState)(!1),[E,F]=(0,c.useState)(null),[G,H]=(0,c.useState)(!0),[I,J]=(0,c.useState)([]),[K,L]=(0,c.useState)([]),M=h?.user&&"secretary_general"===h.user.role,N=(0,f.useForm)({resolver:(0,g.zodResolver)(u),defaultValues:{nom:"",type:t.CaisseType.PRINCIPALE,soldeActuel:0,sessionId:"",caissePrincipaleId:""}}),O=N.watch("type");(0,c.useEffect)(()=>{let b=async()=>{if(a&&"string"==typeof a)try{H(!0);let[b,c,d]=await Promise.all([x.getCaisse(a),x.getSessions(),x.getCaisses()]);z(b),J(c),L(d.filter(a=>a.type===t.CaisseType.PRINCIPALE&&a._id!==b._id)),N.reset({nom:b.nom,type:b.type,soldeActuel:b.soldeActuel,sessionId:b.sessionId||"",caissePrincipaleId:b.caissePrincipaleId||""})}catch(a){console.error("Erreur lors du chargement:",a),F("Caisse introuvable")}finally{H(!1)}};h?.accessToken&&b()},[a,v]);let P=async b=>{if(!M||!a||"string"!=typeof a)return void F("Vous n'avez pas les permissions pour modifier cette caisse");try{B(!0),F(null);let c={nom:b.nom,type:b.type,soldeActuel:b.soldeActuel,...b.type===t.CaisseType.REUNION&&{sessionId:b.sessionId,caissePrincipaleId:b.caissePrincipaleId}};await x.updateCaisse(a,c),w.push("/dashboard/caisses")}catch(a){console.error("Erreur lors de la modification:",a),F(a.message||"Une erreur est survenue lors de la modification")}finally{B(!1)}},Q=async()=>{if(M&&a&&"string"==typeof a&&y&&confirm(`\xcates-vous s\xfbr de vouloir supprimer la caisse "${y.nom}" ?`))try{D(!0),await x.deleteCaisse(a),w.push("/dashboard/caisses")}catch(a){console.error("Erreur lors de la suppression:",a),F(a.message||"Une erreur est survenue lors de la suppression")}finally{D(!1)}};return G?(0,b.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"}),(0,b.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Chargement de la caisse..."})]})}):M?y?(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(n.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,b.jsx)(m.default,{href:"/dashboard/caisses",children:(0,b.jsx)(i.ArrowLeft,{className:"h-4 w-4"})})}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("h1",{className:"text-3xl font-bold tracking-tight",children:["Modifier ",y.nom]}),(0,b.jsx)("p",{className:"text-muted-foreground",children:"Modifier les paramètres de la caisse"})]})]}),(0,b.jsxs)(n.Button,{variant:"destructive",onClick:Q,disabled:C,children:[(0,b.jsx)(l.Trash2,{className:"mr-2 h-4 w-4"}),C?"Suppression...":"Supprimer"]})]}),(0,b.jsxs)(p.Card,{children:[(0,b.jsxs)(p.CardHeader,{children:[(0,b.jsxs)(p.CardTitle,{className:"flex items-center gap-2",children:[(0,b.jsx)(j.Wallet,{className:"h-5 w-5"}),"Informations de la caisse"]}),(0,b.jsx)(p.CardDescription,{children:"Modifiez les paramètres de la caisse"})]}),(0,b.jsx)(p.CardContent,{children:(0,b.jsx)(q.Form,{...N,children:(0,b.jsxs)("form",{onSubmit:N.handleSubmit(P),className:"space-y-6",children:[E&&(0,b.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,b.jsx)("p",{className:"text-sm text-red-600",children:E})}),(0,b.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,b.jsx)(q.FormField,{control:N.control,name:"nom",render:({field:a})=>(0,b.jsxs)(q.FormItem,{children:[(0,b.jsx)(q.FormLabel,{children:"Nom de la caisse"}),(0,b.jsx)(q.FormControl,{children:(0,b.jsx)(o.Input,{placeholder:"Ex: Caisse Principale 2025",...a})}),(0,b.jsx)(q.FormDescription,{children:"Nom descriptif de la caisse"}),(0,b.jsx)(q.FormMessage,{})]})}),(0,b.jsx)(q.FormField,{control:N.control,name:"type",render:({field:a})=>(0,b.jsxs)(q.FormItem,{children:[(0,b.jsx)(q.FormLabel,{children:"Type de caisse"}),(0,b.jsxs)(r.Select,{onValueChange:a.onChange,defaultValue:a.value,children:[(0,b.jsx)(q.FormControl,{children:(0,b.jsx)(r.SelectTrigger,{children:(0,b.jsx)(r.SelectValue,{placeholder:"Sélectionnez le type"})})}),(0,b.jsxs)(r.SelectContent,{children:[(0,b.jsx)(r.SelectItem,{value:t.CaisseType.PRINCIPALE,children:"Principale"}),(0,b.jsx)(r.SelectItem,{value:t.CaisseType.REUNION,children:"Réunion"})]})]}),(0,b.jsx)(q.FormDescription,{children:O===t.CaisseType.PRINCIPALE?"Caisse pour les fonds consolidés":"Caisse liée à une session spécifique"}),(0,b.jsx)(q.FormMessage,{})]})})]}),(0,b.jsx)(q.FormField,{control:N.control,name:"soldeActuel",render:({field:a})=>(0,b.jsxs)(q.FormItem,{children:[(0,b.jsxs)(q.FormLabel,{className:"flex items-center gap-2",children:[(0,b.jsx)(k.DollarSign,{className:"h-4 w-4"}),"Solde actuel (FCFA)"]}),(0,b.jsx)(q.FormControl,{children:(0,b.jsx)(o.Input,{type:"number",...a,onChange:b=>a.onChange(parseInt(b.target.value)||0)})}),(0,b.jsx)(q.FormDescription,{children:"Montant actuel dans la caisse"}),(0,b.jsx)(q.FormMessage,{})]})}),O===t.CaisseType.REUNION&&(0,b.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,b.jsx)(q.FormField,{control:N.control,name:"sessionId",render:({field:a})=>(0,b.jsxs)(q.FormItem,{children:[(0,b.jsx)(q.FormLabel,{children:"Session associée"}),(0,b.jsxs)(r.Select,{onValueChange:a.onChange,defaultValue:a.value,children:[(0,b.jsx)(q.FormControl,{children:(0,b.jsx)(r.SelectTrigger,{children:(0,b.jsx)(r.SelectValue,{placeholder:"Sélectionnez une session"})})}),(0,b.jsx)(r.SelectContent,{children:I.map(a=>(0,b.jsxs)(r.SelectItem,{value:a._id,children:[a.annee," (",new Date(a.dateDebut).toLocaleDateString()," ","-"," ",new Date(a.dateFin).toLocaleDateString(),")"]},a._id))})]}),(0,b.jsx)(q.FormDescription,{children:"Session à laquelle cette caisse est liée"}),(0,b.jsx)(q.FormMessage,{})]})}),(0,b.jsx)(q.FormField,{control:N.control,name:"caissePrincipaleId",render:({field:a})=>(0,b.jsxs)(q.FormItem,{children:[(0,b.jsx)(q.FormLabel,{children:"Caisse principale"}),(0,b.jsxs)(r.Select,{onValueChange:a.onChange,defaultValue:a.value,children:[(0,b.jsx)(q.FormControl,{children:(0,b.jsx)(r.SelectTrigger,{children:(0,b.jsx)(r.SelectValue,{placeholder:"Sélectionnez une caisse principale"})})}),(0,b.jsx)(r.SelectContent,{children:K.map(a=>(0,b.jsxs)(r.SelectItem,{value:a._id,children:[a.nom," (",a.soldeActuel.toLocaleString()," FCFA)"]},a._id))})]}),(0,b.jsx)(q.FormDescription,{children:"Caisse principale pour l'émargement"}),(0,b.jsx)(q.FormMessage,{})]})})]}),(0,b.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,b.jsx)(n.Button,{variant:"outline",asChild:!0,children:(0,b.jsx)(m.default,{href:"/dashboard/caisses",children:"Annuler"})}),(0,b.jsx)(n.Button,{type:"submit",disabled:A,children:A?"Modification...":"Modifier la caisse"})]})]})})})]})]}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(n.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,b.jsx)(m.default,{href:"/dashboard/caisses",children:(0,b.jsx)(i.ArrowLeft,{className:"h-4 w-4"})})}),(0,b.jsx)("div",{children:(0,b.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Caisse introuvable"})})]}),(0,b.jsx)(p.Card,{children:(0,b.jsx)(p.CardContent,{className:"pt-6",children:(0,b.jsx)("div",{className:"text-center py-8",children:(0,b.jsx)("p",{className:"text-muted-foreground",children:"La caisse demandée n'a pas été trouvée."})})})})]}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(n.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,b.jsx)(m.default,{href:"/dashboard/caisses",children:(0,b.jsx)(i.ArrowLeft,{className:"h-4 w-4"})})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Modifier Caisse"}),(0,b.jsx)("p",{className:"text-muted-foreground",children:"Modifier les paramètres de la caisse"})]})]}),(0,b.jsx)(p.Card,{children:(0,b.jsx)(p.CardContent,{className:"pt-6",children:(0,b.jsxs)("div",{className:"text-center py-8",children:[(0,b.jsx)("p",{className:"text-muted-foreground",children:"Vous n'avez pas les permissions pour modifier cette caisse."}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Seuls les administrateurs et trésoriers peuvent modifier les caisses."})]})})})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__01885298._.js.map