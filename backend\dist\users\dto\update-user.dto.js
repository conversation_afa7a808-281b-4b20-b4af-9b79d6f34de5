"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUserWrapperDto = exports.UpdateUserDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const user_role_enum_1 = require("../../common/enums/user-role.enum");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class UpdateUserDto {
}
exports.UpdateUserDto = UpdateUserDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiPropertyOptional)({ description: 'Mot de passe (laisser vide si non modifié)', example: '' }),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "password", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(user_role_enum_1.UserRole),
    (0, class_validator_1.IsNotEmpty)(),
    (0, swagger_1.ApiProperty)({ enum: user_role_enum_1.UserRole, description: 'Rôle de l\'utilisateur' }),
    __metadata("design:type", String)
], UpdateUserDto.prototype, "role", void 0);
class UpdateUserWrapperDto {
}
exports.UpdateUserWrapperDto = UpdateUserWrapperDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({ example: '64f8c2b7e1a2b3c4d5e6f7a8' }),
    __metadata("design:type", String)
], UpdateUserWrapperDto.prototype, "_id", void 0);
__decorate([
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => UpdateUserDto),
    (0, swagger_1.ApiProperty)({ type: UpdateUserDto }),
    __metadata("design:type", UpdateUserDto)
], UpdateUserWrapperDto.prototype, "data", void 0);
