import { Body, Controller, ForbiddenException, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../common/guards/role.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../common/enums/user-role.enum';
import { PaymentsService } from './payments.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Caisse, CaisseDocument } from '../caisses/schemas/caisse.schema';
import { Model, Types } from 'mongoose';

@ApiTags('payments')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('payments')
export class PaymentsController {
  constructor(
    private readonly paymentsService: PaymentsService,
    @InjectModel(Caisse.name) private caisseModel: Model<CaisseDocument>,
  ) {}

  // cashier records payments for reunion caisse; secretary/controller can also record external/transfer
  @Post()
  @Roles(UserRole.CASHIER, UserRole.SECRETARY_GENERAL, UserRole.CONTROLLER)
  @ApiOperation({ summary: 'Créer un paiement', description: 'Crée un paiement sur une caisse. Règles: (1) REUNION doit être OPEN (sauf TRANSFER), (2) contribution doit être IN, (3) transfert requiert receivingCaisseId, (4) sortie REUNION requiert un exitOrderId valide.' })
  async create(@Body() dto: CreatePaymentDto, @Req() req: any) {
    // Règle 3-A: un CASHIER ne peut créer que sur sa caisse assignée
    if (req.user?.role === UserRole.CASHIER) {
      const caisse = await this.caisseModel.findById(new Types.ObjectId(dto.caisseId));
      if (!caisse || !caisse.cashierId || caisse.cashierId.toString() !== req.user._id) {
        throw new ForbiddenException('CASHIER cannot create payment on a different caisse');
      }
    }
    return this.paymentsService.create(dto, req.user._id);
  }
}