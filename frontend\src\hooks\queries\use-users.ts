import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { useApi } from '@/hooks/use-api';

// Query Keys
export const userKeys = {
  all: ['users'] as const,
  lists: () => [...userKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...userKeys.lists(), { filters }] as const,
  details: () => [...userKeys.all, 'detail'] as const,
  detail: (id: string) => [...userKeys.details(), id] as const,
  stats: () => [...userKeys.all, 'stats'] as const,
  byRole: (role: string) => [...userKeys.all, 'by-role', role] as const,
};

// Hooks
export function useUsers() {
  const { data: session } = useSession();
  const api = useApi();

  return useQuery({
    queryKey: userKeys.lists(),
    queryFn: () => api.getUsers(),
    enabled: !!session?.accessToken,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useUser(userId: string) {
  const { data: session } = useSession();
  const api = useApi();

  return useQuery({
    queryKey: userKeys.detail(userId),
    queryFn: () => api.getUser(userId),
    enabled: !!session?.accessToken && !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useUserStats() {
  const { data: session } = useSession();
  const api = useApi();

  return useQuery({
    queryKey: userKeys.stats(),
    queryFn: () => api.authenticatedRequest('/users/stats'),
    enabled: !!session?.accessToken,
    staleTime: 10 * 60 * 1000, // 10 minutes - stats don't change frequently
  });
}

export function useUsersByRole(role: string) {
  const { data: session } = useSession();
  const api = useApi();

  return useQuery({
    queryKey: userKeys.byRole(role),
    queryFn: () => api.authenticatedRequest(`/users/by-role/${role}`),
    enabled: !!session?.accessToken && !!role,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Mutations
export function useCreateUser() {
  const queryClient = useQueryClient();
  const api = useApi();

  return useMutation({
    mutationFn: (userData: any) => api.createUser(userData),
    onSuccess: () => {
      // Invalidate and refetch users list and stats
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeys.stats() });
    },
    onError: (error) => {
      console.error('Error creating user:', error);
    },
  });
}

export function useUpdateUser() {
  const queryClient = useQueryClient();
  const api = useApi();

  return useMutation({
    mutationFn: ({ id, userData }: { id: string; userData: any }) => 
      api.updateUser(id, userData),
    onSuccess: (_, { id }) => {
      // Invalidate specific user, users list, and stats
      queryClient.invalidateQueries({ queryKey: userKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeys.stats() });
      // Invalidate all by-role queries since role might have changed
      queryClient.invalidateQueries({ queryKey: [...userKeys.all, 'by-role'] });
    },
    onError: (error) => {
      console.error('Error updating user:', error);
    },
  });
}

export function useDeleteUser() {
  const queryClient = useQueryClient();
  const api = useApi();

  return useMutation({
    mutationFn: (userId: string) => api.deleteUser(userId),
    onSuccess: (_, userId) => {
      // Remove from cache and invalidate lists
      queryClient.removeQueries({ queryKey: userKeys.detail(userId) });
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeys.stats() });
      // Invalidate all by-role queries
      queryClient.invalidateQueries({ queryKey: [...userKeys.all, 'by-role'] });
    },
    onError: (error) => {
      console.error('Error deleting user:', error);
    },
  });
}
