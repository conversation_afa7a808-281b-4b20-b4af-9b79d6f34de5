import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsMongoId, IsOptional } from 'class-validator';
import { PaymentDirection, PaymentFunction } from '../../payments/schemas/payment.schema';

export class PaymentFiltersDto {
  @ApiPropertyOptional({ description: 'Date de début (incluse) ISO 8601' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: 'Date de fin (incluse) ISO 8601' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsMongoId()
  memberId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsMongoId()
  sessionId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsMongoId()
  reunionId?: string;

  @ApiPropertyOptional({ enum: PaymentDirection })
  @IsOptional()
  @IsEnum(PaymentDirection)
  direction?: PaymentDirection;

  @ApiPropertyOptional({ enum: PaymentFunction })
  @IsOptional()
  @IsEnum(PaymentFunction)
  func?: PaymentFunction;
}