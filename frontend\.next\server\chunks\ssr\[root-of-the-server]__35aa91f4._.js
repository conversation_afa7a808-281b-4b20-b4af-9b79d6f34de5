module.exports=[24725,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},62303,a=>{"use strict";a.s(["DollarSign",()=>b],62303);let b=(0,a.i(621).default)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},48099,85955,54839,a=>{"use strict";a.s(["createCollection",()=>g],48099);var b=a.i(128),c=a.i(54130),d=a.i(9403),e=a.i(85689),f=a.i(68116);function g(a){let g=a+"CollectionProvider",[h,i]=(0,c.createContextScope)(g),[j,k]=h(g,{collectionRef:{current:null},itemMap:new Map}),l=a=>{let{scope:c,children:d}=a,e=b.default.useRef(null),g=b.default.useRef(new Map).current;return(0,f.jsx)(j,{scope:c,itemMap:g,collectionRef:e,children:d})};l.displayName=g;let m=a+"CollectionSlot",n=(0,e.createSlot)(m),o=b.default.forwardRef((a,b)=>{let{scope:c,children:e}=a,g=k(m,c),h=(0,d.useComposedRefs)(b,g.collectionRef);return(0,f.jsx)(n,{ref:h,children:e})});o.displayName=m;let p=a+"CollectionItemSlot",q="data-radix-collection-item",r=(0,e.createSlot)(p),s=b.default.forwardRef((a,c)=>{let{scope:e,children:g,...h}=a,i=b.default.useRef(null),j=(0,d.useComposedRefs)(c,i),l=k(p,e);return b.default.useEffect(()=>(l.itemMap.set(i,{ref:i,...h}),()=>void l.itemMap.delete(i))),(0,f.jsx)(r,{...{[q]:""},ref:j,children:g})});return s.displayName=p,[{Provider:l,Slot:o,ItemSlot:s},function(c){let d=k(a+"CollectionConsumer",c);return b.default.useCallback(()=>{let a=d.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${q}]`));return Array.from(d.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[d.collectionRef,d.itemMap])},i]}var h=new WeakMap;function i(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=j(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function j(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],h.set(this,!0)}set(a,b){return h.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=j(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let k=this.size+ +!e;g<0&&h++;let l=[...this.#a],m=!1;for(let a=h;a<k;a++)if(h===a){let f=l[a];l[a]===b&&(f=l[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{m||l[a-1]!==b||(m=!0);let c=l[m?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=i(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=i(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return i(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}}),a.s(["useDirection",()=>l],85955);var k=b.createContext(void 0);function l(a){let c=b.useContext(k);return a||c||"ltr"}a.s(["CheckIcon",()=>m],54839);let m=(0,a.i(621).default)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},12594,a=>{"use strict";a.s(["useApi",()=>d]);var b=a.i(81223),c=a.i(34075);function d(){let{data:a}=(0,b.useSession)(),d=async(b,d={})=>{if(!a?.accessToken)throw Error("Non authentifié");return c.apiService.authenticatedRequest(b,a.accessToken,d)};return{login:c.apiService.login.bind(c.apiService),register:c.apiService.register.bind(c.apiService),authenticatedRequest:d,getUsers:()=>d("/users"),getUser:a=>d(`/users/${a}`),createUser:a=>d("/users",{method:"POST",body:JSON.stringify(a)}),updateUser:(a,b)=>d(`/users/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteUser:a=>d(`/users/${a}`,{method:"DELETE"}),getSessions:()=>d("/sessions"),getSession:a=>d(`/sessions/${a}`),createSession:a=>d("/sessions",{method:"POST",body:JSON.stringify(a)}),updateSession:(a,b)=>d(`/sessions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteSession:a=>d(`/sessions/${a}`,{method:"DELETE"}),getCaisses:()=>d("/caisses"),getCaisse:a=>d(`/caisses/${a}`),createCaisse:a=>d("/caisses",{method:"POST",body:JSON.stringify(a)}),updateCaisse:(a,b)=>d(`/caisses/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteCaisse:a=>d(`/caisses/${a}`,{method:"DELETE"}),emargerCaisse:a=>d(`/caisses/${a}/emarger`,{method:"POST"}),getReunions:()=>d("/reunions"),getReunion:a=>d(`/reunions/${a}`),updateReunion:(a,b)=>d(`/reunions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),getMembers:()=>d("/members"),getMember:a=>d(`/members/${a}`),createMember:a=>d("/members",{method:"POST",body:JSON.stringify(a)}),updateMember:(a,b)=>d(`/members/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteMember:a=>d(`/members/${a}`,{method:"DELETE"}),getMemberDebrief:(a,b)=>{let c=new URLSearchParams;b?.dateFrom&&c.append("dateFrom",b.dateFrom),b?.dateTo&&c.append("dateTo",b.dateTo),b?.sessionId&&c.append("sessionId",b.sessionId);let e=c.toString()?`?${c.toString()}`:"";return d(`/members/${a}/debrief${e}`)},createPayment:a=>d("/payments",{method:"POST",body:JSON.stringify(a)}),getSessionMembers:a=>d(`/sessions/${a}/members`),addSessionMember:a=>d("/session-members",{method:"POST",body:JSON.stringify(a)}),removeSessionMember:(a,b)=>d(`/sessions/${a}/members/${b}`,{method:"DELETE"})}}},78655,a=>{"use strict";a.s(["CaisseType",()=>b,"PaymentDirection",()=>d,"PaymentFunction",()=>e,"UserRole",()=>c]);var b=function(a){return a.PRINCIPALE="PRINCIPALE",a.REUNION="REUNION",a}({}),c=function(a){return a.SECRETARY_GENERAL="secretary_general",a.CONTROLLER="controller",a.CASHIER="cashier",a}({}),d=function(a){return a.IN="IN",a.OUT="OUT",a}({}),e=function(a){return a.CONTRIBUTION="cotisation",a.TRANSFER="transfert",a.EXTERNAL="exterieur",a}({})},91486,a=>{"use strict";a.s(["Plus",()=>b],91486);let b=(0,a.i(621).default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},93470,a=>{"use strict";a.s(["Badge",()=>g]);var b=a.i(68116),c=a.i(85689),d=a.i(57167),e=a.i(22171);let f=(0,d.cva)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function g({className:a,variant:d,asChild:g=!1,...h}){let i=g?c.Slot:"span";return(0,b.jsx)(i,{"data-slot":"badge",className:(0,e.cn)(f({variant:d}),a),...h})}},6601,a=>{"use strict";a.s(["Trash2",()=>b],6601);let b=(0,a.i(621).default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},20283,a=>{"use strict";a.s(["Table",()=>d,"TableBody",()=>f,"TableCell",()=>i,"TableHead",()=>h,"TableHeader",()=>e,"TableRow",()=>g]);var b=a.i(68116),c=a.i(22171);function d({className:a,...d}){return(0,b.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,b.jsx)("table",{"data-slot":"table",className:(0,c.cn)("w-full caption-bottom text-sm",a),...d})})}function e({className:a,...d}){return(0,b.jsx)("thead",{"data-slot":"table-header",className:(0,c.cn)("[&_tr]:border-b",a),...d})}function f({className:a,...d}){return(0,b.jsx)("tbody",{"data-slot":"table-body",className:(0,c.cn)("[&_tr:last-child]:border-0",a),...d})}function g({className:a,...d}){return(0,b.jsx)("tr",{"data-slot":"table-row",className:(0,c.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...d})}function h({className:a,...d}){return(0,b.jsx)("th",{"data-slot":"table-head",className:(0,c.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...d})}function i({className:a,...d}){return(0,b.jsx)("td",{"data-slot":"table-cell",className:(0,c.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...d})}},95163,a=>{"use strict";a.s(["default",()=>z],95163);var b=a.i(68116),c=a.i(128),d=a.i(81223),e=a.i(91486),f=a.i(23814),g=a.i(78928),h=a.i(51190),i=a.i(6601),j=a.i(72376),k=a.i(62303),l=a.i(621);let m=(0,l.default)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),n=(0,l.default)("building",[["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M9 22v-3a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3",key:"cabbwy"}],["rect",{x:"4",y:"2",width:"16",height:"20",rx:"2",key:"1uxh74"}]]);var o=a.i(3666),p=a.i(33055),q=a.i(2979),r=a.i(78184),s=a.i(75780),t=a.i(20283),u=a.i(51075),v=a.i(44932),w=a.i(93470),x=a.i(12594),y=a.i(78655);function z(){let{data:a}=(0,d.useSession)(),l=(0,x.useApi)(),[z,A]=(0,c.useState)([]),[B,C]=(0,c.useState)(null),[D,E]=(0,c.useState)(!0),[F,G]=(0,c.useState)(""),[H,I]=(0,c.useState)("all"),J=a?.user&&("secretary_general"===a.user.role||"controller"===a.user.role||"cashier"===a.user.role),K=a?.user&&"secretary_general"===a.user.role,L=a?.user&&"cashier"===a.user.role,M=async()=>{try{E(!0);let a=await l.getCaisses();A(a);let b=a.filter(a=>a.type===y.CaisseType.PRINCIPALE),c=a.filter(a=>a.type===y.CaisseType.REUNION),d=a.reduce((a,b)=>a+b.soldeActuel,0),e=b.reduce((a,b)=>a+b.soldeActuel,0),f=c.reduce((a,b)=>a+b.soldeActuel,0);C({total:a.length,principales:b.length,reunions:c.length,soldeTotal:d,soldePrincipales:e,soldeReunions:f})}catch(a){console.error("Erreur lors du chargement des caisses:",a)}finally{E(!1)}};(0,c.useEffect)(()=>{a?.accessToken&&M()},[a]);let N=async a=>{if(confirm("Êtes-vous sûr de vouloir supprimer cette caisse ?"))try{await l.deleteCaisse(a),M()}catch(a){console.error("Erreur lors de la suppression:",a)}},O=async a=>{if(confirm("Êtes-vous sûr de vouloir émarger cette caisse ? Le solde sera transféré vers la caisse principale."))try{await l.emargerCaisse(a),M()}catch(a){console.error("Erreur lors de l'émargement:",a)}},P=z.filter(a=>{let b=a.nom.toLowerCase().includes(F.toLowerCase())||a.soldeActuel.toString().includes(F),c="all"===H||a.type===H;return b&&c});return D?(0,b.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"}),(0,b.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Chargement des caisses..."})]})}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Caisses"}),(0,b.jsx)("p",{className:"text-muted-foreground",children:"Gérez les caisses principales et de réunion"})]}),K&&(0,b.jsx)(q.Button,{asChild:!0,children:(0,b.jsxs)(p.default,{href:"/dashboard/caisses/new",children:[(0,b.jsx)(e.Plus,{className:"mr-2 h-4 w-4"}),"Nouvelle caisse"]})})]}),B&&(0,b.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,b.jsxs)(s.Card,{children:[(0,b.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Total Caisses"}),(0,b.jsx)(j.Wallet,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsxs)(s.CardContent,{children:[(0,b.jsx)("div",{className:"text-2xl font-bold",children:B.total}),(0,b.jsxs)("p",{className:"text-xs text-muted-foreground",children:[B.principales," principales, ",B.reunions," réunions"]})]})]}),(0,b.jsxs)(s.Card,{children:[(0,b.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Solde Total"}),(0,b.jsx)(k.DollarSign,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsx)(s.CardContent,{children:(0,b.jsxs)("div",{className:"text-2xl font-bold",children:[B.soldeTotal.toLocaleString()," FCFA"]})})]}),(0,b.jsxs)(s.Card,{children:[(0,b.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Caisses Principales"}),(0,b.jsx)(n,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsxs)(s.CardContent,{children:[(0,b.jsxs)("div",{className:"text-2xl font-bold",children:[B.soldePrincipales.toLocaleString()," FCFA"]}),(0,b.jsxs)("p",{className:"text-xs text-muted-foreground",children:[B.principales," caisses"]})]})]}),(0,b.jsxs)(s.Card,{children:[(0,b.jsxs)(s.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(s.CardTitle,{className:"text-sm font-medium",children:"Caisses Réunions"}),(0,b.jsx)(o.Users,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsxs)(s.CardContent,{children:[(0,b.jsxs)("div",{className:"text-2xl font-bold",children:[B.soldeReunions.toLocaleString()," FCFA"]}),(0,b.jsxs)("p",{className:"text-xs text-muted-foreground",children:[B.reunions," caisses"]})]})]})]}),(0,b.jsxs)(s.Card,{children:[(0,b.jsxs)(s.CardHeader,{children:[(0,b.jsx)(s.CardTitle,{children:"Filtres"}),(0,b.jsx)(s.CardDescription,{children:"Recherchez et filtrez les caisses"})]}),(0,b.jsx)(s.CardContent,{children:(0,b.jsxs)("div",{className:"flex gap-4",children:[(0,b.jsx)("div",{className:"flex-1",children:(0,b.jsxs)("div",{className:"relative",children:[(0,b.jsx)(f.Search,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,b.jsx)(r.Input,{placeholder:"Rechercher par nom ou solde...",value:F,onChange:a=>G(a.target.value),className:"pl-8"})]})}),(0,b.jsxs)(v.Select,{value:H,onValueChange:I,children:[(0,b.jsx)(v.SelectTrigger,{className:"w-[180px]",children:(0,b.jsx)(v.SelectValue,{placeholder:"Filtrer par type"})}),(0,b.jsxs)(v.SelectContent,{children:[(0,b.jsx)(v.SelectItem,{value:"all",children:"Tous les types"}),(0,b.jsx)(v.SelectItem,{value:y.CaisseType.PRINCIPALE,children:"Principales"}),(0,b.jsx)(v.SelectItem,{value:y.CaisseType.REUNION,children:"Réunions"})]})]})]})})]}),(0,b.jsxs)(s.Card,{children:[(0,b.jsx)(s.CardHeader,{children:(0,b.jsxs)(s.CardTitle,{children:["Caisses (",P.length,")"]})}),(0,b.jsxs)(s.CardContent,{children:[(0,b.jsxs)(t.Table,{children:[(0,b.jsx)(t.TableHeader,{children:(0,b.jsxs)(t.TableRow,{children:[(0,b.jsx)(t.TableHead,{children:"Nom"}),(0,b.jsx)(t.TableHead,{children:"Type"}),(0,b.jsx)(t.TableHead,{children:"Solde Actuel"}),(0,b.jsx)(t.TableHead,{children:"Créée le"}),J&&(0,b.jsx)(t.TableHead,{children:"Actions"})]})}),(0,b.jsx)(t.TableBody,{children:P.map(a=>(0,b.jsxs)(t.TableRow,{children:[(0,b.jsx)(t.TableCell,{className:"font-medium",children:a.nom}),(0,b.jsx)(t.TableCell,{children:(0,b.jsx)(w.Badge,{variant:(a=>{switch(a){case y.CaisseType.PRINCIPALE:return"default";case y.CaisseType.REUNION:return"secondary";default:return"outline"}})(a.type),children:(a=>{switch(a){case y.CaisseType.PRINCIPALE:return"Principale";case y.CaisseType.REUNION:return"Réunion";default:return a}})(a.type)})}),(0,b.jsx)(t.TableCell,{children:(0,b.jsxs)("span",{className:a.soldeActuel>0?"text-green-600 font-medium":"text-gray-500",children:[a.soldeActuel.toLocaleString()," FCFA"]})}),(0,b.jsx)(t.TableCell,{children:new Date(a.createdAt).toLocaleDateString()}),J&&(0,b.jsx)(t.TableCell,{children:(0,b.jsxs)(u.DropdownMenu,{children:[(0,b.jsx)(u.DropdownMenuTrigger,{asChild:!0,children:(0,b.jsx)(q.Button,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,b.jsx)(g.MoreHorizontal,{className:"h-4 w-4"})})}),(0,b.jsxs)(u.DropdownMenuContent,{align:"end",children:[(0,b.jsx)(u.DropdownMenuItem,{asChild:!0,children:(0,b.jsxs)(p.default,{href:`/dashboard/caisses/${a._id}/edit`,children:[(0,b.jsx)(h.Edit,{className:"mr-2 h-4 w-4"}),"Modifier"]})}),L&&a.type===y.CaisseType.REUNION&&a.soldeActuel>0&&(0,b.jsxs)(u.DropdownMenuItem,{onClick:()=>O(a._id),children:[(0,b.jsx)(m,{className:"mr-2 h-4 w-4"}),"Émarger"]}),K&&(0,b.jsxs)(u.DropdownMenuItem,{onClick:()=>N(a._id),className:"text-red-600",children:[(0,b.jsx)(i.Trash2,{className:"mr-2 h-4 w-4"}),"Supprimer"]})]})]})})]},a._id))})]}),0===P.length&&(0,b.jsx)("div",{className:"text-center py-8",children:(0,b.jsx)("p",{className:"text-muted-foreground",children:"Aucune caisse trouvée"})})]})]})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__35aa91f4._.js.map