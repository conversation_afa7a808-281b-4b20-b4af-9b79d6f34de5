module.exports=[24725,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},12594,a=>{"use strict";a.s(["useApi",()=>d]);var b=a.i(81223),c=a.i(34075);function d(){let{data:a}=(0,b.useSession)(),d=async(b,d={})=>{if(!a?.accessToken)throw Error("Non authentifié");return c.apiService.authenticatedRequest(b,a.accessToken,d)};return{login:c.apiService.login.bind(c.apiService),register:c.apiService.register.bind(c.apiService),authenticatedRequest:d,getUsers:()=>d("/users"),getUser:a=>d(`/users/${a}`),createUser:a=>d("/users",{method:"POST",body:JSON.stringify(a)}),updateUser:(a,b)=>d(`/users/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteUser:a=>d(`/users/${a}`,{method:"DELETE"}),getSessions:()=>d("/sessions"),getSession:a=>d(`/sessions/${a}`),createSession:a=>d("/sessions",{method:"POST",body:JSON.stringify(a)}),updateSession:(a,b)=>d(`/sessions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteSession:a=>d(`/sessions/${a}`,{method:"DELETE"}),getCaisses:()=>d("/caisses"),getCaisse:a=>d(`/caisses/${a}`),createCaisse:a=>d("/caisses",{method:"POST",body:JSON.stringify(a)}),updateCaisse:(a,b)=>d(`/caisses/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteCaisse:a=>d(`/caisses/${a}`,{method:"DELETE"}),emargerCaisse:a=>d(`/caisses/${a}/emarger`,{method:"POST"}),getReunions:()=>d("/reunions"),getReunion:a=>d(`/reunions/${a}`),updateReunion:(a,b)=>d(`/reunions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),getMembers:()=>d("/members"),getMember:a=>d(`/members/${a}`),createMember:a=>d("/members",{method:"POST",body:JSON.stringify(a)}),updateMember:(a,b)=>d(`/members/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteMember:a=>d(`/members/${a}`,{method:"DELETE"}),getMemberDebrief:(a,b)=>{let c=new URLSearchParams;b?.dateFrom&&c.append("dateFrom",b.dateFrom),b?.dateTo&&c.append("dateTo",b.dateTo),b?.sessionId&&c.append("sessionId",b.sessionId);let e=c.toString()?`?${c.toString()}`:"";return d(`/members/${a}/debrief${e}`)},createPayment:a=>d("/payments",{method:"POST",body:JSON.stringify(a)}),getSessionMembers:a=>d(`/sessions/${a}/members`),addSessionMember:a=>d("/session-members",{method:"POST",body:JSON.stringify(a)}),removeSessionMember:(a,b)=>d(`/sessions/${a}/members/${b}`,{method:"DELETE"})}}},78655,a=>{"use strict";a.s(["CaisseType",()=>b,"PaymentDirection",()=>d,"PaymentFunction",()=>e,"UserRole",()=>c]);var b=function(a){return a.PRINCIPALE="PRINCIPALE",a.REUNION="REUNION",a}({}),c=function(a){return a.SECRETARY_GENERAL="secretary_general",a.CONTROLLER="controller",a.CASHIER="cashier",a}({}),d=function(a){return a.IN="IN",a.OUT="OUT",a}({}),e=function(a){return a.CONTRIBUTION="cotisation",a.TRANSFER="transfert",a.EXTERNAL="exterieur",a}({})},91486,a=>{"use strict";a.s(["Plus",()=>b],91486);let b=(0,a.i(621).default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},93470,a=>{"use strict";a.s(["Badge",()=>g]);var b=a.i(68116),c=a.i(85689),d=a.i(57167),e=a.i(22171);let f=(0,d.cva)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function g({className:a,variant:d,asChild:g=!1,...h}){let i=g?c.Slot:"span";return(0,b.jsx)(i,{"data-slot":"badge",className:(0,e.cn)(f({variant:d}),a),...h})}},6601,a=>{"use strict";a.s(["Trash2",()=>b],6601);let b=(0,a.i(621).default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},20283,a=>{"use strict";a.s(["Table",()=>d,"TableBody",()=>f,"TableCell",()=>i,"TableHead",()=>h,"TableHeader",()=>e,"TableRow",()=>g]);var b=a.i(68116),c=a.i(22171);function d({className:a,...d}){return(0,b.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,b.jsx)("table",{"data-slot":"table",className:(0,c.cn)("w-full caption-bottom text-sm",a),...d})})}function e({className:a,...d}){return(0,b.jsx)("thead",{"data-slot":"table-header",className:(0,c.cn)("[&_tr]:border-b",a),...d})}function f({className:a,...d}){return(0,b.jsx)("tbody",{"data-slot":"table-body",className:(0,c.cn)("[&_tr:last-child]:border-0",a),...d})}function g({className:a,...d}){return(0,b.jsx)("tr",{"data-slot":"table-row",className:(0,c.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...d})}function h({className:a,...d}){return(0,b.jsx)("th",{"data-slot":"table-head",className:(0,c.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...d})}function i({className:a,...d}){return(0,b.jsx)("td",{"data-slot":"table-cell",className:(0,c.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...d})}},76052,a=>{"use strict";a.s(["default",()=>p]);var b=a.i(68116),c=a.i(128),d=a.i(81223),e=a.i(91486),f=a.i(23814),g=a.i(6601),h=a.i(33055),i=a.i(2979),j=a.i(78184),k=a.i(75780),l=a.i(20283),m=a.i(93470),n=a.i(12594),o=a.i(78655);function p(){let{data:a}=(0,d.useSession)(),p=(0,n.useApi)(),[q,r]=(0,c.useState)([]),[s,t]=(0,c.useState)(!0),[u,v]=(0,c.useState)(""),w=a?.user&&a.user.role===o.UserRole.SECRETARY_GENERAL;(0,c.useEffect)(()=>{a?.accessToken&&x()},[a]);let x=async()=>{try{t(!0);let a=await p.getUsers();r(a)}catch(a){console.error("Erreur lors du chargement des utilisateurs:",a)}finally{t(!1)}},y=async a=>{if(confirm("Êtes-vous sûr de vouloir supprimer cet utilisateur ?"))try{await p.deleteUser(a),await x()}catch(a){console.error("Erreur lors de la suppression:",a)}},z=q.filter(a=>a.username.toLowerCase().includes(u.toLowerCase()));return w?s?(0,b.jsx)("div",{className:"space-y-6",children:(0,b.jsx)("div",{className:"flex justify-center py-8",children:(0,b.jsx)("div",{className:"text-gray-500",children:"Chargement..."})})}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Utilisateurs"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Gestion des comptes d'accès à l'application"})]}),(0,b.jsx)(h.default,{href:"/auth/register",children:(0,b.jsxs)(i.Button,{children:[(0,b.jsx)(e.Plus,{className:"h-4 w-4 mr-2"}),"Nouvel utilisateur"]})})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{className:"pb-2",children:(0,b.jsx)(k.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Total utilisateurs"})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold",children:q.length})})]}),(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{className:"pb-2",children:(0,b.jsx)(k.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Secrétaires Généraux"})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:q.filter(a=>a.role===o.UserRole.SECRETARY_GENERAL).length})})]}),(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{className:"pb-2",children:(0,b.jsx)(k.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Contrôleurs"})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:q.filter(a=>a.role===o.UserRole.CONTROLLER).length})})]}),(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{className:"pb-2",children:(0,b.jsx)(k.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Caissiers"})}),(0,b.jsx)(k.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-green-600",children:q.filter(a=>a.role===o.UserRole.CASHIER).length})})]})]}),(0,b.jsxs)(k.Card,{children:[(0,b.jsx)(k.CardHeader,{children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(k.CardTitle,{children:"Liste des utilisateurs"}),(0,b.jsxs)(k.CardDescription,{children:[z.length," utilisateur(s)"]})]}),(0,b.jsx)("div",{className:"flex items-center gap-4",children:(0,b.jsxs)("div",{className:"relative",children:[(0,b.jsx)(f.Search,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,b.jsx)(j.Input,{placeholder:"Rechercher un utilisateur...",value:u,onChange:a=>v(a.target.value),className:"pl-10 w-64"})]})})]})}),(0,b.jsx)(k.CardContent,{children:z.length>0?(0,b.jsxs)(l.Table,{children:[(0,b.jsx)(l.TableHeader,{children:(0,b.jsxs)(l.TableRow,{children:[(0,b.jsx)(l.TableHead,{children:"Nom d'utilisateur"}),(0,b.jsx)(l.TableHead,{children:"Rôle"}),(0,b.jsx)(l.TableHead,{children:"Créé le"}),(0,b.jsx)(l.TableHead,{children:"Actions"})]})}),(0,b.jsx)(l.TableBody,{children:z.map(a=>(0,b.jsxs)(l.TableRow,{children:[(0,b.jsx)(l.TableCell,{children:(0,b.jsx)("div",{className:"font-medium",children:a.username})}),(0,b.jsx)(l.TableCell,{children:(0,b.jsx)(m.Badge,{className:(a=>{switch(a){case o.UserRole.SECRETARY_GENERAL:return"bg-purple-100 text-purple-800";case o.UserRole.CONTROLLER:return"bg-blue-100 text-blue-800";case o.UserRole.CASHIER:return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}})(a.role),children:(a=>{switch(a){case o.UserRole.SECRETARY_GENERAL:return"Secrétaire Général";case o.UserRole.CONTROLLER:return"Contrôleur";case o.UserRole.CASHIER:return"Caissier";default:return a}})(a.role)})}),(0,b.jsx)(l.TableCell,{className:"text-gray-600",children:new Date(a.createdAt).toLocaleDateString("fr-FR",{year:"numeric",month:"short",day:"numeric"})}),(0,b.jsx)(l.TableCell,{children:(0,b.jsx)("div",{className:"flex items-center gap-2",children:(0,b.jsx)(i.Button,{variant:"ghost",size:"sm",onClick:()=>y(a._id),className:"text-red-600 hover:text-red-700",children:(0,b.jsx)(g.Trash2,{className:"h-4 w-4"})})})})]},a._id))})]}):(0,b.jsx)("div",{className:"text-center py-8 text-gray-500",children:u?"Aucun utilisateur trouvé":"Aucun utilisateur"})})]})]}):(0,b.jsx)("div",{className:"space-y-6",children:(0,b.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Accès refusé"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Seul le Secrétaire Général peut accéder à cette page."})]})})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__8e0891af._.js.map