{"version": 3, "sources": ["turbopack:///[project]/frontend/src/components/layout/header.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[project]/frontend/src/components/layout/sidebar.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[project]/frontend/src/components/ui/sidebar.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[project]/frontend/src/app/dashboard/layout.tsx"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/layout/header.tsx\",\n    \"Header\",\n);\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppSidebar() from the server but AppSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/layout/sidebar.tsx\",\n    \"AppSidebar\",\n);\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Sidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"Sidebar\",\n);\nexport const SidebarContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarContent\",\n);\nexport const SidebarFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarFooter() from the server but <PERSON><PERSON><PERSON>oot<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarFooter\",\n);\nexport const SidebarGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarGroup\",\n);\nexport const SidebarGroupAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarGroupAction\",\n);\nexport const SidebarGroupContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarGroupContent\",\n);\nexport const SidebarGroupLabel = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarGroupLabel\",\n);\nexport const SidebarHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarHeader\",\n);\nexport const SidebarInput = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarInput\",\n);\nexport const SidebarInset = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarInset\",\n);\nexport const SidebarMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarMenu\",\n);\nexport const SidebarMenuAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuAction\",\n);\nexport const SidebarMenuBadge = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuBadge\",\n);\nexport const SidebarMenuButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuButton\",\n);\nexport const SidebarMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuItem\",\n);\nexport const SidebarMenuSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuSkeleton\",\n);\nexport const SidebarMenuSub = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuSub\",\n);\nexport const SidebarMenuSubButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuSubButton\",\n);\nexport const SidebarMenuSubItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuSubItem\",\n);\nexport const SidebarProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarProvider\",\n);\nexport const SidebarRail = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarRail\",\n);\nexport const SidebarSeparator = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarSeparator\",\n);\nexport const SidebarTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"SidebarTrigger\",\n);\nexport const useSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/ui/sidebar.tsx\",\n    \"useSidebar\",\n);\n", "import { Header } from \"@/components/layout/header\";\nimport { AppSidebar } from \"@/components/layout/sidebar\";\nimport { SidebarProvider, SidebarInset } from \"@/components/ui/sidebar\";\n// import { cookies } from \"next/headers\";\n\nexport default function DashboardLayout({\n\tchildren,\n}: {\n\tchildren: React.ReactNode;\n}) {\n\t// const cookieStore = await cookies();\n\t// const defaultOpen = cookieStore.get(\"sidebar_state\")?.value === \"true\";\n\treturn (\n\t\t<SidebarProvider defaultOpen={true}>\n\t\t\t<AppSidebar />\n\t\t\t<SidebarInset>\n\t\t\t\t<Header />\n\t\t\t\t<main className=\"flex-1 p-6\">{children}</main>\n\t\t\t</SidebarInset>\n\t\t</SidebarProvider>\n\t);\n}\n"], "names": [], "mappings": "6DAEO,IAAM,EAAS,CAAA,EAAA,AADtB,EAAA,CAAA,CAAA,OACsB,uBAAA,AAAuB,EACzC,WAAa,MAAM,AAAI,MAAM,0NAA4N,EACzP,0EACA,wDAHG,IAAM,EAAS,CAAA,EADtB,AACsB,EADtB,CAAA,CAAA,OACsB,uBAAA,AAAuB,EACzC,WAAa,MAAM,AAAI,MAAM,0NAA4N,EACzP,sDACA,uHCHG,IAAM,EAAa,CAAA,EAD1B,AAC0B,EAD1B,CAAA,CAAA,OAC0B,uBAAA,AAAuB,EAC7C,WAAa,MAAU,AAAJ,MAAU,kOAAoO,EACjQ,2EACA,gEAHG,IAAM,EAAa,CAAA,EAD1B,AAC0B,EAD1B,CAAA,CAAA,OAC0B,uBAAA,AAAuB,EAC7C,WAAa,MAAM,AAAI,MAAM,kOAAoO,EACjQ,uDACA,8pBCJJ,IAAA,EAAA,EAAA,CAAA,CAAA,OACO,IAAM,EAAU,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC1C,WAAa,MAAM,AAAI,MAAM,4NAA8N,EAC3P,uEACA,WAES,EAAiB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACjD,WAAa,MAAM,AAAI,MAAM,0OAA4O,EACzQ,uEACA,kBAES,EAAgB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAChD,WAAa,MAAM,AAAI,MAAM,wOAA0O,EACvQ,uEACA,iBAES,EAAe,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC/C,WAAa,MAAM,AAAI,MAAM,sOAAwO,EACrQ,uEACA,gBAES,EAAqB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACrD,WAAa,MAAU,AAAJ,MAAU,kPAAoP,EACjR,uEACA,sBAES,EAAsB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACtD,WAAa,MAAM,AAAI,MAAM,oPAAsP,EACnR,uEACA,uBAES,EAAoB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACpD,WAAa,MAAM,AAAI,MAAM,gPAAkP,EAC/Q,uEACA,qBAES,EAAgB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAChD,WAAa,MAAM,AAAI,MAAM,wOAA0O,EACvQ,uEACA,iBAES,EAAe,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC/C,WAAa,MAAM,AAAI,MAAM,sOAAwO,EACrQ,uEACA,gBAES,EAAe,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC/C,WAAa,MAAM,AAAI,MAAM,sOAAwO,EACrQ,uEACA,gBAES,EAAc,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC9C,WAAa,MAAM,AAAI,MAAM,oOAAsO,EACnQ,uEACA,eAES,EAAoB,CAAA,EAAA,EAAA,uBAAuB,AAAvB,EAC7B,WAAa,MAAM,AAAI,MAAM,gPAAkP,EAC/Q,uEACA,qBAES,EAAmB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACnD,WAAa,MAAM,AAAI,MAAM,8OAAgP,EAC7Q,uEACA,oBAES,EAAoB,CAAA,EAAA,EAAA,uBAAuB,AAAvB,EAC7B,WAAa,MAAM,AAAI,MAAM,gPAAkP,EAC/Q,uEACA,qBAES,EAAkB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAClD,WAAa,MAAU,AAAJ,MAAU,4OAA8O,EAC3Q,uEACA,mBAES,EAAsB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACtD,WAAa,MAAM,AAAI,MAAM,oPAAsP,EACnR,uEACA,uBAES,EAAiB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACjD,WAAa,MAAM,AAAI,MAAM,0OAA4O,EACzQ,uEACA,kBAES,EAAuB,CAAA,EAAA,EAAA,uBAAuB,AAAvB,EAChC,WAAa,MAAM,AAAI,MAAM,sPAAwP,EACrR,uEACA,wBAES,EAAqB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACrD,WAAa,MAAM,AAAI,MAAM,kPAAoP,EACjR,uEACA,sBAES,EAAkB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAClD,WAAa,MAAM,AAAI,MAAM,4OAA8O,EAC3Q,uEACA,mBAES,EAAc,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC9C,WAAa,MAAM,AAAI,MAAM,oOAAsO,EACnQ,uEACA,eAES,EAAmB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACnD,WAAa,MAAM,AAAI,MAAM,8OAAgP,EAC7Q,uEACA,oBAES,EAAiB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACjD,WAAa,MAAM,AAAI,MAAM,0OAA4O,EACzQ,uEACA,kBAES,EAAa,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC7C,WAAa,MAAM,AAAI,MAAM,kOAAoO,EACjQ,uEACA,kmBAvHJ,IAAA,EAAA,EAAA,CAAA,CAAA,OACO,IAAM,EAAU,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC1C,WAAa,MAAM,AAAI,MAAM,4NAA8N,EAC3P,mDACA,WAES,EAAiB,CAAA,EAAA,EAAA,uBAAuB,AAAvB,EAC1B,WAAa,MAAM,AAAI,MAAM,0OAA4O,EACzQ,mDACA,kBAES,EAAgB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAChD,WAAa,MAAM,AAAI,MAAM,wOAA0O,EACvQ,mDACA,iBAES,EAAe,CAAA,EAAA,EAAA,uBAAuB,AAAvB,EACxB,WAAa,MAAM,AAAI,MAAM,sOAAwO,EACrQ,mDACA,gBAES,EAAqB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACrD,WAAa,MAAM,AAAI,MAAM,kPAAoP,EACjR,mDACA,sBAES,EAAsB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACtD,WAAa,MAAM,AAAI,MAAM,oPAAsP,EACnR,mDACA,uBAES,EAAoB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACpD,WAAa,MAAM,AAAI,MAAM,gPAAkP,EAC/Q,mDACA,qBAES,EAAgB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAChD,WAAa,MAAM,AAAI,MAAM,wOAA0O,EACvQ,mDACA,iBAES,EAAe,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC/C,WAAa,MAAM,AAAI,MAAM,sOAAwO,EACrQ,mDACA,gBAES,EAAe,CAAA,EAAA,EAAA,uBAAuB,AAAvB,EACxB,WAAa,MAAM,AAAI,MAAM,sOAAwO,EACrQ,mDACA,gBAES,EAAc,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC9C,WAAa,MAAM,AAAI,MAAM,oOAAsO,EACnQ,mDACA,eAES,EAAoB,CAAA,EAAA,EAAA,uBAAuB,AAAvB,EAC7B,WAAa,MAAM,AAAI,MAAM,gPAAkP,EAC/Q,mDACA,qBAES,EAAmB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACnD,WAAa,MAAM,AAAI,MAAM,8OAAgP,EAC7Q,mDACA,oBAES,EAAoB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACpD,WAAa,MAAM,AAAI,MAAM,gPAAkP,EAC/Q,mDACA,qBAES,EAAkB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAClD,WAAa,MAAU,AAAJ,MAAU,4OAA8O,EAC3Q,mDACA,mBAES,EAAsB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACtD,WAAa,MAAM,AAAI,MAAM,oPAAsP,EACnR,mDACA,uBAES,EAAiB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACjD,WAAa,MAAM,AAAI,MAAM,0OAA4O,EACzQ,mDACA,kBAES,EAAuB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACvD,WAAa,MAAM,AAAI,MAAM,sPAAwP,EACrR,mDACA,wBAES,EAAqB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACrD,WAAa,MAAM,AAAI,MAAM,kPAAoP,EACjR,mDACA,sBAES,EAAkB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAClD,WAAa,MAAM,AAAI,MAAM,4OAA8O,EAC3Q,mDACA,mBAES,EAAc,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC9C,WAAa,MAAM,AAAI,MAAM,oOAAsO,EACnQ,mDACA,eAES,EAAmB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACnD,WAAa,MAAM,AAAI,MAAM,8OAAgP,EAC7Q,mDACA,oBAES,EAAiB,CAAA,EAAA,EAAA,uBAAA,AAAuB,EACjD,WAAa,MAAM,AAAI,MAAM,0OAA4O,EACzQ,mDACA,kBAES,EAAa,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC7C,WAAa,MAAM,AAAI,MAAM,kOAAoO,EACjQ,mDACA,wICxHJ,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAGe,SAAS,EAAgB,CACvC,UAAQ,CAGR,EAGA,MACC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,eAAe,CAAA,CAAC,aAAa,YAC7B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,CAAA,GACX,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,YAAY,CAAA,WACZ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAA,GACP,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,sBAAc,SAIlC", "ignoreList": [0, 1, 2]}