(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,44640,(e,s,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,s,t){let r=Reflect.get(e,s,t);return"function"==typeof r?r.bind(e):r}static set(e,s,t,r){return Reflect.set(e,s,t,r)}static has(e,s){return Reflect.has(e,s)}static deleteProperty(e,s){return Reflect.deleteProperty(e,s)}}},8356,(e,s,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"afterTaskAsyncStorageInstance",{enumerable:!0,get:function(){return r}});let r=(0,e.r(97918).createAsyncLocalStorage)()},62355,(e,s,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"afterTaskAsyncStorage",{enumerable:!0,get:function(){return r.afterTaskAsyncStorageInstance}});let r=e.r(8356)},17939,(e,s,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(t,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return o},throwWithStaticGenerationBailoutError:function(){return i},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return a}});let r=e.r(85115),n=e.r(62355);function i(e,s){throw Object.defineProperty(new r.StaticGenBailoutError("Route ".concat(e," couldn't be rendered statically because it used ").concat(s,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function a(e,s){throw Object.defineProperty(new r.StaticGenBailoutError("Route ".concat(e,' with `dynamic = "error"` couldn\'t be rendered statically because it used ').concat(s,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function o(e,s){let t=Object.defineProperty(Error("Route ".concat(e.route,' used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(t,s),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=t),t}function c(){let e=n.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},93168,(e,s,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(t,{describeHasCheckingStringProperty:function(){return i},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return a}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,s){return r.test(s)?"`"+e+"."+s+"`":"`"+e+"["+JSON.stringify(s)+"]`"}function i(e,s){let t=JSON.stringify(s);return"`Reflect.has("+e+", "+t+")`, `"+t+" in "+e+"`, or similar"}let a=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},63470,(e,s,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let r=function(e,s){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(s);if(t&&t.has(e))return t.get(e);var r={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(r,a,o):r[a]=e[a]}return r.default=e,t&&t.set(e,r),r}(e.r(38477));function n(e){if("function"!=typeof WeakMap)return null;var s=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:s})(e)}let i={current:null},a="function"==typeof r.cache?r.cache:e=>e,o=console.warn;function c(e){return function(){for(var s=arguments.length,t=Array(s),r=0;r<s;r++)t[r]=arguments[r];o(e(...t))}}a(e=>{try{o(i.current)}finally{i.current=null}})},4467,e=>{"use strict";e.s(["useApi",()=>r]);var s=e.i(1269),t=e.i(1831);function r(){let{data:e}=(0,s.useSession)(),r=async function(s){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(null==e?void 0:e.accessToken))throw Error("Non authentifié");return t.apiService.authenticatedRequest(s,e.accessToken,r)};return{login:t.apiService.login.bind(t.apiService),register:t.apiService.register.bind(t.apiService),authenticatedRequest:r,getUsers:()=>r("/users"),getUser:e=>r("/users/".concat(e)),createUser:e=>r("/users",{method:"POST",body:JSON.stringify(e)}),updateUser:(e,s)=>r("/users/".concat(e),{method:"PATCH",body:JSON.stringify(s)}),deleteUser:e=>r("/users/".concat(e),{method:"DELETE"}),getSessions:()=>r("/sessions"),getSession:e=>r("/sessions/".concat(e)),createSession:e=>r("/sessions",{method:"POST",body:JSON.stringify(e)}),updateSession:(e,s)=>r("/sessions/".concat(e),{method:"PATCH",body:JSON.stringify(s)}),deleteSession:e=>r("/sessions/".concat(e),{method:"DELETE"}),getCaisses:()=>r("/caisses"),getCaisse:e=>r("/caisses/".concat(e)),createCaisse:e=>r("/caisses",{method:"POST",body:JSON.stringify(e)}),updateCaisse:(e,s)=>r("/caisses/".concat(e),{method:"PATCH",body:JSON.stringify(s)}),deleteCaisse:e=>r("/caisses/".concat(e),{method:"DELETE"}),emargerCaisse:e=>r("/caisses/".concat(e,"/emarger"),{method:"POST"}),getReunions:()=>r("/reunions"),getReunion:e=>r("/reunions/".concat(e)),updateReunion:(e,s)=>r("/reunions/".concat(e),{method:"PATCH",body:JSON.stringify(s)}),getMembers:()=>r("/members"),getMember:e=>r("/members/".concat(e)),createMember:e=>r("/members",{method:"POST",body:JSON.stringify(e)}),updateMember:(e,s)=>r("/members/".concat(e),{method:"PATCH",body:JSON.stringify(s)}),deleteMember:e=>r("/members/".concat(e),{method:"DELETE"}),getMemberDebrief:(e,s)=>{let t=new URLSearchParams;(null==s?void 0:s.dateFrom)&&t.append("dateFrom",s.dateFrom),(null==s?void 0:s.dateTo)&&t.append("dateTo",s.dateTo),(null==s?void 0:s.sessionId)&&t.append("sessionId",s.sessionId);let n=t.toString()?"?".concat(t.toString()):"";return r("/members/".concat(e,"/debrief").concat(n))},createPayment:e=>r("/payments",{method:"POST",body:JSON.stringify(e)}),getSessionMembers:e=>r("/sessions/".concat(e,"/members")),addSessionMember:e=>r("/session-members",{method:"POST",body:JSON.stringify(e)}),removeSessionMember:(e,s)=>r("/sessions/".concat(e,"/members/").concat(s),{method:"DELETE"})}}},12058,e=>{"use strict";e.s(["CaisseType",()=>s,"PaymentDirection",()=>r,"PaymentFunction",()=>n,"UserRole",()=>t]);var s=function(e){return e.PRINCIPALE="PRINCIPALE",e.REUNION="REUNION",e}({}),t=function(e){return e.SECRETARY_GENERAL="secretary_general",e.CONTROLLER="controller",e.CASHIER="cashier",e}({}),r=function(e){return e.IN="IN",e.OUT="OUT",e}({}),n=function(e){return e.CONTRIBUTION="cotisation",e.TRANSFER="transfert",e.EXTERNAL="exterieur",e}({})},57223,e=>{"use strict";e.s(["DollarSign",()=>s],57223);let s=(0,e.i(44571).default)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},27973,e=>{"use strict";e.s(["Trash2",()=>s],27973);let s=(0,e.i(44571).default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},65429,e=>{"use strict";e.s(["Label",()=>a],65429);var s=e.i(4051),t=e.i(38477),r=e.i(38909),n=t.forwardRef((e,t)=>(0,s.jsx)(r.Primitive.label,{...e,ref:t,onMouseDown:s=>{var t;s.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var i=e.i(41428);function a(e){let{className:t,...r}=e;return(0,s.jsx)(n,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},14545,e=>{"use strict";e.s(["ArrowLeft",()=>s],14545);let s=(0,e.i(44571).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},81149,e=>{"use strict";e.s(["default",()=>S]);var s=e.i(4051),t=e.i(38477),r=e.i(1269),n=e.i(57691),i=e.i(67967),a=e.i(78381),o=e.i(45086),c=e.i(14545),l=e.i(42633),d=e.i(57223),u=e.i(27973),m=e.i(5085),p=e.i(85205),h=e.i(96134),f=e.i(75680),y=e.i(5647),g=e.i(83194),x=e.i(4467),j=e.i(12058);let b=o.z.object({nom:o.z.string().min(1,"Le nom est requis").max(100,"Le nom est trop long"),type:o.z.nativeEnum(j.CaisseType,{required_error:"Le type est requis"}),soldeActuel:o.z.number().min(0,"Le solde ne peut pas être négatif").max(1e7,"Le solde ne peut pas dépasser 10,000,000 FCFA"),sessionId:o.z.string().optional(),caissePrincipaleId:o.z.string().optional()}).refine(e=>e.type!==j.CaisseType.REUNION||e.sessionId&&e.caissePrincipaleId,{message:"Pour une caisse de réunion, la session et la caisse principale sont requises",path:["sessionId"]});function S(){let{id:e}=(0,n.useParams)(),{data:o,status:S}=(0,r.useSession)(),v=(0,n.useRouter)(),C=(0,x.useApi)(),[N,P]=(0,t.useState)(null),[O,E]=(0,t.useState)(!1),[I,T]=(0,t.useState)(!1),[R,A]=(0,t.useState)(null),[F,w]=(0,t.useState)(!0),[_,M]=(0,t.useState)([]),[L,D]=(0,t.useState)([]),k=(null==o?void 0:o.user)&&"secretary_general"===o.user.role,U=(0,i.useForm)({resolver:(0,a.zodResolver)(b),defaultValues:{nom:"",type:j.CaisseType.PRINCIPALE,soldeActuel:0,sessionId:"",caissePrincipaleId:""}}),z=U.watch("type");(0,t.useEffect)(()=>{let s=async()=>{if(e&&"string"==typeof e)try{w(!0);let[s,t,r]=await Promise.all([C.getCaisse(e),C.getSessions(),C.getCaisses()]);P(s),M(t),D(r.filter(e=>e.type===j.CaisseType.PRINCIPALE&&e._id!==s._id)),U.reset({nom:s.nom,type:s.type,soldeActuel:s.soldeActuel,sessionId:s.sessionId||"",caissePrincipaleId:s.caissePrincipaleId||""})}catch(e){console.error("Erreur lors du chargement:",e),A("Caisse introuvable")}finally{w(!1)}};(null==o?void 0:o.accessToken)&&s()},[e,S]);let B=async s=>{if(!k||!e||"string"!=typeof e)return void A("Vous n'avez pas les permissions pour modifier cette caisse");try{E(!0),A(null);let t={nom:s.nom,type:s.type,soldeActuel:s.soldeActuel,...s.type===j.CaisseType.REUNION&&{sessionId:s.sessionId,caissePrincipaleId:s.caissePrincipaleId}};await C.updateCaisse(e,t),v.push("/dashboard/caisses")}catch(e){console.error("Erreur lors de la modification:",e),A(e.message||"Une erreur est survenue lors de la modification")}finally{E(!1)}},J=async()=>{if(k&&e&&"string"==typeof e&&N&&confirm('Êtes-vous sûr de vouloir supprimer la caisse "'.concat(N.nom,'" ?')))try{T(!0),await C.deleteCaisse(e),v.push("/dashboard/caisses")}catch(e){console.error("Erreur lors de la suppression:",e),A(e.message||"Une erreur est survenue lors de la suppression")}finally{T(!1)}};return F?(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Chargement de la caisse..."})]})}):k?N?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(p.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,s.jsx)(m.default,{href:"/dashboard/caisses",children:(0,s.jsx)(c.ArrowLeft,{className:"h-4 w-4"})})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold tracking-tight",children:["Modifier ",N.nom]}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Modifier les paramètres de la caisse"})]})]}),(0,s.jsxs)(p.Button,{variant:"destructive",onClick:J,disabled:I,children:[(0,s.jsx)(u.Trash2,{className:"mr-2 h-4 w-4"}),I?"Suppression...":"Supprimer"]})]}),(0,s.jsxs)(f.Card,{children:[(0,s.jsxs)(f.CardHeader,{children:[(0,s.jsxs)(f.CardTitle,{className:"flex items-center gap-2",children:[(0,s.jsx)(l.Wallet,{className:"h-5 w-5"}),"Informations de la caisse"]}),(0,s.jsx)(f.CardDescription,{children:"Modifiez les paramètres de la caisse"})]}),(0,s.jsx)(f.CardContent,{children:(0,s.jsx)(y.Form,{...U,children:(0,s.jsxs)("form",{onSubmit:U.handleSubmit(B),className:"space-y-6",children:[R&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,s.jsx)("p",{className:"text-sm text-red-600",children:R})}),(0,s.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,s.jsx)(y.FormField,{control:U.control,name:"nom",render:e=>{let{field:t}=e;return(0,s.jsxs)(y.FormItem,{children:[(0,s.jsx)(y.FormLabel,{children:"Nom de la caisse"}),(0,s.jsx)(y.FormControl,{children:(0,s.jsx)(h.Input,{placeholder:"Ex: Caisse Principale 2025",...t})}),(0,s.jsx)(y.FormDescription,{children:"Nom descriptif de la caisse"}),(0,s.jsx)(y.FormMessage,{})]})}}),(0,s.jsx)(y.FormField,{control:U.control,name:"type",render:e=>{let{field:t}=e;return(0,s.jsxs)(y.FormItem,{children:[(0,s.jsx)(y.FormLabel,{children:"Type de caisse"}),(0,s.jsxs)(g.Select,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,s.jsx)(y.FormControl,{children:(0,s.jsx)(g.SelectTrigger,{children:(0,s.jsx)(g.SelectValue,{placeholder:"Sélectionnez le type"})})}),(0,s.jsxs)(g.SelectContent,{children:[(0,s.jsx)(g.SelectItem,{value:j.CaisseType.PRINCIPALE,children:"Principale"}),(0,s.jsx)(g.SelectItem,{value:j.CaisseType.REUNION,children:"Réunion"})]})]}),(0,s.jsx)(y.FormDescription,{children:z===j.CaisseType.PRINCIPALE?"Caisse pour les fonds consolidés":"Caisse liée à une session spécifique"}),(0,s.jsx)(y.FormMessage,{})]})}})]}),(0,s.jsx)(y.FormField,{control:U.control,name:"soldeActuel",render:e=>{let{field:t}=e;return(0,s.jsxs)(y.FormItem,{children:[(0,s.jsxs)(y.FormLabel,{className:"flex items-center gap-2",children:[(0,s.jsx)(d.DollarSign,{className:"h-4 w-4"}),"Solde actuel (FCFA)"]}),(0,s.jsx)(y.FormControl,{children:(0,s.jsx)(h.Input,{type:"number",...t,onChange:e=>t.onChange(parseInt(e.target.value)||0)})}),(0,s.jsx)(y.FormDescription,{children:"Montant actuel dans la caisse"}),(0,s.jsx)(y.FormMessage,{})]})}}),z===j.CaisseType.REUNION&&(0,s.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,s.jsx)(y.FormField,{control:U.control,name:"sessionId",render:e=>{let{field:t}=e;return(0,s.jsxs)(y.FormItem,{children:[(0,s.jsx)(y.FormLabel,{children:"Session associée"}),(0,s.jsxs)(g.Select,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,s.jsx)(y.FormControl,{children:(0,s.jsx)(g.SelectTrigger,{children:(0,s.jsx)(g.SelectValue,{placeholder:"Sélectionnez une session"})})}),(0,s.jsx)(g.SelectContent,{children:_.map(e=>(0,s.jsxs)(g.SelectItem,{value:e._id,children:[e.annee," (",new Date(e.dateDebut).toLocaleDateString()," ","-"," ",new Date(e.dateFin).toLocaleDateString(),")"]},e._id))})]}),(0,s.jsx)(y.FormDescription,{children:"Session à laquelle cette caisse est liée"}),(0,s.jsx)(y.FormMessage,{})]})}}),(0,s.jsx)(y.FormField,{control:U.control,name:"caissePrincipaleId",render:e=>{let{field:t}=e;return(0,s.jsxs)(y.FormItem,{children:[(0,s.jsx)(y.FormLabel,{children:"Caisse principale"}),(0,s.jsxs)(g.Select,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,s.jsx)(y.FormControl,{children:(0,s.jsx)(g.SelectTrigger,{children:(0,s.jsx)(g.SelectValue,{placeholder:"Sélectionnez une caisse principale"})})}),(0,s.jsx)(g.SelectContent,{children:L.map(e=>(0,s.jsxs)(g.SelectItem,{value:e._id,children:[e.nom," (",e.soldeActuel.toLocaleString()," FCFA)"]},e._id))})]}),(0,s.jsx)(y.FormDescription,{children:"Caisse principale pour l'émargement"}),(0,s.jsx)(y.FormMessage,{})]})}})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,s.jsx)(p.Button,{variant:"outline",asChild:!0,children:(0,s.jsx)(m.default,{href:"/dashboard/caisses",children:"Annuler"})}),(0,s.jsx)(p.Button,{type:"submit",disabled:O,children:O?"Modification...":"Modifier la caisse"})]})]})})})]})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(p.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,s.jsx)(m.default,{href:"/dashboard/caisses",children:(0,s.jsx)(c.ArrowLeft,{className:"h-4 w-4"})})}),(0,s.jsx)("div",{children:(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Caisse introuvable"})})]}),(0,s.jsx)(f.Card,{children:(0,s.jsx)(f.CardContent,{className:"pt-6",children:(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"La caisse demandée n'a pas été trouvée."})})})})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(p.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,s.jsx)(m.default,{href:"/dashboard/caisses",children:(0,s.jsx)(c.ArrowLeft,{className:"h-4 w-4"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Modifier Caisse"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Modifier les paramètres de la caisse"})]})]}),(0,s.jsx)(f.Card,{children:(0,s.jsx)(f.CardContent,{className:"pt-6",children:(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("p",{className:"text-muted-foreground",children:"Vous n'avez pas les permissions pour modifier cette caisse."}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Seuls les administrateurs et trésoriers peuvent modifier les caisses."})]})})})]})}}]);