(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,44640,(e,s,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ReflectAdapter",{enumerable:!0,get:function(){return t}});class t{static get(e,s,r){let t=Reflect.get(e,s,r);return"function"==typeof t?t.bind(e):t}static set(e,s,r,t){return Reflect.set(e,s,r,t)}static has(e,s){return Reflect.has(e,s)}static deleteProperty(e,s){return Reflect.deleteProperty(e,s)}}},8356,(e,s,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorageInstance",{enumerable:!0,get:function(){return t}});let t=(0,e.r(97918).createAsyncLocalStorage)()},62355,(e,s,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"afterTaskAsyncStorage",{enumerable:!0,get:function(){return t.afterTaskAsyncStorageInstance}});let t=e.r(8356)},17939,(e,s,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,s){for(var r in s)Object.defineProperty(e,r,{enumerable:!0,get:s[r]})}(r,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return o},throwWithStaticGenerationBailoutError:function(){return i},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return a}});let t=e.r(85115),n=e.r(62355);function i(e,s){throw Object.defineProperty(new t.StaticGenBailoutError("Route ".concat(e," couldn't be rendered statically because it used ").concat(s,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function a(e,s){throw Object.defineProperty(new t.StaticGenBailoutError("Route ".concat(e,' with `dynamic = "error"` couldn\'t be rendered statically because it used ').concat(s,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function o(e,s){let r=Object.defineProperty(Error("Route ".concat(e.route,' used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(r,s),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=r),r}function c(){let e=n.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},93168,(e,s,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,s){for(var r in s)Object.defineProperty(e,r,{enumerable:!0,get:s[r]})}(r,{describeHasCheckingStringProperty:function(){return i},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return a}});let t=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,s){return t.test(s)?"`"+e+"."+s+"`":"`"+e+"["+JSON.stringify(s)+"]`"}function i(e,s){let r=JSON.stringify(s);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let a=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},63470,(e,s,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let t=function(e,s){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(s);if(r&&r.has(e))return r.get(e);var t={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(t,a,o):t[a]=e[a]}return t.default=e,r&&r.set(e,t),t}(e.r(38477));function n(e){if("function"!=typeof WeakMap)return null;var s=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:s})(e)}let i={current:null},a="function"==typeof t.cache?t.cache:e=>e,o=console.warn;function c(e){return function(){for(var s=arguments.length,r=Array(s),t=0;t<s;t++)r[t]=arguments[t];o(e(...r))}}a(e=>{try{o(i.current)}finally{i.current=null}})},4467,e=>{"use strict";e.s(["useApi",()=>t]);var s=e.i(1269),r=e.i(1831);function t(){let{data:e}=(0,s.useSession)(),t=async function(s){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(null==e?void 0:e.accessToken))throw Error("Non authentifié");return r.apiService.authenticatedRequest(s,e.accessToken,t)};return{login:r.apiService.login.bind(r.apiService),register:r.apiService.register.bind(r.apiService),authenticatedRequest:t,getUsers:()=>t("/users"),getUser:e=>t("/users/".concat(e)),createUser:e=>t("/users",{method:"POST",body:JSON.stringify(e)}),updateUser:(e,s)=>t("/users/".concat(e),{method:"PATCH",body:JSON.stringify(s)}),deleteUser:e=>t("/users/".concat(e),{method:"DELETE"}),getSessions:()=>t("/sessions"),getSession:e=>t("/sessions/".concat(e)),createSession:e=>t("/sessions",{method:"POST",body:JSON.stringify(e)}),updateSession:(e,s)=>t("/sessions/".concat(e),{method:"PATCH",body:JSON.stringify(s)}),deleteSession:e=>t("/sessions/".concat(e),{method:"DELETE"}),getCaisses:()=>t("/caisses"),getCaisse:e=>t("/caisses/".concat(e)),createCaisse:e=>t("/caisses",{method:"POST",body:JSON.stringify(e)}),updateCaisse:(e,s)=>t("/caisses/".concat(e),{method:"PATCH",body:JSON.stringify(s)}),deleteCaisse:e=>t("/caisses/".concat(e),{method:"DELETE"}),emargerCaisse:e=>t("/caisses/".concat(e,"/emarger"),{method:"POST"}),getReunions:()=>t("/reunions"),getReunion:e=>t("/reunions/".concat(e)),updateReunion:(e,s)=>t("/reunions/".concat(e),{method:"PATCH",body:JSON.stringify(s)}),getMembers:()=>t("/members"),getMember:e=>t("/members/".concat(e)),createMember:e=>t("/members",{method:"POST",body:JSON.stringify(e)}),updateMember:(e,s)=>t("/members/".concat(e),{method:"PATCH",body:JSON.stringify(s)}),deleteMember:e=>t("/members/".concat(e),{method:"DELETE"}),getMemberDebrief:(e,s)=>{let r=new URLSearchParams;(null==s?void 0:s.dateFrom)&&r.append("dateFrom",s.dateFrom),(null==s?void 0:s.dateTo)&&r.append("dateTo",s.dateTo),(null==s?void 0:s.sessionId)&&r.append("sessionId",s.sessionId);let n=r.toString()?"?".concat(r.toString()):"";return t("/members/".concat(e,"/debrief").concat(n))},createPayment:e=>t("/payments",{method:"POST",body:JSON.stringify(e)}),getSessionMembers:e=>t("/sessions/".concat(e,"/members")),addSessionMember:e=>t("/session-members",{method:"POST",body:JSON.stringify(e)}),removeSessionMember:(e,s)=>t("/sessions/".concat(e,"/members/").concat(s),{method:"DELETE"})}}},12058,e=>{"use strict";e.s(["CaisseType",()=>s,"PaymentDirection",()=>t,"PaymentFunction",()=>n,"UserRole",()=>r]);var s=function(e){return e.PRINCIPALE="PRINCIPALE",e.REUNION="REUNION",e}({}),r=function(e){return e.SECRETARY_GENERAL="secretary_general",e.CONTROLLER="controller",e.CASHIER="cashier",e}({}),t=function(e){return e.IN="IN",e.OUT="OUT",e}({}),n=function(e){return e.CONTRIBUTION="cotisation",e.TRANSFER="transfert",e.EXTERNAL="exterieur",e}({})},57223,e=>{"use strict";e.s(["DollarSign",()=>s],57223);let s=(0,e.i(44571).default)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},65429,e=>{"use strict";e.s(["Label",()=>a],65429);var s=e.i(4051),r=e.i(38477),t=e.i(38909),n=r.forwardRef((e,r)=>(0,s.jsx)(t.Primitive.label,{...e,ref:r,onMouseDown:s=>{var r;s.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var i=e.i(41428);function a(e){let{className:r,...t}=e;return(0,s.jsx)(n,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}},14545,e=>{"use strict";e.s(["ArrowLeft",()=>s],14545);let s=(0,e.i(44571).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8889,e=>{"use strict";e.s(["default",()=>b]);var s=e.i(4051),r=e.i(38477),t=e.i(1269),n=e.i(57691),i=e.i(67967),a=e.i(78381),o=e.i(45086),c=e.i(14545),l=e.i(42633),d=e.i(57223),u=e.i(5085),m=e.i(85205),p=e.i(96134),h=e.i(75680),f=e.i(5647),g=e.i(83194),y=e.i(4467),x=e.i(12058);let j=o.z.object({nom:o.z.string().min(1,"Le nom est requis").max(100,"Le nom est trop long"),type:o.z.nativeEnum(x.CaisseType,{required_error:"Le type est requis"}),soldeActuel:o.z.number().gte(0,"Le solde ne peut pas être négatif").max(1e7,"Le solde ne peut pas dépasser 10,000,000 FCFA"),sessionId:o.z.string().optional(),caissePrincipaleId:o.z.string().optional()}).refine(e=>e.type!==x.CaisseType.REUNION||e.sessionId&&e.caissePrincipaleId,{message:"Pour une caisse de réunion, la session et la caisse principale sont requises",path:["sessionId"]});function b(){let{data:e,status:o}=(0,t.useSession)(),b=(0,n.useRouter)(),S=(0,y.useApi)(),[C,v]=(0,r.useState)(!1),[N,P]=(0,r.useState)(null),[O,E]=(0,r.useState)([]),[T,I]=(0,r.useState)([]),[R,F]=(0,r.useState)(!0),A=(null==e?void 0:e.user)&&"secretary_general"===e.user.role,_=(0,i.useForm)({resolver:(0,a.zodResolver)(j),defaultValues:{nom:"",type:x.CaisseType.PRINCIPALE,soldeActuel:0,sessionId:"",caissePrincipaleId:""}}),w=_.watch("type");(0,r.useEffect)(()=>{let s=async()=>{try{F(!0);let[e,s]=await Promise.all([S.getSessions(),S.getCaisses()]);E(e),I(s.filter(e=>e.type===x.CaisseType.PRINCIPALE))}catch(e){console.error("Erreur lors du chargement des données:",e)}finally{F(!1)}};(null==e?void 0:e.accessToken)&&s()},[o]);let L=async e=>{if(!A)return void P("Vous n'avez pas les permissions pour créer une caisse");try{v(!0),P(null);let s={nom:e.nom,type:e.type,soldeActuel:e.soldeActuel,...e.type===x.CaisseType.REUNION&&{sessionId:e.sessionId,caissePrincipaleId:e.caissePrincipaleId}};await S.createCaisse(s),b.push("/dashboard/caisses")}catch(e){console.error("Erreur lors de la création:",e),P(e.message||"Une erreur est survenue lors de la création")}finally{v(!1)}};return A?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(m.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,s.jsx)(u.default,{href:"/dashboard/caisses",children:(0,s.jsx)(c.ArrowLeft,{className:"h-4 w-4"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Nouvelle Caisse"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Créer une nouvelle caisse principale ou de réunion"})]})]}),(0,s.jsxs)(h.Card,{children:[(0,s.jsxs)(h.CardHeader,{children:[(0,s.jsxs)(h.CardTitle,{className:"flex items-center gap-2",children:[(0,s.jsx)(l.Wallet,{className:"h-5 w-5"}),"Informations de la caisse"]}),(0,s.jsx)(h.CardDescription,{children:"Définissez les paramètres de la nouvelle caisse"})]}),(0,s.jsx)(h.CardContent,{children:R?(0,s.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"}),(0,s.jsx)("span",{className:"ml-2",children:"Chargement des données..."})]}):(0,s.jsx)(f.Form,{..._,children:(0,s.jsxs)("form",{onSubmit:_.handleSubmit(L),className:"space-y-6",children:[N&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,s.jsx)("p",{className:"text-sm text-red-600",children:N})}),(0,s.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,s.jsx)(f.FormField,{control:_.control,name:"nom",render:e=>{let{field:r}=e;return(0,s.jsxs)(f.FormItem,{children:[(0,s.jsx)(f.FormLabel,{children:"Nom de la caisse"}),(0,s.jsx)(f.FormControl,{children:(0,s.jsx)(p.Input,{placeholder:"Ex: Caisse Principale 2025",...r})}),(0,s.jsx)(f.FormDescription,{children:"Nom descriptif de la caisse"}),(0,s.jsx)(f.FormMessage,{})]})}}),(0,s.jsx)(f.FormField,{control:_.control,name:"type",render:e=>{let{field:r}=e;return(0,s.jsxs)(f.FormItem,{children:[(0,s.jsx)(f.FormLabel,{children:"Type de caisse"}),(0,s.jsxs)(g.Select,{onValueChange:r.onChange,defaultValue:r.value,children:[(0,s.jsx)(f.FormControl,{children:(0,s.jsx)(g.SelectTrigger,{children:(0,s.jsx)(g.SelectValue,{placeholder:"Sélectionnez le type"})})}),(0,s.jsxs)(g.SelectContent,{children:[(0,s.jsx)(g.SelectItem,{value:x.CaisseType.PRINCIPALE,children:"Principale"}),(0,s.jsx)(g.SelectItem,{value:x.CaisseType.REUNION,children:"Réunion"})]})]}),(0,s.jsx)(f.FormDescription,{children:w===x.CaisseType.PRINCIPALE?"Caisse pour les fonds consolidés":"Caisse liée à une session spécifique"}),(0,s.jsx)(f.FormMessage,{})]})}})]}),(0,s.jsx)(f.FormField,{control:_.control,name:"soldeActuel",render:e=>{let{field:r}=e;return(0,s.jsxs)(f.FormItem,{children:[(0,s.jsxs)(f.FormLabel,{className:"flex items-center gap-2",children:[(0,s.jsx)(d.DollarSign,{className:"h-4 w-4"}),"Solde initial (FCFA)"]}),(0,s.jsx)(f.FormControl,{children:(0,s.jsx)(p.Input,{type:"number",...r,onChange:e=>r.onChange(parseInt(e.target.value)||0)})}),(0,s.jsx)(f.FormDescription,{children:"Montant initial dans la caisse"}),(0,s.jsx)(f.FormMessage,{})]})}}),w===x.CaisseType.REUNION&&(0,s.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,s.jsx)(f.FormField,{control:_.control,name:"sessionId",render:e=>{let{field:r}=e;return(0,s.jsxs)(f.FormItem,{children:[(0,s.jsx)(f.FormLabel,{children:"Session associée"}),(0,s.jsxs)(g.Select,{onValueChange:r.onChange,defaultValue:r.value,children:[(0,s.jsx)(f.FormControl,{children:(0,s.jsx)(g.SelectTrigger,{children:(0,s.jsx)(g.SelectValue,{placeholder:"Sélectionnez une session"})})}),(0,s.jsx)(g.SelectContent,{children:O.map(e=>(0,s.jsxs)(g.SelectItem,{value:e._id,children:[e.annee," (",new Date(e.dateDebut).toLocaleDateString()," ","-"," ",new Date(e.dateFin).toLocaleDateString(),")"]},e._id))})]}),(0,s.jsx)(f.FormDescription,{children:"Session à laquelle cette caisse est liée"}),(0,s.jsx)(f.FormMessage,{})]})}}),(0,s.jsx)(f.FormField,{control:_.control,name:"caissePrincipaleId",render:e=>{let{field:r}=e;return(0,s.jsxs)(f.FormItem,{children:[(0,s.jsx)(f.FormLabel,{children:"Caisse principale"}),(0,s.jsxs)(g.Select,{onValueChange:r.onChange,defaultValue:r.value,children:[(0,s.jsx)(f.FormControl,{children:(0,s.jsx)(g.SelectTrigger,{children:(0,s.jsx)(g.SelectValue,{placeholder:"Sélectionnez une caisse principale"})})}),(0,s.jsx)(g.SelectContent,{children:T.map(e=>(0,s.jsxs)(g.SelectItem,{value:e._id,children:[e.nom," (",e.soldeActuel.toLocaleString()," FCFA)"]},e._id))})]}),(0,s.jsx)(f.FormDescription,{children:"Caisse principale pour l'émargement"}),(0,s.jsx)(f.FormMessage,{})]})}})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,s.jsx)(m.Button,{variant:"outline",asChild:!0,children:(0,s.jsx)(u.default,{href:"/dashboard/caisses",children:"Annuler"})}),(0,s.jsx)(m.Button,{type:"submit",disabled:C,children:C?"Création...":"Créer la caisse"})]})]})})})]}),(0,s.jsxs)(h.Card,{children:[(0,s.jsx)(h.CardHeader,{children:(0,s.jsx)(h.CardTitle,{children:"Types de caisses"})}),(0,s.jsx)(h.CardContent,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Caisse Principale"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Caisse pour consolider les fonds de toutes les réunions. Les fonds des caisses de réunion peuvent être émargés vers cette caisse."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Caisse de Réunion"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Caisse liée à une session spécifique. Doit être associée à une caisse principale pour permettre l'émargement des fonds."})]})]})})]})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(m.Button,{variant:"outline",size:"icon",asChild:!0,children:(0,s.jsx)(u.default,{href:"/dashboard/caisses",children:(0,s.jsx)(c.ArrowLeft,{className:"h-4 w-4"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Nouvelle Caisse"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Créer une nouvelle caisse"})]})]}),(0,s.jsx)(h.Card,{children:(0,s.jsx)(h.CardContent,{className:"pt-6",children:(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("p",{className:"text-muted-foreground",children:"Vous n'avez pas les permissions pour créer une caisse."}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Seuls les administrateurs et trésoriers peuvent créer des caisses."})]})})})]})}}]);