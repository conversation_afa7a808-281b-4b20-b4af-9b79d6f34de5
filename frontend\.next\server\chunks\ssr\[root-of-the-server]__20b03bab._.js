module.exports=[24725,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},12594,a=>{"use strict";a.s(["useApi",()=>d]);var b=a.i(81223),c=a.i(34075);function d(){let{data:a}=(0,b.useSession)(),d=async(b,d={})=>{if(!a?.accessToken)throw Error("Non authentifié");return c.apiService.authenticatedRequest(b,a.accessToken,d)};return{login:c.apiService.login.bind(c.apiService),register:c.apiService.register.bind(c.apiService),authenticatedRequest:d,getUsers:()=>d("/users"),getUser:a=>d(`/users/${a}`),createUser:a=>d("/users",{method:"POST",body:JSON.stringify(a)}),updateUser:(a,b)=>d(`/users/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteUser:a=>d(`/users/${a}`,{method:"DELETE"}),getSessions:()=>d("/sessions"),getSession:a=>d(`/sessions/${a}`),createSession:a=>d("/sessions",{method:"POST",body:JSON.stringify(a)}),updateSession:(a,b)=>d(`/sessions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteSession:a=>d(`/sessions/${a}`,{method:"DELETE"}),getCaisses:()=>d("/caisses"),getCaisse:a=>d(`/caisses/${a}`),createCaisse:a=>d("/caisses",{method:"POST",body:JSON.stringify(a)}),updateCaisse:(a,b)=>d(`/caisses/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteCaisse:a=>d(`/caisses/${a}`,{method:"DELETE"}),emargerCaisse:a=>d(`/caisses/${a}/emarger`,{method:"POST"}),getReunions:()=>d("/reunions"),getReunion:a=>d(`/reunions/${a}`),updateReunion:(a,b)=>d(`/reunions/${a}`,{method:"PATCH",body:JSON.stringify(b)}),getMembers:()=>d("/members"),getMember:a=>d(`/members/${a}`),createMember:a=>d("/members",{method:"POST",body:JSON.stringify(a)}),updateMember:(a,b)=>d(`/members/${a}`,{method:"PATCH",body:JSON.stringify(b)}),deleteMember:a=>d(`/members/${a}`,{method:"DELETE"}),getMemberDebrief:(a,b)=>{let c=new URLSearchParams;b?.dateFrom&&c.append("dateFrom",b.dateFrom),b?.dateTo&&c.append("dateTo",b.dateTo),b?.sessionId&&c.append("sessionId",b.sessionId);let e=c.toString()?`?${c.toString()}`:"";return d(`/members/${a}/debrief${e}`)},createPayment:a=>d("/payments",{method:"POST",body:JSON.stringify(a)}),getSessionMembers:a=>d(`/sessions/${a}/members`),addSessionMember:a=>d("/session-members",{method:"POST",body:JSON.stringify(a)}),removeSessionMember:(a,b)=>d(`/sessions/${a}/members/${b}`,{method:"DELETE"})}}},78655,a=>{"use strict";a.s(["CaisseType",()=>b,"PaymentDirection",()=>d,"PaymentFunction",()=>e,"UserRole",()=>c]);var b=function(a){return a.PRINCIPALE="PRINCIPALE",a.REUNION="REUNION",a}({}),c=function(a){return a.SECRETARY_GENERAL="secretary_general",a.CONTROLLER="controller",a.CASHIER="cashier",a}({}),d=function(a){return a.IN="IN",a.OUT="OUT",a}({}),e=function(a){return a.CONTRIBUTION="cotisation",a.TRANSFER="transfert",a.EXTERNAL="exterieur",a}({})},6821,a=>{"use strict";a.s(["ArrowLeft",()=>b],6821);let b=(0,a.i(621).default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},20283,a=>{"use strict";a.s(["Table",()=>d,"TableBody",()=>f,"TableCell",()=>i,"TableHead",()=>h,"TableHeader",()=>e,"TableRow",()=>g]);var b=a.i(68116),c=a.i(22171);function d({className:a,...d}){return(0,b.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,b.jsx)("table",{"data-slot":"table",className:(0,c.cn)("w-full caption-bottom text-sm",a),...d})})}function e({className:a,...d}){return(0,b.jsx)("thead",{"data-slot":"table-header",className:(0,c.cn)("[&_tr]:border-b",a),...d})}function f({className:a,...d}){return(0,b.jsx)("tbody",{"data-slot":"table-body",className:(0,c.cn)("[&_tr:last-child]:border-0",a),...d})}function g({className:a,...d}){return(0,b.jsx)("tr",{"data-slot":"table-row",className:(0,c.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...d})}function h({className:a,...d}){return(0,b.jsx)("th",{"data-slot":"table-head",className:(0,c.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...d})}function i({className:a,...d}){return(0,b.jsx)("td",{"data-slot":"table-cell",className:(0,c.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...d})}},93470,a=>{"use strict";a.s(["Badge",()=>g]);var b=a.i(68116),c=a.i(85689),d=a.i(57167),e=a.i(22171);let f=(0,d.cva)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function g({className:a,variant:d,asChild:g=!1,...h}){let i=g?c.Slot:"span";return(0,b.jsx)(i,{"data-slot":"badge",className:(0,e.cn)(f({variant:d}),a),...h})}},53391,63482,a=>{"use strict";a.s(["TrendingUp",()=>c],53391);var b=a.i(621);let c=(0,b.default)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);a.s(["TrendingDown",()=>d],63482);let d=(0,b.default)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},95128,37539,70417,a=>{"use strict";a.s(["Mail",()=>c],95128);var b=a.i(621);let c=(0,b.default)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);a.s(["Phone",()=>d],37539);let d=(0,b.default)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);a.s(["MapPin",()=>e],70417);let e=(0,b.default)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},34957,a=>{"use strict";a.s(["default",()=>s]);var b=a.i(68116),c=a.i(128),d=a.i(50395),e=a.i(81223),f=a.i(6821),g=a.i(95128),h=a.i(37539),i=a.i(70417),j=a.i(53391),k=a.i(63482),l=a.i(33055),m=a.i(2979),n=a.i(75780),o=a.i(20283),p=a.i(93470),q=a.i(12594),r=a.i(78655);function s(){let a=(0,d.useParams)();(0,d.useRouter)();let{data:s,status:t}=(0,e.useSession)(),u=(0,q.useApi)(),[v,w]=(0,c.useState)(null),[x,y]=(0,c.useState)(null),[z,A]=(0,c.useState)(!0),[B,C]=(0,c.useState)(null),D=a.id,E=s?.user&&(s.user.role===r.UserRole.SECRETARY_GENERAL||s.user.role===r.UserRole.CONTROLLER);(0,c.useEffect)(()=>{s?.accessToken&&D&&F()},[t,D]);let F=async()=>{try{A(!0),C(null);let[a,b]=await Promise.all([u.getMember(D),u.getMemberDebrief(D)]);w(a),y(b)}catch(a){console.error("Erreur lors du chargement:",a),C("Erreur lors du chargement des données")}finally{A(!1)}},G=a=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"XAF"}).format(a);return E?z?(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsx)("div",{className:"flex items-center gap-4",children:(0,b.jsx)(l.default,{href:"/dashboard/members",children:(0,b.jsxs)(m.Button,{variant:"ghost",size:"sm",children:[(0,b.jsx)(f.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,b.jsx)("div",{className:"flex justify-center py-8",children:(0,b.jsx)("div",{className:"text-gray-500",children:"Chargement..."})})]}):B||!v?(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsx)("div",{className:"flex items-center gap-4",children:(0,b.jsx)(l.default,{href:"/dashboard/members",children:(0,b.jsxs)(m.Button,{variant:"ghost",size:"sm",children:[(0,b.jsx)(f.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,b.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Erreur"}),(0,b.jsx)("p",{className:"text-gray-600",children:B||"Membre introuvable"})]})})]}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(l.default,{href:"/dashboard/members",children:(0,b.jsxs)(m.Button,{variant:"ghost",size:"sm",children:[(0,b.jsx)(f.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:[v.firstName," ",v.lastName]}),(0,b.jsx)("p",{className:"text-gray-600",children:"Détails du membre"})]})]}),(0,b.jsx)(l.default,{href:`/dashboard/members/${v._id}/edit`,children:(0,b.jsx)(m.Button,{children:"Modifier"})})]}),(0,b.jsxs)(n.Card,{children:[(0,b.jsx)(n.CardHeader,{children:(0,b.jsx)(n.CardTitle,{children:"Informations personnelles"})}),(0,b.jsx)(n.CardContent,{children:(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Nom complet"}),(0,b.jsxs)("p",{className:"text-gray-600",children:[v.firstName," ",v.lastName]})]}),v.email&&(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Email"}),(0,b.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,b.jsx)(g.Mail,{className:"h-4 w-4 mr-2"}),v.email]})]}),v.phone&&(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Téléphone"}),(0,b.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,b.jsx)(h.Phone,{className:"h-4 w-4 mr-2"}),v.phone]})]}),v.address&&(0,b.jsxs)("div",{className:"md:col-span-3",children:[(0,b.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Adresse"}),(0,b.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,b.jsx)(i.MapPin,{className:"h-4 w-4 mr-2"}),v.address]})]})]})})]}),x&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,b.jsxs)(n.Card,{children:[(0,b.jsx)(n.CardHeader,{className:"pb-2",children:(0,b.jsx)(n.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Total Entrées"})}),(0,b.jsx)(n.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-green-600",children:G(x.totalIn)})})]}),(0,b.jsxs)(n.Card,{children:[(0,b.jsx)(n.CardHeader,{className:"pb-2",children:(0,b.jsx)(n.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Total Sorties"})}),(0,b.jsx)(n.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-red-600",children:G(x.totalOut)})})]}),(0,b.jsxs)(n.Card,{children:[(0,b.jsx)(n.CardHeader,{className:"pb-2",children:(0,b.jsx)(n.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Solde Net"})}),(0,b.jsx)(n.CardContent,{children:(0,b.jsx)("div",{className:`text-2xl font-bold ${x.netAmount>=0?"text-green-600":"text-red-600"}`,children:G(x.netAmount??0)})})]}),(0,b.jsxs)(n.Card,{children:[(0,b.jsx)(n.CardHeader,{className:"pb-2",children:(0,b.jsx)(n.CardTitle,{className:"text-sm font-medium text-gray-600",children:"Cotisations"})}),(0,b.jsx)(n.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:G(x.contributionsTotal??0)})})]})]}),(0,b.jsxs)(n.Card,{children:[(0,b.jsxs)(n.CardHeader,{children:[(0,b.jsx)(n.CardTitle,{children:"Historique des paiements"}),(0,b.jsxs)(n.CardDescription,{children:[x.payments?.length," paiement(s) enregistré(s)"]})]}),(0,b.jsx)(n.CardContent,{children:x.payments?.length>0?(0,b.jsxs)(o.Table,{children:[(0,b.jsx)(o.TableHeader,{children:(0,b.jsxs)(o.TableRow,{children:[(0,b.jsx)(o.TableHead,{children:"Date"}),(0,b.jsx)(o.TableHead,{children:"Type"}),(0,b.jsx)(o.TableHead,{children:"Fonction"}),(0,b.jsx)(o.TableHead,{children:"Montant"})]})}),(0,b.jsx)(o.TableBody,{children:x.payments.map(a=>(0,b.jsxs)(o.TableRow,{children:[(0,b.jsx)(o.TableCell,{children:new Date(a.date).toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric"})}),(0,b.jsx)(o.TableCell,{children:(0,b.jsxs)("div",{className:"flex items-center",children:[a.direction===r.PaymentDirection.IN?(0,b.jsx)(j.TrendingUp,{className:"h-4 w-4 text-green-600"}):(0,b.jsx)(k.TrendingDown,{className:"h-4 w-4 text-red-600"}),(0,b.jsx)("span",{className:"ml-2",children:a.direction===r.PaymentDirection.IN?"Entrée":"Sortie"})]})}),(0,b.jsx)(o.TableCell,{children:(a=>{let c={[r.PaymentFunction.CONTRIBUTION]:"default",[r.PaymentFunction.TRANSFER]:"secondary",[r.PaymentFunction.EXTERNAL]:"outline"},d={[r.PaymentFunction.CONTRIBUTION]:"Cotisation",[r.PaymentFunction.TRANSFER]:"Transfert",[r.PaymentFunction.EXTERNAL]:"Extérieur"};return(0,b.jsx)(p.Badge,{variant:c[a],children:d[a]})})(a.func)}),(0,b.jsx)(o.TableCell,{children:(0,b.jsx)("span",{className:a.direction===r.PaymentDirection.IN?"text-green-600":"text-red-600",children:G(a.amount)})})]},a._id))})]}):(0,b.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Aucun paiement enregistré"})})]})]})]}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsx)("div",{className:"flex items-center gap-4",children:(0,b.jsx)(l.default,{href:"/dashboard/members",children:(0,b.jsxs)(m.Button,{variant:"ghost",size:"sm",children:[(0,b.jsx)(f.ArrowLeft,{className:"h-4 w-4 mr-2"}),"Retour"]})})}),(0,b.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Accès refusé"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Vous n'avez pas les permissions pour accéder à cette page."})]})})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__20b03bab._.js.map