{"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_a71539c9.module.css [app-rsc] (css module)", "turbopack:///[next]/internal/font/google/geist_mono_8d43a2aa.module.css [app-rsc] (css module)", "turbopack:///[project]/frontend/src/components/provider.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[next]/internal/font/google/geist_a71539c9.js", "turbopack:///[next]/internal/font/google/geist_mono_8d43a2aa.js", "turbopack:///[project]/frontend/src/app/layout.tsx"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_a71539c9-module__T19VSG__className\",\n  \"variable\": \"geist_a71539c9-module__T19VSG__variable\",\n});\n", "__turbopack_context__.v({\n  \"className\": \"geist_mono_8d43a2aa-module__8Li5zG__className\",\n  \"variable\": \"geist_mono_8d43a2aa-module__8Li5zG__variable\",\n});\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/frontend/src/components/provider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/frontend/src/components/provider.tsx\",\n    \"default\",\n);\n", "import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport Providers from \"@/components/provider\";\n\nconst geistSans = Geist({\n\tvariable: \"--font-geist-sans\",\n\tsubsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n\tvariable: \"--font-geist-mono\",\n\tsubsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n\ttitle: \"Tontine App\",\n\tdescription: \"Tontine management application\",\n};\n\nexport default function RootLayout({\n\tchildren,\n}: Readonly<{\n\tchildren: React.ReactNode;\n}>) {\n\treturn (\n\t\t<html lang=\"en\">\n\t\t\t<body\n\t\t\t\tclassName={`${geistSans.variable} ${geistMono.variable} antialiased`}\n\t\t\t>\n\t\t\t\t<Providers>{children}</Providers>\n\t\t\t</body>\n\t\t</html>\n\t);\n}\n"], "names": [], "mappings": "0BAAA,EAAA,CAAA,CAAA,CACA,UAAA,2CACA,SAAA,yCACA,cCHA,EAAA,CAAA,CAAA,CACA,UAAA,gDACA,SAAA,8CACA,wDCDe,CAAA,EADf,AACe,EADf,CAAA,CAAA,OACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,sSAAwS,EACrU,qEACA,gEAHW,CAAA,EADf,AACe,EADf,CAAA,CAAA,OACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,kRAAoR,EACjT,iDACA,6JCLJ,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,4BACZ,UAAW,QAEf,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAA,AAAQ,ECX1C,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,sCACZ,UAAW,QAEf,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAQ,AAAR,ECRlC,IAAA,EAAA,EAAA,CAAA,CAAA,OAYO,IAAM,EAAqB,CACjC,MAAO,cACP,YAAa,gCACd,EAEe,SAAS,EAAW,CAClC,UAAQ,CAGP,EACD,MACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,cACV,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACA,UAAW,CAAA,EFdA,AEcG,EAAU,QAAQ,CAAC,CAAC,EDdvB,ACcyB,EAAU,QAAQ,CAAC,YAAY,CAAC,UAEpE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAS,CAAA,UAAE,OAIhB", "ignoreList": [0, 1, 2, 3, 4]}