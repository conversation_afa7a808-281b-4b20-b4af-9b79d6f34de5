"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ArrowLeft, Wallet, DollarSign } from "lucide-react";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useApi } from "@/hooks/use-api";
import { CaisseForm, CaisseType, Session, Caisse } from "@/types";

const caisseSchema = z
	.object({
		nom: z
			.string()
			.min(1, "Le nom est requis")
			.max(100, "Le nom est trop long"),
		type: z.nativeEnum(CaisseType, {
			required_error: "Le type est requis",
		}),
		soldeActuel: z
			.number()
			.gte(0, "Le solde ne peut pas être négatif")
			.max(10000000, "Le solde ne peut pas dépasser 10,000,000 FCFA"),
		sessionId: z.string().optional(),
		caissePrincipaleId: z.string().optional(),
	})
	.refine(
		(data) => {
			if (data.type === CaisseType.REUNION) {
				return data.sessionId && data.caissePrincipaleId;
			}
			return true;
		},
		{
			message:
				"Pour une caisse de réunion, la session et la caisse principale sont requises",
			path: ["sessionId"],
		},
	);

export default function NewCaissePage() {
	const { data: session, status } = useSession();
	const router = useRouter();
	const api = useApi();

	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [sessions, setSessions] = useState<Session[]>([]);
	const [caissesPrincipales, setCaissesPrincipales] = useState<Caisse[]>([]);
	const [loadingData, setLoadingData] = useState(true);

	// Vérifier les permissions
	const canCreateCaisses =
		session?.user && (session.user as any).role === "secretary_general";

	const form = useForm<CaisseForm>({
		resolver: zodResolver(caisseSchema),
		defaultValues: {
			nom: "",
			type: CaisseType.PRINCIPALE,
			soldeActuel: 0,
			sessionId: "",
			caissePrincipaleId: "",
		},
	});

	const watchType = form.watch("type");

	// Charger les données nécessaires
	useEffect(() => {
		const loadData = async () => {
			try {
				setLoadingData(true);
				const [sessionsData, caissesData] = await Promise.all([
					api.getSessions(),
					api.getCaisses(),
				]);

				setSessions(sessionsData);
				setCaissesPrincipales(
					caissesData.filter((c) => c.type === CaisseType.PRINCIPALE),
				);
			} catch (error) {
				console.error("Erreur lors du chargement des données:", error);
			} finally {
				setLoadingData(false);
			}
		};

		if (session?.accessToken) {
			loadData();
		}
	}, [status]);

	const onSubmit = async (data: CaisseForm) => {
		if (!canCreateCaisses) {
			setError("Vous n'avez pas les permissions pour créer une caisse");
			return;
		}

		try {
			setIsLoading(true);
			setError(null);

			// Préparer les données
			const caisseData = {
				nom: data.nom,
				type: data.type,
				soldeActuel: data.soldeActuel,
				...(data.type === CaisseType.REUNION && {
					sessionId: data.sessionId,
					caissePrincipaleId: data.caissePrincipaleId,
				}),
			};

			await api.createCaisse(caisseData);
			router.push("/dashboard/caisses");
		} catch (error: any) {
			console.error("Erreur lors de la création:", error);
			setError(error.message || "Une erreur est survenue lors de la création");
		} finally {
			setIsLoading(false);
		}
	};

	if (!canCreateCaisses) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Button variant="outline" size="icon" asChild>
						<Link href="/dashboard/caisses">
							<ArrowLeft className="h-4 w-4" />
						</Link>
					</Button>
					<div>
						<h1 className="text-3xl font-bold tracking-tight">
							Nouvelle Caisse
						</h1>
						<p className="text-muted-foreground">Créer une nouvelle caisse</p>
					</div>
				</div>

				<Card>
					<CardContent className="pt-6">
						<div className="text-center py-8">
							<p className="text-muted-foreground">
								Vous n'avez pas les permissions pour créer une caisse.
							</p>
							<p className="text-sm text-muted-foreground mt-2">
								Seuls les administrateurs et trésoriers peuvent créer des
								caisses.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex items-center gap-4">
				<Button variant="outline" size="icon" asChild>
					<Link href="/dashboard/caisses">
						<ArrowLeft className="h-4 w-4" />
					</Link>
				</Button>
				<div>
					<h1 className="text-3xl font-bold tracking-tight">Nouvelle Caisse</h1>
					<p className="text-muted-foreground">
						Créer une nouvelle caisse principale ou de réunion
					</p>
				</div>
			</div>

			{/* Formulaire */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Wallet className="h-5 w-5" />
						Informations de la caisse
					</CardTitle>
					<CardDescription>
						Définissez les paramètres de la nouvelle caisse
					</CardDescription>
				</CardHeader>
				<CardContent>
					{loadingData ? (
						<div className="flex items-center justify-center py-8">
							<div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
							<span className="ml-2">Chargement des données...</span>
						</div>
					) : (
						<Form {...form}>
							<form
								onSubmit={form.handleSubmit(onSubmit)}
								className="space-y-6"
							>
								{error && (
									<div className="bg-red-50 border border-red-200 rounded-md p-4">
										<p className="text-sm text-red-600">{error}</p>
									</div>
								)}

								<div className="grid gap-6 md:grid-cols-2">
									<FormField
										control={form.control}
										name="nom"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Nom de la caisse</FormLabel>
												<FormControl>
													<Input
														placeholder="Ex: Caisse Principale 2025"
														{...field}
													/>
												</FormControl>
												<FormDescription>
													Nom descriptif de la caisse
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="type"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Type de caisse</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Sélectionnez le type" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value={CaisseType.PRINCIPALE}>
															Principale
														</SelectItem>
														<SelectItem value={CaisseType.REUNION}>
															Réunion
														</SelectItem>
													</SelectContent>
												</Select>
												<FormDescription>
													{watchType === CaisseType.PRINCIPALE
														? "Caisse pour les fonds consolidés"
														: "Caisse liée à une session spécifique"}
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>

								<FormField
									control={form.control}
									name="soldeActuel"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="flex items-center gap-2">
												<DollarSign className="h-4 w-4" />
												Solde initial (FCFA)
											</FormLabel>
											<FormControl>
												<Input
													type="number"
													{...field}
													onChange={(e) =>
														field.onChange(parseInt(e.target.value) || 0)
													}
												/>
											</FormControl>
											<FormDescription>
												Montant initial dans la caisse
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								{watchType === CaisseType.REUNION && (
									<div className="grid gap-6 md:grid-cols-2">
										<FormField
											control={form.control}
											name="sessionId"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Session associée</FormLabel>
													<Select
														onValueChange={field.onChange}
														defaultValue={field.value}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder="Sélectionnez une session" />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															{sessions.map((session) => (
																<SelectItem
																	key={session._id}
																	value={session._id}
																>
																	{session.annee} (
																	{new Date(
																		session.dateDebut,
																	).toLocaleDateString()}{" "}
																	-{" "}
																	{new Date(
																		session.dateFin,
																	).toLocaleDateString()}
																	)
																</SelectItem>
															))}
														</SelectContent>
													</Select>
													<FormDescription>
														Session à laquelle cette caisse est liée
													</FormDescription>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="caissePrincipaleId"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Caisse principale</FormLabel>
													<Select
														onValueChange={field.onChange}
														defaultValue={field.value}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder="Sélectionnez une caisse principale" />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															{caissesPrincipales.map((caisse) => (
																<SelectItem key={caisse._id} value={caisse._id}>
																	{caisse.nom} (
																	{caisse.soldeActuel.toLocaleString()} FCFA)
																</SelectItem>
															))}
														</SelectContent>
													</Select>
													<FormDescription>
														Caisse principale pour l'émargement
													</FormDescription>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
								)}

								<div className="flex justify-end gap-4">
									<Button variant="outline" asChild>
										<Link href="/dashboard/caisses">Annuler</Link>
									</Button>
									<Button type="submit" disabled={isLoading}>
										{isLoading ? "Création..." : "Créer la caisse"}
									</Button>
								</div>
							</form>
						</Form>
					)}
				</CardContent>
			</Card>

			{/* Informations supplémentaires */}
			<Card>
				<CardHeader>
					<CardTitle>Types de caisses</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<div>
							<h4 className="font-medium">Caisse Principale</h4>
							<p className="text-sm text-muted-foreground">
								Caisse pour consolider les fonds de toutes les réunions. Les
								fonds des caisses de réunion peuvent être émargés vers cette
								caisse.
							</p>
						</div>
						<div>
							<h4 className="font-medium">Caisse de Réunion</h4>
							<p className="text-sm text-muted-foreground">
								Caisse liée à une session spécifique. Doit être associée à une
								caisse principale pour permettre l'émargement des fonds.
							</p>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
