import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument, Types } from 'mongoose';
import { User } from '../../users/schemas/user.schema';

export type SessionDocument = HydratedDocument<Session>;

@Schema({ timestamps: true })
export class Session {
  @Prop({ required: true })
  annee!: number;

  @Prop({ type: Date, required: true })
  dateDebut!: Date;

  @Prop({ type: Date, required: true })
  dateFin!: Date;

  @Prop({ type: Date })
  dateProchaineReunion!: Date;

  // Track the exact next reunion for reliable opening checks
  @Prop({ type: Types.ObjectId, ref: 'Reunion', required: false })
  nextReunionId?: Types.ObjectId;

  @Prop({ type: Number, required: true, min: 0 })
  partFixe!: number;

  @Prop({ type: Types.ObjectId, ref: User.name, required: true })
  createdBy!: Types.ObjectId;
}

export const SessionSchema = SchemaFactory.createForClass(Session);