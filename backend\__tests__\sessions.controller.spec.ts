import 'reflect-metadata';
import { Test } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { SessionsController } from '../src/sessions/sessions.controller';
import { SessionsService } from '../src/sessions/sessions.service';
import { AppTestingModule } from './app-testing.module';

const serviceMock = {
  create: jest.fn().mockResolvedValue({ _id: 's1' }),
  findAll: jest.fn().mockResolvedValue([{ _id: 's1' }]),
  findOne: jest.fn().mockResolvedValue({ _id: 's1' }),
  update: jest.fn().mockResolvedValue({ _id: 's1' }),
  remove: jest.fn().mockResolvedValue({}),
  addMember: jest.fn().mockResolvedValue({ ok: true }),
  paymentsDebrief: jest.fn().mockResolvedValue({ totalIn: 0, totalOut: 0 }),
};

describe('SessionsController', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      controllers: [SessionsController],
      providers: [{ provide: SessionsService, useValue: serviceMock }],
    })
      .overrideGuard(require('../src/auth/jwt-auth.guard').JwtAuthGuard)
      .useClass(require('./utils/mock-guards').MockJwtAuthGuard)
      .overrideGuard(require('../src/common/guards/role.guard').RolesGuard)
      .useClass(require('./utils/mock-guards').MockRolesGuard)
      .compile();

    app = moduleRef.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('POST /sessions', async () => {
    await request(app.getHttpServer()).post('/sessions').set('x-user-role','secretary_general').send({ name: 'S' }).expect(201);
  });

  it('GET /sessions', async () => {
    await request(app.getHttpServer()).get('/sessions').set('x-user-role','cashier').expect(200);
  });

  it('GET /sessions/:id', async () => {
    await request(app.getHttpServer()).get('/sessions/s1').set('x-user-role','controller').expect(200);
  });

  it('PATCH /sessions/:id', async () => {
    await request(app.getHttpServer()).patch('/sessions/s1').set('x-user-role','secretary_general').send({ name: 'S2' }).expect(200);
  });

  it('DELETE /sessions/:id', async () => {
    await request(app.getHttpServer()).delete('/sessions/s1').set('x-user-role','secretary_general').expect(200);
  });

  it('POST /sessions/:id/members', async () => {
    await request(app.getHttpServer()).post('/sessions/s1/members').set('x-user-role','secretary_general').send({ memberId: 'm1', parts: 2 }).expect(201);
  });

  it('GET /sessions/:id/payments-debrief', async () => {
    await request(app.getHttpServer()).get('/sessions/s1/payments-debrief').set('x-user-role','controller').expect(200);
  });
});