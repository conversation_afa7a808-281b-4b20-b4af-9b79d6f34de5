import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { useApi } from '@/hooks/use-api';
import type { Member, CreateMemberDto, UpdateMemberDto, MemberDebrief, PaymentFilters } from '@/types';

// Query Keys
export const memberKeys = {
  all: ['members'] as const,
  lists: () => [...memberKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...memberKeys.lists(), { filters }] as const,
  details: () => [...memberKeys.all, 'detail'] as const,
  detail: (id: string) => [...memberKeys.details(), id] as const,
  debrief: (id: string, filters?: PaymentFilters) => [...memberKeys.detail(id), 'debrief', { filters }] as const,
};

// Hooks
export function useMembers(searchTerm?: string) {
  const { data: session } = useSession();
  const api = useApi();

  return useQuery({
    queryKey: memberKeys.list({ searchTerm }),
    queryFn: () => api.getMembers(),
    enabled: !!session?.accessToken,
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => {
      // Client-side filtering for search
      if (!searchTerm) return data;
      const search = searchTerm.toLowerCase();
      return data.filter(
        (member) =>
          member.firstName.toLowerCase().includes(search) ||
          member.lastName.toLowerCase().includes(search) ||
          member.email?.toLowerCase().includes(search) ||
          member.phone?.includes(search)
      );
    },
  });
}

export function useMember(memberId: string) {
  const { data: session } = useSession();
  const api = useApi();

  return useQuery({
    queryKey: memberKeys.detail(memberId),
    queryFn: () => api.getMember(memberId),
    enabled: !!session?.accessToken && !!memberId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useMemberDebrief(memberId: string, filters?: PaymentFilters) {
  const { data: session } = useSession();
  const api = useApi();

  return useQuery({
    queryKey: memberKeys.debrief(memberId, filters),
    queryFn: () => api.getMemberDebrief(memberId, filters),
    enabled: !!session?.accessToken && !!memberId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Mutations
export function useCreateMember() {
  const queryClient = useQueryClient();
  const api = useApi();

  return useMutation({
    mutationFn: (data: CreateMemberDto) => api.createMember(data),
    onSuccess: () => {
      // Invalidate and refetch members list
      queryClient.invalidateQueries({ queryKey: memberKeys.lists() });
    },
    onError: (error) => {
      console.error('Error creating member:', error);
    },
  });
}

export function useUpdateMember() {
  const queryClient = useQueryClient();
  const api = useApi();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateMemberDto }) => 
      api.updateMember(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate specific member and members list
      queryClient.invalidateQueries({ queryKey: memberKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: memberKeys.lists() });
      // Also invalidate debrief data
      queryClient.invalidateQueries({ queryKey: memberKeys.debrief(id) });
    },
    onError: (error) => {
      console.error('Error updating member:', error);
    },
  });
}

export function useDeleteMember() {
  const queryClient = useQueryClient();
  const api = useApi();

  return useMutation({
    mutationFn: (memberId: string) => api.deleteMember(memberId),
    onSuccess: (_, memberId) => {
      // Remove from cache and invalidate lists
      queryClient.removeQueries({ queryKey: memberKeys.detail(memberId) });
      queryClient.removeQueries({ queryKey: memberKeys.debrief(memberId) });
      queryClient.invalidateQueries({ queryKey: memberKeys.lists() });
    },
    onError: (error) => {
      console.error('Error deleting member:', error);
    },
  });
}
