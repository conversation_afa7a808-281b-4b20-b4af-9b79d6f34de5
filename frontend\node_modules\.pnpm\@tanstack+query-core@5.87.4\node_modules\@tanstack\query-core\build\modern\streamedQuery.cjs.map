{"version": 3, "sources": ["../../src/streamedQuery.ts"], "sourcesContent": ["import { addToEnd } from './utils'\nimport type { QueryFunction, QueryFunctionContext, QueryKey } from './types'\n\ntype BaseStreamedQueryParams<TQueryFnData, TQueryKey extends QueryKey> = {\n  streamFn: (\n    context: QueryFunctionContext<TQueryKey>,\n  ) => AsyncIterable<TQueryFnData> | Promise<AsyncIterable<TQueryFnData>>\n  refetchMode?: 'append' | 'reset' | 'replace'\n}\n\ntype SimpleStreamedQueryParams<\n  TQueryFnData,\n  TQueryKey extends QueryKey,\n> = BaseStreamedQueryParams<TQueryFnData, TQueryKey> & {\n  reducer?: never\n  initialValue?: never\n}\n\ntype ReducibleStreamedQueryParams<\n  TQueryFnData,\n  TData,\n  TQueryKey extends QueryKey,\n> = BaseStreamedQueryParams<TQueryFnData, TQueryKey> & {\n  reducer: (acc: TData, chunk: TQueryFnData) => TData\n  initialValue: TData\n}\n\ntype StreamedQueryParams<TQueryFnData, TData, TQueryKey extends QueryKey> =\n  | SimpleStreamedQueryParams<TQueryFnData, TQueryKey>\n  | ReducibleStreamedQueryParams<TQueryFnData, TData, TQueryKey>\n\n/**\n * This is a helper function to create a query function that streams data from an AsyncIterable.\n * Data will be an Array of all the chunks received.\n * The query will be in a 'pending' state until the first chunk of data is received, but will go to 'success' after that.\n * The query will stay in fetchStatus 'fetching' until the stream ends.\n * @param queryFn - The function that returns an AsyncIterable to stream data from.\n * @param refetchMode - Defines how re-fetches are handled.\n * Defaults to `'reset'`, erases all data and puts the query back into `pending` state.\n * Set to `'append'` to append new data to the existing data.\n * Set to `'replace'` to write all data to the cache once the stream ends.\n * @param reducer - A function to reduce the streamed chunks into the final data.\n * Defaults to a function that appends chunks to the end of the array.\n * @param initialValue - Initial value to be used while the first chunk is being fetched.\n */\nexport function streamedQuery<\n  TQueryFnData = unknown,\n  TData = Array<TQueryFnData>,\n  TQueryKey extends QueryKey = QueryKey,\n>({\n  streamFn,\n  refetchMode = 'reset',\n  reducer = (items, chunk) =>\n    addToEnd(items as Array<TQueryFnData>, chunk) as TData,\n  initialValue = [] as TData,\n}: StreamedQueryParams<TQueryFnData, TData, TQueryKey>): QueryFunction<\n  TData,\n  TQueryKey\n> {\n  return async (context) => {\n    const query = context.client\n      .getQueryCache()\n      .find({ queryKey: context.queryKey, exact: true })\n    const isRefetch = !!query && query.state.data !== undefined\n    if (isRefetch && refetchMode === 'reset') {\n      query.setState({\n        status: 'pending',\n        data: undefined,\n        error: null,\n        fetchStatus: 'fetching',\n      })\n    }\n\n    let result = initialValue\n\n    const stream = await streamFn(context)\n\n    for await (const chunk of stream) {\n      if (context.signal.aborted) {\n        break\n      }\n\n      // don't append to the cache directly when replace-refetching\n      if (!isRefetch || refetchMode !== 'replace') {\n        context.client.setQueryData<TData>(context.queryKey, (prev) =>\n          reducer(prev === undefined ? initialValue : prev, chunk),\n        )\n      }\n      result = reducer(result, chunk)\n    }\n\n    // finalize result: replace-refetching needs to write to the cache\n    if (isRefetch && refetchMode === 'replace' && !context.signal.aborted) {\n      context.client.setQueryData<TData>(context.queryKey, result)\n    }\n\n    return context.client.getQueryData(context.queryKey)!\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAyB;AA6ClB,SAAS,cAId;AAAA,EACA;AAAA,EACA,cAAc;AAAA,EACd,UAAU,CAAC,OAAO,cAChB,uBAAS,OAA8B,KAAK;AAAA,EAC9C,eAAe,CAAC;AAClB,GAGE;AACA,SAAO,OAAO,YAAY;AACxB,UAAM,QAAQ,QAAQ,OACnB,cAAc,EACd,KAAK,EAAE,UAAU,QAAQ,UAAU,OAAO,KAAK,CAAC;AACnD,UAAM,YAAY,CAAC,CAAC,SAAS,MAAM,MAAM,SAAS;AAClD,QAAI,aAAa,gBAAgB,SAAS;AACxC,YAAM,SAAS;AAAA,QACb,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACf,CAAC;AAAA,IACH;AAEA,QAAI,SAAS;AAEb,UAAM,SAAS,MAAM,SAAS,OAAO;AAErC,qBAAiB,SAAS,QAAQ;AAChC,UAAI,QAAQ,OAAO,SAAS;AAC1B;AAAA,MACF;AAGA,UAAI,CAAC,aAAa,gBAAgB,WAAW;AAC3C,gBAAQ,OAAO;AAAA,UAAoB,QAAQ;AAAA,UAAU,CAAC,SACpD,QAAQ,SAAS,SAAY,eAAe,MAAM,KAAK;AAAA,QACzD;AAAA,MACF;AACA,eAAS,QAAQ,QAAQ,KAAK;AAAA,IAChC;AAGA,QAAI,aAAa,gBAAgB,aAAa,CAAC,QAAQ,OAAO,SAAS;AACrE,cAAQ,OAAO,aAAoB,QAAQ,UAAU,MAAM;AAAA,IAC7D;AAEA,WAAO,QAAQ,OAAO,aAAa,QAAQ,QAAQ;AAAA,EACrD;AACF;", "names": []}