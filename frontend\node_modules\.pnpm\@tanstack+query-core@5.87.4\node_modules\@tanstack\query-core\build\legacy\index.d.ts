export { focusManager } from './focusManager.js';
export { T as AnyDataTag, b6 as CancelOptions, C as CancelledError, V as DataTag, G as DefaultError, b5 as DefaultOptions, ak as DefaultedInfiniteQueryObserverOptions, ai as DefaultedQueryObserverOptions, aP as DefinedInfiniteQueryObserverResult, aG as DefinedQueryObserverResult, w as DehydrateOptions, D as DehydratedState, B as DistributiveOmit, $ as Enabled, an as EnsureInfiniteQueryDataOptions, am as EnsureQueryDataOptions, ao as FetchInfiniteQueryOptions, av as FetchNextPageOptions, aw as FetchPreviousPageOptions, al as FetchQueryOptions, ay as FetchStatus, a7 as GetNextPageParamFunction, a6 as GetPreviousPageParamFunction, H as HydrateOptions, W as InferDataFromTag, X as InferErrorFromTag, a8 as InfiniteData, aI as InfiniteQueryObserverBaseResult, aL as InfiniteQueryObserverLoadingErrorResult, aK as InfiniteQueryObserverLoadingResult, aj as InfiniteQueryObserverOptions, aJ as InfiniteQueryObserverPendingResult, aO as InfiniteQueryObserverPlaceholderResult, aM as InfiniteQueryObserverRefetchErrorResult, aQ as InfiniteQueryObserverResult, aN as InfiniteQueryObserverSuccessResult, ae as InfiniteQueryPageParamsOptions, a2 as InitialDataFunction, ad as InitialPageParam, at as InvalidateOptions, ar as InvalidateQueryFilters, aZ as MutateFunction, aY as MutateOptions, x as Mutation, M as MutationCache, c as MutationCacheNotifyEvent, u as MutationFilters, aV as MutationFunction, aR as MutationKey, aU as MutationMeta, e as MutationObserver, a_ as MutationObserverBaseResult, b1 as MutationObserverErrorResult, a$ as MutationObserverIdleResult, b0 as MutationObserverLoadingResult, aX as MutationObserverOptions, b3 as MutationObserverResult, b2 as MutationObserverSuccessResult, aW as MutationOptions, aT as MutationScope, y as MutationState, aS as MutationStatus, aa as NetworkMode, F as NoInfer, N as NonUndefinedGuard, b9 as NotifyEvent, b8 as NotifyEventType, ab as NotifyOnChangeProps, O as OmitKeyof, E as Override, a3 as PlaceholderDataFunction, a4 as QueriesPlaceholderDataFunction, z as Query, Q as QueryCache, f as QueryCacheNotifyEvent, g as QueryClient, b4 as QueryClientConfig, v as QueryFilters, Y as QueryFunction, a1 as QueryFunctionContext, I as QueryKey, a5 as QueryKeyHashFunction, a9 as QueryMeta, i as QueryObserver, az as QueryObserverBaseResult, aC as QueryObserverLoadingErrorResult, aB as QueryObserverLoadingResult, ag as QueryObserverOptions, aA as QueryObserverPendingResult, aF as QueryObserverPlaceholderResult, aD as QueryObserverRefetchErrorResult, aH as QueryObserverResult, aE as QueryObserverSuccessResult, ac as QueryOptions, a0 as QueryPersister, A as QueryState, ax as QueryStatus, aq as RefetchOptions, as as RefetchQueryFilters, R as Register, au as ResetOptions, ap as ResultOptions, b7 as SetDataOptions, S as SkipToken, Z as StaleTime, _ as StaleTimeFunction, af as ThrowOnError, P as UnsetMarker, U as Updater, ah as WithRequired, K as dataTagErrorSymbol, J as dataTagSymbol, d as defaultShouldDehydrateMutation, a as defaultShouldDehydrateQuery, b as dehydrate, k as hashKey, h as hydrate, j as isCancelledError, l as isServer, m as keepPreviousData, n as matchMutation, o as matchQuery, p as noop, q as partialMatchKey, r as replaceEqualDeep, s as shouldThrowError, t as skipToken, L as unsetMarker } from './hydration-BYonJkjc.js';
export { InfiniteQueryObserver } from './infiniteQueryObserver.js';
export { defaultScheduler, notifyManager } from './notifyManager.js';
export { onlineManager } from './onlineManager.js';
export { QueriesObserver, QueriesObserverOptions } from './queriesObserver.js';
export { ManagedTimerId, TimeoutCallback, TimeoutProvider, timeoutManager } from './timeoutManager.js';
export { streamedQuery as experimental_streamedQuery } from './streamedQuery.js';
import './subscribable.js';
import './removable.js';
