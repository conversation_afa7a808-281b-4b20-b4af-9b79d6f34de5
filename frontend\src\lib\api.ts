import { signOut } from "next-auth/react";
import { signOut as ServerSignOut } from "./auth";
import { Caisse, Session } from "@/types";

// Configuration de l'API
export const API_BASE_URL =
	process.env.NEXT_PUBLIC_API_URL || "http://localhost:4000";

// Types pour l'authentification
export interface LoginRequest {
	username: string;
	password: string;
}

export interface LoginResponse {
	access_token: string;
	user: {
		id: string;
		username: string;
		nom: string;
		prenom: string;
		email: string;
		role: "admin" | "tresorier" | "membre";
		statut: "actif" | "en_attente" | "suspendu";
	};
}

export interface RegisterRequest {
	username: string;
	password: string;
	role: string;
	// statut?: "actif" | "en_attente" | "suspendu";
}

export interface ApiError {
	message: string;
	statusCode: number;
	error?: string;
}

// Service API
export class ApiService {
	private baseUrl: string;

	constructor(baseUrl: string = API_BASE_URL) {
		this.baseUrl = baseUrl;
	}

	private async request<T>(
		endpoint: string,
		options: RequestInit = {},
	): Promise<T> {
		const url = `${this.baseUrl}${endpoint}`;

		const config: RequestInit = {
			headers: {
				"Content-Type": "application/json",
				...options.headers,
			},
			...options,
		};

		try {
			const response = await fetch(url, config);

			if (!response.ok) {
				const errorData: ApiError = await response.json().catch(() => ({
					message: "Une erreur est survenue",
					statusCode: response.status,
				}));

				// 🚨 Gestion du cas 401
				if (response.status === 401) {
					// Si côté client → on déconnecte
					if (typeof window !== "undefined") {
						await signOut({ callbackUrl: "/auth/signin" });
					} else {
						await ServerSignOut({ redirectTo: "/auth/signin" });
					}
				}
				throw new Error(errorData.message || `HTTP ${response.status}`);
			}

			return await response.json();
		} catch (error) {
			console.log(error);

			if (error instanceof Error) {
				throw error;
			}
			throw new Error("Erreur de connexion au serveur");
		}
	}

	// Authentification
	async login(credentials: LoginRequest): Promise<LoginResponse> {
		return this.request<LoginResponse>("/auth/login", {
			method: "POST",
			body: JSON.stringify(credentials),
		});
	}

	async register(
		userData: RegisterRequest,
	): Promise<{ message: string; user: any }> {
		return this.request<{ message: string; user: any }>("/auth/register", {
			method: "POST",
			body: JSON.stringify(userData),
		});
	}

	// Méthodes avec authentification
	async authenticatedRequest<T>(
		endpoint: string,
		token: string,
		options: RequestInit = {},
	): Promise<T> {
		return this.request<T>(endpoint, {
			...options,
			headers: {
				...options.headers,
				"Content-Type": "application/json",
				Authorization: `Bearer ${token}`,
			},
		});
	}

	// Utilisateurs
	async getUsers(token: string): Promise<any[]> {
		return this.authenticatedRequest<any[]>("/users", token);
	}

	async getUser(id: string, token: string): Promise<any> {
		return this.authenticatedRequest<any>(`/users/${id}`, token);
	}

	async getSessions(token: string): Promise<Session[]> {
		return this.authenticatedRequest<Session[]>("/sessions", token);
	}

	async getCaisses(token: string): Promise<Caisse[]> {
		return this.authenticatedRequest<Caisse[]>("/caisses", token);
	}
}

// Instance par défaut
export const apiService = new ApiService();
