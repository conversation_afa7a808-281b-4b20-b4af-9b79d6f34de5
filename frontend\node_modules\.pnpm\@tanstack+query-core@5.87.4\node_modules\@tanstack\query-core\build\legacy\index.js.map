{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["/* istanbul ignore file */\n\nexport { focusManager } from './focusManager'\nexport {\n  defaultShouldDehydrateMutation,\n  defaultShouldDehydrateQuery,\n  dehydrate,\n  hydrate,\n} from './hydration'\nexport { InfiniteQueryObserver } from './infiniteQueryObserver'\nexport { MutationCache } from './mutationCache'\nexport type { MutationCacheNotifyEvent } from './mutationCache'\nexport { MutationObserver } from './mutationObserver'\nexport { defaultScheduler, notifyManager } from './notifyManager'\nexport { onlineManager } from './onlineManager'\nexport { QueriesObserver } from './queriesObserver'\nexport { QueryCache } from './queryCache'\nexport type { QueryCacheNotifyEvent } from './queryCache'\nexport { QueryClient } from './queryClient'\nexport { QueryObserver } from './queryObserver'\nexport { CancelledError, isCancelledError } from './retryer'\nexport {\n  timeoutManager,\n  type ManagedTimerId,\n  type TimeoutCallback,\n  type TimeoutProvider,\n} from './timeoutManager'\nexport {\n  hashKey,\n  isServer,\n  keepPreviousData,\n  matchMutation,\n  matchQuery,\n  noop,\n  partialMatchKey,\n  replaceEqualDeep,\n  shouldThrowError,\n  skipToken,\n} from './utils'\nexport type { MutationFilters, QueryFilters, SkipToken, Updater } from './utils'\n\nexport { streamedQuery as experimental_streamedQuery } from './streamedQuery'\n\n// Types\nexport type {\n  DehydratedState,\n  DehydrateOptions,\n  HydrateOptions,\n} from './hydration'\nexport { Mutation } from './mutation'\nexport type { MutationState } from './mutation'\nexport type { QueriesObserverOptions } from './queriesObserver'\nexport { Query } from './query'\nexport type { QueryState } from './query'\nexport * from './types'\n"], "mappings": ";;;AAEA,SAAS,oBAAoB;AAC7B;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,6BAA6B;AACtC,SAAS,qBAAqB;AAE9B,SAAS,wBAAwB;AACjC,SAAS,kBAAkB,qBAAqB;AAChD,SAAS,qBAAqB;AAC9B,SAAS,uBAAuB;AAChC,SAAS,kBAAkB;AAE3B,SAAS,mBAAmB;AAC5B,SAAS,qBAAqB;AAC9B,SAAS,gBAAgB,wBAAwB;AACjD;AAAA,EACE;AAAA,OAIK;AACP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAGP,SAA0B,qBAAkC;AAQ5D,SAAS,gBAAgB;AAGzB,SAAS,aAAa;AAEtB,cAAc;", "names": []}