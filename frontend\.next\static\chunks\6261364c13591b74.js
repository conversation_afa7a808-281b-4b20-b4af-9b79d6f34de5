(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,72355,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},44640,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return n}});class n{static get(e,r,t){let n=Reflect.get(e,r,t);return"function"==typeof n?n.bind(e):n}static set(e,r,t,n){return Reflect.set(e,r,t,n)}static has(e,r){return Reflect.has(e,r)}static deleteProperty(e,r){return Reflect.deleteProperty(e,r)}}},8356,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"afterTaskAsyncStorageInstance",{enumerable:!0,get:function(){return n}});let n=(0,e.r(97918).createAsyncLocalStorage)()},62355,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"afterTaskAsyncStorage",{enumerable:!0,get:function(){return n.afterTaskAsyncStorageInstance}});let n=e.r(8356)},17939,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(t,{isRequestAPICallableInsideAfter:function(){return l},throwForSearchParamsAccessInUseCache:function(){return s},throwWithStaticGenerationBailoutError:function(){return a},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return o}});let n=e.r(85115),i=e.r(62355);function a(e,r){throw Object.defineProperty(new n.StaticGenBailoutError("Route ".concat(e," couldn't be rendered statically because it used ").concat(r,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function o(e,r){throw Object.defineProperty(new n.StaticGenBailoutError("Route ".concat(e,' with `dynamic = "error"` couldn\'t be rendered statically because it used ').concat(r,". See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering")),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function s(e,r){let t=Object.defineProperty(Error("Route ".concat(e.route,' used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache')),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(t,r),null!=e.invalidDynamicUsageError||(e.invalidDynamicUsageError=t),t}function l(){let e=i.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},93168,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(t,{describeHasCheckingStringProperty:function(){return a},describeStringPropertyAccess:function(){return i},wellKnownProperties:function(){return o}});let n=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function i(e,r){return n.test(r)?"`"+e+"."+r+"`":"`"+e+"["+JSON.stringify(r)+"]`"}function a(e,r){let t=JSON.stringify(r);return"`Reflect.has("+e+", "+t+")`, `"+t+" in "+e+"`, or similar"}let o=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"])},63470,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return l}});let n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var s=a?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(n,o,s):n[o]=e[o]}return n.default=e,t&&t.set(e,n),n}(e.r(38477));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}let a={current:null},o="function"==typeof n.cache?n.cache:e=>e,s=console.warn;function l(e){return function(){for(var r=arguments.length,t=Array(r),n=0;n<r;n++)t[n]=arguments[n];s(e(...t))}}o(e=>{try{s(a.current)}finally{a.current=null}})},44571,e=>{"use strict";e.s(["default",()=>o],44571);var r=e.i(38477);let t=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},n=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((e,t)=>{let{color:a="currentColor",size:o=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:c="",children:u,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:a,strokeWidth:l?24*Number(s)/Number(o):s,className:n("lucide",c),...!u&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(f)&&{"aria-hidden":"true"},...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(u)?u:[u]])}),o=(e,i)=>{let o=(0,r.forwardRef)((o,s)=>{let{className:l,...c}=o;return(0,r.createElement)(a,{ref:s,iconNode:i,className:n("lucide-".concat(t(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),l),...c})});return o.displayName=t(e),o}},12058,e=>{"use strict";e.s(["CaisseType",()=>r,"PaymentDirection",()=>n,"PaymentFunction",()=>i,"UserRole",()=>t]);var r=function(e){return e.PRINCIPALE="PRINCIPALE",e.REUNION="REUNION",e}({}),t=function(e){return e.SECRETARY_GENERAL="secretary_general",e.CONTROLLER="controller",e.CASHIER="cashier",e}({}),n=function(e){return e.IN="IN",e.OUT="OUT",e}({}),i=function(e){return e.CONTRIBUTION="cotisation",e.TRANSFER="transfert",e.EXTERNAL="exterieur",e}({})},96134,e=>{"use strict";e.s(["Input",()=>n]);var r=e.i(4051),t=e.i(41428);function n(e){let{className:n,type:i,...a}=e;return(0,r.jsx)("input",{type:i,"data-slot":"input",className:(0,t.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",n),...a})}},38909,e=>{"use strict";e.s(["Primitive",()=>a,"dispatchDiscreteCustomEvent",()=>o]);var r=e.i(38477),t=e.i(41902),n=e.i(81221),i=e.i(4051),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let a=(0,n.createSlot)("Primitive.".concat(t)),o=r.forwardRef((e,r)=>{let{asChild:n,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?a:t,{...o,ref:r})});return o.displayName="Primitive.".concat(t),{...e,[t]:o}},{});function o(e,r){e&&t.flushSync(()=>e.dispatchEvent(r))}},65429,e=>{"use strict";e.s(["Label",()=>o],65429);var r=e.i(4051),t=e.i(38477),n=e.i(38909),i=t.forwardRef((e,t)=>(0,r.jsx)(n.Primitive.label,{...e,ref:t,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var a=e.i(41428);function o(e){let{className:t,...n}=e;return(0,r.jsx)(i,{"data-slot":"label",className:(0,a.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...n})}},51131,e=>{"use strict";e.s(["default",()=>g]);var r=e.i(4051),t=e.i(38477),n=e.i(57691),i=e.i(67967),a=e.i(78381),o=e.i(45086),s=e.i(5085),l=e.i(85205),c=e.i(96134),u=e.i(75680),d=e.i(5647),f=e.i(83194),m=e.i(1831),p=e.i(12058);let h=o.z.object({username:o.z.string().min(3,"Le nom d'utilisateur doit contenir au moins 3 caractères"),password:o.z.string().min(6,"Le mot de passe doit contenir au moins 6 caractères"),role:o.z.nativeEnum(p.UserRole)});function g(){let[e,o]=(0,t.useState)(!1),[g,b]=(0,t.useState)(null),[x,y]=(0,t.useState)(!1),j=(0,n.useRouter)(),v=(0,i.useForm)({resolver:(0,a.zodResolver)(h),defaultValues:{username:"",password:"",role:p.UserRole.CASHIER}}),w=async e=>{o(!0),b(null);try{let r=await m.apiService.register({...e});console.log("Inscription réussie:",r),y(!0),setTimeout(()=>{j.push("/auth/signin")},2e3)}catch(e){console.error("Erreur d'inscription:",e),e instanceof Error?b(e.message):b("Erreur lors de la création du compte. Veuillez réessayer.")}finally{o(!1)}};return x?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsx)(u.Card,{className:"w-full max-w-md",children:(0,r.jsxs)(u.CardHeader,{className:"text-center",children:[(0,r.jsx)(u.CardTitle,{className:"text-2xl font-bold text-green-600",children:"Compte créé avec succès !"}),(0,r.jsx)(u.CardDescription,{children:"Vous allez être redirigé vers la page de connexion..."})]})})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)(u.Card,{className:"w-full max-w-md",children:[(0,r.jsxs)(u.CardHeader,{className:"space-y-1",children:[(0,r.jsx)(u.CardTitle,{className:"text-2xl font-bold text-center",children:"Créer un compte"}),(0,r.jsx)(u.CardDescription,{className:"text-center",children:"Créez votre compte Tontine"}),(0,r.jsx)("div",{className:"text-xs text-orange-600 text-center bg-orange-50 p-2 rounded",children:"⚠️ Page temporaire - Sera supprimée en production"})]}),(0,r.jsxs)(u.CardContent,{children:[(0,r.jsx)(d.Form,{...v,children:(0,r.jsxs)("form",{onSubmit:v.handleSubmit(w),className:"space-y-4",children:[(0,r.jsx)(d.FormField,{control:v.control,name:"username",render:t=>{let{field:n}=t;return(0,r.jsxs)(d.FormItem,{children:[(0,r.jsx)(d.FormLabel,{children:"Nom d'utilisateur"}),(0,r.jsx)(d.FormControl,{children:(0,r.jsx)(c.Input,{placeholder:"Nom d'utilisateur unique",...n,disabled:e})}),(0,r.jsx)(d.FormMessage,{})]})}}),(0,r.jsx)(d.FormField,{control:v.control,name:"password",render:t=>{let{field:n}=t;return(0,r.jsxs)(d.FormItem,{children:[(0,r.jsx)(d.FormLabel,{children:"Mot de passe"}),(0,r.jsx)(d.FormControl,{children:(0,r.jsx)(c.Input,{type:"password",placeholder:"Minimum 6 caractères",...n,disabled:e})}),(0,r.jsx)(d.FormMessage,{})]})}}),(0,r.jsx)(d.FormField,{control:v.control,name:"role",render:t=>{let{field:n}=t;return(0,r.jsxs)(d.FormItem,{children:[(0,r.jsx)(d.FormLabel,{children:"Rôle"}),(0,r.jsxs)(f.Select,{onValueChange:n.onChange,defaultValue:n.value,children:[(0,r.jsx)(d.FormControl,{children:(0,r.jsx)(f.SelectTrigger,{disabled:e,children:(0,r.jsx)(f.SelectValue,{placeholder:"Sélectionnez un rôle"})})}),(0,r.jsxs)(f.SelectContent,{children:[(0,r.jsx)(f.SelectItem,{value:p.UserRole.CASHIER,children:"Caissier"}),(0,r.jsx)(f.SelectItem,{value:p.UserRole.CONTROLLER,children:"Contrôleur"}),(0,r.jsx)(f.SelectItem,{value:p.UserRole.SECRETARY_GENERAL,children:"Secrétaire Général"})]})]}),(0,r.jsx)(d.FormMessage,{})]})}}),g&&(0,r.jsx)("div",{className:"text-red-600 text-sm text-center bg-red-50 p-2 rounded",children:g}),(0,r.jsx)(l.Button,{type:"submit",className:"w-full",disabled:e,children:e?"Création du compte...":"Créer le compte"})]})}),(0,r.jsxs)("div",{className:"mt-4 text-center text-sm text-gray-600",children:["Vous avez déjà un compte ?"," ",(0,r.jsx)(s.default,{href:"/auth/signin",className:"text-blue-600 hover:underline",children:"Se connecter"})]})]})]})})}}]);